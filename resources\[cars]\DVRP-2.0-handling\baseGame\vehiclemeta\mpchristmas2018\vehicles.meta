<?xml version="1.0" encoding="UTF-8"?>
<CVehicleModelInfo__InitDataList>
  <residentTxd>vehshare</residentTxd>
  <residentAnims />
  <InitDatas>
    <Item>
      <modelName>impaler3</modelName>
      <txdName>impaler3</txdName>
      <handlingId>IMPALER3</handlingId>
      <gameName>IMPALER3</gameName>
      <vehicleMakeName>DECLASSE</vehicleMakeName>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected />
      <ptfxAssetName>weap_xs_vehicle_weapons</ptfxAssetName>
      <audioNameHash />
      <layout>LAYOUT_STD_ARENA_1HONLY</layout>
      <coverBoundOffsets>IMPALER_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout />
      <cameraName>FOLLOW_CHEETAH_CAMERA</cameraName>
      <aimCameraName>DEFAULT_THIRD_PERSON_VEHICLE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>VEHICLE_BONNET_CAMERA_STANDARD_LONG_DEVIANT</bonnetCameraName>
      <povCameraName>REDUCED_NEAR_CLIP_POV_CAMERA</povCameraName>
      <FirstPersonDriveByIKOffset x="0.020000" y="-0.065000" z="-0.050000" />
      <FirstPersonDriveByUnarmedIKOffset x="0.000000" y="-0.100000" z="0.000000" />
	  <FirstPersonProjectileDriveByIKOffset x="0.000000" y="-0.130000" z="-0.050000" />
	  <FirstPersonProjectileDriveByPassengerIKOffset x="0.000000" y="-0.100000" z="0.000000" />
	  <FirstPersonDriveByRightPassengerIKOffset x="-0.020000" y="-0.065000" z="-0.050000" />
	  <FirstPersonDriveByRightPassengerUnarmedIKOffset x="0.000000" y="-0.100000" z="0.000000" />
	  <FirstPersonMobilePhoneOffset x="0.146000" y="0.220000" z="0.510000" />
      <FirstPersonPassengerMobilePhoneOffset x="0.234000" y="0.169000" z="0.395000" />
      <PovCameraOffset x="0.000000" y="-0.195000" z="0.640000" />
      <PovCameraVerticalAdjustmentForRollCage value="0.000000" />
      <PovPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000" />
      <PovRearPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000" />
      <vfxInfoName>VFXVEHICLEINFO_CAR_GENERIC</vfxInfoName>
      <shouldUseCinematicViewMode value="true" />
      <shouldCameraTransitionOnClimbUpDown value="false" />
      <shouldCameraIgnoreExiting value="false" />
      <AllowPretendOccupants value="true" />
      <AllowJoyriding value="true" />
      <AllowSundayDriving value="true" />
      <AllowBodyColorMapping value="true" />
      <wheelScale value="0.202300" />
      <wheelScaleRear value="0.0.201800" />
      <dirtLevelMin value="0.000000" />
      <dirtLevelMax value="0.450000" />
      <envEffScaleMin value="0.000000" />
      <envEffScaleMax value="1.000000" />
      <envEffScaleMin2 value="0.000000" />
      <envEffScaleMax2 value="1.000000" />
      <damageMapScale value="0.000000" />
      <damageOffsetScale value="0.100000" />
      <diffuseTint value="0x00FFFFFF" />
      <steerWheelMult value="0.700000" />
      <HDTextureDist value="5.000000" />
      <lodDistances content="float_array">
        10.000000
        25.000000
        60.000000
        120.000000
        500.000000
        500.000000
      </lodDistances>
      <minSeatHeight value="0.844" />
      <identicalModelSpawnDistance value="20" />
      <maxNumOfSameColor value="1" />
      <defaultBodyHealth value="1000.000000" />
      <pretendOccupantsScale value="1.000000" />
      <visibleSpawnDistScale value="1.000000" />
      <trackerPathWidth value="2.000000" />
      <weaponForceMult value="1.000000" />
      <frequency value="30" />
      <swankness>SWANKNESS_4</swankness>
      <maxNum value="10" />
      <flags>FLAG_RECESSED_HEADLIGHT_CORONAS FLAG_EXTRAS_STRONG FLAG_AVERAGE_CAR FLAG_HAS_INTERIOR_EXTRAS FLAG_CAN_HAVE_NEONS FLAG_HAS_JUMP_MOD FLAG_HAS_NITROUS_MOD FLAG_HAS_RAMMING_SCOOP_MOD FLAG_USE_AIRCRAFT_STYLE_WEAPON_TARGETING FLAG_HAS_SIDE_SHUNT FLAG_HAS_WEAPON_SPIKE_MODS FLAG_HAS_SUPERCHARGER FLAG_INCREASE_CAMBER_WITH_SUSPENSION_MOD FLAG_DISABLE_DEFORMATION</flags>
      <type>VEHICLE_TYPE_CAR</type>
      <plateType>VPT_FRONT_AND_BACK_PLATES</plateType>
	  <dashboardType>VDT_DUKES</dashboardType>
      <vehicleClass>VC_MUSCLE</vehicleClass>
      <wheelType>VWT_MUSCLE</wheelType>
      <trailers />
      <additionalTrailers />
      <drivers />
      <extraIncludes />
      <doorsWithCollisionWhenClosed />
      <driveableDoors />
      <bumpersNeedToCollideWithMap value="false" />
      <needsRopeTexture value="false" />
      <requiredExtras />
      <rewards />
      <cinematicPartCamera>
        <Item>WHEEL_FRONT_RIGHT_CAMERA</Item>
        <Item>WHEEL_FRONT_LEFT_CAMERA</Item>
        <Item>WHEEL_REAR_RIGHT_CAMERA</Item>
        <Item>WHEEL_REAR_LEFT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet />
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000" />
      <buoyancySphereSizeScale value="1.000000" />
      <pOverrideRagdollThreshold type="NULL" />
	  <firstPersonDrivebyData>
        <Item>STD_IMPALER2_FRONT_LEFT</Item>
        <Item>STD_IMPALER2_FRONT_RIGHT</Item>
      </firstPersonDrivebyData>
    </Item>
	<Item>
      <modelName>monster4</modelName>
      <txdName>monster4</txdName>
      <handlingId>MONSTER3</handlingId>
      <gameName>MONSTER4</gameName>
      <vehicleMakeName>BRAVADO</vehicleMakeName>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected />
      <ptfxAssetName>weap_xs_vehicle_weapons</ptfxAssetName>
      <audioNameHash />
      <layout>LAYOUT_TRUCK_MONSTER3</layout>
      <coverBoundOffsets>MONSTER3_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout />
      <cameraName>MONSTER_FOLLOW_VEHICLE_CAMERA</cameraName>
      <aimCameraName>DEFAULT_THIRD_PERSON_VEHICLE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>VEHICLE_BONNET_CAMERA_STANDARD_LONG</bonnetCameraName>
      <povCameraName>DEFAULT_POV_CAMERA_LOOKAROUND</povCameraName>
      <FirstPersonDriveByIKOffset x="-0.030000" y="-0.050000" z="-0.05000" />
      <FirstPersonDriveByUnarmedIKOffset x="0.000000" y="-0.120000" z="-0.030000" />
      <FirstPersonProjectileDriveByIKOffset x="0.04000" y="-0.120000" z="-0.058000" />
      <FirstPersonProjectileDriveByPassengerIKOffset x="-0.03000" y="-0.130000" z="-0.058000" />
      <FirstPersonDriveByRightPassengerIKOffset x="0.000000" y="-0.040000" z="-0.05000" />
      <FirstPersonDriveByRightPassengerUnarmedIKOffset x="0.000000" y="-0.120000" z="-0.030000" />
      <FirstPersonMobilePhoneOffset x="0.120000" y="0.336000" z="0.503000" />
      <FirstPersonPassengerMobilePhoneOffset x="0.253000" y="0.343000" z="0.453000" />
      <PovCameraOffset x="0.000000" y="-0.020000" z="0.650000" />
      <PovCameraVerticalAdjustmentForRollCage value="0.000000" />
      <PovPassengerCameraOffset x="0.000000" y="0.000000" z="0.080000" />
      <vfxInfoName>VFXVEHICLEINFO_CAR_OFFROAD</vfxInfoName>
      <shouldUseCinematicViewMode value="true" />
      <shouldCameraTransitionOnClimbUpDown value="false" />
      <shouldCameraIgnoreExiting value="false" />
      <AllowPretendOccupants value="true" />
      <AllowJoyriding value="true" />
      <AllowSundayDriving value="true" />
      <AllowBodyColorMapping value="true" />
      <wheelScale value="0.388000" />
      <wheelScaleRear value="0.388000" />
      <dirtLevelMin value="0.000000" />
      <dirtLevelMax value="1.000000" />
      <envEffScaleMin value="1.000000" />
      <envEffScaleMax value="1.000000" />
      <envEffScaleMin2 value="1.000000" />
      <envEffScaleMax2 value="1.000000" />
      <damageMapScale value="0.000000" />
      <damageOffsetScale value="0.100000" />
      <diffuseTint value="0x00FFFFFF" />
      <steerWheelMult value="1.000000" />
      <HDTextureDist value="5.000000" />
      <lodDistances content="float_array">
        15
        35
        90
        200
        500
        500
      </lodDistances>
      <minSeatHeight value="0.883" />
      <identicalModelSpawnDistance value="20" />
      <maxNumOfSameColor value="10" />
      <defaultBodyHealth value="1000.000000" />
      <pretendOccupantsScale value="1.000000" />
      <visibleSpawnDistScale value="1.000000" />
      <trackerPathWidth value="2.000000" />
      <weaponForceMult value="1.000000" />
      <frequency value="30" />
      <swankness>SWANKNESS_1</swankness>
      <maxNum value="5" />
      <flags>FLAG_IS_OFFROAD_VEHICLE FLAG_IS_BULKY FLAG_EXTRAS_REQUIRE FLAG_EXTRAS_STRONG FLAG_USE_RESTRICTED_DRIVEBY_HEIGHT FLAG_CRUSHES_OTHER_VEHICLES FLAG_HAS_JUMP_MOD FLAG_HAS_NITROUS_MOD FLAG_HAS_RAMMING_SCOOP_MOD FLAG_USE_AIRCRAFT_STYLE_WEAPON_TARGETING FLAG_HAS_SIDE_SHUNT FLAG_HAS_RAMMING_BAR_MOD FLAG_TURRET_MODS_ON_CHASSIS5 FLAG_TURRET_MOD_WITH_NO_STOCK_TURRET FLAG_DISABLE_DEFORMATION</flags>
      <type>VEHICLE_TYPE_CAR</type>
      <plateType>VPT_NONE</plateType>
      <dashboardType>VDT_DUKES</dashboardType>
      <vehicleClass>VC_OFF_ROAD</vehicleClass>
      <wheelType>VWT_SUV</wheelType>
      <trailers />
      <additionalTrailers />
      <drivers />
      <extraIncludes />
      <doorsWithCollisionWhenClosed />
      <driveableDoors />
      <bumpersNeedToCollideWithMap value="false" />
      <needsRopeTexture value="false" />
      <requiredExtras />
      <rewards />
      <cinematicPartCamera>
        <Item>WHEEL_WIDE_REAR_RIGHT_CAMERA</Item>
        <Item>WHEEL_WIDE_REAR_LEFT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet>Truck</NmBraceOverrideSet>
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000" />
      <buoyancySphereSizeScale value="1.000000" />
      <pOverrideRagdollThreshold type="NULL" />
      <firstPersonDrivebyData>
        <Item>TRUCK_MONSTER3_FRONT_LEFT</Item>
        <Item>TRUCK_MONSTER3_FRONT_RIGHT</Item>
      </firstPersonDrivebyData>
    </Item>
	<Item>
      <modelName>monster5</modelName>
      <txdName>monster5</txdName>
      <handlingId>MONSTER3</handlingId>
      <gameName>MONSTER5</gameName>
      <vehicleMakeName>BRAVADO</vehicleMakeName>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected />
      <ptfxAssetName>weap_xs_vehicle_weapons</ptfxAssetName>
      <audioNameHash />
      <layout>LAYOUT_TRUCK_MONSTER3</layout>
      <coverBoundOffsets>MONSTER3_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout />
      <cameraName>MONSTER_FOLLOW_VEHICLE_CAMERA</cameraName>
      <aimCameraName>DEFAULT_THIRD_PERSON_VEHICLE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>VEHICLE_BONNET_CAMERA_STANDARD_LONG</bonnetCameraName>
      <povCameraName>DEFAULT_POV_CAMERA_LOOKAROUND</povCameraName>
      <FirstPersonDriveByIKOffset x="-0.030000" y="-0.050000" z="-0.05000" />
      <FirstPersonDriveByUnarmedIKOffset x="0.000000" y="-0.120000" z="-0.030000" />
      <FirstPersonProjectileDriveByIKOffset x="0.04000" y="-0.120000" z="-0.058000" />
      <FirstPersonProjectileDriveByPassengerIKOffset x="-0.03000" y="-0.130000" z="-0.058000" />
      <FirstPersonDriveByRightPassengerIKOffset x="0.000000" y="-0.040000" z="-0.05000" />
      <FirstPersonDriveByRightPassengerUnarmedIKOffset x="0.000000" y="-0.120000" z="-0.030000" />
      <FirstPersonMobilePhoneOffset x="0.120000" y="0.336000" z="0.503000" />
      <FirstPersonPassengerMobilePhoneOffset x="0.253000" y="0.343000" z="0.453000" />
      <PovCameraOffset x="0.000000" y="-0.020000" z="0.650000" />
      <PovCameraVerticalAdjustmentForRollCage value="0.000000" />
      <PovPassengerCameraOffset x="0.000000" y="0.000000" z="0.080000" />
      <vfxInfoName>VFXVEHICLEINFO_CAR_OFFROAD</vfxInfoName>
      <shouldUseCinematicViewMode value="true" />
      <shouldCameraTransitionOnClimbUpDown value="false" />
      <shouldCameraIgnoreExiting value="false" />
      <AllowPretendOccupants value="true" />
      <AllowJoyriding value="true" />
      <AllowSundayDriving value="true" />
      <AllowBodyColorMapping value="true" />
      <wheelScale value="0.388000" />
      <wheelScaleRear value="0.388000" />
      <dirtLevelMin value="0.000000" />
      <dirtLevelMax value="1.000000" />
      <envEffScaleMin value="1.000000" />
      <envEffScaleMax value="1.000000" />
      <envEffScaleMin2 value="1.000000" />
      <envEffScaleMax2 value="1.000000" />
      <damageMapScale value="0.200000" />
      <damageOffsetScale value="0.200000" />
      <diffuseTint value="0x00FFFFFF" />
      <steerWheelMult value="1.000000" />
      <HDTextureDist value="5.000000" />
      <lodDistances content="float_array">
        15
        35
        90
        200
        500
        500
      </lodDistances>
      <minSeatHeight value="0.883" />
      <identicalModelSpawnDistance value="20" />
      <maxNumOfSameColor value="10" />
      <defaultBodyHealth value="1000.000000" />
      <pretendOccupantsScale value="1.000000" />
      <visibleSpawnDistScale value="1.000000" />
      <trackerPathWidth value="2.000000" />
      <weaponForceMult value="1.000000" />
      <frequency value="30" />
      <swankness>SWANKNESS_1</swankness>
      <maxNum value="5" />
      <flags>FLAG_IS_OFFROAD_VEHICLE FLAG_IS_BULKY FLAG_EXTRAS_REQUIRE FLAG_EXTRAS_STRONG FLAG_USE_RESTRICTED_DRIVEBY_HEIGHT FLAG_CRUSHES_OTHER_VEHICLES FLAG_HAS_JUMP_MOD FLAG_HAS_NITROUS_MOD FLAG_HAS_RAMMING_SCOOP_MOD FLAG_USE_AIRCRAFT_STYLE_WEAPON_TARGETING FLAG_HAS_SIDE_SHUNT FLAG_HAS_RAMMING_BAR_MOD FLAG_TURRET_MODS_ON_CHASSIS5 FLAG_TURRET_MOD_WITH_NO_STOCK_TURRET</flags>
      <type>VEHICLE_TYPE_CAR</type>
      <plateType>VPT_NONE</plateType>
      <dashboardType>VDT_DUKES</dashboardType>
      <vehicleClass>VC_OFF_ROAD</vehicleClass>
      <wheelType>VWT_SUV</wheelType>
      <trailers />
      <additionalTrailers />
      <drivers />
      <extraIncludes />
      <doorsWithCollisionWhenClosed />
      <driveableDoors />
      <bumpersNeedToCollideWithMap value="false" />
      <needsRopeTexture value="false" />
      <requiredExtras />
      <rewards />
      <cinematicPartCamera>
        <Item>WHEEL_WIDE_REAR_RIGHT_CAMERA</Item>
        <Item>WHEEL_WIDE_REAR_LEFT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet>Truck</NmBraceOverrideSet>
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000" />
      <buoyancySphereSizeScale value="1.000000" />
      <pOverrideRagdollThreshold type="NULL" />
      <firstPersonDrivebyData>
        <Item>TRUCK_MONSTER3_FRONT_LEFT</Item>
        <Item>TRUCK_MONSTER3_FRONT_RIGHT</Item>
      </firstPersonDrivebyData>
    </Item>  
    <Item>
      <modelName>slamvan6</modelName>
      <txdName>slamvan6</txdName>
      <handlingId>SLAMVAN4</handlingId>
      <gameName>SLAMVAN6</gameName>
      <vehicleMakeName>VAPID</vehicleMakeName>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected />
      <ptfxAssetName>weap_xs_vehicle_weapons</ptfxAssetName>
      <audioNameHash />
      <layout>LAYOUT_STD_SLAMVAN4</layout>
      <coverBoundOffsets>SLAMVAN4_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout />
      <cameraName>SLAMVAN_FOLLOW_VEHICLE_CAMERA</cameraName>
      <aimCameraName>DEFAULT_THIRD_PERSON_VEHICLE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>DEFAULT_VEHICLE_BONNET_CAMERA</bonnetCameraName>
      <povCameraName>DEFAULT_POV_CAMERA_NO_REVERSE</povCameraName>
      <FirstPersonDriveByIKOffset x="0.050000" y="-0.140000" z="-0.060000" />
      <FirstPersonDriveByUnarmedIKOffset x="-0.025000" y="-0.050000" z="0.000000" />
	  <FirstPersonProjectileDriveByIKOffset x="0.040000" y="-0.100000" z="-0.040000" />
	  <FirstPersonProjectileDriveByPassengerIKOffset x="0.000000" y="-0.100000" z="0.000000" />
	  <FirstPersonDriveByRightPassengerIKOffset x="-0.050000" y="-0.140000" z="-0.060000" />
	  <FirstPersonDriveByRightPassengerUnarmedIKOffset x="-0.025000" y="-0.050000" z="0.000000" />
	  <FirstPersonMobilePhoneOffset x="0.146000" y="0.321000" z="0.518000" />
      <FirstPersonPassengerMobilePhoneOffset x="0.224000" y="0.279000" z="0.400000" />
      <PovCameraOffset x="0.000000" y="-0.205000" z="0.655000" />
      <PovCameraVerticalAdjustmentForRollCage value="0.000000" />
      <PovPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000" />
      <vfxInfoName>VFXVEHICLEINFO_CAR_GENERIC</vfxInfoName>
      <shouldUseCinematicViewMode value="true" />
      <shouldCameraTransitionOnClimbUpDown value="false" />
      <shouldCameraIgnoreExiting value="false" />
      <AllowPretendOccupants value="true" />
      <AllowJoyriding value="true" />
      <AllowSundayDriving value="true" />
      <AllowBodyColorMapping value="true" />
      <wheelScale value="0.250000" />
      <wheelScaleRear value="0.279600" />
      <dirtLevelMin value="0.000000" />
      <dirtLevelMax value="0.450000" />
      <envEffScaleMin value="1.000000" />
      <envEffScaleMax value="1.000000" />
      <envEffScaleMin2 value="1.000000" />
      <envEffScaleMax2 value="1.000000" />
      <damageMapScale value="0.300000" />
      <damageOffsetScale value="0.300000" />
      <diffuseTint value="0x00FFFFFF" />
      <steerWheelMult value="0.700000" />
      <HDTextureDist value="5.000000" />
      <lodDistances content="float_array">
        10.000000
        25.000000
        60.000000
        120.000000
        500.000000
        500.000000
      </lodDistances>
      <minSeatHeight value="0.844" />
      <identicalModelSpawnDistance value="20" />
      <maxNumOfSameColor value="1" />
      <defaultBodyHealth value="1000.000000" />
      <pretendOccupantsScale value="1.000000" />
      <visibleSpawnDistScale value="1.000000" />
      <trackerPathWidth value="2.000000" />
      <weaponForceMult value="1.000000" />
      <frequency value="30" />
      <swankness>SWANKNESS_4</swankness>
      <maxNum value="10" />
      <flags>FLAG_EXTRAS_STRONG FLAG_AVERAGE_CAR FLAG_HAS_INTERIOR_EXTRAS FLAG_CAN_HAVE_NEONS FLAG_USE_AIRCRAFT_STYLE_WEAPON_TARGETING FLAG_HAS_JUMP_MOD FLAG_HAS_SIDE_SHUNT FLAG_HAS_WEAPON_BLADE_MODS FLAG_HAS_WEAPON_SPIKE_MODS FLAG_HAS_RAMMING_SCOOP_MOD FLAG_HAS_RAMMING_BAR_MOD FLAG_HAS_NITROUS_MOD</flags>
      <type>VEHICLE_TYPE_CAR</type>
      <plateType>VPT_NONE</plateType>
	  <dashboardType>VDT_DUKES</dashboardType>
      <vehicleClass>VC_MUSCLE</vehicleClass>
      <wheelType>VWT_MUSCLE</wheelType>
      <trailers />
      <additionalTrailers />
      <drivers />
      <extraIncludes />
      <doorsWithCollisionWhenClosed />
      <driveableDoors />
      <bumpersNeedToCollideWithMap value="false" />
      <needsRopeTexture value="false" />
      <requiredExtras />
      <rewards />
      <cinematicPartCamera>
        <Item>WHEEL_FRONT_RIGHT_CAMERA</Item>
        <Item>WHEEL_FRONT_LEFT_CAMERA</Item>
        <Item>WHEEL_REAR_RIGHT_CAMERA</Item>
        <Item>WHEEL_REAR_LEFT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet />
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000" />
      <buoyancySphereSizeScale value="1.000000" />
      <pOverrideRagdollThreshold type="NULL" />
	  <firstPersonDrivebyData>
        <Item>STD_SLAMVAN4_FRONT_LEFT</Item>
        <Item>STD_SLAMVAN4_FRONT_RIGHT</Item>
      </firstPersonDrivebyData>
    </Item>   
    <Item>
      <modelName>issi6</modelName>
      <txdName>issi6</txdName>
      <handlingId>ISSI4</handlingId>
      <gameName>ISSI6</gameName>
      <vehicleMakeName>WEENY</vehicleMakeName>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected />
      <ptfxAssetName>weap_xs_vehicle_weapons</ptfxAssetName>
      <audioNameHash />
      <layout>LAYOUT_STD_ISSI4</layout>
      <coverBoundOffsets>ISSI4_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout />
      <cameraName>DEFAULT_FOLLOW_VEHICLE_CAMERA</cameraName>
      <aimCameraName>DEFAULT_THIRD_PERSON_VEHICLE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>VEHICLE_BONNET_CAMERA_STANDARD_LONG_EXTRA_LOW</bonnetCameraName>
      <povCameraName>REDUCED_NEAR_CLIP_POV_CAMERA</povCameraName>
      <FirstPersonDriveByIKOffset x="0.000000" y="-0.100000" z="-0.070000" />
      <FirstPersonDriveByUnarmedIKOffset x="-0.030000" y="-0.140000" z="-0.010000" />
	  <FirstPersonProjectileDriveByIKOffset x="0.000000" y="-0.050000" z="-0.055000" />
	  <FirstPersonProjectileDriveByPassengerIKOffset x="-0.040000" y="-0.130000" z="-0.055000" />
	  <FirstPersonDriveByRightPassengerIKOffset x="0.050000" y="-0.070000" z="-0.090000" />
	  <FirstPersonDriveByRightPassengerUnarmedIKOffset x="0.000000" y="-0.120000" z="-0.025000" />
	  <FirstPersonMobilePhoneOffset x="0.105000" y="0.170000" z="0.510000" />
      <FirstPersonPassengerMobilePhoneOffset x="0.221000" y="0.160000" z="0.403000" />
      <PovCameraOffset x="-0.020000" y="-0.225000" z="0.625000" />
      <PovCameraVerticalAdjustmentForRollCage value="0.000000" />
      <PovPassengerCameraOffset x="0.000000" y="0.000000" z="0.010000" />
      <vfxInfoName>VFXVEHICLEINFO_CAR_GENERIC_SMALL</vfxInfoName>
      <shouldUseCinematicViewMode value="true" />
      <shouldCameraTransitionOnClimbUpDown value="false" />
      <shouldCameraIgnoreExiting value="false" />
      <AllowPretendOccupants value="true" />
      <AllowJoyriding value="true" />
      <AllowSundayDriving value="true" />
      <AllowBodyColorMapping value="true" />
      <wheelScale value="0.191600" />
      <wheelScaleRear value="0.191600" />
      <dirtLevelMin value="0.000000" />
      <dirtLevelMax value="0.300000" />
      <envEffScaleMin value="1.000000" />
      <envEffScaleMax value="1.000000" />
      <envEffScaleMin2 value="1.000000" />
      <envEffScaleMax2 value="1.000000" />
      <damageMapScale value="0.30000" />
      <damageOffsetScale value="0.30000" />
      <diffuseTint value="0x00FFFFFF" />
      <steerWheelMult value="1.000000" />
      <HDTextureDist value="5.000000" />
      <lodDistances content="float_array">
        10.000000	
        25.000000	
        60.000000	
        120.000000	
        500.000000	
        500.000000
      </lodDistances>
      <minSeatHeight value="0.881" />
      <identicalModelSpawnDistance value="20" />
      <maxNumOfSameColor value="1" />
      <defaultBodyHealth value="1000.000000" />
      <pretendOccupantsScale value="1.000000" />
      <visibleSpawnDistScale value="1.000000" />
      <trackerPathWidth value="2.000000" />
      <weaponForceMult value="1.000000" />
      <frequency value="30" />
      <swankness>SWANKNESS_5</swankness>
      <maxNum value="5" />
      <flags>FLAG_SPORTS FLAG_SPAWN_ON_TRAILER FLAG_RICH_CAR FLAG_NO_BROKEN_DOWN_SCENARIO FLAG_CAN_HAVE_NEONS FLAG_HAS_JUMP_MOD FLAG_HAS_NITROUS_MOD FLAG_HAS_RAMMING_SCOOP_MOD FLAG_USE_AIRCRAFT_STYLE_WEAPON_TARGETING FLAG_HAS_WEAPON_SPIKE_MODS FLAG_HAS_SIDE_SHUNT FLAG_HAS_SUPERCHARGER</flags>
      <type>VEHICLE_TYPE_CAR</type>
      <plateType>VPT_BACK_PLATES</plateType>
      <dashboardType>VDT_RACE</dashboardType>
      <vehicleClass>VC_COMPACTS</vehicleClass>
      <wheelType>VWT_SPORT</wheelType>
      <trailers />
      <additionalTrailers />
      <drivers />
      <extraIncludes />
      <doorsWithCollisionWhenClosed />
      <driveableDoors />
      <bumpersNeedToCollideWithMap value="false" />
      <needsRopeTexture value="false" />
      <requiredExtras />
      <rewards />
      <cinematicPartCamera>
        <Item>WHEEL_FRONT_RIGHT_CAMERA</Item>
        <Item>WHEEL_FRONT_LEFT_CAMERA</Item>
        <Item>WHEEL_REAR_RIGHT_CAMERA</Item>
        <Item>WHEEL_REAR_LEFT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet />
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000" />
      <buoyancySphereSizeScale value="1.000000" />
      <pOverrideRagdollThreshold type="NULL" />
      <firstPersonDrivebyData>
        <Item>STD_ISSI4_FRONT_LEFT</Item>
        <Item>STD_ISSI4_FRONT_RIGHT</Item>
      </firstPersonDrivebyData>
	  <numSeatsOverride value="2" />
    </Item>
	<Item>      
      <modelName>cerberus2</modelName>
      <txdName>cerberus2</txdName>
      <handlingId>CERBERUS2</handlingId>
      <gameName>cerberus2</gameName>
      <vehicleMakeName>MTL</vehicleMakeName>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected />
      <ptfxAssetName>weap_xs_vehicle_weapons</ptfxAssetName>
      <audioNameHash />
      <layout>LAYOUT_TRUCK_CERBERUS</layout>
      <coverBoundOffsets>CERBERUS_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout />
      <cameraName>DEFAULT_FOLLOW_VEHICLE_CAMERA</cameraName>
      <aimCameraName>DEFAULT_THIRD_PERSON_NO_LOCKON_VEHICLE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>VEHICLE_BONNET_CAMERA_STANDARD_LONG</bonnetCameraName>
      <povCameraName>DEFAULT_POV_CAMERA_NO_REVERSE_LOOKAROUND_MID</povCameraName>
      <FirstPersonDriveByIKOffset x="0.050000" y="-0.070000" z="-0.010000" />
      <FirstPersonDriveByUnarmedIKOffset x="0.000000" y="-0.100000" z="0.000000" />
	  <FirstPersonProjectileDriveByIKOffset x="0.040000" y="-0.100000" z="-0.020000" />
	  <FirstPersonProjectileDriveByPassengerIKOffset x="-0.040000" y="-0.030000" z="-0.060000" />
	  <FirstPersonDriveByLeftPassengerIKOffset x="0.000000" y="0.000000" z="0.000000" />
	  <FirstPersonDriveByRightPassengerIKOffset x="-0.050000" y="-0.070000" z="-0.010000" />
	  <FirstPersonDriveByRightRearPassengerIKOffset x="0.000000" y="0.000000" z="0.000000" />
	  <FirstPersonDriveByLeftPassengerUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000" />
	  <FirstPersonDriveByRightPassengerUnarmedIKOffset x="0.000000" y="-0.100000" z="0.000000" />
	  <FirstPersonMobilePhoneOffset x="0.131000" y="0.250000" z="0.503000" />
      <FirstPersonPassengerMobilePhoneOffset x="0.240000" y="0.200000" z="0.495000" />
      <PovCameraOffset x="0.000000" y="-0.095000" z="0.610000" />
      <PovCameraVerticalAdjustmentForRollCage value="0.000000" />
      <PovPassengerCameraOffset x="0.000000" y="0.000000" z="0.100000" />
      <vfxInfoName>VFXVEHICLEINFO_TRUCK_RIG</vfxInfoName>
      <shouldUseCinematicViewMode value="true" />
      <shouldCameraTransitionOnClimbUpDown value="false" />
      <shouldCameraIgnoreExiting value="false" />
      <AllowPretendOccupants value="true" />
      <AllowJoyriding value="true" />
      <AllowSundayDriving value="true" />
      <AllowBodyColorMapping value="true" />
      <wheelScale value="0.353600" />
      <wheelScaleRear value="0.321000" />
      <dirtLevelMin value="0.000000" />
      <dirtLevelMax value="0.700000" />
      <envEffScaleMin value="1.000000" />
      <envEffScaleMax value="1.000000" />
      <envEffScaleMin2 value="1.000000" />
      <envEffScaleMax2 value="1.000000" />
      <damageMapScale value="0.000000" />
      <damageOffsetScale value="0.100000" />
      <diffuseTint value="0x00FFFFFF" />
      <steerWheelMult value="0.500000" />
      <HDTextureDist value="5.000000" />
      <lodDistances content="float_array">
        20.000000
        90.000000
        130.000000
        260.000000
        750.000000
        750.000000
      </lodDistances>
      <minSeatHeight value="0.852" />
      <identicalModelSpawnDistance value="20" />
      <maxNumOfSameColor value="10" />
      <defaultBodyHealth value="1000.000000" />
      <pretendOccupantsScale value="1.000000" />
      <visibleSpawnDistScale value="1.000000" />
      <trackerPathWidth value="2.000000" />
      <weaponForceMult value="1.000000" />
      <frequency value="50" />
      <swankness>SWANKNESS_1</swankness>
      <maxNum value="10" />
      <flags>FLAG_BIG FLAG_NO_BOOT FLAG_AVOID_TURNS FLAG_DELIVERY FLAG_EXTRAS_REQUIRE FLAG_PEDS_CAN_STAND_ON_TOP FLAG_USE_FAT_INTERIOR_LIGHT FLAG_IS_BULKY FLAG_BLOCK_FROM_ATTRACTOR_SCENARIO FLAG_HAS_BULLET_RESISTANT_GLASS FLAG_HAS_REAR_MOUNTED_TURRET FLAG_TURRET_MODS_ON_ROOF FLAG_RESET_TURRET_SEAT_HEADING FLAG_HAS_WEAPON_SPIKE_MODS FLAG_HAS_NITROUS_MOD FLAG_HAS_JUMP_MOD FLAG_HAS_SIDE_SHUNT FLAG_HAS_RAMMING_SCOOP_MOD FLAG_HAS_RAMMING_BAR_MOD FLAG_INCREASE_LOW_SPEED_TORQUE FLAG_DISABLE_DEFORMATION</flags>
      <type>VEHICLE_TYPE_CAR</type>
      <plateType>VPT_FRONT_AND_BACK_PLATES</plateType>
	  <dashboardType>VDT_TRUCK</dashboardType>
      <vehicleClass>VC_COMMERCIAL</vehicleClass>
      <wheelType>VWT_SPORT</wheelType>
      <trailers />
      <additionalTrailers />
      <drivers />
      <extraIncludes />
      <doorsWithCollisionWhenClosed>
        <Item>VEH_EXT_DOOR_DSIDE_R</Item>
        <Item>VEH_EXT_DOOR_PSIDE_R</Item>
      </doorsWithCollisionWhenClosed>
      <driveableDoors />
      <bumpersNeedToCollideWithMap value="false" />
      <needsRopeTexture value="false" />
      <requiredExtras />
      <rewards />
      <cinematicPartCamera>
        <Item>WHEEL_WIDE_REAR_RIGHT_CAMERA</Item>
        <Item>WHEEL_WIDE_REAR_LEFT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet />
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000" />
      <buoyancySphereSizeScale value="1.000000" />
      <pOverrideRagdollThreshold type="NULL" />
	  <firstPersonDrivebyData />
    </Item>  	
    <Item>      
      <modelName>cerberus3</modelName>
      <txdName>cerberus3</txdName>
      <handlingId>CERBERUS</handlingId>
      <gameName>cerberus3</gameName>
      <vehicleMakeName>MTL</vehicleMakeName>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected />
      <ptfxAssetName>weap_xs_vehicle_weapons</ptfxAssetName>
      <audioNameHash />
      <layout>LAYOUT_TRUCK_CERBERUS</layout>
      <coverBoundOffsets>CERBERUS_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout />
      <cameraName>DEFAULT_FOLLOW_VEHICLE_CAMERA</cameraName>
      <aimCameraName>DEFAULT_THIRD_PERSON_NO_LOCKON_VEHICLE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>VEHICLE_BONNET_CAMERA_STANDARD_LONG</bonnetCameraName>
      <povCameraName>DEFAULT_POV_CAMERA_NO_REVERSE_LOOKAROUND_MID</povCameraName>
      <FirstPersonDriveByIKOffset x="0.050000" y="-0.070000" z="-0.010000" />
      <FirstPersonDriveByUnarmedIKOffset x="0.000000" y="-0.100000" z="0.000000" />
	  <FirstPersonProjectileDriveByIKOffset x="0.040000" y="-0.100000" z="-0.020000" />
	  <FirstPersonProjectileDriveByPassengerIKOffset x="-0.040000" y="-0.030000" z="-0.060000" />
	  <FirstPersonDriveByLeftPassengerIKOffset x="0.000000" y="0.000000" z="0.000000" />
	  <FirstPersonDriveByRightPassengerIKOffset x="-0.050000" y="-0.070000" z="-0.010000" />
	  <FirstPersonDriveByRightRearPassengerIKOffset x="0.000000" y="0.000000" z="0.000000" />
	  <FirstPersonDriveByLeftPassengerUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000" />
	  <FirstPersonDriveByRightPassengerUnarmedIKOffset x="0.000000" y="-0.100000" z="0.000000" />
	  <FirstPersonMobilePhoneOffset x="0.131000" y="0.250000" z="0.503000" />
      <FirstPersonPassengerMobilePhoneOffset x="0.240000" y="0.200000" z="0.495000" />
      <PovCameraOffset x="0.000000" y="-0.095000" z="0.610000" />
      <PovCameraVerticalAdjustmentForRollCage value="0.000000" />
      <PovPassengerCameraOffset x="0.000000" y="0.000000" z="0.100000" />
      <vfxInfoName>VFXVEHICLEINFO_TRUCK_RIG</vfxInfoName>
      <shouldUseCinematicViewMode value="true" />
      <shouldCameraTransitionOnClimbUpDown value="false" />
      <shouldCameraIgnoreExiting value="false" />
      <AllowPretendOccupants value="true" />
      <AllowJoyriding value="true" />
      <AllowSundayDriving value="true" />
      <AllowBodyColorMapping value="true" />
      <wheelScale value="0.353600" />
      <wheelScaleRear value="0.321000" />
      <dirtLevelMin value="0.000000" />
      <dirtLevelMax value="0.700000" />
      <envEffScaleMin value="1.000000" />
      <envEffScaleMax value="1.000000" />
      <envEffScaleMin2 value="1.000000" />
      <envEffScaleMax2 value="1.000000" />
      <damageMapScale value="0.200000" />
      <damageOffsetScale value="0.200000" />
      <diffuseTint value="0x00FFFFFF" />
      <steerWheelMult value="0.500000" />
      <HDTextureDist value="5.000000" />
      <lodDistances content="float_array">
        20.000000
        90.000000
        130.000000
        260.000000
        750.000000
        750.000000
      </lodDistances>
      <minSeatHeight value="0.852" />
      <identicalModelSpawnDistance value="20" />
      <maxNumOfSameColor value="10" />
      <defaultBodyHealth value="1000.000000" />
      <pretendOccupantsScale value="1.000000" />
      <visibleSpawnDistScale value="1.000000" />
      <trackerPathWidth value="2.000000" />
      <weaponForceMult value="1.000000" />
      <frequency value="50" />
      <swankness>SWANKNESS_1</swankness>
      <maxNum value="10" />
      <flags>FLAG_BIG FLAG_NO_BOOT FLAG_AVOID_TURNS FLAG_DELIVERY FLAG_EXTRAS_REQUIRE FLAG_PEDS_CAN_STAND_ON_TOP FLAG_USE_FAT_INTERIOR_LIGHT FLAG_IS_BULKY FLAG_BLOCK_FROM_ATTRACTOR_SCENARIO FLAG_HAS_BULLET_RESISTANT_GLASS FLAG_HAS_REAR_MOUNTED_TURRET FLAG_TURRET_MODS_ON_ROOF FLAG_RESET_TURRET_SEAT_HEADING FLAG_HAS_WEAPON_SPIKE_MODS FLAG_HAS_NITROUS_MOD FLAG_HAS_JUMP_MOD FLAG_HAS_SIDE_SHUNT FLAG_HAS_RAMMING_SCOOP_MOD FLAG_HAS_RAMMING_BAR_MOD FLAG_INCREASE_LOW_SPEED_TORQUE</flags>
      <type>VEHICLE_TYPE_CAR</type>
      <plateType>VPT_FRONT_AND_BACK_PLATES</plateType>
	  <dashboardType>VDT_TRUCK</dashboardType>
      <vehicleClass>VC_COMMERCIAL</vehicleClass>
      <wheelType>VWT_SPORT</wheelType>
      <trailers />
      <additionalTrailers />
      <drivers />
      <extraIncludes />
      <doorsWithCollisionWhenClosed>
        <Item>VEH_EXT_DOOR_DSIDE_R</Item>
        <Item>VEH_EXT_DOOR_PSIDE_R</Item>
      </doorsWithCollisionWhenClosed>
      <driveableDoors />
      <bumpersNeedToCollideWithMap value="false" />
      <needsRopeTexture value="false" />
      <requiredExtras />
      <rewards />
      <cinematicPartCamera>
        <Item>WHEEL_WIDE_REAR_RIGHT_CAMERA</Item>
        <Item>WHEEL_WIDE_REAR_LEFT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet />
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000" />
      <buoyancySphereSizeScale value="1.000000" />
      <pOverrideRagdollThreshold type="NULL" />
	  <firstPersonDrivebyData />
    </Item>  
    <Item>
      <modelName>deathbike2</modelName>
      <txdName>deathbike2</txdName>
      <handlingId>DEATHBIKE2</handlingId>
      <gameName>DEATHBIKE2</gameName>
      <vehicleMakeName>WESTERN</vehicleMakeName>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected />
      <ptfxAssetName>weap_xs_vehicle_weapons</ptfxAssetName>
      <audioNameHash />
      <layout>LAYOUT_BIKE_FREEWAY</layout>
      <coverBoundOffsets>DEATHBIKE_COVER_OFFSET_INFO</coverBoundOffsets>
	  <POVTuningInfo>BAGGER_POV_TUNING</POVTuningInfo>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout />
      <cameraName>DEATHBIKE_FOLLOW_CAMERA</cameraName>
      <aimCameraName>BIKE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>BIKE_BAGGER_POV_CAMERA</bonnetCameraName>
      <povCameraName>BIKE_BAGGER_POV_CAMERA</povCameraName>
      <FirstPersonDriveByIKOffset x="0.000000" y="-0.100000" z="0.000000" />
      <FirstPersonDriveByUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000" />
	  <FirstPersonProjectileDriveByIKOffset x="0.000000" y="0.000000" z="0.000000" />
	  <FirstPersonProjectileDriveByPassengerIKOffset x="0.000000" y="0.000000" z="0.000000" />
	  <FirstPersonProjectileDriveByRearLeftIKOffset x="0.000000" y="0.000000" z="0.000000" />
	  <FirstPersonProjectileDriveByRearRightIKOffset x="0.000000" y="0.000000" z="0.000000" />
	  <FirstPersonDriveByLeftPassengerIKOffset x="0.000000" y="-0.060000" z="=0.030000" />
	  <FirstPersonDriveByRightPassengerIKOffset x="0.000000" y="0.000000" z="0.000000" />
	  <FirstPersonDriveByRightRearPassengerIKOffset x="0.000000" y="0.000000" z="0.000000" />
	  <FirstPersonDriveByLeftPassengerUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000" />
	  <FirstPersonDriveByRightPassengerUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000" />
	  <FirstPersonMobilePhoneOffset x="0.155000" y="0.315000" z="0.520000" />
      <FirstPersonPassengerMobilePhoneOffset x="0.136000" y="0.223000" z="0.425000" />
      <PovCameraOffset x="0.000000" y="0.000000" z="0.000000" />
      <PovCameraVerticalAdjustmentForRollCage value="0.000000" />
      <PovPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000" />
      <PovRearPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000" />
      <vfxInfoName>VFXVEHICLEINFO_MOTORBIKE_GENERIC</vfxInfoName>
      <shouldUseCinematicViewMode value="true" />
      <shouldCameraTransitionOnClimbUpDown value="false" />
      <shouldCameraIgnoreExiting value="false" />
      <AllowPretendOccupants value="false" />
      <AllowJoyriding value="true" />
      <AllowSundayDriving value="true" />
      <AllowBodyColorMapping value="true" />
      <wheelScale value="0.213500" />
      <wheelScaleRear value="0.185100" />
      <dirtLevelMin value="0.000000" />
      <dirtLevelMax value="0.500000" />
      <envEffScaleMin value="1.000000" />
      <envEffScaleMax value="1.000000" />
      <envEffScaleMin2 value="1.000000" />
      <envEffScaleMax2 value="1.000000" />
      <damageMapScale value="0.600000" />
      <damageOffsetScale value="1.000000" />
      <diffuseTint value="0x00FFFFFF" />
      <steerWheelMult value="0.350000" />
      <HDTextureDist value="5.000000" />
      <lodDistances content="float_array">
        10.000000
        25.000000
        50.000000
        100.000000
        500.000000
        500.000000
      </lodDistances>
      <minSeatHeight value="10" />
      <identicalModelSpawnDistance value="80" />
      <maxNumOfSameColor value="10" />
      <defaultBodyHealth value="1000.000000" />
      <pretendOccupantsScale value="1.000000" />
      <visibleSpawnDistScale value="1.000000" />
      <trackerPathWidth value="2.000000" />
      <weaponForceMult value="1.000000" />
      <frequency value="30" />
      <swankness>SWANKNESS_1</swankness>
      <maxNum value="3" />
      <flags>FLAG_NO_BOOT FLAG_EXTRAS_REQUIRE FLAG_EXTRAS_STRONG FLAG_IGNORE_ON_SIDE_CHECK FLAG_AVERAGE_CAR FLAG_HEADLIGHTS_USE_ACTUAL_BONE_POS FLAG_BIKE_CLAMP_PICKUP_LEAN_RATE FLAG_HAS_JUMP_MOD FLAG_HAS_NITROUS_MOD FLAG_HAS_WEAPON_BLADE_MODS FLAG_USE_AIRCRAFT_STYLE_WEAPON_TARGETING</flags>
      <type>VEHICLE_TYPE_BIKE</type>
      <plateType>VPT_NONE</plateType>
      <dashboardType>VDT_ZTYPE</dashboardType>
      <vehicleClass>VC_MOTORCYCLE</vehicleClass>
      <wheelType>VWT_BIKE</wheelType>
      <trailers />
      <additionalTrailers />
      <drivers />
      <extraIncludes />
      <doorsWithCollisionWhenClosed />
      <driveableDoors />
      <bumpersNeedToCollideWithMap value="false" />
      <needsRopeTexture value="false" />
      <requiredExtras>EXTRA_1 EXTRA_2 EXTRA_3</requiredExtras>
      <rewards />
      <cinematicPartCamera>
        <Item>WHEEL_REAR_LEFT_CAMERA</Item>
        <Item>WHEEL_REAR_RIGHT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet>Bike</NmBraceOverrideSet>
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000" />
      <buoyancySphereSizeScale value="1.000000" />
      <pOverrideRagdollThreshold type="NULL" />
      <firstPersonDrivebyData>
        <Item>BIKE_BAGGER_FRONT</Item>
        <Item>BIKE_BAGGER_REAR</Item>
      </firstPersonDrivebyData>
      <numSeatsOverride value="1" />
    </Item>    
	<Item>
      <modelName>dominator6</modelName>
      <txdName>dominator6</txdName>
      <handlingId>DOMINATOR4</handlingId>
      <gameName>DOMINATOR6</gameName>
      <vehicleMakeName>VAPID</vehicleMakeName>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected />
      <ptfxAssetName>weap_xs_vehicle_weapons</ptfxAssetName>
      <audioNameHash />
      <layout>LAYOUT_STD_ARENA_1HONLY</layout>
      <coverBoundOffsets>DOMINATOR4_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout />
      <cameraName>FOLLOW_CHEETAH_CAMERA</cameraName>
      <aimCameraName>DEFAULT_THIRD_PERSON_VEHICLE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>DEFAULT_VEHICLE_BONNET_CAMERA</bonnetCameraName>
      <povCameraName>REDUCED_NEAR_CLIP_POV_CAMERA_NO_REVERSE</povCameraName>
      <FirstPersonDriveByIKOffset x="0.060000" y="-0.130000" z="-0.030000" />
      <FirstPersonDriveByUnarmedIKOffset x="0.000000" y="-0.025000" z="0.000000" />
	  <FirstPersonProjectileDriveByIKOffset x="0.000000" y="-0.120000" z="0.030000" />
	  <FirstPersonProjectileDriveByPassengerIKOffset x="0.000000" y="-0.120000" z="0.030000" />
	  <FirstPersonDriveByRightPassengerIKOffset x="-0.060000" y="-0.130000" z="-0.030000" />
	  <FirstPersonDriveByRightPassengerUnarmedIKOffset x="0.000000" y="-0.035000" z="0.000000" />
	  <FirstPersonMobilePhoneOffset x="0.133000" y="0.208000" z="0.533000" />
      <FirstPersonPassengerMobilePhoneOffset x="0.216000" y="0.173000" z="0.415000" />
      <PovCameraOffset x="0.000000" y="-0.195000" z="0.660000" />
      <PovCameraVerticalAdjustmentForRollCage value="0.000000" />
      <PovPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000" />
      <vfxInfoName>VFXVEHICLEINFO_CAR_GENERIC</vfxInfoName>
      <shouldUseCinematicViewMode value="true" />
      <shouldCameraTransitionOnClimbUpDown value="false" />
      <shouldCameraIgnoreExiting value="false" />
      <AllowPretendOccupants value="true" />
      <AllowJoyriding value="true" />
      <AllowSundayDriving value="true" />
      <AllowBodyColorMapping value="true" />
      <wheelScale value="0.279900" />
      <wheelScaleRear value="0.279900" />
      <dirtLevelMin value="0.000000" />
      <dirtLevelMax value="0.450000" />
      <envEffScaleMin value="1.000000" />
      <envEffScaleMax value="1.000000" />
      <envEffScaleMin2 value="1.000000" />
      <envEffScaleMax2 value="1.000000" />
      <damageMapScale value="0.30000" />
      <damageOffsetScale value="0.30000" />
      <diffuseTint value="0x00FFFFFF" />
      <steerWheelMult value="0.700000" />
      <HDTextureDist value="5.000000" />
      <lodDistances content="float_array">
        12.000000
        30.000000
        60.000000
        120.000000
        500.000000
        500.000000
      </lodDistances>
      <identicalModelSpawnDistance value="20" />
      <maxNumOfSameColor value="10" />
      <defaultBodyHealth value="1000.000000" />
      <pretendOccupantsScale value="1.000000" />
      <visibleSpawnDistScale value="1.000000" />
      <trackerPathWidth value="2.000000" />
      <weaponForceMult value="1.000000" />
      <frequency value="90" />
      <swankness>SWANKNESS_2</swankness>
      <maxNum value="20" />
      <flags>FLAG_SPAWN_ON_TRAILER FLAG_PARKING_SENSORS FLAG_AVERAGE_CAR FLAG_RECESSED_HEADLIGHT_CORONAS FLAG_HAS_INTERIOR_EXTRAS FLAG_HAS_RAMMING_SCOOP_MOD FLAG_HAS_SUPER_BRAKES_MOD FLAG_HAS_WEAPON_BLADE_MODS FLAG_HAS_WEAPON_SPIKE_MODS FLAG_HAS_NITROUS_MOD FLAG_USE_AIRCRAFT_STYLE_WEAPON_TARGETING FLAG_HAS_JUMP_MOD FLAG_HAS_TOMBSTONE FLAG_HAS_SIDE_SHUNT FLAG_HAS_FRONT_SPIKE_MOD FLAG_HAS_SUPERCHARGER FLAG_DISABLE_THROUGH_WINDSCREEN</flags>
      <type>VEHICLE_TYPE_CAR</type>
      <plateType>VPT_BACK_PLATES</plateType>
      <dashboardType>VDT_DUKES</dashboardType>
      <vehicleClass>VC_MUSCLE</vehicleClass>
      <wheelType>VWT_MUSCLE</wheelType>
      <trailers />
      <additionalTrailers />
      <drivers />
      <extraIncludes />
      <doorsWithCollisionWhenClosed />
      <driveableDoors />
      <bumpersNeedToCollideWithMap value="false" />
      <needsRopeTexture value="false" />
      <requiredExtras />
      <rewards />
      <cinematicPartCamera>
        <Item>WHEEL_FRONT_RIGHT_CAMERA</Item>
        <Item>WHEEL_FRONT_LEFT_CAMERA</Item>
        <Item>WHEEL_REAR_RIGHT_CAMERA</Item>
        <Item>WHEEL_REAR_LEFT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet />
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000" />
      <buoyancySphereSizeScale value="1.000000" />
      <pOverrideRagdollThreshold type="NULL" />
      <firstPersonDrivebyData>
        <Item>STD_DOMINATOR4_FRONT_LEFT</Item>
        <Item>STD_DOMINATOR4_FRONT_RIGHT</Item>
      </firstPersonDrivebyData>
    </Item>  
    <Item>
      <modelName>deathbike3</modelName>
      <txdName>deathbike3</txdName>
      <handlingId>DEATHBIKE</handlingId>
      <gameName>DEATHBIKE3</gameName>
      <vehicleMakeName>WESTERN</vehicleMakeName>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected />
      <ptfxAssetName>weap_xs_vehicle_weapons</ptfxAssetName>
      <audioNameHash />
      <layout>LAYOUT_BIKE_FREEWAY</layout>
      <coverBoundOffsets>DEATHBIKE_COVER_OFFSET_INFO</coverBoundOffsets>
	  <POVTuningInfo>BAGGER_POV_TUNING</POVTuningInfo>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout />
      <cameraName>DEATHBIKE_FOLLOW_CAMERA</cameraName>
      <aimCameraName>BIKE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>BIKE_BAGGER_POV_CAMERA</bonnetCameraName>
      <povCameraName>BIKE_BAGGER_POV_CAMERA</povCameraName>
      <FirstPersonDriveByIKOffset x="0.000000" y="-0.100000" z="0.000000" />
      <FirstPersonDriveByUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000" />
	  <FirstPersonProjectileDriveByIKOffset x="0.000000" y="0.000000" z="0.000000" />
	  <FirstPersonProjectileDriveByPassengerIKOffset x="0.000000" y="0.000000" z="0.000000" />
	  <FirstPersonProjectileDriveByRearLeftIKOffset x="0.000000" y="0.000000" z="0.000000" />
	  <FirstPersonProjectileDriveByRearRightIKOffset x="0.000000" y="0.000000" z="0.000000" />
	  <FirstPersonDriveByLeftPassengerIKOffset x="0.000000" y="-0.060000" z="=0.030000" />
	  <FirstPersonDriveByRightPassengerIKOffset x="0.000000" y="0.000000" z="0.000000" />
	  <FirstPersonDriveByRightRearPassengerIKOffset x="0.000000" y="0.000000" z="0.000000" />
	  <FirstPersonDriveByLeftPassengerUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000" />
	  <FirstPersonDriveByRightPassengerUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000" />
	  <FirstPersonMobilePhoneOffset x="0.155000" y="0.315000" z="0.520000" />
      <FirstPersonPassengerMobilePhoneOffset x="0.136000" y="0.223000" z="0.425000" />
      <PovCameraOffset x="0.000000" y="0.000000" z="0.000000" />
      <PovCameraVerticalAdjustmentForRollCage value="0.000000" />
      <PovPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000" />
      <PovRearPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000" />
      <vfxInfoName>VFXVEHICLEINFO_MOTORBIKE_GENERIC</vfxInfoName>
      <shouldUseCinematicViewMode value="true" />
      <shouldCameraTransitionOnClimbUpDown value="false" />
      <shouldCameraIgnoreExiting value="false" />
      <AllowPretendOccupants value="false" />
      <AllowJoyriding value="true" />
      <AllowSundayDriving value="true" />
      <AllowBodyColorMapping value="true" />
      <wheelScale value="0.213500" />
      <wheelScaleRear value="0.185100" />
      <dirtLevelMin value="0.000000" />
      <dirtLevelMax value="0.850000" />
      <envEffScaleMin value="1.000000" />
      <envEffScaleMax value="1.000000" />
      <envEffScaleMin2 value="1.000000" />
      <envEffScaleMax2 value="1.000000" />
      <damageMapScale value="0.600000" />
      <damageOffsetScale value="1.000000" />
      <diffuseTint value="0x00FFFFFF" />
      <steerWheelMult value="0.350000" />
      <HDTextureDist value="5.000000" />
      <lodDistances content="float_array">
        10.000000
        25.000000
        50.000000
        100.000000
        500.000000
        500.000000
      </lodDistances>
      <minSeatHeight value="10" />
      <identicalModelSpawnDistance value="80" />
      <maxNumOfSameColor value="10" />
      <defaultBodyHealth value="1000.000000" />
      <pretendOccupantsScale value="1.000000" />
      <visibleSpawnDistScale value="1.000000" />
      <trackerPathWidth value="2.000000" />
      <weaponForceMult value="1.000000" />
      <frequency value="30" />
      <swankness>SWANKNESS_1</swankness>
      <maxNum value="3" />
      <flags>FLAG_NO_BOOT FLAG_EXTRAS_REQUIRE FLAG_EXTRAS_STRONG FLAG_IGNORE_ON_SIDE_CHECK FLAG_AVERAGE_CAR FLAG_HEADLIGHTS_USE_ACTUAL_BONE_POS FLAG_BIKE_CLAMP_PICKUP_LEAN_RATE FLAG_HAS_JUMP_MOD FLAG_HAS_NITROUS_MOD FLAG_HAS_WEAPON_BLADE_MODS FLAG_USE_AIRCRAFT_STYLE_WEAPON_TARGETING</flags>
      <type>VEHICLE_TYPE_BIKE</type>
      <plateType>VPT_NONE</plateType>
      <dashboardType>VDT_ZTYPE</dashboardType>
      <vehicleClass>VC_MOTORCYCLE</vehicleClass>
      <wheelType>VWT_BIKE</wheelType>
      <trailers />
      <additionalTrailers />
      <drivers />
      <extraIncludes />
      <doorsWithCollisionWhenClosed />
      <driveableDoors />
      <bumpersNeedToCollideWithMap value="false" />
      <needsRopeTexture value="false" />
      <requiredExtras>EXTRA_1 EXTRA_2 EXTRA_3</requiredExtras>
      <rewards />
      <cinematicPartCamera>
        <Item>WHEEL_REAR_LEFT_CAMERA</Item>
        <Item>WHEEL_REAR_RIGHT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet>Bike</NmBraceOverrideSet>
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000" />
      <buoyancySphereSizeScale value="1.000000" />
      <pOverrideRagdollThreshold type="NULL" />
	  <firstPersonDrivebyData>
        <Item>BIKE_BAGGER_FRONT</Item>
        <Item>BIKE_BAGGER_REAR</Item>
      </firstPersonDrivebyData>
	  <numSeatsOverride value="1" />
    </Item>  
	<Item>
      <modelName>impaler4</modelName>
      <txdName>impaler4</txdName>
      <handlingId>IMPALER2</handlingId>
      <gameName>IMPALER4</gameName>
      <vehicleMakeName>DECLASSE</vehicleMakeName>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected />
      <ptfxAssetName>weap_xs_vehicle_weapons</ptfxAssetName>
      <audioNameHash />
      <layout>LAYOUT_STD_ARENA_1HONLY</layout>
      <coverBoundOffsets>IMPALER_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout />
      <cameraName>FOLLOW_CHEETAH_CAMERA</cameraName>
      <aimCameraName>DEFAULT_THIRD_PERSON_VEHICLE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>VEHICLE_BONNET_CAMERA_STANDARD_LONG_DEVIANT</bonnetCameraName>
      <povCameraName>REDUCED_NEAR_CLIP_POV_CAMERA</povCameraName>
      <FirstPersonDriveByIKOffset x="0.020000" y="-0.065000" z="-0.050000" />
      <FirstPersonDriveByUnarmedIKOffset x="0.000000" y="-0.100000" z="0.000000" />
	  <FirstPersonProjectileDriveByIKOffset x="0.000000" y="-0.130000" z="-0.050000" />
	  <FirstPersonProjectileDriveByPassengerIKOffset x="0.000000" y="-0.100000" z="0.000000" />
	  <FirstPersonDriveByRightPassengerIKOffset x="-0.020000" y="-0.065000" z="-0.050000" />
	  <FirstPersonDriveByRightPassengerUnarmedIKOffset x="0.000000" y="-0.100000" z="0.000000" />
	  <FirstPersonMobilePhoneOffset x="0.146000" y="0.220000" z="0.510000" />
      <FirstPersonPassengerMobilePhoneOffset x="0.234000" y="0.169000" z="0.395000" />
      <PovCameraOffset x="0.000000" y="-0.195000" z="0.640000" />
      <PovCameraVerticalAdjustmentForRollCage value="0.000000" />
      <PovPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000" />
      <PovRearPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000" />
      <vfxInfoName>VFXVEHICLEINFO_CAR_GENERIC</vfxInfoName>
      <shouldUseCinematicViewMode value="true" />
      <shouldCameraTransitionOnClimbUpDown value="false" />
      <shouldCameraIgnoreExiting value="false" />
      <AllowPretendOccupants value="true" />
      <AllowJoyriding value="true" />
      <AllowSundayDriving value="true" />
      <AllowBodyColorMapping value="true" />
      <wheelScale value="0.225000" />
      <wheelScaleRear value="0.201800" />
      <dirtLevelMin value="0.000000" />
      <dirtLevelMax value="0.450000" />
      <envEffScaleMin value="1.000000" />
      <envEffScaleMax value="1.000000" />
      <envEffScaleMin2 value="1.000000" />
      <envEffScaleMax2 value="1.000000" />
      <damageMapScale value="0.400000" />
      <damageOffsetScale value="0.400000" />
      <diffuseTint value="0x00FFFFFF" />
      <steerWheelMult value="0.700000" />
      <HDTextureDist value="5.000000" />
      <lodDistances content="float_array">
        10.000000
        25.000000
        60.000000
        120.000000
        500.000000
        500.000000
      </lodDistances>
      <minSeatHeight value="0.844" />
      <identicalModelSpawnDistance value="20" />
      <maxNumOfSameColor value="1" />
      <defaultBodyHealth value="1000.000000" />
      <pretendOccupantsScale value="1.000000" />
      <visibleSpawnDistScale value="1.000000" />
      <trackerPathWidth value="2.000000" />
      <weaponForceMult value="1.000000" />
      <frequency value="30" />
      <swankness>SWANKNESS_4</swankness>
      <maxNum value="10" />
      <flags>FLAG_RECESSED_HEADLIGHT_CORONAS FLAG_EXTRAS_STRONG FLAG_AVERAGE_CAR FLAG_HAS_INTERIOR_EXTRAS FLAG_CAN_HAVE_NEONS FLAG_HAS_JUMP_MOD FLAG_HAS_NITROUS_MOD FLAG_HAS_RAMMING_SCOOP_MOD FLAG_USE_AIRCRAFT_STYLE_WEAPON_TARGETING FLAG_HAS_SIDE_SHUNT FLAG_HAS_WEAPON_SPIKE_MODS FLAG_HAS_SUPERCHARGER FLAG_INCREASE_CAMBER_WITH_SUSPENSION_MOD</flags>
      <type>VEHICLE_TYPE_CAR</type>
      <plateType>VPT_FRONT_AND_BACK_PLATES</plateType>
	  <dashboardType>VDT_DUKES</dashboardType>
      <vehicleClass>VC_MUSCLE</vehicleClass>
      <wheelType>VWT_MUSCLE</wheelType>
      <trailers />
      <additionalTrailers />
      <drivers />
      <extraIncludes />
      <doorsWithCollisionWhenClosed />
      <driveableDoors />
      <bumpersNeedToCollideWithMap value="false" />
      <needsRopeTexture value="false" />
      <requiredExtras />
      <rewards />
      <cinematicPartCamera>
        <Item>WHEEL_FRONT_RIGHT_CAMERA</Item>
        <Item>WHEEL_FRONT_LEFT_CAMERA</Item>
        <Item>WHEEL_REAR_RIGHT_CAMERA</Item>
        <Item>WHEEL_REAR_LEFT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet />
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000" />
      <buoyancySphereSizeScale value="1.000000" />
      <pOverrideRagdollThreshold type="NULL" />
	  <firstPersonDrivebyData>
        <Item>STD_IMPALER2_FRONT_LEFT</Item>
        <Item>STD_IMPALER2_FRONT_RIGHT</Item>
      </firstPersonDrivebyData>
    </Item>    
    <Item>
      <modelName>slamvan4</modelName>
      <txdName>slamvan4</txdName>
      <handlingId>SLAMVAN4</handlingId>
      <gameName>SLAMVAN4</gameName>
      <vehicleMakeName>VAPID</vehicleMakeName>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected />
      <ptfxAssetName>weap_xs_vehicle_weapons</ptfxAssetName>
      <audioNameHash />
      <layout>LAYOUT_STD_SLAMVAN4</layout>
      <coverBoundOffsets>SLAMVAN4_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout />
      <cameraName>SLAMVAN_FOLLOW_VEHICLE_CAMERA</cameraName>
      <aimCameraName>DEFAULT_THIRD_PERSON_VEHICLE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>DEFAULT_VEHICLE_BONNET_CAMERA</bonnetCameraName>
      <povCameraName>DEFAULT_POV_CAMERA_NO_REVERSE</povCameraName>
      <FirstPersonDriveByIKOffset x="0.050000" y="-0.140000" z="-0.060000" />
      <FirstPersonDriveByUnarmedIKOffset x="-0.025000" y="-0.050000" z="0.000000" />
	  <FirstPersonProjectileDriveByIKOffset x="0.040000" y="-0.100000" z="-0.040000" />
	  <FirstPersonProjectileDriveByPassengerIKOffset x="0.000000" y="-0.100000" z="0.000000" />
	  <FirstPersonDriveByRightPassengerIKOffset x="-0.050000" y="-0.140000" z="-0.060000" />
	  <FirstPersonDriveByRightPassengerUnarmedIKOffset x="-0.025000" y="-0.050000" z="0.000000" />
	  <FirstPersonMobilePhoneOffset x="0.146000" y="0.321000" z="0.518000" />
      <FirstPersonPassengerMobilePhoneOffset x="0.224000" y="0.279000" z="0.400000" />
      <PovCameraOffset x="0.000000" y="-0.205000" z="0.655000" />
      <PovCameraVerticalAdjustmentForRollCage value="0.000000" />
      <PovPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000" />
      <vfxInfoName>VFXVEHICLEINFO_CAR_GENERIC</vfxInfoName>
      <shouldUseCinematicViewMode value="true" />
      <shouldCameraTransitionOnClimbUpDown value="false" />
      <shouldCameraIgnoreExiting value="false" />
      <AllowPretendOccupants value="true" />
      <AllowJoyriding value="true" />
      <AllowSundayDriving value="true" />
      <AllowBodyColorMapping value="true" />
      <wheelScale value="0.209700" />
      <wheelScaleRear value="0.279600" />
      <dirtLevelMin value="0.000000" />
      <dirtLevelMax value="0.450000" />
      <envEffScaleMin value="1.000000" />
      <envEffScaleMax value="1.000000" />
      <envEffScaleMin2 value="1.000000" />
      <envEffScaleMax2 value="1.000000" />
      <damageMapScale value="0.300000" />
      <damageOffsetScale value="0.300000" />
      <diffuseTint value="0x00FFFFFF" />
      <steerWheelMult value="0.700000" />
      <HDTextureDist value="5.000000" />
      <lodDistances content="float_array">
        10.000000
        25.000000
        60.000000
        120.000000
        500.000000
        500.000000
      </lodDistances>
      <minSeatHeight value="0.844" />
      <identicalModelSpawnDistance value="20" />
      <maxNumOfSameColor value="1" />
      <defaultBodyHealth value="1000.000000" />
      <pretendOccupantsScale value="1.000000" />
      <visibleSpawnDistScale value="1.000000" />
      <trackerPathWidth value="2.000000" />
      <weaponForceMult value="1.000000" />
      <frequency value="30" />
      <swankness>SWANKNESS_4</swankness>
      <maxNum value="10" />
      <flags>FLAG_EXTRAS_STRONG FLAG_AVERAGE_CAR FLAG_HAS_INTERIOR_EXTRAS FLAG_CAN_HAVE_NEONS FLAG_USE_AIRCRAFT_STYLE_WEAPON_TARGETING FLAG_HAS_JUMP_MOD FLAG_HAS_SIDE_SHUNT FLAG_HAS_WEAPON_BLADE_MODS FLAG_HAS_WEAPON_SPIKE_MODS FLAG_HAS_RAMMING_SCOOP_MOD FLAG_HAS_RAMMING_BAR_MOD FLAG_HAS_NITROUS_MOD</flags>
      <type>VEHICLE_TYPE_CAR</type>
      <plateType>VPT_NONE</plateType>
	  <dashboardType>VDT_DUKES</dashboardType>
      <vehicleClass>VC_MUSCLE</vehicleClass>
      <wheelType>VWT_MUSCLE</wheelType>
      <trailers />
      <additionalTrailers />
      <drivers />
      <extraIncludes />
      <doorsWithCollisionWhenClosed />
      <driveableDoors />
      <bumpersNeedToCollideWithMap value="false" />
      <needsRopeTexture value="false" />
      <requiredExtras />
      <rewards />
      <cinematicPartCamera>
        <Item>WHEEL_FRONT_RIGHT_CAMERA</Item>
        <Item>WHEEL_FRONT_LEFT_CAMERA</Item>
        <Item>WHEEL_REAR_RIGHT_CAMERA</Item>
        <Item>WHEEL_REAR_LEFT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet />
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000" />
      <buoyancySphereSizeScale value="1.000000" />
      <pOverrideRagdollThreshold type="NULL" />
	  <firstPersonDrivebyData>
        <Item>STD_SLAMVAN4_FRONT_LEFT</Item>
        <Item>STD_SLAMVAN4_FRONT_RIGHT</Item>
      </firstPersonDrivebyData>
    </Item>
    <Item>
      <modelName>slamvan5</modelName>
      <txdName>slamvan5</txdName>
      <handlingId>SLAMVAN5</handlingId>
      <gameName>SLAMVAN5</gameName>
      <vehicleMakeName>VAPID</vehicleMakeName>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected />
      <ptfxAssetName>weap_xs_vehicle_weapons</ptfxAssetName>
      <audioNameHash />
      <layout>LAYOUT_STD_SLAMVAN4</layout>
      <coverBoundOffsets>SLAMVAN4_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout />
      <cameraName>SLAMVAN_FOLLOW_VEHICLE_CAMERA</cameraName>
      <aimCameraName>DEFAULT_THIRD_PERSON_VEHICLE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>DEFAULT_VEHICLE_BONNET_CAMERA</bonnetCameraName>
      <povCameraName>DEFAULT_POV_CAMERA_NO_REVERSE</povCameraName>
      <FirstPersonDriveByIKOffset x="0.050000" y="-0.140000" z="-0.060000" />
      <FirstPersonDriveByUnarmedIKOffset x="-0.025000" y="-0.050000" z="0.000000" />
	  <FirstPersonProjectileDriveByIKOffset x="0.040000" y="-0.100000" z="-0.040000" />
	  <FirstPersonProjectileDriveByPassengerIKOffset x="0.000000" y="-0.100000" z="0.000000" />
	  <FirstPersonDriveByRightPassengerIKOffset x="-0.050000" y="-0.140000" z="-0.060000" />
	  <FirstPersonDriveByRightPassengerUnarmedIKOffset x="-0.025000" y="-0.050000" z="0.000000" />
	  <FirstPersonMobilePhoneOffset x="0.146000" y="0.321000" z="0.518000" />
      <FirstPersonPassengerMobilePhoneOffset x="0.224000" y="0.279000" z="0.400000" />
      <PovCameraOffset x="0.000000" y="-0.205000" z="0.655000" />
      <PovCameraVerticalAdjustmentForRollCage value="0.000000" />
      <PovPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000" />
      <vfxInfoName>VFXVEHICLEINFO_CAR_GENERIC</vfxInfoName>
      <shouldUseCinematicViewMode value="true" />
      <shouldCameraTransitionOnClimbUpDown value="false" />
      <shouldCameraIgnoreExiting value="false" />
      <AllowPretendOccupants value="true" />
      <AllowJoyriding value="true" />
      <AllowSundayDriving value="true" />
      <AllowBodyColorMapping value="true" />
      <wheelScale value="0.210000" />
      <wheelScaleRear value="0.279600" />
      <dirtLevelMin value="0.000000" />
      <dirtLevelMax value="0.450000" />
      <envEffScaleMin value="1.000000" />
      <envEffScaleMax value="1.000000" />
      <envEffScaleMin2 value="1.000000" />
      <envEffScaleMax2 value="1.000000" />
      <damageMapScale value="0.000000" />
      <damageOffsetScale value="0.100000" />
      <diffuseTint value="0x00FFFFFF" />
      <steerWheelMult value="0.700000" />
      <HDTextureDist value="5.000000" />
      <lodDistances content="float_array">
        10.000000
        25.000000
        60.000000
        120.000000
        500.000000
        500.000000
      </lodDistances>
      <minSeatHeight value="0.844" />
      <identicalModelSpawnDistance value="20" />
      <maxNumOfSameColor value="1" />
      <defaultBodyHealth value="1000.000000" />
      <pretendOccupantsScale value="1.000000" />
      <visibleSpawnDistScale value="1.000000" />
      <trackerPathWidth value="2.000000" />
      <weaponForceMult value="1.000000" />
      <frequency value="30" />
      <swankness>SWANKNESS_4</swankness>
      <maxNum value="10" />
      <flags>FLAG_EXTRAS_STRONG FLAG_AVERAGE_CAR FLAG_HAS_INTERIOR_EXTRAS FLAG_CAN_HAVE_NEONS FLAG_USE_AIRCRAFT_STYLE_WEAPON_TARGETING FLAG_HAS_JUMP_MOD FLAG_HAS_SIDE_SHUNT FLAG_HAS_WEAPON_BLADE_MODS FLAG_HAS_WEAPON_SPIKE_MODS FLAG_HAS_RAMMING_SCOOP_MOD FLAG_HAS_RAMMING_BAR_MOD FLAG_HAS_NITROUS_MOD FLAG_DISABLE_DEFORMATION</flags>
      <type>VEHICLE_TYPE_CAR</type>
      <plateType>VPT_NONE</plateType>
	  <dashboardType>VDT_DUKES</dashboardType>
      <vehicleClass>VC_MUSCLE</vehicleClass>
      <wheelType>VWT_MUSCLE</wheelType>
      <trailers />
      <additionalTrailers />
      <drivers />
      <extraIncludes />
      <doorsWithCollisionWhenClosed />
      <driveableDoors />
      <bumpersNeedToCollideWithMap value="false" />
      <needsRopeTexture value="false" />
      <requiredExtras />
      <rewards />
      <cinematicPartCamera>
        <Item>WHEEL_FRONT_RIGHT_CAMERA</Item>
        <Item>WHEEL_FRONT_LEFT_CAMERA</Item>
        <Item>WHEEL_REAR_RIGHT_CAMERA</Item>
        <Item>WHEEL_REAR_LEFT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet />
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000" />
      <buoyancySphereSizeScale value="1.000000" />
      <pOverrideRagdollThreshold type="NULL" />
	  <firstPersonDrivebyData>
        <Item>STD_SLAMVAN4_FRONT_LEFT</Item>
        <Item>STD_SLAMVAN4_FRONT_RIGHT</Item>
      </firstPersonDrivebyData>
    </Item> 	
    <Item>
      <modelName>brutus</modelName>
      <txdName>brutus</txdName>
      <handlingId>BRUTUS</handlingId>
      <gameName>BRUTUS</gameName>
      <vehicleMakeName>DECLASSE</vehicleMakeName>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected />
      <ptfxAssetName>weap_xs_vehicle_weapons</ptfxAssetName>
      <audioNameHash />
      <layout>LAYOUT_VAN_BRUTUS</layout>
      <coverBoundOffsets>BRUTUS_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout />
      <cameraName>DEFAULT_FOLLOW_VEHICLE_CAMERA</cameraName>
      <aimCameraName>MID_BOX_VEHICLE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>BRUTUS_BONNET_CAMERA</bonnetCameraName>
      <povCameraName>DEFAULT_POV_CAMERA_NO_REVERSE_LOOKAROUND_MID</povCameraName>
      <FirstPersonDriveByIKOffset x="0.000000" y="-0.050000" z="-0.040000" />
      <FirstPersonDriveByUnarmedIKOffset x="0.000000" y="0.000000" z="-0.020000" />
	  <FirstPersonProjectileDriveByIKOffset x="0.035000" y="-0.130000" z="-0.020000" />
	  <FirstPersonProjectileDriveByPassengerIKOffset x="0.035000" y="-0.130000" z="-0.020000" />
	  <FirstPersonDriveByRightPassengerIKOffset x="-0.040000" y="-0.080000" z="-0.060000" />
	  <FirstPersonDriveByRightPassengerUnarmedIKOffset x="0.000000" y="0.000000" z="-0.020000" />
	  <FirstPersonMobilePhoneOffset x="0.140000" y="0.218000" z="0.515000" />
      <FirstPersonPassengerMobilePhoneOffset x="0.240000" y="0.203000" z="0.465000" />
      <PovCameraOffset x="0.000000" y="-0.145000" z="0.630000" />
      <PovCameraVerticalAdjustmentForRollCage value="0.000000" />
      <PovPassengerCameraOffset x="0.000000" y="0.000000" z="0.070000" />
      <vfxInfoName>VFXVEHICLEINFO_CAR_OFFROAD</vfxInfoName>
      <shouldUseCinematicViewMode value="true" />
      <shouldCameraTransitionOnClimbUpDown value="false" />
      <shouldCameraIgnoreExiting value="false" />
      <AllowPretendOccupants value="true" />
      <AllowJoyriding value="true" />
      <AllowSundayDriving value="true" />
      <AllowBodyColorMapping value="true" />
      <wheelScale value="0.278300" />
      <wheelScaleRear value="0.278300" />
      <dirtLevelMin value="0.000000" />
      <dirtLevelMax value="0.850000" />
      <envEffScaleMin value="1.000000" />
      <envEffScaleMax value="1.000000" />
      <envEffScaleMin2 value="1.000000" />
      <envEffScaleMax2 value="1.000000" />
      <damageMapScale value="0.400000" />
      <damageOffsetScale value="0.400000" />
      <diffuseTint value="0x00FFFFFF" />
      <steerWheelMult value="1.000000" />
      <HDTextureDist value="5.000000" />
      <lodDistances content="float_array">
        20.000000
        35.000000
        90.000000
        180.000000
        500.000000
        500.000000
      </lodDistances>
      <minSeatHeight value="1.006" />
      <identicalModelSpawnDistance value="20" />
      <maxNumOfSameColor value="10" />
      <defaultBodyHealth value="1000.000000" />
      <pretendOccupantsScale value="1.000000" />
      <visibleSpawnDistScale value="1.000000" />
      <trackerPathWidth value="2.000000" />
      <weaponForceMult value="1.000000" />
      <frequency value="30" />
      <swankness>SWANKNESS_1</swankness>
      <maxNum value="5" />
      <flags>FLAG_AVERAGE_CAR FLAG_POOR_CAR FLAG_IS_OFFROAD_VEHICLE FLAG_HEADLIGHTS_USE_ACTUAL_BONE_POS FLAG_IS_BULKY FLAG_HAS_INTERIOR_EXTRAS FLAG_RECESSED_HEADLIGHT_CORONAS FLAG_HAS_NITROUS_MOD FLAG_HAS_JUMP_MOD FLAG_HAS_SIDE_SHUNT FLAG_HAS_RAMMING_SCOOP_MOD FLAG_HAS_WEAPON_BLADE_MODS FLAG_USE_AIRCRAFT_STYLE_WEAPON_TARGETING FLAG_HAS_RAMMING_BAR_MOD</flags>
      <type>VEHICLE_TYPE_CAR</type>
      <plateType>VPT_BACK_PLATES</plateType>
      <dashboardType>VDT_SPEEDO</dashboardType> 
      <vehicleClass>VC_OFF_ROAD</vehicleClass>
      <wheelType>VWT_SUV</wheelType>
      <trailers />
      <additionalTrailers />
      <drivers />
      <extraIncludes />
      <doorsWithCollisionWhenClosed>
        <Item>VEH_EXT_BOOT</Item>
      </doorsWithCollisionWhenClosed>
      <driveableDoors />
      <bumpersNeedToCollideWithMap value="false" />
      <needsRopeTexture value="false" />
      <requiredExtras>EXTRA_1</requiredExtras>
      <rewards />
      <cinematicPartCamera>
        <Item>WHEEL_WIDE_REAR_RIGHT_CAMERA</Item>
        <Item>WHEEL_WIDE_REAR_LEFT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet>Truck</NmBraceOverrideSet>
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000" />
      <buoyancySphereSizeScale value="1.000000" />
      <pOverrideRagdollThreshold type="NULL" />
      <firstPersonDrivebyData>
        <Item>VAN_BRUTUS_FRONT_LEFT</Item>
        <Item>VAN_BRUTUS_FRONT_RIGHT</Item>
      </firstPersonDrivebyData>
	  <numSeatsOverride value="2" />
    </Item>  
	<Item>
      <modelName>brutus2</modelName>
      <txdName>brutus2</txdName>
      <handlingId>BRUTUS2</handlingId>
      <gameName>BRUTUS2</gameName>
      <vehicleMakeName>DECLASSE</vehicleMakeName>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected />
      <ptfxAssetName>weap_xs_vehicle_weapons</ptfxAssetName>
      <audioNameHash />
      <layout>LAYOUT_VAN_BRUTUS</layout>
      <coverBoundOffsets>BRUTUS_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout />
      <cameraName>DEFAULT_FOLLOW_VEHICLE_CAMERA</cameraName>
      <aimCameraName>MID_BOX_VEHICLE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>BRUTUS_BONNET_CAMERA</bonnetCameraName>
      <povCameraName>DEFAULT_POV_CAMERA_NO_REVERSE_LOOKAROUND_MID</povCameraName>
      <FirstPersonDriveByIKOffset x="0.000000" y="-0.050000" z="-0.040000" />
      <FirstPersonDriveByUnarmedIKOffset x="0.000000" y="0.000000" z="-0.020000" />
	  <FirstPersonProjectileDriveByIKOffset x="0.035000" y="-0.130000" z="-0.020000" />
	  <FirstPersonProjectileDriveByPassengerIKOffset x="0.035000" y="-0.130000" z="-0.020000" />
	  <FirstPersonDriveByRightPassengerIKOffset x="-0.040000" y="-0.080000" z="-0.060000" />
	  <FirstPersonDriveByRightPassengerUnarmedIKOffset x="0.000000" y="0.000000" z="-0.020000" />
	  <FirstPersonMobilePhoneOffset x="0.140000" y="0.218000" z="0.515000" />
      <FirstPersonPassengerMobilePhoneOffset x="0.240000" y="0.203000" z="0.465000" />
      <PovCameraOffset x="0.000000" y="-0.145000" z="0.630000" />
      <PovCameraVerticalAdjustmentForRollCage value="0.000000" />
      <PovPassengerCameraOffset x="0.000000" y="0.000000" z="0.070000" />
      <vfxInfoName>VFXVEHICLEINFO_CAR_OFFROAD</vfxInfoName>
      <shouldUseCinematicViewMode value="true" />
      <shouldCameraTransitionOnClimbUpDown value="false" />
      <shouldCameraIgnoreExiting value="false" />
      <AllowPretendOccupants value="true" />
      <AllowJoyriding value="true" />
      <AllowSundayDriving value="true" />
      <AllowBodyColorMapping value="true" />
      <wheelScale value="0.380000" />
      <wheelScaleRear value="0.380000" />
      <dirtLevelMin value="0.000000" />
      <dirtLevelMax value="0.850000" />
      <envEffScaleMin value="1.000000" />
      <envEffScaleMax value="1.000000" />
      <envEffScaleMin2 value="1.000000" />
      <envEffScaleMax2 value="1.000000" />
      <damageMapScale value="0.000000" />
      <damageOffsetScale value="0.100000" />
      <diffuseTint value="0x00FFFFFF" />
      <steerWheelMult value="1.000000" />
      <HDTextureDist value="5.000000" />
      <lodDistances content="float_array">
        20.000000
        35.000000
        90.000000
        180.000000
        500.000000
        500.000000
      </lodDistances>
      <minSeatHeight value="1.006" />
      <identicalModelSpawnDistance value="20" />
      <maxNumOfSameColor value="10" />
      <defaultBodyHealth value="1000.000000" />
      <pretendOccupantsScale value="1.000000" />
      <visibleSpawnDistScale value="1.000000" />
      <trackerPathWidth value="2.000000" />
      <weaponForceMult value="1.000000" />
      <frequency value="30" />
      <swankness>SWANKNESS_1</swankness>
      <maxNum value="5" />
      <flags>FLAG_AVERAGE_CAR FLAG_POOR_CAR FLAG_IS_OFFROAD_VEHICLE FLAG_HEADLIGHTS_USE_ACTUAL_BONE_POS FLAG_IS_BULKY FLAG_HAS_INTERIOR_EXTRAS FLAG_RECESSED_HEADLIGHT_CORONAS FLAG_HAS_NITROUS_MOD FLAG_HAS_JUMP_MOD FLAG_HAS_SIDE_SHUNT FLAG_HAS_RAMMING_SCOOP_MOD FLAG_HAS_RAMMING_BAR_MOD FLAG_HAS_WEAPON_BLADE_MODS FLAG_USE_AIRCRAFT_STYLE_WEAPON_TARGETING FLAG_DISABLE_DEFORMATION</flags>
      <type>VEHICLE_TYPE_CAR</type>
      <plateType>VPT_BACK_PLATES</plateType>
	  <dashboardType>VDT_SPEEDO</dashboardType>
      <vehicleClass>VC_OFF_ROAD</vehicleClass>
      <wheelType>VWT_SUV</wheelType>
      <trailers />
      <additionalTrailers />
      <drivers />
      <extraIncludes />
      <doorsWithCollisionWhenClosed>
        <Item>VEH_EXT_BOOT</Item>
      </doorsWithCollisionWhenClosed>
      <driveableDoors />
      <bumpersNeedToCollideWithMap value="false" />
      <needsRopeTexture value="false" />
      <requiredExtras>EXTRA_1</requiredExtras>
      <rewards />
      <cinematicPartCamera>
        <Item>WHEEL_WIDE_REAR_RIGHT_CAMERA</Item>
        <Item>WHEEL_WIDE_REAR_LEFT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet>Truck</NmBraceOverrideSet>
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000" />
      <buoyancySphereSizeScale value="1.000000" />
      <pOverrideRagdollThreshold type="NULL" />
      <firstPersonDrivebyData>
        <Item>VAN_BRUTUS_FRONT_LEFT</Item>
        <Item>VAN_BRUTUS_FRONT_RIGHT</Item>
      </firstPersonDrivebyData>
	  <numSeatsOverride value="2" />
    </Item>  
    <Item>
      <modelName>brutus3</modelName>
      <txdName>brutus3</txdName>
      <handlingId>BRUTUS</handlingId>
      <gameName>BRUTUS3</gameName>
      <vehicleMakeName>DECLASSE</vehicleMakeName>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected />
      <ptfxAssetName>weap_xs_vehicle_weapons</ptfxAssetName>
      <audioNameHash />
      <layout>LAYOUT_VAN_BRUTUS</layout>
      <coverBoundOffsets>BRUTUS_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout />
      <cameraName>DEFAULT_FOLLOW_VEHICLE_CAMERA</cameraName>
      <aimCameraName>MID_BOX_VEHICLE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>BRUTUS_BONNET_CAMERA</bonnetCameraName>
      <povCameraName>DEFAULT_POV_CAMERA_NO_REVERSE_LOOKAROUND_MID</povCameraName>
      <FirstPersonDriveByIKOffset x="0.000000" y="-0.050000" z="-0.040000" />
      <FirstPersonDriveByUnarmedIKOffset x="0.000000" y="0.000000" z="-0.020000" />
	  <FirstPersonProjectileDriveByIKOffset x="0.035000" y="-0.130000" z="-0.020000" />
	  <FirstPersonProjectileDriveByPassengerIKOffset x="0.035000" y="-0.130000" z="-0.020000" />
	  <FirstPersonDriveByRightPassengerIKOffset x="-0.040000" y="-0.080000" z="-0.060000" />
	  <FirstPersonDriveByRightPassengerUnarmedIKOffset x="0.000000" y="0.000000" z="-0.020000" />
	  <FirstPersonMobilePhoneOffset x="0.140000" y="0.218000" z="0.515000" />
      <FirstPersonPassengerMobilePhoneOffset x="0.240000" y="0.203000" z="0.465000" />
      <PovCameraOffset x="0.000000" y="-0.145000" z="0.630000" />
      <PovCameraVerticalAdjustmentForRollCage value="0.000000" />
      <PovPassengerCameraOffset x="0.000000" y="0.000000" z="0.070000" />
      <vfxInfoName>VFXVEHICLEINFO_CAR_OFFROAD</vfxInfoName>
      <shouldUseCinematicViewMode value="true" />
      <shouldCameraTransitionOnClimbUpDown value="false" />
      <shouldCameraIgnoreExiting value="false" />
      <AllowPretendOccupants value="true" />
      <AllowJoyriding value="true" />
      <AllowSundayDriving value="true" />
      <AllowBodyColorMapping value="true" />
      <wheelScale value="0.278300" />
      <wheelScaleRear value="0.278300" />
      <dirtLevelMin value="0.000000" />
      <dirtLevelMax value="0.850000" />
      <envEffScaleMin value="1.000000" />
      <envEffScaleMax value="1.000000" />
      <envEffScaleMin2 value="1.000000" />
      <envEffScaleMax2 value="1.000000" />
      <damageMapScale value="0.400000" />
      <damageOffsetScale value="0.400000" />
      <diffuseTint value="0x00FFFFFF" />
      <steerWheelMult value="1.000000" />
      <HDTextureDist value="5.000000" />
      <lodDistances content="float_array">
        20.000000
        35.000000
        90.000000
        180.000000
        500.000000
        500.000000
      </lodDistances>
      <minSeatHeight value="1.006" />
      <identicalModelSpawnDistance value="20" />
      <maxNumOfSameColor value="10" />
      <defaultBodyHealth value="1000.000000" />
      <pretendOccupantsScale value="1.000000" />
      <visibleSpawnDistScale value="1.000000" />
      <trackerPathWidth value="2.000000" />
      <weaponForceMult value="1.000000" />
      <frequency value="30" />
      <swankness>SWANKNESS_1</swankness>
      <maxNum value="5" />
      <flags>FLAG_AVERAGE_CAR FLAG_POOR_CAR FLAG_IS_OFFROAD_VEHICLE FLAG_HEADLIGHTS_USE_ACTUAL_BONE_POS FLAG_IS_BULKY FLAG_HAS_INTERIOR_EXTRAS FLAG_RECESSED_HEADLIGHT_CORONAS FLAG_HAS_NITROUS_MOD FLAG_HAS_JUMP_MOD FLAG_HAS_SIDE_SHUNT FLAG_HAS_RAMMING_SCOOP_MOD FLAG_HAS_WEAPON_BLADE_MODS FLAG_USE_AIRCRAFT_STYLE_WEAPON_TARGETING FLAG_HAS_RAMMING_BAR_MOD</flags>
      <type>VEHICLE_TYPE_CAR</type>
      <plateType>VPT_BACK_PLATES</plateType>
	  <dashboardType>VDT_SPEEDO</dashboardType>
      <vehicleClass>VC_OFF_ROAD</vehicleClass>
      <wheelType>VWT_SUV</wheelType>
      <trailers />
      <additionalTrailers />
      <drivers />
      <extraIncludes />
      <doorsWithCollisionWhenClosed>
        <Item>VEH_EXT_BOOT</Item>
      </doorsWithCollisionWhenClosed>
      <driveableDoors />
      <bumpersNeedToCollideWithMap value="false" />
      <needsRopeTexture value="false" />
      <requiredExtras>EXTRA_1</requiredExtras>
      <rewards />
      <cinematicPartCamera>
        <Item>WHEEL_WIDE_REAR_RIGHT_CAMERA</Item>
        <Item>WHEEL_WIDE_REAR_LEFT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet>Truck</NmBraceOverrideSet>
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000" />
      <buoyancySphereSizeScale value="1.000000" />
      <pOverrideRagdollThreshold type="NULL" />
      <firstPersonDrivebyData>
        <Item>VAN_BRUTUS_FRONT_LEFT</Item>
        <Item>VAN_BRUTUS_FRONT_RIGHT</Item>
      </firstPersonDrivebyData>
	  <numSeatsOverride value="2" />
    </Item>  
    <Item>
      <modelName>deathbike</modelName>
      <txdName>deathbike</txdName>
      <handlingId>DEATHBIKE</handlingId>
      <gameName>DEATHBIKE</gameName>
      <vehicleMakeName>WESTERN</vehicleMakeName>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected />
      <ptfxAssetName>weap_xs_vehicle_weapons</ptfxAssetName>
      <audioNameHash />
      <layout>LAYOUT_BIKE_FREEWAY</layout>
      <coverBoundOffsets>DEATHBIKE_COVER_OFFSET_INFO</coverBoundOffsets>
	  <POVTuningInfo>BAGGER_POV_TUNING</POVTuningInfo>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout />
      <cameraName>DEATHBIKE_FOLLOW_CAMERA</cameraName>
      <aimCameraName>BIKE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>BIKE_BAGGER_POV_CAMERA</bonnetCameraName>
      <povCameraName>BIKE_BAGGER_POV_CAMERA</povCameraName>
      <FirstPersonDriveByIKOffset x="0.000000" y="-0.100000" z="0.000000" />
      <FirstPersonDriveByUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000" />
	  <FirstPersonProjectileDriveByIKOffset x="0.000000" y="0.000000" z="0.000000" />
	  <FirstPersonProjectileDriveByPassengerIKOffset x="0.000000" y="0.000000" z="0.000000" />
	  <FirstPersonProjectileDriveByRearLeftIKOffset x="0.000000" y="0.000000" z="0.000000" />
	  <FirstPersonProjectileDriveByRearRightIKOffset x="0.000000" y="0.000000" z="0.000000" />
	  <FirstPersonDriveByLeftPassengerIKOffset x="0.000000" y="-0.060000" z="=0.030000" />
	  <FirstPersonDriveByRightPassengerIKOffset x="0.000000" y="0.000000" z="0.000000" />
	  <FirstPersonDriveByRightRearPassengerIKOffset x="0.000000" y="0.000000" z="0.000000" />
	  <FirstPersonDriveByLeftPassengerUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000" />
	  <FirstPersonDriveByRightPassengerUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000" />
	  <FirstPersonMobilePhoneOffset x="0.155000" y="0.315000" z="0.520000" />
      <FirstPersonPassengerMobilePhoneOffset x="0.136000" y="0.223000" z="0.425000" />
      <PovCameraOffset x="0.000000" y="0.000000" z="0.000000" />
      <PovCameraVerticalAdjustmentForRollCage value="0.000000" />
      <PovPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000" />
      <PovRearPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000" />
      <vfxInfoName>VFXVEHICLEINFO_MOTORBIKE_GENERIC</vfxInfoName>
      <shouldUseCinematicViewMode value="true" />
      <shouldCameraTransitionOnClimbUpDown value="false" />
      <shouldCameraIgnoreExiting value="false" />
      <AllowPretendOccupants value="false" />
      <AllowJoyriding value="true" />
      <AllowSundayDriving value="true" />
      <AllowBodyColorMapping value="true" />
      <wheelScale value="0.213500" />
      <wheelScaleRear value="0.185100" />
      <dirtLevelMin value="0.000000" />
      <dirtLevelMax value="0.850000" />
      <envEffScaleMin value="1.000000" />
      <envEffScaleMax value="1.000000" />
      <envEffScaleMin2 value="1.000000" />
      <envEffScaleMax2 value="1.000000" />
      <damageMapScale value="0.600000" />
      <damageOffsetScale value="1.000000" />
      <diffuseTint value="0x00FFFFFF" />
      <steerWheelMult value="0.350000" />
      <HDTextureDist value="5.000000" />
      <lodDistances content="float_array">
        10.000000
        25.000000
        50.000000
        100.000000
        500.000000
        500.000000
      </lodDistances>
      <minSeatHeight value="10" />
      <identicalModelSpawnDistance value="80" />
      <maxNumOfSameColor value="10" />
      <defaultBodyHealth value="1000.000000" />
      <pretendOccupantsScale value="1.000000" />
      <visibleSpawnDistScale value="1.000000" />
      <trackerPathWidth value="2.000000" />
      <weaponForceMult value="1.000000" />
      <frequency value="30" />
      <swankness>SWANKNESS_1</swankness>
      <maxNum value="3" />
      <flags>FLAG_NO_BOOT FLAG_EXTRAS_REQUIRE FLAG_EXTRAS_STRONG FLAG_IGNORE_ON_SIDE_CHECK FLAG_AVERAGE_CAR FLAG_HEADLIGHTS_USE_ACTUAL_BONE_POS FLAG_BIKE_CLAMP_PICKUP_LEAN_RATE FLAG_HAS_JUMP_MOD FLAG_HAS_NITROUS_MOD FLAG_HAS_WEAPON_BLADE_MODS FLAG_USE_AIRCRAFT_STYLE_WEAPON_TARGETING</flags>
      <type>VEHICLE_TYPE_BIKE</type>
      <plateType>VPT_NONE</plateType>
      <dashboardType>VDT_ZTYPE</dashboardType>
      <vehicleClass>VC_MOTORCYCLE</vehicleClass>
      <wheelType>VWT_BIKE</wheelType>
      <trailers />
      <additionalTrailers />
      <drivers />
      <extraIncludes />
      <doorsWithCollisionWhenClosed />
      <driveableDoors />
      <bumpersNeedToCollideWithMap value="false" />
      <needsRopeTexture value="false" />
      <requiredExtras>EXTRA_1 EXTRA_2 EXTRA_3</requiredExtras>
      <rewards />
      <cinematicPartCamera>
        <Item>WHEEL_REAR_LEFT_CAMERA</Item>
        <Item>WHEEL_REAR_RIGHT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet>Bike</NmBraceOverrideSet>
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000" />
      <buoyancySphereSizeScale value="1.000000" />
      <pOverrideRagdollThreshold type="NULL" />
	  <firstPersonDrivebyData>
        <Item>BIKE_BAGGER_FRONT</Item>
        <Item>BIKE_BAGGER_REAR</Item>
      </firstPersonDrivebyData>
	  <numSeatsOverride value="1" />
    </Item>
	<Item>
      <modelName>dominator4</modelName>
      <txdName>dominator4</txdName>
      <handlingId>DOMINATOR4</handlingId>
      <gameName>DOMINATOR4</gameName>
      <vehicleMakeName>VAPID</vehicleMakeName>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected />
      <ptfxAssetName>weap_xs_vehicle_weapons</ptfxAssetName>
      <audioNameHash />
      <layout>LAYOUT_STD_ARENA_1HONLY</layout>
      <coverBoundOffsets>DOMINATOR4_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout />
      <cameraName>FOLLOW_CHEETAH_CAMERA</cameraName>
      <aimCameraName>DEFAULT_THIRD_PERSON_VEHICLE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>DEFAULT_VEHICLE_BONNET_CAMERA</bonnetCameraName>
      <povCameraName>REDUCED_NEAR_CLIP_POV_CAMERA_NO_REVERSE</povCameraName>
      <FirstPersonDriveByIKOffset x="0.060000" y="-0.130000" z="-0.030000" />
      <FirstPersonDriveByUnarmedIKOffset x="0.000000" y="-0.025000" z="0.000000" />
	  <FirstPersonProjectileDriveByIKOffset x="0.000000" y="-0.120000" z="0.030000" />
	  <FirstPersonProjectileDriveByPassengerIKOffset x="0.000000" y="-0.120000" z="0.030000" />
	  <FirstPersonDriveByRightPassengerIKOffset x="-0.060000" y="-0.130000" z="-0.030000" />
	  <FirstPersonDriveByRightPassengerUnarmedIKOffset x="0.000000" y="-0.035000" z="0.000000" />
	  <FirstPersonMobilePhoneOffset x="0.133000" y="0.208000" z="0.533000" />
      <FirstPersonPassengerMobilePhoneOffset x="0.216000" y="0.173000" z="0.415000" />
      <PovCameraOffset x="0.000000" y="-0.195000" z="0.660000" />
      <PovCameraVerticalAdjustmentForRollCage value="0.000000" />
      <PovPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000" />
      <vfxInfoName>VFXVEHICLEINFO_CAR_GENERIC</vfxInfoName>
      <shouldUseCinematicViewMode value="true" />
      <shouldCameraTransitionOnClimbUpDown value="false" />
      <shouldCameraIgnoreExiting value="false" />
      <AllowPretendOccupants value="true" />
      <AllowJoyriding value="true" />
      <AllowSundayDriving value="true" />
      <AllowBodyColorMapping value="true" />
      <wheelScale value="0.279900" />
      <wheelScaleRear value="0.279900" />
      <dirtLevelMin value="0.000000" />
      <dirtLevelMax value="0.450000" />
      <envEffScaleMin value="1.000000" />
      <envEffScaleMax value="1.000000" />
      <envEffScaleMin2 value="1.000000" />
      <envEffScaleMax2 value="1.000000" />
      <damageMapScale value="0.30000" />
      <damageOffsetScale value="0.30000" />
      <diffuseTint value="0x00FFFFFF" />
      <steerWheelMult value="0.700000" />
      <HDTextureDist value="5.000000" />
      <lodDistances content="float_array">
        12.000000
        30.000000
        60.000000
        120.000000
        500.000000
        500.000000
      </lodDistances>
      <identicalModelSpawnDistance value="20" />
      <maxNumOfSameColor value="10" />
      <defaultBodyHealth value="1000.000000" />
      <pretendOccupantsScale value="1.000000" />
      <visibleSpawnDistScale value="1.000000" />
      <trackerPathWidth value="2.000000" />
      <weaponForceMult value="1.000000" />
      <frequency value="90" />
      <swankness>SWANKNESS_2</swankness>
      <maxNum value="20" />
      <flags>FLAG_SPAWN_ON_TRAILER FLAG_PARKING_SENSORS FLAG_AVERAGE_CAR FLAG_RECESSED_HEADLIGHT_CORONAS FLAG_HAS_INTERIOR_EXTRAS FLAG_HAS_RAMMING_SCOOP_MOD FLAG_HAS_SUPER_BRAKES_MOD FLAG_HAS_WEAPON_BLADE_MODS FLAG_HAS_WEAPON_SPIKE_MODS FLAG_HAS_NITROUS_MOD FLAG_USE_AIRCRAFT_STYLE_WEAPON_TARGETING FLAG_HAS_JUMP_MOD FLAG_HAS_TOMBSTONE FLAG_HAS_SIDE_SHUNT FLAG_HAS_FRONT_SPIKE_MOD FLAG_HAS_SUPERCHARGER FLAG_DISABLE_THROUGH_WINDSCREEN</flags>
      <type>VEHICLE_TYPE_CAR</type>
      <plateType>VPT_BACK_PLATES</plateType>
      <dashboardType>VDT_DUKES</dashboardType>
      <vehicleClass>VC_MUSCLE</vehicleClass>
      <wheelType>VWT_MUSCLE</wheelType>
      <trailers />
      <additionalTrailers />
      <drivers />
      <extraIncludes />
      <doorsWithCollisionWhenClosed />
      <driveableDoors />
      <bumpersNeedToCollideWithMap value="false" />
      <needsRopeTexture value="false" />
      <requiredExtras />
      <rewards />
      <cinematicPartCamera>
        <Item>WHEEL_FRONT_RIGHT_CAMERA</Item>
        <Item>WHEEL_FRONT_LEFT_CAMERA</Item>
        <Item>WHEEL_REAR_RIGHT_CAMERA</Item>
        <Item>WHEEL_REAR_LEFT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet />
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000" />
      <buoyancySphereSizeScale value="1.000000" />
      <pOverrideRagdollThreshold type="NULL" />
      <firstPersonDrivebyData>
        <Item>STD_DOMINATOR4_FRONT_LEFT</Item>
        <Item>STD_DOMINATOR4_FRONT_RIGHT</Item>
      </firstPersonDrivebyData>
    </Item>
	<Item>
      <modelName>dominator5</modelName>
      <txdName>dominator5</txdName>
      <handlingId>DOMINATOR5</handlingId>
      <gameName>DOMINATOR5</gameName>
      <vehicleMakeName>VAPID</vehicleMakeName>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected />
      <ptfxAssetName>weap_xs_vehicle_weapons</ptfxAssetName>
      <audioNameHash />
      <layout>LAYOUT_STD_ARENA_1HONLY</layout>
      <coverBoundOffsets>DOMINATOR4_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout />
      <cameraName>FOLLOW_CHEETAH_CAMERA</cameraName>
      <aimCameraName>DEFAULT_THIRD_PERSON_VEHICLE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>DEFAULT_VEHICLE_BONNET_CAMERA</bonnetCameraName>
      <povCameraName>REDUCED_NEAR_CLIP_POV_CAMERA_NO_REVERSE</povCameraName>
      <FirstPersonDriveByIKOffset x="0.060000" y="-0.130000" z="-0.030000" />
      <FirstPersonDriveByUnarmedIKOffset x="0.000000" y="-0.025000" z="0.000000" />
	  <FirstPersonProjectileDriveByIKOffset x="0.000000" y="-0.120000" z="0.030000" />
	  <FirstPersonProjectileDriveByPassengerIKOffset x="0.000000" y="-0.120000" z="0.030000" />
	  <FirstPersonDriveByRightPassengerIKOffset x="-0.060000" y="-0.130000" z="-0.030000" />
	  <FirstPersonDriveByRightPassengerUnarmedIKOffset x="0.000000" y="-0.035000" z="0.000000" />
	  <FirstPersonMobilePhoneOffset x="0.133000" y="0.208000" z="0.533000" />
      <FirstPersonPassengerMobilePhoneOffset x="0.216000" y="0.173000" z="0.415000" />
      <PovCameraOffset x="0.000000" y="-0.195000" z="0.660000" />
      <PovCameraVerticalAdjustmentForRollCage value="0.000000" />
      <PovPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000" />
      <vfxInfoName>VFXVEHICLEINFO_CAR_GENERIC</vfxInfoName>
      <shouldUseCinematicViewMode value="true" />
      <shouldCameraTransitionOnClimbUpDown value="false" />
      <shouldCameraIgnoreExiting value="false" />
      <AllowPretendOccupants value="true" />
      <AllowJoyriding value="true" />
      <AllowSundayDriving value="true" />
      <AllowBodyColorMapping value="true" />
      <wheelScale value="0.279900" />
      <wheelScaleRear value="0.279900" />
      <dirtLevelMin value="0.000000" />
      <dirtLevelMax value="0.450000" />
      <envEffScaleMin value="1.000000" />
      <envEffScaleMax value="1.000000" />
      <envEffScaleMin2 value="1.000000" />
      <envEffScaleMax2 value="1.000000" />
      <damageMapScale value="0.00000" />
      <damageOffsetScale value="0.10000" />
      <diffuseTint value="0x00FFFFFF" />
      <steerWheelMult value="0.700000" />
      <HDTextureDist value="5.000000" />
      <lodDistances content="float_array">
        12.000000
        30.000000
        60.000000
        120.000000
        500.000000
        500.000000
      </lodDistances>
      <identicalModelSpawnDistance value="20" />
      <maxNumOfSameColor value="10" />
      <defaultBodyHealth value="1000.000000" />
      <pretendOccupantsScale value="1.000000" />
      <visibleSpawnDistScale value="1.000000" />
      <trackerPathWidth value="2.000000" />
      <weaponForceMult value="1.000000" />
      <frequency value="90" />
      <swankness>SWANKNESS_2</swankness>
      <maxNum value="20" />
      <flags>FLAG_SPAWN_ON_TRAILER FLAG_PARKING_SENSORS FLAG_AVERAGE_CAR FLAG_RECESSED_HEADLIGHT_CORONAS FLAG_HAS_INTERIOR_EXTRAS FLAG_HAS_RAMMING_SCOOP_MOD FLAG_HAS_SUPER_BRAKES_MOD FLAG_HAS_WEAPON_BLADE_MODS FLAG_HAS_WEAPON_SPIKE_MODS FLAG_HAS_NITROUS_MOD FLAG_USE_AIRCRAFT_STYLE_WEAPON_TARGETING FLAG_HAS_JUMP_MOD FLAG_HAS_TOMBSTONE FLAG_HAS_SIDE_SHUNT FLAG_HAS_FRONT_SPIKE_MOD FLAG_HAS_SUPERCHARGER FLAG_DISABLE_THROUGH_WINDSCREEN FLAG_DISABLE_DEFORMATION</flags>
      <type>VEHICLE_TYPE_CAR</type>
      <plateType>VPT_BACK_PLATES</plateType>
      <dashboardType>VDT_DUKES</dashboardType>
      <vehicleClass>VC_MUSCLE</vehicleClass>
      <wheelType>VWT_MUSCLE</wheelType>
      <trailers />
      <additionalTrailers />
      <drivers />
      <extraIncludes />
      <doorsWithCollisionWhenClosed />
      <driveableDoors />
      <bumpersNeedToCollideWithMap value="false" />
      <needsRopeTexture value="false" />
      <requiredExtras />
      <rewards />
      <cinematicPartCamera>
        <Item>WHEEL_FRONT_RIGHT_CAMERA</Item>
        <Item>WHEEL_FRONT_LEFT_CAMERA</Item>
        <Item>WHEEL_REAR_RIGHT_CAMERA</Item>
        <Item>WHEEL_REAR_LEFT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet />
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000" />
      <buoyancySphereSizeScale value="1.000000" />
      <pOverrideRagdollThreshold type="NULL" />
      <firstPersonDrivebyData>
        <Item>STD_DOMINATOR4_FRONT_LEFT</Item>
        <Item>STD_DOMINATOR4_FRONT_RIGHT</Item>
      </firstPersonDrivebyData>
    </Item>
    <Item>
      <modelName>bruiser</modelName>
      <txdName>bruiser</txdName>
      <handlingId>BRUISER</handlingId>
      <gameName>BRUISER</gameName>
      <vehicleMakeName>BENEFAC</vehicleMakeName>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected />
      <ptfxAssetName>weap_xs_vehicle_weapons</ptfxAssetName>
      <audioNameHash />
      <layout>LAYOUT_STD_BRUISER</layout>
      <coverBoundOffsets>BRUISER_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_TRUCK</explosionInfo>
      <scenarioLayout />
      <cameraName>DEFAULT_FOLLOW_VEHICLE_CAMERA</cameraName>
      <aimCameraName>BOX_VEHICLE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>VEHICLE_BONNET_CAMERA_LOW</bonnetCameraName>
      <povCameraName>REDUCED_NEAR_CLIP_POV_CAMERA</povCameraName>
      <FirstPersonDriveByIKOffset x="0.080000" y="-0.110000" z="-0.080000" />
      <FirstPersonDriveByUnarmedIKOffset x="-0.020000" y="-0.060000" z="-0.020000" />
	  <FirstPersonProjectileDriveByIKOffset x="0.063000" y="-0.088000" z="-0.070000" />
	  <FirstPersonProjectileDriveByPassengerIKOffset x="0.000000" y="-0.088000" z="-0.070000" />
	  <FirstPersonProjectileDriveByRearLeftIKOffset x="-0.040000" y="-0.088000" z="-0.070000" />
	  <FirstPersonProjectileDriveByRearRightIKOffset x="0.050000" y="-0.060000" z="-0.090000" />
	  <FirstPersonDriveByLeftPassengerIKOffset x="0.020000" y="-0.060000" z="-0.020000" />
	  <FirstPersonDriveByRightPassengerIKOffset x="-0.010000" y="-0.110000" z="-0.080000" />
	  <FirstPersonDriveByRightRearPassengerIKOffset x="-0.005000" y="-0.060000" z="-0.020000" />
	  <FirstPersonDriveByLeftPassengerUnarmedIKOffset x="0.020000" y="-0.060000" z="-0.020000" />
	  <FirstPersonDriveByRightPassengerUnarmedIKOffset x="0.000000" y="-0.108000" z="-0.013000" />
	  <FirstPersonMobilePhoneOffset x="0.133000" y="0.165000" z="0.520000" />
      <FirstPersonPassengerMobilePhoneOffset x="0.213000" y="0.240000" z="0.445000" />
      <FirstPersonMobilePhoneSeatIKOffset>
		<Item>
			<Offset x="0.213000" y="0.240000" z="0.445000" />
			<SeatIndex value="2" />
		</Item>
		<Item>
			<Offset x="0.213000" y="0.240000" z="0.445000" />
			<SeatIndex value="3" />
		</Item>
      </FirstPersonMobilePhoneSeatIKOffset>
      <PovCameraOffset x="0.000000" y="-0.185000" z="0.645000" />
      <PovCameraVerticalAdjustmentForRollCage value="0.000000" />
      <PovPassengerCameraOffset x="0.000000" y="0.100000" z="0.040000" />
      <PovRearPassengerCameraOffset x="0.000000" y="0.100000" z="0.040000" />
      <vfxInfoName>VFXVEHICLEINFO_TRUCK_RIG</vfxInfoName>
      <shouldUseCinematicViewMode value="true" />
      <shouldCameraTransitionOnClimbUpDown value="false" />
      <shouldCameraIgnoreExiting value="false" />
      <AllowPretendOccupants value="true" />
      <AllowJoyriding value="false" />
      <AllowSundayDriving value="false" />
      <AllowBodyColorMapping value="true" />
      <wheelScale value="0.351400" />
      <wheelScaleRear value="0.351400" />
      <dirtLevelMin value="0.000000" />
      <dirtLevelMax value="0.450000" />
      <envEffScaleMin value="1.000000" />
      <envEffScaleMax value="1.000000" />
      <envEffScaleMin2 value="1.000000" />
      <envEffScaleMax2 value="1.000000" />
      <damageMapScale value="0.300000" />
      <damageOffsetScale value="0.300000" />
      <diffuseTint value="0x00FFFFFF" />
      <steerWheelMult value="1.000000" />
      <HDTextureDist value="5.000000" />
      <lodDistances content="float_array">
        20.000000	
        90.000000	
        130.000000	
        260.000000	
        750.000000	
        750.000000
      </lodDistances>
      <minSeatHeight value="0.600" />
      <identicalModelSpawnDistance value="20" />
      <maxNumOfSameColor value="10" />
      <defaultBodyHealth value="1000.000000" />
      <pretendOccupantsScale value="1.000000" />
      <visibleSpawnDistScale value="1.000000" />
      <trackerPathWidth value="2.000000" />
      <weaponForceMult value="1.000000" />
      <frequency value="10" />
      <swankness>SWANKNESS_1</swankness>
      <maxNum value="999" />
      <flags>FLAG_CAN_HAVE_NEONS FLAG_USE_AIRCRAFT_STYLE_WEAPON_TARGETING FLAG_HAS_NITROUS_MOD FLAG_HAS_JUMP_MOD FLAG_NO_BOOT FLAG_BIG FLAG_AVOID_TURNS FLAG_INCREASE_LOW_SPEED_TORQUE FLAG_HAS_RAMMING_SCOOP_MOD FLAG_USE_AIRCRAFT_STYLE_WEAPON_TARGETING FLAG_HAS_WEAPON_SPIKE_MODS FLAG_HAS_SIDE_SHUNT FLAG_HAS_RAMMING_BAR_MOD</flags>
      <type>VEHICLE_TYPE_CAR</type>
      <plateType>VPT_NONE</plateType>
      <dashboardType>VDT_DUKES</dashboardType>
      <vehicleClass>VC_OFF_ROAD</vehicleClass>
      <wheelType>VWT_OFFROAD</wheelType>
      <trailers />
      <additionalTrailers />
      <drivers />
      <extraIncludes />
      <doorsWithCollisionWhenClosed />
      <driveableDoors />
      <bumpersNeedToCollideWithMap value="false" />
      <needsRopeTexture value="false" />
      <requiredExtras />
      <rewards />
      <cinematicPartCamera>
        <Item>WHEEL_FRONT_RIGHT_CAMERA</Item>
        <Item>WHEEL_FRONT_LEFT_CAMERA</Item>
        <Item>WHEEL_REAR_RIGHT_CAMERA</Item>
        <Item>WHEEL_REAR_LEFT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet />
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000" />
      <buoyancySphereSizeScale value="1.000000" />
      <pOverrideRagdollThreshold type="NULL" />
      <firstPersonDrivebyData>
        <Item>STD_BRUISER_FRONT_LEFT</Item>
        <Item>STD_BRUISER_FRONT_RIGHT</Item>
        <Item>STD_BRUISER_REAR_LEFT</Item>
        <Item>STD_BRUISER_REAR_RIGHT</Item>
      </firstPersonDrivebyData>
    </Item>
	<Item>
      <modelName>bruiser2</modelName>
      <txdName>bruiser2</txdName>
      <handlingId>BRUISER2</handlingId>
      <gameName>BRUISER2</gameName>
      <vehicleMakeName>BENEFAC</vehicleMakeName>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected />
      <ptfxAssetName>weap_xs_vehicle_weapons</ptfxAssetName>
      <audioNameHash />
      <layout>LAYOUT_STD_BRUISER</layout>
      <coverBoundOffsets>BRUISER_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_TRUCK</explosionInfo>
      <scenarioLayout />
      <cameraName>DEFAULT_FOLLOW_VEHICLE_CAMERA</cameraName>
      <aimCameraName>BOX_VEHICLE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>VEHICLE_BONNET_CAMERA_LOW</bonnetCameraName>
      <povCameraName>REDUCED_NEAR_CLIP_POV_CAMERA</povCameraName>
      <FirstPersonDriveByIKOffset x="0.080000" y="-0.110000" z="-0.080000" />
      <FirstPersonDriveByUnarmedIKOffset x="-0.020000" y="-0.060000" z="-0.020000" />
	  <FirstPersonProjectileDriveByIKOffset x="0.063000" y="-0.088000" z="-0.070000" />
	  <FirstPersonProjectileDriveByPassengerIKOffset x="0.000000" y="-0.088000" z="-0.070000" />
	  <FirstPersonProjectileDriveByRearLeftIKOffset x="-0.040000" y="-0.088000" z="-0.070000" />
	  <FirstPersonProjectileDriveByRearRightIKOffset x="0.050000" y="-0.060000" z="-0.090000" />
	  <FirstPersonDriveByLeftPassengerIKOffset x="0.020000" y="-0.060000" z="-0.020000" />
	  <FirstPersonDriveByRightPassengerIKOffset x="-0.010000" y="-0.110000" z="-0.080000" />
	  <FirstPersonDriveByRightRearPassengerIKOffset x="-0.005000" y="-0.060000" z="-0.020000" />
	  <FirstPersonDriveByLeftPassengerUnarmedIKOffset x="0.020000" y="-0.060000" z="-0.020000" />
	  <FirstPersonDriveByRightPassengerUnarmedIKOffset x="0.000000" y="-0.108000" z="-0.013000" />
	  <FirstPersonMobilePhoneOffset x="0.133000" y="0.165000" z="0.520000" />
      <FirstPersonPassengerMobilePhoneOffset x="0.213000" y="0.240000" z="0.445000" />
      <FirstPersonMobilePhoneSeatIKOffset>
		<Item>
			<Offset x="0.213000" y="0.240000" z="0.445000" />
			<SeatIndex value="2" />
		</Item>
		<Item>
			<Offset x="0.213000" y="0.240000" z="0.445000" />
			<SeatIndex value="3" />
		</Item>
      </FirstPersonMobilePhoneSeatIKOffset>
      <PovCameraOffset x="0.000000" y="-0.185000" z="0.645000" />
      <PovCameraVerticalAdjustmentForRollCage value="0.000000" />
      <PovPassengerCameraOffset x="0.000000" y="0.100000" z="0.040000" />
      <PovRearPassengerCameraOffset x="0.000000" y="0.100000" z="0.040000" />
      <vfxInfoName>VFXVEHICLEINFO_TRUCK_RIG</vfxInfoName>
      <shouldUseCinematicViewMode value="true" />
      <shouldCameraTransitionOnClimbUpDown value="false" />
      <shouldCameraIgnoreExiting value="false" />
      <AllowPretendOccupants value="true" />
      <AllowJoyriding value="false" />
      <AllowSundayDriving value="false" />
      <AllowBodyColorMapping value="true" />
      <wheelScale value="0.351400" />
      <wheelScaleRear value="0.351400" />
      <dirtLevelMin value="0.000000" />
      <dirtLevelMax value="0.450000" />
      <envEffScaleMin value="1.000000" />
      <envEffScaleMax value="1.000000" />
      <envEffScaleMin2 value="1.000000" />
      <envEffScaleMax2 value="1.000000" />
      <damageMapScale value="0.000000" />
      <damageOffsetScale value="0.100000" />
      <diffuseTint value="0x00FFFFFF" />
      <steerWheelMult value="1.000000" />
      <HDTextureDist value="5.000000" />
      <lodDistances content="float_array">
        20.000000	
        90.000000	
        130.000000	
        260.000000	
        750.000000	
        750.000000
      </lodDistances>
      <minSeatHeight value="0.600" />
      <identicalModelSpawnDistance value="20" />
      <maxNumOfSameColor value="10" />
      <defaultBodyHealth value="1000.000000" />
      <pretendOccupantsScale value="1.000000" />
      <visibleSpawnDistScale value="1.000000" />
      <trackerPathWidth value="2.000000" />
      <weaponForceMult value="1.000000" />
      <frequency value="10" />
      <swankness>SWANKNESS_1</swankness>
      <maxNum value="999" />
      <flags>FLAG_CAN_HAVE_NEONS FLAG_USE_AIRCRAFT_STYLE_WEAPON_TARGETING FLAG_HAS_NITROUS_MOD FLAG_HAS_JUMP_MOD FLAG_NO_BOOT FLAG_BIG FLAG_AVOID_TURNS FLAG_INCREASE_LOW_SPEED_TORQUE FLAG_HAS_RAMMING_SCOOP_MOD FLAG_USE_AIRCRAFT_STYLE_WEAPON_TARGETING FLAG_HAS_WEAPON_SPIKE_MODS FLAG_HAS_SIDE_SHUNT FLAG_HAS_RAMMING_BAR_MOD</flags>
      <type>VEHICLE_TYPE_CAR</type>
      <plateType>VPT_NONE</plateType>
      <dashboardType>VDT_DUKES</dashboardType>
      <vehicleClass>VC_OFF_ROAD</vehicleClass>
      <wheelType>VWT_OFFROAD</wheelType>
      <trailers />
      <additionalTrailers />
      <drivers />
      <extraIncludes />
      <doorsWithCollisionWhenClosed />
      <driveableDoors />
      <bumpersNeedToCollideWithMap value="false" />
      <needsRopeTexture value="false" />
      <requiredExtras />
      <rewards />
      <cinematicPartCamera>
        <Item>WHEEL_FRONT_RIGHT_CAMERA</Item>
        <Item>WHEEL_FRONT_LEFT_CAMERA</Item>
        <Item>WHEEL_REAR_RIGHT_CAMERA</Item>
        <Item>WHEEL_REAR_LEFT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet />
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000" />
      <buoyancySphereSizeScale value="1.000000" />
      <pOverrideRagdollThreshold type="NULL" />
      <firstPersonDrivebyData>
        <Item>STD_BRUISER_FRONT_LEFT</Item>
        <Item>STD_BRUISER_FRONT_RIGHT</Item>
        <Item>STD_BRUISER_REAR_LEFT</Item>
        <Item>STD_BRUISER_REAR_RIGHT</Item>
      </firstPersonDrivebyData>
    </Item>
	<Item>
      <modelName>bruiser3</modelName>
      <txdName>bruiser3</txdName>
      <handlingId>BRUISER</handlingId>
      <gameName>BRUISER3</gameName>
      <vehicleMakeName>BENEFAC</vehicleMakeName>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected />
      <ptfxAssetName>weap_xs_vehicle_weapons</ptfxAssetName>
      <audioNameHash />
      <layout>LAYOUT_STD_BRUISER</layout>
      <coverBoundOffsets>BRUISER_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_TRUCK</explosionInfo>
      <scenarioLayout />
      <cameraName>DEFAULT_FOLLOW_VEHICLE_CAMERA</cameraName>
      <aimCameraName>BOX_VEHICLE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>VEHICLE_BONNET_CAMERA_LOW</bonnetCameraName>
      <povCameraName>REDUCED_NEAR_CLIP_POV_CAMERA</povCameraName>
      <FirstPersonDriveByIKOffset x="0.080000" y="-0.110000" z="-0.080000" />
      <FirstPersonDriveByUnarmedIKOffset x="-0.020000" y="-0.060000" z="-0.020000" />
	  <FirstPersonProjectileDriveByIKOffset x="0.063000" y="-0.088000" z="-0.070000" />
	  <FirstPersonProjectileDriveByPassengerIKOffset x="0.000000" y="-0.088000" z="-0.070000" />
	  <FirstPersonProjectileDriveByRearLeftIKOffset x="-0.040000" y="-0.088000" z="-0.070000" />
	  <FirstPersonProjectileDriveByRearRightIKOffset x="0.050000" y="-0.060000" z="-0.090000" />
	  <FirstPersonDriveByLeftPassengerIKOffset x="0.020000" y="-0.060000" z="-0.020000" />
	  <FirstPersonDriveByRightPassengerIKOffset x="-0.010000" y="-0.110000" z="-0.080000" />
	  <FirstPersonDriveByRightRearPassengerIKOffset x="-0.005000" y="-0.060000" z="-0.020000" />
	  <FirstPersonDriveByLeftPassengerUnarmedIKOffset x="0.020000" y="-0.060000" z="-0.020000" />
	  <FirstPersonDriveByRightPassengerUnarmedIKOffset x="0.000000" y="-0.108000" z="-0.013000" />
	  <FirstPersonMobilePhoneOffset x="0.133000" y="0.165000" z="0.520000" />
      <FirstPersonPassengerMobilePhoneOffset x="0.213000" y="0.240000" z="0.445000" />
      <FirstPersonMobilePhoneSeatIKOffset>
		<Item>
			<Offset x="0.213000" y="0.240000" z="0.445000" />
			<SeatIndex value="2" />
		</Item>
		<Item>
			<Offset x="0.213000" y="0.240000" z="0.445000" />
			<SeatIndex value="3" />
		</Item>
      </FirstPersonMobilePhoneSeatIKOffset>
      <PovCameraOffset x="0.000000" y="-0.185000" z="0.645000" />
      <PovCameraVerticalAdjustmentForRollCage value="0.000000" />
      <PovPassengerCameraOffset x="0.000000" y="0.100000" z="0.040000" />
      <PovRearPassengerCameraOffset x="0.000000" y="0.100000" z="0.040000" />
      <vfxInfoName>VFXVEHICLEINFO_TRUCK_RIG</vfxInfoName>
      <shouldUseCinematicViewMode value="true" />
      <shouldCameraTransitionOnClimbUpDown value="false" />
      <shouldCameraIgnoreExiting value="false" />
      <AllowPretendOccupants value="true" />
      <AllowJoyriding value="false" />
      <AllowSundayDriving value="false" />
      <AllowBodyColorMapping value="true" />
      <wheelScale value="0.351400" />
      <wheelScaleRear value="0.351400" />
      <dirtLevelMin value="0.000000" />
      <dirtLevelMax value="0.450000" />
      <envEffScaleMin value="1.000000" />
      <envEffScaleMax value="1.000000" />
      <envEffScaleMin2 value="1.000000" />
      <envEffScaleMax2 value="1.000000" />
      <damageMapScale value="0.300000" />
      <damageOffsetScale value="0.300000" />
      <diffuseTint value="0x00FFFFFF" />
      <steerWheelMult value="1.000000" />
      <HDTextureDist value="5.000000" />
      <lodDistances content="float_array">
        20.000000	
        90.000000	
        130.000000	
        260.000000	
        750.000000	
        750.000000
      </lodDistances>
      <minSeatHeight value="0.600" />
      <identicalModelSpawnDistance value="20" />
      <maxNumOfSameColor value="10" />
      <defaultBodyHealth value="1000.000000" />
      <pretendOccupantsScale value="1.000000" />
      <visibleSpawnDistScale value="1.000000" />
      <trackerPathWidth value="2.000000" />
      <weaponForceMult value="1.000000" />
      <frequency value="10" />
      <swankness>SWANKNESS_1</swankness>
      <maxNum value="999" />
      <flags>FLAG_CAN_HAVE_NEONS FLAG_USE_AIRCRAFT_STYLE_WEAPON_TARGETING FLAG_HAS_NITROUS_MOD FLAG_HAS_JUMP_MOD FLAG_NO_BOOT FLAG_BIG FLAG_AVOID_TURNS FLAG_INCREASE_LOW_SPEED_TORQUE FLAG_HAS_RAMMING_SCOOP_MOD FLAG_USE_AIRCRAFT_STYLE_WEAPON_TARGETING FLAG_HAS_WEAPON_SPIKE_MODS FLAG_HAS_SIDE_SHUNT FLAG_HAS_RAMMING_BAR_MOD</flags>
      <type>VEHICLE_TYPE_CAR</type>
      <plateType>VPT_NONE</plateType>
      <dashboardType>VDT_DUKES</dashboardType>
      <vehicleClass>VC_OFF_ROAD</vehicleClass>
      <wheelType>VWT_OFFROAD</wheelType>
      <trailers />
      <additionalTrailers />
      <drivers />
      <extraIncludes />
      <doorsWithCollisionWhenClosed />
      <driveableDoors />
      <bumpersNeedToCollideWithMap value="false" />
      <needsRopeTexture value="false" />
      <requiredExtras />
      <rewards />
      <cinematicPartCamera>
        <Item>WHEEL_FRONT_RIGHT_CAMERA</Item>
        <Item>WHEEL_FRONT_LEFT_CAMERA</Item>
        <Item>WHEEL_REAR_RIGHT_CAMERA</Item>
        <Item>WHEEL_REAR_LEFT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet />
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000" />
      <buoyancySphereSizeScale value="1.000000" />
      <pOverrideRagdollThreshold type="NULL" />
      <firstPersonDrivebyData>
        <Item>STD_BRUISER_FRONT_LEFT</Item>
        <Item>STD_BRUISER_FRONT_RIGHT</Item>
        <Item>STD_BRUISER_REAR_LEFT</Item>
        <Item>STD_BRUISER_REAR_RIGHT</Item>
      </firstPersonDrivebyData>
    </Item>
	<Item>
      <modelName>rcbandito</modelName>
      <txdName>rcbandito</txdName>
      <handlingId>RCBANDITO</handlingId>
      <gameName>RCBANDITO</gameName>
      <vehicleMakeName />
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <ptfxAssetName>veh_xs_vehicle_mods</ptfxAssetName>
      <layout>LAYOUT_LOW_DUNE</layout>
      <coverBoundOffsets />
      <scenarioLayout />
      <cameraName>RC_FOLLOW_VEHICLE_CAMERA</cameraName>
      <aimCameraName>DEFAULT_THIRD_PERSON_VEHICLE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>RC_BONNET_CAMERA</bonnetCameraName>
      <povCameraName />
      <FirstPersonDriveByIKOffset x="0.000000" y="-0.020000" z="0.020000" />
      <FirstPersonDriveByUnarmedIKOffset x="0.000000" y="-0.100000" z="0.000000" />
	  <FirstPersonProjectileDriveByIKOffset x="0.000000" y="0.000000" z="0.000000" />
	  <FirstPersonProjectileDriveByPassengerIKOffset x="-0.140000" y="0.000000" z="0.000000" />
	  <FirstPersonDriveByLeftPassengerIKOffset x="0.000000" y="0.000000" z="0.000000" />
	  <FirstPersonDriveByRightPassengerIKOffset x="0.000000" y="-0.020000" z="0.020000" />
	  <FirstPersonDriveByLeftPassengerUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000" />
	  <FirstPersonDriveByRightPassengerUnarmedIKOffset x="0.000000" y="-0.100000" z="0.000000" />
	  <FirstPersonMobilePhoneOffset x="0.140000" y="0.166000" z="0.526000" />
      <FirstPersonPassengerMobilePhoneOffset x="0.136000" y="0.138000" z="0.438000" />
      <PovCameraOffset x="0.000000" y="-0.250000" z="0.630000" />
      <PovCameraVerticalAdjustmentForRollCage value="0.000000" />
      <PovPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000" />
      <vfxInfoName>VFXVEHICLEINFO_RC</vfxInfoName>
      <shouldUseCinematicViewMode value="true" />
      <shouldCameraExitUseInterrupt value="false" />
      <shouldCameraIgnoreExiting value="false" />
      <AllowPretendOccupants value="true" />
      <AllowJoyriding value="true" />
      <AllowSundayDriving value="true" />
      <AllowBodyColorMapping value="true" />
      <wheelScale value="0.010200" />
      <wheelScaleRear value="0.010200" />
      <dirtLevelMin value="0.000000" />
      <dirtLevelMax value="1.000000" />
      <envEffScaleMin value="0.000000" />
      <envEffScaleMax value="1.000000" />
      <envEffScaleMin2 value="0.000000" />
      <envEffScaleMax2 value="1.000000" />
      <damageMapScale value="0.10000" />
      <damageOffsetScale value="0.10000" />
      <diffuseTint value="0x57000000" />
      <steerWheelMult value="1.200000" />
      <HDTextureDist value="5.000000" />
      <lodDistances content="float_array">
        10.000000	
        25.000000	
        60.000000	
        120.000000	
        500.000000	
        500.000000
      </lodDistances>
      <minSeatHeight value="10" />
      <identicalModelSpawnDistance value="20" />
      <maxNumOfSameColor value="10" />
      <defaultBodyHealth value="1000.000000" />
      <pretendOccupantsScale value="1.000000" />
      <frequency value="30" />
      <swankness>SWANKNESS_1</swankness>
      <vehicleClass>VC_OFF_ROAD</vehicleClass>
      <maxNum value="5" />
      <flags>FLAG_AVERAGE_CAR FLAG_POOR_CAR FLAG_IS_OFFROAD_VEHICLE FLAG_IS_ELECTRIC FLAG_FORCE_BONNET_CAMERA_INSTEAD_OF_POV FLAG_HAS_JUMP_MOD FLAG_DISABLE_DEFORMATION</flags>
      <type>VEHICLE_TYPE_CAR</type>
      <plateType>VPT_NONE</plateType>
      <wheelType>VWT_OFFROAD</wheelType>
      <trailers />
      <drivers />
      <extraIncludes />
      <doorsWithCollisionWhenClosed />
      <driveableDoors />
      <requiredExtras />
      <rewards />
      <cinematicPartCamera />
      <NmBraceOverrideSet>Truck</NmBraceOverrideSet>
	  <firstPersonDrivebyData>
        <Item>LOW_DUNE_FRONT_LEFT</Item>
        <Item>LOW_DUNE_FRONT_RIGHT</Item>
      </firstPersonDrivebyData>
	  <numSeatsOverride value="1" />
    </Item>
	<Item>
      <modelName>italigto</modelName>
      <txdName>italigto</txdName>
      <handlingId>ITALIGTO</handlingId>
      <gameName>ITALIGTO</gameName>
      <vehicleMakeName>GROTTI</vehicleMakeName>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected />
      <ptfxAssetName>null</ptfxAssetName>
      <audioNameHash />
      <layout>LAYOUT_LOW_ITALIGTO</layout>
      <coverBoundOffsets>ITALIGTO_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout />
      <cameraName>FOLLOW_CHEETAH_CAMERA</cameraName>
      <aimCameraName>DEFAULT_THIRD_PERSON_VEHICLE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>VEHICLE_BONNET_CAMERA_STANDARD_LONG_EXTRA_LOW</bonnetCameraName>
      <povCameraName>REDUCED_NEAR_CLIP_POV_CAMERA</povCameraName>
      <FirstPersonDriveByIKOffset x="0.000000" y="-0.100000" z="-0.040000" />
      <FirstPersonDriveByUnarmedIKOffset x="0.000000" y="-0.160000" z="-0.025000" />
	  <FirstPersonProjectileDriveByIKOffset x="0.010000" y="-0.140000" z="-0.060000" />
	  <FirstPersonProjectileDriveByPassengerIKOffset x="-0.020000" y="-0.140000" z="-0.060000" />
	  <FirstPersonDriveByRightPassengerIKOffset x="0.000000" y="-0.100000" z="-0.040000" />
	  <FirstPersonDriveByRightPassengerUnarmedIKOffset x="0.000000" y="-0.160000" z="-0.025000" />
	  <FirstPersonMobilePhoneOffset x="0.133000" y="0.133000" z="0.526000" />
      <FirstPersonPassengerMobilePhoneOffset x="0.209000" y="0.124000" z="0.401000" />
      <PovCameraOffset x="0.000000" y="-0.285000" z="0.650000" />
      <PovCameraVerticalAdjustmentForRollCage value="0.000000" />
      <PovPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000" />
      <PovRearPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000" />
      <vfxInfoName>VFXVEHICLEINFO_CAR_GENERIC</vfxInfoName>
      <shouldUseCinematicViewMode value="true" />
      <shouldCameraTransitionOnClimbUpDown value="false" />
      <shouldCameraIgnoreExiting value="false" />
      <AllowPretendOccupants value="true" />
      <AllowJoyriding value="true" />
      <AllowSundayDriving value="true" />
      <AllowBodyColorMapping value="true" />
      <wheelScale value="0.274500" />
      <wheelScaleRear value="0.274500" />
      <dirtLevelMin value="0.000000" />
      <dirtLevelMax value="0.400000" />
      <envEffScaleMin value="0.000000" />
      <envEffScaleMax value="1.000000" />
      <envEffScaleMin2 value="0.000000" />
      <envEffScaleMax2 value="1.000000" />
      <damageMapScale value="0.40000" />
      <damageOffsetScale value="0.40000" />
      <diffuseTint value="0x00FFFFFF" />
      <steerWheelMult value="1.000000" />
      <HDTextureDist value="5.000000" />
      <lodDistances content="float_array">
        10.000000
        25.000000
        60.000000
        120.000000
        500.000000
        500.000000
      </lodDistances>
      <identicalModelSpawnDistance value="20" />
      <maxNumOfSameColor value="1" />
      <defaultBodyHealth value="1000.000000" />
      <pretendOccupantsScale value="1.000000" />
      <visibleSpawnDistScale value="1.000000" />
      <trackerPathWidth value="2.000000" />
      <weaponForceMult value="1.000000" />
      <frequency value="30" />
      <swankness>SWANKNESS_5</swankness>
      <maxNum value="4" />
      <flags>FLAG_SPORTS FLAG_RICH_CAR FLAG_RECESSED_HEADLIGHT_CORONAS FLAG_NO_BROKEN_DOWN_SCENARIO FLAG_COUNT_AS_FACEBOOK_DRIVEN</flags>
      <type>VEHICLE_TYPE_CAR</type>
      <plateType>VPT_BACK_PLATES</plateType>
      <dashboardType>VDT_RACE</dashboardType>
      <vehicleClass>VC_SPORT</vehicleClass>
      <wheelType>VWT_HIEND</wheelType>
      <trailers />
      <additionalTrailers />
      <drivers />
      <extraIncludes />
      <doorsWithCollisionWhenClosed />
      <driveableDoors />
      <bumpersNeedToCollideWithMap value="false" />
      <needsRopeTexture value="false" />
      <requiredExtras />
      <rewards />
      <cinematicPartCamera>
        <Item>WHEEL_FRONT_RIGHT_CAMERA</Item>
        <Item>WHEEL_FRONT_LEFT_CAMERA</Item>
        <Item>WHEEL_REAR_RIGHT_CAMERA</Item>
        <Item>WHEEL_REAR_LEFT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet />
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000" />
      <buoyancySphereSizeScale value="1.000000" />
      <pOverrideRagdollThreshold type="NULL" />
      <firstPersonDrivebyData>
        <Item>LOW_ITALIGTO_FRONT_LEFT</Item>
        <Item>LOW_ITALIGTO_FRONT_RIGHT</Item>
      </firstPersonDrivebyData>
    </Item>
	<Item>      
      <modelName>cerberus</modelName>
      <txdName>cerberus</txdName>
      <handlingId>CERBERUS</handlingId>
      <gameName>cerberus</gameName>
      <vehicleMakeName>MTL</vehicleMakeName>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected />
      <ptfxAssetName>weap_xs_vehicle_weapons</ptfxAssetName>
      <audioNameHash />
      <layout>LAYOUT_TRUCK_CERBERUS</layout>
      <coverBoundOffsets>CERBERUS_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout />
      <cameraName>DEFAULT_FOLLOW_VEHICLE_CAMERA</cameraName>
      <aimCameraName>DEFAULT_THIRD_PERSON_NO_LOCKON_VEHICLE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>VEHICLE_BONNET_CAMERA_STANDARD_LONG</bonnetCameraName>
      <povCameraName>DEFAULT_POV_CAMERA_NO_REVERSE_LOOKAROUND_MID</povCameraName>
      <FirstPersonDriveByIKOffset x="0.050000" y="-0.070000" z="-0.010000" />
      <FirstPersonDriveByUnarmedIKOffset x="0.000000" y="-0.100000" z="0.000000" />
	  <FirstPersonProjectileDriveByIKOffset x="0.040000" y="-0.100000" z="-0.020000" />
	  <FirstPersonProjectileDriveByPassengerIKOffset x="-0.040000" y="-0.030000" z="-0.060000" />
	  <FirstPersonDriveByLeftPassengerIKOffset x="0.000000" y="0.000000" z="0.000000" />
	  <FirstPersonDriveByRightPassengerIKOffset x="-0.050000" y="-0.070000" z="-0.010000" />
	  <FirstPersonDriveByRightRearPassengerIKOffset x="0.000000" y="0.000000" z="0.000000" />
	  <FirstPersonDriveByLeftPassengerUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000" />
	  <FirstPersonDriveByRightPassengerUnarmedIKOffset x="0.000000" y="-0.100000" z="0.000000" />
	  <FirstPersonMobilePhoneOffset x="0.131000" y="0.250000" z="0.503000" />
      <FirstPersonPassengerMobilePhoneOffset x="0.240000" y="0.200000" z="0.495000" />
      <PovCameraOffset x="0.000000" y="-0.095000" z="0.610000" />
      <PovCameraVerticalAdjustmentForRollCage value="0.000000" />
      <PovPassengerCameraOffset x="0.000000" y="0.000000" z="0.100000" />
      <vfxInfoName>VFXVEHICLEINFO_TRUCK_RIG</vfxInfoName>
      <shouldUseCinematicViewMode value="true" />
      <shouldCameraTransitionOnClimbUpDown value="false" />
      <shouldCameraIgnoreExiting value="false" />
      <AllowPretendOccupants value="true" />
      <AllowJoyriding value="true" />
      <AllowSundayDriving value="true" />
      <AllowBodyColorMapping value="true" />
      <wheelScale value="0.353600" />
      <wheelScaleRear value="0.321000" />
      <dirtLevelMin value="0.000000" />
      <dirtLevelMax value="0.700000" />
      <envEffScaleMin value="1.000000" />
      <envEffScaleMax value="1.000000" />
      <envEffScaleMin2 value="1.000000" />
      <envEffScaleMax2 value="1.000000" />
      <damageMapScale value="0.200000" />
      <damageOffsetScale value="0.200000" />
      <diffuseTint value="0x00FFFFFF" />
      <steerWheelMult value="0.500000" />
      <HDTextureDist value="5.000000" />
      <lodDistances content="float_array">
        20.000000
        90.000000
        130.000000
        260.000000
        750.000000
        750.000000
      </lodDistances>
      <minSeatHeight value="0.852" />
      <identicalModelSpawnDistance value="20" />
      <maxNumOfSameColor value="10" />
      <defaultBodyHealth value="1000.000000" />
      <pretendOccupantsScale value="1.000000" />
      <visibleSpawnDistScale value="1.000000" />
      <trackerPathWidth value="2.000000" />
      <weaponForceMult value="1.000000" />
      <frequency value="50" />
      <swankness>SWANKNESS_1</swankness>
      <maxNum value="10" />
      <flags>FLAG_BIG FLAG_NO_BOOT FLAG_AVOID_TURNS FLAG_DELIVERY FLAG_EXTRAS_REQUIRE FLAG_PEDS_CAN_STAND_ON_TOP FLAG_USE_FAT_INTERIOR_LIGHT FLAG_IS_BULKY FLAG_BLOCK_FROM_ATTRACTOR_SCENARIO FLAG_HAS_BULLET_RESISTANT_GLASS FLAG_HAS_REAR_MOUNTED_TURRET FLAG_TURRET_MODS_ON_ROOF FLAG_RESET_TURRET_SEAT_HEADING FLAG_HAS_WEAPON_SPIKE_MODS FLAG_HAS_NITROUS_MOD FLAG_HAS_JUMP_MOD FLAG_HAS_SIDE_SHUNT FLAG_HAS_RAMMING_SCOOP_MOD FLAG_HAS_RAMMING_BAR_MOD FLAG_INCREASE_LOW_SPEED_TORQUE</flags>
      <type>VEHICLE_TYPE_CAR</type>
      <plateType>VPT_FRONT_AND_BACK_PLATES</plateType>
	  <dashboardType>VDT_TRUCK</dashboardType>
      <vehicleClass>VC_COMMERCIAL</vehicleClass>
      <wheelType>VWT_SPORT</wheelType>
      <trailers />
      <additionalTrailers />
      <drivers />
      <extraIncludes />
      <doorsWithCollisionWhenClosed>
        <Item>VEH_EXT_DOOR_DSIDE_R</Item>
        <Item>VEH_EXT_DOOR_PSIDE_R</Item>
      </doorsWithCollisionWhenClosed>
      <driveableDoors />
      <bumpersNeedToCollideWithMap value="false" />
      <needsRopeTexture value="false" />
      <requiredExtras />
      <rewards />
      <cinematicPartCamera>
        <Item>WHEEL_WIDE_REAR_RIGHT_CAMERA</Item>
        <Item>WHEEL_WIDE_REAR_LEFT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet />
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000" />
      <buoyancySphereSizeScale value="1.000000" />
      <pOverrideRagdollThreshold type="NULL" />
	  <firstPersonDrivebyData />
    </Item>
	<Item>
      <modelName>impaler2</modelName>
      <txdName>impaler2</txdName>
      <handlingId>IMPALER2</handlingId>
      <gameName>IMPALER2</gameName>
      <vehicleMakeName>DECLASSE</vehicleMakeName>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected />
      <ptfxAssetName>weap_xs_vehicle_weapons</ptfxAssetName>
      <audioNameHash />
      <layout>LAYOUT_STD_ARENA_1HONLY</layout>
      <coverBoundOffsets>IMPALER_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout />
      <cameraName>FOLLOW_CHEETAH_CAMERA</cameraName>
      <aimCameraName>DEFAULT_THIRD_PERSON_VEHICLE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>VEHICLE_BONNET_CAMERA_STANDARD_LONG_DEVIANT</bonnetCameraName>
      <povCameraName>REDUCED_NEAR_CLIP_POV_CAMERA</povCameraName>
      <FirstPersonDriveByIKOffset x="0.020000" y="-0.065000" z="-0.050000" />
      <FirstPersonDriveByUnarmedIKOffset x="0.000000" y="-0.100000" z="0.000000" />
	  <FirstPersonProjectileDriveByIKOffset x="0.000000" y="-0.130000" z="-0.050000" />
	  <FirstPersonProjectileDriveByPassengerIKOffset x="0.000000" y="-0.100000" z="0.000000" />
	  <FirstPersonDriveByRightPassengerIKOffset x="-0.020000" y="-0.065000" z="-0.050000" />
	  <FirstPersonDriveByRightPassengerUnarmedIKOffset x="0.000000" y="-0.100000" z="0.000000" />
	  <FirstPersonMobilePhoneOffset x="0.146000" y="0.220000" z="0.510000" />
      <FirstPersonPassengerMobilePhoneOffset x="0.234000" y="0.169000" z="0.395000" />
      <PovCameraOffset x="0.000000" y="-0.195000" z="0.640000" />
      <PovCameraVerticalAdjustmentForRollCage value="0.000000" />
      <PovPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000" />
      <PovRearPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000" />
      <vfxInfoName>VFXVEHICLEINFO_CAR_GENERIC</vfxInfoName>
      <shouldUseCinematicViewMode value="true" />
      <shouldCameraTransitionOnClimbUpDown value="false" />
      <shouldCameraIgnoreExiting value="false" />
      <AllowPretendOccupants value="true" />
      <AllowJoyriding value="true" />
      <AllowSundayDriving value="true" />
      <AllowBodyColorMapping value="true" />
      <wheelScale value="0.225000" />
      <wheelScaleRear value="0.201800" />
      <dirtLevelMin value="0.000000" />
      <dirtLevelMax value="0.450000" />
      <envEffScaleMin value="1.000000" />
      <envEffScaleMax value="1.000000" />
      <envEffScaleMin2 value="1.000000" />
      <envEffScaleMax2 value="1.000000" />
      <damageMapScale value="0.400000" />
      <damageOffsetScale value="0.400000" />
      <diffuseTint value="0x00FFFFFF" />
      <steerWheelMult value="0.700000" />
      <HDTextureDist value="5.000000" />
      <lodDistances content="float_array">
        10.000000
        25.000000
        60.000000
        120.000000
        500.000000
        500.000000
      </lodDistances>
      <minSeatHeight value="0.844" />
      <identicalModelSpawnDistance value="20" />
      <maxNumOfSameColor value="1" />
      <defaultBodyHealth value="1000.000000" />
      <pretendOccupantsScale value="1.000000" />
      <visibleSpawnDistScale value="1.000000" />
      <trackerPathWidth value="2.000000" />
      <weaponForceMult value="1.000000" />
      <frequency value="30" />
      <swankness>SWANKNESS_4</swankness>
      <maxNum value="10" />
      <flags>FLAG_RECESSED_HEADLIGHT_CORONAS FLAG_EXTRAS_STRONG FLAG_AVERAGE_CAR FLAG_HAS_INTERIOR_EXTRAS FLAG_CAN_HAVE_NEONS FLAG_HAS_JUMP_MOD FLAG_HAS_NITROUS_MOD FLAG_HAS_RAMMING_SCOOP_MOD FLAG_USE_AIRCRAFT_STYLE_WEAPON_TARGETING FLAG_HAS_SIDE_SHUNT FLAG_HAS_WEAPON_SPIKE_MODS FLAG_HAS_SUPERCHARGER FLAG_INCREASE_CAMBER_WITH_SUSPENSION_MOD</flags>
      <type>VEHICLE_TYPE_CAR</type>
      <plateType>VPT_FRONT_AND_BACK_PLATES</plateType>
	  <dashboardType>VDT_DUKES</dashboardType>
      <vehicleClass>VC_MUSCLE</vehicleClass>
      <wheelType>VWT_MUSCLE</wheelType>
      <trailers />
      <additionalTrailers />
      <drivers />
      <extraIncludes />
      <doorsWithCollisionWhenClosed />
      <driveableDoors />
      <bumpersNeedToCollideWithMap value="false" />
      <needsRopeTexture value="false" />
      <requiredExtras />
      <rewards />
      <cinematicPartCamera>
        <Item>WHEEL_FRONT_RIGHT_CAMERA</Item>
        <Item>WHEEL_FRONT_LEFT_CAMERA</Item>
        <Item>WHEEL_REAR_RIGHT_CAMERA</Item>
        <Item>WHEEL_REAR_LEFT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet />
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000" />
      <buoyancySphereSizeScale value="1.000000" />
      <pOverrideRagdollThreshold type="NULL" />
	  <firstPersonDrivebyData>
        <Item>STD_IMPALER2_FRONT_LEFT</Item>
        <Item>STD_IMPALER2_FRONT_RIGHT</Item>
      </firstPersonDrivebyData>
    </Item>
	<Item>
      <modelName>monster3</modelName>
      <txdName>monster3</txdName>
      <handlingId>MONSTER3</handlingId>
      <gameName>MONSTER3</gameName>
      <vehicleMakeName>BRAVADO</vehicleMakeName>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected />
      <ptfxAssetName>weap_xs_vehicle_weapons</ptfxAssetName>
      <audioNameHash />
      <layout>LAYOUT_TRUCK_MONSTER3</layout>
      <coverBoundOffsets>MONSTER3_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout />
      <cameraName>MONSTER_FOLLOW_VEHICLE_CAMERA</cameraName>
      <aimCameraName>DEFAULT_THIRD_PERSON_VEHICLE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>VEHICLE_BONNET_CAMERA_STANDARD_LONG</bonnetCameraName>
      <povCameraName>DEFAULT_POV_CAMERA_LOOKAROUND</povCameraName>
      <FirstPersonDriveByIKOffset x="-0.030000" y="-0.050000" z="-0.05000" />
      <FirstPersonDriveByUnarmedIKOffset x="0.000000" y="-0.120000" z="-0.030000" />
      <FirstPersonProjectileDriveByIKOffset x="0.04000" y="-0.120000" z="-0.058000" />
      <FirstPersonProjectileDriveByPassengerIKOffset x="-0.03000" y="-0.130000" z="-0.058000" />
      <FirstPersonDriveByRightPassengerIKOffset x="0.000000" y="-0.040000" z="-0.05000" />
      <FirstPersonDriveByRightPassengerUnarmedIKOffset x="0.000000" y="-0.120000" z="-0.030000" />
      <FirstPersonMobilePhoneOffset x="0.120000" y="0.336000" z="0.503000" />
      <FirstPersonPassengerMobilePhoneOffset x="0.253000" y="0.343000" z="0.453000" />
      <PovCameraOffset x="0.000000" y="-0.020000" z="0.650000" />
      <PovCameraVerticalAdjustmentForRollCage value="0.000000" />
      <PovPassengerCameraOffset x="0.000000" y="0.000000" z="0.080000" />
      <vfxInfoName>VFXVEHICLEINFO_CAR_OFFROAD</vfxInfoName>
      <shouldUseCinematicViewMode value="true" />
      <shouldCameraTransitionOnClimbUpDown value="false" />
      <shouldCameraIgnoreExiting value="false" />
      <AllowPretendOccupants value="true" />
      <AllowJoyriding value="true" />
      <AllowSundayDriving value="true" />
      <AllowBodyColorMapping value="true" />
      <wheelScale value="0.388000" />
      <wheelScaleRear value="0.388000" />
      <dirtLevelMin value="0.000000" />
      <dirtLevelMax value="1.000000" />
      <envEffScaleMin value="1.000000" />
      <envEffScaleMax value="1.000000" />
      <envEffScaleMin2 value="1.000000" />
      <envEffScaleMax2 value="1.000000" />
      <damageMapScale value="0.200000" />
      <damageOffsetScale value="0.200000" />
      <diffuseTint value="0x00FFFFFF" />
      <steerWheelMult value="1.000000" />
      <HDTextureDist value="5.000000" />
      <lodDistances content="float_array">
        15
        35
        90
        200
        500
        500
      </lodDistances>
      <minSeatHeight value="0.883" /> 
      <identicalModelSpawnDistance value="20" />
      <maxNumOfSameColor value="10" />
      <defaultBodyHealth value="1000.000000" />
      <pretendOccupantsScale value="1.000000" />
      <visibleSpawnDistScale value="1.000000" />
      <trackerPathWidth value="2.000000" />
      <weaponForceMult value="1.000000" />
      <frequency value="30" />
      <swankness>SWANKNESS_1</swankness>
      <maxNum value="5" />
      <flags>FLAG_IS_OFFROAD_VEHICLE FLAG_IS_BULKY FLAG_EXTRAS_REQUIRE FLAG_EXTRAS_STRONG FLAG_USE_RESTRICTED_DRIVEBY_HEIGHT FLAG_CRUSHES_OTHER_VEHICLES FLAG_HAS_JUMP_MOD FLAG_HAS_NITROUS_MOD FLAG_HAS_RAMMING_SCOOP_MOD FLAG_USE_AIRCRAFT_STYLE_WEAPON_TARGETING FLAG_HAS_SIDE_SHUNT FLAG_HAS_RAMMING_BAR_MOD FLAG_TURRET_MODS_ON_CHASSIS5 FLAG_TURRET_MOD_WITH_NO_STOCK_TURRET</flags>
      <type>VEHICLE_TYPE_CAR</type>
      <plateType>VPT_NONE</plateType>
      <dashboardType>VDT_DUKES</dashboardType>
      <vehicleClass>VC_OFF_ROAD</vehicleClass>
      <wheelType>VWT_SUV</wheelType>
      <trailers />
      <additionalTrailers />
      <drivers />
      <extraIncludes />
      <doorsWithCollisionWhenClosed />
      <driveableDoors />
      <bumpersNeedToCollideWithMap value="false" />
      <needsRopeTexture value="false" />
      <requiredExtras />
      <rewards />
      <cinematicPartCamera>
        <Item>WHEEL_WIDE_REAR_RIGHT_CAMERA</Item>
        <Item>WHEEL_WIDE_REAR_LEFT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet>Truck</NmBraceOverrideSet>
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000" />
      <buoyancySphereSizeScale value="1.000000" />
      <pOverrideRagdollThreshold type="NULL" />
      <firstPersonDrivebyData>
        <Item>TRUCK_MONSTER3_FRONT_LEFT</Item>
        <Item>TRUCK_MONSTER3_FRONT_RIGHT</Item>
      </firstPersonDrivebyData>
    </Item>
    <Item>
      <modelName>tulip</modelName>
      <txdName>tulip</txdName>
      <handlingId>TULIP</handlingId>
      <gameName>TULIP</gameName>
      <vehicleMakeName>DECLASSE</vehicleMakeName>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected />
      <ptfxAssetName>null</ptfxAssetName>
      <audioNameHash />
      <layout>LAYOUT_STANDARD</layout>
      <coverBoundOffsets>TULIP_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout />
      <cameraName>FOLLOW_CHEETAH_CAMERA</cameraName>
      <aimCameraName>DEFAULT_THIRD_PERSON_VEHICLE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>DEFAULT_VEHICLE_BONNET_CAMERA</bonnetCameraName>
      <povCameraName>DEFAULT_POV_CAMERA</povCameraName>
      <FirstPersonDriveByIKOffset x="0.050000" y="-0.060000" z="-0.060000" />
      <FirstPersonDriveByUnarmedIKOffset x="-0.025000" y="-0.050000" z="0.000000" />
	  <FirstPersonProjectileDriveByIKOffset x="0.040000" y="-0.100000" z="-0.040000" />
	  <FirstPersonProjectileDriveByPassengerIKOffset x="0.000000" y="-0.100000" z="0.000000" />
	  <FirstPersonProjectileDriveByRearLeftIKOffset x="-0.050000" y="-0.060000" z="-0.040000" />
	  <FirstPersonProjectileDriveByRearRightIKOffset x="0.050000" y="-0.060000" z="-0.090000" />
	  <FirstPersonDriveByLeftPassengerIKOffset x="0.070000" y="-0.050000" z="-0.060000" />
	  <FirstPersonDriveByRightPassengerIKOffset x="-0.050000" y="-0.100000" z="-0.060000" />
	  <FirstPersonDriveByRightRearPassengerIKOffset x="-0.050000" y="-0.060000" z="-0.040000" />
	  <FirstPersonDriveByLeftPassengerUnarmedIKOffset x="0.040000" y="0.010000" z="-0.020000" />
	  <FirstPersonDriveByRightPassengerUnarmedIKOffset x="-0.025000" y="-0.050000" z="0.000000" />
	  <FirstPersonMobilePhoneOffset x="0.146000" y="0.321000" z="0.518000" />
      <FirstPersonPassengerMobilePhoneOffset x="0.224000" y="0.279000" z="0.400000" />
      <FirstPersonMobilePhoneSeatIKOffset>
		<Item>
			<Offset x="0.204000" y="0.099000" z="0.445000" />
			<SeatIndex value="2" />
		</Item>
		<Item>
			<Offset x="0.204000" y="0.099000" z="0.445000" />
			<SeatIndex value="3" />
		</Item>
      </FirstPersonMobilePhoneSeatIKOffset>
      <PovCameraOffset x="0.000000" y="-0.105000" z="0.655000" />
      <PovCameraVerticalAdjustmentForRollCage value="0.000000" />
      <PovPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000" />
      <PovRearPassengerCameraOffset x="0.000000" y="-0.140000" z="0.030000" />
      <vfxInfoName>VFXVEHICLEINFO_CAR_GENERIC</vfxInfoName>
      <shouldUseCinematicViewMode value="true" />
      <shouldCameraTransitionOnClimbUpDown value="false" />
      <shouldCameraIgnoreExiting value="false" />
      <AllowPretendOccupants value="true" />
      <AllowJoyriding value="true" />
      <AllowSundayDriving value="true" />
      <AllowBodyColorMapping value="true" />
      <wheelScale value="0.189100" />
      <wheelScaleRear value="0.203700" />
      <dirtLevelMin value="0.000000" />
      <dirtLevelMax value="0.450000" />
      <envEffScaleMin value="0.000000" />
      <envEffScaleMax value="1.000000" />
      <envEffScaleMin2 value="0.000000" />
      <envEffScaleMax2 value="1.000000" />
      <damageMapScale value="0.300000" />
      <damageOffsetScale value="0.300000" />
      <diffuseTint value="0x00FFFFFF" />
      <steerWheelMult value="0.700000" />
      <HDTextureDist value="5.000000" />
      <lodDistances content="float_array">
        10.000000
        25.000000
        60.000000
        120.000000
        500.000000
        500.000000
      </lodDistances>
      <minSeatHeight value="0.844" />
      <identicalModelSpawnDistance value="20" />
      <maxNumOfSameColor value="1" />
      <defaultBodyHealth value="1000.000000" />
      <pretendOccupantsScale value="1.000000" />
      <visibleSpawnDistScale value="1.000000" />
      <trackerPathWidth value="2.000000" />
      <weaponForceMult value="1.000000" />
      <frequency value="30" />
      <swankness>SWANKNESS_4</swankness>
      <maxNum value="10" />
      <flags>FLAG_EXTRAS_STRONG FLAG_AVERAGE_CAR FLAG_HAS_INTERIOR_EXTRAS FLAG_CAN_HAVE_NEONS FLAG_HAS_SUPERCHARGER</flags>
      <type>VEHICLE_TYPE_CAR</type>
      <plateType>VPT_FRONT_AND_BACK_PLATES</plateType>
	  <dashboardType>VDT_DUKES</dashboardType>
      <vehicleClass>VC_MUSCLE</vehicleClass>
      <wheelType>VWT_MUSCLE</wheelType>
      <trailers />
      <additionalTrailers />
      <drivers />
      <extraIncludes />
      <doorsWithCollisionWhenClosed />
      <driveableDoors />
      <bumpersNeedToCollideWithMap value="false" />
      <needsRopeTexture value="false" />
      <requiredExtras />
      <rewards />
      <cinematicPartCamera>
        <Item>WHEEL_FRONT_RIGHT_CAMERA</Item>
        <Item>WHEEL_FRONT_LEFT_CAMERA</Item>
        <Item>WHEEL_REAR_RIGHT_CAMERA</Item>
        <Item>WHEEL_REAR_LEFT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet />
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000" />
      <buoyancySphereSizeScale value="1.000000" />
      <pOverrideRagdollThreshold type="NULL" />
	  <firstPersonDrivebyData>
        <Item>STD_TULIP_FRONT_LEFT</Item>
        <Item>STD_TULIP_FRONT_RIGHT</Item>
        <Item>STD_TULIP_REAR_LEFT</Item>
        <Item>STD_TULIP_REAR_RIGHT</Item>
      </firstPersonDrivebyData>
    </Item>
	<Item>
      <modelName>scarab</modelName>
      <txdName>scarab</txdName>
      <handlingId>SCARAB</handlingId>
      <gameName>SCARAB</gameName>
      <vehicleMakeName>HVY</vehicleMakeName>
      <expressionDictName>vehicle</expressionDictName>
      <expressionName>rhino</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected />
      <ptfxAssetName>weap_xs_vehicle_weapons</ptfxAssetName>
      <audioNameHash />
      <layout>LAYOUT_TANK_SCARAB</layout>
      <coverBoundOffsets>SCARAB_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout />
      <cameraName>DEFAULT_FOLLOW_VEHICLE_CAMERA</cameraName>
      <aimCameraName>DEFAULT_THIRD_PERSON_NO_LOCKON_VEHICLE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>VEHICLE_BONNET_CAMERA_SCARAB</bonnetCameraName>
      <povCameraName>DEFAULT_POV_CAMERA_NO_REVERSE_LOOKAROUND_MID</povCameraName>
      <FirstPersonDriveByIKOffset x="0.000000" y="-0.050000" z="-0.020000" />
      <FirstPersonDriveByUnarmedIKOffset x="-0.020000" y="-0.160000" z="-0.030000" />
	  <FirstPersonProjectileDriveByIKOffset x="0.000000" y="0.000000" z="0.000000" />
	  <FirstPersonProjectileDriveByPassengerIKOffset x="0.000000" y="0.000000" z="0.000000" />
	  <FirstPersonProjectileDriveByRearLeftIKOffset x="0.100000" y="0.090000" z="-0.090000" />
	  <FirstPersonProjectileDriveByRearRightIKOffset x="-0.180000" y="0.090000" z="-0.090000" />
	  <FirstPersonDriveByLeftPassengerIKOffset x="0.030000" y="0.030000" z="-0.043000" />
	  <FirstPersonDriveByRightPassengerIKOffset x="0.000000" y="-0.050000" z="-0.020000" />
	  <FirstPersonDriveByRightRearPassengerIKOffset x="-0.050000" y="0.020000" z="-0.043000" />
	  <FirstPersonDriveByLeftPassengerUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000" />
	  <FirstPersonDriveByRightPassengerUnarmedIKOffset x="-0.020000" y="-0.160000" z="-0.030000" />
      <FirstPersonMobilePhoneOffset x="0.155000" y="0.230000" z="0.470000" />
      <FirstPersonPassengerMobilePhoneOffset x="0.216000" y="0.323000" z="0.380000" />
      <FirstPersonMobilePhoneSeatIKOffset>
		<Item>
			<Offset x="0.190000" y="0.316000" z="0.623000" />
			<SeatIndex value="2" />
		</Item>
		<Item>
			<Offset x="0.190000" y="0.456000" z="0.623000" />
			<SeatIndex value="3" />
		</Item>
      </FirstPersonMobilePhoneSeatIKOffset>
      <PovCameraOffset x="0.000000" y="-0.110000" z="0.605000" />
      <PovCameraVerticalAdjustmentForRollCage value="0.000000" />
      <PovPassengerCameraOffset x="0.000000" y="0.160000" z="0.075000" />
      <PovRearPassengerCameraOffset x="0.000000" y="0.260000" z="0.240000" />
      <vfxInfoName>VFXVEHICLEINFO_TANK_SCARAB</vfxInfoName>
      <shouldUseCinematicViewMode value="true" />
      <shouldCameraTransitionOnClimbUpDown value="false" />
      <shouldCameraIgnoreExiting value="false" />
      <AllowPretendOccupants value="true" />
      <AllowJoyriding value="false" />
      <AllowSundayDriving value="false" />
      <AllowBodyColorMapping value="true" />
      <wheelScale value="0.510000" />
      <wheelScaleRear value="0.510000" />
      <dirtLevelMin value="0.000000" />
      <dirtLevelMax value="0.700000" />
      <envEffScaleMin value="1.000000" />
      <envEffScaleMax value="1.000000" />
      <envEffScaleMin2 value="1.000000" />
      <envEffScaleMax2 value="1.000000" />
      <damageMapScale value="0.100000" />
      <damageOffsetScale value="0.100000" />
      <diffuseTint value="0x00FFFFFF" />
      <steerWheelMult value="1.000000" />
      <HDTextureDist value="5.000000" />
      <lodDistances content="float_array">
        25.000000
        50.000000
        90.000000
        180.000000
        500.000000
        500.000000
      </lodDistances>
      <minSeatHeight value="0.400" />
      <identicalModelSpawnDistance value="20" />
      <maxNumOfSameColor value="10" />
      <defaultBodyHealth value="1000.000000" />
      <pretendOccupantsScale value="1.000000" />
      <visibleSpawnDistScale value="1.000000" />
      <trackerPathWidth value="2.000000" />
      <weaponForceMult value="1.000000" />
      <frequency value="100" />
      <swankness>SWANKNESS_2</swankness>
      <maxNum value="5" />
      <flags>FLAG_NO_BOOT FLAG_EXTRAS_ONLY_BREAK_WHEN_DESTROYED FLAG_EXTRAS_ALL FLAG_USE_HIGHER_DOOR_TORQUE FLAG_DONT_SPAWN_IN_CARGEN FLAG_DONT_SPAWN_AS_AMBIENT FLAG_IS_TANK FLAG_USE_COVERBOUND_INFO_FOR_COVERGEN FLAG_DONT_TIMESLICE_WHEELS FLAG_INCREASE_LOW_SPEED_TORQUE FLAG_CAN_NAVIGATE_TO_ON_VEHICLE_ENTRY FLAG_RESET_TURRET_SEAT_HEADING FLAG_USE_TURRET_RELATIVE_AIM_CALCULATION FLAG_HAS_RAMMING_SCOOP_MOD FLAG_USE_AIRCRAFT_STYLE_WEAPON_TARGETING FLAG_HAS_NITROUS_MOD FLAG_DISABLE_THROUGH_WINDSCREEN FLAG_HAS_JUMP_MOD FLAG_HAS_SIDE_SHUNT FLAG_HAS_WEAPON_SPIKE_MODS FLAG_HAS_RAMMING_BAR_MOD FLAG_IS_TANK_WITH_FLAME_DAMAGE</flags>
      <type>VEHICLE_TYPE_CAR</type>
      <plateType>VPT_NONE</plateType>
	  <dashboardType>VDT_RACE</dashboardType>
      <vehicleClass>VC_MILITARY</vehicleClass>
      <wheelType>VWT_SPORT</wheelType>
      <trailers />
      <additionalTrailers />
      <drivers />
      <extraIncludes />
      <doorsWithCollisionWhenClosed />
      <driveableDoors />
      <bumpersNeedToCollideWithMap value="false" />
      <needsRopeTexture value="false" />
      <requiredExtras />
      <rewards />
      <cinematicPartCamera />
      <NmBraceOverrideSet>Truck</NmBraceOverrideSet>
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000" />
      <buoyancySphereSizeScale value="1.000000" />
      <pOverrideRagdollThreshold type="CVehicleModelInfo__CVehicleOverrideRagdollThreshold">
        <MinComponent value="22" />
        <MaxComponent value="22" />
        <ThresholdMult value="1.500000" />
      </pOverrideRagdollThreshold>
	  <firstPersonDrivebyData>
        <Item>TANK_SCARAB_FRONT_LEFT</Item>
        <Item>TANK_SCARAB_FRONT_RIGHT</Item>
        <Item>VAN_BODHI_REAR_LEFT</Item>
        <Item>VAN_BODHI_REAR_RIGHT</Item>
      </firstPersonDrivebyData>
    </Item>
	<Item>
      <modelName>scarab2</modelName>
      <txdName>scarab2</txdName>
      <handlingId>SCARAB2</handlingId>
      <gameName>SCARAB2</gameName>
      <vehicleMakeName>HVY</vehicleMakeName>
      <expressionDictName>vehicle</expressionDictName>
      <expressionName>rhino</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected />
      <ptfxAssetName>weap_xs_vehicle_weapons</ptfxAssetName>
      <audioNameHash />
      <layout>LAYOUT_TANK_SCARAB</layout>
      <coverBoundOffsets>SCARAB_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout />
      <cameraName>DEFAULT_FOLLOW_VEHICLE_CAMERA</cameraName>
      <aimCameraName>DEFAULT_THIRD_PERSON_NO_LOCKON_VEHICLE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>VEHICLE_BONNET_CAMERA_SCARAB</bonnetCameraName>
      <povCameraName>DEFAULT_POV_CAMERA_NO_REVERSE_LOOKAROUND_MID</povCameraName>
      <FirstPersonDriveByIKOffset x="0.000000" y="-0.050000" z="-0.020000" />
      <FirstPersonDriveByUnarmedIKOffset x="-0.020000" y="-0.160000" z="-0.030000" />
	  <FirstPersonProjectileDriveByIKOffset x="0.000000" y="0.000000" z="0.000000" />
	  <FirstPersonProjectileDriveByPassengerIKOffset x="0.000000" y="0.000000" z="0.000000" />
	  <FirstPersonProjectileDriveByRearLeftIKOffset x="0.100000" y="0.090000" z="-0.090000" />
	  <FirstPersonProjectileDriveByRearRightIKOffset x="-0.180000" y="0.090000" z="-0.090000" />
	  <FirstPersonDriveByLeftPassengerIKOffset x="0.030000" y="0.030000" z="-0.043000" />
	  <FirstPersonDriveByRightPassengerIKOffset x="0.000000" y="-0.050000" z="-0.020000" />
	  <FirstPersonDriveByRightRearPassengerIKOffset x="-0.050000" y="0.020000" z="-0.043000" />
	  <FirstPersonDriveByLeftPassengerUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000" />
	  <FirstPersonDriveByRightPassengerUnarmedIKOffset x="-0.020000" y="-0.160000" z="-0.030000" />
      <FirstPersonMobilePhoneOffset x="0.155000" y="0.230000" z="0.470000" />
      <FirstPersonPassengerMobilePhoneOffset x="0.216000" y="0.323000" z="0.380000" />
      <FirstPersonMobilePhoneSeatIKOffset>
		<Item>
			<Offset x="0.190000" y="0.316000" z="0.623000" />
			<SeatIndex value="2" />
		</Item>
		<Item>
			<Offset x="0.190000" y="0.456000" z="0.623000" />
			<SeatIndex value="3" />
		</Item>
      </FirstPersonMobilePhoneSeatIKOffset>
      <PovCameraOffset x="0.000000" y="-0.110000" z="0.605000" />
      <PovCameraVerticalAdjustmentForRollCage value="0.000000" />
      <PovPassengerCameraOffset x="0.000000" y="0.160000" z="0.075000" />
      <PovRearPassengerCameraOffset x="0.000000" y="0.260000" z="0.240000" />
      <vfxInfoName>VFXVEHICLEINFO_TANK_SCARAB</vfxInfoName>
      <shouldUseCinematicViewMode value="true" />
      <shouldCameraTransitionOnClimbUpDown value="false" />
      <shouldCameraIgnoreExiting value="false" />
      <AllowPretendOccupants value="true" />
      <AllowJoyriding value="false" />
      <AllowSundayDriving value="false" />
      <AllowBodyColorMapping value="true" />
      <wheelScale value="0.510000" />
      <wheelScaleRear value="0.510000" />
      <dirtLevelMin value="0.000000" />
      <dirtLevelMax value="0.700000" />
      <envEffScaleMin value="1.000000" />
      <envEffScaleMax value="1.000000" />
      <envEffScaleMin2 value="1.000000" />
      <envEffScaleMax2 value="1.000000" />
      <damageMapScale value="0.000000" />
      <damageOffsetScale value="0.100000" />
      <diffuseTint value="0x00FFFFFF" />
      <steerWheelMult value="1.000000" />
      <HDTextureDist value="5.000000" />
      <lodDistances content="float_array">
        25.000000
        50.000000
        90.000000
        180.000000
        500.000000
        500.000000
      </lodDistances>
      <minSeatHeight value="0.400" />
      <identicalModelSpawnDistance value="20" />
      <maxNumOfSameColor value="10" />
      <defaultBodyHealth value="1000.000000" />
      <pretendOccupantsScale value="1.000000" />
      <visibleSpawnDistScale value="1.000000" />
      <trackerPathWidth value="2.000000" />
      <weaponForceMult value="1.000000" />
      <frequency value="100" />
      <swankness>SWANKNESS_2</swankness>
      <maxNum value="5" />
      <flags>FLAG_NO_BOOT FLAG_EXTRAS_ONLY_BREAK_WHEN_DESTROYED FLAG_EXTRAS_ALL FLAG_USE_HIGHER_DOOR_TORQUE FLAG_DONT_SPAWN_IN_CARGEN FLAG_DONT_SPAWN_AS_AMBIENT FLAG_IS_TANK FLAG_USE_COVERBOUND_INFO_FOR_COVERGEN FLAG_DONT_TIMESLICE_WHEELS FLAG_INCREASE_LOW_SPEED_TORQUE FLAG_CAN_NAVIGATE_TO_ON_VEHICLE_ENTRY FLAG_RESET_TURRET_SEAT_HEADING FLAG_USE_TURRET_RELATIVE_AIM_CALCULATION FLAG_HAS_RAMMING_SCOOP_MOD FLAG_USE_AIRCRAFT_STYLE_WEAPON_TARGETING FLAG_HAS_NITROUS_MOD FLAG_DISABLE_THROUGH_WINDSCREEN FLAG_HAS_JUMP_MOD FLAG_HAS_SIDE_SHUNT FLAG_HAS_WEAPON_SPIKE_MODS FLAG_HAS_RAMMING_BAR_MOD FLAG_IS_TANK_WITH_FLAME_DAMAGE FLAG_DISABLE_DEFORMATION</flags>
      <type>VEHICLE_TYPE_CAR</type>
      <plateType>VPT_NONE</plateType>
	  <dashboardType>VDT_RACE</dashboardType>
      <vehicleClass>VC_MILITARY</vehicleClass>
      <wheelType>VWT_SPORT</wheelType>
      <trailers />
      <additionalTrailers />
      <drivers />
      <extraIncludes />
      <doorsWithCollisionWhenClosed />
      <driveableDoors />
      <bumpersNeedToCollideWithMap value="false" />
      <needsRopeTexture value="false" />
      <requiredExtras />
      <rewards />
      <cinematicPartCamera />
      <NmBraceOverrideSet>Truck</NmBraceOverrideSet>
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000" />
      <buoyancySphereSizeScale value="1.000000" />
      <pOverrideRagdollThreshold type="CVehicleModelInfo__CVehicleOverrideRagdollThreshold">
        <MinComponent value="22" />
        <MaxComponent value="22" />
        <ThresholdMult value="1.500000" />
      </pOverrideRagdollThreshold>
	  <firstPersonDrivebyData>
        <Item>TANK_SCARAB_FRONT_LEFT</Item>
        <Item>TANK_SCARAB_FRONT_RIGHT</Item>
        <Item>VAN_BODHI_REAR_LEFT</Item>
        <Item>VAN_BODHI_REAR_RIGHT</Item>
      </firstPersonDrivebyData>
    </Item>
	<Item>
      <modelName>scarab3</modelName>
      <txdName>scarab3</txdName>
      <handlingId>SCARAB</handlingId>
      <gameName>SCARAB3</gameName>
      <vehicleMakeName>HVY</vehicleMakeName>
      <expressionDictName>vehicle</expressionDictName>
      <expressionName>rhino</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected />
      <ptfxAssetName>weap_xs_vehicle_weapons</ptfxAssetName>
      <audioNameHash />
      <layout>LAYOUT_TANK_SCARAB</layout>
      <coverBoundOffsets>SCARAB_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout />
      <cameraName>DEFAULT_FOLLOW_VEHICLE_CAMERA</cameraName>
      <aimCameraName>DEFAULT_THIRD_PERSON_NO_LOCKON_VEHICLE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>VEHICLE_BONNET_CAMERA_SCARAB</bonnetCameraName>
      <povCameraName>DEFAULT_POV_CAMERA_NO_REVERSE_LOOKAROUND_MID</povCameraName>
      <FirstPersonDriveByIKOffset x="0.000000" y="-0.050000" z="-0.020000" />
      <FirstPersonDriveByUnarmedIKOffset x="-0.020000" y="-0.160000" z="-0.030000" />
	  <FirstPersonProjectileDriveByIKOffset x="0.000000" y="0.000000" z="0.000000" />
	  <FirstPersonProjectileDriveByPassengerIKOffset x="0.000000" y="0.000000" z="0.000000" />
	  <FirstPersonProjectileDriveByRearLeftIKOffset x="0.100000" y="0.090000" z="-0.090000" />
	  <FirstPersonProjectileDriveByRearRightIKOffset x="-0.180000" y="0.090000" z="-0.090000" />
	  <FirstPersonDriveByLeftPassengerIKOffset x="0.030000" y="0.030000" z="-0.043000" />
	  <FirstPersonDriveByRightPassengerIKOffset x="0.000000" y="-0.050000" z="-0.020000" />
	  <FirstPersonDriveByRightRearPassengerIKOffset x="-0.050000" y="0.020000" z="-0.043000" />
	  <FirstPersonDriveByLeftPassengerUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000" />
	  <FirstPersonDriveByRightPassengerUnarmedIKOffset x="-0.020000" y="-0.160000" z="-0.030000" />
      <FirstPersonMobilePhoneOffset x="0.155000" y="0.230000" z="0.470000" />
      <FirstPersonPassengerMobilePhoneOffset x="0.216000" y="0.323000" z="0.380000" />
      <FirstPersonMobilePhoneSeatIKOffset>
		<Item>
			<Offset x="0.190000" y="0.316000" z="0.623000" />
			<SeatIndex value="2" />
		</Item>
		<Item>
			<Offset x="0.190000" y="0.456000" z="0.623000" />
			<SeatIndex value="3" />
		</Item>
      </FirstPersonMobilePhoneSeatIKOffset>
      <PovCameraOffset x="0.000000" y="-0.110000" z="0.605000" />
      <PovCameraVerticalAdjustmentForRollCage value="0.000000" />
      <PovPassengerCameraOffset x="0.000000" y="0.160000" z="0.075000" />
      <PovRearPassengerCameraOffset x="0.000000" y="0.260000" z="0.240000" />
      <vfxInfoName>VFXVEHICLEINFO_TANK_SCARAB</vfxInfoName>
      <shouldUseCinematicViewMode value="true" />
      <shouldCameraTransitionOnClimbUpDown value="false" />
      <shouldCameraIgnoreExiting value="false" />
      <AllowPretendOccupants value="true" />
      <AllowJoyriding value="false" />
      <AllowSundayDriving value="false" />
      <AllowBodyColorMapping value="true" />
      <wheelScale value="0.510000" />
      <wheelScaleRear value="0.510000" />
      <dirtLevelMin value="0.000000" />
      <dirtLevelMax value="0.700000" />
      <envEffScaleMin value="1.000000" />
      <envEffScaleMax value="1.000000" />
      <envEffScaleMin2 value="1.000000" />
      <envEffScaleMax2 value="1.000000" />
      <damageMapScale value="0.100000" />
      <damageOffsetScale value="0.100000" />
      <diffuseTint value="0x00FFFFFF" />
      <steerWheelMult value="1.000000" />
      <HDTextureDist value="5.000000" />
      <lodDistances content="float_array">
        25.000000
        50.000000
        90.000000
        180.000000
        500.000000
        500.000000
      </lodDistances>
      <minSeatHeight value="0.400" />
      <identicalModelSpawnDistance value="20" />
      <maxNumOfSameColor value="10" />
      <defaultBodyHealth value="1000.000000" />
      <pretendOccupantsScale value="1.000000" />
      <visibleSpawnDistScale value="1.000000" />
      <trackerPathWidth value="2.000000" />
      <weaponForceMult value="1.000000" />
      <frequency value="100" />
      <swankness>SWANKNESS_2</swankness>
      <maxNum value="5" />
	  <flags>FLAG_NO_BOOT FLAG_EXTRAS_ONLY_BREAK_WHEN_DESTROYED FLAG_EXTRAS_ALL FLAG_USE_HIGHER_DOOR_TORQUE FLAG_DONT_SPAWN_IN_CARGEN FLAG_DONT_SPAWN_AS_AMBIENT FLAG_IS_TANK FLAG_USE_COVERBOUND_INFO_FOR_COVERGEN FLAG_DONT_TIMESLICE_WHEELS FLAG_INCREASE_LOW_SPEED_TORQUE FLAG_CAN_NAVIGATE_TO_ON_VEHICLE_ENTRY FLAG_RESET_TURRET_SEAT_HEADING FLAG_USE_TURRET_RELATIVE_AIM_CALCULATION FLAG_HAS_RAMMING_SCOOP_MOD FLAG_USE_AIRCRAFT_STYLE_WEAPON_TARGETING FLAG_HAS_NITROUS_MOD FLAG_DISABLE_THROUGH_WINDSCREEN FLAG_HAS_JUMP_MOD FLAG_HAS_SIDE_SHUNT FLAG_HAS_WEAPON_SPIKE_MODS FLAG_HAS_RAMMING_BAR_MOD FLAG_IS_TANK_WITH_FLAME_DAMAGE</flags>
      <type>VEHICLE_TYPE_CAR</type>
      <plateType>VPT_NONE</plateType>
	  <dashboardType>VDT_RACE</dashboardType>
      <vehicleClass>VC_MILITARY</vehicleClass>
      <wheelType>VWT_SPORT</wheelType>
      <trailers />
      <additionalTrailers />
      <drivers />
      <extraIncludes />
      <doorsWithCollisionWhenClosed />
      <driveableDoors />
      <bumpersNeedToCollideWithMap value="false" />
      <needsRopeTexture value="false" />
      <requiredExtras />
      <rewards />
      <cinematicPartCamera />
      <NmBraceOverrideSet>Truck</NmBraceOverrideSet>
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000" />
      <buoyancySphereSizeScale value="1.000000" />
      <pOverrideRagdollThreshold type="CVehicleModelInfo__CVehicleOverrideRagdollThreshold">
        <MinComponent value="22" />
        <MaxComponent value="22" />
        <ThresholdMult value="1.500000" />
      </pOverrideRagdollThreshold>
	  <firstPersonDrivebyData>
        <Item>TANK_SCARAB_FRONT_LEFT</Item>
        <Item>TANK_SCARAB_FRONT_RIGHT</Item>
        <Item>VAN_BODHI_REAR_LEFT</Item>
        <Item>VAN_BODHI_REAR_RIGHT</Item>
      </firstPersonDrivebyData>
    </Item>
    <Item>
      <modelName>issi4</modelName>
      <txdName>issi4</txdName>
      <handlingId>ISSI4</handlingId>
      <gameName>ISSI4</gameName>
      <vehicleMakeName>WEENY</vehicleMakeName>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected />
      <ptfxAssetName>weap_xs_vehicle_weapons</ptfxAssetName>
      <audioNameHash />
      <layout>LAYOUT_STD_ISSI4</layout>
      <coverBoundOffsets>ISSI4_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout />
      <cameraName>DEFAULT_FOLLOW_VEHICLE_CAMERA</cameraName>
      <aimCameraName>DEFAULT_THIRD_PERSON_VEHICLE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>VEHICLE_BONNET_CAMERA_STANDARD_LONG_EXTRA_LOW</bonnetCameraName>
      <povCameraName>REDUCED_NEAR_CLIP_POV_CAMERA</povCameraName>
      <FirstPersonDriveByIKOffset x="0.000000" y="-0.100000" z="-0.070000" />
      <FirstPersonDriveByUnarmedIKOffset x="-0.030000" y="-0.140000" z="-0.010000" />
	  <FirstPersonProjectileDriveByIKOffset x="0.000000" y="-0.050000" z="-0.055000" />
	  <FirstPersonProjectileDriveByPassengerIKOffset x="-0.040000" y="-0.130000" z="-0.055000" />
	  <FirstPersonDriveByRightPassengerIKOffset x="0.050000" y="-0.070000" z="-0.090000" />
	  <FirstPersonDriveByRightPassengerUnarmedIKOffset x="0.000000" y="-0.120000" z="-0.025000" />
	  <FirstPersonMobilePhoneOffset x="0.105000" y="0.170000" z="0.510000" />
      <FirstPersonPassengerMobilePhoneOffset x="0.221000" y="0.160000" z="0.403000" />
      <PovCameraOffset x="-0.020000" y="-0.225000" z="0.625000" />
      <PovCameraVerticalAdjustmentForRollCage value="0.000000" />
      <PovPassengerCameraOffset x="0.000000" y="0.000000" z="0.010000" />
      <vfxInfoName>VFXVEHICLEINFO_CAR_GENERIC_SMALL</vfxInfoName>
      <shouldUseCinematicViewMode value="true" />
      <shouldCameraTransitionOnClimbUpDown value="false" />
      <shouldCameraIgnoreExiting value="false" />
      <AllowPretendOccupants value="true" />
      <AllowJoyriding value="true" />
      <AllowSundayDriving value="true" />
      <AllowBodyColorMapping value="true" />
      <wheelScale value="0.191600" />
      <wheelScaleRear value="0.191600" />
      <dirtLevelMin value="0.000000" />
      <dirtLevelMax value="0.300000" />
      <envEffScaleMin value="1.000000" />
      <envEffScaleMax value="1.000000" />
      <envEffScaleMin2 value="1.000000" />
      <envEffScaleMax2 value="1.000000" />
      <damageMapScale value="0.30000" />
      <damageOffsetScale value="0.30000" />
      <diffuseTint value="0x00FFFFFF" />
      <steerWheelMult value="1.000000" />
      <HDTextureDist value="5.000000" />
      <lodDistances content="float_array">
        10.000000	
        25.000000	
        60.000000	
        120.000000	
        500.000000	
        500.000000
      </lodDistances>
      <minSeatHeight value="0.881" />
      <identicalModelSpawnDistance value="20" />
      <maxNumOfSameColor value="1" />
      <defaultBodyHealth value="1000.000000" />
      <pretendOccupantsScale value="1.000000" />
      <visibleSpawnDistScale value="1.000000" />
      <trackerPathWidth value="2.000000" />
      <weaponForceMult value="1.000000" />
      <frequency value="30" />
      <swankness>SWANKNESS_5</swankness>
      <maxNum value="5" />
      <flags>FLAG_SPORTS FLAG_SPAWN_ON_TRAILER FLAG_RICH_CAR FLAG_NO_BROKEN_DOWN_SCENARIO FLAG_CAN_HAVE_NEONS FLAG_HAS_JUMP_MOD FLAG_HAS_NITROUS_MOD FLAG_HAS_RAMMING_SCOOP_MOD FLAG_USE_AIRCRAFT_STYLE_WEAPON_TARGETING FLAG_HAS_WEAPON_SPIKE_MODS FLAG_HAS_SIDE_SHUNT FLAG_HAS_SUPERCHARGER</flags>
      <type>VEHICLE_TYPE_CAR</type>
      <plateType>VPT_BACK_PLATES</plateType>
      <dashboardType>VDT_RACE</dashboardType>
      <vehicleClass>VC_COMPACTS</vehicleClass>
      <wheelType>VWT_SPORT</wheelType>
      <trailers />
      <additionalTrailers />
      <drivers />
      <extraIncludes />
      <doorsWithCollisionWhenClosed />
      <driveableDoors />
      <bumpersNeedToCollideWithMap value="false" />
      <needsRopeTexture value="false" />
      <requiredExtras />
      <rewards />
      <cinematicPartCamera>
        <Item>WHEEL_FRONT_RIGHT_CAMERA</Item>
        <Item>WHEEL_FRONT_LEFT_CAMERA</Item>
        <Item>WHEEL_REAR_RIGHT_CAMERA</Item>
        <Item>WHEEL_REAR_LEFT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet />
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000" />
      <buoyancySphereSizeScale value="1.000000" />
      <pOverrideRagdollThreshold type="NULL" />
      <firstPersonDrivebyData>
        <Item>STD_ISSI4_FRONT_LEFT</Item>
        <Item>STD_ISSI4_FRONT_RIGHT</Item>
      </firstPersonDrivebyData>
	  <numSeatsOverride value="2" />
    </Item>
	<Item>
      <modelName>issi5</modelName>
      <txdName>issi5</txdName>
      <handlingId>ISSI5</handlingId>
      <gameName>ISSI5</gameName>
      <vehicleMakeName>WEENY</vehicleMakeName>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected />
      <ptfxAssetName>weap_xs_vehicle_weapons</ptfxAssetName>
      <audioNameHash />
      <layout>LAYOUT_STD_ISSI4</layout>
      <coverBoundOffsets>ISSI4_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout />
      <cameraName>DEFAULT_FOLLOW_VEHICLE_CAMERA</cameraName>
      <aimCameraName>DEFAULT_THIRD_PERSON_VEHICLE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>VEHICLE_BONNET_CAMERA_STANDARD_LONG_EXTRA_LOW</bonnetCameraName>
      <povCameraName>REDUCED_NEAR_CLIP_POV_CAMERA</povCameraName>
      <FirstPersonDriveByIKOffset x="0.000000" y="-0.100000" z="-0.070000" />
      <FirstPersonDriveByUnarmedIKOffset x="-0.030000" y="-0.140000" z="-0.010000" />
	  <FirstPersonProjectileDriveByIKOffset x="0.000000" y="-0.050000" z="-0.055000" />
	  <FirstPersonProjectileDriveByPassengerIKOffset x="-0.040000" y="-0.130000" z="-0.055000" />
	  <FirstPersonDriveByRightPassengerIKOffset x="0.050000" y="-0.070000" z="-0.090000" />
	  <FirstPersonDriveByRightPassengerUnarmedIKOffset x="0.000000" y="-0.120000" z="-0.025000" />
	  <FirstPersonMobilePhoneOffset x="0.105000" y="0.170000" z="0.510000" />
      <FirstPersonPassengerMobilePhoneOffset x="0.221000" y="0.160000" z="0.403000" />
      <PovCameraOffset x="-0.020000" y="-0.225000" z="0.625000" />
      <PovCameraVerticalAdjustmentForRollCage value="0.000000" />
      <PovPassengerCameraOffset x="0.000000" y="0.000000" z="0.010000" />
      <vfxInfoName>VFXVEHICLEINFO_CAR_GENERIC_SMALL</vfxInfoName>
      <shouldUseCinematicViewMode value="true" />
      <shouldCameraTransitionOnClimbUpDown value="false" />
      <shouldCameraIgnoreExiting value="false" />
      <AllowPretendOccupants value="true" />
      <AllowJoyriding value="true" />
      <AllowSundayDriving value="true" />
      <AllowBodyColorMapping value="true" />
      <wheelScale value="0.204500" />
      <wheelScaleRear value="0.204500" />
      <dirtLevelMin value="0.000000" />
      <dirtLevelMax value="0.300000" />
      <envEffScaleMin value="1.000000" />
      <envEffScaleMax value="1.000000" />
      <envEffScaleMin2 value="1.000000" />
      <envEffScaleMax2 value="1.000000" />
      <damageMapScale value="0.00000" />
      <damageOffsetScale value="0.10000" />
      <diffuseTint value="0x00FFFFFF" />
      <steerWheelMult value="1.000000" />
      <HDTextureDist value="5.000000" />
      <lodDistances content="float_array">
        10.000000	
        25.000000	
        60.000000	
        120.000000	
        500.000000	
        500.000000
      </lodDistances>
      <minSeatHeight value="0.881" />
      <identicalModelSpawnDistance value="20" />
      <maxNumOfSameColor value="1" />
      <defaultBodyHealth value="1000.000000" />
      <pretendOccupantsScale value="1.000000" />
      <visibleSpawnDistScale value="1.000000" />
      <trackerPathWidth value="2.000000" />
      <weaponForceMult value="1.000000" />
      <frequency value="30" />
      <swankness>SWANKNESS_5</swankness>
      <maxNum value="5" />
      <flags>FLAG_SPORTS FLAG_SPAWN_ON_TRAILER FLAG_RICH_CAR FLAG_NO_BROKEN_DOWN_SCENARIO FLAG_CAN_HAVE_NEONS FLAG_HAS_JUMP_MOD FLAG_HAS_NITROUS_MOD FLAG_HAS_RAMMING_SCOOP_MOD FLAG_USE_AIRCRAFT_STYLE_WEAPON_TARGETING FLAG_HAS_WEAPON_SPIKE_MODS FLAG_HAS_SIDE_SHUNT FLAG_HAS_SUPERCHARGER FLAG_DISABLE_DEFORMATION</flags>
      <type>VEHICLE_TYPE_CAR</type>
      <plateType>VPT_BACK_PLATES</plateType>
      <dashboardType>VDT_RACE</dashboardType>
      <vehicleClass>VC_COMPACTS</vehicleClass>
      <wheelType>VWT_SPORT</wheelType>
      <trailers />
      <additionalTrailers />
      <drivers />
      <extraIncludes />
      <doorsWithCollisionWhenClosed />
      <driveableDoors />
      <bumpersNeedToCollideWithMap value="false" />
      <needsRopeTexture value="false" />
      <requiredExtras />
      <rewards />
      <cinematicPartCamera>
        <Item>WHEEL_FRONT_RIGHT_CAMERA</Item>
        <Item>WHEEL_FRONT_LEFT_CAMERA</Item>
        <Item>WHEEL_REAR_RIGHT_CAMERA</Item>
        <Item>WHEEL_REAR_LEFT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet />
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000" />
      <buoyancySphereSizeScale value="1.000000" />
      <pOverrideRagdollThreshold type="NULL" />
      <firstPersonDrivebyData>
        <Item>STD_ISSI4_FRONT_LEFT</Item>
        <Item>STD_ISSI4_FRONT_RIGHT</Item>
      </firstPersonDrivebyData>
	  <numSeatsOverride value="2" />
    </Item>
	<Item>
      <modelName>clique</modelName>
      <txdName>clique</txdName>
      <handlingId>CLIQUE</handlingId>
      <gameName>CLIQUE</gameName>
      <vehicleMakeName>VAPID</vehicleMakeName>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected />
      <ptfxAssetName>null</ptfxAssetName>
      <audioNameHash />
      <layout>LAYOUT_STD_CLIQUE</layout>
      <coverBoundOffsets>CLIQUE_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout />
      <cameraName>FOLLOW_CHEETAH_CAMERA</cameraName>
      <aimCameraName>DEFAULT_THIRD_PERSON_VEHICLE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>VEHICLE_BONNET_CAMERA_STANDARD_LONG_EXTRA_LOW</bonnetCameraName>
      <povCameraName>REDUCED_NEAR_CLIP_POV_CAMERA</povCameraName>
      <FirstPersonDriveByIKOffset x="-0.010000" y="-0.070000" z="-0.050000" />
      <FirstPersonDriveByUnarmedIKOffset x="-0.025000" y="-0.050000" z="0.000000" />
	  <FirstPersonProjectileDriveByIKOffset x="0.090000" y="-0.170000" z="-0.070000" />
	  <FirstPersonProjectileDriveByPassengerIKOffset x="-0.080000" y="-0.130000" z="-0.060000" />
	  <FirstPersonDriveByRightPassengerIKOffset x="0.020000" y="-0.090000" z="-0.050000" />
	  <FirstPersonDriveByRightPassengerUnarmedIKOffset x="-0.025000" y="-0.050000" z="0.000000" />
	  <FirstPersonMobilePhoneOffset x="0.136000" y="0.211000" z="0.510000" />
      <FirstPersonPassengerMobilePhoneOffset x="0.244000" y="0.169000" z="0.400000" />
      <PovCameraOffset x="0.000000" y="-0.150000" z="0.650000" />
      <PovCameraVerticalAdjustmentForRollCage value="0.000000" />
      <PovPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000" />
      <PovRearPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000" />
      <vfxInfoName>VFXVEHICLEINFO_CAR_GENERIC</vfxInfoName>
      <shouldUseCinematicViewMode value="true" />
      <shouldCameraTransitionOnClimbUpDown value="false" />
      <shouldCameraIgnoreExiting value="false" />
      <AllowPretendOccupants value="true" />
      <AllowJoyriding value="true" />
      <AllowSundayDriving value="true" />
      <AllowBodyColorMapping value="true" />
      <wheelScale value="0.216500" />
      <wheelScaleRear value="0.216500" />
      <dirtLevelMin value="0.000000" />
      <dirtLevelMax value="0.450000" />
      <envEffScaleMin value="0.000000" />
      <envEffScaleMax value="1.000000" />
      <envEffScaleMin2 value="0.000000" />
      <envEffScaleMax2 value="1.000000" />
      <damageMapScale value="0.300000" />
      <damageOffsetScale value="0.300000" />
      <diffuseTint value="0x00FFFFFF" />
      <steerWheelMult value="0.700000" />
      <HDTextureDist value="5.000000" />
      <lodDistances content="float_array">
        10.000000
        25.000000
        60.000000
        120.000000
        500.000000
        500.000000
      </lodDistances>
      <minSeatHeight value="0.844" />
      <identicalModelSpawnDistance value="20" />
      <maxNumOfSameColor value="1" />
      <defaultBodyHealth value="1000.000000" />
      <pretendOccupantsScale value="1.000000" />
      <visibleSpawnDistScale value="1.000000" />
      <trackerPathWidth value="2.000000" />
      <weaponForceMult value="1.000000" />
      <frequency value="30" />
      <swankness>SWANKNESS_4</swankness>
      <maxNum value="10" />
      <flags>FLAG_EXTRAS_STRONG FLAG_AVERAGE_CAR FLAG_HAS_INTERIOR_EXTRAS FLAG_CAN_HAVE_NEONS FLAG_INCREASE_CAMBER_WITH_SUSPENSION_MOD</flags>
      <type>VEHICLE_TYPE_CAR</type>
      <plateType>VPT_BACK_PLATES</plateType>
	  <dashboardType>VDT_DUKES</dashboardType>
      <vehicleClass>VC_MUSCLE</vehicleClass>
      <wheelType>VWT_TUNER</wheelType>
      <trailers />
      <additionalTrailers />
      <drivers />
      <extraIncludes />
      <doorsWithCollisionWhenClosed />
      <driveableDoors />
      <bumpersNeedToCollideWithMap value="false" />
      <needsRopeTexture value="false" />
      <requiredExtras />
      <rewards />
      <cinematicPartCamera>
        <Item>WHEEL_FRONT_RIGHT_CAMERA</Item>
        <Item>WHEEL_FRONT_LEFT_CAMERA</Item>
        <Item>WHEEL_REAR_RIGHT_CAMERA</Item>
        <Item>WHEEL_REAR_LEFT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet />
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000" />
      <buoyancySphereSizeScale value="1.000000" />
      <pOverrideRagdollThreshold type="NULL" />
	  <firstPersonDrivebyData>
        <Item>STD_CLIQUE_FRONT_LEFT</Item>
        <Item>STD_CLIQUE_FRONT_RIGHT</Item>
      </firstPersonDrivebyData>
	  <numSeatsOverride value="2" />
    </Item> 
    <Item>
      <modelName>deveste</modelName>
      <txdName>deveste</txdName>
      <handlingId>DEVESTE</handlingId>
      <gameName>DEVESTE</gameName>
      <vehicleMakeName>PRINCIPL</vehicleMakeName>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected />
      <ptfxAssetName>null</ptfxAssetName>
      <audioNameHash />
      <layout>LAYOUT_LOW_INFERNUS</layout>
      <coverBoundOffsets>DEVESTE_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout />
      <cameraName>FOLLOW_CHEETAH_CAMERA</cameraName>
      <aimCameraName>DEFAULT_THIRD_PERSON_VEHICLE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>VEHICLE_BONNET_CAMERA_LOW</bonnetCameraName>
      <povCameraName>DEFAULT_POV_CAMERA</povCameraName>
      <FirstPersonDriveByIKOffset x="0.060000" y="-0.050000" z="-0.060000" />
      <FirstPersonDriveByUnarmedIKOffset x="0.000000" y="-0.100000" z="0.000000" />
      <FirstPersonProjectileDriveByIKOffset x="-0.060000" y="-0.120000" z="-0.030000" />
      <FirstPersonProjectileDriveByPassengerIKOffset x="0.030000" y="-0.120000" z="-0.030000" />
      <FirstPersonDriveByRightPassengerIKOffset x="-0.050000" y="-0.060000" z="-0.045000" />
      <FirstPersonDriveByRightPassengerUnarmedIKOffset x="0.000000" y="-0.100000" z="0.000000" />
      <FirstPersonMobilePhoneOffset x="0.120000" y="0.155000" z="0.513000" />
      <FirstPersonPassengerMobilePhoneOffset x="0.220000" y="0.110000" z="0.395000" />
      <PovCameraOffset x="0.000000" y="-0.275000" z="0.635000" />
      <PovCameraVerticalAdjustmentForRollCage value="0.000000" />
      <PovPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000" />
      <vfxInfoName>VFXVEHICLEINFO_CAR_GENERIC</vfxInfoName>
      <shouldUseCinematicViewMode value="true" />
      <shouldCameraTransitionOnClimbUpDown value="false" />
      <shouldCameraIgnoreExiting value="false" />
      <AllowPretendOccupants value="true" />
      <AllowJoyriding value="true" />
      <AllowSundayDriving value="true" />
      <AllowBodyColorMapping value="true" />
      <wheelScale value="0.274500" />
      <wheelScaleRear value="0.274500" />
      <dirtLevelMin value="0.000000" />
      <dirtLevelMax value="1.000000" />
      <envEffScaleMin value="0.800000" />
      <envEffScaleMax value="1.000000" />
      <envEffScaleMin2 value="0.000000" />
      <envEffScaleMax2 value="1.00000" />
      <damageMapScale value="0.400000" />
      <damageOffsetScale value="0.400000" />
      <diffuseTint value="0x00FFFFFF" />
      <steerWheelMult value="1.000000" />
      <HDTextureDist value="5.000000" />
      <lodDistances content="float_array">
        15.000000
        30.000000
        60.000000	
        120.000000	
        500.000000	
        500.000000
      </lodDistances>
      <minSeatHeight value="0.681" />
      <identicalModelSpawnDistance value="20" />
      <maxNumOfSameColor value="10" />
      <defaultBodyHealth value="1000.000000" />
      <pretendOccupantsScale value="1.000000" />
      <visibleSpawnDistScale value="1.000000" />
      <trackerPathWidth value="2.000000" />
      <weaponForceMult value="1.000000" />
      <frequency value="50" />
      <swankness>SWANKNESS_5</swankness>
      <maxNum value="5" />
      <flags>FLAG_SPORTS FLAG_SPAWN_ON_TRAILER FLAG_RICH_CAR FLAG_NO_BROKEN_DOWN_SCENARIO FLAG_RECESSED_TAILLIGHT_CORONAS FLAG_HAS_GULL_WING_DOORS FLAG_EXTRAS_REQUIRE FLAG_EXTRAS_STRONG</flags>
      <type>VEHICLE_TYPE_CAR</type>
      <plateType>VPT_NONE</plateType>
      <dashboardType>VDT_BANSHEE</dashboardType>
      <vehicleClass>VC_SUPER</vehicleClass>
      <wheelType>VWT_HIEND</wheelType>
      <trailers />
      <additionalTrailers />
      <drivers />
      <extraIncludes />
      <doorsWithCollisionWhenClosed>
        <Item>VEH_EXT_BONNET</Item>
      </doorsWithCollisionWhenClosed>
      <driveableDoors />
      <bumpersNeedToCollideWithMap value="false" />
      <needsRopeTexture value="false" />
      <requiredExtras />
      <rewards />
      <cinematicPartCamera>
        <Item>WHEEL_FRONT_RIGHT_CAMERA</Item>
        <Item>WHEEL_FRONT_LEFT_CAMERA</Item>
        <Item>WHEEL_REAR_RIGHT_CAMERA</Item>
        <Item>WHEEL_REAR_LEFT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet />
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000" />
      <buoyancySphereSizeScale value="1.000000" />
      <pOverrideRagdollThreshold type="NULL" />
      <firstPersonDrivebyData>
        <Item>LOW_DEVESTE_FRONT_LEFT</Item>
        <Item>LOW_DEVESTE_FRONT_RIGHT</Item>
      </firstPersonDrivebyData>
    </Item>
    <Item>
      <modelName>vamos</modelName>
      <txdName>vamos</txdName>
      <handlingId>VAMOS</handlingId>
      <gameName>VAMOS</gameName>
      <vehicleMakeName>DECLASSE</vehicleMakeName>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected />
      <ptfxAssetName>null</ptfxAssetName>
      <audioNameHash />
      <layout>LAYOUT_LOW</layout>
      <coverBoundOffsets>VAMOS_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout />
      <cameraName>FOLLOW_CHEETAH_CAMERA</cameraName>
      <aimCameraName>DEFAULT_THIRD_PERSON_VEHICLE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>DEFAULT_VEHICLE_BONNET_CAMERA</bonnetCameraName>
      <povCameraName>REDUCED_NEAR_CLIP_POV_CAMERA</povCameraName>
      <FirstPersonDriveByIKOffset x="0.030000" y="-0.100000" z="-0.060000" />
      <FirstPersonDriveByUnarmedIKOffset x="-0.010000" y="-0.100000" z="-0.010000" />
	  <FirstPersonProjectileDriveByIKOffset x="0.060000" y="-0.125000" z="-0.040000" />
	  <FirstPersonProjectileDriveByPassengerIKOffset x="-0.060000" y="-0.125000" z="-0.040000" />
	  <FirstPersonDriveByRightPassengerIKOffset x="-0.030000" y="-0.100000" z="-0.060000" />
	  <FirstPersonDriveByRightPassengerUnarmedIKOffset x="-0.010000" y="-0.100000" z="-0.010000" />
	  <FirstPersonMobilePhoneOffset x="0.123000" y="0.120000" z="0.525000" />
      <FirstPersonPassengerMobilePhoneOffset x="0.181000" y="0.100000" z="0.450000" />
      <PovCameraOffset x="0.000000" y="-0.280000" z="0.650000" />
      <PovCameraVerticalAdjustmentForRollCage value="0.000000" />
      <PovPassengerCameraOffset x="0.000000" y="0.000000" z="0.050000" />
      <vfxInfoName>VFXVEHICLEINFO_CAR_GENERIC</vfxInfoName>
      <shouldUseCinematicViewMode value="true" />
      <shouldCameraTransitionOnClimbUpDown value="false" />
      <shouldCameraIgnoreExiting value="false" />
      <AllowPretendOccupants value="true" />
      <AllowJoyriding value="true" />
      <AllowSundayDriving value="true" />
      <AllowBodyColorMapping value="true" />
      <wheelScale value="0.197500" />
      <wheelScaleRear value="0.197500" />
      <dirtLevelMin value="0.000000" />
      <dirtLevelMax value="0.450000" />
      <envEffScaleMin value="0.000000" />
      <envEffScaleMax value="1.000000" />
      <envEffScaleMin2 value="0.000000" />
      <envEffScaleMax2 value="1.000000" />
      <damageMapScale value="0.40000" />
      <damageOffsetScale value="0.40000" />
      <diffuseTint value="0x00FFFFFF" />
      <steerWheelMult value="0.700000" />
      <HDTextureDist value="5.000000" />
      <lodDistances content="float_array">
        15.000000
        30.000000
        60.000000	
        120.000000
        500.000000
        500.000000
      </lodDistances>
      <minSeatHeight value="0.844" />
      <identicalModelSpawnDistance value="20" />
      <maxNumOfSameColor value="1" />
      <defaultBodyHealth value="1000.000000" />
      <pretendOccupantsScale value="1.000000" />
      <visibleSpawnDistScale value="1.000000" />
      <trackerPathWidth value="2.000000" />
      <weaponForceMult value="1.000000" />
      <frequency value="30" />
      <swankness>SWANKNESS_4</swankness>
      <maxNum value="10" />
      <flags>FLAG_EXTRAS_STRONG FLAG_AVERAGE_CAR FLAG_HAS_INTERIOR_EXTRAS FLAG_CAN_HAVE_NEONS FLAG_HAS_SUPERCHARGER</flags>
      <type>VEHICLE_TYPE_CAR</type>
      <plateType>VPT_FRONT_AND_BACK_PLATES</plateType>
      <dashboardType>VDT_DUKES</dashboardType>
      <vehicleClass>VC_MUSCLE</vehicleClass>
      <wheelType>VWT_MUSCLE</wheelType>
      <trailers />
      <additionalTrailers />
      <drivers />
      <extraIncludes />
      <doorsWithCollisionWhenClosed />
      <driveableDoors />
      <bumpersNeedToCollideWithMap value="false" />
      <needsRopeTexture value="false" />
      <requiredExtras />
      <rewards />
      <cinematicPartCamera>
        <Item>WHEEL_FRONT_RIGHT_CAMERA</Item>
        <Item>WHEEL_FRONT_LEFT_CAMERA</Item>
        <Item>WHEEL_REAR_RIGHT_CAMERA</Item>
        <Item>WHEEL_REAR_LEFT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet />
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000" />
      <buoyancySphereSizeScale value="1.000000" />
      <pOverrideRagdollThreshold type="NULL" />
	  <firstPersonDrivebyData>
        <Item>STD_VAMOS_FRONT_LEFT</Item>
        <Item>STD_VAMOS_FRONT_RIGHT</Item>
      </firstPersonDrivebyData>
	  <numSeatsOverride value="2" />
    </Item>
    <Item>
      <modelName>imperator</modelName>
      <txdName>imperator</txdName>
      <handlingId>IMPERATOR</handlingId>
      <gameName>IMPERATOR</gameName>
      <vehicleMakeName>VAPID</vehicleMakeName>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected />
      <ptfxAssetName>weap_xs_vehicle_weapons</ptfxAssetName>
      <audioNameHash />
      <layout>LAYOUT_LOW</layout>
      <coverBoundOffsets>IMPERATOR_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout />
      <cameraName>IMPERATOR_FOLLOW_VEHICLE_CAMERA</cameraName>
      <aimCameraName>MID_BOX_VEHICLE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>IMPERATOR_BONNET_CAMERA</bonnetCameraName>
      <povCameraName>REDUCED_NEAR_CLIP_POV_CAMERA_NO_REVERSE</povCameraName>
      <FirstPersonDriveByIKOffset x="0.030000" y="-0.100000" z="-0.050000" />
      <FirstPersonDriveByUnarmedIKOffset x="-0.010000" y="-0.100000" z="-0.010000" />
	  <FirstPersonProjectileDriveByIKOffset x="0.060000" y="-0.180000" z="-0.050000" />
	  <FirstPersonProjectileDriveByPassengerIKOffset x="-0.030000" y="-0.160000" z="-0.050000" />
	  <FirstPersonDriveByRightPassengerIKOffset x="-0.030000" y="-0.100000" z="-0.050000" />
	  <FirstPersonDriveByRightPassengerUnarmedIKOffset x="-0.010000" y="-0.100000" z="-0.010000" />
	  <FirstPersonMobilePhoneOffset x="0.123000" y="0.130000" z="0.525000" />
      <FirstPersonPassengerMobilePhoneOffset x="0.211000" y="0.110000" z="0.410000" />
      <PovCameraOffset x="0.000000" y="-0.250000" z="0.650000" />
      <PovCameraVerticalAdjustmentForRollCage value="0.000000" />
      <PovPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000" />
      <vfxInfoName>VFXVEHICLEINFO_CAR_GENERIC</vfxInfoName>
      <shouldUseCinematicViewMode value="true" />
      <shouldCameraTransitionOnClimbUpDown value="false" />
      <shouldCameraIgnoreExiting value="false" />
      <AllowPretendOccupants value="true" />
      <AllowJoyriding value="true" />
      <AllowSundayDriving value="true" />
      <AllowBodyColorMapping value="true" />
      <wheelScale value="0.204000" />
      <wheelScaleRear value="0.204000" />
      <dirtLevelMin value="0.000000" />
      <dirtLevelMax value="0.450000" />
      <envEffScaleMin value="1.000000" />
      <envEffScaleMax value="1.000000" />
      <envEffScaleMin2 value="1.000000" />
      <envEffScaleMax2 value="1.000000" />
      <damageMapScale value="0.30000" />
      <damageOffsetScale value="0.30000" />
      <diffuseTint value="0x00FFFFFF" />
      <steerWheelMult value="0.700000" />
      <HDTextureDist value="5.000000" />
      <lodDistances content="float_array">
        15.000000
        30.000000
        60.000000	
        120.000000
        500.000000
        500.000000
      </lodDistances>
      <minSeatHeight value="0.844" />
      <identicalModelSpawnDistance value="20" />
      <maxNumOfSameColor value="1" />
      <defaultBodyHealth value="1000.000000" />
      <pretendOccupantsScale value="1.000000" />
      <visibleSpawnDistScale value="1.000000" />
      <trackerPathWidth value="2.000000" />
      <weaponForceMult value="1.000000" />
      <frequency value="30" />
      <swankness>SWANKNESS_4</swankness>
      <maxNum value="10" />
      <flags>FLAG_EXTRAS_STRONG FLAG_AVERAGE_CAR FLAG_HAS_INTERIOR_EXTRAS FLAG_CAN_HAVE_NEONS FLAG_HAS_JUMP_MOD FLAG_HAS_NITROUS_MOD FLAG_HAS_RAMMING_SCOOP_MOD FLAG_HAS_WEAPON_BLADE_MODS FLAG_USE_AIRCRAFT_STYLE_WEAPON_TARGETING FLAG_HAS_WEAPON_SPIKE_MODS FLAG_HAS_SIDE_SHUNT FLAG_HAS_SUPERCHARGER</flags>
      <type>VEHICLE_TYPE_CAR</type>
      <plateType>VPT_BACK_PLATES</plateType>
      <dashboardType>VDT_DUKES</dashboardType>
      <vehicleClass>VC_MUSCLE</vehicleClass>
      <wheelType>VWT_MUSCLE</wheelType>
      <trailers />
      <additionalTrailers />
      <drivers />
      <extraIncludes />
      <doorsWithCollisionWhenClosed />
      <driveableDoors />
      <bumpersNeedToCollideWithMap value="false" />
      <needsRopeTexture value="false" />
      <requiredExtras />
      <rewards />
      <cinematicPartCamera>
        <Item>WHEEL_FRONT_RIGHT_CAMERA</Item>
        <Item>WHEEL_FRONT_LEFT_CAMERA</Item>
        <Item>WHEEL_REAR_RIGHT_CAMERA</Item>
        <Item>WHEEL_REAR_LEFT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet />
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000" />
      <buoyancySphereSizeScale value="1.000000" />
      <pOverrideRagdollThreshold type="NULL" />
	  <firstPersonDrivebyData>
        <Item>STD_IMPERATOR_FRONT_LEFT</Item>
        <Item>STD_IMPERATOR_FRONT_RIGHT</Item>
      </firstPersonDrivebyData>
	  <numSeatsOverride value="2" />
    </Item>
    <Item>
      <modelName>imperator2</modelName>
      <txdName>imperator2</txdName>
      <handlingId>IMPERATOR2</handlingId>
      <gameName>IMPERATOR2</gameName>
      <vehicleMakeName>VAPID</vehicleMakeName>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected />
      <ptfxAssetName>weap_xs_vehicle_weapons</ptfxAssetName>
      <audioNameHash />
      <layout>LAYOUT_LOW</layout>
      <coverBoundOffsets>IMPERATOR_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout />
      <cameraName>IMPERATOR_FOLLOW_VEHICLE_CAMERA</cameraName>
      <aimCameraName>MID_BOX_VEHICLE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>IMPERATOR_BONNET_CAMERA</bonnetCameraName>
      <povCameraName>REDUCED_NEAR_CLIP_POV_CAMERA_NO_REVERSE</povCameraName>
      <FirstPersonDriveByIKOffset x="0.030000" y="-0.100000" z="-0.050000" />
      <FirstPersonDriveByUnarmedIKOffset x="-0.010000" y="-0.100000" z="-0.010000" />
	  <FirstPersonProjectileDriveByIKOffset x="0.060000" y="-0.180000" z="-0.050000" />
	  <FirstPersonProjectileDriveByPassengerIKOffset x="-0.030000" y="-0.160000" z="-0.050000" />
	  <FirstPersonDriveByRightPassengerIKOffset x="-0.030000" y="-0.100000" z="-0.050000" />
	  <FirstPersonDriveByRightPassengerUnarmedIKOffset x="-0.010000" y="-0.100000" z="-0.010000" />
	  <FirstPersonMobilePhoneOffset x="0.123000" y="0.130000" z="0.525000" />
      <FirstPersonPassengerMobilePhoneOffset x="0.211000" y="0.110000" z="0.410000" />
      <PovCameraOffset x="0.000000" y="-0.250000" z="0.650000" />
      <PovCameraVerticalAdjustmentForRollCage value="0.000000" />
      <PovPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000" />
      <vfxInfoName>VFXVEHICLEINFO_CAR_GENERIC</vfxInfoName>
      <shouldUseCinematicViewMode value="true" />
      <shouldCameraTransitionOnClimbUpDown value="false" />
      <shouldCameraIgnoreExiting value="false" />
      <AllowPretendOccupants value="true" />
      <AllowJoyriding value="true" />
      <AllowSundayDriving value="true" />
      <AllowBodyColorMapping value="true" />
      <wheelScale value="0.204000" />
      <wheelScaleRear value="0.204000" />
      <dirtLevelMin value="0.000000" />
      <dirtLevelMax value="0.450000" />
      <envEffScaleMin value="1.000000" />
      <envEffScaleMax value="1.000000" />
      <envEffScaleMin2 value="1.000000" />
      <envEffScaleMax2 value="1.000000" />
      <damageMapScale value="0.00000" />
      <damageOffsetScale value="0.10000" />
      <diffuseTint value="0x00FFFFFF" />
      <steerWheelMult value="0.700000" />
      <HDTextureDist value="5.000000" />
      <lodDistances content="float_array">
        15.000000
        30.000000
        60.000000	
        120.000000
        500.000000
        500.000000
      </lodDistances>
      <minSeatHeight value="0.844" />
      <identicalModelSpawnDistance value="20" />
      <maxNumOfSameColor value="1" />
      <defaultBodyHealth value="1000.000000" />
      <pretendOccupantsScale value="1.000000" />
      <visibleSpawnDistScale value="1.000000" />
      <trackerPathWidth value="2.000000" />
      <weaponForceMult value="1.000000" />
      <frequency value="30" />
      <swankness>SWANKNESS_4</swankness>
      <maxNum value="10" />
      <flags>FLAG_EXTRAS_STRONG FLAG_AVERAGE_CAR FLAG_HAS_INTERIOR_EXTRAS FLAG_CAN_HAVE_NEONS FLAG_HAS_JUMP_MOD FLAG_HAS_NITROUS_MOD FLAG_HAS_RAMMING_SCOOP_MOD FLAG_HAS_WEAPON_BLADE_MODS FLAG_USE_AIRCRAFT_STYLE_WEAPON_TARGETING FLAG_HAS_WEAPON_SPIKE_MODS FLAG_HAS_SIDE_SHUNT FLAG_HAS_SUPERCHARGER FLAG_DISABLE_DEFORMATION</flags>
      <type>VEHICLE_TYPE_CAR</type>
      <plateType>VPT_BACK_PLATES</plateType>
      <dashboardType>VDT_DUKES</dashboardType>
      <vehicleClass>VC_MUSCLE</vehicleClass>
      <wheelType>VWT_MUSCLE</wheelType>
      <trailers />
      <additionalTrailers />
      <drivers />
      <extraIncludes />
      <doorsWithCollisionWhenClosed />
      <driveableDoors />
      <bumpersNeedToCollideWithMap value="false" />
      <needsRopeTexture value="false" />
      <requiredExtras />
      <rewards />
      <cinematicPartCamera>
        <Item>WHEEL_FRONT_RIGHT_CAMERA</Item>
        <Item>WHEEL_FRONT_LEFT_CAMERA</Item>
        <Item>WHEEL_REAR_RIGHT_CAMERA</Item>
        <Item>WHEEL_REAR_LEFT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet />
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000" />
      <buoyancySphereSizeScale value="1.000000" />
      <pOverrideRagdollThreshold type="NULL" />
	  <firstPersonDrivebyData>
        <Item>STD_IMPERATOR_FRONT_LEFT</Item>
        <Item>STD_IMPERATOR_FRONT_RIGHT</Item>
      </firstPersonDrivebyData>
	  <numSeatsOverride value="2" />
    </Item>
    <Item>
      <modelName>imperator3</modelName>
      <txdName>imperator3</txdName>
      <handlingId>IMPERATOR</handlingId>
      <gameName>IMPERATOR3</gameName>
      <vehicleMakeName>VAPID</vehicleMakeName>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected />
      <ptfxAssetName>weap_xs_vehicle_weapons</ptfxAssetName>
      <audioNameHash />
      <layout>LAYOUT_LOW</layout>
      <coverBoundOffsets>IMPERATOR_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout />
      <cameraName>IMPERATOR_FOLLOW_VEHICLE_CAMERA</cameraName>
      <aimCameraName>MID_BOX_VEHICLE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>IMPERATOR_BONNET_CAMERA</bonnetCameraName>
      <povCameraName>REDUCED_NEAR_CLIP_POV_CAMERA_NO_REVERSE</povCameraName>
      <FirstPersonDriveByIKOffset x="0.030000" y="-0.100000" z="-0.050000" />
      <FirstPersonDriveByUnarmedIKOffset x="-0.010000" y="-0.100000" z="-0.010000" />
	  <FirstPersonProjectileDriveByIKOffset x="0.060000" y="-0.180000" z="-0.050000" />
	  <FirstPersonProjectileDriveByPassengerIKOffset x="-0.030000" y="-0.160000" z="-0.050000" />
	  <FirstPersonDriveByRightPassengerIKOffset x="-0.030000" y="-0.100000" z="-0.050000" />
	  <FirstPersonDriveByRightPassengerUnarmedIKOffset x="-0.010000" y="-0.100000" z="-0.010000" />
	  <FirstPersonMobilePhoneOffset x="0.123000" y="0.130000" z="0.525000" />
      <FirstPersonPassengerMobilePhoneOffset x="0.211000" y="0.110000" z="0.410000" />
      <PovCameraOffset x="0.000000" y="-0.250000" z="0.650000" />
      <PovCameraVerticalAdjustmentForRollCage value="0.000000" />
      <PovPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000" />
      <vfxInfoName>VFXVEHICLEINFO_CAR_GENERIC</vfxInfoName>
      <shouldUseCinematicViewMode value="true" />
      <shouldCameraTransitionOnClimbUpDown value="false" />
      <shouldCameraIgnoreExiting value="false" />
      <AllowPretendOccupants value="true" />
      <AllowJoyriding value="true" />
      <AllowSundayDriving value="true" />
      <AllowBodyColorMapping value="true" />
      <wheelScale value="0.204000" />
      <wheelScaleRear value="0.204000" />
      <dirtLevelMin value="0.000000" />
      <dirtLevelMax value="0.450000" />
      <envEffScaleMin value="1.000000" />
      <envEffScaleMax value="1.000000" />
      <envEffScaleMin2 value="1.000000" />
      <envEffScaleMax2 value="1.000000" />
      <damageMapScale value="0.30000" />
      <damageOffsetScale value="0.30000" />
      <diffuseTint value="0x00FFFFFF" />
      <steerWheelMult value="0.700000" />
      <HDTextureDist value="5.000000" />
      <lodDistances content="float_array">
        15.000000
        30.000000
        60.000000	
        120.000000
        500.000000
        500.000000
      </lodDistances>
      <minSeatHeight value="0.844" />
      <identicalModelSpawnDistance value="20" />
      <maxNumOfSameColor value="1" />
      <defaultBodyHealth value="1000.000000" />
      <pretendOccupantsScale value="1.000000" />
      <visibleSpawnDistScale value="1.000000" />
      <trackerPathWidth value="2.000000" />
      <weaponForceMult value="1.000000" />
      <frequency value="30" />
      <swankness>SWANKNESS_4</swankness>
      <maxNum value="10" />
      <flags>FLAG_EXTRAS_STRONG FLAG_AVERAGE_CAR FLAG_HAS_INTERIOR_EXTRAS FLAG_CAN_HAVE_NEONS FLAG_HAS_JUMP_MOD FLAG_HAS_NITROUS_MOD FLAG_HAS_RAMMING_SCOOP_MOD FLAG_HAS_WEAPON_BLADE_MODS FLAG_USE_AIRCRAFT_STYLE_WEAPON_TARGETING FLAG_HAS_WEAPON_SPIKE_MODS FLAG_HAS_SIDE_SHUNT FLAG_HAS_SUPERCHARGER</flags>
      <type>VEHICLE_TYPE_CAR</type>
      <plateType>VPT_BACK_PLATES</plateType>
      <dashboardType>VDT_DUKES</dashboardType>
      <vehicleClass>VC_MUSCLE</vehicleClass>
      <wheelType>VWT_MUSCLE</wheelType>
      <trailers />
      <additionalTrailers />
      <drivers />
      <extraIncludes />
      <doorsWithCollisionWhenClosed />
      <driveableDoors />
      <bumpersNeedToCollideWithMap value="false" />
      <needsRopeTexture value="false" />
      <requiredExtras />
      <rewards />
      <cinematicPartCamera>
        <Item>WHEEL_FRONT_RIGHT_CAMERA</Item>
        <Item>WHEEL_FRONT_LEFT_CAMERA</Item>
        <Item>WHEEL_REAR_RIGHT_CAMERA</Item>
        <Item>WHEEL_REAR_LEFT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet />
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000" />
      <buoyancySphereSizeScale value="1.000000" />
      <pOverrideRagdollThreshold type="NULL" />
	  <firstPersonDrivebyData>
        <Item>STD_IMPERATOR_FRONT_LEFT</Item>
        <Item>STD_IMPERATOR_FRONT_RIGHT</Item>
      </firstPersonDrivebyData>
	  <numSeatsOverride value="2" />
    </Item>
	<Item>
      <modelName>toros</modelName>
      <txdName>toros</txdName>
      <handlingId>TOROS</handlingId>
      <gameName>TOROS</gameName>
      <vehicleMakeName>PEGASSI</vehicleMakeName>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected />
      <ptfxAssetName>null</ptfxAssetName>
      <audioNameHash />
      <layout>LAYOUT_RANGER_TOROS</layout>
      <coverBoundOffsets>TOROS_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout />
      <cameraName>DEFAULT_FOLLOW_VEHICLE_CAMERA</cameraName>
      <aimCameraName>DEFAULT_THIRD_PERSON_VEHICLE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>VEHICLE_BONNET_CAMERA_SPEEDO4</bonnetCameraName>
      <povCameraName>DEFAULT_POV_CAMERA</povCameraName>
      <FirstPersonDriveByIKOffset x="0.040000" y="-0.060000" z="-0.050000" />
      <FirstPersonDriveByUnarmedIKOffset x="0.000000" y="-0.060000" z="-0.010000" />
	  <FirstPersonProjectileDriveByIKOffset x="0.035000" y="-0.120000" z="-0.040000" />
	  <FirstPersonProjectileDriveByPassengerIKOffset x="-0.035000" y="-0.120000" z="-0.040000" />
	  <FirstPersonProjectileDriveByRearLeftIKOffset x="-0.060000" y="-0.040000" z="-0.060000" />
	  <FirstPersonProjectileDriveByRearRightIKOffset x="-0.060000" y="-0.040000" z="-0.100000" />
	  <FirstPersonDriveByLeftPassengerIKOffset x="0.040000" y="0.000000" z="-0.040000" />
	  <FirstPersonDriveByRightPassengerIKOffset x="-0.040000" y="-0.060000" z="-0.050000" />
	  <FirstPersonDriveByRightRearPassengerIKOffset x="-0.050000" y="0.010000" z="-0.030000" />
	  <FirstPersonDriveByRightPassengerUnarmedIKOffset x="0.000000" y="-0.060000" z="-0.010000" />
      <FirstPersonDriveByLeftPassengerUnarmedIKOffset x="0.070000" y="-0.040000" z="-0.030000" />
	  <FirstPersonMobilePhoneOffset x="0.140000" y="0.168000" z="0.535000" />
      <FirstPersonPassengerMobilePhoneOffset x="0.250000" y="0.173000" z="0.455000" />
      <FirstPersonMobilePhoneSeatIKOffset>
		<Item>
			<Offset x="0.210000" y="0.230000" z="0.440000" />
			<SeatIndex value="2" />
		</Item>
		<Item>
			<Offset x="0.190000" y="0.230000" z="0.440000" />
			<SeatIndex value="3" />
		</Item>
      </FirstPersonMobilePhoneSeatIKOffset>
      <PovCameraOffset x="0.000000" y="-0.215000" z="0.655000" />
      <PovCameraVerticalAdjustmentForRollCage value="0.000000" />
      <PovPassengerCameraOffset x="0.000000" y="0.020000" z="0.045000" />
	  <PovRearPassengerCameraOffset x="0.015000" y="0.100000" z="0.020000" />
      <vfxInfoName>VFXVEHICLEINFO_CAR_OFFROAD</vfxInfoName>
      <shouldUseCinematicViewMode value="true" />
      <shouldCameraTransitionOnClimbUpDown value="false" />
      <shouldCameraIgnoreExiting value="false" />
      <AllowPretendOccupants value="true" />
      <AllowJoyriding value="true" />
      <AllowSundayDriving value="true" />
      <AllowBodyColorMapping value="true" />
      <wheelScale value="0.375000" />
      <wheelScaleRear value="0.375000" />
      <dirtLevelMin value="0.000000" />
      <dirtLevelMax value="0.850000" />
      <envEffScaleMin value="0.000000" />
      <envEffScaleMax value="1.000000" />
      <envEffScaleMin2 value="0.000000" />
      <envEffScaleMax2 value="1.000000" />
      <damageMapScale value="0.400000" />
      <damageOffsetScale value="0.400000" />
      <diffuseTint value="0x00FFFFFF" />
      <steerWheelMult value="1.000000" />
      <HDTextureDist value="5.000000" />
      <lodDistances content="float_array">
        20.000000
        35.000000
        90.000000
        180.000000
        500.000000
        500.000000
      </lodDistances>
      <minSeatHeight value="0.400" />
      <identicalModelSpawnDistance value="20" />
      <maxNumOfSameColor value="10" />
      <defaultBodyHealth value="1000.000000" />
      <pretendOccupantsScale value="1.000000" />
      <visibleSpawnDistScale value="1.000000" />
      <trackerPathWidth value="2.000000" />
      <weaponForceMult value="1.000000" />
      <frequency value="30" />
      <swankness>SWANKNESS_1</swankness>
      <maxNum value="5" />
      <flags>FLAG_AVERAGE_CAR FLAG_POOR_CAR FLAG_IS_OFFROAD_VEHICLE FLAG_HEADLIGHTS_USE_ACTUAL_BONE_POS FLAG_IS_BULKY FLAG_HAS_INTERIOR_EXTRAS</flags>
      <type>VEHICLE_TYPE_CAR</type>
      <plateType>VPT_BACK_PLATES</plateType>
	  <dashboardType>VDT_BANSHEE</dashboardType>
      <vehicleClass>VC_SUV</vehicleClass>
      <wheelType>VWT_SUV</wheelType>
      <trailers />
      <additionalTrailers />
      <drivers />
      <extraIncludes />
      <doorsWithCollisionWhenClosed>
        <Item>VEH_EXT_BOOT</Item>
      </doorsWithCollisionWhenClosed>
      <driveableDoors />
      <bumpersNeedToCollideWithMap value="false" />
      <needsRopeTexture value="false" />
      <requiredExtras>EXTRA_1</requiredExtras>
      <rewards />
      <cinematicPartCamera>
        <Item>WHEEL_WIDE_REAR_RIGHT_CAMERA</Item>
        <Item>WHEEL_WIDE_REAR_LEFT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet>Truck</NmBraceOverrideSet>
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000" />
      <buoyancySphereSizeScale value="1.000000" />
      <pOverrideRagdollThreshold type="NULL" />
      <firstPersonDrivebyData>
        <Item>VAN_TOROS_FRONT_LEFT</Item>
        <Item>VAN_TOROS_FRONT_RIGHT</Item>
        <Item>VAN_TOROS_REAR_LEFT</Item>
        <Item>VAN_TOROS_REAR_RIGHT</Item>
      </firstPersonDrivebyData>
    </Item>
    <Item>
      <modelName>deviant</modelName>
      <txdName>deviant</txdName>
      <handlingId>DEVIANT</handlingId>
      <gameName>DEVIANT</gameName>
      <vehicleMakeName>SCHYSTER</vehicleMakeName>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected />
      <ptfxAssetName>null</ptfxAssetName>
      <audioNameHash />
      <layout>LAYOUT_STD_DEVIANT</layout>
      <coverBoundOffsets>DEVIANT_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout />
      <cameraName>FOLLOW_CHEETAH_CAMERA</cameraName>
      <aimCameraName>DEFAULT_THIRD_PERSON_VEHICLE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>VEHICLE_BONNET_CAMERA_STANDARD_LONG_DEVIANT</bonnetCameraName>
      <povCameraName>REDUCED_NEAR_CLIP_POV_CAMERA</povCameraName>
      <FirstPersonDriveByIKOffset x="0.020000" y="-0.140000" z="-0.060000" />
      <FirstPersonDriveByUnarmedIKOffset x="-0.010000" y="-0.180000" z="-0.010000" />
	  <FirstPersonProjectileDriveByIKOffset x="0.060000" y="-0.160000" z="-0.070000" />
	  <FirstPersonProjectileDriveByPassengerIKOffset x="-0.100000" y="-0.130000" z="-0.060000" />
	  <FirstPersonDriveByRightPassengerIKOffset x="-0.020000" y="-0.140000" z="-0.060000" />
	  <FirstPersonDriveByRightPassengerUnarmedIKOffset x="-0.010000" y="-0.180000" z="-0.010000" />
	  <FirstPersonMobilePhoneOffset x="0.140000" y="0.190000" z="0.530000" />
      <FirstPersonPassengerMobilePhoneOffset x="0.240000" y="0.140000" z="0.415000" />
      <PovCameraOffset x="0.000000" y="-0.230000" z="0.660000" />
      <PovCameraVerticalAdjustmentForRollCage value="0.000000" />
      <PovPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000" />
      <PovRearPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000" />
      <vfxInfoName>VFXVEHICLEINFO_CAR_GENERIC</vfxInfoName>
      <shouldUseCinematicViewMode value="true" />
      <shouldCameraTransitionOnClimbUpDown value="false" />
      <shouldCameraIgnoreExiting value="false" />
      <AllowPretendOccupants value="true" />
      <AllowJoyriding value="true" />
      <AllowSundayDriving value="true" />
      <AllowBodyColorMapping value="true" />
      <wheelScale value="0.265000" />
      <wheelScaleRear value="0.265000" />
      <dirtLevelMin value="0.000000" />
      <dirtLevelMax value="0.450000" />
      <envEffScaleMin value="0.000000" />
      <envEffScaleMax value="1.000000" />
      <envEffScaleMin2 value="0.000000" />
      <envEffScaleMax2 value="1.000000" />
      <damageMapScale value="0.40000" />
      <damageOffsetScale value="0.40000" />
      <diffuseTint value="0x00FFFFFF" />
      <steerWheelMult value="0.700000" />
      <HDTextureDist value="5.000000" />
      <lodDistances content="float_array">
        15.000000
        30.000000
        60.000000	
        120.000000
        500.000000
        500.000000
      </lodDistances>
      <minSeatHeight value="0.844" />
      <identicalModelSpawnDistance value="20" />
      <maxNumOfSameColor value="1" />
      <defaultBodyHealth value="1000.000000" />
      <pretendOccupantsScale value="1.000000" />
      <visibleSpawnDistScale value="1.000000" />
      <trackerPathWidth value="2.000000" />
      <weaponForceMult value="1.000000" />
      <frequency value="30" />
      <swankness>SWANKNESS_4</swankness>
      <maxNum value="10" />
      <flags>FLAG_EXTRAS_ALL FLAG_EXTRAS_STRONG FLAG_AVERAGE_CAR FLAG_HAS_INTERIOR_EXTRAS FLAG_CAN_HAVE_NEONS FLAG_HAS_SUPERCHARGER</flags>
      <type>VEHICLE_TYPE_CAR</type>
      <plateType>VPT_BACK_PLATES</plateType>
	  <dashboardType>VDT_RACE</dashboardType>
      <vehicleClass>VC_MUSCLE</vehicleClass>
      <wheelType>VWT_MUSCLE</wheelType>
      <trailers />
      <additionalTrailers />
      <drivers />
      <extraIncludes />
      <doorsWithCollisionWhenClosed />
      <driveableDoors />
      <bumpersNeedToCollideWithMap value="false" />
      <needsRopeTexture value="false" />
      <requiredExtras />
      <rewards />
      <cinematicPartCamera>
        <Item>WHEEL_FRONT_RIGHT_CAMERA</Item>
        <Item>WHEEL_FRONT_LEFT_CAMERA</Item>
        <Item>WHEEL_REAR_RIGHT_CAMERA</Item>
        <Item>WHEEL_REAR_LEFT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet />
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000" />
      <buoyancySphereSizeScale value="1.000000" />
      <pOverrideRagdollThreshold type="NULL" />
	  <firstPersonDrivebyData>
        <Item>STD_DEVIANT_FRONT_LEFT</Item>
        <Item>STD_DEVIANT_FRONT_RIGHT</Item>
      </firstPersonDrivebyData>
    </Item>
	<Item>
      <modelName>schlagen</modelName>
      <txdName>schlagen</txdName>
      <handlingId>SCHLAGEN</handlingId>
      <gameName>SCHLAGEN</gameName>
      <vehicleMakeName>BENEFACTOR</vehicleMakeName>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected />
      <ptfxAssetName>null</ptfxAssetName>
      <audioNameHash />
      <layout>LAYOUT_LOW_RESTRICTED_SCHLAGEN</layout>
      <coverBoundOffsets>SCHLAGEN_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout />
      <cameraName>FOLLOW_CHEETAH_CAMERA</cameraName>
      <aimCameraName>DEFAULT_THIRD_PERSON_VEHICLE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>DEFAULT_VEHICLE_BONNET_CAMERA</bonnetCameraName>
      <vfxInfoName>VFXVEHICLEINFO_CAR_GENERIC</vfxInfoName>
      <povCameraName>REDUCED_NEAR_CLIP_POV_CAMERA</povCameraName>
      <FirstPersonDriveByIKOffset x="0.040000" y="-0.120000" z="-0.030000" />
      <FirstPersonDriveByUnarmedIKOffset x="-0.020000" y="-0.060000" z="-0.025000" />
	  <FirstPersonProjectileDriveByIKOffset x="0.000000" y="-0.110000" z="-0.060000" />
	  <FirstPersonProjectileDriveByPassengerIKOffset x="0.000000" y="-0.110000" z="-0.060000" />
	  <FirstPersonDriveByRightPassengerIKOffset x="-0.040000" y="-0.12000" z="-0.030000" />
	  <FirstPersonDriveByRightPassengerUnarmedIKOffset x="0.000000" y="-0.120000" z="-0.025000" />
	  <FirstPersonMobilePhoneOffset x="0.141000" y="0.166000" z="0.560000" />
	  <FirstPersonPassengerMobilePhoneOffset x="0.233000" y="0.138000" z="0.440000" />
      <PovCameraOffset x="0.000000" y="-0.275000" z="0.690000" />
      <PovCameraVerticalAdjustmentForRollCage value="0.000000" />
      <PovPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000" />
      <shouldUseCinematicViewMode value="true" />
      <shouldCameraTransitionOnClimbUpDown value="false" />
      <shouldCameraIgnoreExiting value="false" />
      <AllowPretendOccupants value="true" />
      <AllowJoyriding value="true" />
      <AllowSundayDriving value="true" />
      <AllowBodyColorMapping value="true" />
      <wheelScale value="0.270000" />
      <wheelScaleRear value="0.270000" />
      <dirtLevelMin value="0.000000" />
      <dirtLevelMax value="0.800000" />
      <envEffScaleMin value="0.000000" />
      <envEffScaleMax value="1.000000" />
      <envEffScaleMin2 value="0.000000" />
      <envEffScaleMax2 value="1.000000" />
      <damageMapScale value="0.350000" />
      <damageOffsetScale value="0.350000" />
      <diffuseTint value="0x00FFFFFF" />
      <steerWheelMult value="1.000000" />
      <HDTextureDist value="5.000000" />
      <lodDistances content="float_array">
        10.000000	
        25.000000	
        60.000000	
        120.000000	
        500.000000	
        500.000000
      </lodDistances>
      <minSeatHeight value="0.422" />
      <identicalModelSpawnDistance value="20" />
      <maxNumOfSameColor value="1" />
      <defaultBodyHealth value="1000.000000" />
      <pretendOccupantsScale value="1.000000" />
      <visibleSpawnDistScale value="1.000000" />
      <trackerPathWidth value="2.000000" />
      <weaponForceMult value="1.000000" />
      <frequency value="5" />
      <swankness>SWANKNESS_5</swankness>
      <maxNum value="2" />
      <flags>FLAG_SPORTS FLAG_RICH_CAR FLAG_NO_BROKEN_DOWN_SCENARIO FLAG_RECESSED_TAILLIGHT_CORONAS FLAG_COUNT_AS_FACEBOOK_DRIVEN</flags>
      <type>VEHICLE_TYPE_CAR</type>
      <plateType>VPT_BACK_PLATES</plateType>
      <dashboardType>VDT_RACE</dashboardType>
      <vehicleClass>VC_SPORT</vehicleClass>
      <wheelType>VWT_HIEND</wheelType>
      <trailers />
      <additionalTrailers />
      <drivers />
      <extraIncludes />
      <doorsWithCollisionWhenClosed />
      <driveableDoors />
      <bumpersNeedToCollideWithMap value="false" />
      <needsRopeTexture value="false" />
      <requiredExtras />
      <rewards />
      <cinematicPartCamera>
        <Item>WHEEL_FRONT_RIGHT_CAMERA</Item>
        <Item>WHEEL_FRONT_LEFT_CAMERA</Item>
        <Item>WHEEL_REAR_RIGHT_CAMERA</Item>
        <Item>WHEEL_REAR_LEFT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet />
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000" />
      <buoyancySphereSizeScale value="1.000000" />
      <pOverrideRagdollThreshold type="NULL" />
      <firstPersonDrivebyData>
        <Item>LOW_SCHLAGEN_FRONT_LEFT</Item>
        <Item>LOW_SCHLAGEN_FRONT_RIGHT</Item>
      </firstPersonDrivebyData>
    </Item>
    <Item>
      <modelName>impaler</modelName>
      <txdName>impaler</txdName>
      <handlingId>IMPALER</handlingId>
      <gameName>IMPALER</gameName>
      <vehicleMakeName>DECLASSE</vehicleMakeName>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected />
      <ptfxAssetName>null</ptfxAssetName>
      <audioNameHash />
      <layout>LAYOUT_STANDARD</layout>
      <coverBoundOffsets>IMPALER_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout />
      <cameraName>FOLLOW_CHEETAH_CAMERA</cameraName>
      <aimCameraName>DEFAULT_THIRD_PERSON_VEHICLE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>VEHICLE_BONNET_CAMERA_STANDARD_LONG_DEVIANT</bonnetCameraName>
      <povCameraName>REDUCED_NEAR_CLIP_POV_CAMERA</povCameraName>
      <FirstPersonDriveByIKOffset x="0.020000" y="-0.050000" z="-0.050000" />
      <FirstPersonDriveByUnarmedIKOffset x="-0.025000" y="-0.050000" z="0.000000" />
	  <FirstPersonProjectileDriveByIKOffset x="0.000000" y="-0.130000" z="-0.050000" />
	  <FirstPersonProjectileDriveByPassengerIKOffset x="0.000000" y="-0.100000" z="0.000000" />
	  <FirstPersonDriveByRightPassengerIKOffset x="0.020000" y="-0.050000" z="-0.050000" />
	  <FirstPersonDriveByRightPassengerUnarmedIKOffset x="-0.025000" y="-0.050000" z="0.000000" />
	  <FirstPersonMobilePhoneOffset x="0.146000" y="0.280000" z="0.510000" />
      <FirstPersonPassengerMobilePhoneOffset x="0.194000" y="0.199000" z="0.405000" />
      <PovCameraOffset x="0.000000" y="-0.195000" z="0.640000" />
      <PovCameraVerticalAdjustmentForRollCage value="0.000000" />
      <PovPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000" />
      <PovRearPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000" />
      <vfxInfoName>VFXVEHICLEINFO_CAR_GENERIC</vfxInfoName>
      <shouldUseCinematicViewMode value="true" />
      <shouldCameraTransitionOnClimbUpDown value="false" />
      <shouldCameraIgnoreExiting value="false" />
      <AllowPretendOccupants value="true" />
      <AllowJoyriding value="true" />
      <AllowSundayDriving value="true" />
      <AllowBodyColorMapping value="true" />
      <wheelScale value="0.202300" />
      <wheelScaleRear value="0.202300" />
      <dirtLevelMin value="0.000000" />
      <dirtLevelMax value="0.450000" />
      <envEffScaleMin value="0.000000" />
      <envEffScaleMax value="1.000000" />
      <envEffScaleMin2 value="0.000000" />
      <envEffScaleMax2 value="1.000000" />
      <damageMapScale value="0.400000" />
      <damageOffsetScale value="0.400000" />
      <diffuseTint value="0x00FFFFFF" />
      <steerWheelMult value="0.700000" />
      <HDTextureDist value="5.000000" />
      <lodDistances content="float_array">
        10.000000
        25.000000
        60.000000
        120.000000
        500.000000
        500.000000
      </lodDistances>
      <minSeatHeight value="0.844" />
      <identicalModelSpawnDistance value="20" />
      <maxNumOfSameColor value="1" />
      <defaultBodyHealth value="1000.000000" />
      <pretendOccupantsScale value="1.000000" />
      <visibleSpawnDistScale value="1.000000" />
      <trackerPathWidth value="2.000000" />
      <weaponForceMult value="1.000000" />
      <frequency value="30" />
      <swankness>SWANKNESS_4</swankness>
      <maxNum value="10" />
      <flags>FLAG_EXTRAS_STRONG FLAG_AVERAGE_CAR FLAG_HAS_INTERIOR_EXTRAS FLAG_CAN_HAVE_NEONS FLAG_INCREASE_CAMBER_WITH_SUSPENSION_MOD FLAG_HAS_SUPERCHARGER</flags>
      <type>VEHICLE_TYPE_CAR</type>
      <plateType>VPT_FRONT_AND_BACK_PLATES</plateType>
	  <dashboardType>VDT_DUKES</dashboardType>
      <vehicleClass>VC_MUSCLE</vehicleClass>
      <wheelType>VWT_MUSCLE</wheelType>
      <trailers />
      <additionalTrailers />
      <drivers />
      <extraIncludes />
      <doorsWithCollisionWhenClosed />
      <driveableDoors />
      <bumpersNeedToCollideWithMap value="false" />
      <needsRopeTexture value="false" />
      <requiredExtras />
      <rewards />
      <cinematicPartCamera>
        <Item>WHEEL_FRONT_RIGHT_CAMERA</Item>
        <Item>WHEEL_FRONT_LEFT_CAMERA</Item>
        <Item>WHEEL_REAR_RIGHT_CAMERA</Item>
        <Item>WHEEL_REAR_LEFT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet />
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000" />
      <buoyancySphereSizeScale value="1.000000" />
      <pOverrideRagdollThreshold type="NULL" />
	  <firstPersonDrivebyData>
        <Item>STD_IMPALER_FRONT_LEFT</Item>
        <Item>STD_IMPALER_FRONT_RIGHT</Item>
      </firstPersonDrivebyData>
	  <numSeatsOverride value="2" />
    </Item>
	<Item>
      <modelName>zr380</modelName>
      <txdName>zr380</txdName>
      <handlingId>ZR380</handlingId>
      <gameName>ZR380</gameName>
      <vehicleMakeName>ANNIS</vehicleMakeName>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected />
      <ptfxAssetName>weap_xs_vehicle_weapons</ptfxAssetName>
      <audioNameHash />
      <layout>LAYOUT_STD_ARENA_1HONLY</layout>
      <coverBoundOffsets>ZR380_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout />
      <cameraName>ZR380_FOLLOW_CAMERA</cameraName>
      <aimCameraName>DEFAULT_THIRD_PERSON_VEHICLE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>VEHICLE_BONNET_CAMERA_STOCKADE</bonnetCameraName>
      <povCameraName>DEFAULT_POV_CAMERA_NO_REVERSE_ZR380</povCameraName>
      <FirstPersonDriveByIKOffset x="0.070000" y="-0.200000" z="-0.010000" />
      <FirstPersonDriveByUnarmedIKOffset x="0.000000" y="-0.100000" z="-0.020000" />
	  <FirstPersonProjectileDriveByIKOffset x="0.000000" y="-0.130000" z="-0.050000" />
	  <FirstPersonProjectileDriveByPassengerIKOffset x="0.000000" y="-0.130000" z="-0.050000" />
	  <FirstPersonDriveByRightPassengerIKOffset x="-0.070000" y="-0.200000" z="-0.010000" />
	  <FirstPersonDriveByRightPassengerUnarmedIKOffset x="0.000000" y="-0.100000" z="-0.020000" />
	  <FirstPersonMobilePhoneOffset x="0.133000" y="0.228000" z="0.563000" />
      <FirstPersonPassengerMobilePhoneOffset x="0.226000" y="0.223000" z="0.435000" />
      <PovCameraOffset x="0.000000" y="-0.165000" z="0.670000" />
      <PovCameraVerticalAdjustmentForRollCage value="0.000000" />
      <PovPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000" />
      <vfxInfoName>VFXVEHICLEINFO_CAR_GENERIC</vfxInfoName>
      <shouldUseCinematicViewMode value="true" />
      <shouldCameraTransitionOnClimbUpDown value="false" />
      <shouldCameraIgnoreExiting value="false" />
      <AllowPretendOccupants value="true" />
      <AllowJoyriding value="true" />
      <AllowSundayDriving value="true" />
      <AllowBodyColorMapping value="true" />
      <wheelScale value="0.262000" />
      <wheelScaleRear value="0.262000" />
      <dirtLevelMin value="0.000000" />
      <dirtLevelMax value="0.800000" />
      <envEffScaleMin value="1.000000" />
      <envEffScaleMax value="1.000000" />
      <envEffScaleMin2 value="1.000000" />
      <envEffScaleMax2 value="1.000000" />
      <damageMapScale value="0.300000" />
      <damageOffsetScale value="0.300000" />
      <diffuseTint value="0x00FFFFFF" />
      <steerWheelMult value="1.000000" />
      <HDTextureDist value="5.000000" />
      <lodDistances content="float_array">
        12.000000
        30.000000
        60.000000
        120.000000
        500.000000
        500.000000
      </lodDistances>
      <identicalModelSpawnDistance value="20" />
      <maxNumOfSameColor value="10" />
      <defaultBodyHealth value="1000.000000" />
      <pretendOccupantsScale value="1.000000" />
      <visibleSpawnDistScale value="1.000000" />
      <trackerPathWidth value="2.000000" />
      <weaponForceMult value="1.000000" />
      <frequency value="90" />
      <swankness>SWANKNESS_2</swankness>
      <maxNum value="20" />
      <flags>FLAG_SPAWN_ON_TRAILER FLAG_PARKING_SENSORS FLAG_AVERAGE_CAR FLAG_RECESSED_HEADLIGHT_CORONAS FLAG_RECESSED_TAILLIGHT_CORONAS FLAG_HAS_INTERIOR_EXTRAS FLAG_HAS_NITROUS_MOD FLAG_USE_AIRCRAFT_STYLE_WEAPON_TARGETING FLAG_HAS_WEAPON_BLADE_MODS FLAG_HAS_JUMP_MOD FLAG_HAS_SIDE_SHUNT FLAG_HAS_RAMMING_SCOOP_MOD FLAG_DISABLE_THROUGH_WINDSCREEN FLAG_HAS_WEAPON_SPIKE_MODS</flags>
      <type>VEHICLE_TYPE_CAR</type>
      <plateType>VPT_BACK_PLATES</plateType>
      <dashboardType>VDT_DUKES</dashboardType>
      <vehicleClass>VC_SPORT</vehicleClass>
      <wheelType>VWT_MUSCLE</wheelType>
      <trailers />
      <additionalTrailers />
      <drivers />
      <extraIncludes />
      <doorsWithCollisionWhenClosed />
      <driveableDoors />
      <bumpersNeedToCollideWithMap value="false" />
      <needsRopeTexture value="false" />
      <requiredExtras />
      <rewards />
      <cinematicPartCamera>
        <Item>WHEEL_FRONT_RIGHT_CAMERA</Item>
        <Item>WHEEL_FRONT_LEFT_CAMERA</Item>
        <Item>WHEEL_REAR_RIGHT_CAMERA</Item>
        <Item>WHEEL_REAR_LEFT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet />
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000" />
      <buoyancySphereSizeScale value="1.000000" />
      <pOverrideRagdollThreshold type="NULL" />
      <firstPersonDrivebyData>
        <Item>STD_ZR380_FRONT_LEFT</Item>
        <Item>STD_ZR380_FRONT_RIGHT</Item>
      </firstPersonDrivebyData>
	  <numSeatsOverride value="2" />
    </Item>
	<Item>
      <modelName>zr3802</modelName>
      <txdName>zr3802</txdName>
      <handlingId>ZR3802</handlingId>
      <gameName>ZR3802</gameName>
      <vehicleMakeName>ANNIS</vehicleMakeName>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected />
      <ptfxAssetName>weap_xs_vehicle_weapons</ptfxAssetName>
      <audioNameHash />
      <layout>LAYOUT_STD_ARENA_1HONLY</layout>
      <coverBoundOffsets>ZR380_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout />
      <cameraName>ZR380_FOLLOW_CAMERA</cameraName>
      <aimCameraName>DEFAULT_THIRD_PERSON_VEHICLE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>VEHICLE_BONNET_CAMERA_STOCKADE</bonnetCameraName>
      <povCameraName>DEFAULT_POV_CAMERA_NO_REVERSE_ZR380</povCameraName>
      <FirstPersonDriveByIKOffset x="0.070000" y="-0.200000" z="-0.010000" />
      <FirstPersonDriveByUnarmedIKOffset x="0.000000" y="-0.100000" z="-0.020000" />
	  <FirstPersonProjectileDriveByIKOffset x="0.000000" y="-0.130000" z="-0.050000" />
	  <FirstPersonProjectileDriveByPassengerIKOffset x="0.000000" y="-0.130000" z="-0.050000" />
	  <FirstPersonDriveByRightPassengerIKOffset x="-0.070000" y="-0.200000" z="-0.010000" />
	  <FirstPersonDriveByRightPassengerUnarmedIKOffset x="0.000000" y="-0.100000" z="-0.020000" />
	  <FirstPersonMobilePhoneOffset x="0.133000" y="0.228000" z="0.563000" />
      <FirstPersonPassengerMobilePhoneOffset x="0.226000" y="0.223000" z="0.435000" />
      <PovCameraOffset x="0.000000" y="-0.165000" z="0.670000" />
      <PovCameraVerticalAdjustmentForRollCage value="0.000000" />
      <PovPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000" />
      <vfxInfoName>VFXVEHICLEINFO_CAR_GENERIC</vfxInfoName>
      <shouldUseCinematicViewMode value="true" />
      <shouldCameraTransitionOnClimbUpDown value="false" />
      <shouldCameraIgnoreExiting value="false" />
      <AllowPretendOccupants value="true" />
      <AllowJoyriding value="true" />
      <AllowSundayDriving value="true" />
      <AllowBodyColorMapping value="true" />
      <wheelScale value="0.262000" />
      <wheelScaleRear value="0.262000" />
      <dirtLevelMin value="0.000000" />
      <dirtLevelMax value="0.800000" />
      <envEffScaleMin value="1.000000" />
      <envEffScaleMax value="1.000000" />
      <envEffScaleMin2 value="1.000000" />
      <envEffScaleMax2 value="1.000000" />
      <damageMapScale value="0.000000" />
      <damageOffsetScale value="0.100000" />
      <diffuseTint value="0x00FFFFFF" />
      <steerWheelMult value="1.000000" />
      <HDTextureDist value="5.000000" />
      <lodDistances content="float_array">
        12.000000
        30.000000
        60.000000
        120.000000
        500.000000
        500.000000
      </lodDistances>
      <identicalModelSpawnDistance value="20" />
      <maxNumOfSameColor value="10" />
      <defaultBodyHealth value="1000.000000" />
      <pretendOccupantsScale value="1.000000" />
      <visibleSpawnDistScale value="1.000000" />
      <trackerPathWidth value="2.000000" />
      <weaponForceMult value="1.000000" />
      <frequency value="90" />
      <swankness>SWANKNESS_2</swankness>
      <maxNum value="20" />
      <flags>FLAG_SPAWN_ON_TRAILER FLAG_PARKING_SENSORS FLAG_AVERAGE_CAR FLAG_RECESSED_HEADLIGHT_CORONAS FLAG_RECESSED_TAILLIGHT_CORONAS FLAG_HAS_INTERIOR_EXTRAS FLAG_HAS_NITROUS_MOD FLAG_USE_AIRCRAFT_STYLE_WEAPON_TARGETING FLAG_HAS_WEAPON_BLADE_MODS FLAG_HAS_JUMP_MOD FLAG_HAS_SIDE_SHUNT FLAG_HAS_RAMMING_SCOOP_MOD FLAG_DISABLE_THROUGH_WINDSCREEN FLAG_HAS_WEAPON_SPIKE_MODS FLAG_DISABLE_DEFORMATION</flags>
      <type>VEHICLE_TYPE_CAR</type>
      <plateType>VPT_BACK_PLATES</plateType>
      <dashboardType>VDT_DUKES</dashboardType>
      <vehicleClass>VC_SPORT</vehicleClass>
      <wheelType>VWT_MUSCLE</wheelType>
      <trailers />
      <additionalTrailers />
      <drivers />
      <extraIncludes />
      <doorsWithCollisionWhenClosed />
      <driveableDoors />
      <bumpersNeedToCollideWithMap value="false" />
      <needsRopeTexture value="false" />
      <requiredExtras />
      <rewards />
      <cinematicPartCamera>
        <Item>WHEEL_FRONT_RIGHT_CAMERA</Item>
        <Item>WHEEL_FRONT_LEFT_CAMERA</Item>
        <Item>WHEEL_REAR_RIGHT_CAMERA</Item>
        <Item>WHEEL_REAR_LEFT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet />
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000" />
      <buoyancySphereSizeScale value="1.000000" />
      <pOverrideRagdollThreshold type="NULL" />
      <firstPersonDrivebyData>
        <Item>STD_ZR380_FRONT_LEFT</Item>
        <Item>STD_ZR380_FRONT_RIGHT</Item>
      </firstPersonDrivebyData>
	  <numSeatsOverride value="2" />
    </Item>
	<Item>
      <modelName>zr3803</modelName>
      <txdName>zr3803</txdName>
      <handlingId>ZR380</handlingId>
      <gameName>ZR3803</gameName>
      <vehicleMakeName>ANNIS</vehicleMakeName>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected />
      <ptfxAssetName>weap_xs_vehicle_weapons</ptfxAssetName>
      <audioNameHash />
      <layout>LAYOUT_STD_ARENA_1HONLY</layout>
      <coverBoundOffsets>ZR380_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout />
      <cameraName>ZR380_FOLLOW_CAMERA</cameraName>
      <aimCameraName>DEFAULT_THIRD_PERSON_VEHICLE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>VEHICLE_BONNET_CAMERA_STOCKADE</bonnetCameraName>
      <povCameraName>DEFAULT_POV_CAMERA_NO_REVERSE_ZR380</povCameraName>
      <FirstPersonDriveByIKOffset x="0.070000" y="-0.200000" z="-0.010000" />
      <FirstPersonDriveByUnarmedIKOffset x="0.000000" y="-0.100000" z="-0.020000" />
	  <FirstPersonProjectileDriveByIKOffset x="0.000000" y="-0.130000" z="-0.050000" />
	  <FirstPersonProjectileDriveByPassengerIKOffset x="0.000000" y="-0.130000" z="-0.050000" />
	  <FirstPersonDriveByRightPassengerIKOffset x="-0.070000" y="-0.200000" z="-0.010000" />
	  <FirstPersonDriveByRightPassengerUnarmedIKOffset x="0.000000" y="-0.100000" z="-0.020000" />
	  <FirstPersonMobilePhoneOffset x="0.133000" y="0.228000" z="0.563000" />
      <FirstPersonPassengerMobilePhoneOffset x="0.226000" y="0.223000" z="0.435000" />
      <PovCameraOffset x="0.000000" y="-0.165000" z="0.670000" />
      <PovCameraVerticalAdjustmentForRollCage value="0.000000" />
      <PovPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000" />
      <vfxInfoName>VFXVEHICLEINFO_CAR_GENERIC</vfxInfoName>
      <shouldUseCinematicViewMode value="true" />
      <shouldCameraTransitionOnClimbUpDown value="false" />
      <shouldCameraIgnoreExiting value="false" />
      <AllowPretendOccupants value="true" />
      <AllowJoyriding value="true" />
      <AllowSundayDriving value="true" />
      <AllowBodyColorMapping value="true" />
      <wheelScale value="0.262000" />
      <wheelScaleRear value="0.262000" />
      <dirtLevelMin value="0.000000" />
      <dirtLevelMax value="0.800000" />
      <envEffScaleMin value="1.000000" />
      <envEffScaleMax value="1.000000" />
      <envEffScaleMin2 value="1.000000" />
      <envEffScaleMax2 value="1.000000" />
      <damageMapScale value="0.300000" />
      <damageOffsetScale value="0.300000" />
      <diffuseTint value="0x00FFFFFF" />
      <steerWheelMult value="1.000000" />
      <HDTextureDist value="5.000000" />
      <lodDistances content="float_array">
        12.000000
        30.000000
        60.000000
        120.000000
        500.000000
        500.000000
      </lodDistances>
      <identicalModelSpawnDistance value="20" />
      <maxNumOfSameColor value="10" />
      <defaultBodyHealth value="1000.000000" />
      <pretendOccupantsScale value="1.000000" />
      <visibleSpawnDistScale value="1.000000" />
      <trackerPathWidth value="2.000000" />
      <weaponForceMult value="1.000000" />
      <frequency value="90" />
      <swankness>SWANKNESS_2</swankness>
      <maxNum value="20" />
      <flags>FLAG_SPAWN_ON_TRAILER FLAG_PARKING_SENSORS FLAG_AVERAGE_CAR FLAG_RECESSED_HEADLIGHT_CORONAS FLAG_RECESSED_TAILLIGHT_CORONAS FLAG_HAS_INTERIOR_EXTRAS FLAG_HAS_NITROUS_MOD FLAG_USE_AIRCRAFT_STYLE_WEAPON_TARGETING FLAG_HAS_WEAPON_BLADE_MODS FLAG_HAS_JUMP_MOD FLAG_HAS_SIDE_SHUNT FLAG_HAS_RAMMING_SCOOP_MOD FLAG_DISABLE_THROUGH_WINDSCREEN FLAG_HAS_WEAPON_SPIKE_MODS</flags>
      <type>VEHICLE_TYPE_CAR</type>
      <plateType>VPT_BACK_PLATES</plateType>
      <dashboardType>VDT_DUKES</dashboardType>
      <vehicleClass>VC_SPORT</vehicleClass>
      <wheelType>VWT_MUSCLE</wheelType>
      <trailers />
      <additionalTrailers />
      <drivers />
      <extraIncludes />
      <doorsWithCollisionWhenClosed />
      <driveableDoors />
      <bumpersNeedToCollideWithMap value="false" />
      <needsRopeTexture value="false" />
      <requiredExtras />
      <rewards />
      <cinematicPartCamera>
        <Item>WHEEL_FRONT_RIGHT_CAMERA</Item>
        <Item>WHEEL_FRONT_LEFT_CAMERA</Item>
        <Item>WHEEL_REAR_RIGHT_CAMERA</Item>
        <Item>WHEEL_REAR_LEFT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet />
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000" />
      <buoyancySphereSizeScale value="1.000000" />
      <pOverrideRagdollThreshold type="NULL" />
      <firstPersonDrivebyData>
        <Item>STD_ZR380_FRONT_LEFT</Item>
        <Item>STD_ZR380_FRONT_RIGHT</Item>
      </firstPersonDrivebyData>
	  <numSeatsOverride value="2" />
    </Item>
	
 </InitDatas>
  <txdRelationships>
    <Item>
      <parent>vehshare</parent>
      <child>vehicles_itali_w_interior</child>
    </Item>
    <Item>
      <parent>vehshare</parent>
      <child>vehicles_arena</child>
    </Item>
    <Item>
      <parent>vehicles_arena</parent>
      <child>vehicles_wasteland</child>
    </Item>
    <Item>
      <parent>vehicles_arena</parent>
      <child>vehicles_scifi</child>
    </Item>
    <Item>
      <parent>vehicles_arena</parent>
      <child>vehicles_consumer</child>
    </Item> 	
	<Item>
      <parent>vehicles_wasteland</parent>
      <child>bruiser</child>
    </Item> 
    <Item>
      <parent>vehicles_scifi</parent>
      <child>bruiser2</child>
    </Item> 
    <Item>
      <parent>vehicles_consumer</parent>
      <child>bruiser3</child>
    </Item> 
  	<Item>
      <parent>vehicles_wasteland</parent>
      <child>brutus</child>
	</Item>
	<Item>
      <parent>vehicles_scifi</parent>
      <child>brutus2</child>
    </Item> 
  	<Item>
      <parent>vehicles_consumer</parent>
      <child>brutus3</child>
	</Item>	
	<Item>
      <parent>vehicles_wasteland</parent>
      <child>cerberus</child>
    </Item>
	<Item>
      <parent>vehicles_scifi</parent>
      <child>cerberus2</child>
    </Item>
	<Item>
      <parent>vehicles_consumer</parent>
      <child>cerberus3</child>
    </Item>
    <Item>
      <parent>vehicles_dom_interior</parent>
      <child>clique</child>
    </Item>
    <Item>
      <parent>vehicles_wasteland</parent>
      <child>deathbike</child>
    </Item>	
    <Item>
      <parent>vehicles_consumer</parent>
      <child>deathbike3</child>
    </Item>		
    <Item>
      <parent>vehicles_proto_w_interior</parent>
      <child>deveste</child>
    </Item>
    <Item>
      <parent>vehicles_race_generic</parent>
      <child>deviant</child>
    </Item>
    <Item>
      <parent>vehicles_wasteland</parent>
      <child>dominator4</child>
    </Item>
    <Item>
      <parent>vehicles_scifi</parent>
      <child>dominator5</child>
    </Item> 
    <Item>
      <parent>vehicles_consumer</parent>
      <child>dominator6</child>
    </Item> 	
    <Item>
      <parent>vehicles_dom_w_interior</parent>
      <child>impaler</child>
    </Item>
    <Item>
      <parent>vehicles_wasteland</parent>
      <child>impaler2</child>
    </Item> 	
    <Item>
      <parent>vehicles_scifi</parent>
      <child>impaler3</child>
    </Item>
    <Item>
      <parent>vehicles_consumer</parent>
      <child>impaler4</child>
    </Item> 
    <Item>
      <parent>vehicles_wasteland</parent>
      <child>imperator</child>
    </Item> 
    <Item>
      <parent>vehicles_scifi</parent>
      <child>imperator2</child>
    </Item> 
    <Item>
      <parent>vehicles_consumer</parent>
      <child>imperator3</child>
    </Item>
	<Item>
      <parent>vehicles_wasteland</parent>
      <child>issi4</child>
    </Item>
	<Item>
      <parent>vehicles_scifi</parent>
      <child>issi5</child>
    </Item> 
	<Item>
      <parent>vehicles_consumer</parent>
      <child>issi6</child>
    </Item> 
	<Item>
      <parent>vehicles_itali_w_interior</parent>
      <child>italigto</child>
    </Item>
    <Item>
      <parent>vehicles_wasteland</parent>
      <child>monster3</child>
    </Item>
    <Item>
      <parent>vehicles_scifi</parent>
      <child>monster4</child>
    </Item>
    <Item>
      <parent>vehicles_consumer</parent>
      <child>monster5</child>
    </Item>	
	<Item>
      <parent>vehicles_specter_w_interior</parent>
      <child>schlagen</child>
    </Item>	
	<Item>
      <parent>vehicles_wasteland</parent>
      <child>slamvan4</child>
    </Item> 
    <Item>
      <parent>vehicles_scifi</parent>
      <child>slamvan5</child>
    </Item> 
    <Item>
      <parent>vehicles_consumer</parent>
      <child>slamvan6</child>
    </Item>
	<Item>
      <parent>vehicles_wasteland</parent>
      <child>scarab</child>
    </Item>	
	<Item>
      <parent>vehicles_scifi</parent>
      <child>scarab2</child>
    </Item>	
    <Item>
      <parent>vehicles_consumer</parent>
      <child>scarab3</child>
    </Item>	
	<Item>
      <parent>vehicles_fmj_interior</parent>
      <child>toros</child>
    </Item>
    <Item>
      <parent>vehicles_dom_interior</parent>
      <child>tulip</child>
    </Item>
    <Item>
      <parent>vehicles_dom_interior</parent>
      <child>vamos</child>
    </Item> 
    <Item>
      <parent>vehicles_wasteland</parent>
      <child>zr380</child>
    </Item>
    <Item>
      <parent>vehicles_scifi</parent>
      <child>zr3802</child>
    </Item>
    <Item>
      <parent>vehicles_consumer</parent>
      <child>zr3803</child>
    </Item>	
  </txdRelationships>
</CVehicleModelInfo__InitDataList>
