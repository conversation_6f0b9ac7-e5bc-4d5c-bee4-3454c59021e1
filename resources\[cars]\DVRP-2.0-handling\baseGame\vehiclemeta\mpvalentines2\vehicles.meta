<?xml version="1.0" encoding="UTF-8"?>
<CVehicleModelInfo__InitDataList>
  <residentTxd>vehshare</residentTxd>
  <residentAnims />
  <InitDatas>
    <Item>
      <modelName>btype3</modelName>
      <txdName>btype3</txdName>
      <handlingId>ROOSEVELT</handlingId>
      <gameName>ROOSEVELT2</gameName>
      <vehicleMakeName>ALBANY</vehicleMakeName>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected />
      <ptfxAssetName>null</ptfxAssetName>
      <audioNameHash />
      <layout>LAYOUT_VAN_ROOSEVELT2</layout>
      <coverBoundOffsets>ROOSEVELT2_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout />
      <cameraName>DEFAULT_FOLLOW_VEHICLE_CAMERA</cameraName>
      <aimCameraName>DEFAULT_THIRD_PERSON_VEHICLE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>VEHICLE_BONNET_CAMERA_STANDARD_LONG_EXTRA_LOW</bonnetCameraName>
	  <povCameraName>DEFAULT_POV_CAMERA_LOOKAROUND_MID</povCameraName>
	  <FirstPersonDriveByIKOffset x="0.000000" y="-0.030000" z="-0.030000" />
	  <FirstPersonDriveByUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000" />
	  <FirstPersonProjectileDriveByIKOffset x="0.000000" y="0.000000" z="0.000000" />
	  <FirstPersonProjectileDriveByPassengerIKOffset x="0.000000" y="0.000000" z="0.020000" />
	  <FirstPersonProjectileDriveByRearLeftIKOffset x="0.000000" y="0.000000" z="0.000000" />
	  <FirstPersonProjectileDriveByRearRightIKOffset x="0.000000" y="0.000000" z="-0.068000" />
	  <FirstPersonDriveByLeftPassengerIKOffset x="0.000000" y="0.015000" z="-0.050000" />
	  <FirstPersonDriveByRightPassengerIKOffset x="0.023000" y="-0.040000" z="-0.030000" />
	  <FirstPersonDriveByRightRearPassengerIKOffset x="0.000000" y="0.015000" z="-0.050000" />
	  <FirstPersonDriveByLeftPassengerUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000" />
	  <FirstPersonDriveByRightPassengerUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000" />
	  <FirstPersonPassengerMobilePhoneOffset x="0.000000" y="0.000000" z="0.000000" />
	  <FirstPersonMobilePhoneOffset x="0.145000" y="0.224000" z="0.443000" />
	  <PovCameraOffset x="-0.020000" y="-0.170000" z="0.570000" />
	  <PovCameraVerticalAdjustmentForRollCage value="0.000000" />
	  <PovPassengerCameraOffset x="0.000000" y="0.030000" z="0.070000" />
	  <PovRearPassengerCameraOffset x="0.000000" y="0.030000" z="0.090000" />
      <vfxInfoName>VFXVEHICLEINFO_CAR_GENERIC</vfxInfoName>
      <shouldUseCinematicViewMode value="true" />
      <shouldCameraTransitionOnClimbUpDown value="false" />
      <shouldCameraIgnoreExiting value="false" />
      <AllowPretendOccupants value="true" />
      <AllowJoyriding value="true" />
      <AllowSundayDriving value="true" />
      <AllowBodyColorMapping value="true" />
      <wheelScale value="0.245000" />
      <wheelScaleRear value="0.450700" />
      <dirtLevelMin value="0.000000" />
      <dirtLevelMax value="0.800000" />
      <envEffScaleMin value="0.000000" />
      <envEffScaleMax value="1.000000" />
      <envEffScaleMin2 value="0.000000" />
      <envEffScaleMax2 value="1.000000" />
      <damageMapScale value="0.500000" />
      <damageOffsetScale value="0.500000" />
      <diffuseTint value="0x00FFFFFF" />
      <steerWheelMult value="1.000000" />
      <HDTextureDist value="5.000000" />
      <lodDistances content="float_array">
        10.000000	
        25.000000	
        60.000000	
        120.000000	
        500.000000	
        500.000000
      </lodDistances>
      <minSeatHeight value="0.863" />
      <identicalModelSpawnDistance value="20" />
      <maxNumOfSameColor value="10" />
      <defaultBodyHealth value="1000.000000" />
      <pretendOccupantsScale value="1.000000" />
      <visibleSpawnDistScale value="1.000000" />
      <trackerPathWidth value="2.000000" />
      <weaponForceMult value="1.000000" />
      <frequency value="1" />
      <swankness>SWANKNESS_5</swankness>
      <maxNum value="2" />
      <flags>FLAG_SPORTS FLAG_RICH_CAR FLAG_COUNT_AS_FACEBOOK_DRIVEN</flags>
      <type>VEHICLE_TYPE_CAR</type>
      <plateType>VPT_BACK_PLATES</plateType>
      <dashboardType>VDT_ZTYPE</dashboardType>
      <vehicleClass>VC_SPORT_CLASSIC</vehicleClass>
      <wheelType>VWT_LOWRIDER</wheelType>
      <trailers />
      <additionalTrailers />
      <drivers />
      <extraIncludes />
      <doorsWithCollisionWhenClosed />
      <driveableDoors />
      <bumpersNeedToCollideWithMap value="false" />
      <needsRopeTexture value="false" />
      <requiredExtras />
      <rewards />
      <cinematicPartCamera>
        <Item>WHEEL_FRONT_RIGHT_CAMERA</Item>
        <Item>WHEEL_FRONT_LEFT_CAMERA</Item>
        <Item>WHEEL_REAR_RIGHT_CAMERA</Item>
        <Item>WHEEL_REAR_LEFT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet />
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000" />
      <buoyancySphereSizeScale value="1.000000" />
      <pOverrideRagdollThreshold type="NULL" />
      <firstPersonDrivebyData>
        <Item>ROOSEVELT_FRONT_LEFT</Item>
        <Item>ROOSEVELT_FRONT_RIGHT</Item>
        <Item>ROOSEVELT_REAR_LEFT</Item>
        <Item>ROOSEVELT_REAR_RIGHT</Item>
      </firstPersonDrivebyData>
    </Item>
  </InitDatas>
  <txdRelationships>
    <Item>
      <parent>vehicles_btype_interior</parent>
      <child>btype3</child>
    </Item>    
  </txdRelationships>
</CVehicleModelInfo__InitDataList>
