Citizen.CreateThread(function()

	AddTextEntry('NULL_SPOILER', 'Aero Package')

	AddTextEntryByHash(`SENTMK4`, "Sentinel MK4")
	AddTextEntryByHash(`SENTMK4GTR`, "Sentinel MK4 GTR")
	AddTextEntryByHash(`SENTMK42`, "Sentinel MK4 FD")
	AddTextEntryByHash(`SENTMK43`, "Sentinel MK4 Wagon")
	AddTextEntryByHash(`SENTMK4SG4`, "Sentinel SG4")
	AddTextEntryByHash(`SENTINELMK4_BODY1`, "SG4 Bodykit")
	AddTextEntry('tfbison', 'Bison 35XD')
	AddTextEntry('tfbison2', 'Bison 25XD')
	AddTextEntry('tfbison3', 'Bison 45XD')
	AddTextEntry('tfbison4', 'Bison 35XD Sasquatch')
	AddTextEntry('VMT_TFBIS_WTRIM', 'Window Trim')
	AddTextEntry('VMT_TFBIS_ROOFLITE', 'Roof Lights')
	AddTextEntry('VMT_TFBIS_MIRROR', 'Mirrors')
	AddTextEntry('VMT_TFBIS_RNBRD', 'Running Boards')
	AddTextEntry('VMT_TFBIS_DRHANDL', 'Door Handles')
	AddTextEntry('VMT_TFBIS_TGATE', 'Tailgate')
	AddTextEntry('VMT_TFBIS_BED', 'Bed')
	AddTextEntry('VMT_TFBIS_GRLTRIM', 'Grille Trim')
	AddTextEntry('VMT_TFBIS_BEDLIN', 'Bed Liner')
	AddTextEntry('tfbison_wing_1a', 'Rollbar')
	AddTextEntry('tfbison_wing_1ab', 'Rollbar w/ Spotlight')
	AddTextEntry('tfbison_wing_2a', 'Headache Rack')
	AddTextEntry('tfbison_wing_2ab', 'Headache Rack w/ Toolbox')
	AddTextEntry('tfbison_wing_2ac', 'Headache Rack w/ Cover')
	AddTextEntry('tfbison_wing_3a', 'Toolbox')
	AddTextEntry('tfbison_wing_4a', 'Tonneau Cover')
	AddTextEntry('tfbison_wing_5a', 'Painted Bed Cover')
	AddTextEntry('tfbison_wing_6a', 'Overland Rack')
	AddTextEntry('tfbison_wing_6ab', 'Overland Rack w/ Accessories')
	AddTextEntry('tfbison_wing_6ac', 'Overland Rack w/ Accessories & Tent')
	AddTextEntry('tfbison_wing_8a', 'Work Rack')
	AddTextEntry('tfbison_wing_8ab', 'Work Rack w/ Stuff')
	AddTextEntry('tfbison_wing_9a', 'Bed Cap')
	AddTextEntry('tfbison2_fbumper_1a', 'Primary')
	AddTextEntry('tfbison2_fbumper_1ab', 'Primary w/ Brushguard')
	AddTextEntry('tfbison_fbumper_1b', 'Brushguard')
	AddTextEntry('tfbison_fbumper_1a', 'Plastic')
	AddTextEntry('tfbison_fbumper_1ab', 'Plastic w/ Brushguard')
	AddTextEntry('tfbison_fbumper_2a', 'Chrome')
	AddTextEntry('tfbison_fbumper_2ab', 'Chrome w/ Brushguard')
	AddTextEntry('tfbison_fbumper_2b', 'Dark Chrome')
	AddTextEntry('tfbison_fbumper_2bb', 'Dark Chrome w/ Brushguard')
	AddTextEntry('tfbison_fbumper_3a', 'Paint Match')
	AddTextEntry('tfbison_fbumper_3ab', 'Paint Match w/ Brushguard')
	AddTextEntry('tfbison_fbumper_4a', 'Heavy Duty')
	AddTextEntry('tfbison_fbumper_5a', 'Paint Heavy Duty')
	AddTextEntry('tfbison_fbumper_6a', 'Heavy Duty 2')
	AddTextEntry('tfbison2_rbumper_1a', 'Primary')
	AddTextEntry('tfbison_rbumper_1a', 'Plastic')
	AddTextEntry('tfbison_rbumper_2a', 'Chrome')
	AddTextEntry('tfbison_rbumper_2b', 'Dark Chrome')
	AddTextEntry('tfbison_rbumper_3a', 'Paint Match')
	AddTextEntry('tfbison_rbumper_4a', 'Heavy Duty')
	AddTextEntry('tfbison_rbumper_5a', 'Paint Heavy Duty')
	AddTextEntry('tfbison_bedaccs_1a', 'Winch & Materials')
	AddTextEntry('tfbison_bedaccs_2a', 'Lawncare Clutter')
	AddTextEntry('tfbison_bed_2a', 'Bed Liner')
	AddTextEntry('tfbison_bed_2ab', 'Bed Liner w/ Winch')
	AddTextEntry('tfbison_bed_2ac', 'Bed Liner w/ Lawncare')
	AddTextEntry('tfbison2_grille_1a', 'Chrome')
	AddTextEntry('tfbison_grille_1a', 'Plastic')
	AddTextEntry('tfbison_grille_2a', 'Dark Chrome')
	AddTextEntry('tfbison_grille_3a', 'Gloss Black')
	AddTextEntry('tfbison_grille_4a', 'Primary Paint')
	AddTextEntry('tfbison_grille_5a', 'Secondary Paint')
	AddTextEntry('tfbison_grille_6a', 'Vintage Chrome')
	AddTextEntry('tfbison_grille_7a', 'Vintage Plastic')
	AddTextEntry('tfbison_grille_8a', 'Vintage Dark Chrome')
	AddTextEntry('tfbison_grille_9a', 'Vintage Gloss Black')
	AddTextEntry('tfbison_grille_10a', 'Vintage Primary Paint')
	AddTextEntry('tfbison_grille_11a', 'Vintage Secondary Paint')
	AddTextEntry('tfbison_grille_12a', 'Duneloader')
	AddTextEntry('tfbison_grille_13a', 'Mesh')
	AddTextEntry('tfbison2_grille_1b', 'Chrome')
	AddTextEntry('tfbison_grille_1b', 'Plastic')
	AddTextEntry('tfbison_grille_2b', 'Dark Chrome')
	AddTextEntry('tfbison_grille_3b', 'Gloss Black')
	AddTextEntry('tfbison_grille_4b', 'Primary Paint')
	AddTextEntry('tfbison_grille_5b', 'Secondary Paint')
	AddTextEntry('tfbison_roof_1', 'Covered Roof')
	AddTextEntry('tfbison2_mirror_1a', 'Primary')
	AddTextEntry('tfbison_mirror_1a', 'Secondary')
	AddTextEntry('tfbison_mirror_2a', 'Chrome')
	AddTextEntry('tfbison_mirror_3a', 'Dark Chrome')
	AddTextEntry('tfbison_mirror_4a', 'Plastic')
	AddTextEntry('tfbison_mirror_5a', 'Primary Paint Match')
	AddTextEntry('tfbison_mirror_6a', 'Secondary Paint Match')
	AddTextEntry('tfbison_mirror_7a', 'Chrome Paint Match')
	AddTextEntry('tfbison_mirror_8a', 'Dark Chrome Paint Match')
	AddTextEntry('tfbison_mirror_9a', 'Plastic Paint Match')
	AddTextEntry('tfbison_trimdsidef1', 'Chrome Trim')
	AddTextEntry('tfbison_trimdsidef2', 'Dark Chrome Trim')
	AddTextEntry('tfbison_bonnetaccs_1a', 'Bullhorns')
	AddTextEntry('tfbison_bonnetaccs_2a', 'Bison')
	AddTextEntry('tfbison_rooflite_1', 'Lightbar')
	AddTextEntry('tfbison_rooflite_2', 'Clearance Lights')
	AddTextEntry('tfbison_rooflite_3', 'White Clearance Lights')
	AddTextEntry('tfbison_boot_1a', 'Primary Bison')
	AddTextEntry('tfbison_boot_2a', 'Secondary Bison')
	AddTextEntry('tfbison_boot_3a', 'Chrome Bison')
	AddTextEntry('tfbison_boot_4a', 'Black Bison')
	AddTextEntry('tfbison_runbrd_1a', 'Stock Short')
	AddTextEntry('tfbison_runbrd_2a', 'Lux Board')
	AddTextEntry('tfbison_runbrd_3a', 'Heavy Duty Long')
	AddTextEntry('tfbison_runbrd_4a', 'Chrome Tube')
	AddTextEntry('tfbison_runbrd_4b', 'Black Tube')
	AddTextEntry('tfbison_runbrd_5a', 'Primary Offroad')
	AddTextEntry('tfbison_runbrd_5b', 'Secondary Offroad')
	AddTextEntry('tfbison_hndldsidef1', 'Secondary Handles')
	AddTextEntry('tfbison_hndldsidef2', 'Plastic Handles')
	AddTextEntry('tfbison_hndldsidef3', 'Chrome Handles')
	AddTextEntry('tfbison_hndldsidef4', 'Dark Chrome Handles')
	AddTextEntry('tfbison_flaps_1a', 'Mudflaps')
	AddTextEntry('tfbison_flaps_3a', 'Bison Mudflaps')
	AddTextEntry('tfbison_fenders_1a', 'Primary Fender Flares')
	AddTextEntry('tfbison_fenders_1ab', 'Primary Fender Flares w/ Mudflaps')
	AddTextEntry('tfbison_fenders_2a', 'Secondary Fender Flares')
	AddTextEntry('tfbison_fenders_2ab', 'Secondary Fender Flares w/ Mudflaps')
	AddTextEntry('tfbison_fenders_3a', 'Plastic Fender Flares')
	AddTextEntry('tfbison_fenders_3ab', 'Plastic Fender Flares w/ Mudflaps')
	AddTextEntry('tfbison_livery_1', 'Black Stripes')
	AddTextEntry('tfbison_livery_2', 'White Stripes')
	AddTextEntry('tfbison_livery_3', 'Black Gradient')
	AddTextEntry('tfbison_livery_4', 'Vintage')
	AddTextEntry('tfbison_livery_5', 'Duneloader')
	AddTextEntry('tfbison_livery_6', 'Kabel Manufacturing')
	AddTextEntry('tfbison_livery_7', 'The Mighty Bush')
	AddTextEntry('tfbison_livery_8', 'SanTrak')
	AddTextEntry('tfbison_livery_9', 'McGill-Olsen')
	AddTextEntry('tfbison_livery_10', 'YouTool')
	AddTextEntry('tfbison_livery_11', 'Schlott Construction')
	AddTextEntry('tfbison_livery_12', 'Shopping List')
	AddTextEntry('tfbison_livery_13', 'Grapseseed Oneil Ranch')

	-- Lady Bird 6STR
	AddTextEntryByHash(0x025F295B, 'Winter Rack')
	AddTextEntryByHash(0x1C40297C, 'Ladybird')
	AddTextEntryByHash(0x3DFE2094, 'Roof Rack')
	AddTextEntryByHash(0x4F368F15, 'Secondary Paint')
	AddTextEntryByHash(0x6B2146EA, 'Indicators Delete')
	AddTextEntryByHash(0x26C46FA8, 'Headlight Brows Delete')
	AddTextEntryByHash(0x39D75457, 'Double Arch Delete')
	AddTextEntryByHash(0x74FC44CC, 'Primary Paint')
	AddTextEntryByHash(0x86D5687E, 'Secondary Paint')
	AddTextEntryByHash(0x187A5314, 'De-badge')
	AddTextEntryByHash(0x236E21B9, 'Reverse Light Delete')
	AddTextEntryByHash(0x272E72F9, 'Travel Rack')
	AddTextEntryByHash(0x590B8CEB, 'Remove Bumper')
	AddTextEntryByHash(0x702F8D11, 'Ladybird Livery')
	AddTextEntryByHash(0x845E7964, 'Primary Paint')
	AddTextEntryByHash(0x5499CB4E, 'De-chrome')
	AddTextEntryByHash(0x5978A399, 'Remove Bumper')
	AddTextEntryByHash(0x54564D40, 'Summer Rack')
	AddTextEntryByHash(0x57538F57, 'Front Arch Delete')
	AddTextEntryByHash(0xB31C12E9, 'Rusty Herbert Livery')
	AddTextEntryByHash(0xC4E1B674, 'Herbert Livery')
	AddTextEntryByHash(0xD792DBD6, 'Mothership Livery')
	AddTextEntryByHash(0xE1FCF0AA, 'Rat Livery')

	-- Ruiner 6STR
	AddTextEntryByHash(0x0EE89242, 'Pacific Skirt')
	AddTextEntryByHash(0x1F99F526, 'Big Bore Exhaust')
	AddTextEntryByHash(0x3AC2E9F6, 'Drift Skirt')
	AddTextEntryByHash(0x3E5E8EBB, 'Sport Bumper')
	AddTextEntryByHash(0x4C9C4F2E, 'FatNappy Exhaust')
	AddTextEntryByHash(0x4F421A41, 'FatNappy Carbon Diffuser')
	AddTextEntryByHash(0x5ADDEBB1, 'Backyard Racecar Exhaust')
	AddTextEntryByHash(0x5E24B802, 'FatNappy Colored Diffuser')
	AddTextEntryByHash(0x6EE713BF, 'Tuner Exhaust')
	AddTextEntryByHash(0x7C982F21, 'Titanium Tuner Exhaust')
	AddTextEntryByHash(0x49A4A547, 'Lip Splitter')
	AddTextEntryByHash(0x51F31F9F, 'Pacific Bumper')
	AddTextEntryByHash(0x2187D50A, 'Drift Missile FatNappy')
	AddTextEntryByHash(0x194647FE, 'Chassis Mount Wing')
	AddTextEntryByHash(0x325676A7, 'FatNappy Kit and Splitter')
	AddTextEntryByHash(0xA5CBC753, 'FatNappy Bumper Delete')
	AddTextEntryByHash(0xA78B050A, 'Bozo Titanium Exhaust')
	AddTextEntryByHash(0xB023F240, 'FatNappy Kit')
	AddTextEntryByHash(0xB4A8650C, 'FatNappy Delete Carbon')
	AddTextEntryByHash(0xB5D9A1A7, 'Bozo Chrome Exhaust')
	AddTextEntryByHash(0xBE650EC6, 'Drift Bumper')
	AddTextEntryByHash(0xBE680D00, 'Clean Tail')
	AddTextEntryByHash(0xC3B01CD7, 'Oppressed Garage Wing')
	AddTextEntryByHash(0xC3C1A112, 'Vented Pacific Fenders')
	AddTextEntryByHash(0xCA514A96, 'Chrome Tuner Exhaust')
	AddTextEntryByHash(0xCCB6AB69, 'Pacific Bumper')
	AddTextEntryByHash(0xD83B666A, 'Bozo Exhaust')
	AddTextEntryByHash(0xD248BA08, 'FatNappy Ducktail')
	AddTextEntryByHash(0xDB00C7FD, 'Clean Cut Bumper')
	AddTextEntryByHash(0xE087E534, '6STR Ruiner Custom')
	AddTextEntryByHash(0xEEEE7353, 'SEND IT Wing')
	AddTextEntryByHash(0xF0281643, 'Big Bore Titanium Exhaust')
	AddTextEntryByHash(0xFE22B238, 'Big Bore Chrome Exhaust')

end)