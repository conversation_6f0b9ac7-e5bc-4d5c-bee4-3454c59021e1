<?xml version="1.0" encoding="UTF-8"?>
<CVehicleModelInfo__InitDataList>
  <residentTxd>vehshare</residentTxd>
  <residentAnims/>
  <InitDatas>
    <Item>
      <modelName>argento</modelName>
      <txdName>argento</txdName>
      <handlingId>ARGENTO</handlingId>
      <gameName>ARGENTO</gameName>
      <vehicleMakeName>OBEY</vehicleMakeName>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected/>
      <ptfxAssetName>null</ptfxAssetName>
      <audioNameHash>rhinehart</audioNameHash>
      <layout>LAYOUT_STANDARD</layout>
      <coverBoundOffsets>TAILGATER_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout/>
      <cameraName>DEFAULT_FOLLOW_VEHICLE_CAMERA</cameraName>
      <aimCameraName>DEFAULT_THIRD_PERSON_VEHICLE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>VEHICLE_BONNET_CAMERA_MID</bonnetCameraName>
      <povCameraName>DEFAULT_POV_CAMERA</povCameraName>
      <FirstPersonDriveByIKOffset x="0.000000" y="-0.040000" z="-0.040000"/>
      <FirstPersonDriveByUnarmedIKOffset x="0.000000" y="-0.025000" z="0.000000"/>
      <FirstPersonProjectileDriveByIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonProjectileDriveByPassengerIKOffset x="0.020000" y="0.060000" z="-0.090000"/>
      <FirstPersonProjectileDriveByRearLeftIKOffset x="0.000000" y="0.020000" z="0.010000"/>
      <FirstPersonProjectileDriveByRearRightIKOffset x="0.000000" y="0.020000" z="0.010000"/>
      <FirstPersonDriveByLeftPassengerIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonDriveByRightPassengerIKOffset x="0.000000" y="-0.040000" z="0.000000"/>
      <FirstPersonDriveByRightRearPassengerIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonDriveByLeftPassengerUnarmedIKOffset x="0.000000" y="-0.030000" z="0.020000"/>
      <FirstPersonDriveByRightPassengerUnarmedIKOffset x="0.000000" y="-0.030000" z="0.020000"/>
      <FirstPersonMobilePhoneOffset x="0.140000" y="0.285000" z="0.525000"/>
      <FirstPersonPassengerMobilePhoneOffset x="0.135000" y="0.145000" z="0.435000"/>
      <FirstPersonMobilePhoneSeatIKOffset>
        <Item>
          <Offset x="0.136000" y="0.126000" z="0.455000"/>
          <SeatIndex value="2"/>
        </Item>
        <Item>
          <Offset x="0.136000" y="0.126000" z="0.455000"/>
          <SeatIndex value="3"/>
        </Item>
      </FirstPersonMobilePhoneSeatIKOffset>
      <PovCameraOffset x="0.000000" y="-0.175000" z="0.662500"/>
      <PovCameraVerticalAdjustmentForRollCage value="0.000000"/>
      <PovPassengerCameraOffset x="0.000000" y="0.000000" z="-0.015000"/>
      <PovRearPassengerCameraOffset x="0.000000" y="0.000000" z="-0.015000"/>
      <vfxInfoName>VFXVEHICLEINFO_CAR_GENERIC</vfxInfoName>
      <shouldUseCinematicViewMode value="true"/>
      <shouldCameraTransitionOnClimbUpDown value="false"/>
      <shouldCameraIgnoreExiting value="false"/>
      <AllowPretendOccupants value="true"/>
      <AllowJoyriding value="true"/>
      <AllowSundayDriving value="true"/>
      <AllowBodyColorMapping value="true"/>
      <wheelScale value="0.311351"/>
      <wheelScaleRear value="0.314419"/>
      <dirtLevelMin value="0.000000"/>
      <dirtLevelMax value="0.300000"/>
      <envEffScaleMin value="0.000000"/>
      <envEffScaleMax value="1.000000"/>
      <envEffScaleMin2 value="0.000000"/>
      <envEffScaleMax2 value="1.000000"/>
      <damageMapScale value="0.600000"/>
      <damageOffsetScale value="0.800000"/>
      <diffuseTint value="0x00FFFFFF"/>
      <steerWheelMult value="0.700000"/>
      <HDTextureDist value="5.000000"/>
      <lodDistances content="float_array">
        20.000000
        40.000000
        80.000000
        120.000000
        500.000000
        500.000000
      </lodDistances>
      <minSeatHeight value="0.862"/>
      <identicalModelSpawnDistance value="20"/>
      <maxNumOfSameColor value="1"/>
      <defaultBodyHealth value="1000.000000"/>
      <pretendOccupantsScale value="1.000000"/>
      <visibleSpawnDistScale value="1.000000"/>
      <trackerPathWidth value="2.000000"/>
      <weaponForceMult value="1.000000"/>
      <frequency value="20"/>
      <swankness>SWANKNESS_4</swankness>
      <maxNum value="3"/>
      <flags>FLAG_SPORTS FLAG_PARKING_SENSORS FLAG_RICH_CAR FLAG_NO_BROKEN_DOWN_SCENARIO FLAG_RECESSED_HEADLIGHT_CORONAS FLAG_RECESSED_TAILLIGHT_CORONAS FLAG_CAN_HAVE_NEONS</flags>
      <type>VEHICLE_TYPE_CAR</type>
      <plateType>VPT_BACK_PLATES</plateType>
      <dashboardType>VDT_RACE</dashboardType>
      <vehicleClass>VC_SPORT</vehicleClass>
      <wheelType>VWT_SPORT</wheelType>
      <trailers/>
      <additionalTrailers/>
      <drivers/>
      <extraIncludes/>
      <doorsWithCollisionWhenClosed/>
      <driveableDoors/>
      <bumpersNeedToCollideWithMap value="false"/>
      <needsRopeTexture value="false"/>
      <requiredExtras/>
      <rewards/>
      <cinematicPartCamera>
        <Item>WHEEL_FRONT_RIGHT_CAMERA</Item>
        <Item>WHEEL_FRONT_LEFT_CAMERA</Item>
        <Item>WHEEL_REAR_RIGHT_CAMERA</Item>
        <Item>WHEEL_REAR_LEFT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet/>
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000"/>
      <buoyancySphereSizeScale value="1.000000"/>
      <pOverrideRagdollThreshold type="NULL"/>
      <maxSteeringWheelAngle value="109.0"/>
      <firstPersonDrivebyData>
        <Item>STD_TAILGATER_FRONT_LEFT</Item>
        <Item>STD_FRONT_RIGHT</Item>
        <Item>STD_TAILGATER_REAR_LEFT</Item>
        <Item>STD_TAILGATER_REAR_RIGHT</Item>
      </firstPersonDrivebyData>
    </Item>
    <Item>
      <modelName>asteropers</modelName>
      <txdName>asteropers</txdName>
      <handlingId>ASTEROPERS</handlingId>
      <gameName>ASTEROPERS</gameName>
      <vehicleMakeName>KARIN</vehicleMakeName>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected/>
      <ptfxAssetName>weap_xs_vehicle_weapons</ptfxAssetName>
      <audioNameHash>sultan3</audioNameHash>
      <layout>LAYOUT_STANDARD_BUFFALO4</layout>
      <coverBoundOffsets>ASTEROPE_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout/>
      <cameraName>DEFAULT_FOLLOW_VEHICLE_CAMERA</cameraName>
      <aimCameraName>DEFAULT_THIRD_PERSON_VEHICLE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>VEHICLE_BONNET_CAMERA_MID</bonnetCameraName>
      <povCameraName>DEFAULT_POV_CAMERA</povCameraName>
      <FirstPersonDriveByIKOffset x="0.000000" y="-0.085000" z="-0.055000"/>
      <FirstPersonDriveByUnarmedIKOffset x="0.000000" y="0.000000" z="-0.020000"/>
      <FirstPersonProjectileDriveByIKOffset x="0.050000" y="-0.120000" z="-0.080000"/>
      <FirstPersonProjectileDriveByPassengerIKOffset x="-0.060000" y="-0.100000" z="-0.05000"/>
      <FirstPersonProjectileDriveByRearLeftIKOffset x="0.000000" y="-0.070000" z="-0.080000"/>
      <FirstPersonProjectileDriveByRearRightIKOffset x="-0.050000" y="-0.070000" z="-0.060000"/>
      <FirstPersonDriveByLeftPassengerIKOffset x="0.070000" y="-0.080000" z="-0.010000"/>
      <FirstPersonDriveByRightPassengerIKOffset x="-0.060000" y="-0.090000" z="-0.040000"/>
      <FirstPersonDriveByRightRearPassengerIKOffset x="-0.090000" y="-0.060000" z="0.000000"/>
      <FirstPersonDriveByLeftPassengerUnarmedIKOffset x="0.000000" y="0.040000" z="0.000000"/>
      <FirstPersonDriveByRightPassengerUnarmedIKOffset x="0.000000" y="-0.020000" z="0.000000"/>
      <FirstPersonMobilePhoneOffset x="0.135000" y="0.240000" z="0.570000"/>
      <FirstPersonPassengerMobilePhoneOffset x="0.196000" y="0.203000" z="0.435000"/>
      <FirstPersonMobilePhoneSeatIKOffset>
        <Item>
          <Offset x="0.136000" y="0.156000" z="0.455000"/>
          <SeatIndex value="2"/>
        </Item>
        <Item>
          <Offset x="0.136000" y="0.156000" z="0.455000"/>
          <SeatIndex value="3"/>
        </Item>
      </FirstPersonMobilePhoneSeatIKOffset>
      <PovCameraOffset x="0.000000" y="-0.175000" z="0.680000"/>
      <PovCameraVerticalAdjustmentForRollCage value="-0.025000"/>
      <PovPassengerCameraOffset x="0.000000" y="-0.005000" z="-0.050000"/>
      <PovRearPassengerCameraOffset x="0.018000" y="-0.045000" z="0.000000"/>
      <vfxInfoName>VFXVEHICLEINFO_CAR_GENERIC</vfxInfoName>
      <shouldUseCinematicViewMode value="true"/>
      <shouldCameraTransitionOnClimbUpDown value="false"/>
      <shouldCameraIgnoreExiting value="false"/>
      <AllowPretendOccupants value="true"/>
      <AllowJoyriding value="true"/>
      <AllowSundayDriving value="true"/>
      <AllowBodyColorMapping value="true"/>
      <wheelScale value="0.270442"/>
      <wheelScaleRear value="0.270442"/>
      <dirtLevelMin value="0.000000"/>
      <dirtLevelMax value="0.300000"/>
      <envEffScaleMin value="0.000000"/>
      <envEffScaleMax value="1.000000"/>
      <envEffScaleMin2 value="0.000000"/>
      <envEffScaleMax2 value="1.000000"/>
      <damageMapScale value="0.600000"/>
      <damageOffsetScale value="1.000000"/>
      <diffuseTint value="0x00FFFFFF"/>
      <steerWheelMult value="1.000000"/>
      <HDTextureDist value="5.000000"/>
      <lodDistances content="float_array">
        10.000000
        25.000000
        60.000000	
        120.000000	
        500.000000	
        500.000000
      </lodDistances>
      <minSeatHeight value="0.854"/>
      <identicalModelSpawnDistance value="20"/>
      <maxNumOfSameColor value="1"/>
      <defaultBodyHealth value="1000.000000"/>
      <pretendOccupantsScale value="1.000000"/>
      <visibleSpawnDistScale value="1.000000"/>
      <trackerPathWidth value="2.000000"/>
      <weaponForceMult value="1.000000"/>
      <frequency value="20"/>
      <swankness>SWANKNESS_2</swankness>
      <maxNum value="3"/>
      <flags>FLAG_AVERAGE_CAR FLAG_PARKING_SENSORS FLAG_NO_BROKEN_DOWN_SCENARIO FLAG_RECESSED_HEADLIGHT_CORONAS FLAG_RECESSED_TAILLIGHT_CORONAS FLAG_HAS_NITROUS_MOD FLAG_HAS_SUPER_BRAKES_MOD FLAG_SPOILER_MOD_DOESNT_INCREASE_GRIP FLAG_INCREASE_CAMBER_WITH_SUSPENSION_MOD FLAG_CAN_HAVE_NEONS</flags>
      <type>VEHICLE_TYPE_CAR</type>
      <plateType>VPT_BACK_PLATES</plateType>
      <dashboardType>VDT_RACE</dashboardType>
      <vehicleClass>VC_SPORT</vehicleClass>
      <wheelType>VWT_SPORT</wheelType>
      <trailers/>
      <additionalTrailers/>
      <drivers/>
      <extraIncludes/>
      <doorsWithCollisionWhenClosed/>
      <driveableDoors/>
      <bumpersNeedToCollideWithMap value="false"/>
      <needsRopeTexture value="false"/>
      <requiredExtras/>
      <rewards/>
      <cinematicPartCamera>
        <Item>WHEEL_FRONT_RIGHT_CAMERA</Item>
        <Item>WHEEL_FRONT_LEFT_CAMERA</Item>
        <Item>WHEEL_REAR_RIGHT_CAMERA</Item>
        <Item>WHEEL_REAR_LEFT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet/>
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000"/>
      <buoyancySphereSizeScale value="1.000000"/>
      <pOverrideRagdollThreshold type="NULL"/>
      <firstPersonDrivebyData>
        <Item>STANDARD_BUFFALO4_FRONT_LEFT</Item>
        <Item>STANDARD_BUFFALO4_FRONT_RIGHT</Item>
        <Item>STANDARD_BUFFALO4_REAR_LEFT</Item>
        <Item>STANDARD_BUFFALO4_REAR_RIGHT</Item>
      </firstPersonDrivebyData>
    </Item>
    <Item>
      <modelName>domc</modelName>
      <txdName>domc</txdName>
      <handlingId>DOMC</handlingId>
      <gameName>DOMC</gameName>
      <vehicleMakeName>VAPID</vehicleMakeName>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected/>
      <ptfxAssetName>null</ptfxAssetName>
      <audioNameHash>MAMBA</audioNameHash>
      <layout>LAYOUT_STANDARD</layout>
      <coverBoundOffsets>DOMINATOR_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout/>
      <cameraName>DEFAULT_FOLLOW_VEHICLE_CAMERA</cameraName>
      <aimCameraName>DEFAULT_THIRD_PERSON_VEHICLE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>VEHICLE_BONNET_CAMERA_STANDARD_LONG</bonnetCameraName>
      <povCameraName>DEFAULT_POV_CAMERA_LOOKAROUND_MID</povCameraName>
      <FirstPersonDriveByIKOffset x="0.000000" y="-0.055000" z="0.000000"/>
      <FirstPersonDriveByUnarmedIKOffset x="0.000000" y="-0.025000" z="0.000000"/>
      <FirstPersonProjectileDriveByIKOffset x="0.000000" y="-0.120000" z="0.030000"/>
      <FirstPersonProjectileDriveByPassengerIKOffset x="0.000000" y="0.030000" z="-0.040000"/>
      <FirstPersonProjectileDriveByRearLeftIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonProjectileDriveByRearRightIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonDriveByLeftPassengerIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonDriveByRightPassengerIKOffset x="0.000000" y="-0.055000" z="0.020000"/>
      <FirstPersonDriveByRightRearPassengerIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonDriveByLeftPassengerUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonDriveByRightPassengerUnarmedIKOffset x="0.000000" y="-0.035000" z="0.000000"/>
      <FirstPersonMobilePhoneOffset x="0.133000" y="0.228000" z="0.468000"/>
      <FirstPersonPassengerMobilePhoneOffset x="0.136000" y="0.223000" z="0.425000"/>
      <PovCameraOffset x="0.000000" y="-0.145000" z="0.590000"/>
      <PovCameraVerticalAdjustmentForRollCage value="0.000000"/>
      <PovPassengerCameraOffset x="0.000000" y="0.000000" z="0.070000"/>
      <PovRearPassengerCameraOffset x="0.000000" y="0.000000" z="0.070000"/>
      <vfxInfoName>VFXVEHICLEINFO_CAR_GENERIC</vfxInfoName>
      <shouldUseCinematicViewMode value="true"/>
      <shouldCameraTransitionOnClimbUpDown value="false"/>
      <shouldCameraIgnoreExiting value="false"/>
      <AllowPretendOccupants value="true"/>
      <AllowJoyriding value="true"/>
      <AllowSundayDriving value="true"/>
      <AllowBodyColorMapping value="true"/>
      <wheelScale value="0.279400"/>
      <wheelScaleRear value="0.279400"/>
      <dirtLevelMin value="0.000000"/>
      <dirtLevelMax value="0.700000"/>
      <envEffScaleMin value="0.000000"/>
      <envEffScaleMax value="1.000000"/>
      <envEffScaleMin2 value="0.000000"/>
      <envEffScaleMax2 value="1.000000"/>
      <damageMapScale value="0.600000"/>
      <damageOffsetScale value="1.000000"/>
      <diffuseTint value="0x00FFFFFF"/>
      <steerWheelMult value="1.000000"/>
      <HDTextureDist value="5.000000"/>
      <lodDistances content="float_array">
        20.000000
        40.000000
        60.000000
        120.000000
        500.000000
        500.000000
      </lodDistances>
      <minSeatHeight value="0.85"/>
      <identicalModelSpawnDistance value="20"/>
      <maxNumOfSameColor value="1"/>
      <defaultBodyHealth value="1000.000000"/>
      <pretendOccupantsScale value="1.000000"/>
      <visibleSpawnDistScale value="1.000000"/>
      <trackerPathWidth value="2.000000"/>
      <weaponForceMult value="1.000000"/>
      <frequency value="90"/>
      <swankness>SWANKNESS_2</swankness>
      <maxNum value="18"/>
      <flags>FLAG_AVERAGE_CAR FLAG_HAS_LIVERY FLAG_RECESSED_HEADLIGHT_CORONAS FLAG_HAS_INTERIOR_EXTRAS</flags>
      <type>VEHICLE_TYPE_CAR</type>
      <plateType>VPT_FRONT_AND_BACK_PLATES</plateType>
      <dashboardType>VDT_DUKES</dashboardType>
      <vehicleClass>VC_MUSCLE</vehicleClass>
      <wheelType>VWT_MUSCLE</wheelType>
      <trailers/>
      <additionalTrailers/>
      <drivers/>
      <extraIncludes/>
      <doorsWithCollisionWhenClosed/>
      <driveableDoors/>
      <bumpersNeedToCollideWithMap value="false"/>
      <needsRopeTexture value="false"/>
      <requiredExtras/>
      <rewards/>
      <cinematicPartCamera>
        <Item>WHEEL_FRONT_RIGHT_CAMERA</Item>
        <Item>WHEEL_FRONT_LEFT_CAMERA</Item>
        <Item>WHEEL_REAR_RIGHT_CAMERA</Item>
        <Item>WHEEL_REAR_LEFT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet/>
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000"/>
      <buoyancySphereSizeScale value="1.000000"/>
      <pOverrideRagdollThreshold type="NULL"/>
      <firstPersonDrivebyData>
        <Item>STD_DOMINATOR_FRONT_LEFT</Item>
        <Item>STD_DOMINATOR_FRONT_RIGHT</Item>
      </firstPersonDrivebyData>
      <numSeatsOverride value="2"/>
    </Item>
    <Item>
      <modelName>doubled</modelName>
      <txdName>doubled</txdName>
      <handlingId>DOUBLED</handlingId>
      <gameName>DOUBLED</gameName>
      <vehicleMakeName>obey</vehicleMakeName>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected/>
      <ptfxAssetName>weap_xs_vehicle_weapons</ptfxAssetName>
      <audioNameHash>jackal</audioNameHash>
      <layout>LAYOUT_LOW</layout>
      <coverBoundOffsets>NINEF_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout/>
      <cameraName>DEFAULT_FOLLOW_VEHICLE_CAMERA</cameraName>
      <aimCameraName>DEFAULT_THIRD_PERSON_VEHICLE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>VEHICLE_BONNET_CAMERA_MID_NEAR</bonnetCameraName>
      <povCameraName>REDUCED_NEAR_CLIP_POV_CAMERA</povCameraName>
      <FirstPersonDriveByIKOffset x="0.000000" y="-0.055000" z="-0.020000"/>
      <FirstPersonDriveByUnarmedIKOffset x="0.000000" y="-0.100000" z="0.000000"/>
      <FirstPersonProjectileDriveByIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonProjectileDriveByPassengerIKOffset x="0.000000" y="-0.045000" z="0.015000"/>
      <FirstPersonDriveByLeftPassengerIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonDriveByRightPassengerIKOffset x="0.000000" y="-0.055000" z="-0.020000"/>
      <FirstPersonDriveByLeftPassengerUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonDriveByRightPassengerUnarmedIKOffset x="0.000000" y="-0.100000" z="0.000000"/>
      <FirstPersonMobilePhoneOffset x="0.130000" y="0.211000" z="0.580000"/>
      <FirstPersonPassengerMobilePhoneOffset x="0.136000" y="0.156000" z="0.463000"/>
      <PovCameraOffset x="0.000000" y="-0.200000" z="0.695000"/>
      <PovCameraVerticalAdjustmentForRollCage value="0.000000"/>
      <PovPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000"/>
      <vfxInfoName>VFXVEHICLEINFO_CAR_GENERIC</vfxInfoName>
      <shouldUseCinematicViewMode value="true"/>
      <shouldCameraTransitionOnClimbUpDown value="false"/>
      <shouldCameraIgnoreExiting value="false"/>
      <AllowPretendOccupants value="true"/>
      <AllowJoyriding value="true"/>
      <AllowSundayDriving value="true"/>
      <AllowBodyColorMapping value="true"/>
      <wheelScale value="0.268000"/>
      <wheelScaleRear value="0.268000"/>
      <dirtLevelMin value="0.000000"/>
      <dirtLevelMax value="0.450000"/>
      <envEffScaleMin value="0.000000"/>
      <envEffScaleMax value="1.000000"/>
      <envEffScaleMin2 value="0.000000"/>
      <envEffScaleMax2 value="1.000000"/>
      <damageMapScale value="0.600000"/>
      <damageOffsetScale value="1.000000"/>
      <diffuseTint value="0x00FFFFFF"/>
      <steerWheelMult value="1.000000"/>
      <HDTextureDist value="5.000000"/>
      <lodDistances content="float_array">
        10.000000	
        25.000000	
        60.000000	
        120.000000	
        500.000000	
        500.000000
      </lodDistances>
      <minSeatHeight value="0.812"/>
      <identicalModelSpawnDistance value="20"/>
      <maxNumOfSameColor value="1"/>
      <defaultBodyHealth value="1000.000000"/>
      <pretendOccupantsScale value="1.000000"/>
      <visibleSpawnDistScale value="1.000000"/>
      <trackerPathWidth value="2.000000"/>
      <weaponForceMult value="1.000000"/>
      <frequency value="30"/>
      <swankness>SWANKNESS_4</swankness>
      <maxNum value="2"/>
      <flags>FLAG_SPORTS FLAG_HAS_NITROUS_MOD FLAG_SPAWN_ON_TRAILER FLAG_RICH_CAR FLAG_NO_BROKEN_DOWN_SCENARIO FLAG_HAS_INTERIOR_EXTRAS</flags>
      <type>VEHICLE_TYPE_CAR</type>
      <plateType>VPT_BACK_PLATES</plateType>
      <dashboardType>VDT_RACE</dashboardType>
      <vehicleClass>VC_SPORT</vehicleClass>
      <wheelType>VWT_HIEND</wheelType>
      <trailers/>
      <additionalTrailers/>
      <drivers/>
      <extraIncludes/>
      <doorsWithCollisionWhenClosed/>
      <driveableDoors/>
      <bumpersNeedToCollideWithMap value="false"/>
      <needsRopeTexture value="false"/>
      <requiredExtras/>
      <rewards/>
      <cinematicPartCamera>
        <Item>WHEEL_FRONT_RIGHT_CAMERA</Item>
        <Item>WHEEL_FRONT_LEFT_CAMERA</Item>
        <Item>WHEEL_REAR_RIGHT_CAMERA</Item>
        <Item>WHEEL_REAR_LEFT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet/>
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000"/>
      <buoyancySphereSizeScale value="1.000000"/>
      <pOverrideRagdollThreshold type="NULL"/>
      <firstPersonDrivebyData>
        <Item>LOW_RAPIDGT_FRONT_LEFT</Item>
        <Item>LOW_RAPIDGT_FRONT_RIGHT</Item>
      </firstPersonDrivebyData>
    </Item>
    <Item>
      <modelName>dubsta22</modelName>
      <txdName>dubsta22</txdName>
      <handlingId>DUBSTA22</handlingId>
      <gameName>DUBSTA22</gameName>
      <vehicleMakeName>BENEFAC</vehicleMakeName>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected/>
      <ptfxAssetName>null</ptfxAssetName>
      <audioNameHash>schlagen</audioNameHash>
      <layout>LAYOUT_4X4</layout>
      <coverBoundOffsets>DUBSTA_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout/>
      <cameraName>DEFAULT_FOLLOW_VEHICLE_CAMERA</cameraName>
      <aimCameraName>DEFAULT_THIRD_PERSON_VEHICLE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>VEHICLE_BONNET_CAMERA_LOW_LOW</bonnetCameraName>
      <povCameraName>DEFAULT_POV_CAMERA_LOOKAROUND</povCameraName>
      <FirstPersonDriveByIKOffset x="-0.018000" y="-0.060000" z="-0.025000"/>
      <FirstPersonDriveByUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonProjectileDriveByIKOffset x="0.080000" y="0.000000" z="-0.060000"/>
      <FirstPersonProjectileDriveByPassengerIKOffset x="0.000000" y="0.040000" z="-0.090000"/>
      <FirstPersonProjectileDriveByRearLeftIKOffset x="0.000000" y="0.000000" z="-0.058000"/>
      <FirstPersonProjectileDriveByRearRightIKOffset x="0.000000" y="0.000000" z="-0.058000"/>
      <FirstPersonDriveByLeftPassengerIKOffset x="0.000000" y="0.010000" z="-0.053000"/>
      <FirstPersonDriveByRightPassengerIKOffset x="0.000000" y="0.000000" z="-0.040000"/>
      <FirstPersonDriveByRightRearPassengerIKOffset x="0.000000" y="0.010000" z="-0.053000"/>
      <FirstPersonDriveByLeftPassengerUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonDriveByRightPassengerUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonMobilePhoneOffset x="0.150000" y="0.248000" z="0.494000"/>
      <FirstPersonPassengerMobilePhoneOffset x="0.174000" y="0.169000" z="0.4350000"/>
      <FirstPersonMobilePhoneSeatIKOffset>
        <Item>
          <Offset x="0.174000" y="0.059000" z="0.435000"/>
          <SeatIndex value="2"/>
        </Item>
        <Item>
          <Offset x="0.174000" y="0.059000" z="0.435000"/>
          <SeatIndex value="3"/>
        </Item>
      </FirstPersonMobilePhoneSeatIKOffset>
      <PovCameraOffset x="0.000000" y="-0.120000" z="0.610000"/>
      <PovCameraVerticalAdjustmentForRollCage value="0.000000"/>
      <PovPassengerCameraOffset x="0.000000" y="0.000000" z="0.020000"/>
      <PovRearPassengerCameraOffset x="0.000000" y="-0.180000" z="0.070000"/>
      <vfxInfoName>VFXVEHICLEINFO_CAR_GENERIC</vfxInfoName>
      <shouldUseCinematicViewMode value="true"/>
      <shouldCameraTransitionOnClimbUpDown value="false"/>
      <shouldCameraIgnoreExiting value="false"/>
      <AllowPretendOccupants value="true"/>
      <AllowJoyriding value="true"/>
      <AllowSundayDriving value="true"/>
      <AllowBodyColorMapping value="true"/>
      <wheelScale value="0.298800"/>
      <wheelScaleRear value="0.298800"/>
      <dirtLevelMin value="0.000000"/>
      <dirtLevelMax value="0.450000"/>
      <envEffScaleMin value="0.000000"/>
      <envEffScaleMax value="1.000000"/>
      <envEffScaleMin2 value="0.000000"/>
      <envEffScaleMax2 value="1.000000"/>
      <damageMapScale value="0.400000"/>
      <damageOffsetScale value="0.800000"/>
      <diffuseTint value="0xAA0A0A0A"/>
      <steerWheelMult value="1.000000"/>
      <HDTextureDist value="5.000000"/>
      <lodDistances content="float_array">
        15.000000
        30.000000
        60.000000
        120.000000
        500.000000
        500.000000
      </lodDistances>
      <minSeatHeight value="0.967"/>
      <identicalModelSpawnDistance value="20"/>
      <maxNumOfSameColor value="2"/>
      <defaultBodyHealth value="1000.000000"/>
      <pretendOccupantsScale value="1.000000"/>
      <visibleSpawnDistScale value="1.000000"/>
      <trackerPathWidth value="2.000000"/>
      <weaponForceMult value="1.000000"/>
      <frequency value="20"/>
      <swankness>SWANKNESS_4</swankness>
      <maxNum value="5"/>
      <flags>FLAG_PARKING_SENSORS FLAG_RICH_CAR FLAG_IS_OFFROAD_VEHICLE FLAG_IS_BULKY FLAG_RECESSED_HEADLIGHT_CORONAS FLAG_HAS_INTERIOR_EXTRAS</flags>
      <type>VEHICLE_TYPE_CAR</type>
      <plateType>VPT_BACK_PLATES</plateType>
      <dashboardType>VDT_RACE</dashboardType>
      <vehicleClass>VC_SUV</vehicleClass>
      <wheelType>VWT_SUV</wheelType>
      <trailers/>
      <additionalTrailers/>
      <drivers/>
      <extraIncludes/>
      <doorsWithCollisionWhenClosed/>
      <driveableDoors/>
      <bumpersNeedToCollideWithMap value="false"/>
      <needsRopeTexture value="false"/>
      <requiredExtras/>
      <rewards/>
      <cinematicPartCamera>
        <Item>WHEEL_FRONT_RIGHT_CAMERA</Item>
        <Item>WHEEL_FRONT_LEFT_CAMERA</Item>
        <Item>WHEEL_REAR_RIGHT_CAMERA</Item>
        <Item>WHEEL_REAR_LEFT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet>Truck</NmBraceOverrideSet>
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000"/>
      <buoyancySphereSizeScale value="1.000000"/>
      <pOverrideRagdollThreshold type="NULL"/>
      <firstPersonDrivebyData>
        <Item>RANGER_DUBSTA_FRONT_LEFT</Item>
        <Item>VAN_SPEEDO_FRONT_RIGHT</Item>
        <Item>RANGER_DUBSTA_REAR_LEFT</Item>
        <Item>RANGER_DUBSTA_REAR_RIGHT</Item>
      </firstPersonDrivebyData>
    </Item>
    <Item>
      <modelName>ferocid</modelName>
      <txdName>ferocid</txdName>
      <handlingId>FEROCID</handlingId>
      <gameName>FEROCID</gameName>
      <vehicleMakeName>BRAVADO</vehicleMakeName>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected/>
      <ptfxAssetName>null</ptfxAssetName>
      <audioNameHash>SULTAN</audioNameHash>
      <layout>LAYOUT_STD_STRATUM</layout>
      <coverBoundOffsets>STRATUM_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout/>
      <cameraName>DEFAULT_FOLLOW_VEHICLE_CAMERA</cameraName>
      <aimCameraName>MID_BOX_VEHICLE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>VEHICLE_BONNET_CAMERA_MID_EXTRA_HIGH</bonnetCameraName>
      <povCameraName>DEFAULT_POV_CAMERA</povCameraName>
      <FirstPersonDriveByIKOffset x="0.000000" y="-0.060000" z="-0.010000"/>
      <FirstPersonDriveByUnarmedIKOffset x="0.000000" y="-0.025000" z="0.000000"/>
      <FirstPersonProjectileDriveByIKOffset x="0.070000" y="-0.080000" z="-0.035000"/>
      <FirstPersonProjectileDriveByPassengerIKOffset x="-0.070000" y="-0.080000" z="-0.035000"/>
      <FirstPersonProjectileDriveByRearLeftIKOffset x="0.000000" y="0.020000" z="-0.010000"/>
      <FirstPersonProjectileDriveByRearRightIKOffset x="0.000000" y="0.020000" z="-0.010000"/>
      <FirstPersonDriveByLeftPassengerIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonDriveByRightPassengerIKOffset x="0.000000" y="-0.060000" z="-0.010000"/>
      <FirstPersonDriveByRightRearPassengerIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonDriveByLeftPassengerUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonDriveByRightPassengerUnarmedIKOffset x="0.000000" y="-0.100000" z="0.000000"/>
      <FirstPersonMobilePhoneOffset x="0.113000" y="0.168000" z="0.515000"/>
      <FirstPersonPassengerMobilePhoneOffset x="0.135000" y="0.095000" z="0.425000"/>
      <FirstPersonMobilePhoneSeatIKOffset>
        <Item>
          <Offset x="0.136000" y="0.066000" z="0.415000"/>
          <SeatIndex value="2"/>
        </Item>
        <Item>
          <Offset x="0.136000" y="0.066000" z="0.415000"/>
          <SeatIndex value="3"/>
        </Item>
      </FirstPersonMobilePhoneSeatIKOffset>
      <PovCameraOffset x="0.000000" y="-0.270000" z="0.625000"/>
      <PovCameraVerticalAdjustmentForRollCage value="0.000000"/>
      <PovPassengerCameraOffset x="0.000000" y="0.025000" z="0.000000"/>
      <PovRearPassengerCameraOffset x="0.000000" y="-0.025000" z="0.000000"/>
      <vfxInfoName>VFXVEHICLEINFO_CAR_GENERIC</vfxInfoName>
      <shouldUseCinematicViewMode value="true"/>
      <shouldCameraTransitionOnClimbUpDown value="false"/>
      <shouldCameraIgnoreExiting value="false"/>
      <AllowPretendOccupants value="true"/>
      <AllowJoyriding value="true"/>
      <AllowSundayDriving value="true"/>
      <AllowBodyColorMapping value="true"/>
      <wheelScale value="0.260200"/>
      <wheelScaleRear value="0.260200"/>
      <dirtLevelMin value="0.000000"/>
      <dirtLevelMax value="0.850000"/>
      <envEffScaleMin value="0.000000"/>
      <envEffScaleMax value="1.000000"/>
      <envEffScaleMin2 value="0.000000"/>
      <envEffScaleMax2 value="1.000000"/>
      <damageMapScale value="0.600000"/>
      <damageOffsetScale value="1.000000"/>
      <diffuseTint value="0x00FFFFFF"/>
      <steerWheelMult value="1.000000"/>
      <HDTextureDist value="5.000000"/>
      <lodDistances content="float_array">
        15.000000
        30.000000
        60.000000
        120.000000
        500.000000
        500.000000
      </lodDistances>
      <minSeatHeight value="0.787"/>
      <identicalModelSpawnDistance value="10"/>
      <maxNumOfSameColor value="10"/>
      <defaultBodyHealth value="1000.000000"/>
      <pretendOccupantsScale value="1.000000"/>
      <visibleSpawnDistScale value="1.000000"/>
      <trackerPathWidth value="2.000000"/>
      <weaponForceMult value="1.000000"/>
      <frequency value="70"/>
      <swankness>SWANKNESS_1</swankness>
      <maxNum value="100"/>
      <flags>FLAG_AVERAGE_CAR FLAG_EXTRAS_ALL FLAG_HAS_LIVERY</flags>
      <type>VEHICLE_TYPE_CAR</type>
      <plateType>VPT_FRONT_AND_BACK_PLATES</plateType>
      <dashboardType>VDT_DUKES</dashboardType>
      <vehicleClass>VC_SEDAN</vehicleClass>
      <wheelType>VWT_TUNER</wheelType>
      <trailers/>
      <additionalTrailers/>
      <drivers/>
      <extraIncludes>/
      </extraIncludes>
      <doorsWithCollisionWhenClosed/>
      <driveableDoors/>
      <bumpersNeedToCollideWithMap value="true"/>
      <needsRopeTexture value="false"/>
      <requiredExtras/>
      <rewards/>
      <cinematicPartCamera>
        <Item>WHEEL_FRONT_RIGHT_CAMERA</Item>
        <Item>WHEEL_FRONT_LEFT_CAMERA</Item>
        <Item>WHEEL_REAR_RIGHT_CAMERA</Item>
        <Item>WHEEL_REAR_LEFT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet/>
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000"/>
      <buoyancySphereSizeScale value="1.000000"/>
      <pOverrideRagdollThreshold type="NULL"/>
      <firstPersonDrivebyData>
        <Item>STD_STRATUM_FRONT_LEFT</Item>
        <Item>STD_PREMIER_FRONT_RIGHT</Item>
        <Item>STD_STRATUM_REAR_LEFT</Item>
        <Item>STD_STRATUM_REAR_RIGHT</Item>
      </firstPersonDrivebyData>
    </Item>
    <Item>
      <modelName>gauntletac</modelName>
      <txdName>gauntletac</txdName>
      <handlingId>GAUNTLETAC</handlingId>
      <gameName>GAUNTLETAC</gameName>
      <vehicleMakeName>BRAVADO</vehicleMakeName>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected/>
      <ptfxAssetName>null</ptfxAssetName>
      <audioNameHash>GAUNTLET</audioNameHash>
      <layout>LAYOUT_STANDARD</layout>
      <coverBoundOffsets>GAUNTLET4_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout/>
      <cameraName>FOLLOW_CHEETAH_CAMERA</cameraName>
      <aimCameraName>DEFAULT_THIRD_PERSON_VEHICLE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>DEFAULT_VEHICLE_BONNET_CAMERA</bonnetCameraName>
      <povCameraName>REDUCED_NEAR_CLIP_POV_CAMERA</povCameraName>
      <FirstPersonDriveByIKOffset x="0.050000" y="-0.060000" z="-0.035000"/>
      <FirstPersonDriveByUnarmedIKOffset x="-0.050000" y="-0.080000" z="-0.020000"/>
      <FirstPersonProjectileDriveByIKOffset x="0.000000" y="-0.140000" z="-0.050000"/>
      <FirstPersonProjectileDriveByPassengerIKOffset x="0.000000" y="-0.140000" z="-0.050000"/>
      <FirstPersonDriveByRightPassengerIKOffset x="-0.050000" y="-0.060000" z="-0.035000"/>
      <FirstPersonDriveByRightPassengerUnarmedIKOffset x="-0.010000" y="-0.100000" z="-0.020000"/>
      <FirstPersonMobilePhoneOffset x="0.145000" y="0.203000" z="0.560000"/>
      <FirstPersonPassengerMobilePhoneOffset x="0.216000" y="0.148000" z="0.450000"/>
      <PovCameraOffset x="0.000000" y="-0.230000" z="0.700000"/>
      <PovCameraVerticalAdjustmentForRollCage value="0.000000"/>
      <PovPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000"/>
      <vfxInfoName>VFXVEHICLEINFO_CAR_GENERIC</vfxInfoName>
      <shouldUseCinematicViewMode value="true"/>
      <shouldCameraTransitionOnClimbUpDown value="false"/>
      <shouldCameraIgnoreExiting value="false"/>
      <AllowPretendOccupants value="true"/>
      <AllowJoyriding value="true"/>
      <AllowSundayDriving value="true"/>
      <AllowBodyColorMapping value="true"/>
      <wheelScale value="0.231000"/>
      <wheelScaleRear value="0.231000"/>
      <dirtLevelMin value="0.000000"/>
      <dirtLevelMax value="0.450000"/>
      <envEffScaleMin value="0.000000"/>
      <envEffScaleMax value="1.000000"/>
      <envEffScaleMin2 value="0.000000"/>
      <envEffScaleMax2 value="1.000000"/>
      <damageMapScale value="0.40000"/>
      <damageOffsetScale value="0.40000"/>
      <diffuseTint value="0x00FFFFFF"/>
      <steerWheelMult value="0.700000"/>
      <HDTextureDist value="5.000000"/>
      <lodDistances content="float_array">
        15.000000
        30.000000
        60.000000	
        120.000000
        500.000000
        500.000000
      </lodDistances>
      <minSeatHeight value="0.844"/>
      <identicalModelSpawnDistance value="20"/>
      <maxNumOfSameColor value="1"/>
      <defaultBodyHealth value="1000.000000"/>
      <pretendOccupantsScale value="1.000000"/>
      <visibleSpawnDistScale value="1.000000"/>
      <trackerPathWidth value="2.000000"/>
      <weaponForceMult value="1.000000"/>
      <frequency value="10"/>
      <swankness>SWANKNESS_2</swankness>
      <maxNum value="5"/>
      <flags>FLAG_HAS_LIVERY FLAG_EXTRAS_STRONG FLAG_AVERAGE_CAR FLAG_HAS_INTERIOR_EXTRAS FLAG_CAN_HAVE_NEONS FLAG_HAS_SUPERCHARGER</flags>
      <type>VEHICLE_TYPE_CAR</type>
      <plateType>VPT_BACK_PLATES</plateType>
      <dashboardType>VDT_RACE</dashboardType>
      <vehicleClass>VC_MUSCLE</vehicleClass>
      <wheelType>VWT_MUSCLE</wheelType>
      <trailers/>
      <additionalTrailers/>
      <drivers/>
      <extraIncludes/>
      <doorsWithCollisionWhenClosed/>
      <driveableDoors/>
      <bumpersNeedToCollideWithMap value="false"/>
      <needsRopeTexture value="false"/>
      <requiredExtras/>
      <rewards/>
      <cinematicPartCamera>
        <Item>WHEEL_FRONT_RIGHT_CAMERA</Item>
        <Item>WHEEL_FRONT_LEFT_CAMERA</Item>
        <Item>WHEEL_REAR_RIGHT_CAMERA</Item>
        <Item>WHEEL_REAR_LEFT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet/>
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000"/>
      <buoyancySphereSizeScale value="1.000000"/>
      <pOverrideRagdollThreshold type="NULL"/>
      <firstPersonDrivebyData>
        <Item>STD_GAUNTLET4_FRONT_LEFT</Item>
        <Item>STD_GAUNTLET4_FRONT_RIGHT</Item>
      </firstPersonDrivebyData>
      <numSeatsOverride value="2"/>
    </Item>
    <Item>
      <modelName>gauntletv6</modelName>
      <txdName>gauntletv6</txdName>
      <handlingId>GAUNTLETV6</handlingId>
      <gameName>GAUNTLETV6</gameName>
      <vehicleMakeName>BRAVADO</vehicleMakeName>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected/>
      <ptfxAssetName>null</ptfxAssetName>
      <audioNameHash>buffalo</audioNameHash>
      <layout>LAYOUT_STANDARD</layout>
      <coverBoundOffsets>GAUNTLET4_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout/>
      <cameraName>FOLLOW_CHEETAH_CAMERA</cameraName>
      <aimCameraName>DEFAULT_THIRD_PERSON_VEHICLE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>DEFAULT_VEHICLE_BONNET_CAMERA</bonnetCameraName>
      <povCameraName>REDUCED_NEAR_CLIP_POV_CAMERA</povCameraName>
      <FirstPersonDriveByIKOffset x="0.050000" y="-0.060000" z="-0.035000"/>
      <FirstPersonDriveByUnarmedIKOffset x="-0.050000" y="-0.080000" z="-0.020000"/>
      <FirstPersonProjectileDriveByIKOffset x="0.000000" y="-0.140000" z="-0.050000"/>
      <FirstPersonProjectileDriveByPassengerIKOffset x="0.000000" y="-0.140000" z="-0.050000"/>
      <FirstPersonDriveByRightPassengerIKOffset x="-0.050000" y="-0.060000" z="-0.035000"/>
      <FirstPersonDriveByRightPassengerUnarmedIKOffset x="-0.010000" y="-0.100000" z="-0.020000"/>
      <FirstPersonMobilePhoneOffset x="0.145000" y="0.203000" z="0.560000"/>
      <FirstPersonPassengerMobilePhoneOffset x="0.216000" y="0.148000" z="0.450000"/>
      <PovCameraOffset x="0.000000" y="-0.230000" z="0.700000"/>
      <PovCameraVerticalAdjustmentForRollCage value="0.000000"/>
      <PovPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000"/>
      <vfxInfoName>VFXVEHICLEINFO_CAR_GENERIC</vfxInfoName>
      <shouldUseCinematicViewMode value="true"/>
      <shouldCameraTransitionOnClimbUpDown value="false"/>
      <shouldCameraIgnoreExiting value="false"/>
      <AllowPretendOccupants value="true"/>
      <AllowJoyriding value="true"/>
      <AllowSundayDriving value="true"/>
      <AllowBodyColorMapping value="true"/>
      <wheelScale value="0.231000"/>
      <wheelScaleRear value="0.231000"/>
      <dirtLevelMin value="0.000000"/>
      <dirtLevelMax value="0.450000"/>
      <envEffScaleMin value="0.000000"/>
      <envEffScaleMax value="1.000000"/>
      <envEffScaleMin2 value="0.000000"/>
      <envEffScaleMax2 value="1.000000"/>
      <damageMapScale value="0.40000"/>
      <damageOffsetScale value="0.40000"/>
      <diffuseTint value="0x00FFFFFF"/>
      <steerWheelMult value="0.700000"/>
      <HDTextureDist value="5.000000"/>
      <lodDistances content="float_array">
        15.000000
        30.000000
        60.000000	
        120.000000
        500.000000
        500.000000
      </lodDistances>
      <minSeatHeight value="0.844"/>
      <identicalModelSpawnDistance value="20"/>
      <maxNumOfSameColor value="1"/>
      <defaultBodyHealth value="1000.000000"/>
      <pretendOccupantsScale value="1.000000"/>
      <visibleSpawnDistScale value="1.000000"/>
      <trackerPathWidth value="2.000000"/>
      <weaponForceMult value="1.000000"/>
      <frequency value="10"/>
      <swankness>SWANKNESS_1</swankness>
      <maxNum value="5"/>
      <flags>FLAG_EXTRAS_STRONG FLAG_AVERAGE_CAR FLAG_HAS_INTERIOR_EXTRAS FLAG_CAN_HAVE_NEONS FLAG_HAS_SUPERCHARGER</flags>
      <type>VEHICLE_TYPE_CAR</type>
      <plateType>VPT_BACK_PLATES</plateType>
      <dashboardType>VDT_RACE</dashboardType>
      <vehicleClass>VC_COUPE</vehicleClass>
      <wheelType>VWT_MUSCLE</wheelType>
      <trailers/>
      <additionalTrailers/>
      <drivers/>
      <extraIncludes/>
      <doorsWithCollisionWhenClosed/>
      <driveableDoors/>
      <bumpersNeedToCollideWithMap value="false"/>
      <needsRopeTexture value="false"/>
      <requiredExtras/>
      <rewards/>
      <cinematicPartCamera>
        <Item>WHEEL_FRONT_RIGHT_CAMERA</Item>
        <Item>WHEEL_FRONT_LEFT_CAMERA</Item>
        <Item>WHEEL_REAR_RIGHT_CAMERA</Item>
        <Item>WHEEL_REAR_LEFT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet/>
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000"/>
      <buoyancySphereSizeScale value="1.000000"/>
      <pOverrideRagdollThreshold type="NULL"/>
      <firstPersonDrivebyData>
        <Item>STD_GAUNTLET4_FRONT_LEFT</Item>
        <Item>STD_GAUNTLET4_FRONT_RIGHT</Item>
      </firstPersonDrivebyData>
      <numSeatsOverride value="2"/>
    </Item>
    <Item>
      <modelName>gstyosemite1</modelName>
      <txdName>gstyosemite1</txdName>
      <handlingId>gstyosemite1</handlingId>
      <gameName>gstyosete1</gameName>
      <vehicleMakeName>DECLASSE</vehicleMakeName>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected/>
      <ptfxAssetName>null</ptfxAssetName>
      <audioNameHash>imperator</audioNameHash>
      <layout>LAYOUT_STD_TROPHY</layout>
      <coverBoundOffsets>SENTINEL3_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout/>
      <cameraName>DEFAULT_FOLLOW_VEHICLE_CAMERA</cameraName>
      <aimCameraName>MID_BOX_VEHICLE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>VEHICLE_BONNET_CAMERA_LOW_LOW</bonnetCameraName>
      <povCameraName>DEFAULT_POV_CAMERA</povCameraName>
      <FirstPersonDriveByIKOffset x="0.015000" y="-0.016000" z="-0.073000"/>
      <FirstPersonDriveByUnarmedIKOffset x="0.000000" y="0.040000" z="-0.005000"/>
      <FirstPersonProjectileDriveByIKOffset x="0.000000" y="-0.100000" z="0.000000"/>
      <FirstPersonProjectileDriveByPassengerIKOffset x="0.000000" y="-0.100000" z="0.000000"/>
      <FirstPersonProjectileDriveByRearLeftIKOffset x="0.050000" y="0.000000" z="-0.030000"/>
      <FirstPersonProjectileDriveByRearRightIKOffset x="0.050000" y="0.000000" z="-0.030000"/>
      <FirstPersonDriveByLeftPassengerIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonDriveByRightPassengerIKOffset x="0.015000" y="-0.036000" z="-0.073000"/>
      <FirstPersonDriveByRightRearPassengerIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonDriveByLeftPassengerUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonDriveByRightPassengerUnarmedIKOffset x="0.000000" y="-0.060000" z="-0.010000"/>
      <FirstPersonMobilePhoneOffset x="0.130000" y="0.152000" z="0.525000"/>
      <FirstPersonPassengerMobilePhoneOffset x="0.210000" y="0.070000" z="0.420000"/>
      <PovCameraOffset x="0.000000" y="-0.230000" z="0.670000"/>
      <PovCameraVerticalAdjustmentForRollCage value="0.000000"/>
      <PovPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000"/>
      <PovRearPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000"/>
      <vfxInfoName>VFXVEHICLEINFO_CAR_GENERIC</vfxInfoName>
      <shouldUseCinematicViewMode value="true"/>
      <shouldCameraTransitionOnClimbUpDown value="false"/>
      <shouldCameraIgnoreExiting value="false"/>
      <AllowPretendOccupants value="true"/>
      <AllowJoyriding value="true"/>
      <AllowSundayDriving value="true"/>
      <AllowBodyColorMapping value="true"/>
      <wheelScale value="0.248000"/>
      <wheelScaleRear value="0.248000"/>
      <dirtLevelMin value="0.000000"/>
      <dirtLevelMax value="0.450000"/>
      <envEffScaleMin value="0.000000"/>
      <envEffScaleMax value="1.000000"/>
      <envEffScaleMin2 value="0.000000"/>
      <envEffScaleMax2 value="1.000000"/>
      <damageMapScale value="0.350000"/>
      <damageOffsetScale value="0.350000"/>
      <diffuseTint value="0x00FFFFFF"/>
      <steerWheelMult value="1.000000"/>
      <HDTextureDist value="5.000000"/>
      <lodDistances content="float_array">
        40.000000
        65.000000
        90.000000	
        120.000000
        500.000000
        500.000000
      </lodDistances>
      <minSeatHeight value="0.844"/>
      <identicalModelSpawnDistance value="20"/>
      <maxNumOfSameColor value="1"/>
      <defaultBodyHealth value="1000.000000"/>
      <pretendOccupantsScale value="1.000000"/>
      <visibleSpawnDistScale value="1.000000"/>
      <trackerPathWidth value="2.000000"/>
      <weaponForceMult value="1.000000"/>
      <frequency value="30"/>
      <swankness>SWANKNESS_4</swankness>
      <maxNum value="10"/>
      <flags>FLAG_NO_BOOT FLAG_EXTRAS_ALL FLAG_EXTRAS_STRONG FLAG_AVERAGE_CAR FLAG_POOR_CAR FLAG_IS_OFFROAD_VEHICLE FLAG_HEADLIGHTS_USE_ACTUAL_BONE_POS FLAG_IS_BULKY</flags>
      <type>VEHICLE_TYPE_CAR</type>
      <dashboardType>VDT_RACE</dashboardType>
      <plateType>VPT_FRONT_AND_BACK_PLATES</plateType>
      <vehicleClass>VC_OFF_ROAD</vehicleClass>
      <wheelType>VWT_OFFROAD</wheelType>
      <trailers>
        <Item>boattrailer</Item>
        <Item>trailersmall</Item>
      </trailers>
      <additionalTrailers/>
      <drivers/>
      <extraIncludes/>
      <doorsWithCollisionWhenClosed/>
      <driveableDoors/>
      <bumpersNeedToCollideWithMap value="false"/>
      <needsRopeTexture value="false"/>
      <requiredExtras/>
      <rewards/>
      <cinematicPartCamera>
        <Item>WHEEL_FRONT_RIGHT_CAMERA</Item>
        <Item>WHEEL_FRONT_LEFT_CAMERA</Item>
        <Item>WHEEL_REAR_RIGHT_CAMERA</Item>
        <Item>WHEEL_REAR_LEFT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet/>
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000"/>
      <buoyancySphereSizeScale value="1.000000"/>
      <pOverrideRagdollThreshold type="NULL"/>
      <firstPersonDrivebyData>
        <Item>STD_SENTINEL3_FRONT_LEFT</Item>
        <Item>STD_SENTINEL3_FRONT_RIGHT</Item>
      </firstPersonDrivebyData>
      <numSeatsOverride value="2"/>
    </Item>
    <Item>
      <modelName>howitzer</modelName>
      <txdName>howitzer</txdName>
      <handlingId>HOWITZER</handlingId>
      <gameName>HOWITZER</gameName>
      <vehicleMakeName>BRAVADO</vehicleMakeName>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected/>
      <ptfxAssetName>null</ptfxAssetName>
      <audioNameHash>gauntlet4</audioNameHash>
      <layout>LAYOUT_STANDARD</layout>
      <coverBoundOffsets>BUFFALO_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout/>
      <cameraName>DEFAULT_FOLLOW_VEHICLE_CAMERA</cameraName>
      <aimCameraName>DEFAULT_THIRD_PERSON_VEHICLE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>VEHICLE_BONNET_CAMERA_STANDARD</bonnetCameraName>
      <povCameraName>DEFAULT_POV_CAMERA</povCameraName>
      <FirstPersonDriveByIKOffset x="0.000000" y="-0.055000" z="0.000000"/>
      <FirstPersonDriveByUnarmedIKOffset x="0.000000" y="0.000000" z="0.-020000"/>
      <FirstPersonProjectileDriveByIKOffset x="0.000000" y="-0.065000" z="0.030000"/>
      <FirstPersonProjectileDriveByPassengerIKOffset x="0.020000" y="0.060000" z="-0.090000"/>
      <FirstPersonProjectileDriveByRearLeftIKOffset x="0.000000" y="0.020000" z="0.000000"/>
      <FirstPersonProjectileDriveByRearRightIKOffset x="0.000000" y="0.020000" z="-0.025000"/>
      <FirstPersonDriveByLeftPassengerIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonDriveByRightPassengerIKOffset x="0.000000" y="-0.055000" z="0.000000"/>
      <FirstPersonDriveByRightRearPassengerIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonDriveByLeftPassengerUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonDriveByRightPassengerUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonMobilePhoneOffset x="0.135000" y="0.240000" z="0.550000"/>
      <FirstPersonPassengerMobilePhoneOffset x="0.136000" y="0.223000" z="0.425000"/>
      <FirstPersonMobilePhoneSeatIKOffset>
        <Item>
          <Offset x="0.136000" y="0.156000" z="0.435000"/>
          <SeatIndex value="2"/>
        </Item>
        <Item>
          <Offset x="0.136000" y="0.156000" z="0.435000"/>
          <SeatIndex value="3"/>
        </Item>
      </FirstPersonMobilePhoneSeatIKOffset>
      <PovCameraOffset x="0.000000" y="-0.130000" z="0.640000"/>
      <PovCameraVerticalAdjustmentForRollCage value="0.000000"/>
      <PovPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000"/>
      <PovRearPassengerCameraOffset x="0.000000" y="-0.050000" z="0.015000"/>
      <vfxInfoName>VFXVEHICLEINFO_CAR_GENERIC</vfxInfoName>
      <shouldUseCinematicViewMode value="true"/>
      <shouldCameraTransitionOnClimbUpDown value="false"/>
      <shouldCameraIgnoreExiting value="false"/>
      <AllowPretendOccupants value="true"/>
      <AllowJoyriding value="true"/>
      <AllowSundayDriving value="true"/>
      <AllowBodyColorMapping value="true"/>
      <wheelScale value="0.281385"/>
      <wheelScaleRear value="0.281385"/>
      <dirtLevelMin value="0.000000"/>
      <dirtLevelMax value="0.550000"/>
      <envEffScaleMin value="0.000000"/>
      <envEffScaleMax value="1.000000"/>
      <envEffScaleMin2 value="0.000000"/>
      <envEffScaleMax2 value="1.000000"/>
      <damageMapScale value="0.900000"/>
      <damageOffsetScale value="0.700000"/>
      <diffuseTint value="0x00FFFFFF"/>
      <steerWheelMult value="1.000000"/>
      <HDTextureDist value="5.000000"/>
      <lodDistances content="float_array">
        15.000000
        30.000000
        65.000000
        130.000000
        500.000000
        500.000000
      </lodDistances>
      <minSeatHeight value="0.868"/>
      <identicalModelSpawnDistance value="20"/>
      <maxNumOfSameColor value="10"/>
      <defaultBodyHealth value="1000.000000"/>
      <pretendOccupantsScale value="1.000000"/>
      <visibleSpawnDistScale value="1.000000"/>
      <trackerPathWidth value="2.000000"/>
      <weaponForceMult value="1.000000"/>
      <frequency value="100"/>
      <swankness>SWANKNESS_3</swankness>
      <maxNum value="999"/>
      <flags>FLAG_AVERAGE_CAR FLAG_RECESSED_HEADLIGHT_CORONAS</flags>
      <type>VEHICLE_TYPE_CAR</type>
      <plateType>VPT_BACK_PLATES</plateType>
      <dashboardType>VDT_GENTAXI</dashboardType>
      <vehicleClass>VC_SPORT</vehicleClass>
      <wheelType>VWT_SPORT</wheelType>
      <trailers/>
      <additionalTrailers/>
      <drivers/>
      <extraIncludes/>
      <doorsWithCollisionWhenClosed/>
      <driveableDoors/>
      <bumpersNeedToCollideWithMap value="false"/>
      <needsRopeTexture value="false"/>
      <requiredExtras/>
      <rewards/>
      <cinematicPartCamera>
        <Item>WHEEL_FRONT_RIGHT_CAMERA</Item>
        <Item>WHEEL_FRONT_LEFT_CAMERA</Item>
        <Item>WHEEL_REAR_RIGHT_CAMERA</Item>
        <Item>WHEEL_REAR_LEFT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet/>
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000"/>
      <buoyancySphereSizeScale value="1.000000"/>
      <pOverrideRagdollThreshold type="NULL"/>
      <firstPersonDrivebyData>
        <Item>STD_BUFFALO_FRONT_LEFT</Item>
        <Item>STD_BUFFALO_FRONT_RIGHT</Item>
        <Item>STD_BUFFALO_REAR_LEFT</Item>
        <Item>STD_BUFFALO_REAR_RIGHT</Item>
      </firstPersonDrivebyData>
    </Item>
    <Item>
      <modelName>howitzer2</modelName>
      <txdName>howitzer2</txdName>
      <handlingId>HOWITZER2</handlingId>
      <gameName>HOWITZER2</gameName>
      <vehicleMakeName>BRAVADO</vehicleMakeName>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected/>
      <ptfxAssetName>null</ptfxAssetName>
      <audioNameHash>imperator</audioNameHash>
      <layout>LAYOUT_STANDARD</layout>
      <coverBoundOffsets>BUFFALO_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout/>
      <cameraName>DEFAULT_FOLLOW_VEHICLE_CAMERA</cameraName>
      <aimCameraName>DEFAULT_THIRD_PERSON_VEHICLE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>VEHICLE_BONNET_CAMERA_STANDARD</bonnetCameraName>
      <povCameraName>DEFAULT_POV_CAMERA</povCameraName>
      <FirstPersonDriveByIKOffset x="0.000000" y="-0.055000" z="0.000000"/>
      <FirstPersonDriveByUnarmedIKOffset x="0.000000" y="0.000000" z="0.-020000"/>
      <FirstPersonProjectileDriveByIKOffset x="0.000000" y="-0.065000" z="0.030000"/>
      <FirstPersonProjectileDriveByPassengerIKOffset x="0.020000" y="0.060000" z="-0.090000"/>
      <FirstPersonProjectileDriveByRearLeftIKOffset x="0.000000" y="0.020000" z="0.000000"/>
      <FirstPersonProjectileDriveByRearRightIKOffset x="0.000000" y="0.020000" z="-0.025000"/>
      <FirstPersonDriveByLeftPassengerIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonDriveByRightPassengerIKOffset x="0.000000" y="-0.055000" z="0.000000"/>
      <FirstPersonDriveByRightRearPassengerIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonDriveByLeftPassengerUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonDriveByRightPassengerUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonMobilePhoneOffset x="0.135000" y="0.240000" z="0.550000"/>
      <FirstPersonPassengerMobilePhoneOffset x="0.136000" y="0.223000" z="0.425000"/>
      <FirstPersonMobilePhoneSeatIKOffset>
        <Item>
          <Offset x="0.136000" y="0.156000" z="0.435000"/>
          <SeatIndex value="2"/>
        </Item>
        <Item>
          <Offset x="0.136000" y="0.156000" z="0.435000"/>
          <SeatIndex value="3"/>
        </Item>
      </FirstPersonMobilePhoneSeatIKOffset>
      <PovCameraOffset x="0.000000" y="-0.130000" z="0.640000"/>
      <PovCameraVerticalAdjustmentForRollCage value="0.000000"/>
      <PovPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000"/>
      <PovRearPassengerCameraOffset x="0.000000" y="-0.050000" z="0.015000"/>
      <vfxInfoName>VFXVEHICLEINFO_CAR_GENERIC</vfxInfoName>
      <shouldUseCinematicViewMode value="true"/>
      <shouldCameraTransitionOnClimbUpDown value="false"/>
      <shouldCameraIgnoreExiting value="false"/>
      <AllowPretendOccupants value="true"/>
      <AllowJoyriding value="true"/>
      <AllowSundayDriving value="true"/>
      <AllowBodyColorMapping value="true"/>
      <wheelScale value="0.281385"/>
      <wheelScaleRear value="0.281385"/>
      <dirtLevelMin value="0.000000"/>
      <dirtLevelMax value="0.550000"/>
      <envEffScaleMin value="0.000000"/>
      <envEffScaleMax value="1.000000"/>
      <envEffScaleMin2 value="0.000000"/>
      <envEffScaleMax2 value="1.000000"/>
      <damageMapScale value="0.900000"/>
      <damageOffsetScale value="0.700000"/>
      <diffuseTint value="0x00FFFFFF"/>
      <steerWheelMult value="1.000000"/>
      <HDTextureDist value="5.000000"/>
      <lodDistances content="float_array">
        15.000000
        30.000000
        65.000000
        130.000000
        500.000000
        500.000000
      </lodDistances>
      <minSeatHeight value="0.868"/>
      <identicalModelSpawnDistance value="20"/>
      <maxNumOfSameColor value="10"/>
      <defaultBodyHealth value="1000.000000"/>
      <pretendOccupantsScale value="1.000000"/>
      <visibleSpawnDistScale value="1.000000"/>
      <trackerPathWidth value="2.000000"/>
      <weaponForceMult value="1.000000"/>
      <frequency value="100"/>
      <swankness>SWANKNESS_3</swankness>
      <maxNum value="999"/>
      <flags>FLAG_AVERAGE_CAR FLAG_RECESSED_HEADLIGHT_CORONAS</flags>
      <type>VEHICLE_TYPE_CAR</type>
      <plateType>VPT_BACK_PLATES</plateType>
      <dashboardType>VDT_GENTAXI</dashboardType>
      <vehicleClass>VC_SPORT</vehicleClass>
      <wheelType>VWT_SPORT</wheelType>
      <trailers/>
      <additionalTrailers/>
      <drivers/>
      <extraIncludes/>
      <doorsWithCollisionWhenClosed/>
      <driveableDoors/>
      <bumpersNeedToCollideWithMap value="false"/>
      <needsRopeTexture value="false"/>
      <requiredExtras/>
      <rewards/>
      <cinematicPartCamera>
        <Item>WHEEL_FRONT_RIGHT_CAMERA</Item>
        <Item>WHEEL_FRONT_LEFT_CAMERA</Item>
        <Item>WHEEL_REAR_RIGHT_CAMERA</Item>
        <Item>WHEEL_REAR_LEFT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet/>
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000"/>
      <buoyancySphereSizeScale value="1.000000"/>
      <pOverrideRagdollThreshold type="NULL"/>
      <firstPersonDrivebyData>
        <Item>STD_BUFFALO_FRONT_LEFT</Item>
        <Item>STD_BUFFALO_FRONT_RIGHT</Item>
        <Item>STD_BUFFALO_REAR_LEFT</Item>
        <Item>STD_BUFFALO_REAR_RIGHT</Item>
      </firstPersonDrivebyData>
    </Item>

    <Item>
      <modelName>BALLER7R</modelName>
      <txdName>BALLER7R</txdName>
      <handlingId>BALLER7R</handlingId>
      <gameName>BALLER7R</gameName>
      <vehicleMakeName>GALLIVAN</vehicleMakeName>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected/>
      <ptfxAssetName>null</ptfxAssetName>
      <audioNameHash>trophytruck</audioNameHash>
      <layout>LAYOUT_STD_HIGHWINDOW</layout>
      <coverBoundOffsets>BALLER7R_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout/>
      <cameraName>DEFAULT_FOLLOW_VEHICLE_CAMERA</cameraName>
      <aimCameraName>DEFAULT_THIRD_PERSON_VEHICLE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>VEHICLE_BONNET_CAMERA_STANDARD_LONG</bonnetCameraName>
      <povCameraName>DEFAULT_POV_CAMERA</povCameraName>
      <FirstPersonDriveByIKOffset x="0.000000" y="-0.040000" z="-0.040000"/>
      <FirstPersonDriveByUnarmedIKOffset x="0.000000" y="0.000000" z="-0.020000"/>
      <FirstPersonProjectileDriveByIKOffset x="0.030000" y="0.000000" z="0.000000"/>
      <FirstPersonProjectileDriveByPassengerIKOffset x="-0.030000" y="0.000000" z="0.000000"/>
      <FirstPersonProjectileDriveByRearLeftIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonProjectileDriveByRearRightIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonDriveByLeftPassengerIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonDriveByRightPassengerIKOffset x="0.000000" y="-0.040000" z="-0.040000"/>
      <FirstPersonDriveByRightRearPassengerIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonDriveByLeftPassengerUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonDriveByRightPassengerUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonMobilePhoneOffset x="0.135000" y="0.240000" z="0.520000"/>
      <FirstPersonPassengerMobilePhoneOffset x="0.136000" y="0.223000" z="0.425000"/>
      <FirstPersonMobilePhoneSeatIKOffset>
        <Item>
          <Offset x="0.136000" y="0.136000" z="0.415000"/>
          <SeatIndex value="2"/>
        </Item>
        <Item>
          <Offset x="0.136000" y="0.136000" z="0.415000"/>
          <SeatIndex value="3"/>
        </Item>
      </FirstPersonMobilePhoneSeatIKOffset>
      <PovCameraOffset x="0.000000" y="-0.165000" z="0.645000"/>
      <PovCameraVerticalAdjustmentForRollCage value="0.000000"/>
      <PovPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000"/>
      <PovRearPassengerCameraOffset x="0.000000" y="-0.025000" z="0.000000"/>
      <vfxInfoName>VFXVEHICLEINFO_CAR_GENERIC</vfxInfoName>
      <shouldUseCinematicViewMode value="true"/>
      <shouldCameraTransitionOnClimbUpDown value="false"/>
      <shouldCameraIgnoreExiting value="false"/>
      <AllowPretendOccupants value="true"/>
      <AllowJoyriding value="true"/>
      <AllowSundayDriving value="true"/>
      <AllowBodyColorMapping value="true"/>
      <wheelScale value="0.264500"/>
      <wheelScaleRear value="0.264500"/>
      <dirtLevelMin value="0.000000"/>
      <dirtLevelMax value="0.550000"/>
      <envEffScaleMin value="0.000000"/>
      <envEffScaleMax value="0.000000"/>
      <envEffScaleMin2 value="0.000000"/>
      <envEffScaleMax2 value="0.000000"/>
      <damageMapScale value="0.05000"/>
      <damageOffsetScale value="1.000000"/>
      <diffuseTint value="0x00FFFFFF"/>
      <steerWheelMult value="1.000000"/>
      <HDTextureDist value="5.000000"/>
      <lodDistances content="float_array">
        15.000000	
        30.000000
        70.000000	
        140.000000	
        350.000000	
        350.000000
      </lodDistances>
      <minSeatHeight value="0.952"/>
      <identicalModelSpawnDistance value="20"/>
      <maxNumOfSameColor value="10"/>
      <defaultBodyHealth value="1000.000000"/>
      <pretendOccupantsScale value="1.000000"/>
      <visibleSpawnDistScale value="1.000000"/>
      <trackerPathWidth value="2.000000"/>
      <weaponForceMult value="1.000000"/>
      <frequency value="100"/>
      <swankness>SWANKNESS_3</swankness>
      <maxNum value="999"/>
      <flags>FLAG_PARKING_SENSORS FLAG_RICH_CAR FLAG_IS_OFFROAD_VEHICLE FLAG_IS_BULKY FLAG_HAS_INTERIOR_EXTRAS FLAG_EXTRAS_ALL FLAG_EXTRAS_STRONG</flags>
      <type>VEHICLE_TYPE_CAR</type>
      <plateType>VPT_NONE</plateType>
      <dashboardType>VDT_RACE</dashboardType>
      <vehicleClass>VC_OFF_ROAD</vehicleClass>
      <wheelType>VWT_OFFROAD</wheelType>
      <trailers/>
      <additionalTrailers/>
      <drivers/>
      <extraIncludes/>
      <doorsWithCollisionWhenClosed/>
      <driveableDoors/>
      <bumpersNeedToCollideWithMap value="false"/>
      <needsRopeTexture value="false"/>
      <requiredExtras/>
      <rewards/>
      <cinematicPartCamera>
        <Item>WHEEL_FRONT_RIGHT_CAMERA</Item>
        <Item>WHEEL_FRONT_LEFT_CAMERA</Item>
        <Item>WHEEL_REAR_RIGHT_CAMERA</Item>
        <Item>WHEEL_REAR_LEFT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet>Truck</NmBraceOverrideSet>
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000"/>
      <buoyancySphereSizeScale value="1.000000"/>
      <pOverrideRagdollThreshold type="NULL"/>
      <firstPersonDrivebyData>
        <Item>STD_BALLER7_FRONT_LEFT</Item>
        <Item>STD_BALLER7_FRONT_RIGHT</Item>
      </firstPersonDrivebyData>
      <numSeatsOverride value="2"/>
    </Item>
    <Item>
      <modelName>kriegerc</modelName>
      <txdName>kriegerc</txdName>
      <handlingId>KRIEGERC</handlingId>
      <gameName>KRIEGERC</gameName>
      <vehicleMakeName>BENEFAC</vehicleMakeName>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected/>
      <ptfxAssetName>null</ptfxAssetName>
      <audioNameHash>felzter3</audioNameHash>
      <layout>LAYOUT_LOW_INFERNUS2</layout>
      <coverBoundOffsets>INFERNUS2_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout/>
      <cameraName>FOLLOW_CHEETAH_CAMERA</cameraName>
      <aimCameraName>DEFAULT_THIRD_PERSON_VEHICLE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>VEHICLE_BONNET_CAMERA_LOW_LOW</bonnetCameraName>
      <povCameraName>REDUCED_NEAR_CLIP_POV_CAMERA</povCameraName>
      <FirstPersonDriveByIKOffset x="0.000000" y="-0.080000" z="-0.030000"/>
      <FirstPersonDriveByUnarmedIKOffset x="0.000000" y="-0.120000" z="-0.020000"/>
      <FirstPersonProjectileDriveByIKOffset x="0.000000" y="-0.150000" z="-0.015000"/>
      <FirstPersonProjectileDriveByPassengerIKOffset x="-0.050000" y="-0.150000" z="-0.015000"/>
      <FirstPersonProjectileDriveByRearLeftIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonProjectileDriveByRearRightIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonDriveByLeftPassengerIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonDriveByRightPassengerIKOffset x="0.000000" y="-0.080000" z="-0.030000"/>
      <FirstPersonDriveByRightRearPassengerIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonDriveByLeftPassengerUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonDriveByRightPassengerUnarmedIKOffset x="0.000000" y="-0.120000" z="-0.020000"/>
      <FirstPersonMobilePhoneOffset x="0.140000" y="0.160000" z="0.495000000"/>
      <FirstPersonPassengerMobilePhoneOffset x="0.174000" y="0.109000" z="0.411000"/>
      <PovCameraOffset x="0.000000" y="-0.245000" z="0.610000"/>
      <PovCameraVerticalAdjustmentForRollCage value="0.000000"/>
      <PovPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000"/>
      <PovRearPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000"/>
      <vfxInfoName>VFXVEHICLEINFO_CAR_GENERIC</vfxInfoName>
      <shouldUseCinematicViewMode value="true"/>
      <shouldCameraTransitionOnClimbUpDown value="false"/>
      <shouldCameraIgnoreExiting value="false"/>
      <AllowPretendOccupants value="true"/>
      <AllowJoyriding value="true"/>
      <AllowSundayDriving value="true"/>
      <AllowBodyColorMapping value="true"/>
      <wheelScale value="0.245600"/>
      <wheelScaleRear value="0.245600"/>
      <dirtLevelMin value="0.000000"/>
      <dirtLevelMax value="0.700000"/>
      <envEffScaleMin value="0.000000"/>
      <envEffScaleMax value="1.000000"/>
      <envEffScaleMin2 value="0.000000"/>
      <envEffScaleMax2 value="1.000000"/>
      <damageMapScale value="0.300000"/>
      <damageOffsetScale value="0.300000"/>
      <diffuseTint value="0x00FFFFFF"/>
      <steerWheelMult value="1.000000"/>
      <HDTextureDist value="5.000000"/>
      <lodDistances content="float_array">
        20.000000
        40.000000
        80.000000
        120.000000
        500.000000
        500.000000
      </lodDistances>
      <minSeatHeight value="0.796"/>
      <identicalModelSpawnDistance value="80"/>
      <maxNumOfSameColor value="1"/>
      <defaultBodyHealth value="1000.000000"/>
      <pretendOccupantsScale value="1.000000"/>
      <visibleSpawnDistScale value="1.000000"/>
      <trackerPathWidth value="2.000000"/>
      <weaponForceMult value="1.000000"/>
      <frequency value="5"/>
      <swankness>SWANKNESS_5</swankness>
      <maxNum value="1"/>
      <flags>FLAG_BOOT_IN_FRONT FLAG_SPORTS FLAG_RICH_CAR FLAG_INCREASE_PED_COMMENTS FLAG_HAS_GULL_WING_DOORS FLAG_NO_BROKEN_DOWN_SCENARIO FLAG_RECESSED_HEADLIGHT_CORONAS</flags>
      <type>VEHICLE_TYPE_CAR</type>
      <plateType>VPT_BACK_PLATES</plateType>
      <dashboardType>VDT_DUKES</dashboardType>
      <vehicleClass>VC_SPORT_CLASSIC</vehicleClass>
      <wheelType>VWT_HIEND</wheelType>
      <trailers/>
      <additionalTrailers/>
      <drivers/>
      <extraIncludes/>
      <doorsWithCollisionWhenClosed/>
      <driveableDoors/>
      <bumpersNeedToCollideWithMap value="true"/>
      <needsRopeTexture value="false"/>
      <requiredExtras/>
      <rewards/>
      <cinematicPartCamera>
        <Item>WHEEL_FRONT_RIGHT_CAMERA</Item>
        <Item>WHEEL_FRONT_LEFT_CAMERA</Item>
        <Item>WHEEL_REAR_RIGHT_CAMERA</Item>
        <Item>WHEEL_REAR_LEFT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet/>
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000"/>
      <buoyancySphereSizeScale value="1.000000"/>
      <pOverrideRagdollThreshold type="NULL"/>
      <firstPersonDrivebyData>
        <Item>LOW_INFERNUS2_FRONT_LEFT</Item>
        <Item>LOW_INFERNUS2_FRONT_RIGHT</Item>
      </firstPersonDrivebyData>
    </Item>
    <Item>
      <modelName>mesar</modelName>
      <txdName>mesar</txdName>
      <handlingId>MESAR</handlingId>
      <gameName>MESA</gameName>
      <vehicleMakeName>CANIS</vehicleMakeName>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected/>
      <ptfxAssetName>null</ptfxAssetName>
      <audioNameHash>mesa3</audioNameHash>
      <layout>LAYOUT_4X4</layout>
      <coverBoundOffsets>MESA_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout/>
      <cameraName>FOLLOW_JEEP_CAMERA</cameraName>
      <aimCameraName>JEEP_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>VEHICLE_BONNET_CAMERA_MID_NEAR</bonnetCameraName>
      <povCameraName>DEFAULT_POV_CAMERA_LOOKAROUND_MID</povCameraName>
      <FirstPersonDriveByIKOffset x="-0.018000" y="-0.043000" z="-0.020000"/>
      <FirstPersonDriveByUnarmedIKOffset x="0.000000" y="0.000000" z="-0.020000"/>
      <FirstPersonProjectileDriveByIKOffset x="0.080000" y="0.000000" z="-0.060000"/>
      <FirstPersonProjectileDriveByPassengerIKOffset x="0.020000" y="0.060000" z="-0.090000"/>
      <FirstPersonProjectileDriveByRearLeftIKOffset x="0.000000" y="0.000000" z="-0.058000"/>
      <FirstPersonProjectileDriveByRearRightIKOffset x="0.000000" y="0.000000" z="-0.058000"/>
      <FirstPersonDriveByLeftPassengerIKOffset x="-0.030000" y="0.010000" z="-0.076000"/>
      <FirstPersonDriveByRightPassengerIKOffset x="0.000000" y="0.000000" z="-0.040000"/>
      <FirstPersonDriveByRightRearPassengerIKOffset x="0.015000" y="0.010000" z="-0.076000"/>
      <FirstPersonDriveByLeftPassengerUnarmedIKOffset x="0.000000" y="-0.150000" z="0.010000"/>
      <FirstPersonDriveByRightPassengerUnarmedIKOffset x="0.000000" y="-0.150000" z="0.010000"/>
      <FirstPersonMobilePhoneOffset x="0.150000" y="0.260000" z="0.503000"/>
      <FirstPersonPassengerMobilePhoneOffset x="0.136000" y="0.223000" z="0.445000"/>
      <FirstPersonMobilePhoneSeatIKOffset>
        <Item>
          <Offset x="0.154000" y="0.076000" z="0.478000"/>
          <SeatIndex value="2"/>
        </Item>
        <Item>
          <Offset x="0.154000" y="0.076000" z="0.478000"/>
          <SeatIndex value="3"/>
        </Item>
      </FirstPersonMobilePhoneSeatIKOffset>
      <PovCameraOffset x="0.000000" y="-0.120000" z="0.580000"/>
      <PovCameraVerticalAdjustmentForRollCage value="0.000000"/>
      <PovPassengerCameraOffset x="0.000000" y="0.010000" z="0.075000"/>
      <PovRearPassengerCameraOffset x="0.000000" y="-0.190000" z="0.145000"/>
      <vfxInfoName>VFXVEHICLEINFO_CAR_OFFROAD</vfxInfoName>
      <shouldUseCinematicViewMode value="true"/>
      <shouldCameraTransitionOnClimbUpDown value="false"/>
      <shouldCameraIgnoreExiting value="false"/>
      <AllowPretendOccupants value="true"/>
      <AllowJoyriding value="true"/>
      <AllowSundayDriving value="true"/>
      <AllowBodyColorMapping value="true"/>
      <wheelScale value="0.269000"/>
      <wheelScaleRear value="0.269000"/>
      <dirtLevelMin value="0.000000"/>
      <dirtLevelMax value="0.850000"/>
      <envEffScaleMin value="0.000000"/>
      <envEffScaleMax value="1.000000"/>
      <envEffScaleMin2 value="0.000000"/>
      <envEffScaleMax2 value="1.000000"/>
      <damageMapScale value="0.600000"/>
      <damageOffsetScale value="1.000000"/>
      <diffuseTint value="0xC4000000"/>
      <steerWheelMult value="1.000000"/>
      <HDTextureDist value="5.000000"/>
      <lodDistances content="float_array">
        15.000000
        30.000000
        70.000000	
        140.000000	
        500.000000	
        500.000000
      </lodDistances>
      <minSeatHeight value="0.846"/>
      <identicalModelSpawnDistance value="20"/>
      <maxNumOfSameColor value="10"/>
      <defaultBodyHealth value="1000.000000"/>
      <pretendOccupantsScale value="1.000000"/>
      <visibleSpawnDistScale value="1.000000"/>
      <trackerPathWidth value="2.000000"/>
      <weaponForceMult value="0.900000"/>
      <frequency value="100"/>
      <swankness>SWANKNESS_1</swankness>
      <maxNum value="1"/>
      <flags>FLAG_HAS_LIVERY FLAG_EXTRAS_STRONG FLAG_AVERAGE_CAR FLAG_IS_OFFROAD_VEHICLE FLAG_IS_BULKY FLAG_HAS_INTERIOR_EXTRAS</flags>
      <type>VEHICLE_TYPE_CAR</type>
      <plateType>VPT_FRONT_AND_BACK_PLATES</plateType>
      <vehicleClass>VC_OFF_ROAD</vehicleClass>
      <dashboardType>VDT_CAVALCADE</dashboardType>
      <wheelType>VWT_OFFROAD</wheelType>
      <trailers/>
      <additionalTrailers>
        <Item>boattrailer</Item>
        <Item>trailersmall</Item>
        <Item>trailersmall2</Item>
      </additionalTrailers>
      <drivers/>
      <extraIncludes/>
      <doorsWithCollisionWhenClosed/>
      <driveableDoors/>
      <bumpersNeedToCollideWithMap value="true"/>
      <needsRopeTexture value="false"/>
      <requiredExtras/>
      <rewards/>
      <cinematicPartCamera>
        <Item>WHEEL_FRONT_RIGHT_CAMERA</Item>
        <Item>WHEEL_FRONT_LEFT_CAMERA</Item>
        <Item>WHEEL_REAR_RIGHT_CAMERA</Item>
        <Item>WHEEL_REAR_LEFT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet>Truck</NmBraceOverrideSet>
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000"/>
      <buoyancySphereSizeScale value="1.000000"/>
      <pOverrideRagdollThreshold type="NULL"/>
      <firstPersonDrivebyData>
        <Item>RANGER_MESA_FRONT_LEFT</Item>
        <Item>VAN_SPEEDO_FRONT_RIGHT</Item>
        <Item>RANGER_CRUSADER_REAR_LEFT</Item>
        <Item>RANGER_CRUSADER_REAR_RIGHT</Item>
      </firstPersonDrivebyData>
    </Item>
    <Item>
      <modelName>nebulaw</modelName>
      <txdName>nebulaw</txdName>
      <handlingId>NEBULAW</handlingId>
      <gameName>NEBULAW</gameName>
      <vehicleMakeName>VULCAR</vehicleMakeName>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected/>
      <ptfxAssetName>null</ptfxAssetName>
      <audioNameHash>NEBULA</audioNameHash>
      <layout>LAYOUT_STANDARD</layout>
      <coverBoundOffsets>NEBULA_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout/>
      <cameraName>DEFAULT_FOLLOW_VEHICLE_CAMERA</cameraName>
      <aimCameraName>DEFAULT_THIRD_PERSON_VEHICLE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>VEHICLE_BONNET_CAMERA_STANDARD_LONG_EXTRA_LOW</bonnetCameraName>
      <povCameraName>REDUCED_NEAR_CLIP_POV_CAMERA</povCameraName>
      <FirstPersonDriveByIKOffset x="0.030000" y="-0.100000" z="-0.060000"/>
      <FirstPersonDriveByUnarmedIKOffset x="-0.010000" y="-0.100000" z="-0.010000"/>
      <FirstPersonProjectileDriveByIKOffset x="0.060000" y="-0.125000" z="-0.040000"/>
      <FirstPersonProjectileDriveByPassengerIKOffset x="-0.060000" y="-0.125000" z="-0.040000"/>
      <FirstPersonDriveByRightPassengerIKOffset x="-0.030000" y="-0.100000" z="-0.060000"/>
      <FirstPersonDriveByRightPassengerUnarmedIKOffset x="-0.010000" y="-0.100000" z="-0.010000"/>
      <FirstPersonMobilePhoneOffset x="0.150000" y="0.080000" z="0.525000"/>
      <FirstPersonPassengerMobilePhoneOffset x="0.201000" y="0.060000" z="0.402000"/>
      <PovCameraOffset x="0.000000" y="-0.220000" z="0.630000"/>
      <PovCameraVerticalAdjustmentForRollCage value="0.000000"/>
      <PovPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000"/>
      <vfxInfoName>VFXVEHICLEINFO_CAR_GENERIC</vfxInfoName>
      <shouldUseCinematicViewMode value="true"/>
      <shouldCameraTransitionOnClimbUpDown value="false"/>
      <shouldCameraIgnoreExiting value="false"/>
      <AllowPretendOccupants value="true"/>
      <AllowJoyriding value="true"/>
      <AllowSundayDriving value="true"/>
      <AllowBodyColorMapping value="true"/>
      <wheelScale value="0.211900"/>
      <wheelScaleRear value="0.211900"/>
      <dirtLevelMin value="0.000000"/>
      <dirtLevelMax value="0.450000"/>
      <envEffScaleMin value="0.000000"/>
      <envEffScaleMax value="1.000000"/>
      <envEffScaleMin2 value="0.000000"/>
      <envEffScaleMax2 value="1.000000"/>
      <damageMapScale value="0.600000"/>
      <damageOffsetScale value="1.000000"/>
      <diffuseTint value="0x00FFFFFF"/>
      <steerWheelMult value="1.000000"/>
      <HDTextureDist value="5.000000"/>
      <lodDistances content="float_array">
        20.000000
        40.000000
        80.000000	
        120.000000
        500.000000
        500.000000
      </lodDistances>
      <minSeatHeight value="0.844"/>
      <identicalModelSpawnDistance value="80"/>
      <maxNumOfSameColor value="1"/>
      <defaultBodyHealth value="1000.000000"/>
      <pretendOccupantsScale value="1.000000"/>
      <visibleSpawnDistScale value="1.000000"/>
      <trackerPathWidth value="2.000000"/>
      <weaponForceMult value="1.000000"/>
      <frequency value="5"/>
      <swankness>SWANKNESS_1</swankness>
      <maxNum value="1"/>
      <flags>FLAG_AVERAGE_CAR FLAG_HAS_SUPERCHARGER FLAG_INCREASE_CAMBER_WITH_SUSPENSION_MOD</flags>
      <type>VEHICLE_TYPE_CAR</type>
      <plateType>VPT_FRONT_AND_BACK_PLATES</plateType>
      <dashboardType>VDT_FUTO</dashboardType>
      <vehicleClass>VC_SPORT_CLASSIC</vehicleClass>
      <wheelType>VWT_MUSCLE</wheelType>
      <trailers/>
      <additionalTrailers/>
      <drivers/>
      <extraIncludes/>
      <doorsWithCollisionWhenClosed/>
      <driveableDoors/>
      <bumpersNeedToCollideWithMap value="false"/>
      <needsRopeTexture value="false"/>
      <requiredExtras/>
      <rewards/>
      <cinematicPartCamera>
        <Item>WHEEL_FRONT_RIGHT_CAMERA</Item>
        <Item>WHEEL_FRONT_LEFT_CAMERA</Item>
        <Item>WHEEL_REAR_RIGHT_CAMERA</Item>
        <Item>WHEEL_REAR_LEFT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet/>
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000"/>
      <buoyancySphereSizeScale value="1.000000"/>
      <pOverrideRagdollThreshold type="NULL"/>
      <firstPersonDrivebyData>
        <Item>STD_NEBULA_FRONT_LEFT</Item>
        <Item>STD_NEBULA_FRONT_RIGHT</Item>
      </firstPersonDrivebyData>
      <numSeatsOverride value="2"/>
    </Item>
    <Item>
      <modelName>paradox2</modelName>
      <txdName>paradox2</txdName>
      <handlingId>PARADOX2</handlingId>
      <gameName>PARADOX2</gameName>
      <vehicleMakeName>WILLARD</vehicleMakeName>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected/>
      <ptfxAssetName>null</ptfxAssetName>
      <audioNameHash>GAUNTLET5</audioNameHash>
      <layout>LAYOUT_LOW_RESTRICTED_GAUNTLET3</layout>
      <coverBoundOffsets>JESTER3_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout/>
      <cameraName>FOLLOW_CHEETAH_CAMERA</cameraName>
      <aimCameraName>BOX_VEHICLE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>VEHICLE_BONNET_CAMERA_LOW</bonnetCameraName>
      <povCameraName>DEFAULT_POV_CAMERA</povCameraName>
      <FirstPersonDriveByIKOffset x="0.000000" y="-0.060000" z="-0.040000"/>
      <FirstPersonDriveByUnarmedIKOffset x="0.000000" y="0.020000" z="-0.020000"/>
      <FirstPersonProjectileDriveByIKOffset x="0.090000" y="-0.130000" z="-0.070000"/>
      <FirstPersonProjectileDriveByPassengerIKOffset x="0.070000" y="-0.095000" z="-0.070000"/>
      <FirstPersonProjectileDriveByRearLeftIKOffset x="0.000000" y="-0.130000" z="-0.070000"/>
      <FirstPersonProjectileDriveByRearRightIKOffset x="-0.020000" y="-0.130000" z="-0.070000"/>
      <FirstPersonDriveByLeftPassengerIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonDriveByRightPassengerIKOffset x="0.000000" y="-0.015000" z="-0.040000"/>
      <FirstPersonDriveByRightRearPassengerIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonDriveByLeftPassengerUnarmedIKOffset x="0.020000" y="-0.080000" z="-0.020000"/>
      <FirstPersonDriveByRightPassengerUnarmedIKOffset x="0.000000" y="0.020000" z="-0.020000"/>
      <FirstPersonMobilePhoneOffset x="0.125000" y="0.160000" z="0.520000"/>
      <FirstPersonPassengerMobilePhoneOffset x="0.225000" y="0.140000" z="0.440000"/>
      <FirstPersonMobilePhoneSeatIKOffset/>
      <PovCameraOffset x="0.000000" y="-0.190000" z="0.640000"/>
      <PovCameraVerticalAdjustmentForRollCage value="0.000000"/>
      <PovPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000"/>
      <PovRearPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000"/>
      <vfxInfoName>VFXVEHICLEINFO_CAR_GENERIC</vfxInfoName>
      <shouldUseCinematicViewMode value="true"/>
      <shouldCameraTransitionOnClimbUpDown value="false"/>
      <shouldCameraIgnoreExiting value="false"/>
      <AllowPretendOccupants value="true"/>
      <AllowJoyriding value="false"/>
      <AllowSundayDriving value="false"/>
      <AllowBodyColorMapping value="true"/>
      <wheelScale value="0.215000"/>
      <wheelScaleRear value="0.225000"/>
      <dirtLevelMin value="0.000000"/>
      <dirtLevelMax value="1.000000"/>
      <envEffScaleMin value="0.300000"/>
      <envEffScaleMax value="0.400000"/>
      <envEffScaleMin2 value="0.000000"/>
      <envEffScaleMax2 value="1.000000"/>
      <damageMapScale value="0.400000"/>
      <damageOffsetScale value="0.400000"/>
      <diffuseTint value="0x00FFFFFF"/>
      <steerWheelMult value="1.00000"/>
      <HDTextureDist value="5.000000"/>
      <lodDistances content="float_array">
        25.000000
        70.000000
        100.000000
        140.000000
        500.000000
        500.000000
      </lodDistances>
      <minSeatHeight value="0.988"/>
      <identicalModelSpawnDistance value="20"/>
      <maxNumOfSameColor value="10"/>
      <defaultBodyHealth value="1000.000000"/>
      <pretendOccupantsScale value="1.000000"/>
      <visibleSpawnDistScale value="1.000000"/>
      <trackerPathWidth value="2.000000"/>
      <weaponForceMult value="1.000000"/>
      <frequency value="5"/>
      <swankness>SWANKNESS_1</swankness>
      <maxNum value="1"/>
      <flags>FLAG_SPAWN_ON_TRAILER FLAG_NO_BROKEN_DOWN_SCENARIO FLAG_CAN_HAVE_NEONS FLAG_DRIVER_SHOULD_BE_MALE</flags>
      <type>VEHICLE_TYPE_CAR</type>
      <plateType>VPT_BACK_PLATES</plateType>
      <dashboardType>VDT_DUKES</dashboardType>
      <vehicleClass>VC_MUSCLE</vehicleClass>
      <wheelType>VWT_MUSCLE</wheelType>
      <trailers/>
      <additionalTrailers/>
      <drivers/>
      <extraIncludes/>
      <doorsWithCollisionWhenClosed/>
      <driveableDoors/>
      <bumpersNeedToCollideWithMap value="false"/>
      <needsRopeTexture value="true"/>
      <requiredExtras/>
      <rewards/>
      <cinematicPartCamera>
        <Item>WHEEL_WIDE_REAR_RIGHT_CAMERA</Item>
        <Item>WHEEL_WIDE_REAR_LEFT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet/>
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000"/>
      <buoyancySphereSizeScale value="1.000000"/>
      <pOverrideRagdollThreshold type="NULL"/>
      <firstPersonDrivebyData>
        <Item>STD_JESTER3_FRONT_LEFT</Item>
        <Item>STD_JESTER3_FRONT_RIGHT</Item>
      </firstPersonDrivebyData>
      <numSeatsOverride value="2"/>
    </Item>
    <Item>
      <modelName>paradox</modelName>
      <txdName>paradox</txdName>
      <handlingId>PARADOX</handlingId>
      <gameName>PARADOX</gameName>
      <vehicleMakeName>WILLARD</vehicleMakeName>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected/>
      <ptfxAssetName>null</ptfxAssetName>
      <audioNameHash>GAUNTLET5</audioNameHash>
      <layout>LAYOUT_LOW_RESTRICTED_GAUNTLET3</layout>
      <coverBoundOffsets>JESTER3_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout/>
      <cameraName>FOLLOW_CHEETAH_CAMERA</cameraName>
      <aimCameraName>BOX_VEHICLE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>VEHICLE_BONNET_CAMERA_LOW</bonnetCameraName>
      <povCameraName>DEFAULT_POV_CAMERA</povCameraName>
      <FirstPersonDriveByIKOffset x="0.000000" y="-0.060000" z="-0.040000"/>
      <FirstPersonDriveByUnarmedIKOffset x="0.000000" y="0.020000" z="-0.020000"/>
      <FirstPersonProjectileDriveByIKOffset x="0.090000" y="-0.130000" z="-0.070000"/>
      <FirstPersonProjectileDriveByPassengerIKOffset x="0.070000" y="-0.095000" z="-0.070000"/>
      <FirstPersonProjectileDriveByRearLeftIKOffset x="0.000000" y="-0.130000" z="-0.070000"/>
      <FirstPersonProjectileDriveByRearRightIKOffset x="-0.020000" y="-0.130000" z="-0.070000"/>
      <FirstPersonDriveByLeftPassengerIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonDriveByRightPassengerIKOffset x="0.000000" y="-0.015000" z="-0.040000"/>
      <FirstPersonDriveByRightRearPassengerIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonDriveByLeftPassengerUnarmedIKOffset x="0.020000" y="-0.080000" z="-0.020000"/>
      <FirstPersonDriveByRightPassengerUnarmedIKOffset x="0.000000" y="0.020000" z="-0.020000"/>
      <FirstPersonMobilePhoneOffset x="0.125000" y="0.160000" z="0.520000"/>
      <FirstPersonPassengerMobilePhoneOffset x="0.225000" y="0.140000" z="0.440000"/>
      <FirstPersonMobilePhoneSeatIKOffset/>
      <PovCameraOffset x="0.000000" y="-0.190000" z="0.640000"/>
      <PovCameraVerticalAdjustmentForRollCage value="0.000000"/>
      <PovPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000"/>
      <PovRearPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000"/>
      <vfxInfoName>VFXVEHICLEINFO_CAR_GENERIC</vfxInfoName>
      <shouldUseCinematicViewMode value="true"/>
      <shouldCameraTransitionOnClimbUpDown value="false"/>
      <shouldCameraIgnoreExiting value="false"/>
      <AllowPretendOccupants value="true"/>
      <AllowJoyriding value="false"/>
      <AllowSundayDriving value="false"/>
      <AllowBodyColorMapping value="true"/>
      <wheelScale value="0.225000"/>
      <wheelScaleRear value="0.235000"/>
      <dirtLevelMin value="0.000000"/>
      <dirtLevelMax value="1.000000"/>
      <envEffScaleMin value="0.300000"/>
      <envEffScaleMax value="0.400000"/>
      <envEffScaleMin2 value="0.000000"/>
      <envEffScaleMax2 value="1.000000"/>
      <damageMapScale value="0.400000"/>
      <damageOffsetScale value="0.400000"/>
      <diffuseTint value="0x00FFFFFF"/>
      <steerWheelMult value="1.00000"/>
      <HDTextureDist value="5.000000"/>
      <lodDistances content="float_array">
        25.000000
        70.000000
        100.000000
        140.000000
        500.000000
        500.000000
      </lodDistances>
      <minSeatHeight value="0.988"/>
      <identicalModelSpawnDistance value="20"/>
      <maxNumOfSameColor value="10"/>
      <defaultBodyHealth value="1000.000000"/>
      <pretendOccupantsScale value="1.000000"/>
      <visibleSpawnDistScale value="1.000000"/>
      <trackerPathWidth value="2.000000"/>
      <weaponForceMult value="1.000000"/>
      <frequency value="5"/>
      <swankness>SWANKNESS_1</swankness>
      <maxNum value="1"/>
      <flags>FLAG_EXTRAS_CONVERTIBLE FLAG_EXTRAS_ALL FLAG_SPAWN_ON_TRAILER FLAG_NO_BROKEN_DOWN_SCENARIO FLAG_CAN_HAVE_NEONS FLAG_EXTRAS_STRONG</flags>
      <type>VEHICLE_TYPE_CAR</type>
      <plateType>VPT_BACK_PLATES</plateType>
      <dashboardType>VDT_DUKES</dashboardType>
      <vehicleClass>VC_MUSCLE</vehicleClass>
      <wheelType>VWT_MUSCLE</wheelType>
      <trailers/>
      <additionalTrailers/>
      <drivers/>
      <extraIncludes/>
      <doorsWithCollisionWhenClosed/>
      <driveableDoors/>
      <bumpersNeedToCollideWithMap value="false"/>
      <needsRopeTexture value="true"/>
      <requiredExtras/>
      <rewards/>
      <cinematicPartCamera>
        <Item>WHEEL_WIDE_REAR_RIGHT_CAMERA</Item>
        <Item>WHEEL_WIDE_REAR_LEFT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet/>
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000"/>
      <buoyancySphereSizeScale value="1.000000"/>
      <pOverrideRagdollThreshold type="NULL"/>
      <firstPersonDrivebyData>
        <Item>STD_JESTER3_FRONT_LEFT</Item>
        <Item>STD_JESTER3_FRONT_RIGHT</Item>
      </firstPersonDrivebyData>
      <numSeatsOverride value="2"/>
    </Item>
    <Item>
      <modelName>paragono</modelName>
      <txdName>paragono</txdName>
      <handlingId>PARAGON3</handlingId>
      <gameName>PARAGONO</gameName>
      <vehicleMakeName>ENUS</vehicleMakeName>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected/>
      <ptfxAssetName>null</ptfxAssetName>
      <audioNameHash>PARAGON</audioNameHash>
      <layout>LAYOUT_STD_EXITFIXUP</layout>
      <coverBoundOffsets>JESTER3_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout/>
      <cameraName>FOLLOW_CHEETAH_CAMERA</cameraName>
      <aimCameraName>BOX_VEHICLE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>VEHICLE_BONNET_CAMERA_LOW</bonnetCameraName>
      <povCameraName>DEFAULT_POV_CAMERA</povCameraName>
      <FirstPersonDriveByIKOffset x="0.000000" y="-0.060000" z="-0.040000"/>
      <FirstPersonDriveByUnarmedIKOffset x="0.000000" y="0.020000" z="-0.020000"/>
      <FirstPersonProjectileDriveByIKOffset x="0.090000" y="-0.130000" z="-0.070000"/>
      <FirstPersonProjectileDriveByPassengerIKOffset x="0.070000" y="-0.095000" z="-0.070000"/>
      <FirstPersonProjectileDriveByRearLeftIKOffset x="0.000000" y="-0.130000" z="-0.070000"/>
      <FirstPersonProjectileDriveByRearRightIKOffset x="-0.020000" y="-0.130000" z="-0.070000"/>
      <FirstPersonDriveByLeftPassengerIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonDriveByRightPassengerIKOffset x="0.000000" y="-0.015000" z="-0.040000"/>
      <FirstPersonDriveByRightRearPassengerIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonDriveByLeftPassengerUnarmedIKOffset x="0.020000" y="-0.080000" z="-0.020000"/>
      <FirstPersonDriveByRightPassengerUnarmedIKOffset x="0.000000" y="0.020000" z="-0.020000"/>
      <FirstPersonMobilePhoneOffset x="0.125000" y="0.160000" z="0.520000"/>
      <FirstPersonPassengerMobilePhoneOffset x="0.225000" y="0.140000" z="0.440000"/>
      <FirstPersonMobilePhoneSeatIKOffset/>
      <PovCameraOffset x="0.000000" y="-0.190000" z="0.640000"/>
      <PovCameraVerticalAdjustmentForRollCage value="0.000000"/>
      <PovPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000"/>
      <PovRearPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000"/>
      <vfxInfoName>VFXVEHICLEINFO_CAR_GENERIC</vfxInfoName>
      <shouldUseCinematicViewMode value="true"/>
      <shouldCameraTransitionOnClimbUpDown value="false"/>
      <shouldCameraIgnoreExiting value="false"/>
      <AllowPretendOccupants value="true"/>
      <AllowJoyriding value="false"/>
      <AllowSundayDriving value="false"/>
      <AllowBodyColorMapping value="true"/>
      <wheelScale value="0.236500"/>
      <wheelScaleRear value="0.236500"/>
      <dirtLevelMin value="0.000000"/>
      <dirtLevelMax value="1.000000"/>
      <envEffScaleMin value="0.300000"/>
      <envEffScaleMax value="0.400000"/>
      <envEffScaleMin2 value="0.000000"/>
      <envEffScaleMax2 value="1.000000"/>
      <damageMapScale value="0.400000"/>
      <damageOffsetScale value="0.400000"/>
      <diffuseTint value="0x00FFFFFF"/>
      <steerWheelMult value="1.00000"/>
      <HDTextureDist value="5.000000"/>
      <lodDistances content="float_array">
        15.000000
        25.000000
        70.000000
        140.000000
        500.000000
        500.000000
      </lodDistances>
      <minSeatHeight value="0.988"/>
      <identicalModelSpawnDistance value="20"/>
      <maxNumOfSameColor value="10"/>
      <defaultBodyHealth value="1000.000000"/>
      <pretendOccupantsScale value="1.000000"/>
      <visibleSpawnDistScale value="1.000000"/>
      <trackerPathWidth value="2.000000"/>
      <weaponForceMult value="1.000000"/>
      <frequency value="5"/>
      <swankness>SWANKNESS_1</swankness>
      <maxNum value="1"/>
      <flags>FLAG_SPORTS FLAG_SPAWN_ON_TRAILER FLAG_RICH_CAR FLAG_NO_BROKEN_DOWN_SCENARIO FLAG_INCREASE_CAMBER_WITH_SUSPENSION_MOD FLAG_CAN_HAVE_NEONS FLAG_IS_OFFROAD_VEHICLE</flags>
      <type>VEHICLE_TYPE_CAR</type>
      <plateType>VPT_BACK_PLATES</plateType>
      <dashboardType>VDT_RACE</dashboardType>
      <vehicleClass>VC_OFF_ROAD</vehicleClass>
      <wheelType>VWT_OFFROAD</wheelType>
      <trailers/>
      <additionalTrailers/>
      <drivers/>
      <extraIncludes/>
      <doorsWithCollisionWhenClosed>
        <Item>VEH_EXT_BOOT</Item>
      </doorsWithCollisionWhenClosed>
      <driveableDoors/>
      <bumpersNeedToCollideWithMap value="false"/>
      <needsRopeTexture value="true"/>
      <requiredExtras/>
      <rewards/>
      <cinematicPartCamera>
        <Item>WHEEL_WIDE_REAR_RIGHT_CAMERA</Item>
        <Item>WHEEL_WIDE_REAR_LEFT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet>Truck</NmBraceOverrideSet>
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000"/>
      <buoyancySphereSizeScale value="1.000000"/>
      <pOverrideRagdollThreshold type="NULL"/>
      <firstPersonDrivebyData>
        <Item>STD_JESTER3_FRONT_LEFT</Item>
        <Item>STD_JESTER3_FRONT_RIGHT</Item>
      </firstPersonDrivebyData>
      <numSeatsOverride value="2"/>
    </Item>
    <Item>
      <modelName>primoard</modelName>
      <txdName>primoard</txdName>
      <handlingId>PRIMOARD</handlingId>
      <gameName>PRIMOARD</gameName>
      <vehicleMakeName>ALBANY</vehicleMakeName>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected/>
      <ptfxAssetName>null</ptfxAssetName>
      <audioNameHash>elegy2</audioNameHash>
      <layout>LAYOUT_STD_LOWRIDER</layout>
      <coverBoundOffsets>PRIMO_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout/>
      <cameraName>DEFAULT_FOLLOW_VEHICLE_CAMERA</cameraName>
      <aimCameraName>DEFAULT_THIRD_PERSON_VEHICLE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>VEHICLE_BONNET_CAMERA_NEAR_HIGH</bonnetCameraName>
      <povCameraName>DEFAULT_POV_CAMERA</povCameraName>
      <FirstPersonDriveByIKOffset x="0.000000" y="-0.060000" z="-0.060000"/>
      <FirstPersonDriveByUnarmedIKOffset x="0.000000" y="0.000000" z="0.-020000"/>
      <FirstPersonProjectileDriveByIKOffset x="0.000000" y="-0.055000" z="-0.045000"/>
      <FirstPersonProjectileDriveByPassengerIKOffset x="0.000000" y="-0.055000" z="-0.045000"/>
      <FirstPersonProjectileDriveByRearLeftIKOffset x="0.000000" y="0.020000" z="0.000000"/>
      <FirstPersonProjectileDriveByRearRightIKOffset x="0.000000" y="0.020000" z="0.000000"/>
      <FirstPersonDriveByLeftPassengerIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonDriveByRightPassengerIKOffset x="0.000000" y="-0.060000" z="-0.030000"/>
      <FirstPersonDriveByRightRearPassengerIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonDriveByLeftPassengerUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonDriveByRightPassengerUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonMobilePhoneOffset x="0.138000" y="0.228000" z="0.553000"/>
      <FirstPersonPassengerMobilePhoneOffset x="0.136000" y="0.223000" z="0.415000"/>
      <FirstPersonMobilePhoneSeatIKOffset>
        <Item>
          <Offset x="0.136000" y="0.156000" z="0.445000"/>
          <SeatIndex value="2"/>
        </Item>
        <Item>
          <Offset x="0.136000" y="0.156000" z="0.445000"/>
          <SeatIndex value="3"/>
        </Item>
      </FirstPersonMobilePhoneSeatIKOffset>
      <PovCameraOffset x="0.000000" y="-0.160000" z="0.680000"/>
      <PovCameraVerticalAdjustmentForRollCage value="0.000000"/>
      <PovPassengerCameraOffset x="0.000000" y="-0.020000" z="-0.005000"/>
      <PovRearPassengerCameraOffset x="0.000000" y="-0.045000" z="-0.030000"/>
      <vfxInfoName>VFXVEHICLEINFO_CAR_GENERIC</vfxInfoName>
      <shouldUseCinematicViewMode value="true"/>
      <shouldCameraTransitionOnClimbUpDown value="false"/>
      <shouldCameraIgnoreExiting value="false"/>
      <AllowPretendOccupants value="true"/>
      <AllowJoyriding value="true"/>
      <AllowSundayDriving value="true"/>
      <AllowBodyColorMapping value="true"/>
      <wheelScale value="0.234000"/>
      <wheelScaleRear value="0.234000"/>
      <dirtLevelMin value="0.000000"/>
      <dirtLevelMax value="0.850000"/>
      <envEffScaleMin value="0.000000"/>
      <envEffScaleMax value="1.000000"/>
      <envEffScaleMin2 value="0.000000"/>
      <envEffScaleMax2 value="1.000000"/>
      <damageMapScale value="0.700000"/>
      <damageOffsetScale value="0.700000"/>
      <diffuseTint value="0x00FFFFFF"/>
      <steerWheelMult value="1.000000"/>
      <HDTextureDist value="5.000000"/>
      <lodDistances content="float_array">
        10.000000
        25.000000
        60.000000
        120.000000
        500.000000
        500.000000
      </lodDistances>
      <minSeatHeight value="0.845"/>
      <identicalModelSpawnDistance value="20"/>
      <maxNumOfSameColor value="10"/>
      <defaultBodyHealth value="1000.000000"/>
      <pretendOccupantsScale value="1.000000"/>
      <visibleSpawnDistScale value="1.000000"/>
      <trackerPathWidth value="2.000000"/>
      <weaponForceMult value="1.000000"/>
      <frequency value="40"/>
      <swankness>SWANKNESS_1</swankness>
      <maxNum value="50"/>
      <flags>FLAG_PARKING_SENSORS FLAG_AVERAGE_CAR FLAG_POOR_CAR FLAG_HAS_INTERIOR_EXTRAS </flags>
      <type>VEHICLE_TYPE_CAR</type>
      <plateType>VPT_FRONT_AND_BACK_PLATES</plateType>
      <dashboardType>VDT_TAILGATER</dashboardType>
      <vehicleClass>VC_SEDAN</vehicleClass>
      <wheelType>VWT_SPORT</wheelType>
      <trailers/>
      <additionalTrailers/>
      <drivers/>
      <extraIncludes/>
      <doorsWithCollisionWhenClosed/>
      <driveableDoors/>
      <bumpersNeedToCollideWithMap value="true"/>
      <needsRopeTexture value="false"/>
      <requiredExtras/>
      <rewards/>
      <cinematicPartCamera>
        <Item>WHEEL_FRONT_RIGHT_CAMERA</Item>
        <Item>WHEEL_FRONT_LEFT_CAMERA</Item>
        <Item>WHEEL_REAR_RIGHT_CAMERA</Item>
        <Item>WHEEL_REAR_LEFT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet/>
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000"/>
      <buoyancySphereSizeScale value="1.000000"/>
      <pOverrideRagdollThreshold type="NULL"/>
      <firstPersonDrivebyData>
        <Item>STD_PRIMO_FRONT_LEFT</Item>
        <Item>STD_PRIMO_FRONT_RIGHT</Item>
        <Item>STD_ORACLE_REAR_LEFT</Item>
        <Item>STD_ORACLE_REAR_RIGHT</Item>
      </firstPersonDrivebyData>
      <LowriderLeanAccelModifier value="1.000000"/>
    </Item>
    <Item>
      <modelName>proff</modelName>
      <txdName>proff</txdName>
      <handlingId>PROFF</handlingId>
      <gameName>PROFF</gameName>
      <vehicleMakeName>PROGEN</vehicleMakeName>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected/>
      <ptfxAssetName>null</ptfxAssetName>
      <audioNameHash>formula</audioNameHash>
      <layout>LAYOUT_LOW_RACER_FORMULA</layout>
      <coverBoundOffsets>FORMULA_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout/>
      <cameraName>FOLLOW_SCRAMJET_CAMERA</cameraName>
      <aimCameraName>DEFAULT_THIRD_PERSON_VEHICLE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>VEHICLE_BONNET_CAMERA_FORMULA</bonnetCameraName>
      <povCameraName>REDUCED_NEAR_CLIP_POV_CAMERA_NO_REVERSE_FORMULA</povCameraName>
      <FirstPersonDriveByIKOffset x="0.000000" y="-0.050000" z="0.000000"/>
      <FirstPersonDriveByUnarmedIKOffset x="0.000000" y="-0.100000" z="0.000000"/>
      <FirstPersonProjectileDriveByIKOffset x="0.000000" y="0.000000" z="-0.015000"/>
      <FirstPersonProjectileDriveByPassengerIKOffset x="-0.100000" y="0.000000" z="-0.015000"/>
      <FirstPersonProjectileDriveByRearLeftIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonProjectileDriveByRearRightIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonDriveByLeftPassengerIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonDriveByRightPassengerIKOffset x="0.000000" y="-0.050000" z="0.000000"/>
      <FirstPersonDriveByRightRearPassengerIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonDriveByLeftPassengerUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonDriveByRightPassengerUnarmedIKOffset x="0.000000" y="-0.100000" z="0.000000"/>
      <FirstPersonMobilePhoneOffset x="0.150000" y="0.058000" z="0.561000"/>
      <FirstPersonPassengerMobilePhoneOffset x="0.169000" y="0.013000" z="0.458000"/>
      <PovCameraOffset x="0.000000" y="-0.275000" z="0.585000"/>
      <PovCameraVerticalAdjustmentForRollCage value="0.000000"/>
      <PovPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000"/>
      <PovRearPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000"/>
      <vfxInfoName>VFXVEHICLEINFO_MOTORBIKE_GENERIC</vfxInfoName>
      <shouldUseCinematicViewMode value="true"/>
      <shouldCameraTransitionOnClimbUpDown value="false"/>
      <shouldCameraIgnoreExiting value="false"/>
      <AllowPretendOccupants value="true"/>
      <AllowJoyriding value="true"/>
      <AllowSundayDriving value="false"/>
      <AllowBodyColorMapping value="true"/>
      <wheelScale value="0.183000"/>
      <wheelScaleRear value="0.183000"/>
      <dirtLevelMin value="0.300000"/>
      <dirtLevelMax value="2.000000"/>
      <envEffScaleMin value="0.000000"/>
      <envEffScaleMax value="1.000000"/>
      <envEffScaleMin2 value="0.000000"/>
      <envEffScaleMax2 value="1.000000"/>
      <damageMapScale value="0.3"/>
      <damageOffsetScale value="0.3"/>
      <diffuseTint value="0x00FFFFFF"/>
      <steerWheelMult value="0.600000"/>
      <HDTextureDist value="5.000000"/>
      <lodDistances content="float_array">
        15.000000
        30.000000
        70.000000	
        140.000000	
        500.000000	
        500.000000
      </lodDistances>
      <minSeatHeight value="0.896"/>
      <identicalModelSpawnDistance value="20"/>
      <maxNumOfSameColor value="10"/>
      <defaultBodyHealth value="1000.000000"/>
      <pretendOccupantsScale value="1.000000"/>
      <visibleSpawnDistScale value="1.000000"/>
      <trackerPathWidth value="2.000000"/>
      <weaponForceMult value="1.000000"/>
      <frequency value="30"/>
      <swankness>SWANKNESS_1</swankness>
      <maxNum value="5"/>
      <flags>FLAG_EXTRAS_STRONG FLAG_EXTRAS_ALL FLAG_NO_BOOT FLAG_RICH_CAR FLAG_POOR_CAR  FLAG_PEDS_INSIDE_CAN_BE_SET_ON_FIRE_MP FLAG_DONT_TIMESLICE_WHEELS FLAG_IS_OFFROAD_VEHICLE</flags>
      <type>VEHICLE_TYPE_CAR</type>
      <plateType>VPT_NONE</plateType>
      <dashboardType>VDT_RACE</dashboardType>
      <vehicleClass>VC_OPEN_WHEEL</vehicleClass>
      <wheelType>VWT_OFFROAD</wheelType>
      <trailers/>
      <additionalTrailers/>
      <drivers/>
      <extraIncludes/>
      <doorsWithCollisionWhenClosed/>
      <driveableDoors/>
      <bumpersNeedToCollideWithMap value="false"/>
      <needsRopeTexture value="false"/>
      <requiredExtras/>
      <rewards/>
      <cinematicPartCamera>
        <Item>FORMULA_ON_BOARD_CAMERA</Item>
        <Item>WHEEL_FRONT_RIGHT_CAMERA</Item>
        <Item>WHEEL_FRONT_LEFT_CAMERA</Item>
        <Item>WHEEL_WIDE_REAR_RIGHT_CAMERA</Item>
        <Item>WHEEL_WIDE_REAR_LEFT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet/>
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000"/>
      <buoyancySphereSizeScale value="1.200000"/>
      <pOverrideRagdollThreshold type="NULL"/>
      <firstPersonDrivebyData>
        <Item>LOW_DUNE4_FRONT_LEFT</Item>
        <Item>LOW_DUNE4_FRONT_RIGHT</Item>
      </firstPersonDrivebyData>
    </Item>
    <Item>
      <modelName>rh4</modelName>
      <txdName>rh4</txdName>
      <handlingId>RH4</handlingId>
      <gameName>RH4</gameName>
      <vehicleMakeName>ANNIS</vehicleMakeName>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected/>
      <ptfxAssetName>null</ptfxAssetName>
      <audioNameHash>elegy</audioNameHash>
      <layout>LAYOUT_STANDARD</layout>
      <coverBoundOffsets>NEBULA_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout/>
      <cameraName>DEFAULT_FOLLOW_VEHICLE_CAMERA</cameraName>
      <aimCameraName>DEFAULT_THIRD_PERSON_VEHICLE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>VEHICLE_BONNET_CAMERA_STANDARD_LONG_EXTRA_LOW</bonnetCameraName>
      <povCameraName>REDUCED_NEAR_CLIP_POV_CAMERA</povCameraName>
      <FirstPersonDriveByIKOffset x="0.030000" y="-0.100000" z="-0.060000"/>
      <FirstPersonDriveByUnarmedIKOffset x="-0.010000" y="-0.100000" z="-0.010000"/>
      <FirstPersonProjectileDriveByIKOffset x="0.060000" y="-0.125000" z="-0.040000"/>
      <FirstPersonProjectileDriveByPassengerIKOffset x="-0.060000" y="-0.125000" z="-0.040000"/>
      <FirstPersonDriveByRightPassengerIKOffset x="-0.030000" y="-0.100000" z="-0.060000"/>
      <FirstPersonDriveByRightPassengerUnarmedIKOffset x="-0.010000" y="-0.100000" z="-0.010000"/>
      <FirstPersonMobilePhoneOffset x="0.150000" y="0.080000" z="0.525000"/>
      <FirstPersonPassengerMobilePhoneOffset x="0.201000" y="0.060000" z="0.402000"/>
      <PovCameraOffset x="0.000000" y="-0.220000" z="0.630000"/>
      <PovCameraVerticalAdjustmentForRollCage value="0.000000"/>
      <PovPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000"/>
      <vfxInfoName>VFXVEHICLEINFO_CAR_GENERIC</vfxInfoName>
      <shouldUseCinematicViewMode value="true"/>
      <shouldCameraTransitionOnClimbUpDown value="false"/>
      <shouldCameraIgnoreExiting value="false"/>
      <AllowPretendOccupants value="true"/>
      <AllowJoyriding value="true"/>
      <AllowSundayDriving value="true"/>
      <AllowBodyColorMapping value="true"/>
      <wheelScale value="0.232181"/>
      <wheelScaleRear value="0.232181"/>
      <dirtLevelMin value="0.000000"/>
      <dirtLevelMax value="0.450000"/>
      <envEffScaleMin value="0.000000"/>
      <envEffScaleMax value="1.000000"/>
      <envEffScaleMin2 value="0.000000"/>
      <envEffScaleMax2 value="1.000000"/>
      <damageMapScale value="0.40000"/>
      <damageOffsetScale value="0.40000"/>
      <diffuseTint value="0x00FFFFFF"/>
      <steerWheelMult value="1.000000"/>
      <HDTextureDist value="5.000000"/>
      <lodDistances content="float_array">
        15.000000
        30.000000
        60.000000	
        120.000000
        500.000000
        500.000000
      </lodDistances>
      <minSeatHeight value="0.844"/>
      <identicalModelSpawnDistance value="20"/>
      <maxNumOfSameColor value="1"/>
      <defaultBodyHealth value="1000.000000"/>
      <pretendOccupantsScale value="1.000000"/>
      <visibleSpawnDistScale value="1.000000"/>
      <trackerPathWidth value="2.000000"/>
      <weaponForceMult value="1.000000"/>
      <frequency value="20"/>
      <swankness>SWANKNESS_3</swankness>
      <maxNum value="3"/>
      <flags>FLAG_SPORTS FLAG_AVERAGE_CAR FLAG_CAN_HAVE_NEONS FLAG_INCREASE_CAMBER_WITH_SUSPENSION_MOD</flags>
      <type>VEHICLE_TYPE_CAR</type>
      <plateType>VPT_FRONT_AND_BACK_PLATES</plateType>
      <dashboardType>VDT_FUTO</dashboardType>
      <vehicleClass>VC_SPORT</vehicleClass>
      <wheelType>VWT_TUNER</wheelType>
      <trailers/>
      <additionalTrailers/>
      <drivers/>
      <extraIncludes/>
      <doorsWithCollisionWhenClosed/>
      <driveableDoors/>
      <bumpersNeedToCollideWithMap value="false"/>
      <needsRopeTexture value="false"/>
      <requiredExtras/>
      <rewards/>
      <cinematicPartCamera>
        <Item>WHEEL_FRONT_RIGHT_CAMERA</Item>
        <Item>WHEEL_FRONT_LEFT_CAMERA</Item>
        <Item>WHEEL_REAR_RIGHT_CAMERA</Item>
        <Item>WHEEL_REAR_LEFT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet/>
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000"/>
      <buoyancySphereSizeScale value="1.000000"/>
      <pOverrideRagdollThreshold type="NULL"/>
      <firstPersonDrivebyData>
        <Item>STD_NEBULA_FRONT_LEFT</Item>
        <Item>STD_NEBULA_FRONT_RIGHT</Item>
      </firstPersonDrivebyData>
      <numSeatsOverride value="2"/>
    </Item>
    <Item>
      <modelName>bansheepo</modelName>
      <txdName>bansheepo</txdName>
      <handlingId>BANSHEEPO</handlingId>
      <gameName>BANSHEEPO</gameName>
      <vehicleMakeName>BRAVADO</vehicleMakeName>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected/>
      <ptfxAssetName>null</ptfxAssetName>
      <audioNameHash>BANSHEEPO</audioNameHash>
      <layout>LAYOUT_LOW</layout>
      <coverBoundOffsets>AIRTUG_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout/>
      <cameraName>DEFAULT_FOLLOW_VEHICLE_CAMERA</cameraName>
      <aimCameraName>DEFAULT_THIRD_PERSON_VEHICLE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>VEHICLE_BONNET_CAMERA_STANDARD_LONG</bonnetCameraName>
      <povCameraName>DEFAULT_POV_CAMERA</povCameraName>
      <FirstPersonDriveByIKOffset x="0.000000" y="-0.060000" z="0.030000"/>
      <FirstPersonDriveByUnarmedIKOffset x="0.000000" y="-0.100000" z="0.000000"/>
      <FirstPersonProjectileDriveByIKOffset x="0.050000" y="-0.125000" z="-0.080000"/>
      <FirstPersonProjectileDriveByPassengerIKOffset x="-0.180000" y="-0.150000" z="-0.015000"/>
      <FirstPersonProjectileDriveByRearLeftIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonProjectileDriveByRearRightIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonDriveByLeftPassengerIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonDriveByRightPassengerIKOffset x="0.000000" y="-0.060000" z="0.030000"/>
      <FirstPersonDriveByRightRearPassengerIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonDriveByLeftPassengerUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonDriveByRightPassengerUnarmedIKOffset x="0.000000" y="-0.100000" z="0.000000"/>
      <FirstPersonMobilePhoneOffset x="0.140000" y="0.251000" z="0.551000"/>
      <FirstPersonPassengerMobilePhoneOffset x="0.136000" y="0.226000" z="0.435000"/>
      <PovCameraOffset x="0.000000" y="-0.180000" z="0.660000"/>
      <PovCameraVerticalAdjustmentForRollCage value="0.000000"/>
      <PovPassengerCameraOffset x="0.000000" y="-0.133000" z="0.000000"/>
      <PovRearPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000"/>
      <vfxInfoName>VFXVEHICLEINFO_CAR_GENERIC</vfxInfoName>
      <shouldUseCinematicViewMode value="true"/>
      <shouldCameraTransitionOnClimbUpDown value="false"/>
      <shouldCameraIgnoreExiting value="false"/>
      <AllowPretendOccupants value="true"/>
      <AllowJoyriding value="true"/>
      <AllowSundayDriving value="true"/>
      <AllowBodyColorMapping value="true"/>
      <wheelScale value="0.296700"/>
      <wheelScaleRear value="0.296700"/>
      <dirtLevelMin value="0.000000"/>
      <dirtLevelMax value="0.550000"/>
      <envEffScaleMin value="0.000000"/>
      <envEffScaleMax value="1.000000"/>
      <envEffScaleMin2 value="0.000000"/>
      <envEffScaleMax2 value="1.000000"/>
      <damageMapScale value="0.600000"/>
      <damageOffsetScale value="1.000000"/>
      <diffuseTint value="0x00FFFFFF"/>
      <steerWheelMult value="1.000000"/>
      <HDTextureDist value="5.000000"/>
      <lodDistances content="float_array">
        500.000000
        500.000000
        500.000000
        500.000000
        500.000000
        500.000000
      </lodDistances>
      <minSeatHeight value="10"/>
      <identicalModelSpawnDistance value="80"/>
      <maxNumOfSameColor value="1"/>
      <defaultBodyHealth value="1000.000000"/>
      <pretendOccupantsScale value="1.000000"/>
      <visibleSpawnDistScale value="1.000000"/>
      <trackerPathWidth value="2.000000"/>
      <weaponForceMult value="1.000000"/>
      <frequency value="5"/>
      <swankness>SWANKNESS_5</swankness>
      <maxNum value="1"/>
      <flags>FLAG_SPORTS FLAG_RICH_CAR FLAG_EXTRAS_CONVERTIBLE FLAG_ALLOW_HATS_NO_ROOF FLAG_EXTRAS_STRONG FLAG_INCREASE_PED_COMMENTS FLAG_NO_BROKEN_DOWN_SCENARIO FLAG_HAS_INTERIOR_EXTRAS FLAG_RECESSED_TAILLIGHT_CORONAS</flags>
      <type>VEHICLE_TYPE_CAR</type>
      <plateType>VPT_BACK_PLATES</plateType>
      <dashboardType>VDT_DUKES</dashboardType>
      <vehicleClass>VC_SPORT</vehicleClass>
      <wheelType>VWT_SPORT</wheelType>
      <trailers/>
      <additionalTrailers/>
      <drivers/>
      <extraIncludes/>
      <doorsWithCollisionWhenClosed/>
      <driveableDoors/>
      <bumpersNeedToCollideWithMap value="false"/>
      <needsRopeTexture value="false"/>
      <requiredExtras/>
      <rewards/>
      <cinematicPartCamera>
        <Item>WHEEL_FRONT_RIGHT_CAMERA</Item>
        <Item>WHEEL_FRONT_LEFT_CAMERA</Item>
        <Item>WHEEL_REAR_RIGHT_CAMERA</Item>
        <Item>WHEEL_REAR_LEFT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet/>
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000"/>
      <buoyancySphereSizeScale value="1.000000"/>
      <pOverrideRagdollThreshold type="NULL"/>
      <firstPersonDrivebyData>
        <Item>LOW_SURANO_FRONT_LEFT</Item>
        <Item>LOW_SURANO_FRONT_RIGHT</Item>
      </firstPersonDrivebyData>
    </Item>
    <Item>
      <modelName>romtuner</modelName>
      <txdName>romtuner</txdName>
      <handlingId>ROMTUNER</handlingId>
      <gameName>ROMTUNER</gameName>
      <vehicleMakeName>CHARIOT</vehicleMakeName>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected/>
      <ptfxAssetName>null</ptfxAssetName>
      <audioNameHash>CHEETAH2</audioNameHash>
      <layout>LAYOUT_STANDARD</layout>
      <coverBoundOffsets>ROMERO_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout/>
      <cameraName>DEFAULT_FOLLOW_VEHICLE_CAMERA</cameraName>
      <aimCameraName>BOX_VEHICLE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>VEHICLE_BONNET_CAMERA_MID</bonnetCameraName>
      <povCameraName>DEFAULT_POV_CAMERA</povCameraName>
      <FirstPersonDriveByIKOffset x="0.000000" y="-0.060000" z="-0.060000"/>
      <FirstPersonDriveByUnarmedIKOffset x="0.000000" y="-0.025000" z="0.000000"/>
      <FirstPersonProjectileDriveByIKOffset x="0.055000" y="-0.080000" z="0.000000"/>
      <FirstPersonProjectileDriveByPassengerIKOffset x="-0.150000" y="-0.080000" z="0.000000"/>
      <FirstPersonDriveByLeftPassengerIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonDriveByRightPassengerIKOffset x="0.000000" y="-0.060000" z="-0.060000"/>
      <FirstPersonDriveByLeftPassengerUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonDriveByRightPassengerUnarmedIKOffset x="0.000000" y="-0.025000" z="0.000000"/>
      <FirstPersonMobilePhoneOffset x="0.163000" y="0.270000" z="0.530000"/>
      <FirstPersonPassengerMobilePhoneOffset x="0.135000" y="0.185000" z="0.445000"/>
      <PovCameraOffset x="0.000000" y="-0.135000" z="0.650000"/>
      <PovCameraVerticalAdjustmentForRollCage value="0.000000"/>
      <PovPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000"/>
      <vfxInfoName>VFXVEHICLEINFO_CAR_GENERIC</vfxInfoName>
      <shouldUseCinematicViewMode value="true"/>
      <shouldCameraTransitionOnClimbUpDown value="false"/>
      <shouldCameraIgnoreExiting value="false"/>
      <AllowPretendOccupants value="true"/>
      <AllowJoyriding value="false"/>
      <AllowSundayDriving value="false"/>
      <AllowBodyColorMapping value="true"/>
      <wheelScale value="0.270709"/>
      <wheelScaleRear value="0.270709"/>
      <dirtLevelMin value="0.000000"/>
      <dirtLevelMax value="0.850000"/>
      <envEffScaleMin value="0.000000"/>
      <envEffScaleMax value="1.000000"/>
      <envEffScaleMin2 value="0.000000"/>
      <envEffScaleMax2 value="1.000000"/>
      <damageMapScale value="0.600000"/>
      <damageOffsetScale value="1.000000"/>
      <diffuseTint value="0x00FFFFFF"/>
      <steerWheelMult value="1.000000"/>
      <HDTextureDist value="5.000000"/>
      <lodDistances content="float_array">
        20.000000
        25.000000
        60.000000
        120.000000
        500.000000
        500.000000
      </lodDistances>
      <minSeatHeight value="0.832"/>
      <identicalModelSpawnDistance value="20"/>
      <maxNumOfSameColor value="10"/>
      <defaultBodyHealth value="1000.000000"/>
      <pretendOccupantsScale value="1.000000"/>
      <visibleSpawnDistScale value="1.000000"/>
      <trackerPathWidth value="2.000000"/>
      <weaponForceMult value="0.900000"/>
      <frequency value="100"/>
      <swankness>SWANKNESS_1</swankness>
      <maxNum value="999"/>
      <flags>FLAG_EXTRAS_ALL FLAG_EXTRAS_STRONG FLAG_AVERAGE_CAR FLAG_DONT_SPAWN_IN_CARGEN FLAG_HAS_INTERIOR_EXTRAS</flags>
      <type>VEHICLE_TYPE_CAR</type>
      <plateType>VPT_FRONT_AND_BACK_PLATES</plateType>
      <dashboardType>VDT_TAILGATER</dashboardType>
      <vehicleClass>VC_SEDAN</vehicleClass>
      <wheelType>VWT_LOWRIDER</wheelType>
      <trailers/>
      <additionalTrailers/>
      <drivers/>
      <extraIncludes/>
      <doorsWithCollisionWhenClosed/>
      <driveableDoors/>
      <bumpersNeedToCollideWithMap value="false"/>
      <needsRopeTexture value="false"/>
      <requiredExtras/>
      <rewards/>
      <cinematicPartCamera>
        <Item>WHEEL_FRONT_RIGHT_CAMERA</Item>
        <Item>WHEEL_FRONT_LEFT_CAMERA</Item>
        <Item>WHEEL_REAR_RIGHT_CAMERA</Item>
        <Item>WHEEL_REAR_LEFT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet/>
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000"/>
      <buoyancySphereSizeScale value="1.000000"/>
      <pOverrideRagdollThreshold type="NULL"/>
      <firstPersonDrivebyData>
        <Item>STD_ROMERO_FRONT_LEFT</Item>
        <Item>STD_ROMERO_FRONT_RIGHT</Item>
      </firstPersonDrivebyData>
    </Item>
    <Item>
      <modelName>sandstorm</modelName>
      <txdName>sandstorm</txdName>
      <handlingId>SANDSTORM</handlingId>
      <gameName>SANDSTORM</gameName>
      <vehicleMakeName>VAPID</vehicleMakeName>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>va_sandstorm</animConvRoofDictName>
      <animConvRoofName>sandstorm</animConvRoofName>
      <animConvRoofWindowsAffected/>
      <ptfxAssetName>null</ptfxAssetName>
      <audioNameHash>caracara2</audioNameHash>
      <layout>LAYOUT_STD_GRANGER2</layout>
      <coverBoundOffsets>MESA_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout/>
      <cameraName>DEFAULT_FOLLOW_VEHICLE_CAMERA</cameraName>
      <aimCameraName>BOX_VEHICLE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>VEHICLE_BONNET_CAMERA_STANDARD_LONG</bonnetCameraName>
      <povCameraName>DEFAULT_POV_CAMERA_LOOKAROUND_MID</povCameraName>
      <FirstPersonDriveByIKOffset x="-0.018000" y="-0.043000" z="-0.020000"/>
      <FirstPersonDriveByUnarmedIKOffset x="0.000000" y="0.000000" z="-0.020000"/>
      <FirstPersonProjectileDriveByIKOffset x="0.080000" y="0.000000" z="-0.060000"/>
      <FirstPersonProjectileDriveByPassengerIKOffset x="0.020000" y="0.060000" z="-0.090000"/>
      <FirstPersonProjectileDriveByRearLeftIKOffset x="0.000000" y="0.000000" z="-0.058000"/>
      <FirstPersonProjectileDriveByRearRightIKOffset x="0.000000" y="0.000000" z="-0.058000"/>
      <FirstPersonDriveByLeftPassengerIKOffset x="-0.030000" y="0.010000" z="-0.076000"/>
      <FirstPersonDriveByRightPassengerIKOffset x="0.000000" y="0.000000" z="-0.040000"/>
      <FirstPersonDriveByRightRearPassengerIKOffset x="0.015000" y="0.010000" z="-0.076000"/>
      <FirstPersonDriveByLeftPassengerUnarmedIKOffset x="0.000000" y="-0.150000" z="0.010000"/>
      <FirstPersonDriveByRightPassengerUnarmedIKOffset x="0.000000" y="-0.150000" z="0.010000"/>
      <FirstPersonMobilePhoneOffset x="0.150000" y="0.260000" z="0.503000"/>
      <FirstPersonPassengerMobilePhoneOffset x="0.136000" y="0.223000" z="0.445000"/>
      <FirstPersonMobilePhoneSeatIKOffset>
        <Item>
          <Offset x="0.154000" y="0.076000" z="0.478000"/>
          <SeatIndex value="2"/>
        </Item>
        <Item>
          <Offset x="0.154000" y="0.076000" z="0.478000"/>
          <SeatIndex value="3"/>
        </Item>
      </FirstPersonMobilePhoneSeatIKOffset>
      <PovCameraOffset x="0.000000" y="-0.120000" z="0.580000"/>
      <PovCameraVerticalAdjustmentForRollCage value="0.000000"/>
      <PovPassengerCameraOffset x="0.000000" y="0.010000" z="0.075000"/>
      <PovRearPassengerCameraOffset x="0.000000" y="-0.190000" z="0.145000"/>
      <vfxInfoName>VFXVEHICLEINFO_CAR_OFFROAD</vfxInfoName>
      <shouldUseCinematicViewMode value="true"/>
      <shouldCameraTransitionOnClimbUpDown value="false"/>
      <shouldCameraIgnoreExiting value="false"/>
      <AllowPretendOccupants value="true"/>
      <AllowJoyriding value="true"/>
      <AllowSundayDriving value="true"/>
      <AllowBodyColorMapping value="true"/>
      <wheelScale value="0.269000"/>
      <wheelScaleRear value="0.269000"/>
      <dirtLevelMin value="0.000000"/>
      <dirtLevelMax value="0.850000"/>
      <envEffScaleMin value="0.000000"/>
      <envEffScaleMax value="1.000000"/>
      <envEffScaleMin2 value="0.000000"/>
      <envEffScaleMax2 value="1.000000"/>
      <damageMapScale value="0.600000"/>
      <damageOffsetScale value="1.000000"/>
      <diffuseTint value="0xC4000000"/>
      <steerWheelMult value="1.000000"/>
      <HDTextureDist value="5.000000"/>
      <lodDistances content="float_array">
        15.000000
        30.000000
        70.000000	
        140.000000	
        500.000000	
        500.000000
      </lodDistances>
      <minSeatHeight value="0.846"/>
      <identicalModelSpawnDistance value="20"/>
      <maxNumOfSameColor value="10"/>
      <defaultBodyHealth value="1000.000000"/>
      <pretendOccupantsScale value="1.000000"/>
      <visibleSpawnDistScale value="1.000000"/>
      <trackerPathWidth value="2.000000"/>
      <weaponForceMult value="0.900000"/>
      <frequency value="100"/>
      <swankness>SWANKNESS_1</swankness>
      <maxNum value="1"/>
      <flags>FLAG_HAS_LIVERY FLAG_EXTRAS_STRONG FLAG_AVERAGE_CAR FLAG_IS_OFFROAD_VEHICLE FLAG_IS_BULKY FLAG_HAS_INTERIOR_EXTRAS</flags>
      <type>VEHICLE_TYPE_CAR</type>
      <plateType>VPT_FRONT_AND_BACK_PLATES</plateType>
      <vehicleClass>VC_OFF_ROAD</vehicleClass>
      <dashboardType>VDT_BANSHEE</dashboardType>
      <wheelType>VWT_OFFROAD</wheelType>
      <trailers/>
      <additionalTrailers>
        <Item>boattrailer</Item>
        <Item>trailersmall</Item>
        <Item>trailersmall2</Item>
      </additionalTrailers>
      <drivers/>
      <extraIncludes/>
      <doorsWithCollisionWhenClosed>
        <Item>VEH_EXT_BOOT</Item>
      </doorsWithCollisionWhenClosed>
      <driveableDoors/>
      <bumpersNeedToCollideWithMap value="true"/>
      <needsRopeTexture value="false"/>
      <requiredExtras/>
      <rewards/>
      <cinematicPartCamera>
        <Item>WHEEL_FRONT_RIGHT_CAMERA</Item>
        <Item>WHEEL_FRONT_LEFT_CAMERA</Item>
        <Item>WHEEL_REAR_RIGHT_CAMERA</Item>
        <Item>WHEEL_REAR_LEFT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet>Truck</NmBraceOverrideSet>
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000"/>
      <buoyancySphereSizeScale value="1.000000"/>
      <pOverrideRagdollThreshold type="NULL"/>
      <firstPersonDrivebyData>
        <Item>RANGER_MESA_FRONT_LEFT</Item>
        <Item>VAN_SPEEDO_FRONT_RIGHT</Item>
        <Item>RANGER_CRUSADER_REAR_LEFT</Item>
        <Item>RANGER_CRUSADER_REAR_RIGHT</Item>
      </firstPersonDrivebyData>
    </Item>
    <Item>
      <modelName>sandstormxl</modelName>
      <txdName>sandstormxl</txdName>
      <handlingId>SANDSTORMXL</handlingId>
      <gameName>SANDSTORMXL</gameName>
      <vehicleMakeName>VAPID</vehicleMakeName>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>va_sandstorm</animConvRoofDictName>
      <animConvRoofName>sandstorm</animConvRoofName>
      <animConvRoofWindowsAffected/>
      <ptfxAssetName>null</ptfxAssetName>
      <audioNameHash>caracara2</audioNameHash>
      <layout>LAYOUT_STD_GRANGER2</layout>
      <coverBoundOffsets>MESA_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout/>
      <cameraName>DEFAULT_FOLLOW_VEHICLE_CAMERA</cameraName>
      <aimCameraName>BOX_VEHICLE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>VEHICLE_BONNET_CAMERA_STANDARD_LONG</bonnetCameraName>
      <povCameraName>DEFAULT_POV_CAMERA_LOOKAROUND_MID</povCameraName>
      <FirstPersonDriveByIKOffset x="-0.018000" y="-0.043000" z="-0.020000"/>
      <FirstPersonDriveByUnarmedIKOffset x="0.000000" y="0.000000" z="-0.020000"/>
      <FirstPersonProjectileDriveByIKOffset x="0.080000" y="0.000000" z="-0.060000"/>
      <FirstPersonProjectileDriveByPassengerIKOffset x="0.020000" y="0.060000" z="-0.090000"/>
      <FirstPersonProjectileDriveByRearLeftIKOffset x="0.000000" y="0.000000" z="-0.058000"/>
      <FirstPersonProjectileDriveByRearRightIKOffset x="0.000000" y="0.000000" z="-0.058000"/>
      <FirstPersonDriveByLeftPassengerIKOffset x="-0.030000" y="0.010000" z="-0.076000"/>
      <FirstPersonDriveByRightPassengerIKOffset x="0.000000" y="0.000000" z="-0.040000"/>
      <FirstPersonDriveByRightRearPassengerIKOffset x="0.015000" y="0.010000" z="-0.076000"/>
      <FirstPersonDriveByLeftPassengerUnarmedIKOffset x="0.000000" y="-0.150000" z="0.010000"/>
      <FirstPersonDriveByRightPassengerUnarmedIKOffset x="0.000000" y="-0.150000" z="0.010000"/>
      <FirstPersonMobilePhoneOffset x="0.150000" y="0.260000" z="0.503000"/>
      <FirstPersonPassengerMobilePhoneOffset x="0.136000" y="0.223000" z="0.445000"/>
      <FirstPersonMobilePhoneSeatIKOffset>
        <Item>
          <Offset x="0.154000" y="0.076000" z="0.478000"/>
          <SeatIndex value="2"/>
        </Item>
        <Item>
          <Offset x="0.154000" y="0.076000" z="0.478000"/>
          <SeatIndex value="3"/>
        </Item>
      </FirstPersonMobilePhoneSeatIKOffset>
      <PovCameraOffset x="0.000000" y="-0.120000" z="0.580000"/>
      <PovCameraVerticalAdjustmentForRollCage value="0.000000"/>
      <PovPassengerCameraOffset x="0.000000" y="0.010000" z="0.075000"/>
      <PovRearPassengerCameraOffset x="0.000000" y="-0.190000" z="0.145000"/>
      <vfxInfoName>VFXVEHICLEINFO_CAR_OFFROAD</vfxInfoName>
      <shouldUseCinematicViewMode value="true"/>
      <shouldCameraTransitionOnClimbUpDown value="false"/>
      <shouldCameraIgnoreExiting value="false"/>
      <AllowPretendOccupants value="true"/>
      <AllowJoyriding value="true"/>
      <AllowSundayDriving value="true"/>
      <AllowBodyColorMapping value="true"/>
      <wheelScale value="0.269000"/>
      <wheelScaleRear value="0.269000"/>
      <dirtLevelMin value="0.000000"/>
      <dirtLevelMax value="0.850000"/>
      <envEffScaleMin value="0.000000"/>
      <envEffScaleMax value="1.000000"/>
      <envEffScaleMin2 value="0.000000"/>
      <envEffScaleMax2 value="1.000000"/>
      <damageMapScale value="0.600000"/>
      <damageOffsetScale value="1.000000"/>
      <diffuseTint value="0xC4000000"/>
      <steerWheelMult value="1.000000"/>
      <HDTextureDist value="5.000000"/>
      <lodDistances content="float_array">
        15.000000
        30.000000
        70.000000	
        140.000000	
        500.000000	
        500.000000
      </lodDistances>
      <minSeatHeight value="0.846"/>
      <identicalModelSpawnDistance value="20"/>
      <maxNumOfSameColor value="10"/>
      <defaultBodyHealth value="1000.000000"/>
      <pretendOccupantsScale value="1.000000"/>
      <visibleSpawnDistScale value="1.000000"/>
      <trackerPathWidth value="2.000000"/>
      <weaponForceMult value="0.900000"/>
      <frequency value="100"/>
      <swankness>SWANKNESS_1</swankness>
      <maxNum value="1"/>
      <flags>FLAG_HAS_LIVERY FLAG_EXTRAS_STRONG FLAG_AVERAGE_CAR FLAG_IS_OFFROAD_VEHICLE FLAG_IS_BULKY FLAG_HAS_INTERIOR_EXTRAS</flags>
      <type>VEHICLE_TYPE_CAR</type>
      <plateType>VPT_FRONT_AND_BACK_PLATES</plateType>
      <vehicleClass>VC_OFF_ROAD</vehicleClass>
      <dashboardType>VDT_BANSHEE</dashboardType>
      <wheelType>VWT_OFFROAD</wheelType>
      <trailers/>
      <additionalTrailers>
        <Item>boattrailer</Item>
        <Item>trailersmall</Item>
        <Item>trailersmall2</Item>
      </additionalTrailers>
      <drivers/>
      <extraIncludes/>
      <doorsWithCollisionWhenClosed>
        <Item>VEH_EXT_BOOT</Item>
      </doorsWithCollisionWhenClosed>
      <driveableDoors/>
      <bumpersNeedToCollideWithMap value="true"/>
      <needsRopeTexture value="false"/>
      <requiredExtras/>
      <rewards/>
      <cinematicPartCamera>
        <Item>WHEEL_FRONT_RIGHT_CAMERA</Item>
        <Item>WHEEL_FRONT_LEFT_CAMERA</Item>
        <Item>WHEEL_REAR_RIGHT_CAMERA</Item>
        <Item>WHEEL_REAR_LEFT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet>Truck</NmBraceOverrideSet>
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000"/>
      <buoyancySphereSizeScale value="1.000000"/>
      <pOverrideRagdollThreshold type="NULL"/>
      <firstPersonDrivebyData>
        <Item>RANGER_MESA_FRONT_LEFT</Item>
        <Item>VAN_SPEEDO_FRONT_RIGHT</Item>
        <Item>RANGER_CRUSADER_REAR_LEFT</Item>
        <Item>RANGER_CRUSADER_REAR_RIGHT</Item>
      </firstPersonDrivebyData>
    </Item>
    <Item>
      <modelName>savanna</modelName>
      <txdName>savanna</txdName>
      <handlingId>SAVANNA</handlingId>
      <gameName>SAVANNA</gameName>
      <vehicleMakeName>COIL</vehicleMakeName>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected/>
      <ptfxAssetName>null</ptfxAssetName>
      <audioNameHash>raiden</audioNameHash>
      <layout>LAYOUT_STD_NEON</layout>
      <coverBoundOffsets>RAIDEN_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout/>
      <cameraName>FOLLOW_JEEP_CAMERA</cameraName>
      <aimCameraName>DEFAULT_THIRD_PERSON_VEHICLE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>VEHICLE_BONNET_CAMERA_SPEEDO4</bonnetCameraName>
      <povCameraName>REDUCED_NEAR_CLIP_POV_CAMERA</povCameraName>
      <FirstPersonDriveByIKOffset x="0.070000" y="-0.100000" z="-0.050000"/>
      <FirstPersonDriveByUnarmedIKOffset x="-0.020000" y="-0.100000" z="-0.020000"/>
      <FirstPersonProjectileDriveByIKOffset x="0.070000" y="-0.180000" z="-0.050000"/>
      <FirstPersonProjectileDriveByPassengerIKOffset x="-0.100000" y="-0.170000" z="-0.060000"/>
      <FirstPersonProjectileDriveByRearLeftIKOffset x="-0.080000" y="-0.070000" z="-0.050000"/>
      <FirstPersonProjectileDriveByRearRightIKOffset x="0.020000" y="-0.050000" z="-0.070000"/>
      <FirstPersonDriveByLeftPassengerIKOffset x="0.040000" y="-0.070000" z="-0.010000"/>
      <FirstPersonDriveByRightPassengerIKOffset x="-0.060000" y="-0.100000" z="-0.050000"/>
      <FirstPersonDriveByRightRearPassengerIKOffset x="-0.010000" y="-0.050000" z="-0.010000"/>
      <FirstPersonDriveByLeftPassengerUnarmedIKOffset x="0.000000" y="-0.125000" z="0.000000"/>
      <FirstPersonDriveByRightPassengerUnarmedIKOffset x="-0.020000" y="-0.100000" z="-0.020000"/>
      <FirstPersonMobilePhoneOffset x="0.120000" y="0.110000" z="0.540000"/>
      <FirstPersonPassengerMobilePhoneOffset x="0.210000" y="0.085000" z="0.416000"/>
      <FirstPersonMobilePhoneSeatIKOffset>
        <Item>
          <Offset x="0.180000" y="-0.020000" z="0.524000"/>
          <SeatIndex value="2"/>
        </Item>
        <Item>
          <Offset x="0.190000" y="-0.020000" z="0.524000"/>
          <SeatIndex value="3"/>
        </Item>
      </FirstPersonMobilePhoneSeatIKOffset>
      <PovCameraOffset x="0.000000" y="-0.270000" z="0.640000"/>
      <PovCameraVerticalAdjustmentForRollCage value="0.000000"/>
      <PovPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000"/>
      <PovRearPassengerCameraOffset x="0.000000" y="-0.025000" z="0.090000"/>
      <vfxInfoName>VFXVEHICLEINFO_CAR_ELECTRIC</vfxInfoName>
      <shouldUseCinematicViewMode value="true"/>
      <shouldCameraTransitionOnClimbUpDown value="false"/>
      <shouldCameraIgnoreExiting value="false"/>
      <AllowPretendOccupants value="true"/>
      <AllowJoyriding value="true"/>
      <AllowSundayDriving value="true"/>
      <AllowBodyColorMapping value="true"/>
      <wheelScale value="0.303500"/>
      <wheelScaleRear value="0.303500"/>
      <dirtLevelMin value="0.700000"/>
      <dirtLevelMax value="0.000000"/>
      <envEffScaleMin value="0.000000"/>
      <envEffScaleMax value="1.000000"/>
      <envEffScaleMin2 value="0.000000"/>
      <envEffScaleMax2 value="1.000000"/>
      <damageMapScale value="0.5"/>
      <damageOffsetScale value="0.5"/>
      <diffuseTint value="0xAA0A0A0A"/>
      <steerWheelMult value="0.700000"/>
      <HDTextureDist value="5.000000"/>
      <lodDistances content="float_array">20.000000 35.000000 90.000000 180.000000 500.000000 500.000000</lodDistances>
      <identicalModelSpawnDistance value="20"/>
      <maxNumOfSameColor value="10"/>
      <defaultBodyHealth value="1000.000000"/>
      <pretendOccupantsScale value="1.000000"/>
      <visibleSpawnDistScale value="1.000000"/>
      <trackerPathWidth value="2.000000"/>
      <weaponForceMult value="1.000000"/>
      <frequency value="50"/>
      <swankness>SWANKNESS_5</swankness>
      <maxNum value="10"/>
      <flags>FLAG_RICH_CAR FLAG_AVERAGE_CAR FLAG_NO_BROKEN_DOWN_SCENARIO FLAG_IS_OFFROAD_VEHICLE FLAG_DISABLE_THROUGH_WINDSCREEN FLAG_IS_ELECTRIC FLAG_FAKE_EXTRALIGHTS FLAG_HAS_RAMMING_BAR_MOD</flags>
      <type>VEHICLE_TYPE_CAR</type>
      <plateType>VPT_BACK_PLATES</plateType>
      <dashboardType>VDT_BANSHEE</dashboardType>
      <vehicleClass>VC_OFF_ROAD</vehicleClass>
      <wheelType>VWT_OFFROAD</wheelType>
      <trailers/>
      <additionalTrailers/>
      <drivers/>
      <extraIncludes/>
      <doorsWithCollisionWhenClosed/>
      <driveableDoors/>
      <bumpersNeedToCollideWithMap value="false"/>
      <needsRopeTexture value="false"/>
      <requiredExtras/>
      <rewards/>
      <cinematicPartCamera>
        <Item>WHEEL_FRONT_RIGHT_CAMERA</Item>
        <Item>WHEEL_FRONT_LEFT_CAMERA</Item>
        <Item>WHEEL_REAR_RIGHT_CAMERA</Item>
        <Item>WHEEL_REAR_LEFT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet/>
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000"/>
      <buoyancySphereSizeScale value="1.000000"/>
      <pOverrideRagdollThreshold type="NULL"/>
      <firstPersonDrivebyData>
        <Item>STD_RAIDEN_FRONT_LEFT</Item>
        <Item>STD_RAIDEN_FRONT_RIGHT</Item>
        <Item>STD_RAIDEN_REAR_LEFT</Item>
        <Item>STD_RAIDEN_REAR_RIGHT</Item>
      </firstPersonDrivebyData>
    </Item>
    <Item>
      <modelName>seraph3</modelName>
      <txdName>seraph3</txdName>
      <handlingId>SERAPH3</handlingId>
      <gameName>SERAPH3</gameName>
      <vehicleMakeName>UBERMACH</vehicleMakeName>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected/>
      <ptfxAssetName>weap_xs_veh_weapons</ptfxAssetName>
      <audioNameHash>SULTAN</audioNameHash>
      <layout>LAYOUT_STANDARD</layout>
      <coverBoundOffsets>SULTAN_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout/>
      <cameraName>DEFAULT_FOLLOW_VEHICLE_CAMERA</cameraName>
      <aimCameraName>MID_BOX_VEHICLE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>VEHICLE_BONNET_CAMERA_MID_HIGH</bonnetCameraName>
      <povCameraName>DEFAULT_POV_CAMERA</povCameraName>
      <FirstPersonDriveByIKOffset x="0.000000" y="-0.050000" z="0.000000"/>
      <FirstPersonDriveByUnarmedIKOffset x="0.000000" y="0.000000" z="0.-020000"/>
      <FirstPersonProjectileDriveByIKOffset x="0.035000" y="-0.125000" z="-0.035000"/>
      <FirstPersonProjectileDriveByPassengerIKOffset x="-0.120000" y="-0.125000" z="-0.050000"/>
      <FirstPersonProjectileDriveByRearLeftIKOffset x="0.000000" y="0.020000" z="0.030000"/>
      <FirstPersonProjectileDriveByRearRightIKOffset x="0.000000" y="0.020000" z="0.030000"/>
      <FirstPersonDriveByLeftPassengerIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonDriveByRightPassengerIKOffset x="0.000000" y="-0.050000" z="0.000000"/>
      <FirstPersonDriveByRightRearPassengerIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonDriveByLeftPassengerUnarmedIKOffset x="0.000000" y="-0.020000" z="0.010000"/>
      <FirstPersonDriveByRightPassengerUnarmedIKOffset x="0.000000" y="-0.020000" z="0.010000"/>
      <FirstPersonMobilePhoneOffset x="0.150000" y="0.213000" z="0.511000"/>
      <FirstPersonPassengerMobilePhoneOffset x="0.136000" y="0.223000" z="0.385000"/>
      <FirstPersonMobilePhoneSeatIKOffset>
        <Item>
          <Offset x="0.136000" y="0.086000" z="0.435000"/>
          <SeatIndex value="2"/>
        </Item>
        <Item>
          <Offset x="0.136000" y="0.086000" z="0.435000"/>
          <SeatIndex value="3"/>
        </Item>
      </FirstPersonMobilePhoneSeatIKOffset>
      <PovCameraOffset x="0.000000" y="-0.180000" z="0.640000"/>
      <PovCameraVerticalAdjustmentForRollCage value="0.000000"/>
      <PovPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000"/>
      <PovRearPassengerCameraOffset x="0.000000" y="-0.040000" z="0.000000"/>
      <vfxInfoName>VFXVEHICLEINFO_CAR_GENERIC</vfxInfoName>
      <shouldUseCinematicViewMode value="true"/>
      <shouldCameraTransitionOnClimbUpDown value="false"/>
      <shouldCameraIgnoreExiting value="false"/>
      <AllowPretendOccupants value="true"/>
      <AllowJoyriding value="true"/>
      <AllowSundayDriving value="true"/>
      <AllowBodyColorMapping value="true"/>
      <wheelScale value="0.2371"/>
      <wheelScaleRear value="0.2371"/>
      <dirtLevelMin value="0.000000"/>
      <dirtLevelMax value="0.550000"/>
      <envEffScaleMin value="0.000000"/>
      <envEffScaleMax value="1.000000"/>
      <envEffScaleMin2 value="0.000000"/>
      <envEffScaleMax2 value="1.000000"/>
      <damageMapScale value="0.450000"/>
      <damageOffsetScale value="0.450000"/>
      <diffuseTint value="0x00FFFFFF"/>
      <steerWheelMult value="1.000000"/>
      <HDTextureDist value="5.000000"/>
      <lodDistances content="float_array">
        15.000000
        30.000000
        70.000000	
        140.000000	
        500.000000	
        500.000000
      </lodDistances>
      <minSeatHeight value="0.867"/>
      <identicalModelSpawnDistance value="20"/>
      <maxNumOfSameColor value="10"/>
      <defaultBodyHealth value="1000.000000"/>
      <pretendOccupantsScale value="1.000000"/>
      <visibleSpawnDistScale value="1.000000"/>
      <trackerPathWidth value="2.000000"/>
      <weaponForceMult value="1.000000"/>
      <frequency value="100"/>
      <swankness>SWANKNESS_3</swankness>
      <maxNum value="999"/>
      <flags>FLAG_AVERAGE_CAR FLAG_HAS_INTERIOR_EXTRAS FLAG_HAS_NITROUS_MOD</flags>
      <type>VEHICLE_TYPE_CAR</type>
      <plateType>VPT_FRONT_AND_BACK_PLATES</plateType>
      <dashboardType>VDT_SULTAN</dashboardType>
      <vehicleClass>VC_SEDAN</vehicleClass>
      <wheelType>VWT_SPORT</wheelType>
      <trailers/>
      <additionalTrailers/>
      <drivers/>
      <extraIncludes/>
      <doorsWithCollisionWhenClosed/>
      <driveableDoors/>
      <bumpersNeedToCollideWithMap value="false"/>
      <needsRopeTexture value="false"/>
      <requiredExtras/>
      <rewards/>
      <cinematicPartCamera>
        <Item>WHEEL_FRONT_RIGHT_CAMERA</Item>
        <Item>WHEEL_FRONT_LEFT_CAMERA</Item>
        <Item>WHEEL_REAR_RIGHT_CAMERA</Item>
        <Item>WHEEL_REAR_LEFT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet/>
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000"/>
      <buoyancySphereSizeScale value="1.000000"/>
      <pOverrideRagdollThreshold type="NULL"/>
      <firstPersonDrivebyData>
        <Item>STD_SULTAN_FRONT_LEFT</Item>
        <Item>STD_SULTAN_FRONT_RIGHT</Item>
        <Item>STD_SULTAN_REAR_LEFT</Item>
        <Item>STD_SULTAN_REAR_RIGHT</Item>
      </firstPersonDrivebyData>
    </Item>
    <Item>
      <modelName>sentinelsg3</modelName>
      <txdName>sentinelsg3</txdName>
      <handlingId>SENTINELSG3</handlingId>
      <gameName>SG3</gameName>
      <vehicleMakeName>UBERMACH</vehicleMakeName>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected/>
      <ptfxAssetName>weap_xs_vehicle_weapons</ptfxAssetName>
      <audioNameHash>sentinel3</audioNameHash>
      <layout>LAYOUT_STD_EXITFIXUP</layout>
      <coverBoundOffsets>F620_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout/>
      <cameraName>DEFAULT_FOLLOW_VEHICLE_CAMERA</cameraName>
      <aimCameraName>MID_BOX_VEHICLE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>VEHICLE_BONNET_CAMERA_NEAR_EXTRA_HIGH</bonnetCameraName>
      <povCameraName>DEFAULT_POV_CAMERA</povCameraName>
      <FirstPersonDriveByIKOffset x="0.000000" y="-0.060000" z="-0.020000"/>
      <FirstPersonDriveByUnarmedIKOffset x="0.000000" y="-0.100000" z="0.000000"/>
      <FirstPersonProjectileDriveByIKOffset x="0.000000" y="-0.110000" z="0.010000"/>
      <FirstPersonProjectileDriveByPassengerIKOffset x="-0.080000" y="-0.110000" z="0.010000"/>
      <FirstPersonProjectileDriveByRearLeftIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonProjectileDriveByRearRightIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonDriveByLeftPassengerIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonDriveByRightPassengerIKOffset x="0.000000" y="-0.060000" z="-0.020000"/>
      <FirstPersonDriveByRightRearPassengerIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonDriveByLeftPassengerUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonDriveByRightPassengerUnarmedIKOffset x="0.000000" y="-0.140000" z="0.000000"/>
      <FirstPersonMobilePhoneOffset x="0.133000" y="0.208000" z="0.569000"/>
      <FirstPersonPassengerMobilePhoneOffset x="0.136000" y="0.143000" z="0.485000"/>
      <PovCameraOffset x="0.000000" y="-0.225000" z="0.680000"/>
      <PovCameraVerticalAdjustmentForRollCage value="0.000000"/>
      <PovPassengerCameraOffset x="0.000000" y="-0.050000" z="0.000000"/>
      <PovRearPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000"/>
      <vfxInfoName>VFXVEHICLEINFO_CAR_GENERIC</vfxInfoName>
      <shouldUseCinematicViewMode value="true"/>
      <shouldCameraTransitionOnClimbUpDown value="false"/>
      <shouldCameraIgnoreExiting value="false"/>
      <AllowPretendOccupants value="true"/>
      <AllowJoyriding value="true"/>
      <AllowSundayDriving value="true"/>
      <AllowBodyColorMapping value="true"/>
      <wheelScale value="0.260611"/>
      <wheelScaleRear value="0.260611"/>
      <dirtLevelMin value="0.000000"/>
      <dirtLevelMax value="0.550000"/>
      <envEffScaleMin value="0.000000"/>
      <envEffScaleMax value="1.000000"/>
      <envEffScaleMin2 value="0.000000"/>
      <envEffScaleMax2 value="1.000000"/>
      <damageMapScale value="0.600000"/>
      <damageOffsetScale value="1.000000"/>
      <diffuseTint value="0x00FFFFFF"/>
      <steerWheelMult value="0.800000"/>
      <HDTextureDist value="5.000000"/>
      <lodDistances content="float_array">
        15.000000
        30.000000
        60.000000	
        120.000000	
        500.000000	
        500.000000
      </lodDistances>
      <minSeatHeight value="0.852"/>
      <identicalModelSpawnDistance value="20"/>
      <maxNumOfSameColor value="1"/>
      <defaultBodyHealth value="1000.000000"/>
      <pretendOccupantsScale value="1.000000"/>
      <visibleSpawnDistScale value="1.000000"/>
      <trackerPathWidth value="2.000000"/>
      <weaponForceMult value="1.000000"/>
      <frequency value="80"/>
      <swankness>SWANKNESS_3</swankness>
      <maxNum value="3"/>
      <flags>FLAG_SPORTS FLAG_SPAWN_ON_TRAILER FLAG_PARKING_SENSORS FLAG_AVERAGE_CAR FLAG_RECESSED_TAILLIGHT_CORONAS  FLAG_HAS_LIVERY</flags>
      <type>VEHICLE_TYPE_CAR</type>
      <plateType>VPT_FRONT_AND_BACK_PLATES</plateType>
      <dashboardType>VDT_BANSHEE</dashboardType>
      <vehicleClass>VC_COUPE</vehicleClass>
      <wheelType>VWT_SPORT</wheelType>
      <trailers/>
      <additionalTrailers/>
      <drivers/>
      <extraIncludes/>
      <doorsWithCollisionWhenClosed/>
      <driveableDoors/>
      <bumpersNeedToCollideWithMap value="false"/>
      <needsRopeTexture value="false"/>
      <requiredExtras/>
      <rewards/>
      <cinematicPartCamera>
        <Item>WHEEL_FRONT_RIGHT_CAMERA</Item>
        <Item>WHEEL_FRONT_LEFT_CAMERA</Item>
        <Item>WHEEL_REAR_RIGHT_CAMERA</Item>
        <Item>WHEEL_REAR_LEFT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet/>
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000"/>
      <buoyancySphereSizeScale value="1.000000"/>
      <pOverrideRagdollThreshold type="NULL"/>
      <firstPersonDrivebyData>
        <Item>LOW_F620_FRONT_LEFT</Item>
        <Item>LOW_F620_FRONT_RIGHT</Item>
      </firstPersonDrivebyData>
    </Item>
    <Item>
      <modelName>sentinelsg32</modelName>
      <txdName>sentinelsg32</txdName>
      <handlingId>SENTINELSG32</handlingId>
      <gameName>SG32A</gameName>
      <vehicleMakeName>UBERMACH</vehicleMakeName>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected/>
      <ptfxAssetName>weap_xs_vehicle_weapons</ptfxAssetName>
      <audioNameHash>sultan</audioNameHash>
      <layout>LAYOUT_STD_EXITFIXUP</layout>
      <coverBoundOffsets>F620_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout/>
      <cameraName>DEFAULT_FOLLOW_VEHICLE_CAMERA</cameraName>
      <aimCameraName>MID_BOX_VEHICLE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>VEHICLE_BONNET_CAMERA_NEAR_EXTRA_HIGH</bonnetCameraName>
      <povCameraName>DEFAULT_POV_CAMERA</povCameraName>
      <FirstPersonDriveByIKOffset x="0.000000" y="-0.060000" z="-0.020000"/>
      <FirstPersonDriveByUnarmedIKOffset x="0.000000" y="-0.100000" z="0.000000"/>
      <FirstPersonProjectileDriveByIKOffset x="0.000000" y="-0.110000" z="0.010000"/>
      <FirstPersonProjectileDriveByPassengerIKOffset x="-0.080000" y="-0.110000" z="0.010000"/>
      <FirstPersonProjectileDriveByRearLeftIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonProjectileDriveByRearRightIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonDriveByLeftPassengerIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonDriveByRightPassengerIKOffset x="0.000000" y="-0.060000" z="-0.020000"/>
      <FirstPersonDriveByRightRearPassengerIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonDriveByLeftPassengerUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonDriveByRightPassengerUnarmedIKOffset x="0.000000" y="-0.140000" z="0.000000"/>
      <FirstPersonMobilePhoneOffset x="0.133000" y="0.208000" z="0.569000"/>
      <FirstPersonPassengerMobilePhoneOffset x="0.136000" y="0.143000" z="0.485000"/>
      <PovCameraOffset x="0.000000" y="-0.225000" z="0.680000"/>
      <PovCameraVerticalAdjustmentForRollCage value="0.000000"/>
      <PovPassengerCameraOffset x="0.000000" y="-0.050000" z="0.000000"/>
      <PovRearPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000"/>
      <vfxInfoName>VFXVEHICLEINFO_CAR_GENERIC</vfxInfoName>
      <shouldUseCinematicViewMode value="true"/>
      <shouldCameraTransitionOnClimbUpDown value="false"/>
      <shouldCameraIgnoreExiting value="false"/>
      <AllowPretendOccupants value="true"/>
      <AllowJoyriding value="true"/>
      <AllowSundayDriving value="true"/>
      <AllowBodyColorMapping value="true"/>
      <wheelScale value="0.231512"/>
      <wheelScaleRear value="0.231512"/>
      <dirtLevelMin value="0.000000"/>
      <dirtLevelMax value="0.550000"/>
      <envEffScaleMin value="0.000000"/>
      <envEffScaleMax value="1.000000"/>
      <envEffScaleMin2 value="0.000000"/>
      <envEffScaleMax2 value="1.000000"/>
      <damageMapScale value="0.600000"/>
      <damageOffsetScale value="1.000000"/>
      <diffuseTint value="0x00FFFFFF"/>
      <steerWheelMult value="0.800000"/>
      <HDTextureDist value="5.000000"/>
      <lodDistances content="float_array">
        15.000000
        30.000000
        60.000000	
        120.000000	
        500.000000	
        500.000000
      </lodDistances>
      <minSeatHeight value="0.852"/>
      <identicalModelSpawnDistance value="20"/>
      <maxNumOfSameColor value="1"/>
      <defaultBodyHealth value="1000.000000"/>
      <pretendOccupantsScale value="1.000000"/>
      <visibleSpawnDistScale value="1.000000"/>
      <trackerPathWidth value="2.000000"/>
      <weaponForceMult value="1.000000"/>
      <frequency value="80"/>
      <swankness>SWANKNESS_3</swankness>
      <maxNum value="3"/>
      <flags>FLAG_SPORTS FLAG_SPAWN_ON_TRAILER FLAG_PARKING_SENSORS FLAG_AVERAGE_CAR FLAG_RECESSED_TAILLIGHT_CORONAS  FLAG_HAS_LIVERY</flags>
      <type>VEHICLE_TYPE_CAR</type>
      <plateType>VPT_FRONT_AND_BACK_PLATES</plateType>
      <dashboardType>VDT_BANSHEE</dashboardType>
      <vehicleClass>VC_COUPE</vehicleClass>
      <wheelType>VWT_SPORT</wheelType>
      <trailers/>
      <additionalTrailers/>
      <drivers/>
      <extraIncludes/>
      <doorsWithCollisionWhenClosed/>
      <driveableDoors/>
      <bumpersNeedToCollideWithMap value="false"/>
      <needsRopeTexture value="false"/>
      <requiredExtras/>
      <rewards/>
      <cinematicPartCamera>
        <Item>WHEEL_FRONT_RIGHT_CAMERA</Item>
        <Item>WHEEL_FRONT_LEFT_CAMERA</Item>
        <Item>WHEEL_REAR_RIGHT_CAMERA</Item>
        <Item>WHEEL_REAR_LEFT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet/>
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000"/>
      <buoyancySphereSizeScale value="1.000000"/>
      <pOverrideRagdollThreshold type="NULL"/>
      <firstPersonDrivebyData>
        <Item>LOW_F620_FRONT_LEFT</Item>
        <Item>LOW_F620_FRONT_RIGHT</Item>
      </firstPersonDrivebyData>
    </Item>
    <Item>
      <modelName>sentinelsg3a</modelName>
      <txdName>sentinelsg3a</txdName>
      <handlingId>SENTINELSG3A</handlingId>
      <gameName>SG32</gameName>
      <vehicleMakeName>UBERMACH</vehicleMakeName>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected/>
      <ptfxAssetName>weap_xs_vehicle_weapons</ptfxAssetName>
      <audioNameHash>SULTAN</audioNameHash>
      <layout>LAYOUT_STANDARD</layout>
      <coverBoundOffsets>F620_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout/>
      <cameraName>DEFAULT_FOLLOW_VEHICLE_CAMERA</cameraName>
      <aimCameraName>MID_BOX_VEHICLE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>VEHICLE_BONNET_CAMERA_NEAR_EXTRA_HIGH</bonnetCameraName>
      <povCameraName>DEFAULT_POV_CAMERA</povCameraName>
      <FirstPersonDriveByIKOffset x="0.000000" y="-0.060000" z="-0.020000"/>
      <FirstPersonDriveByUnarmedIKOffset x="0.000000" y="-0.100000" z="0.000000"/>
      <FirstPersonProjectileDriveByIKOffset x="0.000000" y="-0.110000" z="0.010000"/>
      <FirstPersonProjectileDriveByPassengerIKOffset x="-0.080000" y="-0.110000" z="0.010000"/>
      <FirstPersonProjectileDriveByRearLeftIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonProjectileDriveByRearRightIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonDriveByLeftPassengerIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonDriveByRightPassengerIKOffset x="0.000000" y="-0.060000" z="-0.020000"/>
      <FirstPersonDriveByRightRearPassengerIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonDriveByLeftPassengerUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonDriveByRightPassengerUnarmedIKOffset x="0.000000" y="-0.140000" z="0.000000"/>
      <FirstPersonMobilePhoneOffset x="0.133000" y="0.208000" z="0.569000"/>
      <FirstPersonPassengerMobilePhoneOffset x="0.136000" y="0.143000" z="0.485000"/>
      <PovCameraOffset x="0.000000" y="-0.225000" z="0.680000"/>
      <PovCameraVerticalAdjustmentForRollCage value="0.000000"/>
      <PovPassengerCameraOffset x="0.000000" y="-0.050000" z="0.000000"/>
      <PovRearPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000"/>
      <vfxInfoName>VFXVEHICLEINFO_CAR_GENERIC</vfxInfoName>
      <shouldUseCinematicViewMode value="true"/>
      <shouldCameraTransitionOnClimbUpDown value="false"/>
      <shouldCameraIgnoreExiting value="false"/>
      <AllowPretendOccupants value="true"/>
      <AllowJoyriding value="true"/>
      <AllowSundayDriving value="true"/>
      <AllowBodyColorMapping value="true"/>
      <wheelScale value="0.260611"/>
      <wheelScaleRear value="0.260611"/>
      <dirtLevelMin value="0.000000"/>
      <dirtLevelMax value="0.550000"/>
      <envEffScaleMin value="0.000000"/>
      <envEffScaleMax value="1.000000"/>
      <envEffScaleMin2 value="0.000000"/>
      <envEffScaleMax2 value="1.000000"/>
      <damageMapScale value="0.600000"/>
      <damageOffsetScale value="1.000000"/>
      <diffuseTint value="0x00FFFFFF"/>
      <steerWheelMult value="0.800000"/>
      <HDTextureDist value="5.000000"/>
      <lodDistances content="float_array">
        15.000000
        30.000000
        60.000000	
        120.000000	
        500.000000	
        500.000000
      </lodDistances>
      <minSeatHeight value="0.852"/>
      <identicalModelSpawnDistance value="20"/>
      <maxNumOfSameColor value="1"/>
      <defaultBodyHealth value="1000.000000"/>
      <pretendOccupantsScale value="1.000000"/>
      <visibleSpawnDistScale value="1.000000"/>
      <trackerPathWidth value="2.000000"/>
      <weaponForceMult value="1.000000"/>
      <frequency value="80"/>
      <swankness>SWANKNESS_3</swankness>
      <maxNum value="3"/>
      <flags>FLAG_SPORTS FLAG_SPAWN_ON_TRAILER FLAG_PARKING_SENSORS FLAG_AVERAGE_CAR FLAG_RECESSED_TAILLIGHT_CORONAS  FLAG_HAS_LIVERY</flags>
      <type>VEHICLE_TYPE_CAR</type>
      <plateType>VPT_FRONT_AND_BACK_PLATES</plateType>
      <dashboardType>VDT_BANSHEE</dashboardType>
      <vehicleClass>VC_SEDAN</vehicleClass>
      <wheelType>VWT_SPORT</wheelType>
      <trailers/>
      <additionalTrailers/>
      <drivers/>
      <extraIncludes/>
      <doorsWithCollisionWhenClosed/>
      <driveableDoors/>
      <bumpersNeedToCollideWithMap value="false"/>
      <needsRopeTexture value="false"/>
      <requiredExtras/>
      <rewards/>
      <cinematicPartCamera>
        <Item>WHEEL_FRONT_RIGHT_CAMERA</Item>
        <Item>WHEEL_FRONT_LEFT_CAMERA</Item>
        <Item>WHEEL_REAR_RIGHT_CAMERA</Item>
        <Item>WHEEL_REAR_LEFT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet/>
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000"/>
      <buoyancySphereSizeScale value="1.000000"/>
      <pOverrideRagdollThreshold type="NULL"/>
      <firstPersonDrivebyData>
        <Item>STD_SULTAN_FRONT_LEFT</Item>
        <Item>STD_SULTAN_FRONT_RIGHT</Item>
        <Item>STD_SULTAN_REAR_LEFT</Item>
        <Item>STD_SULTAN_REAR_RIGHT</Item>
      </firstPersonDrivebyData>
    </Item>
    <Item>
      <modelName>sentinelsg3a2</modelName>
      <txdName>sentinelsg3a2</txdName>
      <handlingId>SENTINELSG3A2</handlingId>
      <gameName>SG3A2</gameName>
      <vehicleMakeName>UBERMACH</vehicleMakeName>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected/>
      <ptfxAssetName>weap_xs_vehicle_weapons</ptfxAssetName>
      <audioNameHash>sultan</audioNameHash>
      <layout>LAYOUT_STANDARD</layout>
      <coverBoundOffsets>F620_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout/>
      <cameraName>DEFAULT_FOLLOW_VEHICLE_CAMERA</cameraName>
      <aimCameraName>MID_BOX_VEHICLE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>VEHICLE_BONNET_CAMERA_NEAR_EXTRA_HIGH</bonnetCameraName>
      <povCameraName>DEFAULT_POV_CAMERA</povCameraName>
      <FirstPersonDriveByIKOffset x="0.000000" y="-0.060000" z="-0.020000"/>
      <FirstPersonDriveByUnarmedIKOffset x="0.000000" y="-0.100000" z="0.000000"/>
      <FirstPersonProjectileDriveByIKOffset x="0.000000" y="-0.110000" z="0.010000"/>
      <FirstPersonProjectileDriveByPassengerIKOffset x="-0.080000" y="-0.110000" z="0.010000"/>
      <FirstPersonProjectileDriveByRearLeftIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonProjectileDriveByRearRightIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonDriveByLeftPassengerIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonDriveByRightPassengerIKOffset x="0.000000" y="-0.060000" z="-0.020000"/>
      <FirstPersonDriveByRightRearPassengerIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonDriveByLeftPassengerUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonDriveByRightPassengerUnarmedIKOffset x="0.000000" y="-0.140000" z="0.000000"/>
      <FirstPersonMobilePhoneOffset x="0.133000" y="0.208000" z="0.569000"/>
      <FirstPersonPassengerMobilePhoneOffset x="0.136000" y="0.143000" z="0.485000"/>
      <PovCameraOffset x="0.000000" y="-0.225000" z="0.680000"/>
      <PovCameraVerticalAdjustmentForRollCage value="0.000000"/>
      <PovPassengerCameraOffset x="0.000000" y="-0.050000" z="0.000000"/>
      <PovRearPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000"/>
      <vfxInfoName>VFXVEHICLEINFO_CAR_GENERIC</vfxInfoName>
      <shouldUseCinematicViewMode value="true"/>
      <shouldCameraTransitionOnClimbUpDown value="false"/>
      <shouldCameraIgnoreExiting value="false"/>
      <AllowPretendOccupants value="true"/>
      <AllowJoyriding value="true"/>
      <AllowSundayDriving value="true"/>
      <AllowBodyColorMapping value="true"/>
      <wheelScale value="0.231512"/>
      <wheelScaleRear value="0.231512"/>
      <dirtLevelMin value="0.000000"/>
      <dirtLevelMax value="0.550000"/>
      <envEffScaleMin value="0.000000"/>
      <envEffScaleMax value="1.000000"/>
      <envEffScaleMin2 value="0.000000"/>
      <envEffScaleMax2 value="1.000000"/>
      <damageMapScale value="0.600000"/>
      <damageOffsetScale value="1.000000"/>
      <diffuseTint value="0x00FFFFFF"/>
      <steerWheelMult value="0.800000"/>
      <HDTextureDist value="5.000000"/>
      <lodDistances content="float_array">
        15.000000
        30.000000
        60.000000	
        120.000000	
        500.000000	
        500.000000
      </lodDistances>
      <minSeatHeight value="0.852"/>
      <identicalModelSpawnDistance value="20"/>
      <maxNumOfSameColor value="1"/>
      <defaultBodyHealth value="1000.000000"/>
      <pretendOccupantsScale value="1.000000"/>
      <visibleSpawnDistScale value="1.000000"/>
      <trackerPathWidth value="2.000000"/>
      <weaponForceMult value="1.000000"/>
      <frequency value="80"/>
      <swankness>SWANKNESS_3</swankness>
      <maxNum value="3"/>
      <flags>FLAG_SPORTS FLAG_SPAWN_ON_TRAILER FLAG_PARKING_SENSORS FLAG_AVERAGE_CAR FLAG_RECESSED_TAILLIGHT_CORONAS  FLAG_HAS_LIVERY</flags>
      <type>VEHICLE_TYPE_CAR</type>
      <plateType>VPT_FRONT_AND_BACK_PLATES</plateType>
      <dashboardType>VDT_BANSHEE</dashboardType>
      <vehicleClass>VC_SEDAN</vehicleClass>
      <wheelType>VWT_SPORT</wheelType>
      <trailers/>
      <additionalTrailers/>
      <drivers/>
      <extraIncludes/>
      <doorsWithCollisionWhenClosed/>
      <driveableDoors/>
      <bumpersNeedToCollideWithMap value="false"/>
      <needsRopeTexture value="false"/>
      <requiredExtras/>
      <rewards/>
      <cinematicPartCamera>
        <Item>WHEEL_FRONT_RIGHT_CAMERA</Item>
        <Item>WHEEL_FRONT_LEFT_CAMERA</Item>
        <Item>WHEEL_REAR_RIGHT_CAMERA</Item>
        <Item>WHEEL_REAR_LEFT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet/>
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000"/>
      <buoyancySphereSizeScale value="1.000000"/>
      <pOverrideRagdollThreshold type="NULL"/>
      <firstPersonDrivebyData>
        <Item>STD_SULTAN_FRONT_LEFT</Item>
        <Item>STD_SULTAN_FRONT_RIGHT</Item>
        <Item>STD_SULTAN_REAR_LEFT</Item>
        <Item>STD_SULTAN_REAR_RIGHT</Item>
      </firstPersonDrivebyData>
    </Item>
    <Item>
      <modelName>sentinelsg3b</modelName>
      <txdName>sentinelsg3b</txdName>
      <handlingId>SENTINELSG3B</handlingId>
      <gameName>SG33</gameName>
      <vehicleMakeName>UBERMACH</vehicleMakeName>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected/>
      <ptfxAssetName>weap_xs_vehicle_weapons</ptfxAssetName>
      <audioNameHash>SULTAN</audioNameHash>
      <layout>LAYOUT_STANDARD</layout>
      <coverBoundOffsets>F620_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout/>
      <cameraName>DEFAULT_FOLLOW_VEHICLE_CAMERA</cameraName>
      <aimCameraName>MID_BOX_VEHICLE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>VEHICLE_BONNET_CAMERA_NEAR_EXTRA_HIGH</bonnetCameraName>
      <povCameraName>DEFAULT_POV_CAMERA</povCameraName>
      <FirstPersonDriveByIKOffset x="0.000000" y="-0.060000" z="-0.020000"/>
      <FirstPersonDriveByUnarmedIKOffset x="0.000000" y="-0.100000" z="0.000000"/>
      <FirstPersonProjectileDriveByIKOffset x="0.000000" y="-0.110000" z="0.010000"/>
      <FirstPersonProjectileDriveByPassengerIKOffset x="-0.080000" y="-0.110000" z="0.010000"/>
      <FirstPersonProjectileDriveByRearLeftIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonProjectileDriveByRearRightIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonDriveByLeftPassengerIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonDriveByRightPassengerIKOffset x="0.000000" y="-0.060000" z="-0.020000"/>
      <FirstPersonDriveByRightRearPassengerIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonDriveByLeftPassengerUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonDriveByRightPassengerUnarmedIKOffset x="0.000000" y="-0.140000" z="0.000000"/>
      <FirstPersonMobilePhoneOffset x="0.133000" y="0.208000" z="0.569000"/>
      <FirstPersonPassengerMobilePhoneOffset x="0.136000" y="0.143000" z="0.485000"/>
      <PovCameraOffset x="0.000000" y="-0.225000" z="0.680000"/>
      <PovCameraVerticalAdjustmentForRollCage value="0.000000"/>
      <PovPassengerCameraOffset x="0.000000" y="-0.050000" z="0.000000"/>
      <PovRearPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000"/>
      <vfxInfoName>VFXVEHICLEINFO_CAR_GENERIC</vfxInfoName>
      <shouldUseCinematicViewMode value="true"/>
      <shouldCameraTransitionOnClimbUpDown value="false"/>
      <shouldCameraIgnoreExiting value="false"/>
      <AllowPretendOccupants value="true"/>
      <AllowJoyriding value="true"/>
      <AllowSundayDriving value="true"/>
      <AllowBodyColorMapping value="true"/>
      <wheelScale value="0.260611"/>
      <wheelScaleRear value="0.260611"/>
      <dirtLevelMin value="0.000000"/>
      <dirtLevelMax value="0.550000"/>
      <envEffScaleMin value="0.000000"/>
      <envEffScaleMax value="1.000000"/>
      <envEffScaleMin2 value="0.000000"/>
      <envEffScaleMax2 value="1.000000"/>
      <damageMapScale value="0.600000"/>
      <damageOffsetScale value="1.000000"/>
      <diffuseTint value="0x00FFFFFF"/>
      <steerWheelMult value="0.800000"/>
      <HDTextureDist value="5.000000"/>
      <lodDistances content="float_array">
        15.000000
        30.000000
        60.000000	
        120.000000	
        500.000000	
        500.000000
      </lodDistances>
      <minSeatHeight value="0.852"/>
      <identicalModelSpawnDistance value="20"/>
      <maxNumOfSameColor value="1"/>
      <defaultBodyHealth value="1000.000000"/>
      <pretendOccupantsScale value="1.000000"/>
      <visibleSpawnDistScale value="1.000000"/>
      <trackerPathWidth value="2.000000"/>
      <weaponForceMult value="1.000000"/>
      <frequency value="80"/>
      <swankness>SWANKNESS_3</swankness>
      <maxNum value="3"/>
      <flags>FLAG_SPORTS FLAG_SPAWN_ON_TRAILER FLAG_PARKING_SENSORS FLAG_AVERAGE_CAR FLAG_RECESSED_TAILLIGHT_CORONAS  FLAG_EXTRAS_STRONG FLAG_HAS_INTERIOR_EXTRAS FLAG_HAS_LIVERY</flags>
      <type>VEHICLE_TYPE_CAR</type>
      <plateType>VPT_FRONT_AND_BACK_PLATES</plateType>
      <dashboardType>VDT_BANSHEE</dashboardType>
      <vehicleClass>VC_COUPE</vehicleClass>
      <wheelType>VWT_SPORT</wheelType>
      <trailers/>
      <additionalTrailers/>
      <drivers/>
      <extraIncludes/>
      <doorsWithCollisionWhenClosed/>
      <driveableDoors/>
      <bumpersNeedToCollideWithMap value="false"/>
      <needsRopeTexture value="false"/>
      <requiredExtras/>
      <rewards/>
      <cinematicPartCamera>
        <Item>WHEEL_FRONT_RIGHT_CAMERA</Item>
        <Item>WHEEL_FRONT_LEFT_CAMERA</Item>
        <Item>WHEEL_REAR_RIGHT_CAMERA</Item>
        <Item>WHEEL_REAR_LEFT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet/>
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000"/>
      <buoyancySphereSizeScale value="1.000000"/>
      <pOverrideRagdollThreshold type="NULL"/>
      <firstPersonDrivebyData>
        <Item>STD_SULTAN_FRONT_LEFT</Item>
        <Item>STD_SULTAN_FRONT_RIGHT</Item>
      </firstPersonDrivebyData>
    </Item>
    <Item>
      <modelName>sentinelsg3b2</modelName>
      <txdName>sentinelsg3b2</txdName>
      <handlingId>SENTINELSG3B2</handlingId>
      <gameName>SG3B2</gameName>
      <vehicleMakeName>UBERMACH</vehicleMakeName>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected/>
      <ptfxAssetName>weap_xs_vehicle_weapons</ptfxAssetName>
      <audioNameHash>sultan</audioNameHash>
      <layout>LAYOUT_STANDARD</layout>
      <coverBoundOffsets>F620_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout/>
      <cameraName>DEFAULT_FOLLOW_VEHICLE_CAMERA</cameraName>
      <aimCameraName>MID_BOX_VEHICLE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>VEHICLE_BONNET_CAMERA_NEAR_EXTRA_HIGH</bonnetCameraName>
      <povCameraName>DEFAULT_POV_CAMERA</povCameraName>
      <FirstPersonDriveByIKOffset x="0.000000" y="-0.060000" z="-0.020000"/>
      <FirstPersonDriveByUnarmedIKOffset x="0.000000" y="-0.100000" z="0.000000"/>
      <FirstPersonProjectileDriveByIKOffset x="0.000000" y="-0.110000" z="0.010000"/>
      <FirstPersonProjectileDriveByPassengerIKOffset x="-0.080000" y="-0.110000" z="0.010000"/>
      <FirstPersonProjectileDriveByRearLeftIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonProjectileDriveByRearRightIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonDriveByLeftPassengerIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonDriveByRightPassengerIKOffset x="0.000000" y="-0.060000" z="-0.020000"/>
      <FirstPersonDriveByRightRearPassengerIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonDriveByLeftPassengerUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonDriveByRightPassengerUnarmedIKOffset x="0.000000" y="-0.140000" z="0.000000"/>
      <FirstPersonMobilePhoneOffset x="0.133000" y="0.208000" z="0.569000"/>
      <FirstPersonPassengerMobilePhoneOffset x="0.136000" y="0.143000" z="0.485000"/>
      <PovCameraOffset x="0.000000" y="-0.225000" z="0.680000"/>
      <PovCameraVerticalAdjustmentForRollCage value="0.000000"/>
      <PovPassengerCameraOffset x="0.000000" y="-0.050000" z="0.000000"/>
      <PovRearPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000"/>
      <vfxInfoName>VFXVEHICLEINFO_CAR_GENERIC</vfxInfoName>
      <shouldUseCinematicViewMode value="true"/>
      <shouldCameraTransitionOnClimbUpDown value="false"/>
      <shouldCameraIgnoreExiting value="false"/>
      <AllowPretendOccupants value="true"/>
      <AllowJoyriding value="true"/>
      <AllowSundayDriving value="true"/>
      <AllowBodyColorMapping value="true"/>
      <wheelScale value="0.231512"/>
      <wheelScaleRear value="0.231512"/>
      <dirtLevelMin value="0.000000"/>
      <dirtLevelMax value="0.550000"/>
      <envEffScaleMin value="0.000000"/>
      <envEffScaleMax value="1.000000"/>
      <envEffScaleMin2 value="0.000000"/>
      <envEffScaleMax2 value="1.000000"/>
      <damageMapScale value="0.600000"/>
      <damageOffsetScale value="1.000000"/>
      <diffuseTint value="0x00FFFFFF"/>
      <steerWheelMult value="0.800000"/>
      <HDTextureDist value="5.000000"/>
      <lodDistances content="float_array">
        15.000000
        30.000000
        60.000000	
        120.000000	
        500.000000	
        500.000000
      </lodDistances>
      <minSeatHeight value="0.852"/>
      <identicalModelSpawnDistance value="20"/>
      <maxNumOfSameColor value="1"/>
      <defaultBodyHealth value="1000.000000"/>
      <pretendOccupantsScale value="1.000000"/>
      <visibleSpawnDistScale value="1.000000"/>
      <trackerPathWidth value="2.000000"/>
      <weaponForceMult value="1.000000"/>
      <frequency value="80"/>
      <swankness>SWANKNESS_3</swankness>
      <maxNum value="3"/>
      <flags>FLAG_SPORTS FLAG_SPAWN_ON_TRAILER FLAG_PARKING_SENSORS FLAG_AVERAGE_CAR FLAG_RECESSED_TAILLIGHT_CORONAS  FLAG_EXTRAS_STRONG FLAG_HAS_INTERIOR_EXTRAS FLAG_HAS_LIVERY</flags>
      <type>VEHICLE_TYPE_CAR</type>
      <plateType>VPT_FRONT_AND_BACK_PLATES</plateType>
      <dashboardType>VDT_BANSHEE</dashboardType>
      <vehicleClass>VC_COUPE</vehicleClass>
      <wheelType>VWT_SPORT</wheelType>
      <trailers/>
      <additionalTrailers/>
      <drivers/>
      <extraIncludes/>
      <doorsWithCollisionWhenClosed/>
      <driveableDoors/>
      <bumpersNeedToCollideWithMap value="false"/>
      <needsRopeTexture value="false"/>
      <requiredExtras/>
      <rewards/>
      <cinematicPartCamera>
        <Item>WHEEL_FRONT_RIGHT_CAMERA</Item>
        <Item>WHEEL_FRONT_LEFT_CAMERA</Item>
        <Item>WHEEL_REAR_RIGHT_CAMERA</Item>
        <Item>WHEEL_REAR_LEFT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet/>
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000"/>
      <buoyancySphereSizeScale value="1.000000"/>
      <pOverrideRagdollThreshold type="NULL"/>
      <firstPersonDrivebyData>
        <Item>STD_SULTAN_FRONT_LEFT</Item>
        <Item>STD_SULTAN_FRONT_RIGHT</Item>
      </firstPersonDrivebyData>
    </Item>
    <Item>
      <modelName>sentinelsg3c</modelName>
      <txdName>sentinelsg3c</txdName>
      <handlingId>SENTINELSG3C</handlingId>
      <gameName>SG34</gameName>
      <vehicleMakeName>UBERMACH</vehicleMakeName>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected/>
      <ptfxAssetName>weap_xs_vehicle_weapons</ptfxAssetName>
      <audioNameHash>SULTAN</audioNameHash>
      <layout>LAYOUT_STANDARD</layout>
      <coverBoundOffsets>F620_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout/>
      <cameraName>DEFAULT_FOLLOW_VEHICLE_CAMERA</cameraName>
      <aimCameraName>MID_BOX_VEHICLE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>VEHICLE_BONNET_CAMERA_NEAR_EXTRA_HIGH</bonnetCameraName>
      <povCameraName>DEFAULT_POV_CAMERA</povCameraName>
      <FirstPersonDriveByIKOffset x="0.000000" y="-0.060000" z="-0.020000"/>
      <FirstPersonDriveByUnarmedIKOffset x="0.000000" y="-0.100000" z="0.000000"/>
      <FirstPersonProjectileDriveByIKOffset x="0.000000" y="-0.110000" z="0.010000"/>
      <FirstPersonProjectileDriveByPassengerIKOffset x="-0.080000" y="-0.110000" z="0.010000"/>
      <FirstPersonProjectileDriveByRearLeftIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonProjectileDriveByRearRightIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonDriveByLeftPassengerIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonDriveByRightPassengerIKOffset x="0.000000" y="-0.060000" z="-0.020000"/>
      <FirstPersonDriveByRightRearPassengerIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonDriveByLeftPassengerUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonDriveByRightPassengerUnarmedIKOffset x="0.000000" y="-0.140000" z="0.000000"/>
      <FirstPersonMobilePhoneOffset x="0.133000" y="0.208000" z="0.569000"/>
      <FirstPersonPassengerMobilePhoneOffset x="0.136000" y="0.143000" z="0.485000"/>
      <PovCameraOffset x="0.000000" y="-0.225000" z="0.680000"/>
      <PovCameraVerticalAdjustmentForRollCage value="0.000000"/>
      <PovPassengerCameraOffset x="0.000000" y="-0.050000" z="0.000000"/>
      <PovRearPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000"/>
      <vfxInfoName>VFXVEHICLEINFO_CAR_GENERIC</vfxInfoName>
      <shouldUseCinematicViewMode value="true"/>
      <shouldCameraTransitionOnClimbUpDown value="false"/>
      <shouldCameraIgnoreExiting value="false"/>
      <AllowPretendOccupants value="true"/>
      <AllowJoyriding value="true"/>
      <AllowSundayDriving value="true"/>
      <AllowBodyColorMapping value="true"/>
      <wheelScale value="0.260611"/>
      <wheelScaleRear value="0.260611"/>
      <dirtLevelMin value="0.000000"/>
      <dirtLevelMax value="0.550000"/>
      <envEffScaleMin value="0.000000"/>
      <envEffScaleMax value="1.000000"/>
      <envEffScaleMin2 value="0.000000"/>
      <envEffScaleMax2 value="1.000000"/>
      <damageMapScale value="0.600000"/>
      <damageOffsetScale value="1.000000"/>
      <diffuseTint value="0x00FFFFFF"/>
      <steerWheelMult value="0.800000"/>
      <HDTextureDist value="5.000000"/>
      <lodDistances content="float_array">
        15.000000
        30.000000
        60.000000	
        120.000000	
        500.000000	
        500.000000
      </lodDistances>
      <minSeatHeight value="0.852"/>
      <identicalModelSpawnDistance value="20"/>
      <maxNumOfSameColor value="1"/>
      <defaultBodyHealth value="1000.000000"/>
      <pretendOccupantsScale value="1.000000"/>
      <visibleSpawnDistScale value="1.000000"/>
      <trackerPathWidth value="2.000000"/>
      <weaponForceMult value="1.000000"/>
      <frequency value="80"/>
      <swankness>SWANKNESS_3</swankness>
      <maxNum value="3"/>
      <flags>FLAG_SPORTS FLAG_SPAWN_ON_TRAILER FLAG_PARKING_SENSORS FLAG_AVERAGE_CAR FLAG_RECESSED_TAILLIGHT_CORONAS  FLAG_HAS_LIVERY</flags>
      <type>VEHICLE_TYPE_CAR</type>
      <plateType>VPT_FRONT_AND_BACK_PLATES</plateType>
      <dashboardType>VDT_BANSHEE</dashboardType>
      <vehicleClass>VC_SEDAN</vehicleClass>
      <wheelType>VWT_SPORT</wheelType>
      <trailers/>
      <additionalTrailers/>
      <drivers/>
      <extraIncludes/>
      <doorsWithCollisionWhenClosed/>
      <driveableDoors/>
      <bumpersNeedToCollideWithMap value="false"/>
      <needsRopeTexture value="false"/>
      <requiredExtras/>
      <rewards/>
      <cinematicPartCamera>
        <Item>WHEEL_FRONT_RIGHT_CAMERA</Item>
        <Item>WHEEL_FRONT_LEFT_CAMERA</Item>
        <Item>WHEEL_REAR_RIGHT_CAMERA</Item>
        <Item>WHEEL_REAR_LEFT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet/>
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000"/>
      <buoyancySphereSizeScale value="1.000000"/>
      <pOverrideRagdollThreshold type="NULL"/>
      <firstPersonDrivebyData>
        <Item>STD_SULTAN_FRONT_LEFT</Item>
        <Item>STD_SULTAN_FRONT_RIGHT</Item>
        <Item>STD_SULTAN_REAR_LEFT</Item>
        <Item>STD_SULTAN_REAR_RIGHT</Item>
      </firstPersonDrivebyData>
    </Item>
    <Item>
      <modelName>sentinelsg3c2</modelName>
      <txdName>sentinelsg3c2</txdName>
      <handlingId>SENTINELSG3C2</handlingId>
      <gameName>SG3C2</gameName>
      <vehicleMakeName>UBERMACH</vehicleMakeName>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected/>
      <ptfxAssetName>weap_xs_vehicle_weapons</ptfxAssetName>
      <audioNameHash>sultan</audioNameHash>
      <layout>LAYOUT_STANDARD</layout>
      <coverBoundOffsets>F620_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout/>
      <cameraName>DEFAULT_FOLLOW_VEHICLE_CAMERA</cameraName>
      <aimCameraName>MID_BOX_VEHICLE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>VEHICLE_BONNET_CAMERA_NEAR_EXTRA_HIGH</bonnetCameraName>
      <povCameraName>DEFAULT_POV_CAMERA</povCameraName>
      <FirstPersonDriveByIKOffset x="0.000000" y="-0.060000" z="-0.020000"/>
      <FirstPersonDriveByUnarmedIKOffset x="0.000000" y="-0.100000" z="0.000000"/>
      <FirstPersonProjectileDriveByIKOffset x="0.000000" y="-0.110000" z="0.010000"/>
      <FirstPersonProjectileDriveByPassengerIKOffset x="-0.080000" y="-0.110000" z="0.010000"/>
      <FirstPersonProjectileDriveByRearLeftIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonProjectileDriveByRearRightIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonDriveByLeftPassengerIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonDriveByRightPassengerIKOffset x="0.000000" y="-0.060000" z="-0.020000"/>
      <FirstPersonDriveByRightRearPassengerIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonDriveByLeftPassengerUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonDriveByRightPassengerUnarmedIKOffset x="0.000000" y="-0.140000" z="0.000000"/>
      <FirstPersonMobilePhoneOffset x="0.133000" y="0.208000" z="0.569000"/>
      <FirstPersonPassengerMobilePhoneOffset x="0.136000" y="0.143000" z="0.485000"/>
      <PovCameraOffset x="0.000000" y="-0.225000" z="0.680000"/>
      <PovCameraVerticalAdjustmentForRollCage value="0.000000"/>
      <PovPassengerCameraOffset x="0.000000" y="-0.050000" z="0.000000"/>
      <PovRearPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000"/>
      <vfxInfoName>VFXVEHICLEINFO_CAR_GENERIC</vfxInfoName>
      <shouldUseCinematicViewMode value="true"/>
      <shouldCameraTransitionOnClimbUpDown value="false"/>
      <shouldCameraIgnoreExiting value="false"/>
      <AllowPretendOccupants value="true"/>
      <AllowJoyriding value="true"/>
      <AllowSundayDriving value="true"/>
      <AllowBodyColorMapping value="true"/>
      <wheelScale value="0.231512"/>
      <wheelScaleRear value="0.231512"/>
      <dirtLevelMin value="0.000000"/>
      <dirtLevelMax value="0.550000"/>
      <envEffScaleMin value="0.000000"/>
      <envEffScaleMax value="1.000000"/>
      <envEffScaleMin2 value="0.000000"/>
      <envEffScaleMax2 value="1.000000"/>
      <damageMapScale value="0.600000"/>
      <damageOffsetScale value="1.000000"/>
      <diffuseTint value="0x00FFFFFF"/>
      <steerWheelMult value="0.800000"/>
      <HDTextureDist value="5.000000"/>
      <lodDistances content="float_array">
        15.000000
        30.000000
        60.000000	
        120.000000	
        500.000000	
        500.000000
      </lodDistances>
      <minSeatHeight value="0.852"/>
      <identicalModelSpawnDistance value="20"/>
      <maxNumOfSameColor value="1"/>
      <defaultBodyHealth value="1000.000000"/>
      <pretendOccupantsScale value="1.000000"/>
      <visibleSpawnDistScale value="1.000000"/>
      <trackerPathWidth value="2.000000"/>
      <weaponForceMult value="1.000000"/>
      <frequency value="80"/>
      <swankness>SWANKNESS_3</swankness>
      <maxNum value="3"/>
      <flags>FLAG_SPORTS FLAG_SPAWN_ON_TRAILER FLAG_PARKING_SENSORS FLAG_AVERAGE_CAR FLAG_RECESSED_TAILLIGHT_CORONAS  FLAG_HAS_LIVERY</flags>
      <type>VEHICLE_TYPE_CAR</type>
      <plateType>VPT_FRONT_AND_BACK_PLATES</plateType>
      <dashboardType>VDT_BANSHEE</dashboardType>
      <vehicleClass>VC_SEDAN</vehicleClass>
      <wheelType>VWT_SPORT</wheelType>
      <trailers/>
      <additionalTrailers/>
      <drivers/>
      <extraIncludes/>
      <doorsWithCollisionWhenClosed/>
      <driveableDoors/>
      <bumpersNeedToCollideWithMap value="false"/>
      <needsRopeTexture value="false"/>
      <requiredExtras/>
      <rewards/>
      <cinematicPartCamera>
        <Item>WHEEL_FRONT_RIGHT_CAMERA</Item>
        <Item>WHEEL_FRONT_LEFT_CAMERA</Item>
        <Item>WHEEL_REAR_RIGHT_CAMERA</Item>
        <Item>WHEEL_REAR_LEFT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet/>
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000"/>
      <buoyancySphereSizeScale value="1.000000"/>
      <pOverrideRagdollThreshold type="NULL"/>
      <firstPersonDrivebyData>
        <Item>STD_SULTAN_FRONT_LEFT</Item>
        <Item>STD_SULTAN_FRONT_RIGHT</Item>
        <Item>STD_SULTAN_REAR_LEFT</Item>
        <Item>STD_SULTAN_REAR_RIGHT</Item>
      </firstPersonDrivebyData>
    </Item>
    <Item>
      <modelName>sentinelsg3d</modelName>
      <txdName>sentinelsg3d</txdName>
      <handlingId>SENTINELSG3D</handlingId>
      <gameName>SG3D</gameName>
      <vehicleMakeName>UBERMACH</vehicleMakeName>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected/>
      <ptfxAssetName>weap_xs_vehicle_weapons</ptfxAssetName>
      <audioNameHash/>
      <layout>LAYOUT_STD_EXITFIXUP</layout>
      <coverBoundOffsets>F620_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout/>
      <cameraName>DEFAULT_FOLLOW_VEHICLE_CAMERA</cameraName>
      <aimCameraName>MID_BOX_VEHICLE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>VEHICLE_BONNET_CAMERA_NEAR_EXTRA_HIGH</bonnetCameraName>
      <povCameraName>DEFAULT_POV_CAMERA</povCameraName>
      <FirstPersonDriveByIKOffset x="0.000000" y="-0.060000" z="-0.020000"/>
      <FirstPersonDriveByUnarmedIKOffset x="0.000000" y="-0.100000" z="0.000000"/>
      <FirstPersonProjectileDriveByIKOffset x="0.000000" y="-0.110000" z="0.010000"/>
      <FirstPersonProjectileDriveByPassengerIKOffset x="-0.080000" y="-0.110000" z="0.010000"/>
      <FirstPersonProjectileDriveByRearLeftIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonProjectileDriveByRearRightIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonDriveByLeftPassengerIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonDriveByRightPassengerIKOffset x="0.000000" y="-0.060000" z="-0.020000"/>
      <FirstPersonDriveByRightRearPassengerIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonDriveByLeftPassengerUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonDriveByRightPassengerUnarmedIKOffset x="0.000000" y="-0.140000" z="0.000000"/>
      <FirstPersonMobilePhoneOffset x="0.133000" y="0.208000" z="0.569000"/>
      <FirstPersonPassengerMobilePhoneOffset x="0.136000" y="0.143000" z="0.485000"/>
      <PovCameraOffset x="0.000000" y="-0.225000" z="0.680000"/>
      <PovCameraVerticalAdjustmentForRollCage value="0.000000"/>
      <PovPassengerCameraOffset x="0.000000" y="-0.050000" z="0.000000"/>
      <PovRearPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000"/>
      <vfxInfoName>VFXVEHICLEINFO_CAR_GENERIC</vfxInfoName>
      <shouldUseCinematicViewMode value="true"/>
      <shouldCameraTransitionOnClimbUpDown value="false"/>
      <shouldCameraIgnoreExiting value="false"/>
      <AllowPretendOccupants value="true"/>
      <AllowJoyriding value="true"/>
      <AllowSundayDriving value="true"/>
      <AllowBodyColorMapping value="true"/>
      <wheelScale value="0.259071"/>
      <wheelScaleRear value="0.259071"/>
      <dirtLevelMin value="0.000000"/>
      <dirtLevelMax value="0.550000"/>
      <envEffScaleMin value="0.000000"/>
      <envEffScaleMax value="1.000000"/>
      <envEffScaleMin2 value="0.000000"/>
      <envEffScaleMax2 value="1.000000"/>
      <damageMapScale value="0.600000"/>
      <damageOffsetScale value="1.000000"/>
      <diffuseTint value="0x00FFFFFF"/>
      <steerWheelMult value="0.800000"/>
      <HDTextureDist value="5.000000"/>
      <lodDistances content="float_array">
        15.000000
        25.000000
        40.000000	
        100.000000	
        300.000000	
        500.000000
      </lodDistances>
      <minSeatHeight value="0.852"/>
      <identicalModelSpawnDistance value="20"/>
      <maxNumOfSameColor value="1"/>
      <defaultBodyHealth value="1000.000000"/>
      <pretendOccupantsScale value="1.000000"/>
      <visibleSpawnDistScale value="1.000000"/>
      <trackerPathWidth value="2.000000"/>
      <weaponForceMult value="1.000000"/>
      <frequency value="80"/>
      <swankness>SWANKNESS_3</swankness>
      <maxNum value="3"/>
      <flags>FLAG_SPORTS FLAG_SPAWN_ON_TRAILER FLAG_PARKING_SENSORS FLAG_AVERAGE_CAR FLAG_RECESSED_TAILLIGHT_CORONAS  FLAG_HAS_LIVERY</flags>
      <type>VEHICLE_TYPE_CAR</type>
      <plateType>VPT_FRONT_AND_BACK_PLATES</plateType>
      <dashboardType>VDT_RACE</dashboardType>
      <vehicleClass>VC_COUPE</vehicleClass>
      <wheelType>VWT_SPORT</wheelType>
      <trailers/>
      <additionalTrailers/>
      <drivers/>
      <extraIncludes/>
      <doorsWithCollisionWhenClosed/>
      <driveableDoors/>
      <bumpersNeedToCollideWithMap value="false"/>
      <needsRopeTexture value="false"/>
      <requiredExtras/>
      <rewards/>
      <cinematicPartCamera>
        <Item>WHEEL_FRONT_RIGHT_CAMERA</Item>
        <Item>WHEEL_FRONT_LEFT_CAMERA</Item>
        <Item>WHEEL_REAR_RIGHT_CAMERA</Item>
        <Item>WHEEL_REAR_LEFT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet/>
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000"/>
      <buoyancySphereSizeScale value="1.000000"/>
      <pOverrideRagdollThreshold type="NULL"/>
      <firstPersonDrivebyData>
        <Item>LOW_F620_FRONT_LEFT</Item>
        <Item>LOW_F620_FRONT_RIGHT</Item>
      </firstPersonDrivebyData>
    </Item>
    <Item>
      <modelName>sentinelsg3d2</modelName>
      <txdName>sentinelsg3d2</txdName>
      <handlingId>SENTINELSG3D2</handlingId>
      <gameName>SG3D2</gameName>
      <vehicleMakeName>UBERMACH</vehicleMakeName>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected/>
      <ptfxAssetName>weap_xs_vehicle_weapons</ptfxAssetName>
      <audioNameHash>SULTAN</audioNameHash>
      <layout>LAYOUT_STD_EXITFIXUP</layout>
      <coverBoundOffsets>F620_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout/>
      <cameraName>DEFAULT_FOLLOW_VEHICLE_CAMERA</cameraName>
      <aimCameraName>MID_BOX_VEHICLE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>VEHICLE_BONNET_CAMERA_NEAR_EXTRA_HIGH</bonnetCameraName>
      <povCameraName>DEFAULT_POV_CAMERA</povCameraName>
      <FirstPersonDriveByIKOffset x="0.000000" y="-0.060000" z="-0.020000"/>
      <FirstPersonDriveByUnarmedIKOffset x="0.000000" y="-0.100000" z="0.000000"/>
      <FirstPersonProjectileDriveByIKOffset x="0.000000" y="-0.110000" z="0.010000"/>
      <FirstPersonProjectileDriveByPassengerIKOffset x="-0.080000" y="-0.110000" z="0.010000"/>
      <FirstPersonProjectileDriveByRearLeftIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonProjectileDriveByRearRightIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonDriveByLeftPassengerIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonDriveByRightPassengerIKOffset x="0.000000" y="-0.060000" z="-0.020000"/>
      <FirstPersonDriveByRightRearPassengerIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonDriveByLeftPassengerUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonDriveByRightPassengerUnarmedIKOffset x="0.000000" y="-0.140000" z="0.000000"/>
      <FirstPersonMobilePhoneOffset x="0.133000" y="0.208000" z="0.569000"/>
      <FirstPersonPassengerMobilePhoneOffset x="0.136000" y="0.143000" z="0.485000"/>
      <PovCameraOffset x="0.000000" y="-0.225000" z="0.680000"/>
      <PovCameraVerticalAdjustmentForRollCage value="0.000000"/>
      <PovPassengerCameraOffset x="0.000000" y="-0.050000" z="0.000000"/>
      <PovRearPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000"/>
      <vfxInfoName>VFXVEHICLEINFO_CAR_GENERIC</vfxInfoName>
      <shouldUseCinematicViewMode value="true"/>
      <shouldCameraTransitionOnClimbUpDown value="false"/>
      <shouldCameraIgnoreExiting value="false"/>
      <AllowPretendOccupants value="true"/>
      <AllowJoyriding value="true"/>
      <AllowSundayDriving value="true"/>
      <AllowBodyColorMapping value="true"/>
      <wheelScale value="0.259071"/>
      <wheelScaleRear value="0.259071"/>
      <dirtLevelMin value="0.000000"/>
      <dirtLevelMax value="0.550000"/>
      <envEffScaleMin value="0.000000"/>
      <envEffScaleMax value="1.000000"/>
      <envEffScaleMin2 value="0.000000"/>
      <envEffScaleMax2 value="1.000000"/>
      <damageMapScale value="0.600000"/>
      <damageOffsetScale value="1.000000"/>
      <diffuseTint value="0x00FFFFFF"/>
      <steerWheelMult value="0.800000"/>
      <HDTextureDist value="5.000000"/>
      <lodDistances content="float_array">
        15.000000
        25.000000
        40.000000	
        100.000000	
        300.000000	
        500.000000
      </lodDistances>
      <minSeatHeight value="0.852"/>
      <identicalModelSpawnDistance value="20"/>
      <maxNumOfSameColor value="1"/>
      <defaultBodyHealth value="1000.000000"/>
      <pretendOccupantsScale value="1.000000"/>
      <visibleSpawnDistScale value="1.000000"/>
      <trackerPathWidth value="2.000000"/>
      <weaponForceMult value="1.000000"/>
      <frequency value="80"/>
      <swankness>SWANKNESS_3</swankness>
      <maxNum value="3"/>
      <flags>FLAG_SPORTS FLAG_SPAWN_ON_TRAILER FLAG_PARKING_SENSORS FLAG_AVERAGE_CAR FLAG_RECESSED_TAILLIGHT_CORONAS  FLAG_HAS_LIVERY</flags>
      <type>VEHICLE_TYPE_CAR</type>
      <plateType>VPT_FRONT_AND_BACK_PLATES</plateType>
      <dashboardType>VDT_RACE</dashboardType>
      <vehicleClass>VC_COUPE</vehicleClass>
      <wheelType>VWT_SPORT</wheelType>
      <trailers/>
      <additionalTrailers/>
      <drivers/>
      <extraIncludes/>
      <doorsWithCollisionWhenClosed/>
      <driveableDoors/>
      <bumpersNeedToCollideWithMap value="false"/>
      <needsRopeTexture value="false"/>
      <requiredExtras/>
      <rewards/>
      <cinematicPartCamera>
        <Item>WHEEL_FRONT_RIGHT_CAMERA</Item>
        <Item>WHEEL_FRONT_LEFT_CAMERA</Item>
        <Item>WHEEL_REAR_RIGHT_CAMERA</Item>
        <Item>WHEEL_REAR_LEFT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet/>
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000"/>
      <buoyancySphereSizeScale value="1.000000"/>
      <pOverrideRagdollThreshold type="NULL"/>
      <firstPersonDrivebyData>
        <Item>LOW_F620_FRONT_LEFT</Item>
        <Item>LOW_F620_FRONT_RIGHT</Item>
      </firstPersonDrivebyData>
    </Item>
    <Item>
      <modelName>spritzer</modelName>
      <txdName>spritzer</txdName>
      <handlingId>SPRITZER</handlingId>
      <gameName>SPRITZER</gameName>
      <vehicleMakeName>BENEFAC</vehicleMakeName>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected/>
      <ptfxAssetName>null</ptfxAssetName>
      <audioNameHash>SPRITZER</audioNameHash>
      <layout>LAYOUT_STANDARD</layout>
      <coverBoundOffsets>SENTINEL3_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout/>
      <cameraName>DEFAULT_FOLLOW_VEHICLE_CAMERA</cameraName>
      <aimCameraName>MID_BOX_VEHICLE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>VEHICLE_BONNET_CAMERA_LOW_LOW</bonnetCameraName>
      <povCameraName>DEFAULT_POV_CAMERA</povCameraName>
      <FirstPersonDriveByIKOffset x="0.015000" y="-0.016000" z="-0.073000"/>
      <FirstPersonDriveByUnarmedIKOffset x="0.000000" y="0.040000" z="-0.005000"/>
      <FirstPersonProjectileDriveByIKOffset x="0.000000" y="-0.100000" z="0.000000"/>
      <FirstPersonProjectileDriveByPassengerIKOffset x="0.000000" y="-0.100000" z="0.000000"/>
      <FirstPersonProjectileDriveByRearLeftIKOffset x="0.050000" y="0.000000" z="-0.030000"/>
      <FirstPersonProjectileDriveByRearRightIKOffset x="0.050000" y="0.000000" z="-0.030000"/>
      <FirstPersonDriveByLeftPassengerIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonDriveByRightPassengerIKOffset x="0.015000" y="-0.036000" z="-0.073000"/>
      <FirstPersonDriveByRightRearPassengerIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonDriveByLeftPassengerUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonDriveByRightPassengerUnarmedIKOffset x="0.000000" y="-0.060000" z="-0.010000"/>
      <FirstPersonMobilePhoneOffset x="0.130000" y="0.152000" z="0.525000"/>
      <FirstPersonPassengerMobilePhoneOffset x="0.210000" y="0.070000" z="0.420000"/>
      <PovCameraOffset x="0.000000" y="-0.230000" z="0.670000"/>
      <PovCameraVerticalAdjustmentForRollCage value="0.000000"/>
      <PovPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000"/>
      <PovRearPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000"/>
      <vfxInfoName>VFXVEHICLEINFO_CAR_GENERIC</vfxInfoName>
      <shouldUseCinematicViewMode value="true"/>
      <shouldCameraTransitionOnClimbUpDown value="false"/>
      <shouldCameraIgnoreExiting value="false"/>
      <AllowPretendOccupants value="true"/>
      <AllowJoyriding value="true"/>
      <AllowSundayDriving value="true"/>
      <AllowBodyColorMapping value="true"/>
      <wheelScale value="0.248000"/>
      <wheelScaleRear value="0.248000"/>
      <dirtLevelMin value="0.000000"/>
      <dirtLevelMax value="0.450000"/>
      <envEffScaleMin value="0.000000"/>
      <envEffScaleMax value="1.000000"/>
      <envEffScaleMin2 value="0.000000"/>
      <envEffScaleMax2 value="1.000000"/>
      <damageMapScale value="0.350000"/>
      <damageOffsetScale value="0.350000"/>
      <diffuseTint value="0x00FFFFFF"/>
      <steerWheelMult value="1.000000"/>
      <HDTextureDist value="5.000000"/>
      <lodDistances content="float_array">
        20.000000
        40.000000
        80.000000	
        120.000000
        500.000000
        500.000000
      </lodDistances>
      <minSeatHeight value="0.844"/>
      <identicalModelSpawnDistance value="80"/>
      <maxNumOfSameColor value="1"/>
      <defaultBodyHealth value="1000.000000"/>
      <pretendOccupantsScale value="1.000000"/>
      <visibleSpawnDistScale value="1.000000"/>
      <trackerPathWidth value="2.000000"/>
      <weaponForceMult value="1.000000"/>
      <frequency value="5"/>
      <swankness>SWANKNESS_5</swankness>
      <maxNum value="1"/>
      <flags>FLAG_RICH_CAR FLAG_SPORTS</flags>
      <type>VEHICLE_TYPE_CAR</type>
      <plateType>VPT_FRONT_AND_BACK_PLATES</plateType>
      <dashboardType>VDT_FELTZER</dashboardType>
      <vehicleClass>VC_SPORT</vehicleClass>
      <wheelType>VWT_SPORT</wheelType>
      <trailers/>
      <additionalTrailers/>
      <drivers/>
      <extraIncludes/>
      <doorsWithCollisionWhenClosed/>
      <driveableDoors/>
      <bumpersNeedToCollideWithMap value="false"/>
      <needsRopeTexture value="false"/>
      <requiredExtras/>
      <rewards/>
      <cinematicPartCamera>
        <Item>WHEEL_FRONT_RIGHT_CAMERA</Item>
        <Item>WHEEL_FRONT_LEFT_CAMERA</Item>
        <Item>WHEEL_REAR_RIGHT_CAMERA</Item>
        <Item>WHEEL_REAR_LEFT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet/>
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000"/>
      <buoyancySphereSizeScale value="1.000000"/>
      <pOverrideRagdollThreshold type="NULL"/>
      <firstPersonDrivebyData>
        <Item>STD_SENTINEL3_FRONT_LEFT</Item>
        <Item>STD_SENTINEL3_FRONT_RIGHT</Item>
      </firstPersonDrivebyData>
      <numSeatsOverride value="2"/>
    </Item>
    <Item>
      <modelName>spritzerdtm</modelName>
      <txdName>spritzerdtm</txdName>
      <handlingId>SPRITZERDTM</handlingId>
      <gameName>SPRITZERDTM</gameName>
      <vehicleMakeName>BENEFAC</vehicleMakeName>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected/>
      <ptfxAssetName>null</ptfxAssetName>
      <audioNameHash>SPRITZERDTM</audioNameHash>
      <layout>LAYOUT_STANDARD</layout>
      <coverBoundOffsets>SENTINEL3_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout/>
      <cameraName>DEFAULT_FOLLOW_VEHICLE_CAMERA</cameraName>
      <aimCameraName>MID_BOX_VEHICLE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>VEHICLE_BONNET_CAMERA_LOW_LOW</bonnetCameraName>
      <povCameraName>DEFAULT_POV_CAMERA</povCameraName>
      <FirstPersonDriveByIKOffset x="0.015000" y="-0.016000" z="-0.073000"/>
      <FirstPersonDriveByUnarmedIKOffset x="0.000000" y="0.040000" z="-0.005000"/>
      <FirstPersonProjectileDriveByIKOffset x="0.000000" y="-0.100000" z="0.000000"/>
      <FirstPersonProjectileDriveByPassengerIKOffset x="0.000000" y="-0.100000" z="0.000000"/>
      <FirstPersonProjectileDriveByRearLeftIKOffset x="0.050000" y="0.000000" z="-0.030000"/>
      <FirstPersonProjectileDriveByRearRightIKOffset x="0.050000" y="0.000000" z="-0.030000"/>
      <FirstPersonDriveByLeftPassengerIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonDriveByRightPassengerIKOffset x="0.015000" y="-0.036000" z="-0.073000"/>
      <FirstPersonDriveByRightRearPassengerIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonDriveByLeftPassengerUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonDriveByRightPassengerUnarmedIKOffset x="0.000000" y="-0.060000" z="-0.010000"/>
      <FirstPersonMobilePhoneOffset x="0.130000" y="0.152000" z="0.525000"/>
      <FirstPersonPassengerMobilePhoneOffset x="0.210000" y="0.070000" z="0.420000"/>
      <PovCameraOffset x="0.000000" y="-0.230000" z="0.670000"/>
      <PovCameraVerticalAdjustmentForRollCage value="0.000000"/>
      <PovPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000"/>
      <PovRearPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000"/>
      <vfxInfoName>VFXVEHICLEINFO_CAR_GENERIC</vfxInfoName>
      <shouldUseCinematicViewMode value="true"/>
      <shouldCameraTransitionOnClimbUpDown value="false"/>
      <shouldCameraIgnoreExiting value="false"/>
      <AllowPretendOccupants value="true"/>
      <AllowJoyriding value="true"/>
      <AllowSundayDriving value="true"/>
      <AllowBodyColorMapping value="true"/>
      <wheelScale value="0.248000"/>
      <wheelScaleRear value="0.248000"/>
      <dirtLevelMin value="0.000000"/>
      <dirtLevelMax value="0.450000"/>
      <envEffScaleMin value="0.000000"/>
      <envEffScaleMax value="1.000000"/>
      <envEffScaleMin2 value="0.000000"/>
      <envEffScaleMax2 value="1.000000"/>
      <damageMapScale value="0.350000"/>
      <damageOffsetScale value="0.350000"/>
      <diffuseTint value="0x00FFFFFF"/>
      <steerWheelMult value="1.000000"/>
      <HDTextureDist value="5.000000"/>
      <lodDistances content="float_array">
        20.000000
        40.000000
        80.000000	
        120.000000
        500.000000
        500.000000
      </lodDistances>
      <minSeatHeight value="0.844"/>
      <identicalModelSpawnDistance value="80"/>
      <maxNumOfSameColor value="1"/>
      <defaultBodyHealth value="1000.000000"/>
      <pretendOccupantsScale value="1.000000"/>
      <visibleSpawnDistScale value="1.000000"/>
      <trackerPathWidth value="2.000000"/>
      <weaponForceMult value="1.000000"/>
      <frequency value="5"/>
      <swankness>SWANKNESS_5</swankness>
      <maxNum value="1"/>
      <flags>FLAG_RICH_CAR FLAG_SPORTS</flags>
      <type>VEHICLE_TYPE_CAR</type>
      <plateType>VPT_FRONT_AND_BACK_PLATES</plateType>
      <dashboardType>VDT_FELTZER</dashboardType>
      <vehicleClass>VC_SPORT</vehicleClass>
      <wheelType>VWT_MUSCLE</wheelType>
      <trailers/>
      <additionalTrailers/>
      <drivers/>
      <extraIncludes/>
      <doorsWithCollisionWhenClosed/>
      <driveableDoors/>
      <bumpersNeedToCollideWithMap value="false"/>
      <needsRopeTexture value="false"/>
      <requiredExtras/>
      <rewards/>
      <cinematicPartCamera>
        <Item>WHEEL_FRONT_RIGHT_CAMERA</Item>
        <Item>WHEEL_FRONT_LEFT_CAMERA</Item>
        <Item>WHEEL_REAR_RIGHT_CAMERA</Item>
        <Item>WHEEL_REAR_LEFT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet/>
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000"/>
      <buoyancySphereSizeScale value="1.000000"/>
      <pOverrideRagdollThreshold type="NULL"/>
      <firstPersonDrivebyData>
        <Item>STD_SENTINEL3_FRONT_LEFT</Item>
        <Item>STD_SENTINEL3_FRONT_RIGHT</Item>
      </firstPersonDrivebyData>
      <numSeatsOverride value="2"/>
    </Item>
    <Item>
      <modelName>superd3</modelName>
      <txdName>superd3</txdName>
      <handlingId>SUPERD3</handlingId>
      <gameName>SUPERD3</gameName>
      <vehicleMakeName>ENUS</vehicleMakeName>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected/>
      <ptfxAssetName>null</ptfxAssetName>
      <audioNameHash>cognoscenti</audioNameHash>
      <layout>LAYOUT_STD_HABANERO</layout>
      <coverBoundOffsets>SUPERD_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout/>
      <cameraName>DEFAULT_FOLLOW_VEHICLE_CAMERA</cameraName>
      <aimCameraName>DEFAULT_THIRD_PERSON_VEHICLE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>VEHICLE_BONNET_CAMERA_STANDARD_LONG</bonnetCameraName>
      <povCameraName>DEFAULT_POV_CAMERA</povCameraName>
      <FirstPersonDriveByIKOffset x="0.000000" y="-0.055000" z="-0.060000"/>
      <FirstPersonDriveByUnarmedIKOffset x="0.000000" y="-0.025000" z="0.000000"/>
      <FirstPersonProjectileDriveByIKOffset x="0.000000" y="0.000000" z="-0.040000"/>
      <FirstPersonProjectileDriveByPassengerIKOffset x="0.000000" y="0.000000" z="-0.040000"/>
      <FirstPersonProjectileDriveByRearLeftIKOffset x="0.000000" y="0.020000" z="0.000000"/>
      <FirstPersonProjectileDriveByRearRightIKOffset x="0.000000" y="0.020000" z="0.000000"/>
      <FirstPersonDriveByLeftPassengerIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonDriveByRightPassengerIKOffset x="0.000000" y="-0.055000" z="-0.060000"/>
      <FirstPersonDriveByRightRearPassengerIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonDriveByLeftPassengerUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonDriveByRightPassengerUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonMobilePhoneOffset x="0.155000" y="0.253000" z="0.563000"/>
      <FirstPersonPassengerMobilePhoneOffset x="0.135000" y="0.135000" z="0.485000"/>
      <FirstPersonMobilePhoneSeatIKOffset>
        <Item>
          <Offset x="0.136000" y="0.116000" z="0.485000"/>
          <SeatIndex value="2"/>
        </Item>
        <Item>
          <Offset x="0.136000" y="0.116000" z="0.485000"/>
          <SeatIndex value="3"/>
        </Item>
      </FirstPersonMobilePhoneSeatIKOffset>
      <PovCameraOffset x="0.000000" y="-0.200000" z="0.700000"/>
      <PovCameraVerticalAdjustmentForRollCage value="0.000000"/>
      <PovPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000"/>
      <PovRearPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000"/>
      <vfxInfoName>VFXVEHICLEINFO_CAR_GENERIC</vfxInfoName>
      <shouldUseCinematicViewMode value="true"/>
      <shouldCameraTransitionOnClimbUpDown value="false"/>
      <shouldCameraIgnoreExiting value="false"/>
      <AllowPretendOccupants value="true"/>
      <AllowJoyriding value="true"/>
      <AllowSundayDriving value="true"/>
      <AllowBodyColorMapping value="true"/>
      <wheelScale value="0.339700"/>
      <wheelScaleRear value="0.339700"/>
      <dirtLevelMin value="0.000000"/>
      <dirtLevelMax value="0.450000"/>
      <envEffScaleMin value="0.000000"/>
      <envEffScaleMax value="1.000000"/>
      <envEffScaleMin2 value="0.000000"/>
      <envEffScaleMax2 value="1.000000"/>
      <damageMapScale value="0.600000"/>
      <damageOffsetScale value="1.000000"/>
      <diffuseTint value="0x00FFFFFF"/>
      <steerWheelMult value="1.000000"/>
      <HDTextureDist value="5.000000"/>
      <lodDistances content="float_array">
         	15.000000
	        30.000000
	        65.000000
	        130.000000
	        500.000000
	        500.000000
      </lodDistances>
      <minSeatHeight value="0.925"/>
      <identicalModelSpawnDistance value="20"/>
      <maxNumOfSameColor value="10"/>
      <defaultBodyHealth value="1000.000000"/>
      <pretendOccupantsScale value="1.000000"/>
      <visibleSpawnDistScale value="1.000000"/>
      <trackerPathWidth value="2.000000"/>
      <weaponForceMult value="1.000000"/>
      <frequency value="80"/>
      <swankness>SWANKNESS_4</swankness>
      <maxNum value="3"/>
      <flags>FLAG_PARKING_SENSORS FLAG_RICH_CAR FLAG_NO_BROKEN_DOWN_SCENARIO</flags>
      <type>VEHICLE_TYPE_CAR</type>
      <plateType>VPT_FRONT_AND_BACK_PLATES</plateType>
      <dashboardType>VDT_CAVALCADE</dashboardType>
      <vehicleClass>VC_SEDAN</vehicleClass>
      <wheelType>VWT_HIEND</wheelType>
      <trailers/>
      <additionalTrailers/>
      <drivers/>
      <extraIncludes>
        <Item>EXTRA_2 EXTRA_3 EXTRA_4 EXTRA_5</Item>
      </extraIncludes>
      <doorsWithCollisionWhenClosed/>
      <driveableDoors/>
      <bumpersNeedToCollideWithMap value="true"/>
      <needsRopeTexture value="false"/>
      <requiredExtras/>
      <rewards/>
      <cinematicPartCamera>
        <Item>WHEEL_FRONT_RIGHT_CAMERA</Item>
        <Item>WHEEL_FRONT_LEFT_CAMERA</Item>
        <Item>WHEEL_REAR_RIGHT_CAMERA</Item>
        <Item>WHEEL_REAR_LEFT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet/>
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000"/>
      <buoyancySphereSizeScale value="1.000000"/>
      <pOverrideRagdollThreshold type="NULL"/>
      <firstPersonDrivebyData>
        <Item>STD_FUTO_FRONT_LEFT</Item>
        <Item>STD_FUTO_FRONT_RIGHT</Item>
        <Item>STD_SULTAN_REAR_LEFT</Item>
        <Item>STD_SULTAN_REAR_RIGHT</Item>
      </firstPersonDrivebyData>
    </Item>
    <Item>
      <modelName>torrence</modelName>
      <txdName>torrence</txdName>
      <handlingId>TORRENCE</handlingId>
      <gameName>TORRENCE</gameName>
      <vehicleMakeName>VAPID</vehicleMakeName>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected/>
      <ptfxAssetName>null</ptfxAssetName>
      <audioNameHash>felon</audioNameHash>
      <layout>LAYOUT_STD_EXITFIXUP</layout>
      <coverBoundOffsets>POLICE3_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout/>
      <cameraName>DEFAULT_FOLLOW_VEHICLE_CAMERA</cameraName>
      <aimCameraName>MID_BOX_VEHICLE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>VEHICLE_BONNET_CAMERA_MID_NEAR</bonnetCameraName>
      <povCameraName>DEFAULT_POV_CAMERA</povCameraName>
      <FirstPersonDriveByIKOffset x="0.000000" y="-0.060000" z="-0.030000"/>
      <FirstPersonDriveByUnarmedIKOffset x="0.000000" y="0.000000" z="-0.020000"/>
      <FirstPersonProjectileDriveByIKOffset x="0.055000" y="-0.075000" z="-0.045000"/>
      <FirstPersonProjectileDriveByPassengerIKOffset x="-0.055000" y="-0.075000" z="-0.045000"/>
      <FirstPersonProjectileDriveByRearLeftIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonProjectileDriveByRearRightIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonDriveByLeftPassengerIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonDriveByRightPassengerIKOffset x="0.000000" y="-0.060000" z="-0.030000"/>
      <FirstPersonDriveByRightRearPassengerIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonDriveByLeftPassengerUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonDriveByRightPassengerUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonMobilePhoneOffset x="0.130000" y="0.275000" z="0.530000"/>
      <FirstPersonPassengerMobilePhoneOffset x="0.136000" y="0.223000" z="0.425000"/>
      <FirstPersonMobilePhoneSeatIKOffset>
        <Item>
          <Offset x="0.136000" y="0.156000" z="0.445000"/>
          <SeatIndex value="2"/>
        </Item>
        <Item>
          <Offset x="0.136000" y="0.156000" z="0.445000"/>
          <SeatIndex value="3"/>
        </Item>
      </FirstPersonMobilePhoneSeatIKOffset>
      <PovCameraOffset x="0.000000" y="-0.175000" z="0.675000"/>
      <PovCameraVerticalAdjustmentForRollCage value="0.000000"/>
      <PovPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000"/>
      <PovRearPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000"/>
      <vfxInfoName>VFXVEHICLEINFO_CAR_GENERIC</vfxInfoName>
      <shouldUseCinematicViewMode value="true"/>
      <shouldCameraTransitionOnClimbUpDown value="false"/>
      <shouldCameraIgnoreExiting value="false"/>
      <AllowPretendOccupants value="true"/>
      <AllowJoyriding value="true"/>
      <AllowSundayDriving value="true"/>
      <AllowBodyColorMapping value="true"/>
      <wheelScale value="0.285000"/>
      <wheelScaleRear value="0.285000"/>
      <dirtLevelMin value="0.000000"/>
      <dirtLevelMax value="0.450000"/>
      <envEffScaleMin value="0.000000"/>
      <envEffScaleMax value="1.000000"/>
      <envEffScaleMin2 value="0.000000"/>
      <envEffScaleMax2 value="1.000000"/>
      <damageMapScale value="0.600000"/>
      <damageOffsetScale value="1.000000"/>
      <diffuseTint value="0x00FFFFFF"/>
      <steerWheelMult value="1.000000"/>
      <HDTextureDist value="5.000000"/>
      <lodDistances content="float_array">
        15.000000
        30.000000
        50.000000
        100.000000
        500.000000
        500.000000
      </lodDistances>
      <minSeatHeight value="0.855"/>
      <identicalModelSpawnDistance value="20"/>
      <maxNumOfSameColor value="10"/>
      <defaultBodyHealth value="1000.000000"/>
      <pretendOccupantsScale value="1.000000"/>
      <visibleSpawnDistScale value="1.000000"/>
      <trackerPathWidth value="2.000000"/>
      <weaponForceMult value="1.000000"/>
      <frequency value="50"/>
      <swankness>SWANKNESS_3</swankness>
      <maxNum value="50"/>
      <flags>FLAG_EXTRAS_REQUIRE FLAG_EXTRAS_STRONG FLAG_AVERAGE_CAR FLAG_PARKING_SENSORS FLAG_RICH_CAR FLAG_HAS_INTERIOR_EXTRAS FLAG_EXTRAS_ONLY_BREAK_WHEN_DESTROYED</flags>
      <type>VEHICLE_TYPE_CAR</type>
      <plateType>VPT_FRONT_AND_BACK_PLATES</plateType>
      <dashboardType>VDT_RACE</dashboardType>
      <vehicleClass>VC_SEDAN</vehicleClass>
      <wheelType>VWT_SPORT</wheelType>
      <trailers/>
      <additionalTrailers/>
      <drivers/>
      <extraIncludes>
        <Item>EXTRA_2</Item>
        <Item>EXTRA_1</Item>
      </extraIncludes>
      <doorsWithCollisionWhenClosed/>
      <driveableDoors/>
      <bumpersNeedToCollideWithMap value="false"/>
      <needsRopeTexture value="false"/>
      <requiredExtras/>
      <rewards/>
      <cinematicPartCamera>
        <Item>WHEEL_FRONT_RIGHT_CAMERA</Item>
        <Item>WHEEL_FRONT_LEFT_CAMERA</Item>
        <Item>WHEEL_REAR_RIGHT_CAMERA</Item>
        <Item>WHEEL_REAR_LEFT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet/>
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000"/>
      <buoyancySphereSizeScale value="1.000000"/>
      <pOverrideRagdollThreshold type="NULL"/>
      <firstPersonDrivebyData>
        <Item>STD_POLICE3_FRONT_LEFT</Item>
        <Item>STD_POLICE3_FRONT_RIGHT</Item>
        <Item>STD_POLICE2_REAR_LEFT</Item>
        <Item>STD_POLICE2_REAR_RIGHT</Item>
      </firstPersonDrivebyData>
    </Item>
    <Item>
      <modelName>velox</modelName>
      <txdName>velox</txdName>
      <handlingId>VELOX</handlingId>
      <gameName>VELOX</gameName>
      <vehicleMakeName>SCHYSTER</vehicleMakeName>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected/>
      <ptfxAssetName>null</ptfxAssetName>
      <audioNameHash>vigero2</audioNameHash>
      <layout>LAYOUT_LOW_RESTRICTED</layout>
      <coverBoundOffsets>GAUNTLET3_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout/>
      <cameraName>FOLLOW_CHEETAH_CAMERA</cameraName>
      <aimCameraName>DEFAULT_THIRD_PERSON_VEHICLE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>VEHICLE_BONNET_CAMERA_MID</bonnetCameraName>
      <povCameraName>DEFAULT_POV_CAMERA</povCameraName>
      <FirstPersonDriveByIKOffset x="0.050000" y="-0.070000" z="-0.045000"/>
      <FirstPersonDriveByUnarmedIKOffset x="-0.050000" y="-0.080000" z="-0.020000"/>
      <FirstPersonProjectileDriveByIKOffset x="0.000000" y="-0.140000" z="-0.050000"/>
      <FirstPersonProjectileDriveByPassengerIKOffset x="0.000000" y="-0.140000" z="-0.050000"/>
      <FirstPersonDriveByRightPassengerIKOffset x="-0.050000" y="-0.070000" z="-0.045000"/>
      <FirstPersonDriveByRightPassengerUnarmedIKOffset x="-0.010000" y="-0.100000" z="-0.020000"/>
      <FirstPersonMobilePhoneOffset x="0.145000" y="0.083000" z="0.535000"/>
      <FirstPersonPassengerMobilePhoneOffset x="0.216000" y="0.048000" z="0.420000"/>
      <PovCameraOffset x="0.000000" y="-0.345000" z="0.665000"/>
      <PovCameraVerticalAdjustmentForRollCage value="0.000000"/>
      <PovPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000"/>
      <vfxInfoName>VFXVEHICLEINFO_CAR_GENERIC</vfxInfoName>
      <shouldUseCinematicViewMode value="true"/>
      <shouldCameraTransitionOnClimbUpDown value="false"/>
      <shouldCameraIgnoreExiting value="false"/>
      <AllowPretendOccupants value="true"/>
      <AllowJoyriding value="true"/>
      <AllowSundayDriving value="true"/>
      <AllowBodyColorMapping value="true"/>
      <wheelScale value="0.220000"/>
      <wheelScaleRear value="0.220000"/>
      <dirtLevelMin value="0.000000"/>
      <dirtLevelMax value="0.550000"/>
      <envEffScaleMin value="0.000000"/>
      <envEffScaleMax value="1.000000"/>
      <envEffScaleMin2 value="0.000000"/>
      <envEffScaleMax2 value="1.000000"/>
      <damageMapScale value="0.350000"/>
      <damageOffsetScale value="0.350000"/>
      <diffuseTint value="0x00FFFFFF"/>
      <steerWheelMult value="1.000000"/>
      <HDTextureDist value="5.000000"/>
      <lodDistances content="float_array">
        10.000000   
        25.000000   
        60.000000   
        120.000000  
        500.000000  
        500.000000
      </lodDistances>
      <minSeatHeight value="0.831"/>
      <identicalModelSpawnDistance value="20"/>
      <maxNumOfSameColor value="10"/>
      <defaultBodyHealth value="1000.000000"/>
      <pretendOccupantsScale value="1.000000"/>
      <visibleSpawnDistScale value="1.000000"/>
      <trackerPathWidth value="2.000000"/>
      <weaponForceMult value="1.000000"/>
      <frequency value="90"/>
      <swankness>SWANKNESS_3</swankness>
      <maxNum value="5"/>
      <flags>FLAG_CAN_HAVE_NEONS FLAG_SPORTS FLAG_PARKING_SENSORS FLAG_RICH_CAR FLAG_AVERAGE_CAR FLAG_HAS_INTERIOR_EXTRAS</flags>
      <type>VEHICLE_TYPE_CAR</type>
      <plateType>VPT_BACK_PLATES</plateType>
      <dashboardType>VDT_RACE</dashboardType>
      <vehicleClass>VC_MUSCLE</vehicleClass>
      <wheelType>VWT_MUSCLE</wheelType>
      <trailers/>
      <additionalTrailers/>
      <drivers/>
      <extraIncludes/>
      <doorsWithCollisionWhenClosed/>
      <driveableDoors/>
      <bumpersNeedToCollideWithMap value="false"/>
      <needsRopeTexture value="false"/>
      <requiredExtras>EXTRA_1</requiredExtras>
      <rewards/>
      <cinematicPartCamera>
        <Item>WHEEL_FRONT_RIGHT_CAMERA</Item>
        <Item>WHEEL_FRONT_LEFT_CAMERA</Item>
        <Item>WHEEL_REAR_RIGHT_CAMERA</Item>
        <Item>WHEEL_REAR_LEFT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet/>
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000"/>
      <buoyancySphereSizeScale value="1.000000"/>
      <pOverrideRagdollThreshold type="NULL"/>
      <firstPersonDrivebyData>
        <Item>LOW_GAUNTLET3_FRONT_LEFT</Item>
        <Item>LOW_GAUNTLET3_FRONT_RIGHT</Item>
      </firstPersonDrivebyData>
    </Item>
    <Item>
      <modelName>velox2</modelName>
      <txdName>velox2</txdName>
      <handlingId>VELOX2</handlingId>
      <gameName>VELOX2</gameName>
      <vehicleMakeName>SCHYSTER</vehicleMakeName>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected/>
      <ptfxAssetName>null</ptfxAssetName>
      <audioNameHash>vigero2</audioNameHash>
      <layout>LAYOUT_LOW_RESTRICTED</layout>
      <coverBoundOffsets>GAUNTLET3_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout/>
      <cameraName>FOLLOW_CHEETAH_CAMERA</cameraName>
      <aimCameraName>DEFAULT_THIRD_PERSON_VEHICLE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>VEHICLE_BONNET_CAMERA_MID</bonnetCameraName>
      <povCameraName>DEFAULT_POV_CAMERA</povCameraName>
      <FirstPersonDriveByIKOffset x="0.050000" y="-0.070000" z="-0.045000"/>
      <FirstPersonDriveByUnarmedIKOffset x="-0.050000" y="-0.080000" z="-0.020000"/>
      <FirstPersonProjectileDriveByIKOffset x="0.000000" y="-0.140000" z="-0.050000"/>
      <FirstPersonProjectileDriveByPassengerIKOffset x="0.000000" y="-0.140000" z="-0.050000"/>
      <FirstPersonDriveByRightPassengerIKOffset x="-0.050000" y="-0.070000" z="-0.045000"/>
      <FirstPersonDriveByRightPassengerUnarmedIKOffset x="-0.010000" y="-0.100000" z="-0.020000"/>
      <FirstPersonMobilePhoneOffset x="0.145000" y="0.083000" z="0.535000"/>
      <FirstPersonPassengerMobilePhoneOffset x="0.216000" y="0.048000" z="0.420000"/>
      <PovCameraOffset x="0.000000" y="-0.345000" z="0.665000"/>
      <PovCameraVerticalAdjustmentForRollCage value="0.000000"/>
      <PovPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000"/>
      <vfxInfoName>VFXVEHICLEINFO_CAR_GENERIC</vfxInfoName>
      <shouldUseCinematicViewMode value="true"/>
      <shouldCameraTransitionOnClimbUpDown value="false"/>
      <shouldCameraIgnoreExiting value="false"/>
      <AllowPretendOccupants value="true"/>
      <AllowJoyriding value="true"/>
      <AllowSundayDriving value="true"/>
      <AllowBodyColorMapping value="true"/>
      <wheelScale value="0.220000"/>
      <wheelScaleRear value="0.220000"/>
      <dirtLevelMin value="0.000000"/>
      <dirtLevelMax value="0.550000"/>
      <envEffScaleMin value="0.000000"/>
      <envEffScaleMax value="1.000000"/>
      <envEffScaleMin2 value="0.000000"/>
      <envEffScaleMax2 value="1.000000"/>
      <damageMapScale value="0.350000"/>
      <damageOffsetScale value="0.350000"/>
      <diffuseTint value="0x00FFFFFF"/>
      <steerWheelMult value="1.000000"/>
      <HDTextureDist value="5.000000"/>
      <lodDistances content="float_array">
        10.000000   
        25.000000   
        60.000000   
        120.000000  
        500.000000  
        500.000000
      </lodDistances>
      <minSeatHeight value="0.831"/>
      <identicalModelSpawnDistance value="20"/>
      <maxNumOfSameColor value="10"/>
      <defaultBodyHealth value="1000.000000"/>
      <pretendOccupantsScale value="1.000000"/>
      <visibleSpawnDistScale value="1.000000"/>
      <trackerPathWidth value="2.000000"/>
      <weaponForceMult value="1.000000"/>
      <frequency value="90"/>
      <swankness>SWANKNESS_3</swankness>
      <maxNum value="5"/>
      <flags>FLAG_CAN_HAVE_NEONS FLAG_SPORTS FLAG_PARKING_SENSORS FLAG_RICH_CAR FLAG_AVERAGE_CAR FLAG_HAS_INTERIOR_EXTRAS</flags>
      <type>VEHICLE_TYPE_CAR</type>
      <plateType>VPT_BACK_PLATES</plateType>
      <dashboardType>VDT_RACE</dashboardType>
      <vehicleClass>VC_MUSCLE</vehicleClass>
      <wheelType>VWT_MUSCLE</wheelType>
      <trailers/>
      <additionalTrailers/>
      <drivers/>
      <extraIncludes/>
      <doorsWithCollisionWhenClosed/>
      <driveableDoors/>
      <bumpersNeedToCollideWithMap value="false"/>
      <needsRopeTexture value="false"/>
      <requiredExtras>EXTRA_1</requiredExtras>
      <rewards/>
      <cinematicPartCamera>
        <Item>WHEEL_FRONT_RIGHT_CAMERA</Item>
        <Item>WHEEL_FRONT_LEFT_CAMERA</Item>
        <Item>WHEEL_REAR_RIGHT_CAMERA</Item>
        <Item>WHEEL_REAR_LEFT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet/>
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000"/>
      <buoyancySphereSizeScale value="1.000000"/>
      <pOverrideRagdollThreshold type="NULL"/>
      <firstPersonDrivebyData>
        <Item>LOW_GAUNTLET3_FRONT_LEFT</Item>
        <Item>LOW_GAUNTLET3_FRONT_RIGHT</Item>
      </firstPersonDrivebyData>
    </Item>
    <Item>
      <modelName>mark1</modelName>
      <txdName>mark1</txdName>
      <handlingId>MARK1</handlingId>
      <gameName>MARK1</gameName>
      <vehicleMakeName>MAXWELL</vehicleMakeName>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected/>
      <ptfxAssetName>null</ptfxAssetName>
      <audioNameHash>mamba</audioNameHash>
      <layout>LAYOUT_LOW_RESTRICTED</layout>
      <coverBoundOffsets>CASCO_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout/>
      <cameraName>DEFAULT_FOLLOW_VEHICLE_CAMERA</cameraName>
      <aimCameraName>DEFAULT_THIRD_PERSON_VEHICLE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>VEHICLE_BONNET_CAMERA_MID_NEAR</bonnetCameraName>
      <vfxInfoName>VFXVEHICLEINFO_CAR_GENERIC</vfxInfoName>
      <povCameraName>DEFAULT_POV_CAMERA</povCameraName>
      <FirstPersonDriveByIKOffset x="0.000000" y="-0.060000" z="-0.040000"/>
      <FirstPersonDriveByUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonProjectileDriveByIKOffset x="0.000000" y="-0.070000" z="0.040000"/>
      <FirstPersonProjectileDriveByPassengerIKOffset x="0.000000" y="0.000000" z="-0.068000"/>
      <FirstPersonProjectileDriveByRearLeftIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonProjectileDriveByRearRightIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonDriveByLeftPassengerIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonDriveByRightPassengerIKOffset x="0.000000" y="-0.060000" z="-0.040000"/>
      <FirstPersonDriveByRightRearPassengerIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonDriveByLeftPassengerUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonDriveByRightPassengerUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonMobilePhoneOffset x="0.108000" y="0.143000" z="0.518000"/>
      <FirstPersonPassengerMobilePhoneOffset x="0.165000" y="0.110000" z="0.400000"/>
      <PovCameraOffset x="0.000000" y="-0.250000" z="0.625000"/>
      <PovCameraVerticalAdjustmentForRollCage value="0.000000"/>
      <PovPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000"/>
      <shouldUseCinematicViewMode value="true"/>
      <shouldCameraTransitionOnClimbUpDown value="false"/>
      <shouldCameraIgnoreExiting value="false"/>
      <AllowPretendOccupants value="true"/>
      <AllowJoyriding value="true"/>
      <AllowSundayDriving value="true"/>
      <AllowBodyColorMapping value="true"/>
      <wheelScale value="0.245000"/>
      <wheelScaleRear value="0.245000"/>
      <dirtLevelMin value="0.000000"/>
      <dirtLevelMax value="0.450000"/>
      <envEffScaleMin value="0.000000"/>
      <envEffScaleMax value="1.000000"/>
      <envEffScaleMin2 value="0.000000"/>
      <envEffScaleMax2 value="1.000000"/>
      <damageMapScale value="0.600000"/>
      <damageOffsetScale value="1.000000"/>
      <diffuseTint value="0x00FFFFFF"/>
      <steerWheelMult value="0.700000"/>
      <HDTextureDist value="5.000000"/>
      <lodDistances content="float_array">
        10.000000
        25.000000
        60.000000
        120.000000
        500.000000
        500.000000
      </lodDistances>
      <minSeatHeight value="0.844"/>
      <identicalModelSpawnDistance value="20"/>
      <maxNumOfSameColor value="1"/>
      <defaultBodyHealth value="1000.000000"/>
      <pretendOccupantsScale value="1.000000"/>
      <visibleSpawnDistScale value="1.000000"/>
      <trackerPathWidth value="2.000000"/>
      <weaponForceMult value="1.000000"/>
      <frequency value="20"/>
      <swankness>SWANKNESS_4</swankness>
      <maxNum value="1"/>
      <flags>FLAG_SPORTS FLAG_EXTRAS_CONVERTIBLE FLAG_RICH_CAR FLAG_RECESSED_TAILLIGHT_CORONAS</flags>
      <type>VEHICLE_TYPE_CAR</type>
      <plateType>VPT_FRONT_AND_BACK_PLATES</plateType>
      <dashboardType>VDT_TRUCK</dashboardType>
      <vehicleClass>VC_SPORT_CLASSIC</vehicleClass>
      <wheelType>VWT_MUSCLE</wheelType>
      <trailers/>
      <additionalTrailers/>
      <drivers/>
      <extraIncludes/>
      <doorsWithCollisionWhenClosed/>
      <driveableDoors/>
      <bumpersNeedToCollideWithMap value="false"/>
      <needsRopeTexture value="false"/>
      <requiredExtras/>
      <rewards/>
      <cinematicPartCamera>
        <Item>WHEEL_FRONT_RIGHT_CAMERA</Item>
        <Item>WHEEL_FRONT_LEFT_CAMERA</Item>
        <Item>WHEEL_REAR_RIGHT_CAMERA</Item>
        <Item>WHEEL_REAR_LEFT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet/>
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000"/>
      <buoyancySphereSizeScale value="1.000000"/>
      <pOverrideRagdollThreshold type="NULL"/>
      <firstPersonDrivebyData>
        <Item>STD_CASCO_FRONT_LEFT</Item>
        <Item>STD_CASCO_FRONT_RIGHT</Item>
      </firstPersonDrivebyData>
    </Item>
    <Item>
      <modelName>vincent2</modelName>
      <txdName>vincent2</txdName>
      <handlingId>VINCENT2</handlingId>
      <gameName>VINCENT2</gameName>
      <vehicleMakeName>MAIBATSU</vehicleMakeName>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected/>
      <ptfxAssetName>null</ptfxAssetName>
      <audioNameHash>OMNIS</audioNameHash>
      <layout>LAYOUT_STANDARD</layout>
      <coverBoundOffsets>SULTAN_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout/>
      <cameraName>DEFAULT_FOLLOW_VEHICLE_CAMERA</cameraName>
      <aimCameraName>MID_BOX_VEHICLE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>VEHICLE_BONNET_CAMERA_NEAR</bonnetCameraName>
      <povCameraName>DEFAULT_POV_CAMERA</povCameraName>
      <FirstPersonDriveByIKOffset x="0.000000" y="-0.060000" z="-0.060000"/>
      <FirstPersonDriveByUnarmedIKOffset x="0.000000" y="-0.025000" z="0.000000"/>
      <FirstPersonProjectileDriveByIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonProjectileDriveByPassengerIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonProjectileDriveByRearLeftIKOffset x="0.000000" y="0.020000" z="0.000000"/>
      <FirstPersonProjectileDriveByRearRightIKOffset x="0.000000" y="0.020000" z="0.000000"/>
      <FirstPersonDriveByLeftPassengerIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonDriveByRightPassengerIKOffset x="0.000000" y="-0.060000" z="-0.060000"/>
      <FirstPersonDriveByRightRearPassengerIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonDriveByLeftPassengerUnarmedIKOffset x="0.000000" y="-0.080000" z="0.000000"/>
      <FirstPersonDriveByRightPassengerUnarmedIKOffset x="0.000000" y="-0.080000" z="0.000000"/>
      <FirstPersonMobilePhoneOffset x="0.155000" y="0.295000" z="0.533000"/>
      <FirstPersonPassengerMobilePhoneOffset x="0.135000" y="0.175000" z="0.465000"/>
      <FirstPersonMobilePhoneSeatIKOffset>
        <Item>
          <Offset x="0.136000" y="0.126000" z="0.475000"/>
          <SeatIndex value="2"/>
        </Item>
        <Item>
          <Offset x="0.136000" y="0.126000" z="0.475000"/>
          <SeatIndex value="3"/>
        </Item>
      </FirstPersonMobilePhoneSeatIKOffset>
      <PovCameraOffset x="0.000000" y="-0.135000" z="0.670000"/>
      <PovCameraVerticalAdjustmentForRollCage value="-0.045000"/>
      <PovPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000"/>
      <PovRearPassengerCameraOffset x="0.000000" y="-0.150000" z="0.000000"/>
      <vfxInfoName>VFXVEHICLEINFO_CAR_GENERIC</vfxInfoName>
      <shouldUseCinematicViewMode value="true"/>
      <shouldCameraTransitionOnClimbUpDown value="false"/>
      <shouldCameraIgnoreExiting value="false"/>
      <AllowPretendOccupants value="true"/>
      <AllowJoyriding value="true"/>
      <AllowSundayDriving value="true"/>
      <AllowBodyColorMapping value="true"/>
      <wheelScale value="0.256300"/>
      <wheelScaleRear value="0.256300"/>
      <dirtLevelMin value="0.000000"/>
      <dirtLevelMax value="3.700000"/>
      <envEffScaleMin value="0.000000"/>
      <envEffScaleMax value="1.000000"/>
      <envEffScaleMin2 value="0.000000"/>
      <envEffScaleMax2 value="1.000000"/>
      <damageMapScale value="0.600000"/>
      <damageOffsetScale value="1.000000"/>
      <diffuseTint value="0x00FFFFFF"/>
      <steerWheelMult value="0.700000"/>
      <HDTextureDist value="5.000000"/>
      <lodDistances content="float_array">
        15.000000
        30.000000
        50.000000
        100.000000
        500.000000
        500.000000
      </lodDistances>
      <minSeatHeight value="0.832"/>
      <identicalModelSpawnDistance value="20"/>
      <maxNumOfSameColor value="10"/>
      <defaultBodyHealth value="1000.000000"/>
      <pretendOccupantsScale value="1.000000"/>
      <visibleSpawnDistScale value="1.000000"/>
      <trackerPathWidth value="2.000000"/>
      <weaponForceMult value="1.000000"/>
      <frequency value="65"/>
      <swankness>SWANKNESS_3</swankness>
      <maxNum value="200"/>
      <flags>FLAG_AVERAGE_CAR FLAG_EXTRAS_ALL FLAG_HAS_LIVERY</flags>
      <type>VEHICLE_TYPE_CAR</type>
      <plateType>VPT_BACK_PLATES</plateType>
      <dashboardType>VDT_RACE</dashboardType>
      <vehicleClass>VC_SPORT</vehicleClass>
      <wheelType>VWT_SPORT</wheelType>
      <trailers/>
      <additionalTrailers/>
      <drivers/>
      <extraIncludes/>
      <doorsWithCollisionWhenClosed/>
      <driveableDoors/>
      <bumpersNeedToCollideWithMap value="false"/>
      <needsRopeTexture value="false"/>
      <requiredExtras/>
      <rewards/>
      <cinematicPartCamera>
        <Item>WHEEL_FRONT_RIGHT_CAMERA</Item>
        <Item>WHEEL_FRONT_LEFT_CAMERA</Item>
        <Item>WHEEL_REAR_RIGHT_CAMERA</Item>
        <Item>WHEEL_REAR_LEFT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet/>
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000"/>
      <buoyancySphereSizeScale value="1.000000"/>
      <pOverrideRagdollThreshold type="NULL"/>
      <firstPersonDrivebyData>
        <Item>STD_SULTAN_FRONT_LEFT</Item>
        <Item>STD_SULTAN_FRONT_RIGHT</Item>
        <Item>STD_SULTAN_REAR_LEFT</Item>
        <Item>STD_SULTAN_REAR_RIGHT</Item>
      </firstPersonDrivebyData>
    </Item>
    <Item>
      <modelName>vincent3</modelName>
      <txdName>vincent3</txdName>
      <handlingId>VINCENT3</handlingId>
      <gameName>VINCENT3</gameName>
      <vehicleMakeName>MAIBATSU</vehicleMakeName>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected/>
      <ptfxAssetName>null</ptfxAssetName>
      <audioNameHash>OMNIS</audioNameHash>
      <layout>LAYOUT_STANDARD</layout>
      <coverBoundOffsets>SULTAN_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout/>
      <cameraName>DEFAULT_FOLLOW_VEHICLE_CAMERA</cameraName>
      <aimCameraName>MID_BOX_VEHICLE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>VEHICLE_BONNET_CAMERA_NEAR</bonnetCameraName>
      <povCameraName>DEFAULT_POV_CAMERA</povCameraName>
      <FirstPersonDriveByIKOffset x="0.000000" y="-0.060000" z="-0.060000"/>
      <FirstPersonDriveByUnarmedIKOffset x="0.000000" y="-0.025000" z="0.000000"/>
      <FirstPersonProjectileDriveByIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonProjectileDriveByPassengerIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonProjectileDriveByRearLeftIKOffset x="0.000000" y="0.020000" z="0.000000"/>
      <FirstPersonProjectileDriveByRearRightIKOffset x="0.000000" y="0.020000" z="0.000000"/>
      <FirstPersonDriveByLeftPassengerIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonDriveByRightPassengerIKOffset x="0.000000" y="-0.060000" z="-0.060000"/>
      <FirstPersonDriveByRightRearPassengerIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonDriveByLeftPassengerUnarmedIKOffset x="0.000000" y="-0.080000" z="0.000000"/>
      <FirstPersonDriveByRightPassengerUnarmedIKOffset x="0.000000" y="-0.080000" z="0.000000"/>
      <FirstPersonMobilePhoneOffset x="0.155000" y="0.295000" z="0.533000"/>
      <FirstPersonPassengerMobilePhoneOffset x="0.135000" y="0.175000" z="0.465000"/>
      <FirstPersonMobilePhoneSeatIKOffset>
        <Item>
          <Offset x="0.136000" y="0.126000" z="0.475000"/>
          <SeatIndex value="2"/>
        </Item>
        <Item>
          <Offset x="0.136000" y="0.126000" z="0.475000"/>
          <SeatIndex value="3"/>
        </Item>
      </FirstPersonMobilePhoneSeatIKOffset>
      <PovCameraOffset x="0.000000" y="-0.135000" z="0.670000"/>
      <PovCameraVerticalAdjustmentForRollCage value="-0.045000"/>
      <PovPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000"/>
      <PovRearPassengerCameraOffset x="0.000000" y="-0.150000" z="0.000000"/>
      <vfxInfoName>VFXVEHICLEINFO_CAR_GENERIC</vfxInfoName>
      <shouldUseCinematicViewMode value="true"/>
      <shouldCameraTransitionOnClimbUpDown value="false"/>
      <shouldCameraIgnoreExiting value="false"/>
      <AllowPretendOccupants value="true"/>
      <AllowJoyriding value="true"/>
      <AllowSundayDriving value="true"/>
      <AllowBodyColorMapping value="true"/>
      <wheelScale value="0.256300"/>
      <wheelScaleRear value="0.256300"/>
      <dirtLevelMin value="0.000000"/>
      <dirtLevelMax value="3.700000"/>
      <envEffScaleMin value="0.000000"/>
      <envEffScaleMax value="1.000000"/>
      <envEffScaleMin2 value="0.000000"/>
      <envEffScaleMax2 value="1.000000"/>
      <damageMapScale value="0.600000"/>
      <damageOffsetScale value="1.000000"/>
      <diffuseTint value="0x00FFFFFF"/>
      <steerWheelMult value="0.700000"/>
      <HDTextureDist value="5.000000"/>
      <lodDistances content="float_array">
        15.000000
        30.000000
        50.000000
        100.000000
        500.000000
        500.000000
      </lodDistances>
      <minSeatHeight value="0.832"/>
      <identicalModelSpawnDistance value="20"/>
      <maxNumOfSameColor value="10"/>
      <defaultBodyHealth value="1000.000000"/>
      <pretendOccupantsScale value="1.000000"/>
      <visibleSpawnDistScale value="1.000000"/>
      <trackerPathWidth value="2.000000"/>
      <weaponForceMult value="1.000000"/>
      <frequency value="65"/>
      <swankness>SWANKNESS_3</swankness>
      <maxNum value="200"/>
      <flags>FLAG_AVERAGE_CAR FLAG_EXTRAS_ALL FLAG_HAS_LIVERY</flags>
      <type>VEHICLE_TYPE_CAR</type>
      <plateType>VPT_BACK_PLATES</plateType>
      <dashboardType>VDT_RACE</dashboardType>
      <vehicleClass>VC_SPORT</vehicleClass>
      <wheelType>VWT_SPORT</wheelType>
      <trailers/>
      <additionalTrailers/>
      <drivers/>
      <extraIncludes/>
      <doorsWithCollisionWhenClosed/>
      <driveableDoors/>
      <bumpersNeedToCollideWithMap value="false"/>
      <needsRopeTexture value="false"/>
      <requiredExtras/>
      <rewards/>
      <cinematicPartCamera>
        <Item>WHEEL_FRONT_RIGHT_CAMERA</Item>
        <Item>WHEEL_FRONT_LEFT_CAMERA</Item>
        <Item>WHEEL_REAR_RIGHT_CAMERA</Item>
        <Item>WHEEL_REAR_LEFT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet/>
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000"/>
      <buoyancySphereSizeScale value="1.000000"/>
      <pOverrideRagdollThreshold type="NULL"/>
      <firstPersonDrivebyData>
        <Item>STD_SULTAN_FRONT_LEFT</Item>
        <Item>STD_SULTAN_FRONT_RIGHT</Item>
        <Item>STD_SULTAN_REAR_LEFT</Item>
        <Item>STD_SULTAN_REAR_RIGHT</Item>
      </firstPersonDrivebyData>
    </Item>
    <Item>
      <modelName>vorstand</modelName>
      <txdName>vorstand</txdName>
      <handlingId>VORSTAND</handlingId>
      <gameName>VORSTAND</gameName>
      <vehicleMakeName>UBERMACH</vehicleMakeName>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected/>
      <ptfxAssetName>null</ptfxAssetName>
      <audioNameHash>ZION3</audioNameHash>
      <layout>LAYOUT_STANDARD</layout>
      <coverBoundOffsets>ZION3_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout/>
      <cameraName>DEFAULT_FOLLOW_VEHICLE_CAMERA</cameraName>
      <aimCameraName>DEFAULT_THIRD_PERSON_VEHICLE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>VEHICLE_BONNET_CAMERA_LOW_LOW</bonnetCameraName>
      <povCameraName>DEFAULT_POV_CAMERA</povCameraName>
      <FirstPersonDriveByIKOffset x="0.070000" y="-0.105000" z="-0.050000"/>
      <FirstPersonDriveByUnarmedIKOffset x="-0.010000" y="-0.120000" z="-0.020000"/>
      <FirstPersonProjectileDriveByIKOffset x="0.000000" y="-0.110000" z="-0.040000"/>
      <FirstPersonProjectileDriveByPassengerIKOffset x="0.000000" y="-0.110000" z="-0.040000"/>
      <FirstPersonDriveByRightPassengerIKOffset x="-0.070000" y="-0.105000" z="-0.050000"/>
      <FirstPersonDriveByRightPassengerUnarmedIKOffset x="-0.010000" y="-0.120000" z="-0.020000"/>
      <FirstPersonMobilePhoneOffset x="0.160000" y="0.152000" z="0.505000"/>
      <FirstPersonPassengerMobilePhoneOffset x="0.220000" y="0.120000" z="0.390000"/>
      <PovCameraOffset x="0.010000" y="-0.230000" z="0.630000"/>
      <PovCameraVerticalAdjustmentForRollCage value="0.000000"/>
      <PovPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000"/>
      <PovRearPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000"/>
      <vfxInfoName>VFXVEHICLEINFO_CAR_GENERIC</vfxInfoName>
      <shouldUseCinematicViewMode value="true"/>
      <shouldCameraTransitionOnClimbUpDown value="false"/>
      <shouldCameraIgnoreExiting value="false"/>
      <AllowPretendOccupants value="true"/>
      <AllowJoyriding value="true"/>
      <AllowSundayDriving value="true"/>
      <AllowBodyColorMapping value="true"/>
      <wheelScale value="0.230000"/>
      <wheelScaleRear value="0.230000"/>
      <dirtLevelMin value="0.000000"/>
      <dirtLevelMax value="0.450000"/>
      <envEffScaleMin value="0.000000"/>
      <envEffScaleMax value="1.000000"/>
      <envEffScaleMin2 value="0.000000"/>
      <envEffScaleMax2 value="1.000000"/>
      <damageMapScale value="0.600000"/>
      <damageOffsetScale value="1.000000"/>
      <diffuseTint value="0x00FFFFFF"/>
      <steerWheelMult value="1.000000"/>
      <HDTextureDist value="5.000000"/>
      <lodDistances content="float_array">
        20.000000
        40.000000
        80.000000
        120.000000
        500.000000
        500.000000
      </lodDistances>
      <minSeatHeight value="0.844"/>
      <identicalModelSpawnDistance value="80"/>
      <maxNumOfSameColor value="1"/>
      <defaultBodyHealth value="1000.000000"/>
      <pretendOccupantsScale value="1.000000"/>
      <visibleSpawnDistScale value="1.000000"/>
      <trackerPathWidth value="2.000000"/>
      <weaponForceMult value="1.000000"/>
      <frequency value="5"/>
      <swankness>SWANKNESS_4</swankness>
      <maxNum value="1"/>
      <flags>FLAG_SPORTS FLAG_RICH_CAR FLAG_NO_BROKEN_DOWN_SCENARIO FLAG_DONT_TIMESLICE_WHEELS</flags>
      <type>VEHICLE_TYPE_CAR</type>
      <plateType>VPT_FRONT_AND_BACK_PLATES</plateType>
      <dashboardType>VDT_FEROCI</dashboardType>
      <vehicleClass>VC_SPORT</vehicleClass>
      <wheelType>VWT_SPORT</wheelType>
      <trailers/>
      <additionalTrailers/>
      <drivers/>
      <extraIncludes/>
      <doorsWithCollisionWhenClosed/>
      <driveableDoors/>
      <bumpersNeedToCollideWithMap value="false"/>
      <needsRopeTexture value="true"/>
      <requiredExtras/>
      <rewards/>
      <cinematicPartCamera>
        <Item>WHEEL_WIDE_REAR_RIGHT_CAMERA</Item>
        <Item>WHEEL_WIDE_REAR_LEFT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet>Truck</NmBraceOverrideSet>
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000"/>
      <buoyancySphereSizeScale value="1.000000"/>
      <firstPersonDrivebyData>
        <Item>STD_ZION3_FRONT_LEFT</Item>
        <Item>STD_ZION3_FRONT_RIGHT</Item>
      </firstPersonDrivebyData>
    </Item>
    <Item>
      <modelName>hotwee</modelName>
      <txdName>hotwee</txdName>
      <handlingId>HOTWEE</handlingId>
      <gameName>HOTWEE</gameName>
      <vehicleMakeName>BF</vehicleMakeName>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected/>
      <ptfxAssetName>veh_sm_car_small</ptfxAssetName>
      <audioNameHash>WEEVIL</audioNameHash>
      <layout>LAYOUT_STD_WEEVIL</layout>
      <coverBoundOffsets>BRIOSO2_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout/>
      <cameraName>FOLLOW_CHEETAH_CAMERA</cameraName>
      <aimCameraName>DEFAULT_THIRD_PERSON_VEHICLE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>VEHICLE_BONNET_CAMERA_NEAR</bonnetCameraName>
      <povCameraName>DEFAULT_POV_CAMERA_NO_REVERSE</povCameraName>
      <FirstPersonDriveByIKOffset x="0.040000" y="-0.075000" z="-0.030000"/>
      <FirstPersonDriveByUnarmedIKOffset x="-0.030000" y="-0.140000" z="-0.010000"/>
      <FirstPersonProjectileDriveByIKOffset x="0.110000" y="-0.100000" z="-0.065000"/>
      <FirstPersonProjectileDriveByPassengerIKOffset x="-0.110000" y="-0.100000" z="-0.065000"/>
      <FirstPersonDriveByRightPassengerIKOffset x="0.000000" y="-0.030000" z="-0.020000"/>
      <FirstPersonDriveByRightPassengerUnarmedIKOffset x="0.000000" y="-0.120000" z="-0.025000"/>
      <FirstPersonMobilePhoneOffset x="0.125000" y="0.180000" z="0.525000"/>
      <FirstPersonPassengerMobilePhoneOffset x="0.201000" y="0.120000" z="0.423000"/>
      <PovCameraOffset x="0.000000" y="-0.235000" z="0.635000"/>
      <PovCameraVerticalAdjustmentForRollCage value="0.000000"/>
      <PovPassengerCameraOffset x="0.000000" y="0.050000" z="0.000000"/>
      <vfxInfoName>VFXVEHICLEINFO_CAR_GENERIC_SMALL</vfxInfoName>
      <shouldUseCinematicViewMode value="true"/>
      <shouldCameraTransitionOnClimbUpDown value="false"/>
      <shouldCameraIgnoreExiting value="false"/>
      <AllowPretendOccupants value="true"/>
      <AllowJoyriding value="true"/>
      <AllowSundayDriving value="true"/>
      <AllowBodyColorMapping value="true"/>
      <wheelScale value="0.195300"/>
      <wheelScaleRear value="0.195300"/>
      <dirtLevelMin value="0.000000"/>
      <dirtLevelMax value="0.300000"/>
      <envEffScaleMin value="0.000000"/>
      <envEffScaleMax value="0.000000"/>
      <envEffScaleMin2 value="0.000000"/>
      <envEffScaleMax2 value="0.000000"/>
      <damageMapScale value="0.10000"/>
      <damageOffsetScale value="0.10000"/>
      <diffuseTint value="0x00FFFFFF"/>
      <steerWheelMult value="0.500000"/>
      <HDTextureDist value="5.000000"/>
      <lodDistances content="float_array">
        10.000000   
        25.000000   
        60.000000   
        120.000000  
        500.000000  
        500.000000
      </lodDistances>
      <minSeatHeight value="0.881"/>
      <identicalModelSpawnDistance value="20"/>
      <maxNumOfSameColor value="1"/>
      <defaultBodyHealth value="1000.000000"/>
      <pretendOccupantsScale value="1.000000"/>
      <visibleSpawnDistScale value="1.000000"/>
      <trackerPathWidth value="2.000000"/>
      <weaponForceMult value="1.000000"/>
      <frequency value="30"/>
      <swankness>SWANKNESS_5</swankness>
      <maxNum value="5"/>
      <flags>FLAG_EXTRAS_ALL FLAG_EXTRAS_STRONG FLAG FLAG_AVERAGE_CAR FLAG_HAS_INTERIOR_EXTRAS FLAG_CAN_HAVE_NEONS FLAG_HAS_SUPERCHARGER FLAG_INCREASE_CAMBER_WITH_SUSPENSION_MOD</flags>
      <type>VEHICLE_TYPE_CAR</type>
      <plateType>VPT_BACK_PLATES</plateType>
      <dashboardType>VDT_DUKES</dashboardType>
      <vehicleClass>VC_COMPACTS</vehicleClass>
      <wheelType>VWT_MUSCLE</wheelType>
      <trailers/>
      <additionalTrailers/>
      <drivers/>
      <extraIncludes/>
      <doorsWithCollisionWhenClosed/>
      <driveableDoors/>
      <bumpersNeedToCollideWithMap value="false"/>
      <needsRopeTexture value="false"/>
      <requiredExtras/>
      <rewards/>
      <cinematicPartCamera>
        <Item>WHEEL_FRONT_RIGHT_CAMERA</Item>
        <Item>WHEEL_FRONT_LEFT_CAMERA</Item>
        <Item>WHEEL_REAR_RIGHT_CAMERA</Item>
        <Item>WHEEL_REAR_LEFT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet/>
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000"/>
      <buoyancySphereSizeScale value="1.000000"/>
      <pOverrideRagdollThreshold type="NULL"/>
      <firstPersonDrivebyData>
        <Item>STD_WEEVIL_FRONT_LEFT</Item>
        <Item>STD_WEEVIL_FRONT_RIGHT</Item>
      </firstPersonDrivebyData>
      <numSeatsOverride value="2"/>
    </Item>
    <Item>
      <modelName>zion4</modelName>
      <txdName>zion4</txdName>
      <handlingId>ZION4</handlingId>
      <gameName>ZION4</gameName>
      <vehicleMakeName>UBERMACH</vehicleMakeName>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected/>
      <ptfxAssetName>null</ptfxAssetName>
      <audioNameHash>vamos</audioNameHash>
      <layout>LAYOUT_LOW</layout>
      <coverBoundOffsets>COMET_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout/>
      <cameraName>DEFAULT_FOLLOW_VEHICLE_CAMERA</cameraName>
      <aimCameraName>DEFAULT_THIRD_PERSON_VEHICLE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>VEHICLE_BONNET_CAMERA_LOW_LOW</bonnetCameraName>
      <povCameraName>DEFAULT_POV_CAMERA</povCameraName>
      <FirstPersonDriveByIKOffset x="0.070000" y="-0.105000" z="-0.050000"/>
      <FirstPersonDriveByUnarmedIKOffset x="-0.010000" y="-0.120000" z="-0.020000"/>
      <FirstPersonProjectileDriveByIKOffset x="0.000000" y="-0.110000" z="-0.040000"/>
      <FirstPersonProjectileDriveByPassengerIKOffset x="0.000000" y="-0.110000" z="-0.040000"/>
      <FirstPersonDriveByRightPassengerIKOffset x="-0.070000" y="-0.105000" z="-0.050000"/>
      <FirstPersonDriveByRightPassengerUnarmedIKOffset x="-0.010000" y="-0.120000" z="-0.020000"/>
      <FirstPersonMobilePhoneOffset x="0.160000" y="0.152000" z="0.505000"/>
      <FirstPersonPassengerMobilePhoneOffset x="0.220000" y="0.120000" z="0.390000"/>
      <PovCameraOffset x="0.010000" y="-0.230000" z="0.630000"/>
      <PovCameraVerticalAdjustmentForRollCage value="0.000000"/>
      <PovPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000"/>
      <PovRearPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000"/>
      <vfxInfoName>VFXVEHICLEINFO_CAR_GENERIC</vfxInfoName>
      <shouldUseCinematicViewMode value="true"/>
      <shouldCameraTransitionOnClimbUpDown value="false"/>
      <shouldCameraIgnoreExiting value="false"/>
      <AllowPretendOccupants value="true"/>
      <AllowJoyriding value="true"/>
      <AllowSundayDriving value="true"/>
      <AllowBodyColorMapping value="true"/>
      <wheelScale value="0.230000"/>
      <wheelScaleRear value="0.230000"/>
      <dirtLevelMin value="0.000000"/>
      <dirtLevelMax value="0.450000"/>
      <envEffScaleMin value="0.000000"/>
      <envEffScaleMax value="1.000000"/>
      <envEffScaleMin2 value="0.000000"/>
      <envEffScaleMax2 value="1.000000"/>
      <damageMapScale value="0.350000"/>
      <damageOffsetScale value="0.350000"/>
      <diffuseTint value="0x00FFFFFF"/>
      <steerWheelMult value="1.000000"/>
      <HDTextureDist value="5.000000"/>
      <lodDistances content="float_array">
        15.000000
        30.000000
        60.000000	
        120.000000
        500.000000
        500.000000
      </lodDistances>
      <minSeatHeight value="0.881"/>
      <identicalModelSpawnDistance value="20"/>
      <maxNumOfSameColor value="1"/>
      <defaultBodyHealth value="1000.000000"/>
      <pretendOccupantsScale value="1.000000"/>
      <visibleSpawnDistScale value="1.000000"/>
      <trackerPathWidth value="2.000000"/>
      <weaponForceMult value="1.000000"/>
      <frequency value="30"/>
      <swankness>SWANKNESS_5</swankness>
      <maxNum value="5"/>
      <flags>FLAG_CAN_HAVE_NEONS FLAG_EXTRAS_STRONG FLAG_AVERAGE_CAR FLAG_POOR_CAR FLAG_RECESSED_HEADLIGHT_CORONAS FLAG_RECESSED_TAILLIGHT_CORONAS FLAG_HAS_INTERIOR_EXTRAS FLAG_USE_AIRCRAFT_STYLE_WEAPON_TARGETING FLAG_HAS_GULL_WING_DOORS FLAG_USE_ROOT_AS_BASE_LOCKON_POS FLAG_DONT_TIMESLICE_WHEELS FLAG_ATTACH_TRAILER_ON_HIGHWAY FLAG_ATTACH_TRAILER_IN_CITY</flags>
      <type>VEHICLE_TYPE_CAR</type>
      <plateType>VPT_BACK_PLATES</plateType>
      <dashboardType>VDT_RACE</dashboardType>
      <vehicleClass>VC_SPORT_CLASSIC</vehicleClass>
      <wheelType>VWT_SPORT</wheelType>
      <trailers>
        <Item>boattrailer</Item>
        <Item>trailersmall</Item>
        <Item>trailersmall2</Item>
      </trailers>
      <additionalTrailers>
        <Item>trailersmall2</Item>
      </additionalTrailers>
      <drivers/>
      <extraIncludes/>
      <doorsWithCollisionWhenClosed/>
      <driveableDoors/>
      <bumpersNeedToCollideWithMap value="false"/>
      <needsRopeTexture value="false"/>
      <requiredExtras/>
      <rewards/>
      <cinematicPartCamera>
        <Item>WHEEL_FRONT_RIGHT_CAMERA</Item>
        <Item>WHEEL_FRONT_LEFT_CAMERA</Item>
        <Item>WHEEL_REAR_RIGHT_CAMERA</Item>
        <Item>WHEEL_REAR_LEFT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet/>
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000"/>
      <buoyancySphereSizeScale value="1.000000"/>
      <pOverrideRagdollThreshold type="NULL"/>
      <firstPersonDrivebyData>
        <Item>LOW_RAPIDGT_FRONT_LEFT</Item>
        <Item>LOW_RAPIDGT_FRONT_RIGHT</Item>
      </firstPersonDrivebyData>
    </Item>
    <Item>
      <modelName>zodiac</modelName>
      <txdName>zodiac</txdName>
      <handlingId>ZODIAC</handlingId>
      <gameName>ZODIAC</gameName>
      <vehicleMakeName>VULCAR</vehicleMakeName>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected/>
      <ptfxAssetName>null</ptfxAssetName>
      <audioNameHash>ZODIAC</audioNameHash>
      <layout>LAYOUT_STANDARD</layout>
      <coverBoundOffsets>NEBULA_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout/>
      <cameraName>DEFAULT_FOLLOW_VEHICLE_CAMERA</cameraName>
      <aimCameraName>DEFAULT_THIRD_PERSON_VEHICLE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>VEHICLE_BONNET_CAMERA_STANDARD_LONG_EXTRA_LOW</bonnetCameraName>
      <povCameraName>REDUCED_NEAR_CLIP_POV_CAMERA</povCameraName>
      <FirstPersonDriveByIKOffset x="0.030000" y="-0.100000" z="-0.060000"/>
      <FirstPersonDriveByUnarmedIKOffset x="-0.010000" y="-0.100000" z="-0.010000"/>
      <FirstPersonProjectileDriveByIKOffset x="0.060000" y="-0.125000" z="-0.040000"/>
      <FirstPersonProjectileDriveByPassengerIKOffset x="-0.060000" y="-0.125000" z="-0.040000"/>
      <FirstPersonDriveByRightPassengerIKOffset x="-0.030000" y="-0.100000" z="-0.060000"/>
      <FirstPersonDriveByRightPassengerUnarmedIKOffset x="-0.010000" y="-0.100000" z="-0.010000"/>
      <FirstPersonMobilePhoneOffset x="0.150000" y="0.080000" z="0.525000"/>
      <FirstPersonPassengerMobilePhoneOffset x="0.201000" y="0.060000" z="0.402000"/>
      <PovCameraOffset x="0.000000" y="-0.220000" z="0.630000"/>
      <PovCameraVerticalAdjustmentForRollCage value="0.000000"/>
      <PovPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000"/>
      <vfxInfoName>VFXVEHICLEINFO_CAR_GENERIC</vfxInfoName>
      <shouldUseCinematicViewMode value="true"/>
      <shouldCameraTransitionOnClimbUpDown value="false"/>
      <shouldCameraIgnoreExiting value="false"/>
      <AllowPretendOccupants value="true"/>
      <AllowJoyriding value="true"/>
      <AllowSundayDriving value="true"/>
      <AllowBodyColorMapping value="true"/>
      <wheelScale value="0.211900"/>
      <wheelScaleRear value="0.211900"/>
      <dirtLevelMin value="0.000000"/>
      <dirtLevelMax value="0.450000"/>
      <envEffScaleMin value="0.000000"/>
      <envEffScaleMax value="1.000000"/>
      <envEffScaleMin2 value="0.000000"/>
      <envEffScaleMax2 value="1.000000"/>
      <damageMapScale value="0.40000"/>
      <damageOffsetScale value="0.40000"/>
      <diffuseTint value="0x00FFFFFF"/>
      <steerWheelMult value="1.000000"/>
      <HDTextureDist value="5.000000"/>
      <lodDistances content="float_array">
        20.000000
        40.000000
        80.000000	
        120.000000
        500.000000
        500.000000
      </lodDistances>
      <minSeatHeight value="0.844"/>
      <identicalModelSpawnDistance value="20"/>
      <maxNumOfSameColor value="1"/>
      <defaultBodyHealth value="1000.000000"/>
      <pretendOccupantsScale value="1.000000"/>
      <visibleSpawnDistScale value="1.000000"/>
      <trackerPathWidth value="2.000000"/>
      <weaponForceMult value="1.000000"/>
      <frequency value="30"/>
      <swankness>SWANKNESS_4</swankness>
      <maxNum value="10"/>
      <flags>FLAG_EXTRAS_STRONG FLAG_AVERAGE_CAR FLAG_HAS_INTERIOR_EXTRAS FLAG_HAS_SUPERCHARGER FLAG_INCREASE_CAMBER_WITH_SUSPENSION_MOD</flags>
      <type>VEHICLE_TYPE_CAR</type>
      <plateType>VPT_FRONT_AND_BACK_PLATES</plateType>
      <dashboardType>VDT_DUKES</dashboardType>
      <vehicleClass>VC_SPORT_CLASSIC</vehicleClass>
      <wheelType>VWT_MUSCLE</wheelType>
      <trailers/>
      <additionalTrailers/>
      <drivers/>
      <extraIncludes/>
      <doorsWithCollisionWhenClosed/>
      <driveableDoors/>
      <bumpersNeedToCollideWithMap value="false"/>
      <needsRopeTexture value="false"/>
      <requiredExtras/>
      <rewards/>
      <cinematicPartCamera>
        <Item>WHEEL_FRONT_RIGHT_CAMERA</Item>
        <Item>WHEEL_FRONT_LEFT_CAMERA</Item>
        <Item>WHEEL_REAR_RIGHT_CAMERA</Item>
        <Item>WHEEL_REAR_LEFT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet/>
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000"/>
      <buoyancySphereSizeScale value="1.000000"/>
      <pOverrideRagdollThreshold type="NULL"/>
      <firstPersonDrivebyData>
        <Item>STD_NEBULA_FRONT_LEFT</Item>
        <Item>STD_NEBULA_FRONT_RIGHT</Item>
      </firstPersonDrivebyData>
      <numSeatsOverride value="2"/>
    </Item>
    <Item>
      <modelName>zodiacr</modelName>
      <txdName>zodiacr</txdName>
      <handlingId>ZODIACR</handlingId>
      <gameName>ZODIACR</gameName>
      <vehicleMakeName>VULCAR</vehicleMakeName>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected/>
      <ptfxAssetName>null</ptfxAssetName>
      <audioNameHash>ZODIACR</audioNameHash>
      <layout>LAYOUT_STANDARD</layout>
      <coverBoundOffsets>NEBULA_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout/>
      <cameraName>DEFAULT_FOLLOW_VEHICLE_CAMERA</cameraName>
      <aimCameraName>DEFAULT_THIRD_PERSON_VEHICLE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>VEHICLE_BONNET_CAMERA_STANDARD_LONG_EXTRA_LOW</bonnetCameraName>
      <povCameraName>REDUCED_NEAR_CLIP_POV_CAMERA</povCameraName>
      <FirstPersonDriveByIKOffset x="0.030000" y="-0.100000" z="-0.060000"/>
      <FirstPersonDriveByUnarmedIKOffset x="-0.010000" y="-0.100000" z="-0.010000"/>
      <FirstPersonProjectileDriveByIKOffset x="0.060000" y="-0.125000" z="-0.040000"/>
      <FirstPersonProjectileDriveByPassengerIKOffset x="-0.060000" y="-0.125000" z="-0.040000"/>
      <FirstPersonDriveByRightPassengerIKOffset x="-0.030000" y="-0.100000" z="-0.060000"/>
      <FirstPersonDriveByRightPassengerUnarmedIKOffset x="-0.010000" y="-0.100000" z="-0.010000"/>
      <FirstPersonMobilePhoneOffset x="0.150000" y="0.080000" z="0.525000"/>
      <FirstPersonPassengerMobilePhoneOffset x="0.201000" y="0.060000" z="0.402000"/>
      <PovCameraOffset x="0.000000" y="-0.220000" z="0.630000"/>
      <PovCameraVerticalAdjustmentForRollCage value="0.000000"/>
      <PovPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000"/>
      <vfxInfoName>VFXVEHICLEINFO_CAR_GENERIC</vfxInfoName>
      <shouldUseCinematicViewMode value="true"/>
      <shouldCameraTransitionOnClimbUpDown value="false"/>
      <shouldCameraIgnoreExiting value="false"/>
      <AllowPretendOccupants value="true"/>
      <AllowJoyriding value="true"/>
      <AllowSundayDriving value="true"/>
      <AllowBodyColorMapping value="true"/>
      <wheelScale value="0.211900"/>
      <wheelScaleRear value="0.211900"/>
      <dirtLevelMin value="0.000000"/>
      <dirtLevelMax value="0.450000"/>
      <envEffScaleMin value="0.000000"/>
      <envEffScaleMax value="1.000000"/>
      <envEffScaleMin2 value="0.000000"/>
      <envEffScaleMax2 value="1.000000"/>
      <damageMapScale value="0.40000"/>
      <damageOffsetScale value="0.40000"/>
      <diffuseTint value="0x00FFFFFF"/>
      <steerWheelMult value="1.000000"/>
      <HDTextureDist value="5.000000"/>
      <lodDistances content="float_array">
        20.000000
        40.000000
        80.000000	
        120.000000
        500.000000
        500.000000
      </lodDistances>
      <minSeatHeight value="0.844"/>
      <identicalModelSpawnDistance value="20"/>
      <maxNumOfSameColor value="1"/>
      <defaultBodyHealth value="1000.000000"/>
      <pretendOccupantsScale value="1.000000"/>
      <visibleSpawnDistScale value="1.000000"/>
      <trackerPathWidth value="2.000000"/>
      <weaponForceMult value="1.000000"/>
      <frequency value="30"/>
      <swankness>SWANKNESS_4</swankness>
      <maxNum value="10"/>
      <flags>FLAG_EXTRAS_STRONG FLAG_SPORTS FLAG_AVERAGE_CAR FLAG_INCREASE_PED_COMMENTS FLAG_HAS_INTERIOR_EXTRAS FLAG_HAS_SUPERCHARGER FLAG_INCREASE_CAMBER_WITH_SUSPENSION_MOD FLAG_ALLOW_HATS_NO_ROOF</flags>
      <type>VEHICLE_TYPE_CAR</type>
      <plateType>VPT_FRONT_AND_BACK_PLATES</plateType>
      <dashboardType>VDT_DUKES</dashboardType>
      <vehicleClass>VC_SPORT_CLASSIC</vehicleClass>
      <wheelType>VWT_MUSCLE</wheelType>
      <trailers/>
      <additionalTrailers/>
      <drivers/>
      <extraIncludes/>
      <doorsWithCollisionWhenClosed/>
      <driveableDoors/>
      <bumpersNeedToCollideWithMap value="true"/>
      <needsRopeTexture value="false"/>
      <requiredExtras/>
      <rewards/>
      <cinematicPartCamera>
        <Item>WHEEL_FRONT_RIGHT_CAMERA</Item>
        <Item>WHEEL_FRONT_LEFT_CAMERA</Item>
        <Item>WHEEL_REAR_RIGHT_CAMERA</Item>
        <Item>WHEEL_REAR_LEFT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet/>
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000"/>
      <buoyancySphereSizeScale value="1.000000"/>
      <pOverrideRagdollThreshold type="NULL"/>
      <firstPersonDrivebyData>
        <Item>STD_NEBULA_FRONT_LEFT</Item>
        <Item>STD_NEBULA_FRONT_RIGHT</Item>
      </firstPersonDrivebyData>
      <numSeatsOverride value="2"/>
    </Item>
    <Item>
      <modelName>zodiacc</modelName>
      <txdName>zodiac</txdName>
      <handlingId>ZODIAC</handlingId>
      <gameName>ZODIACC</gameName>
      <vehicleMakeName>VULCAR</vehicleMakeName>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected/>
      <ptfxAssetName>null</ptfxAssetName>
      <audioNameHash>ZODIAC</audioNameHash>
      <layout>LAYOUT_STANDARD</layout>
      <coverBoundOffsets>NEBULA_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout/>
      <cameraName>DEFAULT_FOLLOW_VEHICLE_CAMERA</cameraName>
      <aimCameraName>DEFAULT_THIRD_PERSON_VEHICLE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>VEHICLE_BONNET_CAMERA_STANDARD_LONG_EXTRA_LOW</bonnetCameraName>
      <povCameraName>REDUCED_NEAR_CLIP_POV_CAMERA</povCameraName>
      <FirstPersonDriveByIKOffset x="0.030000" y="-0.100000" z="-0.060000"/>
      <FirstPersonDriveByUnarmedIKOffset x="-0.010000" y="-0.100000" z="-0.010000"/>
      <FirstPersonProjectileDriveByIKOffset x="0.060000" y="-0.125000" z="-0.040000"/>
      <FirstPersonProjectileDriveByPassengerIKOffset x="-0.060000" y="-0.125000" z="-0.040000"/>
      <FirstPersonDriveByRightPassengerIKOffset x="-0.030000" y="-0.100000" z="-0.060000"/>
      <FirstPersonDriveByRightPassengerUnarmedIKOffset x="-0.010000" y="-0.100000" z="-0.010000"/>
      <FirstPersonMobilePhoneOffset x="0.150000" y="0.080000" z="0.525000"/>
      <FirstPersonPassengerMobilePhoneOffset x="0.201000" y="0.060000" z="0.402000"/>
      <PovCameraOffset x="0.000000" y="-0.220000" z="0.630000"/>
      <PovCameraVerticalAdjustmentForRollCage value="0.000000"/>
      <PovPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000"/>
      <vfxInfoName>VFXVEHICLEINFO_CAR_GENERIC</vfxInfoName>
      <shouldUseCinematicViewMode value="true"/>
      <shouldCameraTransitionOnClimbUpDown value="false"/>
      <shouldCameraIgnoreExiting value="false"/>
      <AllowPretendOccupants value="true"/>
      <AllowJoyriding value="true"/>
      <AllowSundayDriving value="true"/>
      <AllowBodyColorMapping value="true"/>
      <wheelScale value="0.211900"/>
      <wheelScaleRear value="0.211900"/>
      <dirtLevelMin value="0.000000"/>
      <dirtLevelMax value="0.450000"/>
      <envEffScaleMin value="0.000000"/>
      <envEffScaleMax value="1.000000"/>
      <envEffScaleMin2 value="0.000000"/>
      <envEffScaleMax2 value="1.000000"/>
      <damageMapScale value="0.40000"/>
      <damageOffsetScale value="0.40000"/>
      <diffuseTint value="0x00FFFFFF"/>
      <steerWheelMult value="1.000000"/>
      <HDTextureDist value="5.000000"/>
      <lodDistances content="float_array">
        15.000000
        30.000000
        60.000000	
        120.000000
        500.000000
        500.000000
      </lodDistances>
      <minSeatHeight value="0.844"/>
      <identicalModelSpawnDistance value="20"/>
      <maxNumOfSameColor value="1"/>
      <defaultBodyHealth value="1000.000000"/>
      <pretendOccupantsScale value="1.000000"/>
      <visibleSpawnDistScale value="1.000000"/>
      <trackerPathWidth value="2.000000"/>
      <weaponForceMult value="1.000000"/>
      <frequency value="30"/>
      <swankness>SWANKNESS_4</swankness>
      <maxNum value="10"/>
      <flags>FLAG_EXTRAS_STRONG FLAG_AVERAGE_CAR FLAG_HAS_INTERIOR_EXTRAS FLAG_CAN_HAVE_NEONS FLAG_HAS_SUPERCHARGER FLAG_INCREASE_CAMBER_WITH_SUSPENSION_MOD FLAG_NO_BOOT</flags>
      <type>VEHICLE_TYPE_CAR</type>
      <plateType>VPT_FRONT_AND_BACK_PLATES</plateType>
      <dashboardType>VDT_DUKES</dashboardType>
      <vehicleClass>VC_SPORT_CLASSIC</vehicleClass>
      <wheelType>VWT_MUSCLE</wheelType>
      <trailers/>
      <additionalTrailers/>
      <drivers/>
      <extraIncludes/>
      <doorsWithCollisionWhenClosed/>
      <driveableDoors/>
      <bumpersNeedToCollideWithMap value="false"/>
      <needsRopeTexture value="false"/>
      <requiredExtras/>
      <rewards/>
      <cinematicPartCamera>
        <Item>WHEEL_FRONT_RIGHT_CAMERA</Item>
        <Item>WHEEL_FRONT_LEFT_CAMERA</Item>
        <Item>WHEEL_REAR_RIGHT_CAMERA</Item>
        <Item>WHEEL_REAR_LEFT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet/>
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000"/>
      <buoyancySphereSizeScale value="1.000000"/>
      <pOverrideRagdollThreshold type="NULL"/>
      <firstPersonDrivebyData>
        <Item>STD_NEBULA_FRONT_LEFT</Item>
        <Item>STD_NEBULA_FRONT_RIGHT</Item>
      </firstPersonDrivebyData>
      <numSeatsOverride value="2"/>
    </Item>
    <Item>
      <modelName>zr150</modelName>
      <txdName>zr150</txdName>
      <handlingId>ZR150</handlingId>
      <gameName>ZR150</gameName>
      <vehicleMakeName>ANNIS</vehicleMakeName>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected/>
      <ptfxAssetName>null</ptfxAssetName>
      <audioNameHash>SAVESTRA</audioNameHash>
      <layout>LAYOUT_LOW</layout>
      <coverBoundOffsets>PRAIRIE_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout/>
      <cameraName>DEFAULT_FOLLOW_VEHICLE_CAMERA</cameraName>
      <aimCameraName>DEFAULT_THIRD_PERSON_VEHICLE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>VEHICLE_BONNET_CAMERA_MID_HIGH</bonnetCameraName>
      <povCameraName>DEFAULT_POV_CAMERA</povCameraName>
      <FirstPersonDriveByIKOffset x="0.000000" y="-0.055000" z="-0.030000"/>
      <FirstPersonDriveByUnarmedIKOffset x="0.000000" y="-0.100000" z="0.000000"/>
      <FirstPersonProjectileDriveByIKOffset x="0.000000" y="-0.085000" z="-0.020000"/>
      <FirstPersonProjectileDriveByPassengerIKOffset x="-0.020000" y="-0.085000" z="-0.020000"/>
      <FirstPersonProjectileDriveByRearLeftIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonProjectileDriveByRearRightIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonDriveByLeftPassengerIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonDriveByRightPassengerIKOffset x="0.000000" y="-0.055000" z="-0.030000"/>
      <FirstPersonDriveByRightRearPassengerIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonDriveByLeftPassengerUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonDriveByRightPassengerUnarmedIKOffset x="0.000000" y="-0.100000" z="0.000000"/>
      <FirstPersonMobilePhoneOffset x="0.113000" y="0.226000" z="0.535000"/>
      <FirstPersonPassengerMobilePhoneOffset x="0.136000" y="0.153000" z="0.440000"/>
      <PovCameraOffset x="0.000000" y="-0.200000" z="0.645000"/>
      <PovCameraVerticalAdjustmentForRollCage value="0.000000"/>
      <PovPassengerCameraOffset x="0.000000" y="-0.050000" z="0.005000"/>
      <PovRearPassengerCameraOffset x="0.000000" y="0.000000" z="0.005000"/>
      <vfxInfoName>VFXVEHICLEINFO_CAR_GENERIC</vfxInfoName>
      <shouldUseCinematicViewMode value="true"/>
      <shouldCameraTransitionOnClimbUpDown value="false"/>
      <shouldCameraIgnoreExiting value="false"/>
      <AllowPretendOccupants value="true"/>
      <AllowJoyriding value="true"/>
      <AllowSundayDriving value="true"/>
      <AllowBodyColorMapping value="true"/>
      <wheelScale value="0.248500"/>
      <wheelScaleRear value="0.248500"/>
      <dirtLevelMin value="0.000000"/>
      <dirtLevelMax value="0.700000"/>
      <envEffScaleMin value="0.000000"/>
      <envEffScaleMax value="1.000000"/>
      <envEffScaleMin2 value="0.000000"/>
      <envEffScaleMax2 value="1.000000"/>
      <damageMapScale value="0.600000"/>
      <damageOffsetScale value="1.000000"/>
      <diffuseTint value="0x00FFFFFF"/>
      <steerWheelMult value="1.000000"/>
      <HDTextureDist value="5.000000"/>
      <lodDistances content="float_array">
        20.000000
        40.000000
        80.000000
        120.000000
        500.000000
        500.000000
      </lodDistances>
      <minSeatHeight value="0.796"/>
      <identicalModelSpawnDistance value="80"/>
      <maxNumOfSameColor value="1"/>
      <defaultBodyHealth value="1000.000000"/>
      <pretendOccupantsScale value="1.000000"/>
      <visibleSpawnDistScale value="1.000000"/>
      <trackerPathWidth value="2.000000"/>
      <weaponForceMult value="1.000000"/>
      <frequency value="5"/>
      <swankness>SWANKNESS_4</swankness>
      <maxNum value="1"/>
      <flags>FLAG_SPORTS FLAG_AVERAGE_CAR</flags>
      <type>VEHICLE_TYPE_CAR</type>
      <plateType>VPT_BACK_PLATES</plateType>
      <dashboardType>VDT_DUKES</dashboardType>
      <vehicleClass>VC_SPORT_CLASSIC</vehicleClass>
      <wheelType>VWT_SPORT</wheelType>
      <trailers/>
      <additionalTrailers/>
      <drivers/>
      <extraIncludes/>
      <doorsWithCollisionWhenClosed/>
      <driveableDoors/>
      <bumpersNeedToCollideWithMap value="false"/>
      <needsRopeTexture value="false"/>
      <requiredExtras/>
      <rewards/>
      <cinematicPartCamera>
        <Item>WHEEL_FRONT_RIGHT_CAMERA</Item>
        <Item>WHEEL_FRONT_LEFT_CAMERA</Item>
        <Item>WHEEL_REAR_RIGHT_CAMERA</Item>
        <Item>WHEEL_REAR_LEFT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet/>
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000"/>
      <buoyancySphereSizeScale value="1.000000"/>
      <pOverrideRagdollThreshold type="NULL"/>
      <firstPersonDrivebyData>
        <Item>LOW_RAPIDGT_FRONT_LEFT</Item>
        <Item>LOW_RAPIDGT_FRONT_RIGHT</Item>
      </firstPersonDrivebyData>
    </Item>
    <Item>
      <modelName>buffalo4i</modelName>
      <txdName>buffalo4i</txdName>
      <handlingId>BUFFALO4</handlingId>
      <gameName>BUFFALO4</gameName>
      <vehicleMakeName>BRAVADO</vehicleMakeName>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected/>
      <ptfxAssetName>weap_ba_vehicle_weapons</ptfxAssetName>
      <audioNameHash>buffalo4</audioNameHash>
      <layout>LAYOUT_STANDARD_BUFFALO4</layout>
      <coverBoundOffsets>BUFFALO4_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout/>
      <cameraName>DEFAULT_FOLLOW_VEHICLE_CAMERA</cameraName>
      <aimCameraName>DEFAULT_THIRD_PERSON_VEHICLE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>VEHICLE_BONNET_CAMERA_STANDARD_LONG</bonnetCameraName>
      <povCameraName>DEFAULT_POV_CAMERA</povCameraName>
      <FirstPersonDriveByIKOffset x="0.000000" y="-0.085000" z="-0.055000"/>
      <FirstPersonDriveByUnarmedIKOffset x="0.000000" y="0.000000" z="-0.020000"/>
      <FirstPersonProjectileDriveByIKOffset x="0.050000" y="-0.120000" z="-0.080000"/>
      <FirstPersonProjectileDriveByPassengerIKOffset x="-0.060000" y="-0.100000" z="-0.05000"/>
      <FirstPersonProjectileDriveByRearLeftIKOffset x="0.000000" y="-0.070000" z="-0.080000"/>
      <FirstPersonProjectileDriveByRearRightIKOffset x="-0.050000" y="-0.070000" z="-0.060000"/>
      <FirstPersonDriveByLeftPassengerIKOffset x="0.070000" y="-0.080000" z="-0.010000"/>
      <FirstPersonDriveByRightPassengerIKOffset x="-0.060000" y="-0.090000" z="-0.040000"/>
      <FirstPersonDriveByRightRearPassengerIKOffset x="-0.090000" y="-0.060000" z="0.000000"/>
      <FirstPersonDriveByLeftPassengerUnarmedIKOffset x="0.000000" y="0.040000" z="0.000000"/>
      <FirstPersonDriveByRightPassengerUnarmedIKOffset x="0.000000" y="-0.020000" z="0.000000"/>
      <FirstPersonMobilePhoneOffset x="0.135000" y="0.240000" z="0.570000"/>
      <FirstPersonPassengerMobilePhoneOffset x="0.196000" y="0.203000" z="0.435000"/>
      <FirstPersonMobilePhoneSeatIKOffset>
        <Item>
          <Offset x="0.136000" y="0.156000" z="0.455000"/>
          <SeatIndex value="2"/>
        </Item>
        <Item>
          <Offset x="0.136000" y="0.156000" z="0.455000"/>
          <SeatIndex value="3"/>
        </Item>
      </FirstPersonMobilePhoneSeatIKOffset>
      <PovCameraOffset x="0.000000" y="-0.145000" z="0.680000"/>
      <PovCameraVerticalAdjustmentForRollCage value="-0.025000"/>
      <PovPassengerCameraOffset x="0.000000" y="-0.005000" z="-0.050000"/>
      <PovRearPassengerCameraOffset x="0.018000" y="-0.045000" z="0.000000"/>
      <vfxInfoName>VFXVEHICLEINFO_CAR_GENERIC</vfxInfoName>
      <shouldUseCinematicViewMode value="true"/>
      <shouldCameraTransitionOnClimbUpDown value="false"/>
      <shouldCameraIgnoreExiting value="false"/>
      <AllowPretendOccupants value="true"/>
      <AllowJoyriding value="true"/>
      <AllowSundayDriving value="true"/>
      <AllowBodyColorMapping value="true"/>
      <wheelScale value="0.288000"/>
      <wheelScaleRear value="0.288000"/>
      <dirtLevelMin value="0.000000"/>
      <dirtLevelMax value="0.550000"/>
      <envEffScaleMin value="0.000000"/>
      <envEffScaleMax value="0.000000"/>
      <envEffScaleMin2 value="0.000000"/>
      <envEffScaleMax2 value="0.000000"/>
      <damageMapScale value="0.900000"/>
      <damageOffsetScale value="0.700000"/>
      <diffuseTint value="0x00FFFFFF"/>
      <steerWheelMult value="1.000000"/>
      <HDTextureDist value="5.000000"/>
      <lodDistances content="float_array">
        15.000000
        30.000000
        65.000000
        130.000000
        500.000000
        500.000000
      </lodDistances>
      <minSeatHeight value="0.818"/>
      <identicalModelSpawnDistance value="20"/>
      <maxNumOfSameColor value="10"/>
      <defaultBodyHealth value="1000.000000"/>
      <pretendOccupantsScale value="1.000000"/>
      <visibleSpawnDistScale value="1.000000"/>
      <trackerPathWidth value="2.000000"/>
      <weaponForceMult value="1.000000"/>
      <frequency value="100"/>
      <swankness>SWANKNESS_3</swankness>
      <maxNum value="999"/>
      <flags>FLAG_AVERAGE_CAR FLAG_RECESSED_HEADLIGHT_CORONAS FLAG_USE_AIRCRAFT_STYLE_WEAPON_TARGETING FLAG_HAS_BULLET_RESISTANT_GLASS</flags>
      <type>VEHICLE_TYPE_CAR</type>
      <plateType>VPT_BACK_PLATES</plateType>
      <dashboardType>VDT_RACE</dashboardType>
      <vehicleClass>VC_MUSCLE</vehicleClass>
      <wheelType>VWT_SPORT</wheelType>
      <trailers/>
      <additionalTrailers/>
      <drivers/>
      <extraIncludes/>
      <doorsWithCollisionWhenClosed/>
      <driveableDoors/>
      <bumpersNeedToCollideWithMap value="false"/>
      <needsRopeTexture value="false"/>
      <requiredExtras/>
      <rewards/>
      <cinematicPartCamera>
        <Item>WHEEL_FRONT_RIGHT_CAMERA</Item>
        <Item>WHEEL_FRONT_LEFT_CAMERA</Item>
        <Item>WHEEL_REAR_RIGHT_CAMERA</Item>
        <Item>WHEEL_REAR_LEFT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet/>
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000"/>
      <buoyancySphereSizeScale value="1.000000"/>
      <pOverrideRagdollThreshold type="NULL"/>
      <firstPersonDrivebyData>
        <Item>STANDARD_BUFFALO4_FRONT_LEFT</Item>
        <Item>STANDARD_BUFFALO4_FRONT_RIGHT</Item>
        <Item>STANDARD_BUFFALO4_REAR_LEFT</Item>
        <Item>STANDARD_BUFFALO4_REAR_RIGHT</Item>
      </firstPersonDrivebyData>
    </Item>
    <Item>
      <modelName>bulletgt</modelName>
      <txdName>bulletgt</txdName>
      <handlingId>BULLET</handlingId>
      <gameName>BULLETGT</gameName>
      <vehicleMakeName>VAPID</vehicleMakeName>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected/>
      <ptfxAssetName>null</ptfxAssetName>
      <audioNameHash>GT500</audioNameHash>
      <layout>LAYOUT_LOW_RESTRICTED</layout>
      <coverBoundOffsets>BULLET_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout/>
      <cameraName>DEFAULT_FOLLOW_VEHICLE_CAMERA</cameraName>
      <aimCameraName>MID_BOX_VEHICLE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>VEHICLE_BONNET_CAMERA_NEAR_EXTRA_HIGH</bonnetCameraName>
      <povCameraName>DEFAULT_POV_CAMERA</povCameraName>
      <FirstPersonDriveByIKOffset x="0.000000" y="-0.060000" z="-0.005000"/>
      <FirstPersonDriveByUnarmedIKOffset x="0.000000" y="-0.150000" z="0.000000"/>
      <FirstPersonProjectileDriveByIKOffset x="0.025000" y="-0.100000" z="0.050000"/>
      <FirstPersonProjectileDriveByPassengerIKOffset x="-0.025000" y="-0.100000" z="0.050000"/>
      <FirstPersonDriveByLeftPassengerIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonDriveByRightPassengerIKOffset x="0.000000" y="-0.060000" z="-0.005000"/>
      <FirstPersonDriveByLeftPassengerUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonDriveByRightPassengerUnarmedIKOffset x="0.000000" y="-0.150000" z="0.000000"/>
      <FirstPersonMobilePhoneOffset x="0.125000" y="0.175000" z="0.513000"/>
      <FirstPersonPassengerMobilePhoneOffset x="0.136000" y="0.123000" z="0.425000"/>
      <PovCameraOffset x="0.000000" y="-0.200000" z="0.615000"/>
      <PovCameraVerticalAdjustmentForRollCage value="0.000000"/>
      <PovPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000"/>
      <vfxInfoName>VFXVEHICLEINFO_CAR_BULLET</vfxInfoName>
      <shouldUseCinematicViewMode value="true"/>
      <shouldCameraTransitionOnClimbUpDown value="false"/>
      <shouldCameraIgnoreExiting value="false"/>
      <AllowPretendOccupants value="true"/>
      <AllowJoyriding value="true"/>
      <AllowSundayDriving value="true"/>
      <AllowBodyColorMapping value="true"/>
      <wheelScale value="0.293500"/>
      <wheelScaleRear value="0.293500"/>
      <dirtLevelMin value="0.000000"/>
      <dirtLevelMax value="0.450000"/>
      <envEffScaleMin value="0.000000"/>
      <envEffScaleMax value="1.000000"/>
      <envEffScaleMin2 value="0.000000"/>
      <envEffScaleMax2 value="1.000000"/>
      <damageMapScale value="0.600000"/>
      <damageOffsetScale value="1.000000"/>
      <diffuseTint value="0x00FFFFFF"/>
      <steerWheelMult value="1.000000"/>
      <HDTextureDist value="5.000000"/>
      <lodDistances content="float_array">
        10.000000
        25.000000
        60.000000
        120.000000
        500.000000
        500.000000
      </lodDistances>
      <minSeatHeight value="0.813"/>
      <identicalModelSpawnDistance value="20"/>
      <maxNumOfSameColor value="1"/>
      <defaultBodyHealth value="1000.000000"/>
      <pretendOccupantsScale value="1.000000"/>
      <visibleSpawnDistScale value="1.000000"/>
      <trackerPathWidth value="2.000000"/>
      <weaponForceMult value="1.000000"/>
      <frequency value="20"/>
      <swankness>SWANKNESS_4</swankness>
      <maxNum value="5"/>
      <flags>FLAG_SPORTS FLAG_EXTRAS_ALL FLAG_EXTRAS_STRONG FLAG_RICH_CAR FLAG_NO_BROKEN_DOWN_SCENARIO FLAG_RECESSED_TAILLIGHT_CORONAS</flags>
      <type>VEHICLE_TYPE_CAR</type>
      <plateType>VPT_FRONT_AND_BACK_PLATES</plateType>
      <dashboardType>VDT_FELTZER</dashboardType>
      <vehicleClass>VC_SUPER</vehicleClass>
      <wheelType>VWT_HIEND</wheelType>
      <trailers/>
      <additionalTrailers/>
      <drivers/>
      <extraIncludes/>
      <doorsWithCollisionWhenClosed/>
      <driveableDoors/>
      <bumpersNeedToCollideWithMap value="false"/>
      <needsRopeTexture value="false"/>
      <requiredExtras/>
      <rewards/>
      <cinematicPartCamera>
        <Item>WHEEL_FRONT_RIGHT_CAMERA</Item>
        <Item>WHEEL_FRONT_LEFT_CAMERA</Item>
        <Item>WHEEL_REAR_RIGHT_CAMERA</Item>
        <Item>WHEEL_REAR_LEFT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet/>
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000"/>
      <buoyancySphereSizeScale value="1.000000"/>
      <pOverrideRagdollThreshold type="NULL"/>
      <firstPersonDrivebyData>
        <Item>LOW_BULLET_FRONT_LEFT</Item>
        <Item>LOW_BULLET_FRONT_RIGHT</Item>
      </firstPersonDrivebyData>
    </Item>
    <Item>
      <modelName>ccadeesv</modelName>
      <txdName>ccadeesv</txdName>
      <handlingId>CCADEESV</handlingId>
      <gameName>CCADEESV</gameName>
      <vehicleMakeName>ALBANY</vehicleMakeName>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected/>
      <ptfxAssetName>null</ptfxAssetName>
      <audioNameHash>virgo</audioNameHash>
      <layout>LAYOUT_RANGER</layout>
      <coverBoundOffsets>CCADEESV_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout/>
      <cameraName>DEFAULT_FOLLOW_VEHICLE_CAMERA</cameraName>
      <aimCameraName>BOX_VEHICLE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>VEHICLE_BONNET_CAMERA_STANDARD_LONG_REBLA</bonnetCameraName>
      <povCameraName>REDUCED_NEAR_CLIP_POV_CAMERA</povCameraName>
      <FirstPersonDriveByIKOffset x="0.020000" y="-0.090000" z="-0.070000"/>
      <FirstPersonDriveByUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonProjectileDriveByIKOffset x="0.000000" y="-0.088000" z="-0.027000"/>
      <FirstPersonProjectileDriveByPassengerIKOffset x="-0.113000" y="-0.005000" z="-0.078000"/>
      <FirstPersonDriveByLeftPassengerIKOffset x="0.060000" y="0.010000" z="-0.020000"/>
      <FirstPersonDriveByRightPassengerIKOffset x="-0.065000" y="-0.108000" z="-0.060000"/>
      <FirstPersonDriveByLeftPassengerUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonDriveByRightPassengerUnarmedIKOffset x="-0.080000" y="-0.010000" z="-0.030000"/>
      <FirstPersonDriveByRightRearPassengerIKOffset x="-0.060000" y="-0.020000" z="-0.040000"/>
      <FirstPersonDriveByRightRearPassengerIKOffset x="0.000000" y="-0.088000" z="-0.047000"/>
      <FirstPersonProjectileDriveByRearLeftIKOffset x="-0.040000" y="-0.068000" z="-0.047000"/>
      <FirstPersonProjectileDriveByRearRightIKOffset x="-0.040000" y="-0.068000" z="-0.057000"/>
      <FirstPersonMobilePhoneOffset x="0.150000" y="0.208000" z="0.570000"/>
      <FirstPersonPassengerMobilePhoneOffset x="0.206000" y="0.223000" z="0.425000"/>
      <PovCameraOffset x="0.000000" y="-0.175000" z="0.675000"/>
      <PovCameraVerticalAdjustmentForRollCage value="-0.010000"/>
      <PovPassengerCameraOffset x="0.010000" y="0.045000" z="0.005000"/>
      <PovRearPassengerCameraOffset x="0.000000" y="0.150000" z="0.025000"/>
      <vfxInfoName>VFXVEHICLEINFO_CAR_GENERIC</vfxInfoName>
      <shouldUseCinematicViewMode value="true"/>
      <shouldCameraTransitionOnClimbUpDown value="false"/>
      <shouldCameraIgnoreExiting value="false"/>
      <AllowPretendOccupants value="true"/>
      <AllowJoyriding value="true"/>
      <AllowSundayDriving value="true"/>
      <AllowBodyColorMapping value="true"/>
      <wheelScale value="0.317000"/>
      <wheelScaleRear value="0.317000"/>
      <dirtLevelMin value="0.000000"/>
      <dirtLevelMax value="0.850000"/>
      <envEffScaleMin value="0.000000"/>
      <envEffScaleMax value="0.000000"/>
      <envEffScaleMin2 value="0.000000"/>
      <envEffScaleMax2 value="0.000000"/>
      <damageMapScale value="0.600000"/>
      <damageOffsetScale value="1.000000"/>
      <diffuseTint value="0xAA0A0A0A"/>
      <steerWheelMult value="1.000000"/>
      <HDTextureDist value="5.000000"/>
      <lodDistances content="float_array">
        15.000000	
        35.000000	
        80.000000	
        160.000000	
        500.000000	
        500.000000
      </lodDistances>
      <minSeatHeight value="0.966"/>
      <identicalModelSpawnDistance value="20"/>
      <maxNumOfSameColor value="10"/>
      <defaultBodyHealth value="1000.000000"/>
      <pretendOccupantsScale value="1.000000"/>
      <visibleSpawnDistScale value="1.000000"/>
      <trackerPathWidth value="2.000000"/>
      <weaponForceMult value="1.000000"/>
      <frequency value="100"/>
      <swankness>SWANKNESS_1</swankness>
      <maxNum value="999"/>
      <flags>FLAG_AVERAGE_CAR FLAG_IS_OFFROAD_VEHICLE FLAG_IS_BULKY FLAG_RECESSED_HEADLIGHT_CORONAS FLAG_INCREASE_CAMBER_WITH_SUSPENSION_MOD</flags>
      <type>VEHICLE_TYPE_CAR</type>
      <plateType>VPT_FRONT_AND_BACK_PLATES</plateType>
      <dashboardType>VDT_RACE</dashboardType>
      <vehicleClass>VC_SUV</vehicleClass>
      <wheelType>VWT_SUV</wheelType>
      <trailers/>
      <additionalTrailers/>
      <drivers/>
      <extraIncludes/>
      <doorsWithCollisionWhenClosed/>
      <driveableDoors/>
      <bumpersNeedToCollideWithMap value="false"/>
      <needsRopeTexture value="false"/>
      <requiredExtras/>
      <rewards/>
      <cinematicPartCamera>
        <Item>WHEEL_FRONT_RIGHT_CAMERA</Item>
        <Item>WHEEL_FRONT_LEFT_CAMERA</Item>
        <Item>WHEEL_REAR_RIGHT_CAMERA</Item>
        <Item>WHEEL_REAR_LEFT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet/>
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000"/>
      <buoyancySphereSizeScale value="1.000000"/>
      <pOverrideRagdollThreshold type="NULL"/>
      <firstPersonDrivebyData>
        <Item>CCADEESV_FRONT_LEFT</Item>
        <Item>CCADEESV_FRONT_RIGHT</Item>
        <Item>CCADEESV_REAR_LEFT</Item>
        <Item>CCADEESV_REAR_RIGHT</Item>
      </firstPersonDrivebyData>
    </Item>
    <Item>
      <modelName>deluxo2</modelName>
      <txdName>deluxo2</txdName>
      <handlingId>DELUXO2</handlingId>
      <gameName>DELUXO</gameName>
      <vehicleMakeName>IMPONTE</vehicleMakeName>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected/>
      <ptfxAssetName>null</ptfxAssetName>
      <audioNameHash>DELUXO</audioNameHash>
      <layout>LAYOUT_LOW_DELUXO</layout>
      <coverBoundOffsets>DELUXO_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout/>
      <cameraName>FOLLOW_DELUXO_CAMERA</cameraName>
      <aimCameraName>DEFAULT_THIRD_PERSON_NO_LOCKON_VEHICLE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>VEHICLE_BONNET_CAMERA_STANDARD_HIGH</bonnetCameraName>
      <povCameraName>DEFAULT_POV_CAMERA</povCameraName>
      <FirstPersonDriveByIKOffset x="0.000000" y="-0.060000" z="0.000000"/>
      <FirstPersonDriveByUnarmedIKOffset x="0.000000" y="-0.100000" z="0.000000"/>
      <FirstPersonProjectileDriveByIKOffset x="0.000000" y="-0.100000" z="-0.015000"/>
      <FirstPersonProjectileDriveByPassengerIKOffset x="0.000000" y="-0.100000" z="-0.015000"/>
      <FirstPersonProjectileDriveByRearLeftIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonProjectileDriveByRearRightIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonDriveByLeftPassengerIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonDriveByRightPassengerIKOffset x="0.000000" y="-0.060000" z="0.000000"/>
      <FirstPersonDriveByRightRearPassengerIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonDriveByLeftPassengerUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonDriveByRightPassengerUnarmedIKOffset x="0.000000" y="-0.100000" z="0.000000"/>
      <FirstPersonMobilePhoneOffset x="0.151000" y="0.130000" z="0.536000"/>
      <FirstPersonPassengerMobilePhoneOffset x="0.197000" y="0.079000" z="0.441000"/>
      <PovCameraOffset x="0.000000" y="-0.330000" z="0.670000"/>
      <PovCameraVerticalAdjustmentForRollCage value="0.000000"/>
      <PovPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000"/>
      <PovRearPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000"/>
      <vfxInfoName>VFXVEHICLEINFO_CAR_GENERIC</vfxInfoName>
      <shouldUseCinematicViewMode value="true"/>
      <shouldCameraTransitionOnClimbUpDown value="false"/>
      <shouldCameraIgnoreExiting value="false"/>
      <AllowPretendOccupants value="true"/>
      <AllowJoyriding value="true"/>
      <AllowSundayDriving value="true"/>
      <AllowBodyColorMapping value="true"/>
      <wheelScale value="0.197500"/>
      <wheelScaleRear value="0.197500"/>
      <dirtLevelMin value="0.000000"/>
      <dirtLevelMax value="0.700000"/>
      <envEffScaleMin value="0.000000"/>
      <envEffScaleMax value="1.000000"/>
      <envEffScaleMin2 value="0.000000"/>
      <envEffScaleMax2 value="1.000000"/>
      <damageMapScale value="0.300000"/>
      <damageOffsetScale value="0.300000"/>
      <diffuseTint value="0x00FFFFFF"/>
      <steerWheelMult value="1.000000"/>
      <HDTextureDist value="5.000000"/>
      <lodDistances content="float_array">
        20.000000
        40.000000
        80.000000
        120.000000
        500.000000
        500.000000
      </lodDistances>
      <minSeatHeight value="0.852"/>
      <identicalModelSpawnDistance value="20"/>
      <maxNumOfSameColor value="1"/>
      <defaultBodyHealth value="1000.000000"/>
      <pretendOccupantsScale value="1.000000"/>
      <visibleSpawnDistScale value="1.000000"/>
      <trackerPathWidth value="2.000000"/>
      <weaponForceMult value="1.000000"/>
      <frequency value="5"/>
      <swankness>SWANKNESS_2</swankness>
      <maxNum value="10"/>
      <flags>FLAG_EXTRAS_STRONG FLAG_AVERAGE_CAR FLAG_POOR_CAR FLAG_RECESSED_HEADLIGHT_CORONAS FLAG_RECESSED_TAILLIGHT_CORONAS FLAG_HAS_INTERIOR_EXTRAS FLAG_HAS_GULL_WING_DOORS FLAG_DONT_TIMESLICE_WHEELS</flags>
      <type>VEHICLE_TYPE_CAR</type>
      <plateType>VPT_BACK_PLATES</plateType>
      <dashboardType>VDT_DUKES</dashboardType>
      <vehicleClass>VC_SPORT_CLASSIC</vehicleClass>
      <wheelType>VWT_MUSCLE</wheelType>
      <trailers/>
      <additionalTrailers/>
      <drivers/>
      <extraIncludes/>
      <doorsWithCollisionWhenClosed/>
      <driveableDoors/>
      <bumpersNeedToCollideWithMap value="false"/>
      <needsRopeTexture value="false"/>
      <requiredExtras/>
      <rewards/>
      <cinematicPartCamera>
        <Item>WHEEL_FRONT_RIGHT_CAMERA</Item>
        <Item>WHEEL_FRONT_LEFT_CAMERA</Item>
        <Item>WHEEL_REAR_RIGHT_CAMERA</Item>
        <Item>WHEEL_REAR_LEFT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet/>
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000"/>
      <buoyancySphereSizeScale value="1.000000"/>
      <pOverrideRagdollThreshold type="NULL"/>
      <firstPersonDrivebyData>
        <Item>LOW_DELUXO_FRONT_LEFT</Item>
        <Item>LOW_DELUXO_FRONT_RIGHT</Item>
      </firstPersonDrivebyData>
    </Item>
  </InitDatas>
  <txdRelationships>
    <Item>
      <parent>vehicles_muscle_n_w_interior</parent>
      <child>argento</child>
    </Item>
    <Item>
      <parent>vehicles_muscle_n_w_interior</parent>
      <child>asteropers</child>
    </Item>
    <Item>
      <parent>vehicles_dom_interior</parent>
      <child>domc</child>
    </Item>
    <Item>
      <parent>vehicles_race_generic</parent>
      <child>vehicles_doubled_interior</child>
    </Item>
    <Item>
      <parent>vehicles_doubled_interior</parent>
      <child>doubled</child>
    </Item>
    <Item>
      <parent>vehshare</parent>
      <child>vehicles_dubsta22_interior</child>
    </Item>
    <Item>
      <parent>vehicles_dubsta22_interior</parent>
      <child>dubsta22</child>
    </Item>
    <Item>
      <parent>vehicles_feroci_interior</parent>
      <child>ferocid</child>
    </Item>
    <Item>
      <parent>vehicles_muscle_n_w_interior</parent>
      <child>gauntletac</child>
    </Item>
    <Item>
      <parent>vehicles_muscle_n_w_interior</parent>
      <child>gauntletv6</child>
    </Item>
    <Item>
      <parent>vehicles_race_generic</parent>
      <child>gstyosemite1</child>
    </Item>
    <Item>
      <parent>vehicles_race_generic</parent>
      <child>vehicles_race_interior</child>
    </Item>
    <Item>
      <parent>vehicles_race_interior</parent>
      <child>vehicles_fmj_interior</child>
    </Item>
    <Item>
      <parent>vehicles_poltax_interior</parent>
      <child>howitzer</child>
    </Item>
    <Item>
      <parent>vehicles_poltax_interior</parent>
      <child>howitzer2</child>
    </Item>
    <Item>
      <parent>vehicles_race_interior</parent>
      <child>vehicles_sultanrs_interior</child>
    </Item>
    <Item>
      <parent>vehicles_race_generic</parent>
      <child>baller7r</child>
    </Item>
    <Item>
      <parent>michelli</parent>
      <child>kriegerc</child>
    </Item>
    <Item>
      <parent>vehicles_cav_interior</parent>
      <child>mesar</child>
    </Item>
    <Item>
      <parent>vehicles_futo2_interior</parent>
      <child>nebulaw</child>
    </Item>
    <Item>
      <parent>vehshare</parent>
      <child>vehicles_gendials</child>
    </Item>
    <Item>
      <parent>vehicles_gendials</parent>
      <child>granger2</child>
    </Item>
    <Item>
      <parent>vehshare</parent>
      <child>vehicles_oracxsle_race_interior</child>
    </Item>
    <Item>
      <parent>vehicles_muscle_o_w_interior</parent>
      <child>paradox</child>
    </Item>
    <Item>
      <parent>vehicles_muscle_o_w_interior</parent>
      <child>paradox2</child>
    </Item>
    <Item>
      <parent>vehicles_sultanrs_interior</parent>
      <child>paragono</child>
    </Item>
    <Item>
      <parent>vehshare</parent>
      <child>vehicles_race_interior</child>
    </Item>
    <Item>
      <parent>vehicles_race_interior</parent>
      <child>vehicles_primoard_interior</child>
    </Item>
    <Item>
      <parent>vehicles_primoard_interior</parent>
      <child>primoard</child>
    </Item>
    <Item>
      <parent>vehicles_racecar</parent>
      <child>proff</child>
    </Item>
    <Item>
      <parent>vehicles_futo2_interior</parent>
      <child>rh4</child>
    </Item>
    <Item>
      <parent>vehicles_banshee_w_interior</parent>
      <child>bansheepo</child>
    </Item>
    <Item>
      <parent>vehicles_banshee_interior</parent>
      <child>bansheepo</child>
    </Item>
    <Item>
      <parent>vehicles_lowrider</parent>
      <child>vehicles_schaf_interior</child>
    </Item>
    <Item>
      <parent>vehicles_schaf_interior</parent>
      <child>romtuner</child>
    </Item>
    <Item>
      <parent>vehicles_banshee_interior</parent>
      <child>sandstorm</child>
    </Item>
    <Item>
      <parent>vehicles_banshee_interior</parent>
      <child>sandstormxl</child>
    </Item>
    <Item>
      <parent>vehicles_fmj_interior</parent>
      <child>savanna</child>
    </Item>
    <Item>
      <parent>vehicles_sultan_interior</parent>
      <child>seraph3</child>
    </Item>
    <Item>
      <parent>vehicles_race_generic</parent>
      <child>vehicles_racecar</child>
    </Item>
    <Item>
      <parent>vehicles_racecar</parent>
      <child>vehicles_comet_interior</child>
    </Item>
    <Item>
      <parent>vehicles_comet_interior</parent>
      <child>vehicles_sg3_interior</child>
    </Item>
    <Item>
      <parent>vehicles_sg3_interior</parent>
      <child>sentinelsg3</child>
    </Item>
    <Item>
      <parent>vehicles_sg3_interior</parent>
      <child>sentinelsg32</child>
    </Item>
    <Item>
      <parent>vehicles_sg3_interior</parent>
      <child>sentinelsg3a</child>
    </Item>
    <Item>
      <parent>vehicles_sg3_interior</parent>
      <child>sentinelsg3a2</child>
    </Item>
    <Item>
      <parent>vehicles_sg3_interior</parent>
      <child>sentinelsg3b</child>
    </Item>
    <Item>
      <parent>vehicles_sg3_interior</parent>
      <child>sentinelsg3b2</child>
    </Item>
    <Item>
      <parent>vehicles_sg3_interior</parent>
      <child>sentinelsg3c</child>
    </Item>
    <Item>
      <parent>vehicles_sg3_interior</parent>
      <child>sentinelsg3c2</child>
    </Item>
    <Item>
      <parent>vehicles_sg3_interior</parent>
      <child>sentinelsg3d</child>
    </Item>
    <Item>
      <parent>vehicles_sg3_interior</parent>
      <child>sentinelsg3d2</child>
    </Item>
    <Item>
      <parent>vehicles_race_generic</parent>
      <child>vehicles_sultan_interior</child>
    </Item>
    <Item>
      <parent>vehicles_sultan_interior</parent>
      <child>vehicles_feltzer_interior</child>
    </Item>
    <Item>
      <parent>vehicles_feltzer_interior</parent>
      <child>spritzer</child>
    </Item>
    <Item>
      <parent>vehicles_feltzer_interior</parent>
      <child>spritzerdtm</child>
    </Item>
    <Item>
      <parent>vehshare</parent>
      <child>vehicles_superd3_interior</child>
    </Item>
    <Item>
      <parent>vehicles_superd3_interior</parent>
      <child>superd3</child>
    </Item>
    <Item>
      <parent>vehshare</parent>
      <child>torrence_global_mods</child>
    </Item>
    <Item>
      <parent>torrence_global_mods</parent>
      <child>vehicles_torrence_interior</child>
    </Item>
    <Item>
      <parent>vehicles_torrence_interior</parent>
      <child>torrence</child>
    </Item>
    <Item>
      <parent>vehicles_race_interior</parent>
      <child>vehicles_race_generic</child>
    </Item>
    <Item>
      <parent>vehicles_race_generic</parent>
      <child>vehicles_velox_w_interior</child>
    </Item>
    <Item>
      <parent>vehicles_velox_w_interior</parent>
      <child>velox</child>
    </Item>
    <Item>
      <parent>vehicles_velox_w_interior</parent>
      <child>velox2</child>
    </Item>
    <Item>
      <parent>vehicles_race_generic</parent>
      <child>vehicles_elegy_race</child>
    </Item>
    <Item>
      <parent>vehicles_elegy_race</parent>
      <child>vincent2</child>
    </Item>
    <Item>
      <parent>vehicles_race_generic</parent>
      <child>vehicles_elegy_race</child>
    </Item>
    <Item>
      <parent>vehicles_elegy_race</parent>
      <child>vincent3</child>
    </Item>
    <Item>
      <parent>vehicles_feroci_interior</parent>
      <child>vorstand</child>
    </Item>
    <Item>
      <parent>vehicles_sultanrs_interior</parent>
      <child>hotwee</child>
    </Item>
    <Item>
      <parent>zion3</parent>
      <child>zodiac</child>
    </Item>
    <Item>
      <parent>zodiac</parent>
      <child>zodiacr</child>
    </Item>
    <Item>
      <parent>zion3</parent>
      <child>zodiacc</child>
    </Item>
    <Item>
      <parent>vehicles_race_interior</parent>
      <child>vehicles_race_generic</child>
    </Item>
    <Item>
      <parent>vehicles_dom_interior</parent>
      <child>zr150</child>
    </Item>
    <Item>
      <parent>vehicles_muscle_n_w_interior</parent>
      <child>buffalo4i</child>
    </Item>
    <Item>
      <parent>vehicles_feltzer_interior</parent>
      <child>bulletgt</child>
    </Item>
    <Item>
      <parent>vehicles_gendials</parent>
      <child>ccadeesv</child>
    </Item>
    <Item>
      <parent>vehicles_dom_interior</parent>
      <child>deluxo2</child>
    </Item>
  </txdRelationships>
</CVehicleModelInfo__InitDataList>

