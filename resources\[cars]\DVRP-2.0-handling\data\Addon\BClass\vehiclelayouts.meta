<?xml version="1.0" encoding="UTF-8"?>
<CVehicleMetadataMgr>
  <AnimRateSets/>
  <ClipSetMaps>
    <Item type="CClipSetMap">
      <Name>ENTRY_CLIPSET_MAP_LOW_LE7B_FRONT_LEFT</Name>
      <Maps>
        <Item>
          <ClipSetId>clipset@veh@low@le7b@front_ds@enter_exit</ClipSetId>
          <VarClipSetId>EntryVarClipSet</VarClipSetId>
          <ConditionFlags/>
          <InformationFlags/>
        </Item>
      </Maps>
    </Item>
    <Item type="CClipSetMap">
      <Name>EXIT_CLIPSET_MAP_LOW_LE7B_FRONT_LEFT</Name>
      <Maps>
        <Item>
          <ClipSetId>clipset@veh@low@le7b@front_ds@enter_exit</ClipSetId>
          <VarClipSetId>ExitVarClipSet</VarClipSetId>
          <ConditionFlags/>
          <InformationFlags/>
        </Item>
      </Maps>
    </Item>
    <Item type="CClipSetMap">
      <Name>ENTRY_CLIPSET_MAP_LOW_LE7B_FRONT_RIGHT</Name>
      <Maps>
        <Item>
          <ClipSetId>clipset@veh@low@le7b@front_ps@enter_exit</ClipSetId>
          <VarClipSetId>EntryVarClipSet</VarClipSetId>
          <ConditionFlags/>
          <InformationFlags/>
        </Item>
      </Maps>
    </Item>
    <Item type="CClipSetMap">
      <Name>EXIT_CLIPSET_MAP_LOW_LE7B_FRONT_RIGHT</Name>
      <Maps>
        <Item>
          <ClipSetId>clipset@veh@low@le7b@front_ps@enter_exit</ClipSetId>
          <VarClipSetId>ExitVarClipSet</VarClipSetId>
          <ConditionFlags/>
          <InformationFlags/>
        </Item>
      </Maps>
    </Item>
    <Item type="CClipSetMap">
      <Name>INSIDE_CLIPSET_MAP_STD_BRIOSO_FRONT_LEFT</Name>
      <Maps>
        <Item>
          <ClipSetId>anim@veh@std@panto@ds@base</ClipSetId>
          <VarClipSetId>InsideVarClipSet</VarClipSetId>
          <ConditionFlags/>
          <InformationFlags/>
        </Item>
      </Maps>
    </Item>
    <Item type="CClipSetMap">
      <Name>ENTRY_CLIPSET_MAP_HELI_SWIFT2_FRONT_RIGHT</Name>
      <Maps>
        <Item>
          <ClipSetId>anim@veh@helicopter@swift2@ps@enter_exit</ClipSetId>
          <VarClipSetId>EntryVarClipSet</VarClipSetId>
          <ConditionFlags/>
          <InformationFlags/>
        </Item>
      </Maps>
    </Item>
    <Item type="CClipSetMap">
      <Name>EXIT_CLIPSET_MAP_HELI_SWIFT2_FRONT_RIGHT</Name>
      <Maps>
        <Item>
          <ClipSetId>anim@veh@helicopter@swift2@ps@enter_exit</ClipSetId>
          <VarClipSetId>ExitVarClipSet</VarClipSetId>
          <ConditionFlags/>
          <InformationFlags/>
        </Item>
      </Maps>
    </Item>
    <Item type="CClipSetMap">
      <Name>ENTRY_CLIPSET_MAP_HELI_SWIFT2_REAR_LEFT</Name>
      <Maps>
        <Item>
          <ClipSetId>anim@veh@helicopter@swift2@rds@enter_exit</ClipSetId>
          <VarClipSetId>EntryVarClipSet</VarClipSetId>
          <ConditionFlags/>
          <InformationFlags/>
        </Item>
      </Maps>
    </Item>
    <Item type="CClipSetMap">
      <Name>EXIT_CLIPSET_MAP_HELI_SWIFT2_REAR_LEFT</Name>
      <Maps>
        <Item>
          <ClipSetId>anim@veh@helicopter@swift2@rds@enter_exit</ClipSetId>
          <VarClipSetId>ExitVarClipSet</VarClipSetId>
          <ConditionFlags/>
          <InformationFlags/>
        </Item>
      </Maps>
    </Item>
    <Item type="CClipSetMap">
      <Name>INSIDE_CLIPSET_MAP_HELI_SWIFT2_REAR_LEFT</Name>
      <Maps>
        <Item>
          <ClipSetId>anim@veh@helicopter@swift2@rds@base</ClipSetId>
          <VarClipSetId>InsideVarClipSet</VarClipSetId>
          <ConditionFlags/>
          <InformationFlags/>
        </Item>
      </Maps>
    </Item>
    <Item type="CClipSetMap">
      <Name>ENTRY_CLIPSET_MAP_HELI_SWIFT2_REAR_RIGHT</Name>
      <Maps>
        <Item>
          <ClipSetId>anim@veh@helicopter@swift2@rps@enter_exit</ClipSetId>
          <VarClipSetId>EntryVarClipSet</VarClipSetId>
          <ConditionFlags/>
          <InformationFlags/>
        </Item>
      </Maps>
    </Item>
    <Item type="CClipSetMap">
      <Name>EXIT_CLIPSET_MAP_HELI_SWIFT2_REAR_RIGHT</Name>
      <Maps>
        <Item>
          <ClipSetId>anim@veh@helicopter@swift2@rps@enter_exit</ClipSetId>
          <VarClipSetId>ExitVarClipSet</VarClipSetId>
          <ConditionFlags/>
          <InformationFlags/>
        </Item>
      </Maps>
    </Item>
    <Item type="CClipSetMap">
      <Name>INSIDE_CLIPSET_MAP_HELI_SWIFT2_REAR_RIGHT</Name>
      <Maps>
        <Item>
          <ClipSetId>anim@veh@helicopter@swift2@rps@base</ClipSetId>
          <VarClipSetId>InsideVarClipSet</VarClipSetId>
          <ConditionFlags/>
          <InformationFlags/>
        </Item>
      </Maps>
    </Item>
    <Item type="CClipSetMap">
      <Name>ENTRY_CLIPSET_MAP_LOW_GULLWING_FRONT_LEFT</Name>
      <Maps>
        <Item>
          <ClipSetId>anim@veh@low@gullwing@front_ds@enter_exit</ClipSetId>
          <VarClipSetId>EntryVarClipSet</VarClipSetId>
          <ConditionFlags/>
          <InformationFlags/>
        </Item>
        <Item>
          <ClipSetId>clipset@veh@break_in@0h@p_m_zero@</ClipSetId>
          <VarClipSetId>EntryVarClipSet</VarClipSetId>
          <ConditionFlags>CF_BreakInAnims</ConditionFlags>
          <InformationFlags/>
        </Item>
        <Item>
          <ClipSetId>clipset@veh@break_in@0h@p_m_one@</ClipSetId>
          <VarClipSetId>EntryVarClipSet</VarClipSetId>
          <ConditionFlags>CF_BreakInAnims</ConditionFlags>
          <InformationFlags/>
        </Item>
        <Item>
          <ClipSetId>clipset@veh@break_in@0h@p_m_two@</ClipSetId>
          <VarClipSetId>EntryVarClipSet</VarClipSetId>
          <ConditionFlags>CF_BreakInAnims</ConditionFlags>
          <InformationFlags/>
        </Item>
        <Item>
          <ClipSetId>clipset@veh@jacking@0h@p_m_zero@</ClipSetId>
          <VarClipSetId>EntryVarClipSet</VarClipSetId>
          <ConditionFlags>CF_JackingAnims</ConditionFlags>
          <InformationFlags/>
        </Item>
        <Item>
          <ClipSetId>clipset@veh@jacking@0h@p_m_one@</ClipSetId>
          <VarClipSetId>EntryVarClipSet</VarClipSetId>
          <ConditionFlags>CF_JackingAnims</ConditionFlags>
          <InformationFlags/>
        </Item>
        <Item>
          <ClipSetId>clipset@veh@jacking@0h@p_m_two@</ClipSetId>
          <VarClipSetId>EntryVarClipSet</VarClipSetId>
          <ConditionFlags>CF_JackingAnims</ConditionFlags>
          <InformationFlags/>
        </Item>
      </Maps>
    </Item>
    <Item type="CClipSetMap">
      <Name>EXIT_CLIPSET_MAP_LOW_GULLWING_FRONT_LEFT</Name>
      <Maps>
        <Item>
          <ClipSetId>anim@veh@low@gullwing@front_ds@enter_exit</ClipSetId>
          <VarClipSetId>ExitVarClipSet</VarClipSetId>
          <ConditionFlags/>
          <InformationFlags/>
        </Item>
      </Maps>
    </Item>
    <Item type="CClipSetMap">
      <Name>ENTRY_CLIPSET_MAP_LOW_GULLWING_FRONT_RIGHT</Name>
      <Maps>
        <Item>
          <ClipSetId>anim@veh@low@gullwing@front_ps@enter_exit</ClipSetId>
          <VarClipSetId>EntryVarClipSet</VarClipSetId>
          <ConditionFlags/>
          <InformationFlags/>
        </Item>
        <Item>
          <ClipSetId>clipset@veh@break_in@0h@p_m_zero@</ClipSetId>
          <VarClipSetId>EntryVarClipSet</VarClipSetId>
          <ConditionFlags>CF_BreakInAnims</ConditionFlags>
          <InformationFlags/>
        </Item>
        <Item>
          <ClipSetId>clipset@veh@break_in@0h@p_m_one@</ClipSetId>
          <VarClipSetId>EntryVarClipSet</VarClipSetId>
          <ConditionFlags>CF_BreakInAnims</ConditionFlags>
          <InformationFlags/>
        </Item>
        <Item>
          <ClipSetId>clipset@veh@break_in@0h@p_m_two@</ClipSetId>
          <VarClipSetId>EntryVarClipSet</VarClipSetId>
          <ConditionFlags>CF_BreakInAnims</ConditionFlags>
          <InformationFlags/>
        </Item>
        <Item>
          <ClipSetId>clipset@veh@jacking@0h@p_m_zero@</ClipSetId>
          <VarClipSetId>EntryVarClipSet</VarClipSetId>
          <ConditionFlags>CF_JackingAnims</ConditionFlags>
          <InformationFlags/>
        </Item>
        <Item>
          <ClipSetId>clipset@veh@jacking@0h@p_m_one@</ClipSetId>
          <VarClipSetId>EntryVarClipSet</VarClipSetId>
          <ConditionFlags>CF_JackingAnims</ConditionFlags>
          <InformationFlags/>
        </Item>
        <Item>
          <ClipSetId>clipset@veh@jacking@0h@p_m_two@</ClipSetId>
          <VarClipSetId>EntryVarClipSet</VarClipSetId>
          <ConditionFlags>CF_JackingAnims</ConditionFlags>
          <InformationFlags/>
        </Item>
      </Maps>
    </Item>
    <Item type="CClipSetMap">
      <Name>EXIT_CLIPSET_MAP_LOW_GULLWING_FRONT_RIGHT</Name>
      <Maps>
        <Item>
          <ClipSetId>anim@veh@low@gullwing@front_ps@enter_exit</ClipSetId>
          <VarClipSetId>ExitVarClipSet</VarClipSetId>
          <ConditionFlags/>
          <InformationFlags/>
        </Item>
      </Maps>
    </Item>
    <Item type="CClipSetMap">
      <Name>ENTRY_CLIPSET_MAP_PLANE_LUXOR2_REAR_LEFT</Name>
      <Maps>
        <Item>
          <ClipSetId>anim@veh@plane@luxor2@rear@rds@enter_exit</ClipSetId>
          <VarClipSetId>EntryVarClipSet</VarClipSetId>
          <ConditionFlags/>
          <InformationFlags/>
        </Item>
      </Maps>
    </Item>
    <Item type="CClipSetMap">
      <Name>EXIT_CLIPSET_MAP_PLANE_LUXOR2_REAR_LEFT</Name>
      <Maps>
        <Item>
          <ClipSetId>anim@veh@plane@luxor2@rear@rds@enter_exit</ClipSetId>
          <VarClipSetId>ExitVarClipSet</VarClipSetId>
          <ConditionFlags/>
          <InformationFlags/>
        </Item>
      </Maps>
    </Item>
    <Item type="CClipSetMap">
      <Name>ENTRY_CLIPSET_MAP_PLANE_LUXOR2_REAR_RIGHT</Name>
      <Maps>
        <Item>
          <ClipSetId>anim@veh@plane@luxor2@rear@rps@enter_exit</ClipSetId>
          <VarClipSetId>EntryVarClipSet</VarClipSetId>
          <ConditionFlags/>
          <InformationFlags/>
        </Item>
      </Maps>
    </Item>
    <Item type="CClipSetMap">
      <Name>EXIT_CLIPSET_MAP_PLANE_LUXOR2_REAR_RIGHT</Name>
      <Maps>
        <Item>
          <ClipSetId>anim@veh@plane@luxor2@rear@rps@enter_exit</ClipSetId>
          <VarClipSetId>ExitVarClipSet</VarClipSetId>
          <ConditionFlags/>
          <InformationFlags/>
        </Item>
      </Maps>
    </Item>
    <Item type="CClipSetMap">
      <Name>ENTRY_CLIPSET_MAP_PLANE_LUXOR2_EXTRA_LEFT_1</Name>
      <Maps>
        <Item>
          <ClipSetId>anim@veh@plane@luxor2@rear@rds2@enter_exit</ClipSetId>
          <VarClipSetId>EntryVarClipSet</VarClipSetId>
          <ConditionFlags/>
          <InformationFlags/>
        </Item>
      </Maps>
    </Item>
    <Item type="CClipSetMap">
      <Name>EXIT_CLIPSET_MAP_PLANE_LUXOR2_EXTRA_LEFT_1</Name>
      <Maps>
        <Item>
          <ClipSetId>anim@veh@plane@luxor2@rear@rds2@enter_exit</ClipSetId>
          <VarClipSetId>ExitVarClipSet</VarClipSetId>
          <ConditionFlags/>
          <InformationFlags/>
        </Item>
      </Maps>
    </Item>
    <Item type="CClipSetMap">
      <Name>ENTRY_CLIPSET_MAP_PLANE_LUXOR2_EXTRA_RIGHT_1</Name>
      <Maps>
        <Item>
          <ClipSetId>anim@veh@plane@luxor2@rear@rps2@enter_exit</ClipSetId>
          <VarClipSetId>EntryVarClipSet</VarClipSetId>
          <ConditionFlags/>
          <InformationFlags/>
        </Item>
      </Maps>
    </Item>
    <Item type="CClipSetMap">
      <Name>EXIT_CLIPSET_MAP_PLANE_LUXOR2_EXTRA_RIGHT_1</Name>
      <Maps>
        <Item>
          <ClipSetId>anim@veh@plane@luxor2@rear@rps2@enter_exit</ClipSetId>
          <VarClipSetId>ExitVarClipSet</VarClipSetId>
          <ConditionFlags/>
          <InformationFlags/>
        </Item>
      </Maps>
    </Item>
    <Item type="CClipSetMap">
      <Name>ENTRY_CLIPSET_MAP_PLANE_LUXOR2_EXTRA_RIGHT_2</Name>
      <Maps>
        <Item>
          <ClipSetId>anim@veh@plane@luxor2@rear@rps3@enter_exit</ClipSetId>
          <VarClipSetId>EntryVarClipSet</VarClipSetId>
          <ConditionFlags/>
          <InformationFlags/>
        </Item>
      </Maps>
    </Item>
    <Item type="CClipSetMap">
      <Name>EXIT_CLIPSET_MAP_PLANE_LUXOR2_EXTRA_RIGHT_2</Name>
      <Maps>
        <Item>
          <ClipSetId>anim@veh@plane@luxor2@rear@rps3@enter_exit</ClipSetId>
          <VarClipSetId>ExitVarClipSet</VarClipSetId>
          <ConditionFlags/>
          <InformationFlags/>
        </Item>
      </Maps>
    </Item>
    <Item type="CClipSetMap">
      <Name>ENTRY_CLIPSET_MAP_PLANE_LUXOR2_EXTRA_RIGHT_3</Name>
      <Maps>
        <Item>
          <ClipSetId>anim@veh@plane@luxor2@rear@rps4@enter_exit</ClipSetId>
          <VarClipSetId>EntryVarClipSet</VarClipSetId>
          <ConditionFlags/>
          <InformationFlags/>
        </Item>
      </Maps>
    </Item>
    <Item type="CClipSetMap">
      <Name>EXIT_CLIPSET_MAP_PLANE_LUXOR2_EXTRA_RIGHT_3</Name>
      <Maps>
        <Item>
          <ClipSetId>anim@veh@plane@luxor2@rear@rps4@enter_exit</ClipSetId>
          <VarClipSetId>ExitVarClipSet</VarClipSetId>
          <ConditionFlags/>
          <InformationFlags/>
        </Item>
      </Maps>
    </Item>
    <Item type="CClipSetMap">
      <Name>ENTRY_CLIPSET_MAP_BOAT_DINGHY_REAR_LEFT</Name>
      <Maps>
        <Item>
          <ClipSetId>clipset@veh@boat@dinghy@rds@enter_exit</ClipSetId>
          <VarClipSetId>EntryVarClipSet</VarClipSetId>
          <ConditionFlags/>
          <InformationFlags/>
        </Item>
      </Maps>
    </Item>
    <Item type="CClipSetMap">
      <Name>EXIT_CLIPSET_MAP_BOAT_DINGHY_REAR_LEFT</Name>
      <Maps>
        <Item>
          <ClipSetId>clipset@veh@boat@dinghy@rds@enter_exit</ClipSetId>
          <VarClipSetId>ExitVarClipSet</VarClipSetId>
          <ConditionFlags/>
          <InformationFlags/>
        </Item>
      </Maps>
    </Item>
    <Item type="CClipSetMap">
      <Name>ENTRY_CLIPSET_MAP_HELI_SAVAGE_FRONT</Name>
      <Maps>
        <Item>
          <ClipSetId>clipset@veh@savage@front@ds@enter_exit</ClipSetId>
          <VarClipSetId>EntryVarClipSet</VarClipSetId>
          <ConditionFlags/>
        </Item>
      </Maps>
    </Item>
    <Item type="CClipSetMap">
      <Name>EXIT_CLIPSET_MAP_HELI_SAVAGE_FRONT</Name>
      <Maps>
        <Item>
          <ClipSetId>clipset@veh@savage@front@ds@enter_exit</ClipSetId>
          <VarClipSetId>ExitVarClipSet</VarClipSetId>
          <ConditionFlags/>
        </Item>
      </Maps>
    </Item>
    <Item type="CClipSetMap">
      <Name>INSIDE_CLIPSET_MAP_HELI_SAVAGE_FRONT</Name>
      <Maps>
        <Item>
          <ClipSetId>clipset@veh@savage@front@ds@base</ClipSetId>
          <VarClipSetId>InsideVarClipSet</VarClipSetId>
          <ConditionFlags/>
          <InformationFlags/>
        </Item>
      </Maps>
    </Item>
    <Item type="CClipSetMap">
      <Name>ENTRY_CLIPSET_MAP_HELI_SAVAGE_REAR</Name>
      <Maps>
        <Item>
          <ClipSetId>clipset@veh@savage@rear@ds@enter_exit</ClipSetId>
          <VarClipSetId>EntryVarClipSet</VarClipSetId>
          <ConditionFlags/>
        </Item>
      </Maps>
    </Item>
    <Item type="CClipSetMap">
      <Name>EXIT_CLIPSET_MAP_HELI_SAVAGE_REAR</Name>
      <Maps>
        <Item>
          <ClipSetId>clipset@veh@savage@rear@ds@enter_exit</ClipSetId>
          <VarClipSetId>ExitVarClipSet</VarClipSetId>
          <ConditionFlags/>
        </Item>
      </Maps>
    </Item>
    <Item type="CClipSetMap">
      <Name>ENTRY_CLIPSET_MAP_PLANE_VELUM2_REAR_BACK</Name>
      <Maps>
        <Item>
          <ClipSetId>clipset@veh@plane@velum2@rear@back@enter_exit</ClipSetId>
          <VarClipSetId>EntryVarClipSet</VarClipSetId>
          <ConditionFlags/>
          <InformationFlags/>
        </Item>
      </Maps>
    </Item>
    <Item type="CClipSetMap">
      <Name>EXIT_CLIPSET_MAP_PLANE_VELUM2_REAR_BACK</Name>
      <Maps>
        <Item>
          <ClipSetId>clipset@veh@plane@velum2@rear@back@enter_exit</ClipSetId>
          <VarClipSetId>ExitVarClipSet</VarClipSetId>
          <ConditionFlags/>
          <InformationFlags/>
        </Item>
      </Maps>
    </Item>
    <Item type="CClipSetMap">
      <Name>ENTRY_CLIPSET_MAP_PLANE_VELUM2_REAR_LEFT</Name>
      <Maps>
        <Item>
          <ClipSetId>clipset@veh@plane@velum2@rear@ds@enter_exit</ClipSetId>
          <VarClipSetId>EntryVarClipSet</VarClipSetId>
          <ConditionFlags/>
          <InformationFlags/>
        </Item>
      </Maps>
    </Item>
    <Item type="CClipSetMap">
      <Name>EXIT_CLIPSET_MAP_PLANE_VELUM2_REAR_LEFT</Name>
      <Maps>
        <Item>
          <ClipSetId>clipset@veh@plane@velum2@rear@ds@enter_exit</ClipSetId>
          <VarClipSetId>ExitVarClipSet</VarClipSetId>
          <ConditionFlags/>
          <InformationFlags/>
        </Item>
      </Maps>
    </Item>
    <Item type="CClipSetMap">
      <Name>ENTRY_CLIPSET_MAP_PLANE_VELUM2_REAR_RIGHT</Name>
      <Maps>
        <Item>
          <ClipSetId>clipset@veh@plane@velum2@rear@ps@enter_exit</ClipSetId>
          <VarClipSetId>EntryVarClipSet</VarClipSetId>
          <ConditionFlags/>
          <InformationFlags/>
        </Item>
      </Maps>
    </Item>
    <Item type="CClipSetMap">
      <Name>EXIT_CLIPSET_MAP_PLANE_VELUM2_REAR_RIGHT</Name>
      <Maps>
        <Item>
          <ClipSetId>clipset@veh@plane@velum2@rear@ps@enter_exit</ClipSetId>
          <VarClipSetId>ExitVarClipSet</VarClipSetId>
          <ConditionFlags/>
          <InformationFlags/>
        </Item>
      </Maps>
    </Item>
    <Item type="CClipSetMap">
      <Name>INSIDE_CLIPSET_MAP_HELI_VELUM2_REAR_RIGHT</Name>
      <Maps>
        <Item>
          <ClipSetId>clipset@veh@plane@velum2@rear@ds@base_die</ClipSetId>
          <VarClipSetId>InsideVarClipSet</VarClipSetId>
          <ConditionFlags/>
          <InformationFlags/>
        </Item>
      </Maps>
    </Item>
    <Item type="CClipSetMap">
      <Name>ENTRY_CLIPSET_MAP_TRUCK_GUARDIAN_BACK_LEFT</Name>
      <Maps>
        <Item>
          <ClipSetId>clipset@veh@jeep@guardian@rds@enter_exit</ClipSetId>
          <VarClipSetId>EntryVarClipSet</VarClipSetId>
          <ConditionFlags/>
          <InformationFlags/>
        </Item>
      </Maps>
    </Item>
    <Item type="CClipSetMap">
      <Name>EXIT_CLIPSET_MAP_TRUCK_GUARDIAN_BACK_LEFT</Name>
      <Maps>
        <Item>
          <ClipSetId>clipset@veh@jeep@guardian@rds@enter_exit</ClipSetId>
          <VarClipSetId>ExitVarClipSet</VarClipSetId>
          <ConditionFlags/>
          <InformationFlags/>
        </Item>
      </Maps>
    </Item>
    <Item type="CClipSetMap">
      <Name>ENTRY_CLIPSET_MAP_TRUCK_GUARDIAN_BACK_RIGHT</Name>
      <Maps>
        <Item>
          <ClipSetId>clipset@veh@jeep@guardian@rps@enter_exit</ClipSetId>
          <VarClipSetId>EntryVarClipSet</VarClipSetId>
          <ConditionFlags/>
          <InformationFlags/>
        </Item>
      </Maps>
    </Item>
    <Item type="CClipSetMap">
      <Name>EXIT_CLIPSET_MAP_TRUCK_GUARDIAN_BACK_RIGHT</Name>
      <Maps>
        <Item>
          <ClipSetId>clipset@veh@jeep@guardian@rps@enter_exit</ClipSetId>
          <VarClipSetId>ExitVarClipSet</VarClipSetId>
          <ConditionFlags/>
          <InformationFlags/>
        </Item>
      </Maps>
    </Item>
    <Item type="CClipSetMap">
      <Name>COMMON_CLIPSET_MAP_STD_TECHNICAL_TURRET</Name>
      <Maps>
        <Item>
          <ClipSetId>clipset@veh@technical@turret@align_fallback</ClipSetId>
          <VarClipSetId>CommonVarClipSet</VarClipSetId>
          <ConditionFlags/>
          <InformationFlags/>
        </Item>
        <Item>
          <ClipSetId>clipset@veh@technical@turret@align_rear</ClipSetId>
          <VarClipSetId>CommonVarClipSet</VarClipSetId>
          <ConditionFlags/>
          <InformationFlags/>
        </Item>
        <Item>
          <ClipSetId>clipset@veh@technical@turret@align_side</ClipSetId>
          <VarClipSetId>CommonVarClipSet</VarClipSetId>
          <ConditionFlags/>
          <InformationFlags/>
        </Item>
      </Maps>
    </Item>
    <Item type="CClipSetMap">
      <Name>INSIDE_CLIPSET_MAP_STD_TECHNICAL_TURRET</Name>
      <Maps>
        <Item>
          <ClipSetId>clipset@veh@technical@turret@base</ClipSetId>
          <VarClipSetId>InsideVarClipSet</VarClipSetId>
          <ConditionFlags/>
          <InformationFlags/>
        </Item>
      </Maps>
    </Item>
    <Item type="CClipSetMap">
      <Name>ENTRY_CLIPSET_MAP_STD_TECHNICAL_REAR_LEFT_TURRET</Name>
      <Maps>
        <Item>
          <ClipSetId>clipset@veh@technical@turret@rds@enter_exit</ClipSetId>
          <VarClipSetId>EntryVarClipSet</VarClipSetId>
          <ConditionFlags/>
          <InformationFlags/>
        </Item>
        <Item>
          <ClipSetId>clipset@veh@technical@turret@enter_exit_left</ClipSetId>
          <VarClipSetId>EntryVarClipSet</VarClipSetId>
          <ConditionFlags/>
          <InformationFlags/>
        </Item>
      </Maps>
    </Item>
    <Item type="CClipSetMap">
      <Name>EXIT_CLIPSET_MAP_STD_TECHNICAL_REAR_LEFT_TURRET</Name>
      <Maps>
        <Item>
          <ClipSetId>clipset@veh@technical@turret@rds@enter_exit</ClipSetId>
          <VarClipSetId>ExitVarClipSet</VarClipSetId>
          <ConditionFlags/>
          <InformationFlags/>
        </Item>
        <Item>
          <ClipSetId>clipset@veh@technical@turret@enter_exit_left</ClipSetId>
          <VarClipSetId>EntryVarClipSet</VarClipSetId>
          <ConditionFlags/>
          <InformationFlags/>
        </Item>
      </Maps>
    </Item>
    <Item type="CClipSetMap">
      <Name>ENTRY_CLIPSET_MAP_STD_TECHNICAL_REAR_RIGHT_TURRET</Name>
      <Maps>
        <Item>
          <ClipSetId>clipset@veh@technical@turret@rps@enter_exit</ClipSetId>
          <VarClipSetId>EntryVarClipSet</VarClipSetId>
          <ConditionFlags/>
          <InformationFlags/>
        </Item>
        <Item>
          <ClipSetId>clipset@veh@technical@turret@enter_exit_right</ClipSetId>
          <VarClipSetId>EntryVarClipSet</VarClipSetId>
          <ConditionFlags/>
          <InformationFlags/>
        </Item>
      </Maps>
    </Item>
    <Item type="CClipSetMap">
      <Name>EXIT_CLIPSET_MAP_STD_TECHNICAL_REAR_RIGHT_TURRET</Name>
      <Maps>
        <Item>
          <ClipSetId>clipset@veh@technical@turret@rps@enter_exit</ClipSetId>
          <VarClipSetId>ExitVarClipSet</VarClipSetId>
          <ConditionFlags/>
          <InformationFlags/>
        </Item>
        <Item>
          <ClipSetId>clipset@veh@technical@turret@enter_exit_right</ClipSetId>
          <VarClipSetId>EntryVarClipSet</VarClipSetId>
          <ConditionFlags/>
          <InformationFlags/>
        </Item>
      </Maps>
    </Item>
    <Item type="CClipSetMap">
      <Name>ENTRY_CLIPSET_MAP_STD_TECHNICAL_REAR_TURRET</Name>
      <Maps>
        <Item>
          <ClipSetId>clipset@veh@technical@turret@rear@enter_exit</ClipSetId>
          <VarClipSetId>EntryVarClipSet</VarClipSetId>
          <ConditionFlags/>
          <InformationFlags/>
        </Item>
      </Maps>
    </Item>
    <Item type="CClipSetMap">
      <Name>EXIT_CLIPSET_MAP_STD_TECHNICAL_REAR_TURRET</Name>
      <Maps>
        <Item>
          <ClipSetId>clipset@veh@technical@turret@rear@enter_exit</ClipSetId>
          <VarClipSetId>ExitVarClipSet</VarClipSetId>
          <ConditionFlags/>
          <InformationFlags/>
        </Item>
      </Maps>
    </Item>
  </ClipSetMaps>
  <VehicleCoverBoundOffsetInfos>
    <Item type="CVehicleCoverBoundOffsetInfo">
      <Name>BF400_COVER_OFFSET_INFO</Name>
      <ExtraSideOffset value="0.100000"/>
      <ExtraForwardOffset value="-0.150000"/>
      <ExtraBackwardOffset value="-0.050000"/>
      <ExtraZOffset value="-0.100000"/>
      <CoverBoundInfos/>
    </Item>
    <Item type="CVehicleCoverBoundOffsetInfo">
      <Name>BRIOSO_COVER_OFFSET_INFO</Name>
      <ExtraSideOffset value="-0.020000"/>
      <ExtraForwardOffset value="-0.175000"/>
      <ExtraBackwardOffset value="-0.050000"/>
      <ExtraZOffset value="-0.550000"/>
      <CoverBoundInfos/>
    </Item>
    <Item type="CVehicleCoverBoundOffsetInfo">
      <Name>CLIFFHANGER_COVER_OFFSET_INFO</Name>
      <ExtraSideOffset value="0.100000"/>
      <ExtraForwardOffset value="-0.250000"/>
      <ExtraBackwardOffset value="-0.150000"/>
      <ExtraZOffset value="-0.150000"/>
      <CoverBoundInfos/>
    </Item>
    <Item type="CVehicleCoverBoundOffsetInfo">
      <Name>LE7B_COVER_OFFSET_INFO</Name>
      <ExtraSideOffset value="-0.075000"/>
      <ExtraForwardOffset value="-0.050000"/>
      <ExtraBackwardOffset value="-0.200000"/>
      <ExtraZOffset value="-0.050000"/>
      <CoverBoundInfos/>
    </Item>
    <Item type="CVehicleCoverBoundOffsetInfo">
      <Name>LYNX_COVER_OFFSET_INFO</Name>
      <ExtraSideOffset value="0.100000"/>
      <ExtraForwardOffset value="-0.150000"/>
      <ExtraBackwardOffset value="-0.050000"/>
      <ExtraZOffset value="-0.100000"/>
      <CoverBoundInfos>
        <Item>
          <Name>Front</Name>
          <Position x="0.000000" y="0.750000" z="-0.100000"/>
          <Length value="2.900000"/>
          <Width value="1.900000"/>
          <Height value="1.200000"/>
          <ActiveBoundExclusions/>
        </Item>
        <Item>
          <Name>Back</Name>
          <Position x="0.000000" y="-1.500000" z="-0.100000"/>
          <Length value="1.600000"/>
          <Width value="2.100000"/>
          <Height value="1.200000"/>
          <ActiveBoundExclusions/>
        </Item>
      </CoverBoundInfos>
    </Item>
    <Item type="CVehicleCoverBoundOffsetInfo">
      <Name>OMNIS_COVER_OFFSET_INFO</Name>
      <ExtraSideOffset value="-0.075000"/>
      <ExtraForwardOffset value="-0.100000"/>
      <ExtraBackwardOffset value="-0.250000"/>
      <ExtraZOffset value="0.000000"/>
      <CoverBoundInfos/>
    </Item>
    <Item type="CVehicleCoverBoundOffsetInfo">
      <Name>RALLYTRUCK_COVER_OFFSET_INFO</Name>
      <ExtraSideOffset value="-0.050000"/>
      <ExtraForwardOffset value="-0.300000"/>
      <ExtraBackwardOffset value="0.000000"/>
      <ExtraZOffset value="0.000000"/>
      <CoverBoundInfos>
        <Item>
          <Name>Front</Name>
          <Position x="0.000000" y="3.275000" z="-0.150000"/>
          <Length value="2.500000"/>
          <Width value="3.050000"/>
          <Height value="2.500000"/>
          <ActiveBoundExclusions/>
        </Item>
        <Item>
          <Name>MIDDLE</Name>
          <Position x="0.000000" y="0.175000" z="-0.150000"/>
          <Length value="3.700000"/>
          <Width value="2.775000"/>
          <Height value="2.500000"/>
          <ActiveBoundExclusions/>
        </Item>
        <Item>
          <Name>REARWHEELS</Name>
          <Position x="0.000000" y="-2.425000" z="-0.150000"/>
          <Length value="1.500000"/>
          <Width value="2.950000"/>
          <Height value="2.500000"/>
          <ActiveBoundExclusions/>
        </Item>
        <Item>
          <Name>REAR</Name>
          <Position x="0.000000" y="-3.725000" z="-0.150000"/>
          <Length value="1.100000"/>
          <Width value="2.775000"/>
          <Height value="2.500000"/>
          <ActiveBoundExclusions/>
        </Item>
      </CoverBoundInfos>
    </Item>
    <Item type="CVehicleCoverBoundOffsetInfo">
      <Name>SHEAVA_COVER_OFFSET_INFO</Name>
      <ExtraSideOffset value="-0.100000"/>
      <ExtraForwardOffset value="-0.150000"/>
      <ExtraBackwardOffset value="-0.100000"/>
      <ExtraZOffset value="-0.200000"/>
      <CoverBoundInfos/>
    </Item>
    <Item type="CVehicleCoverBoundOffsetInfo">
      <Name>TAMPA2_COVER_OFFSET_INFO</Name>
      <ExtraSideOffset value="-0.050000"/>
      <ExtraForwardOffset value="-0.200000"/>
      <ExtraBackwardOffset value="-0.100000"/>
      <ExtraZOffset value="0.200000"/>
      <CoverBoundInfos>
        <Item>
          <Name>Front</Name>
          <Position x="0.000000" y="1.700000" z="0.150000"/>
          <Length value="1.300000"/>
          <Width value="2.100000"/>
          <Height value="1.000000"/>
          <ActiveBoundExclusions/>
        </Item>
        <Item>
          <Name>MIDDLE</Name>
          <Position x="0.000000" y="0.100000" z="0.150000"/>
          <Length value="1.900000"/>
          <Width value="1.950000"/>
          <Height value="1.000000"/>
          <ActiveBoundExclusions/>
        </Item>
        <Item>
          <Name>REARWHEELS</Name>
          <Position x="0.000000" y="-1.350000" z="0.150000"/>
          <Length value="1.000000"/>
          <Width value="2.200000"/>
          <Height value="1.000000"/>
          <ActiveBoundExclusions/>
        </Item>
        <Item>
          <Name>REAR</Name>
          <Position x="0.000000" y="-2.275000" z="0.150000"/>
          <Length value="0.850000"/>
          <Width value="1.900000"/>
          <Height value="1.000000"/>
          <ActiveBoundExclusions/>
        </Item>
      </CoverBoundInfos>
    </Item>
    <Item type="CVehicleCoverBoundOffsetInfo">
      <Name>TROPHY_COVER_OFFSET_INFO</Name>
      <ExtraSideOffset value="-0.150000"/>
      <ExtraForwardOffset value="-0.100000"/>
      <ExtraBackwardOffset value="-0.200000"/>
      <ExtraZOffset value="0.100000"/>
      <CoverBoundInfos>
        <Item>
          <Name>FRONT_BUMPER</Name>
          <Position x="0.000000" y="2.250000" z="0.000000"/>
          <Length value="0.500000"/>
          <Width value="2.150000"/>
          <Height value="1.500000"/>
          <ActiveBoundExclusions/>
        </Item>
        <Item>
          <Name>WHEELS_FRONT</Name>
          <Position x="0.000000" y="1.450000" z="0.000000"/>
          <Length value="1.100000"/>
          <Width value="2.450000"/>
          <Height value="1.500000"/>
          <ActiveBoundExclusions/>
        </Item>
        <Item>
          <Name>MIDDLE</Name>
          <Position x="0.000000" y="-0.025000" z="0.000000"/>
          <Length value="1.850000"/>
          <Width value="2.150000"/>
          <Height value="1.500000"/>
          <ActiveBoundExclusions/>
        </Item>
        <Item>
          <Name>WHEELS_REAR</Name>
          <Position x="0.000000" y="-1.450000" z="0.000000"/>
          <Length value="1.000000"/>
          <Width value="2.450000"/>
          <Height value="1.500000"/>
          <ActiveBoundExclusions/>
        </Item>
        <Item>
          <Name>REAR_BUMPER</Name>
          <Position x="0.000000" y="-2.300000" z="0.000000"/>
          <Length value="0.720000"/>
          <Width value="2.150000"/>
          <Height value="1.500000"/>
          <ActiveBoundExclusions/>
        </Item>
      </CoverBoundInfos>
    </Item>
    <Item type="CVehicleCoverBoundOffsetInfo">
      <Name>TROPHY2_COVER_OFFSET_INFO</Name>
      <ExtraSideOffset value="-0.150000"/>
      <ExtraForwardOffset value="-0.100000"/>
      <ExtraBackwardOffset value="-0.200000"/>
      <ExtraZOffset value="0.100000"/>
      <CoverBoundInfos>
        <Item>
          <Name>Front</Name>
          <Position x="0.000000" y="1.600000" z="0.000000"/>
          <Length value="1.700000"/>
          <Width value="2.450000"/>
          <Height value="1.500000"/>
          <ActiveBoundExclusions/>
        </Item>
        <Item>
          <Name>MIDDLE</Name>
          <Position x="0.000000" y="0.075000" z="0.200000"/>
          <Length value="1.350000"/>
          <Width value="2.300000"/>
          <Height value="1.900000"/>
          <ActiveBoundExclusions/>
        </Item>
        <Item>
          <Name>REAR</Name>
          <Position x="0.000000" y="-1.900000" z="0.150000"/>
          <Length value="2.600000"/>
          <Width value="2.500000"/>
          <Height value="1.800000"/>
          <ActiveBoundExclusions/>
        </Item>
        <Item>
          <Name>BACKTYRES</Name>
          <Position x="0.000000" y="-3.312000" z="0.150000"/>
          <Length value="0.225000"/>
          <Width value="1.000000"/>
          <Height value="1.800000"/>
          <ActiveBoundExclusions/>
        </Item>
      </CoverBoundInfos>
    </Item>
    <Item type="CVehicleCoverBoundOffsetInfo">
      <Name>TROPOS_COVER_OFFSET_INFO</Name>
      <ExtraSideOffset value="0.100000"/>
      <ExtraForwardOffset value="-0.150000"/>
      <ExtraBackwardOffset value="-0.050000"/>
      <ExtraZOffset value="-0.100000"/>
      <CoverBoundInfos>
        <Item>
          <Name>Front</Name>
          <Position x="0.000000" y="0.750000" z="0.175000"/>
          <Length value="2.500000"/>
          <Width value="1.800000"/>
          <Height value="1.100000"/>
          <ActiveBoundExclusions/>
        </Item>
        <Item>
          <Name>Back</Name>
          <Position x="0.000000" y="-1.200000" z="0.175000"/>
          <Length value="1.400000"/>
          <Width value="2.000000"/>
          <Height value="1.100000"/>
          <ActiveBoundExclusions/>
        </Item>
      </CoverBoundInfos>
    </Item>
    <Item type="CVehicleCoverBoundOffsetInfo">
      <Name>TYRUS_COVER_OFFSET_INFO</Name>
      <ExtraSideOffset value="-0.050000"/>
      <ExtraForwardOffset value="-0.100000"/>
      <ExtraBackwardOffset value="-0.150000"/>
      <ExtraZOffset value="-0.175000"/>
      <CoverBoundInfos/>
    </Item>
    <Item type="CVehicleCoverBoundOffsetInfo">
      <Name>FELTZER3_COVER_OFFSET_INFO</Name>
      <ExtraSideOffset value="-0.050000"/>
      <ExtraForwardOffset value="0.050000"/>
      <ExtraBackwardOffset value="-0.300000"/>
      <ExtraZOffset value="-0.200000"/>
      <CoverBoundInfos/>
    </Item>
    <Item type="CVehicleCoverBoundOffsetInfo">
      <Name>OSIRIS_COVER_OFFSET_INFO</Name>
      <ExtraSideOffset value="-0.100000"/>
      <ExtraForwardOffset value="-0.200000"/>
      <ExtraBackwardOffset value="-0.050000"/>
      <ExtraZOffset value="0.050000"/>
      <CoverBoundInfos/>
    </Item>
    <Item type="CVehicleCoverBoundOffsetInfo">
      <Name>SWIFT2_COVER_OFFSET_INFO</Name>
      <ExtraSideOffset value="0.100000"/>
      <ExtraForwardOffset value="-0.200000"/>
      <ExtraBackwardOffset value="-0.250000"/>
      <ExtraZOffset value="1.000000"/>
      <CoverBoundInfos>
        <Item>
          <Name>NOSE</Name>
          <Position x="0.000000" y="4.525000" z="-0.100000"/>
          <Length value="1.100000"/>
          <Width value="1.700000"/>
          <Height value="1.200000"/>
          <ActiveBoundExclusions/>
        </Item>
        <Item>
          <Name>BODY</Name>
          <Position x="0.000000" y="2.375000" z="0.300000"/>
          <Length value="3.200000"/>
          <Width value="1.800000"/>
          <Height value="2.000000"/>
          <ActiveBoundExclusions/>
        </Item>
      </CoverBoundInfos>
    </Item>
    <Item type="CVehicleCoverBoundOffsetInfo">
      <Name>VIRGO_COVER_OFFSET_INFO</Name>
      <ExtraSideOffset value="-0.050000"/>
      <ExtraForwardOffset value="-0.200000"/>
      <ExtraBackwardOffset value="-0.100000"/>
      <ExtraZOffset value="0.050000"/>
      <CoverBoundInfos/>
    </Item>
    <Item type="CVehicleCoverBoundOffsetInfo">
      <Name>WINDSOR_COVER_OFFSET_INFO</Name>
      <ExtraSideOffset value="0.000000"/>
      <ExtraForwardOffset value="0.000000"/>
      <ExtraBackwardOffset value="-0.250000"/>
      <ExtraZOffset value="0.050000"/>
      <CoverBoundInfos/>
    </Item>
    <Item type="CVehicleCoverBoundOffsetInfo">
      <Name>DOMINATOR_COVER_OFFSET_INFO</Name>
      <ExtraSideOffset value="0.000000"/>
      <ExtraForwardOffset value="0.100000"/>
      <ExtraBackwardOffset value="-0.200000"/>
      <ExtraZOffset value="0.000000"/>
      <CoverBoundInfos/>
    </Item>
    <Item type="CVehicleCoverBoundOffsetInfo">
      <Name>CASCO_COVER_OFFSET_INFO</Name>
      <ExtraSideOffset value="-0.050000"/>
      <ExtraForwardOffset value="0.000000"/>
      <ExtraBackwardOffset value="-0.250000"/>
      <ExtraZOffset value="-0.250000"/>
      <CoverBoundInfos/>
    </Item>
    <Item type="CVehicleCoverBoundOffsetInfo">
      <Name>GUARDIAN_COVER_OFFSET_INFO</Name>
      <ExtraSideOffset value="-0.050000"/>
      <ExtraForwardOffset value="-0.075000"/>
      <ExtraBackwardOffset value="-0.375000"/>
      <ExtraZOffset value="0.325000"/>
      <CoverBoundInfos/>
    </Item>
    <Item type="CVehicleCoverBoundOffsetInfo">
      <Name>SLAMVAN2_COVER_OFFSET_INFO</Name>
      <ExtraSideOffset value="-0.050000"/>
      <ExtraForwardOffset value="0.000000"/>
      <ExtraBackwardOffset value="-0.250000"/>
      <ExtraZOffset value="0.300000"/>
      <CoverBoundInfos/>
    </Item>
  </VehicleCoverBoundOffsetInfos>
  <BicycleInfos/>
  <POVTuningInfos/>
  <EntryAnimVariations>
    <Item type="CEntryAnimVariations">
      <Name>ENTRY_ANIM_VARIATIONS_PLANE_VELUM2_REAR_BACK</Name>
      <Flags/>
      <Conditions/>
      <EntryAnims/>
      <JackingAnims/>
      <ExitAnims>
        <Item type="CConditionalClipSet">
          <ClipSet>clipset@veh@plane@velum2@rear@back@exit_skydive</ClipSet>
          <AssociatedSpeech/>
          <Conditions/>
          <ActionFlags>IsSkyDive</ActionFlags>
        </Item>
      </ExitAnims>
    </Item>
    <Item type="CEntryAnimVariations">
      <Name>ENTRY_ANIM_VARIATIONS_PLANE_VELUM2_REAR_LEFT</Name>
      <Flags/>
      <Conditions/>
      <EntryAnims/>
      <JackingAnims/>
      <ExitAnims>
        <Item type="CConditionalClipSet">
          <ClipSet>clipset@veh@plane@velum2@rear@ds@exit_skydive</ClipSet>
          <AssociatedSpeech/>
          <Conditions/>
          <ActionFlags>IsSkyDive</ActionFlags>
        </Item>
      </ExitAnims>
    </Item>
    <Item type="CEntryAnimVariations">
      <Name>ENTRY_ANIM_VARIATIONS_PLANE_VELUM2_REAR_RIGHT</Name>
      <Flags/>
      <Conditions/>
      <EntryAnims/>
      <JackingAnims/>
      <ExitAnims>
        <Item type="CConditionalClipSet">
          <ClipSet>clipset@veh@plane@velum2@rear@ps@exit_skydive</ClipSet>
          <AssociatedSpeech/>
          <Conditions/>
          <ActionFlags>IsSkyDive</ActionFlags>
        </Item>
      </ExitAnims>
    </Item>
  </EntryAnimVariations>
  <VehicleExtraPointsInfos>
    <Item type="CVehicleExtraPointsInfo">
      <Name>EXTRA_VEHICLE_POINTS_PLANE_LUXOR2_REAR</Name>
      <ExtraVehiclePoints>
        <Item>
          <LocationType>DRIVER_SEAT_RELATIVE</LocationType>
          <PointType>GET_IN</PointType>
          <Position x="0.097500" y="-1.082400" z="0.547100"/>
          <Heading value="-2.712000"/>
          <BoneName/>
        </Item>
        <Item>
          <LocationType>DRIVER_SEAT_RELATIVE</LocationType>
          <PointType>GET_OUT</PointType>
          <Position x="0.340000" y="-1.175000" z="0.548000"/>
          <Heading value="1.538000"/>
          <BoneName/>
        </Item>
      </ExtraVehiclePoints>
    </Item>
    <Item type="CVehicleExtraPointsInfo">
      <Name>EXTRA_VEHICLE_POINTS_DINGHY_REAR_LEFT</Name>
      <ExtraVehiclePoints>
        <Item>
          <LocationType>SEAT_RELATIVE</LocationType>
          <PointType>GET_IN</PointType>
          <Position x="0.320000" y="-0.600000" z="0.625000"/>
          <Heading value="0.000000"/>
          <BoneName/>
        </Item>
        <Item>
          <LocationType>SEAT_RELATIVE</LocationType>
          <PointType>GET_IN_2</PointType>
          <Position x="-1.330000" y="0.185000" z="0.007000"/>
          <Heading value="-1.694000"/>
          <BoneName/>
        </Item>
        <Item>
          <LocationType>SEAT_RELATIVE</LocationType>
          <PointType>GET_IN_3</PointType>
          <Position x="-1.020000" y="0.242000" z="1.178000"/>
          <Heading value="-2.670000"/>
          <BoneName/>
        </Item>
        <Item>
          <LocationType>SEAT_RELATIVE</LocationType>
          <PointType>GET_IN_4</PointType>
          <Position x="-0.881000" y="-0.621000" z="0.624000"/>
          <Heading value="0.000000"/>
          <BoneName/>
        </Item>
        <Item>
          <LocationType>SEAT_RELATIVE</LocationType>
          <PointType>GET_OUT</PointType>
          <Position x="-0.450000" y="-0.400000" z="0.625000"/>
          <Heading value="0.000000"/>
          <BoneName/>
        </Item>
      </ExtraVehiclePoints>
    </Item>
    <Item type="CVehicleExtraPointsInfo">
      <Name>EXTRA_VEHICLE_POINTS_HELI_SAVAGE_FRONT</Name>
      <ExtraVehiclePoints>
        <Item>
          <LocationType>SEAT_RELATIVE</LocationType>
          <PointType>GET_IN</PointType>
          <Position x="-1.120000" y="0.095000" z="0.098000"/>
          <Heading value="-1.434000"/>
          <BoneName/>
        </Item>
      </ExtraVehiclePoints>
    </Item>
    <Item type="CVehicleExtraPointsInfo">
      <Name>EXTRA_VEHICLE_POINTS_HELI_SAVAGE_REAR</Name>
      <ExtraVehiclePoints>
        <Item>
          <LocationType>SEAT_RELATIVE</LocationType>
          <PointType>GET_IN</PointType>
          <Position x="-1.120000" y="0.200000" z="-0.069000"/>
          <Heading value="-1.384000"/>
          <BoneName/>
        </Item>
      </ExtraVehiclePoints>
    </Item>
    <Item type="CVehicleExtraPointsInfo">
      <Name>EXTRA_VEHICLE_POINTS_PLANE_VELUM2_REAR</Name>
      <ExtraVehiclePoints>
        <Item>
          <LocationType>ENTITY_RELATIVE</LocationType>
          <PointType>GET_IN</PointType>
          <Position x="-0.340000" y="-0.830000" z="0.360000"/>
          <Heading value="-1.570000"/>
          <BoneName/>
        </Item>
        <Item>
          <LocationType>ENTITY_RELATIVE</LocationType>
          <PointType>GET_OUT</PointType>
          <Position x="-0.005000" y="-0.640000" z="0.332000"/>
          <Heading value="2.010000"/>
          <BoneName/>
        </Item>
      </ExtraVehiclePoints>
    </Item>
    <Item type="CVehicleExtraPointsInfo">
      <Name>EXTRA_VEHICLE_POINTS_STD_TECHNICAL_REAR_LEFT_TURRET</Name>
      <ExtraVehiclePoints>
        <Item>
          <LocationType>SEAT_RELATIVE</LocationType>
          <PointType>GET_IN</PointType>
          <Position x="0.000000" y="-0.2500000" z="0.500000"/>
          <Heading value="0.0"/>
          <BoneName/>
        </Item>
        <Item>
          <LocationType>ENTITY_RELATIVE</LocationType>
          <PointType>CLIMB_UP_FIXUP_POINT</PointType>
          <Position x="-0.76000000" y="-1.450" z="1.500000"/>
          <Heading value="-1.5707"/>
          <BoneName/>
        </Item>
        <Item>
          <LocationType>SEAT_RELATIVE</LocationType>
          <PointType>GET_OUT</PointType>
          <Position x="0.000000" y="-0.100000" z="0.500000"/>
          <Heading value="0.0"/>
          <BoneName/>
        </Item>
        <Item>
          <LocationType>SEAT_RELATIVE</LocationType>
          <PointType>ON_BOARD_JACK</PointType>
          <Position x="-0.50000" y="1.200" z="0.000000"/>
          <Heading value="-2.0"/>
          <BoneName/>
        </Item>
      </ExtraVehiclePoints>
    </Item>
    <Item type="CVehicleExtraPointsInfo">
      <Name>EXTRA_VEHICLE_POINTS_STD_TECHNICAL_REAR_RIGHT_TURRET</Name>
      <ExtraVehiclePoints>
        <Item>
          <LocationType>SEAT_RELATIVE</LocationType>
          <PointType>GET_IN</PointType>
          <Position x="0.000000" y="-0.2500000" z="0.500000"/>
          <Heading value="0.0"/>
          <BoneName/>
        </Item>
        <Item>
          <LocationType>ENTITY_RELATIVE</LocationType>
          <PointType>CLIMB_UP_FIXUP_POINT</PointType>
          <Position x="0.74000000" y="-1.400" z="1.500000"/>
          <Heading value="1.5707"/>
          <BoneName/>
        </Item>
        <Item>
          <LocationType>SEAT_RELATIVE</LocationType>
          <PointType>GET_OUT</PointType>
          <Position x="0.000000" y="-0.100000" z="0.500000"/>
          <Heading value="0.0"/>
          <BoneName/>
        </Item>
        <Item>
          <LocationType>SEAT_RELATIVE</LocationType>
          <PointType>ON_BOARD_JACK</PointType>
          <Position x="0.50000" y="1.2000" z="0.000000"/>
          <Heading value="2.0"/>
          <BoneName/>
        </Item>
      </ExtraVehiclePoints>
    </Item>
    <Item type="CVehicleExtraPointsInfo">
      <Name>EXTRA_VEHICLE_POINTS_STD_TECHNICAL_REAR_TURRET</Name>
      <ExtraVehiclePoints>
        <Item>
          <LocationType>SEAT_RELATIVE</LocationType>
          <PointType>GET_IN</PointType>
          <Position x="0.000000" y="-0.2500000" z="0.500000"/>
          <Heading value="0.0"/>
          <BoneName/>
        </Item>
        <Item>
          <LocationType>ENTITY_RELATIVE</LocationType>
          <PointType>CLIMB_UP_FIXUP_POINT</PointType>
          <Position x="0.00000" y="-2.1250" z="1.500000"/>
          <Heading value="0.0"/>
          <BoneName/>
        </Item>
        <Item>
          <LocationType>SEAT_RELATIVE</LocationType>
          <PointType>GET_OUT</PointType>
          <Position x="0.000000" y="-0.100000" z="0.500000"/>
          <Heading value="0.0"/>
          <BoneName/>
        </Item>
      </ExtraVehiclePoints>
    </Item>
  </VehicleExtraPointsInfos>
  <DrivebyWeaponGroups/>
  <VehicleDriveByAnimInfos>
    <Item type="CVehicleDriveByAnimInfo">
      <Name>LUXOR2_DB_ANIM_INFO_UNARMED_RDS</Name>
      <WeaponGroup ref="DRIVEBY_DEFAULT_UNARMED"/>
      <DriveByClipSet>anim@veh@driveby@plane@luxor2@rear_ds@0h</DriveByClipSet>
      <FirstPersonDriveByClipSet/>
      <AltFirstPersonDriveByClipSets/>
      <RestrictedDriveByClipSet/>
      <Network>STD_CAR_DRIVEBY</Network>
    </Item>
    <Item type="CVehicleDriveByAnimInfo">
      <Name>LUXOR2_DB_ANIM_INFO_UNARMED_RPS</Name>
      <WeaponGroup ref="DRIVEBY_DEFAULT_UNARMED"/>
      <DriveByClipSet>anim@veh@driveby@plane@luxor2@rear_ps@0h</DriveByClipSet>
      <FirstPersonDriveByClipSet/>
      <AltFirstPersonDriveByClipSets/>
      <RestrictedDriveByClipSet/>
      <Network>STD_CAR_DRIVEBY</Network>
    </Item>
    <Item type="CVehicleDriveByAnimInfo">
      <Name>LUXOR2_DB_ANIM_INFO_ONE_HANDED_RDS</Name>
      <WeaponGroup ref="DRIVEBY_DEFAULT_ONE_HANDED"/>
      <DriveByClipSet>anim@veh@driveby@plane@luxor2@rear_ds@1h</DriveByClipSet>
      <FirstPersonDriveByClipSet/>
      <AltFirstPersonDriveByClipSets/>
      <RestrictedDriveByClipSet/>
      <Network>STD_CAR_DRIVEBY</Network>
    </Item>
    <Item type="CVehicleDriveByAnimInfo">
      <Name>LUXOR2_DB_ANIM_INFO_ONE_HANDED_RPS</Name>
      <WeaponGroup ref="DRIVEBY_DEFAULT_ONE_HANDED"/>
      <DriveByClipSet>anim@veh@driveby@plane@luxor2@rear_ps@1h</DriveByClipSet>
      <FirstPersonDriveByClipSet/>
      <AltFirstPersonDriveByClipSets/>
      <RestrictedDriveByClipSet/>
      <Network>STD_CAR_DRIVEBY</Network>
    </Item>
  </VehicleDriveByAnimInfos>
  <VehicleDriveByInfos>
    <Item type="CVehicleDriveByInfo">
      <Name>DRIVEBY_LUXOR2_REAR_RIGHT</Name>
      <MinAimSweepHeadingAngleDegs value="-225.000000"/>
      <MaxAimSweepHeadingAngleDegs value="225.000000"/>
      <FirstPersonMinAimSweepHeadingAngleDegs value="-180.000000"/>
      <FirstPersonMaxAimSweepHeadingAngleDegs value="180.000000"/>
      <FirstPersonUnarmedMinAimSweepHeadingAngleDegs value="-180.000000"/>
      <FirstPersonUnarmedMaxAimSweepHeadingAngleDegs value="180.000000"/>
      <MinRestrictedAimSweepHeadingAngleDegs value="-225.000000"/>
      <MaxRestrictedAimSweepHeadingAngleDegs value="225.000000"/>
      <MinSmashWindowAngleDegs value="17.500000"/>
      <MaxSmashWindowAngleDegs value="180.000000"/>
      <MinSmashWindowAngleFirstPersonDegs value="17.500000"/>
      <MaxSmashWindowAngleFirstPersonDegs value="180.000000"/>
      <MaxSpeedParam value="0.500000"/>
      <MaxLongitudinalLeanBlendWeightDelta value="0.250000"/>
      <MaxLateralLeanBlendWeightDelta value="0.250000"/>
      <ApproachSpeedToWithinMaxBlendDelta value="1.000000"/>
      <SpineAdditiveBlendInDelay value="0.050000"/>
      <SpineAdditiveBlendInDurationStill value="0.150000"/>
      <SpineAdditiveBlendInDuration value="0.750000"/>
      <SpineAdditiveBlendOutDelay value="0.000000"/>
      <SpineAdditiveBlendOutDuration value="0.500000"/>
      <MinUnarmedDrivebyYawIfWindowRolledUp value="-3.140000"/>
      <MaxUnarmedDrivebyYawIfWindowRolledUp value="0.500000"/>
      <DriveByAnimInfos>
        <Item ref="LUXOR2_DB_ANIM_INFO_UNARMED_RPS"/>
        <Item ref="LUXOR2_DB_ANIM_INFO_ONE_HANDED_RPS"/>
      </DriveByAnimInfos>
      <DriveByCamera/>
      <DriveByFlags>UseThreeAnimIntroOutro LeftHandedFirstPersonAnims LeftHandedUnarmedFirstPersonAnims</DriveByFlags>
    </Item>
    <Item type="CVehicleDriveByInfo">
      <Name>DRIVEBY_LUXOR2_REAR_LEFT</Name>
      <MinAimSweepHeadingAngleDegs value="-225.000000"/>
      <MaxAimSweepHeadingAngleDegs value="225.000000"/>
      <FirstPersonMinAimSweepHeadingAngleDegs value="-180.000000"/>
      <FirstPersonMaxAimSweepHeadingAngleDegs value="180.000000"/>
      <FirstPersonUnarmedMinAimSweepHeadingAngleDegs value="-180.000000"/>
      <FirstPersonUnarmedMaxAimSweepHeadingAngleDegs value="180.000000"/>
      <MinRestrictedAimSweepHeadingAngleDegs value="-225.000000"/>
      <MaxRestrictedAimSweepHeadingAngleDegs value="225.000000"/>
      <MinSmashWindowAngleDegs value="-180.000000"/>
      <MaxSmashWindowAngleDegs value="-17.500000"/>
      <MinSmashWindowAngleFirstPersonDegs value="-180.000000"/>
      <MaxSmashWindowAngleFirstPersonDegs value="-17.500000"/>
      <MaxSpeedParam value="0.500000"/>
      <MaxLongitudinalLeanBlendWeightDelta value="0.250000"/>
      <MaxLateralLeanBlendWeightDelta value="0.250000"/>
      <ApproachSpeedToWithinMaxBlendDelta value="1.000000"/>
      <SpineAdditiveBlendInDelay value="0.050000"/>
      <SpineAdditiveBlendInDurationStill value="0.150000"/>
      <SpineAdditiveBlendInDuration value="0.750000"/>
      <SpineAdditiveBlendOutDelay value="0.000000"/>
      <SpineAdditiveBlendOutDuration value="0.500000"/>
      <MinUnarmedDrivebyYawIfWindowRolledUp value="-0.500000"/>
      <MaxUnarmedDrivebyYawIfWindowRolledUp value="3.140000"/>
      <DriveByAnimInfos>
        <Item ref="LUXOR2_DB_ANIM_INFO_UNARMED_RDS"/>
        <Item ref="LUXOR2_DB_ANIM_INFO_ONE_HANDED_RDS"/>
      </DriveByAnimInfos>
      <DriveByCamera/>
      <DriveByFlags>UseThreeAnimIntroOutro</DriveByFlags>
    </Item>
    <Item type="CVehicleDriveByInfo">
      <Name>DRIVEBY_HELI_SWIFT2_REAR_LEFT</Name>
      <MinAimSweepHeadingAngleDegs value="-180.000000"/>
      <MaxAimSweepHeadingAngleDegs value="0.000000"/>
      <FirstPersonMinAimSweepHeadingAngleDegs value="-180.000000"/>
      <FirstPersonMaxAimSweepHeadingAngleDegs value="180.000000"/>
      <FirstPersonUnarmedMinAimSweepHeadingAngleDegs value="-180.000000"/>
      <FirstPersonUnarmedMaxAimSweepHeadingAngleDegs value="180.000000"/>
      <MinRestrictedAimSweepHeadingAngleDegs value="-180.000000"/>
      <MaxRestrictedAimSweepHeadingAngleDegs value="0.000000"/>
      <MinSmashWindowAngleDegs value="0.000000"/>
      <MaxSmashWindowAngleDegs value="0.000000"/>
      <MinSmashWindowAngleFirstPersonDegs value="0.000000"/>
      <MaxSmashWindowAngleFirstPersonDegs value="0.000000"/>
      <MaxSpeedParam value="0.500000"/>
      <MaxLongitudinalLeanBlendWeightDelta value="0.250000"/>
      <MaxLateralLeanBlendWeightDelta value="0.250000"/>
      <ApproachSpeedToWithinMaxBlendDelta value="1.000000"/>
      <SpineAdditiveBlendInDelay value="0.050000"/>
      <SpineAdditiveBlendInDurationStill value="0.150000"/>
      <SpineAdditiveBlendInDuration value="0.750000"/>
      <SpineAdditiveBlendOutDelay value="0.000000"/>
      <SpineAdditiveBlendOutDuration value="0.500000"/>
      <MinUnarmedDrivebyYawIfWindowRolledUp value="-3.140000"/>
      <MaxUnarmedDrivebyYawIfWindowRolledUp value="3.140000"/>
      <DriveByAnimInfos>
        <Item ref="HELI_FROGGER_DB_ANIM_INFO_ONE_HANDED_RDS"/>
        <Item ref="HELI_FROGGER_DB_ANIM_INFO_TWO_HANDED_RDS"/>
      </DriveByAnimInfos>
      <DriveByCamera>FROGGER_REAR_LEFT_PASSENGER_AIM_CAMERA</DriveByCamera>
      <DriveByFlags>DampenRecoil NeedToOpenDoors UseThreeAnimIntroOutro</DriveByFlags>
    </Item>
    <Item type="CVehicleDriveByInfo">
      <Name>DRIVEBY_HELI_SWIFT2_REAR_RIGHT</Name>
      <MinAimSweepHeadingAngleDegs value="0.000000"/>
      <MaxAimSweepHeadingAngleDegs value="180.000000"/>
      <FirstPersonMinAimSweepHeadingAngleDegs value="-180.000000"/>
      <FirstPersonMaxAimSweepHeadingAngleDegs value="180.000000"/>
      <FirstPersonUnarmedMinAimSweepHeadingAngleDegs value="-180.000000"/>
      <FirstPersonUnarmedMaxAimSweepHeadingAngleDegs value="180.000000"/>
      <MinRestrictedAimSweepHeadingAngleDegs value="0.000000"/>
      <MaxRestrictedAimSweepHeadingAngleDegs value="180.000000"/>
      <MinSmashWindowAngleDegs value="0.000000"/>
      <MaxSmashWindowAngleDegs value="0.000000"/>
      <MinSmashWindowAngleFirstPersonDegs value="0.000000"/>
      <MaxSmashWindowAngleFirstPersonDegs value="0.000000"/>
      <MaxSpeedParam value="0.500000"/>
      <MaxLongitudinalLeanBlendWeightDelta value="0.250000"/>
      <MaxLateralLeanBlendWeightDelta value="0.250000"/>
      <ApproachSpeedToWithinMaxBlendDelta value="1.000000"/>
      <SpineAdditiveBlendInDelay value="0.050000"/>
      <SpineAdditiveBlendInDurationStill value="0.150000"/>
      <SpineAdditiveBlendInDuration value="0.750000"/>
      <SpineAdditiveBlendOutDelay value="0.000000"/>
      <SpineAdditiveBlendOutDuration value="0.500000"/>
      <MinUnarmedDrivebyYawIfWindowRolledUp value="-3.140000"/>
      <MaxUnarmedDrivebyYawIfWindowRolledUp value="3.140000"/>
      <DriveByAnimInfos>
        <Item ref="HELI_FROGGER_DB_ANIM_INFO_ONE_HANDED_RPS"/>
        <Item ref="HELI_FROGGER_DB_ANIM_INFO_TWO_HANDED_RPS"/>
      </DriveByAnimInfos>
      <DriveByCamera/>
      <DriveByFlags>DampenRecoil NeedToOpenDoors UseThreeAnimIntroOutro LeftHandedFirstPersonAnims LeftHandedUnarmedFirstPersonAnims</DriveByFlags>
    </Item>
    <Item type="CVehicleDriveByInfo">
      <Name>DRIVEBY_STD_KURUMA2_FRONT_LEFT</Name>
      <MinAimSweepHeadingAngleDegs value="-225.000000"/>
      <MaxAimSweepHeadingAngleDegs value="225.000000"/>
      <MinRestrictedAimSweepHeadingAngleDegs value="-100.000000"/>
      <MaxRestrictedAimSweepHeadingAngleDegs value="190.000000"/>
      <MinSmashWindowAngleDegs value="-100.000000"/>
      <MaxSmashWindowAngleDegs value="20.000000"/>
      <MaxSpeedParam value="0.500000"/>
      <MaxLongitudinalLeanBlendWeightDelta value="0.250000"/>
      <MaxLateralLeanBlendWeightDelta value="0.250000"/>
      <ApproachSpeedToWithinMaxBlendDelta value="1.000000"/>
      <SpineAdditiveBlendInDelay value="0.050000"/>
      <SpineAdditiveBlendInDurationStill value="0.150000"/>
      <SpineAdditiveBlendInDuration value="0.750000"/>
      <SpineAdditiveBlendOutDelay value="0.000000"/>
      <SpineAdditiveBlendOutDuration value="0.500000"/>
      <DriveByAnimInfos>
        <Item ref="ZTYPE_DB_ANIM_INFO_ONE_HANDED_DS"/>
      </DriveByAnimInfos>
      <DriveByCamera/>
      <DriveByFlags>UseMountedProjectileTask LeftHandedProjctiles</DriveByFlags>
    </Item>
    <Item type="CVehicleDriveByInfo">
      <Name>DRIVEBY_STD_KURUMA2_FRONT_RIGHT</Name>
      <MinAimSweepHeadingAngleDegs value="-225.000000"/>
      <MaxAimSweepHeadingAngleDegs value="225.000000"/>
      <MinRestrictedAimSweepHeadingAngleDegs value="-190.000000"/>
      <MaxRestrictedAimSweepHeadingAngleDegs value="100.000000"/>
      <MinSmashWindowAngleDegs value="-20.000000"/>
      <MaxSmashWindowAngleDegs value="100.000000"/>
      <MaxSpeedParam value="0.500000"/>
      <MaxLongitudinalLeanBlendWeightDelta value="0.250000"/>
      <MaxLateralLeanBlendWeightDelta value="0.250000"/>
      <ApproachSpeedToWithinMaxBlendDelta value="1.000000"/>
      <SpineAdditiveBlendInDelay value="0.050000"/>
      <SpineAdditiveBlendInDurationStill value="0.150000"/>
      <SpineAdditiveBlendInDuration value="0.750000"/>
      <SpineAdditiveBlendOutDelay value="0.000000"/>
      <SpineAdditiveBlendOutDuration value="0.500000"/>
      <DriveByAnimInfos>
        <Item ref="ZTYPE_DB_ANIM_INFO_ONE_HANDED_PS"/>
      </DriveByAnimInfos>
      <DriveByCamera/>
      <DriveByFlags>UseMountedProjectileTask LeftHandedFirstPersonAnims LeftHandedUnarmedFirstPersonAnims</DriveByFlags>
    </Item>
    <Item type="CVehicleDriveByInfo">
      <Name>DRIVEBY_STD_KURUMA2_REAR_LEFT</Name>
      <MinAimSweepHeadingAngleDegs value="-225.000000"/>
      <MaxAimSweepHeadingAngleDegs value="225.000000"/>
      <MinRestrictedAimSweepHeadingAngleDegs value="-225.000000"/>
      <MaxRestrictedAimSweepHeadingAngleDegs value="225.000000"/>
      <MinSmashWindowAngleDegs value="-180.000000"/>
      <MaxSmashWindowAngleDegs value="0.000000"/>
      <MaxSpeedParam value="0.500000"/>
      <MaxLongitudinalLeanBlendWeightDelta value="0.250000"/>
      <MaxLateralLeanBlendWeightDelta value="0.250000"/>
      <ApproachSpeedToWithinMaxBlendDelta value="1.000000"/>
      <SpineAdditiveBlendInDelay value="0.050000"/>
      <SpineAdditiveBlendInDurationStill value="0.150000"/>
      <SpineAdditiveBlendInDuration value="0.750000"/>
      <SpineAdditiveBlendOutDelay value="0.000000"/>
      <SpineAdditiveBlendOutDuration value="0.500000"/>
      <DriveByAnimInfos>
        <Item ref="STD_DB_ANIM_INFO_ONE_HANDED_RDS"/>
      </DriveByAnimInfos>
      <DriveByCamera/>
      <DriveByFlags>UseMountedProjectileTask LeftHandedProjctiles</DriveByFlags>
    </Item>
    <Item type="CVehicleDriveByInfo">
      <Name>DRIVEBY_STD_KURUMA2_REAR_RIGHT</Name>
      <MinAimSweepHeadingAngleDegs value="-225.000000"/>
      <MaxAimSweepHeadingAngleDegs value="225.000000"/>
      <MinRestrictedAimSweepHeadingAngleDegs value="-225.000000"/>
      <MaxRestrictedAimSweepHeadingAngleDegs value="225.000000"/>
      <MinSmashWindowAngleDegs value="0.000000"/>
      <MaxSmashWindowAngleDegs value="180.000000"/>
      <MaxSpeedParam value="0.500000"/>
      <MaxLongitudinalLeanBlendWeightDelta value="0.250000"/>
      <MaxLateralLeanBlendWeightDelta value="0.250000"/>
      <ApproachSpeedToWithinMaxBlendDelta value="1.000000"/>
      <SpineAdditiveBlendInDelay value="0.050000"/>
      <SpineAdditiveBlendInDurationStill value="0.150000"/>
      <SpineAdditiveBlendInDuration value="0.750000"/>
      <SpineAdditiveBlendOutDelay value="0.000000"/>
      <SpineAdditiveBlendOutDuration value="0.500000"/>
      <DriveByAnimInfos>
        <Item ref="STD_DB_ANIM_INFO_ONE_HANDED_RPS"/>
      </DriveByAnimInfos>
      <DriveByCamera/>
      <DriveByFlags>UseMountedProjectileTask LeftHandedFirstPersonAnims LeftHandedUnarmedFirstPersonAnims</DriveByFlags>
    </Item>
  </VehicleDriveByInfos>
  <VehicleSeatInfos>
    <Item type="CVehicleSeatInfo">
      <Name>SEAT_HELI_SWIFT2_REAR_LEFT</Name>
      <SeatBoneName>seat_dside_r</SeatBoneName>
      <ShuffleLink>SEAT_STANDARD_REAR_RIGHT</ShuffleLink>
      <RearSeatLink/>
      <DefaultCarTask>TASK_DRIVE_WANDER</DefaultCarTask>
      <SeatFlags>IsIdleSeat IsRearSeatWithActivities</SeatFlags>
      <HairScale value="-0.275000"/>
      <ShuffleLink2/>
    </Item>
    <Item type="CVehicleSeatInfo">
      <Name>SEAT_HELI_SWIFT2_REAR_RIGHT</Name>
      <SeatBoneName>seat_pside_r</SeatBoneName>
      <ShuffleLink>SEAT_STANDARD_REAR_LEFT</ShuffleLink>
      <RearSeatLink/>
      <DefaultCarTask>TASK_DRIVE_WANDER</DefaultCarTask>
      <SeatFlags>IsIdleSeat IsRearSeatWithActivities</SeatFlags>
      <HairScale value="-0.275000"/>
      <ShuffleLink2/>
    </Item>
    <Item type="CVehicleSeatInfo">
      <Name>SEAT_PLANE_LUXOR2_NO_SHUFFLE_RIGHT_1</Name>
      <SeatBoneName>seat_pside_r1</SeatBoneName>
      <ShuffleLink/>
      <RearSeatLink/>
      <DefaultCarTask>TASK_DRIVE_WANDER</DefaultCarTask>
      <SeatFlags>IsRearSeatWithActivities</SeatFlags>
      <HairScale value="0.000000"/>
      <ShuffleLink2/>
    </Item>
    <Item type="CVehicleSeatInfo">
      <Name>SEAT_PLANE_LUXOR2_NO_SHUFFLE_REAR_RIGHT</Name>
      <SeatBoneName>seat_pside_r</SeatBoneName>
      <ShuffleLink/>
      <RearSeatLink/>
      <DefaultCarTask>TASK_DRIVE_WANDER</DefaultCarTask>
      <SeatFlags>IsRearSeatWithActivities</SeatFlags>
      <HairScale value="0.000000"/>
      <ShuffleLink2/>
    </Item>
    <Item type="CVehicleSeatInfo">
      <Name>SEAT_BOAT_REAR_LEFT</Name>
      <SeatBoneName>seat_dside_r</SeatBoneName>
      <ShuffleLink>SEAT_BOAT_REAR_RIGHT</ShuffleLink>
      <RearSeatLink/>
      <DefaultCarTask>TASK_DRIVE_WANDER</DefaultCarTask>
      <SeatFlags>IsIdleSeat</SeatFlags>
      <HairScale value="0.000000"/>
    </Item>
    <Item type="CVehicleSeatInfo">
      <Name>SEAT_BOAT_REAR_RIGHT</Name>
      <SeatBoneName>seat_pside_r</SeatBoneName>
      <ShuffleLink>SEAT_BOAT_REAR_LEFT</ShuffleLink>
      <RearSeatLink/>
      <DefaultCarTask>TASK_DRIVE_WANDER</DefaultCarTask>
      <SeatFlags>IsIdleSeat</SeatFlags>
      <HairScale value="0.000000"/>
    </Item>
    <Item type="CVehicleSeatInfo">
      <Name>SEAT_STD_TECHNICAL_REAR_TURRET</Name>
      <SeatBoneName>seat_dside_r</SeatBoneName>
      <ShuffleLink/>
      <RearSeatLink/>
      <DefaultCarTask>TASK_DRIVE_WANDER</DefaultCarTask>
      <SeatFlags>KeepOnHeadProp PedInSeatTargetable</SeatFlags>
      <HairScale value="0.000000"/>
      <ShuffleLink2/>
    </Item>
  </VehicleSeatInfos>
  <VehicleSeatAnimInfos>
    <Item type="CVehicleSeatAnimInfo">
      <Name>SEAT_ANIM_STD_BRIOSO_FRONT_LEFT</Name>
      <DriveByInfo ref="DRIVEBY_STD_ZTYPE_FRONT_LEFT"/>
      <InsideClipSetMap ref="INSIDE_CLIPSET_MAP_STD_BRIOSO_FRONT_LEFT"/>
      <PanicClipSet>anim@veh@std@panto@ds@idle_panic</PanicClipSet>
      <AgitatedClipSet>clipset@veh@std@ds@idle_agitated</AgitatedClipSet>
      <DuckedClipSet>clipset@veh@std@ds@idle_duck</DuckedClipSet>
      <FemaleClipSet/>
      <LowriderLeanClipSet/>
      <AltLowriderLeanClipSet/>
      <LowLODIdleAnim>STD_CAR_FRONT_DS_IDLE</LowLODIdleAnim>
      <SeatAmbientContext/>
      <InVehicleMoveNetwork>VEHICLE_DEFAULT</InVehicleMoveNetwork>
      <SeatAnimFlags>UseStandardInVehicleAnims UseCloseDoorBlendAnims</SeatAnimFlags>
      <SteeringSmoothing value="0.040000"/>
      <ExitToAimInfoName>STANDARD</ExitToAimInfoName>
      <MaleGestureClipSetId>ANIM_GROUP_GESTURE_M_CAR_STD_DS</MaleGestureClipSetId>
      <FemaleGestureClipSetId>ANIM_GROUP_GESTURE_F_CAR_STD_DS</FemaleGestureClipSetId>
      <FPSMinSteeringRateOverride value="-1.000000"/>
      <FPSMaxSteeringRateOverride value="-1.000000"/>
      <SeatCollisionBoundsOffset x="0.000000" y="0.000000" z="0.000000"/>
    </Item>
    <Item type="CVehicleSeatAnimInfo">
      <Name>SEAT_ANIM_STD_OMNIS_FRONT_LEFT</Name>
      <DriveByInfo ref="DRIVEBY_STANDARD_FRONT_LEFT"/>
      <InsideClipSetMap ref="INSIDE_CLIPSET_MAP_STD_FRONT_LEFT"/>
      <PanicClipSet>clipset@veh@std@ds@idle_panic</PanicClipSet>
      <AgitatedClipSet>clipset@veh@std@ds@idle_agitated</AgitatedClipSet>
      <DuckedClipSet>clipset@veh@std@ds@idle_duck</DuckedClipSet>
      <FemaleClipSet/>
      <LowriderLeanClipSet/>
      <AltLowriderLeanClipSet/>
      <LowLODIdleAnim>STD_CAR_FRONT_DS_IDLE</LowLODIdleAnim>
      <SeatAmbientContext/>
      <InVehicleMoveNetwork>VEHICLE_DEFAULT</InVehicleMoveNetwork>
      <SeatAnimFlags>UseStandardInVehicleAnims UseCloseDoorBlendAnims</SeatAnimFlags>
      <SteeringSmoothing value="0.040000"/>
      <ExitToAimInfoName>STANDARD</ExitToAimInfoName>
      <MaleGestureClipSetId>ANIM_GROUP_GESTURE_M_CAR_STD_DS</MaleGestureClipSetId>
      <FemaleGestureClipSetId>ANIM_GROUP_GESTURE_F_CAR_STD_DS</FemaleGestureClipSetId>
      <FPSMinSteeringRateOverride value="-1.000000"/>
      <FPSMaxSteeringRateOverride value="-1.000000"/>
      <SeatCollisionBoundsOffset x="0.000000" y="0.000000" z="0.000000"/>
    </Item>
    <Item type="CVehicleSeatAnimInfo">
      <Name>SEAT_ANIM_STD_OMNIS_FRONT_RIGHT</Name>
      <DriveByInfo ref="DRIVEBY_STANDARD_FRONT_RIGHT"/>
      <InsideClipSetMap ref="INSIDE_CLIPSET_MAP_STD_FRONT_RIGHT"/>
      <PanicClipSet>clipset@veh@std@ps@idle_panic</PanicClipSet>
      <AgitatedClipSet/>
      <DuckedClipSet>clipset@veh@std@ps@idle_duck</DuckedClipSet>
      <FemaleClipSet/>
      <LowriderLeanClipSet/>
      <AltLowriderLeanClipSet/>
      <LowLODIdleAnim>STD_CAR_FRONT_PS_IDLE</LowLODIdleAnim>
      <SeatAmbientContext/>
      <InVehicleMoveNetwork>VEHICLE_DEFAULT</InVehicleMoveNetwork>
      <SeatAnimFlags>UseStandardInVehicleAnims HasPanicAnims UseCloseDoorBlendAnims PreventShuffleJack</SeatAnimFlags>
      <SteeringSmoothing value="0.100000"/>
      <ExitToAimInfoName>STANDARD</ExitToAimInfoName>
      <MaleGestureClipSetId>ANIM_GROUP_GESTURE_M_CAR_STD_PS</MaleGestureClipSetId>
      <FemaleGestureClipSetId>ANIM_GROUP_GESTURE_F_CAR_STD_PS</FemaleGestureClipSetId>
      <FPSMinSteeringRateOverride value="-1.000000"/>
      <FPSMaxSteeringRateOverride value="-1.000000"/>
      <SeatCollisionBoundsOffset x="0.000000" y="0.000000" z="0.000000"/>
    </Item>
    <Item type="CVehicleSeatAnimInfo">
      <Name>SEAT_ANIM_HELI_SWIFT2_REAR_LEFT</Name>
      <DriveByInfo ref="DRIVEBY_HELI_SWIFT2_REAR_LEFT"/>
      <InsideClipSetMap ref="INSIDE_CLIPSET_MAP_HELI_SWIFT2_REAR_LEFT"/>
      <PanicClipSet/>
      <AgitatedClipSet/>
      <DuckedClipSet/>
      <FemaleClipSet/>
      <LowLODIdleAnim>STD_CAR_FRONT_PS_IDLE</LowLODIdleAnim>
      <SeatAmbientContext/>
      <InVehicleMoveNetwork>VEHICLE_DEFAULT</InVehicleMoveNetwork>
      <SeatAnimFlags>UseStandardInVehicleAnims PreventShuffleJack FallsOutWhenDead</SeatAnimFlags>
      <SteeringSmoothing value="0.100000"/>
      <ExitToAimInfoName/>
      <MaleGestureClipSetId/>
      <FemaleGestureClipSetId/>
      <FPSMinSteeringRateOverride value="-1.000000"/>
      <FPSMaxSteeringRateOverride value="-1.000000"/>
      <SeatCollisionBoundsOffset x="0.000000" y="0.000000" z="0.000000"/>
    </Item>
    <Item type="CVehicleSeatAnimInfo">
      <Name>SEAT_ANIM_HELI_SWIFT2_REAR_RIGHT</Name>
      <DriveByInfo ref="DRIVEBY_HELI_SWIFT2_REAR_RIGHT"/>
      <InsideClipSetMap ref="INSIDE_CLIPSET_MAP_HELI_SWIFT2_REAR_RIGHT"/>
      <PanicClipSet/>
      <AgitatedClipSet/>
      <DuckedClipSet/>
      <FemaleClipSet/>
      <LowLODIdleAnim>STD_CAR_FRONT_PS_IDLE</LowLODIdleAnim>
      <SeatAmbientContext/>
      <InVehicleMoveNetwork>VEHICLE_DEFAULT</InVehicleMoveNetwork>
      <SeatAnimFlags>UseStandardInVehicleAnims PreventShuffleJack FallsOutWhenDead</SeatAnimFlags>
      <SteeringSmoothing value="0.100000"/>
      <ExitToAimInfoName/>
      <MaleGestureClipSetId/>
      <FemaleGestureClipSetId/>
      <FPSMinSteeringRateOverride value="-1.000000"/>
      <FPSMaxSteeringRateOverride value="-1.000000"/>
      <SeatCollisionBoundsOffset x="0.000000" y="0.000000" z="0.000000"/>
    </Item>
    <Item type="CVehicleSeatAnimInfo">
      <Name>SEAT_ANIM_PLANE_LUXOR2_REAR_LEFT</Name>
      <DriveByInfo ref="DRIVEBY_LUXOR2_REAR_LEFT"/>
      <InsideClipSetMap ref="INSIDE_CLIPSET_MAP_PLANE_JET_FRONT_RIGHT"/>
      <PanicClipSet/>
      <AgitatedClipSet/>
      <DuckedClipSet/>
      <FemaleClipSet/>
      <LowLODIdleAnim>HELI_FRONT_PS_IDLE</LowLODIdleAnim>
      <SeatAmbientContext/>
      <InVehicleMoveNetwork>VEHICLE_DEFAULT</InVehicleMoveNetwork>
      <SeatAnimFlags>CannotBeJacked UseStandardInVehicleAnims DisableAbnormalExits</SeatAnimFlags>
      <SteeringSmoothing value="0.100000"/>
      <ExitToAimInfoName/>
      <MaleGestureClipSetId/>
      <FemaleGestureClipSetId/>
      <FPSMinSteeringRateOverride value="-1.000000"/>
      <FPSMaxSteeringRateOverride value="-1.000000"/>
      <SeatCollisionBoundsOffset x="0.000000" y="0.000000" z="0.000000"/>
    </Item>
    <Item type="CVehicleSeatAnimInfo">
      <Name>SEAT_ANIM_PLANE_LUXOR2_REAR_RIGHT</Name>
      <DriveByInfo ref="DRIVEBY_LUXOR2_REAR_RIGHT"/>
      <InsideClipSetMap ref="INSIDE_CLIPSET_MAP_PLANE_JET_FRONT_RIGHT"/>
      <PanicClipSet/>
      <AgitatedClipSet/>
      <DuckedClipSet/>
      <FemaleClipSet/>
      <LowLODIdleAnim>HELI_FRONT_PS_IDLE</LowLODIdleAnim>
      <SeatAmbientContext/>
      <InVehicleMoveNetwork>VEHICLE_DEFAULT</InVehicleMoveNetwork>
      <SeatAnimFlags>WeaponAttachedToLeftHand CannotBeJacked UseStandardInVehicleAnims DisableAbnormalExits</SeatAnimFlags>
      <SteeringSmoothing value="0.100000"/>
      <ExitToAimInfoName/>
      <MaleGestureClipSetId/>
      <FemaleGestureClipSetId/>
      <FPSMinSteeringRateOverride value="-1.000000"/>
      <FPSMaxSteeringRateOverride value="-1.000000"/>
      <SeatCollisionBoundsOffset x="0.000000" y="0.000000" z="0.000000"/>
    </Item>
    <Item type="CVehicleSeatAnimInfo">
      <Name>SEAT_ANIM_HELI_SAVAGE_FRONT</Name>
      <DriveByInfo ref="DRIVEBY_HELI_FRONT_LEFT"/>
      <InsideClipSetMap ref="INSIDE_CLIPSET_MAP_HELI_SAVAGE_FRONT"/>
      <PanicClipSet/>
      <AgitatedClipSet/>
      <DuckedClipSet/>
      <LowLODIdleAnim>HELI_FRONT_DS_IDLE</LowLODIdleAnim>
      <SeatAmbientContext/>
      <InVehicleMoveNetwork>VEHICLE_DEFAULT</InVehicleMoveNetwork>
      <SeatAnimFlags>UseStandardInVehicleAnims CanWarpToDriverSeatIfNoDriver</SeatAnimFlags>
      <SteeringSmoothing value="0.100000"/>
      <ExitToAimInfoName/>
      <MaleGestureClipSetId/>
      <FemaleGestureClipSetId/>
    </Item>
    <Item type="CVehicleSeatAnimInfo">
      <Name>SEAT_ANIM_PLANE_VELUM2_REAR_LEFT</Name>
      <DriveByInfo ref="NULL"/>
      <InsideClipSetMap ref="INSIDE_CLIPSET_MAP_HELI_FROGGER_REAR_LEFT"/>
      <PanicClipSet/>
      <AgitatedClipSet/>
      <LowLODIdleAnim>HELI_FRONT_PS_IDLE</LowLODIdleAnim>
      <SeatAmbientContext/>
      <InVehicleMoveNetwork>VEHICLE_DEFAULT</InVehicleMoveNetwork>
      <SeatAnimFlags>CannotBeJacked UseStandardInVehicleAnims PreventShuffleJack CanWarpToDriverSeatIfNoDriver</SeatAnimFlags>
      <SteeringSmoothing value="0.100000"/>
      <ExitToAimInfoName/>
      <MaleGestureClipSetId/>
      <FemaleGestureClipSetId/>
    </Item>
    <Item type="CVehicleSeatAnimInfo">
      <Name>SEAT_ANIM_PLANE_VELUM2_REAR_RIGHT</Name>
      <DriveByInfo ref="NULL"/>
      <InsideClipSetMap ref="INSIDE_CLIPSET_MAP_HELI_VELUM2_REAR_RIGHT"/>
      <PanicClipSet/>
      <AgitatedClipSet/>
      <LowLODIdleAnim>HELI_FRONT_PS_IDLE</LowLODIdleAnim>
      <SeatAmbientContext/>
      <InVehicleMoveNetwork>VEHICLE_DEFAULT</InVehicleMoveNetwork>
      <SeatAnimFlags>CannotBeJacked UseStandardInVehicleAnims PreventShuffleJack CanWarpToDriverSeatIfNoDriver</SeatAnimFlags>
      <SteeringSmoothing value="0.100000"/>
      <ExitToAimInfoName/>
      <MaleGestureClipSetId/>
      <FemaleGestureClipSetId/>
    </Item>
    <Item type="CVehicleSeatAnimInfo">
      <Name>SEAT_ANIM_STD_KURUMA2_FRONT_LEFT</Name>
      <DriveByInfo ref="DRIVEBY_STD_KURUMA2_FRONT_LEFT"/>
      <InsideClipSetMap ref="INSIDE_CLIPSET_MAP_STD_FRONT_LEFT"/>
      <PanicClipSet>clipset@veh@std@ds@idle_panic</PanicClipSet>
      <AgitatedClipSet>clipset@veh@std@ds@idle_agitated</AgitatedClipSet>
      <DuckedClipSet>clipset@veh@std@ds@idle_duck</DuckedClipSet>
      <LowLODIdleAnim>STD_CAR_FRONT_DS_IDLE</LowLODIdleAnim>
      <SeatAmbientContext>IN_CAR_STANDARD</SeatAmbientContext>
      <InVehicleMoveNetwork>VEHICLE_DEFAULT</InVehicleMoveNetwork>
      <SeatAnimFlags>UseStandardInVehicleAnims UseCloseDoorBlendAnims</SeatAnimFlags>
      <SteeringSmoothing value="0.040000"/>
      <ExitToAimInfoName>STANDARD</ExitToAimInfoName>
      <MaleGestureClipSetId>ANIM_GROUP_GESTURE_M_CAR_STD_DS</MaleGestureClipSetId>
      <FemaleGestureClipSetId>ANIM_GROUP_GESTURE_F_CAR_STD_DS</FemaleGestureClipSetId>
    </Item>
    <Item type="CVehicleSeatAnimInfo">
      <Name>SEAT_ANIM_STD_KURUMA2_FRONT_RIGHT</Name>
      <DriveByInfo ref="DRIVEBY_STD_KURUMA2_FRONT_RIGHT"/>
      <InsideClipSetMap ref="INSIDE_CLIPSET_MAP_STD_FRONT_RIGHT"/>
      <PanicClipSet>clipset@veh@std@ps@idle_panic</PanicClipSet>
      <AgitatedClipSet/>
      <DuckedClipSet>clipset@veh@std@ps@idle_duck</DuckedClipSet>
      <LowLODIdleAnim>STD_CAR_FRONT_PS_IDLE</LowLODIdleAnim>
      <SeatAmbientContext>IN_CAR_STANDARD</SeatAmbientContext>
      <InVehicleMoveNetwork>VEHICLE_DEFAULT</InVehicleMoveNetwork>
      <SeatAnimFlags>WeaponAttachedToLeftHand UseStandardInVehicleAnims HasPanicAnims UseCloseDoorBlendAnims PreventShuffleJack</SeatAnimFlags>
      <SteeringSmoothing value="0.100000"/>
      <ExitToAimInfoName>STANDARD</ExitToAimInfoName>
      <MaleGestureClipSetId>ANIM_GROUP_GESTURE_M_CAR_STD_PS</MaleGestureClipSetId>
      <FemaleGestureClipSetId>ANIM_GROUP_GESTURE_F_CAR_STD_PS</FemaleGestureClipSetId>
    </Item>
    <Item type="CVehicleSeatAnimInfo">
      <Name>SEAT_ANIM_STD_KURUMA2_REAR_LEFT</Name>
      <DriveByInfo ref="DRIVEBY_STD_KURUMA2_REAR_LEFT"/>
      <InsideClipSetMap ref="INSIDE_CLIPSET_MAP_STD_REAR_LEFT"/>
      <PanicClipSet>clipset@veh@std@rds@idle_panic</PanicClipSet>
      <AgitatedClipSet/>
      <DuckedClipSet>clipset@veh@std@rds@idle_duck</DuckedClipSet>
      <LowLODIdleAnim>STD_CAR_REAR_DS_IDLE</LowLODIdleAnim>
      <SeatAmbientContext>IN_CAR_STANDARD</SeatAmbientContext>
      <InVehicleMoveNetwork>VEHICLE_DEFAULT</InVehicleMoveNetwork>
      <SeatAnimFlags>UseStandardInVehicleAnims HasPanicAnims UseCloseDoorBlendAnims</SeatAnimFlags>
      <SteeringSmoothing value="0.100000"/>
      <ExitToAimInfoName>STANDARD</ExitToAimInfoName>
      <MaleGestureClipSetId>ANIM_GROUP_GESTURE_M_CAR_STD_PS</MaleGestureClipSetId>
      <FemaleGestureClipSetId>ANIM_GROUP_GESTURE_F_CAR_STD_PS</FemaleGestureClipSetId>
    </Item>
    <Item type="CVehicleSeatAnimInfo">
      <Name>SEAT_ANIM_STD_KURUMA2_REAR_RIGHT</Name>
      <DriveByInfo ref="DRIVEBY_STD_KURUMA2_REAR_RIGHT"/>
      <InsideClipSetMap ref="INSIDE_CLIPSET_MAP_STD_REAR_RIGHT"/>
      <PanicClipSet>clipset@veh@std@rps@idle_panic</PanicClipSet>
      <AgitatedClipSet/>
      <DuckedClipSet>clipset@veh@std@rps@idle_duck</DuckedClipSet>
      <LowLODIdleAnim>STD_CAR_REAR_PS_IDLE</LowLODIdleAnim>
      <SeatAmbientContext>IN_CAR_STANDARD</SeatAmbientContext>
      <InVehicleMoveNetwork>VEHICLE_DEFAULT</InVehicleMoveNetwork>
      <SeatAnimFlags>WeaponAttachedToLeftHand UseStandardInVehicleAnims HasPanicAnims UseCloseDoorBlendAnims PreventShuffleJack</SeatAnimFlags>
      <SteeringSmoothing value="0.100000"/>
      <ExitToAimInfoName>STANDARD</ExitToAimInfoName>
      <MaleGestureClipSetId>ANIM_GROUP_GESTURE_M_CAR_STD_PS</MaleGestureClipSetId>
      <FemaleGestureClipSetId>ANIM_GROUP_GESTURE_F_CAR_STD_PS</FemaleGestureClipSetId>
    </Item>
    <Item type="CVehicleSeatAnimInfo">
      <Name>SEAT_ANIM_STD_TECHNICAL_REAR_TURRET</Name>
      <DriveByInfo ref="NULL"/>
      <InsideClipSetMap ref="INSIDE_CLIPSET_MAP_STD_TECHNICAL_TURRET"/>
      <PanicClipSet/>
      <AgitatedClipSet/>
      <DuckedClipSet/>
      <LowLODIdleAnim>TECHNICAL_TURRET_IDLE</LowLODIdleAnim>
      <SeatAmbientContext/>
      <InVehicleMoveNetwork>VEHICLE_TURRET_STANDING</InVehicleMoveNetwork>
      <SeatAnimFlags>UseStandardInVehicleAnims RagdollWhenVehicleUpsideDown CanDetachViaRagdoll UseTorsoLeanIK</SeatAnimFlags>
      <SteeringSmoothing value="0.040000"/>
      <ExitToAimInfoName/>
      <MaleGestureClipSetId/>
      <FemaleGestureClipSetId/>
    </Item>
    <Item type="CVehicleSeatAnimInfo">
      <Name>SEAT_ANIM_VAN_SLAMVAN_FRONT_LEFT</Name>
      <DriveByInfo ref="DRIVEBY_VAN_FRONT_LEFT"/>
      <InsideClipSetMap ref="INSIDE_CLIPSET_MAP_STD_FRONT_LEFT"/>
      <PanicClipSet>clipset@veh@std@ds@idle_panic</PanicClipSet>
      <AgitatedClipSet>clipset@veh@std@ds@idle_agitated</AgitatedClipSet>
      <DuckedClipSet>clipset@veh@std@ds@idle_duck</DuckedClipSet>
      <LowLODIdleAnim>STD_CAR_FRONT_DS_IDLE</LowLODIdleAnim>
      <SeatAmbientContext>IN_CAR_STANDARD</SeatAmbientContext>
      <InVehicleMoveNetwork>VEHICLE_DEFAULT</InVehicleMoveNetwork>
      <SeatAnimFlags>UseStandardInVehicleAnims UseCloseDoorBlendAnims</SeatAnimFlags>
      <SteeringSmoothing value="0.040000"/>
      <ExitToAimInfoName>STANDARD</ExitToAimInfoName>
      <MaleGestureClipSetId>ANIM_GROUP_GESTURE_M_CAR_STD_DS</MaleGestureClipSetId>
      <FemaleGestureClipSetId>ANIM_GROUP_GESTURE_F_CAR_STD_DS</FemaleGestureClipSetId>
    </Item>
    <Item type="CVehicleSeatAnimInfo">
      <Name>SEAT_ANIM_VAN_SLAMVAN_FRONT_RIGHT</Name>
      <DriveByInfo ref="DRIVEBY_VAN_FRONT_RIGHT"/>
      <InsideClipSetMap ref="INSIDE_CLIPSET_MAP_STD_FRONT_RIGHT"/>
      <PanicClipSet>clipset@veh@std@ps@idle_panic</PanicClipSet>
      <AgitatedClipSet/>
      <DuckedClipSet>clipset@veh@std@ps@idle_duck</DuckedClipSet>
      <LowLODIdleAnim>STD_CAR_FRONT_PS_IDLE</LowLODIdleAnim>
      <SeatAmbientContext>IN_CAR_STANDARD</SeatAmbientContext>
      <InVehicleMoveNetwork>VEHICLE_DEFAULT</InVehicleMoveNetwork>
      <SeatAnimFlags>WeaponAttachedToLeftHand UseStandardInVehicleAnims HasPanicAnims UseCloseDoorBlendAnims PreventShuffleJack</SeatAnimFlags>
      <SteeringSmoothing value="0.100000"/>
      <ExitToAimInfoName>STANDARD</ExitToAimInfoName>
      <MaleGestureClipSetId>ANIM_GROUP_GESTURE_M_CAR_STD_PS</MaleGestureClipSetId>
      <FemaleGestureClipSetId>ANIM_GROUP_GESTURE_F_CAR_STD_PS</FemaleGestureClipSetId>
    </Item>
    <Item type="CVehicleSeatAnimInfo">
      <Name>SEAT_ANIM_VAN_SLAMVAN_REAR_LEFT</Name>
      <DriveByInfo ref="DRIVEBY_VAN_REAR_LEFT"/>
      <InsideClipSetMap ref="INSIDE_CLIPSET_MAP_VAN_REAR_LEFT"/>
      <PanicClipSet/>
      <AgitatedClipSet/>
      <LowLODIdleAnim>VAN_REAR_DS_IDLE</LowLODIdleAnim>
      <SeatAmbientContext/>
      <InVehicleMoveNetwork>VEHICLE_DEFAULT</InVehicleMoveNetwork>
      <SeatAnimFlags>UseStandardInVehicleAnims PreventShuffleJack UseCloseDoorBlendAnims</SeatAnimFlags>
      <SteeringSmoothing value="0.100000"/>
      <ExitToAimInfoName/>
      <MaleGestureClipSetId/>
      <FemaleGestureClipSetId/>
    </Item>
    <Item type="CVehicleSeatAnimInfo">
      <Name>SEAT_ANIM_VAN_SLAMVAN_REAR_RIGHT</Name>
      <DriveByInfo ref="DRIVEBY_VAN_REAR_RIGHT"/>
      <InsideClipSetMap ref="INSIDE_CLIPSET_MAP_VAN_REAR_RIGHT"/>
      <PanicClipSet/>
      <AgitatedClipSet/>
      <LowLODIdleAnim>VAN_REAR_PS_IDLE</LowLODIdleAnim>
      <SeatAmbientContext/>
      <InVehicleMoveNetwork>VEHICLE_DEFAULT</InVehicleMoveNetwork>
      <SeatAnimFlags>UseStandardInVehicleAnims PreventShuffleJack UseCloseDoorBlendAnims</SeatAnimFlags>
      <SteeringSmoothing value="0.100000"/>
      <ExitToAimInfoName/>
      <MaleGestureClipSetId/>
      <FemaleGestureClipSetId/>
    </Item>
  </VehicleSeatAnimInfos>
  <VehicleEntryPointInfos>
    <Item type="CVehicleEntryPointInfo">
      <Name>ENTRY_POINT_HELI_SWIFT2_REAR_LEFT</Name>
      <DoorBoneName>door_dside_r</DoorBoneName>
      <SecondDoorBoneName/>
      <DoorHandleBoneName>handle_dside_r</DoorHandleBoneName>
      <WindowId>REAR_LEFT</WindowId>
      <VehicleSide>SIDE_LEFT</VehicleSide>
      <AccessableSeats>
        <Item ref="SEAT_HELI_SWIFT2_REAR_LEFT"/>
      </AccessableSeats>
      <VehicleExtraPointsInfo ref="EXTRA_VEHICLE_POINTS_STD_LEFT"/>
      <Flags>BlockJackReactionUntilJackerIsReady</Flags>
      <BlockJackReactionSides>
        <Item>SIDE_LEFT</Item>
      </BlockJackReactionSides>
      <BreakoutTestPoint>POINT_TYPE_MAX</BreakoutTestPoint>
    </Item>
    <Item type="CVehicleEntryPointInfo">
      <Name>ENTRY_POINT_HELI_SWIFT2_REAR_RIGHT</Name>
      <DoorBoneName>door_pside_r</DoorBoneName>
      <SecondDoorBoneName/>
      <DoorHandleBoneName>handle_pside_r</DoorHandleBoneName>
      <WindowId>REAR_RIGHT</WindowId>
      <VehicleSide>SIDE_RIGHT</VehicleSide>
      <AccessableSeats>
        <Item ref="SEAT_HELI_SWIFT2_REAR_RIGHT"/>
      </AccessableSeats>
      <VehicleExtraPointsInfo ref="EXTRA_VEHICLE_POINTS_STD_RIGHT"/>
      <Flags>BlockJackReactionUntilJackerIsReady</Flags>
      <BlockJackReactionSides>
        <Item>SIDE_RIGHT</Item>
      </BlockJackReactionSides>
      <BreakoutTestPoint>POINT_TYPE_MAX</BreakoutTestPoint>
    </Item>
    <Item type="CVehicleEntryPointInfo">
      <Name>ENTRY_POINT_PLANE_LUXOR2_REAR_LEFT</Name>
      <DoorBoneName>door_dside_f</DoorBoneName>
      <SecondDoorBoneName>door_pside_f</SecondDoorBoneName>
      <DoorHandleBoneName/>
      <WindowId>INVALID</WindowId>
      <VehicleSide>SIDE_LEFT</VehicleSide>
      <AccessableSeats>
        <Item ref="SEAT_STANDARD_NO_SHUFFLE_REAR_LEFT"/>
      </AccessableSeats>
      <VehicleExtraPointsInfo ref="EXTRA_VEHICLE_POINTS_PLANE_LUXOR2_REAR"/>
      <Flags>IgnoreSmashWindowCheck</Flags>
      <BlockJackReactionSides/>
      <BreakoutTestPoint>POINT_TYPE_MAX</BreakoutTestPoint>
    </Item>
    <Item type="CVehicleEntryPointInfo">
      <Name>ENTRY_POINT_PLANE_LUXOR2_REAR_RIGHT</Name>
      <DoorBoneName>door_dside_f</DoorBoneName>
      <SecondDoorBoneName>door_pside_f</SecondDoorBoneName>
      <DoorHandleBoneName/>
      <WindowId>INVALID</WindowId>
      <VehicleSide>SIDE_LEFT</VehicleSide>
      <AccessableSeats>
        <Item ref="SEAT_PLANE_LUXOR2_NO_SHUFFLE_RIGHT_1"/>
      </AccessableSeats>
      <VehicleExtraPointsInfo ref="EXTRA_VEHICLE_POINTS_PLANE_LUXOR2_REAR"/>
      <Flags>IgnoreSmashWindowCheck</Flags>
      <BlockJackReactionSides/>
      <BreakoutTestPoint>POINT_TYPE_MAX</BreakoutTestPoint>
    </Item>
    <Item type="CVehicleEntryPointInfo">
      <Name>ENTRY_POINT_PLANE_LUXOR2_EXTRA_LEFT_1</Name>
      <DoorBoneName>door_dside_f</DoorBoneName>
      <SecondDoorBoneName>door_pside_f</SecondDoorBoneName>
      <DoorHandleBoneName/>
      <WindowId>INVALID</WindowId>
      <VehicleSide>SIDE_LEFT</VehicleSide>
      <AccessableSeats>
        <Item ref="SEAT_EXTRA_NO_SHUFFLE_LEFT_1"/>
      </AccessableSeats>
      <VehicleExtraPointsInfo ref="EXTRA_VEHICLE_POINTS_PLANE_LUXOR2_REAR"/>
      <Flags>IgnoreSmashWindowCheck</Flags>
      <BlockJackReactionSides/>
      <BreakoutTestPoint>POINT_TYPE_MAX</BreakoutTestPoint>
    </Item>
    <Item type="CVehicleEntryPointInfo">
      <Name>ENTRY_POINT_PLANE_LUXOR2_EXTRA_RIGHT_1</Name>
      <DoorBoneName>door_dside_f</DoorBoneName>
      <SecondDoorBoneName>door_pside_f</SecondDoorBoneName>
      <DoorHandleBoneName/>
      <WindowId>INVALID</WindowId>
      <VehicleSide>SIDE_LEFT</VehicleSide>
      <AccessableSeats>
        <Item ref="SEAT_PLANE_LUXOR2_NO_SHUFFLE_REAR_RIGHT"/>
      </AccessableSeats>
      <VehicleExtraPointsInfo ref="EXTRA_VEHICLE_POINTS_PLANE_LUXOR2_REAR"/>
      <Flags>IgnoreSmashWindowCheck</Flags>
      <BlockJackReactionSides/>
      <BreakoutTestPoint>POINT_TYPE_MAX</BreakoutTestPoint>
    </Item>
    <Item type="CVehicleEntryPointInfo">
      <Name>ENTRY_POINT_PLANE_LUXOR2_EXTRA_RIGHT_2</Name>
      <DoorBoneName>door_dside_f</DoorBoneName>
      <SecondDoorBoneName>door_pside_f</SecondDoorBoneName>
      <DoorHandleBoneName/>
      <WindowId>INVALID</WindowId>
      <VehicleSide>SIDE_LEFT</VehicleSide>
      <AccessableSeats>
        <Item ref="SEAT_EXTRA_NO_SHUFFLE_RIGHT_2"/>
      </AccessableSeats>
      <VehicleExtraPointsInfo ref="EXTRA_VEHICLE_POINTS_PLANE_LUXOR2_REAR"/>
      <Flags>IgnoreSmashWindowCheck</Flags>
      <BlockJackReactionSides/>
      <BreakoutTestPoint>POINT_TYPE_MAX</BreakoutTestPoint>
    </Item>
    <Item type="CVehicleEntryPointInfo">
      <Name>ENTRY_POINT_PLANE_LUXOR2_EXTRA_RIGHT_3</Name>
      <DoorBoneName>door_dside_f</DoorBoneName>
      <SecondDoorBoneName>door_pside_f</SecondDoorBoneName>
      <DoorHandleBoneName/>
      <WindowId>INVALID</WindowId>
      <VehicleSide>SIDE_LEFT</VehicleSide>
      <AccessableSeats>
        <Item ref="SEAT_EXTRA_NO_SHUFFLE_RIGHT_3"/>
      </AccessableSeats>
      <VehicleExtraPointsInfo ref="EXTRA_VEHICLE_POINTS_PLANE_LUXOR2_REAR"/>
      <Flags>IgnoreSmashWindowCheck</Flags>
      <BlockJackReactionSides/>
      <BreakoutTestPoint>POINT_TYPE_MAX</BreakoutTestPoint>
    </Item>
    <Item type="CVehicleEntryPointInfo">
      <Name>ENTRY_POINT_BOAT_DINGHY_REAR_LEFT</Name>
      <DoorBoneName/>
      <SecondDoorBoneName/>
      <DoorHandleBoneName/>
      <WindowId>INVALID</WindowId>
      <VehicleSide>SIDE_LEFT</VehicleSide>
      <AccessableSeats>
        <Item ref="SEAT_BOAT_REAR_LEFT"/>
      </AccessableSeats>
      <VehicleExtraPointsInfo ref="EXTRA_VEHICLE_POINTS_DINGHY_REAR_LEFT"/>
      <Flags/>
      <BlockJackReactionSides/>
    </Item>
    <Item type="CVehicleEntryPointInfo">
      <Name>ENTRY_POINT_BOAT_DINGHY_REAR_RIGHT</Name>
      <DoorBoneName/>
      <SecondDoorBoneName/>
      <DoorHandleBoneName/>
      <WindowId>INVALID</WindowId>
      <VehicleSide>SIDE_RIGHT</VehicleSide>
      <AccessableSeats>
        <Item ref="SEAT_BOAT_REAR_RIGHT"/>
      </AccessableSeats>
      <VehicleExtraPointsInfo ref="EXTRA_VEHICLE_POINTS_DINGHY_RIGHT"/>
      <Flags/>
      <BlockJackReactionSides/>
    </Item>
    <Item type="CVehicleEntryPointInfo">
      <Name>ENTRY_POINT_HELI_SAVAGE_FRONT</Name>
      <DoorBoneName>door_dside_f</DoorBoneName>
      <SecondDoorBoneName/>
      <DoorHandleBoneName/>
      <WindowId>FRONT_LEFT</WindowId>
      <VehicleSide>SIDE_LEFT</VehicleSide>
      <AccessableSeats>
        <Item ref="SEAT_SINGLE_FRONT"/>
      </AccessableSeats>
      <VehicleExtraPointsInfo ref="EXTRA_VEHICLE_POINTS_HELI_SAVAGE_FRONT"/>
      <Flags/>
      <BlockJackReactionSides/>
    </Item>
    <Item type="CVehicleEntryPointInfo">
      <Name>ENTRY_POINT_HELI_SAVAGE_REAR</Name>
      <DoorBoneName>door_pside_f</DoorBoneName>
      <SecondDoorBoneName/>
      <DoorHandleBoneName/>
      <WindowId>FRONT_RIGHT</WindowId>
      <VehicleSide>SIDE_LEFT</VehicleSide>
      <AccessableSeats>
        <Seat ref="SEAT_SINGLE_REAR"/>
      </AccessableSeats>
      <VehicleExtraPointsInfo ref="EXTRA_VEHICLE_POINTS_HELI_SAVAGE_REAR"/>
      <Flags/>
    </Item>
    <Item type="CVehicleEntryPointInfo">
      <Name>ENTRY_POINT_PLANE_VELUM2_REAR_BACK</Name>
      <DoorBoneName>door_dside_r</DoorBoneName>
      <SecondDoorBoneName/>
      <DoorHandleBoneName>handle_dside_r</DoorHandleBoneName>
      <WindowId>INVALID</WindowId>
      <VehicleSide>SIDE_LEFT</VehicleSide>
      <AccessableSeats>
        <Item ref="SEAT_EXTRA_NO_SHUFFLE_RIGHT_1"/>
      </AccessableSeats>
      <VehicleExtraPointsInfo ref="EXTRA_VEHICLE_POINTS_PLANE_VELUM2_REAR"/>
      <Flags>IsPlaneHatchEntry</Flags>
      <BlockJackReactionSides/>
      <BreakoutTestPoint>GET_IN</BreakoutTestPoint>
    </Item>
    <Item type="CVehicleEntryPointInfo">
      <Name>ENTRY_POINT_PLANE_VELUM2_REAR_LEFT</Name>
      <DoorBoneName>door_dside_r</DoorBoneName>
      <SecondDoorBoneName/>
      <DoorHandleBoneName>handle_dside_r</DoorHandleBoneName>
      <WindowId>INVALID</WindowId>
      <VehicleSide>SIDE_LEFT</VehicleSide>
      <AccessableSeats>
        <Item ref="SEAT_STANDARD_NO_SHUFFLE_REAR_LEFT"/>
      </AccessableSeats>
      <VehicleExtraPointsInfo ref="EXTRA_VEHICLE_POINTS_PLANE_VELUM2_REAR"/>
      <Flags>IsPlaneHatchEntry</Flags>
      <BlockJackReactionSides/>
      <BreakoutTestPoint>GET_IN</BreakoutTestPoint>
    </Item>
    <Item type="CVehicleEntryPointInfo">
      <Name>ENTRY_POINT_PLANE_VELUM2_REAR_RIGHT</Name>
      <DoorBoneName>door_dside_r</DoorBoneName>
      <SecondDoorBoneName/>
      <DoorHandleBoneName>handle_dside_r</DoorHandleBoneName>
      <WindowId>INVALID</WindowId>
      <VehicleSide>SIDE_LEFT</VehicleSide>
      <AccessableSeats>
        <Item ref="SEAT_STANDARD_NO_SHUFFLE_REAR_RIGHT"/>
      </AccessableSeats>
      <VehicleExtraPointsInfo ref="EXTRA_VEHICLE_POINTS_PLANE_VELUM2_REAR"/>
      <Flags>IsPlaneHatchEntry</Flags>
      <BlockJackReactionSides/>
      <BreakoutTestPoint>GET_IN</BreakoutTestPoint>
    </Item>
    <Item type="CVehicleEntryPointInfo">
      <Name>ENTRY_POINT_STD_NOWINDOW_FRONT_LEFT</Name>
      <DoorBoneName>door_dside_f</DoorBoneName>
      <SecondDoorBoneName/>
      <DoorHandleBoneName>handle_dside_f</DoorHandleBoneName>
      <WindowId>INVALID</WindowId>
      <VehicleSide>SIDE_LEFT</VehicleSide>
      <AccessableSeats>
        <Item ref="SEAT_STANDARD_FRONT_LEFT"/>
      </AccessableSeats>
      <VehicleExtraPointsInfo ref="EXTRA_VEHICLE_POINTS_STD_LEFT"/>
      <Flags>BlockJackReactionUntilJackerIsReady</Flags>
      <BlockJackReactionSides>
        <Item>SIDE_LEFT</Item>
      </BlockJackReactionSides>
    </Item>
    <Item type="CVehicleEntryPointInfo">
      <Name>ENTRY_POINT_STD_NOWINDOW_FRONT_RIGHT</Name>
      <DoorBoneName>door_pside_f</DoorBoneName>
      <SecondDoorBoneName/>
      <DoorHandleBoneName>handle_pside_f</DoorHandleBoneName>
      <WindowId>INVALID</WindowId>
      <VehicleSide>SIDE_RIGHT</VehicleSide>
      <AccessableSeats>
        <Item ref="SEAT_STANDARD_FRONT_RIGHT"/>
      </AccessableSeats>
      <VehicleExtraPointsInfo ref="EXTRA_VEHICLE_POINTS_STD_RIGHT"/>
      <Flags>BlockJackReactionUntilJackerIsReady</Flags>
      <BlockJackReactionSides>
        <Item>SIDE_RIGHT</Item>
      </BlockJackReactionSides>
    </Item>
    <Item type="CVehicleEntryPointInfo">
      <Name>ENTRY_POINT_STD_REAR_LEFT_TURRET</Name>
      <DoorBoneName/>
      <SecondDoorBoneName/>
      <DoorHandleBoneName/>
      <WindowId>REAR_LEFT</WindowId>
      <VehicleSide>SIDE_LEFT</VehicleSide>
      <AccessableSeats>
        <Item ref="SEAT_STD_TECHNICAL_REAR_TURRET"/>
      </AccessableSeats>
      <VehicleExtraPointsInfo ref="EXTRA_VEHICLE_POINTS_STD_TECHNICAL_REAR_LEFT_TURRET"/>
      <Flags>BlockJackReactionUntilJackerIsReady</Flags>
      <BlockJackReactionSides>
        <Item>SIDE_LEFT</Item>
      </BlockJackReactionSides>
    </Item>
    <Item type="CVehicleEntryPointInfo">
      <Name>ENTRY_POINT_STD_REAR_RIGHT_TURRET</Name>
      <DoorBoneName/>
      <SecondDoorBoneName/>
      <DoorHandleBoneName/>
      <WindowId>REAR_LEFT</WindowId>
      <VehicleSide>SIDE_RIGHT</VehicleSide>
      <AccessableSeats>
        <Item ref="SEAT_STD_TECHNICAL_REAR_TURRET"/>
      </AccessableSeats>
      <VehicleExtraPointsInfo ref="EXTRA_VEHICLE_POINTS_STD_TECHNICAL_REAR_RIGHT_TURRET"/>
      <Flags>BlockJackReactionUntilJackerIsReady</Flags>
      <BlockJackReactionSides>
        <Item>SIDE_RIGHT</Item>
      </BlockJackReactionSides>
    </Item>
    <Item type="CVehicleEntryPointInfo">
      <Name>ENTRY_POINT_STD_REAR_TURRET</Name>
      <DoorBoneName/>
      <SecondDoorBoneName/>
      <DoorHandleBoneName/>
      <WindowId>INVALID</WindowId>
      <VehicleSide>SIDE_REAR</VehicleSide>
      <AccessableSeats>
        <Item ref="SEAT_STD_TECHNICAL_REAR_TURRET"/>
      </AccessableSeats>
      <VehicleExtraPointsInfo ref="EXTRA_VEHICLE_POINTS_STD_TECHNICAL_REAR_TURRET"/>
      <Flags>BlockJackReactionUntilJackerIsReady</Flags>
      <BlockJackReactionSides>
        <Item>SIDE_REAR</Item>
      </BlockJackReactionSides>
    </Item>
  </VehicleEntryPointInfos>
  <VehicleEntryPointAnimInfos>
    <Item type="CVehicleEntryPointAnimInfo">
      <Name>ENTRY_POINT_ANIM_LOW_LE7B_FRONT_LEFT</Name>
      <CommonClipSetMap ref="COMMON_CLIPSET_MAP_AUTOMOBILE_LEFT"/>
      <EntryClipSetMap ref="ENTRY_CLIPSET_MAP_LOW_LE7B_FRONT_LEFT"/>
      <ExitClipSetMap ref="EXIT_CLIPSET_MAP_LOW_LE7B_FRONT_LEFT"/>
      <AlternateTryLockedDoorClipId/>
      <AlternateForcedEntryClipId/>
      <AlternateJackFromOutSideClipId/>
      <AlternateBeJackedFromOutSideClipId/>
      <AlternateEntryPointClipSetId/>
      <EnterVehicleMoveNetwork>ENTER_VEHICLE_STD</EnterVehicleMoveNetwork>
      <EntryTranslation x="-1.181000" y="-0.928000" z="0.610300"/>
      <OpenDoorTranslation x="0.000000" y="0.000000"/>
      <OpenDoorHeadingChange value="1.570000"/>
      <EntryHeadingChange value="-1.570000"/>
      <ExtraZForMPPlaneWarp value="-2.000000"/>
      <EntryPointFlags>JackIncludesGetIn UsesNoDoorTransitionForExit UseOpenDoorBlendAnims UseGetUpAfterJack DeadJackIncludesGetIn</EntryPointFlags>
      <EntryAnimVariations ref="NULL"/>
      <NMJumpFromVehicleTuningSet/>
    </Item>
    <Item type="CVehicleEntryPointAnimInfo">
      <Name>ENTRY_POINT_ANIM_LOW_LE7B_FRONT_RIGHT</Name>
      <CommonClipSetMap ref="COMMON_CLIPSET_MAP_AUTOMOBILE_RIGHT"/>
      <EntryClipSetMap ref="ENTRY_CLIPSET_MAP_LOW_LE7B_FRONT_RIGHT"/>
      <ExitClipSetMap ref="EXIT_CLIPSET_MAP_LOW_LE7B_FRONT_RIGHT"/>
      <AlternateTryLockedDoorClipId/>
      <AlternateForcedEntryClipId/>
      <AlternateJackFromOutSideClipId/>
      <AlternateBeJackedFromOutSideClipId/>
      <AlternateEntryPointClipSetId/>
      <EnterVehicleMoveNetwork>ENTER_VEHICLE_STD</EnterVehicleMoveNetwork>
      <EntryTranslation x="1.181000" y="-0.928000" z="0.610000"/>
      <OpenDoorTranslation x="0.000000" y="0.000000"/>
      <OpenDoorHeadingChange value="-1.570000"/>
      <EntryHeadingChange value="1.570000"/>
      <ExtraZForMPPlaneWarp value="-2.000000"/>
      <EntryPointFlags>JackIncludesGetIn UsesNoDoorTransitionForExit UseOpenDoorBlendAnims UseGetUpAfterJack DeadJackIncludesGetIn</EntryPointFlags>
      <EntryAnimVariations ref="NULL"/>
      <NMJumpFromVehicleTuningSet/>
    </Item>
    <Item type="CVehicleEntryPointAnimInfo">
      <Name>ENTRY_POINT_ANIM_HELI_SWIFT2_FRONT_LEFT</Name>
      <CommonClipSetMap ref="COMMON_CLIPSET_MAP_TRUCK_LEFT"/>
      <EntryClipSetMap ref="ENTRY_CLIPSET_MAP_HELI_FRONT_LEFT"/>
      <ExitClipSetMap ref="EXIT_CLIPSET_MAP_HELI_FRONT_LEFT"/>
      <AlternateTryLockedDoorClipId/>
      <AlternateForcedEntryClipId/>
      <AlternateJackFromOutSideClipId/>
      <AlternateBeJackedFromOutSideClipId/>
      <AlternateEntryPointClipSetId/>
      <EnterVehicleMoveNetwork>ENTER_VEHICLE_STD</EnterVehicleMoveNetwork>
      <EntryTranslation x="-1.550000" y="-0.546000" z="0.000000"/>
      <OpenDoorTranslation x="0.000000" y="0.000000"/>
      <OpenDoorHeadingChange value="0.000000"/>
      <EntryHeadingChange value="-1.570000"/>
      <ExtraZForMPPlaneWarp value="0.000000"/>
      <EntryPointFlags>JackIncludesGetIn UsesNoDoorTransitionForEnter UsesNoDoorTransitionForExit UseGetUpAfterJack DeadJackIncludesGetIn</EntryPointFlags>
      <EntryAnimVariations ref="ENTRY_ANIM_VARIATIONS_HELICOPTER_FRONT_LEFT"/>
      <NMJumpFromVehicleTuningSet/>
    </Item>
    <Item type="CVehicleEntryPointAnimInfo">
      <Name>ENTRY_POINT_ANIM_HELI_SWIFT2_FRONT_RIGHT</Name>
      <CommonClipSetMap ref="COMMON_CLIPSET_MAP_TRUCK_RIGHT"/>
      <EntryClipSetMap ref="ENTRY_CLIPSET_MAP_HELI_SWIFT2_FRONT_RIGHT"/>
      <ExitClipSetMap ref="EXIT_CLIPSET_MAP_HELI_SWIFT2_FRONT_RIGHT"/>
      <AlternateTryLockedDoorClipId/>
      <AlternateForcedEntryClipId/>
      <AlternateJackFromOutSideClipId/>
      <AlternateBeJackedFromOutSideClipId/>
      <AlternateEntryPointClipSetId/>
      <EnterVehicleMoveNetwork>ENTER_VEHICLE_STD</EnterVehicleMoveNetwork>
      <EntryTranslation x="1.416400" y="-0.576000" z="0.000000"/>
      <OpenDoorTranslation x="0.000000" y="0.000000"/>
      <OpenDoorHeadingChange value="0.000000"/>
      <EntryHeadingChange value="1.570000"/>
      <ExtraZForMPPlaneWarp value="0.000000"/>
      <EntryPointFlags>JackIncludesGetIn UsesNoDoorTransitionForEnter UsesNoDoorTransitionForExit UseGetUpAfterJack DeadJackIncludesGetIn</EntryPointFlags>
      <EntryAnimVariations ref="ENTRY_ANIM_VARIATIONS_HELICOPTER_FRONT_RIGHT"/>
      <NMJumpFromVehicleTuningSet/>
    </Item>
    <Item type="CVehicleEntryPointAnimInfo">
      <Name>ENTRY_POINT_ANIM_HELI_SWIFT2_REAR_LEFT</Name>
      <CommonClipSetMap ref="COMMON_CLIPSET_MAP_TRUCK_LEFT"/>
      <EntryClipSetMap ref="ENTRY_CLIPSET_MAP_HELI_SWIFT2_REAR_LEFT"/>
      <ExitClipSetMap ref="EXIT_CLIPSET_MAP_HELI_SWIFT2_REAR_LEFT"/>
      <AlternateTryLockedDoorClipId/>
      <AlternateForcedEntryClipId/>
      <AlternateJackFromOutSideClipId/>
      <AlternateBeJackedFromOutSideClipId/>
      <AlternateEntryPointClipSetId/>
      <EnterVehicleMoveNetwork>ENTER_VEHICLE_STD</EnterVehicleMoveNetwork>
      <EntryTranslation x="-1.466000" y="-0.221000" z="0.035000"/>
      <OpenDoorTranslation x="0.000000" y="0.000000"/>
      <OpenDoorHeadingChange value="0.000000"/>
      <EntryHeadingChange value="-1.570000"/>
      <ExtraZForMPPlaneWarp value="0.000000"/>
      <EntryPointFlags>JackIncludesGetIn UseGetUpAfterJack JackVariationsIncludeGetIn DeadJackIncludesGetIn</EntryPointFlags>
      <EntryAnimVariations ref="NULL"/>
      <NMJumpFromVehicleTuningSet/>
    </Item>
    <Item type="CVehicleEntryPointAnimInfo">
      <Name>ENTRY_POINT_ANIM_HELI_SWIFT2_REAR_RIGHT</Name>
      <CommonClipSetMap ref="COMMON_CLIPSET_MAP_TRUCK_RIGHT"/>
      <EntryClipSetMap ref="ENTRY_CLIPSET_MAP_HELI_SWIFT2_REAR_RIGHT"/>
      <ExitClipSetMap ref="EXIT_CLIPSET_MAP_HELI_SWIFT2_REAR_RIGHT"/>
      <AlternateTryLockedDoorClipId/>
      <AlternateForcedEntryClipId/>
      <AlternateJackFromOutSideClipId/>
      <AlternateBeJackedFromOutSideClipId/>
      <AlternateEntryPointClipSetId/>
      <EnterVehicleMoveNetwork>ENTER_VEHICLE_STD</EnterVehicleMoveNetwork>
      <EntryTranslation x="1.466000" y="-0.296000" z="0.035000"/>
      <OpenDoorTranslation x="0.000000" y="0.000000"/>
      <OpenDoorHeadingChange value="0.000000"/>
      <EntryHeadingChange value="1.570000"/>
      <ExtraZForMPPlaneWarp value="0.000000"/>
      <EntryPointFlags>JackIncludesGetIn UseGetUpAfterJack JackVariationsIncludeGetIn DeadJackIncludesGetIn</EntryPointFlags>
      <EntryAnimVariations ref="NULL"/>
      <NMJumpFromVehicleTuningSet/>
    </Item>
    <Item type="CVehicleEntryPointAnimInfo">
      <Name>ENTRY_POINT_ANIM_LOW_FELTZER3_FRONT_LEFT</Name>
      <CommonClipSetMap ref="COMMON_CLIPSET_MAP_AUTOMOBILE_LEFT"/>
      <EntryClipSetMap ref="ENTRY_CLIPSET_MAP_LOW_GULLWING_FRONT_LEFT"/>
      <ExitClipSetMap ref="EXIT_CLIPSET_MAP_LOW_GULLWING_FRONT_LEFT"/>
      <AlternateTryLockedDoorClipId>LOW_Locked_DS</AlternateTryLockedDoorClipId>
      <AlternateForcedEntryClipId>LOW_Force_Entry_DS</AlternateForcedEntryClipId>
      <AlternateJackFromOutSideClipId/>
      <AlternateBeJackedFromOutSideClipId/>
      <AlternateEntryPointClipSetId/>
      <EnterVehicleMoveNetwork>ENTER_VEHICLE_STD</EnterVehicleMoveNetwork>
      <EntryTranslation x="-0.980600" y="-0.753300" z="0.610300"/>
      <OpenDoorTranslation x="0.000000" y="-0.100000"/>
      <OpenDoorHeadingChange value="1.570000"/>
      <EntryHeadingChange value="-1.570000"/>
      <ExtraZForMPPlaneWarp value="-2.000000"/>
      <EntryPointFlags>JackIncludesGetIn UsesNoDoorTransitionForExit UseOpenDoorBlendAnims UseGetUpAfterJack DeadJackIncludesGetIn</EntryPointFlags>
      <EntryAnimVariations ref="NULL"/>
      <NMJumpFromVehicleTuningSet/>
    </Item>
    <Item type="CVehicleEntryPointAnimInfo">
      <Name>ENTRY_POINT_ANIM_LOW_FELTZER3_FRONT_RIGHT</Name>
      <CommonClipSetMap ref="COMMON_CLIPSET_MAP_AUTOMOBILE_RIGHT"/>
      <EntryClipSetMap ref="ENTRY_CLIPSET_MAP_LOW_GULLWING_FRONT_RIGHT"/>
      <ExitClipSetMap ref="EXIT_CLIPSET_MAP_LOW_GULLWING_FRONT_RIGHT"/>
      <AlternateTryLockedDoorClipId>LOW_Locked_PS</AlternateTryLockedDoorClipId>
      <AlternateForcedEntryClipId>LOW_Force_Entry_PS</AlternateForcedEntryClipId>
      <AlternateJackFromOutSideClipId/>
      <AlternateBeJackedFromOutSideClipId/>
      <AlternateEntryPointClipSetId/>
      <EnterVehicleMoveNetwork>ENTER_VEHICLE_STD</EnterVehicleMoveNetwork>
      <EntryTranslation x="0.875000" y="-0.740000" z="0.610000"/>
      <OpenDoorTranslation x="0.000000" y="-0.100000"/>
      <OpenDoorHeadingChange value="-1.570000"/>
      <EntryHeadingChange value="1.570000"/>
      <ExtraZForMPPlaneWarp value="-2.000000"/>
      <EntryPointFlags>JackIncludesGetIn UsesNoDoorTransitionForExit UseOpenDoorBlendAnims UseGetUpAfterJack DeadJackIncludesGetIn</EntryPointFlags>
      <EntryAnimVariations ref="NULL"/>
      <NMJumpFromVehicleTuningSet/>
    </Item>
    <Item type="CVehicleEntryPointAnimInfo">
      <Name>ENTRY_POINT_ANIM_LOW_OSIRIS_FRONT_LEFT</Name>
      <CommonClipSetMap ref="COMMON_CLIPSET_MAP_AUTOMOBILE_LEFT"/>
      <EntryClipSetMap ref="ENTRY_CLIPSET_MAP_LOW_FRONT_LEFT"/>
      <ExitClipSetMap ref="EXIT_CLIPSET_MAP_LOW_INFERNUS_FRONT_LEFT"/>
      <AlternateTryLockedDoorClipId>LOW_Locked_DS</AlternateTryLockedDoorClipId>
      <AlternateForcedEntryClipId>LOW_Force_Entry_DS</AlternateForcedEntryClipId>
      <AlternateJackFromOutSideClipId/>
      <AlternateBeJackedFromOutSideClipId/>
      <AlternateEntryPointClipSetId/>
      <EnterVehicleMoveNetwork>ENTER_VEHICLE_STD</EnterVehicleMoveNetwork>
      <EntryTranslation x="-0.980600" y="-0.753300" z="0.610300"/>
      <OpenDoorTranslation x="0.000000" y="0.000000"/>
      <OpenDoorHeadingChange value="1.570000"/>
      <EntryHeadingChange value="-1.570000"/>
      <ExtraZForMPPlaneWarp value="-2.000000"/>
      <EntryPointFlags>JackIncludesGetIn UsesNoDoorTransitionForExit UseOpenDoorBlendAnims UseGetUpAfterJack DeadJackIncludesGetIn</EntryPointFlags>
      <EntryAnimVariations ref="NULL"/>
      <NMJumpFromVehicleTuningSet/>
    </Item>
    <Item type="CVehicleEntryPointAnimInfo">
      <Name>ENTRY_POINT_ANIM_LOW_OSIRIS_FRONT_RIGHT</Name>
      <CommonClipSetMap ref="COMMON_CLIPSET_MAP_AUTOMOBILE_RIGHT"/>
      <EntryClipSetMap ref="ENTRY_CLIPSET_MAP_LOW_FRONT_RIGHT"/>
      <ExitClipSetMap ref="EXIT_CLIPSET_MAP_LOW_INFERNUS_FRONT_RIGHT"/>
      <AlternateTryLockedDoorClipId>LOW_Locked_PS</AlternateTryLockedDoorClipId>
      <AlternateForcedEntryClipId>LOW_Force_Entry_PS</AlternateForcedEntryClipId>
      <AlternateJackFromOutSideClipId/>
      <AlternateBeJackedFromOutSideClipId/>
      <AlternateEntryPointClipSetId/>
      <EnterVehicleMoveNetwork>ENTER_VEHICLE_STD</EnterVehicleMoveNetwork>
      <EntryTranslation x="0.875000" y="-0.740000" z="0.610000"/>
      <OpenDoorTranslation x="0.000000" y="0.000000"/>
      <OpenDoorHeadingChange value="-1.570000"/>
      <EntryHeadingChange value="1.570000"/>
      <ExtraZForMPPlaneWarp value="-2.000000"/>
      <EntryPointFlags>JackIncludesGetIn UsesNoDoorTransitionForExit UseOpenDoorBlendAnims UseGetUpAfterJack DeadJackIncludesGetIn</EntryPointFlags>
      <EntryAnimVariations ref="NULL"/>
      <NMJumpFromVehicleTuningSet/>
    </Item>
    <Item type="CVehicleEntryPointAnimInfo">
      <Name>ENTRY_POINT_ANIM_PLANE_LUXOR2_REAR_LEFT</Name>
      <CommonClipSetMap ref="COMMON_CLIPSET_MAP_PLANE_JET"/>
      <EntryClipSetMap ref="ENTRY_CLIPSET_MAP_PLANE_LUXOR2_REAR_LEFT"/>
      <ExitClipSetMap ref="EXIT_CLIPSET_MAP_PLANE_LUXOR2_REAR_LEFT"/>
      <AlternateTryLockedDoorClipId/>
      <AlternateForcedEntryClipId/>
      <AlternateJackFromOutSideClipId/>
      <AlternateBeJackedFromOutSideClipId/>
      <AlternateEntryPointClipSetId/>
      <EnterVehicleMoveNetwork>ENTER_VEHICLE_STD</EnterVehicleMoveNetwork>
      <EntryTranslation x="-2.006000" y="4.530000" z="-0.655000"/>
      <OpenDoorTranslation x="-0.475000" y="0.000000"/>
      <OpenDoorHeadingChange value="0.000000"/>
      <EntryHeadingChange value="-1.570000"/>
      <ExtraZForMPPlaneWarp value="-2.000000"/>
      <EntryPointFlags>UseVehicleRelativeEntryPosition JackIncludesGetIn UseNewPlaneSystem DontCloseDoorInside DontCloseDoorOutside JackVariationsIncludeGetIn DeadJackIncludesGetIn</EntryPointFlags>
      <EntryAnimVariations ref="NULL"/>
      <NMJumpFromVehicleTuningSet/>
    </Item>
    <Item type="CVehicleEntryPointAnimInfo">
      <Name>ENTRY_POINT_ANIM_PLANE_LUXOR2_REAR_RIGHT</Name>
      <CommonClipSetMap ref="COMMON_CLIPSET_MAP_PLANE_JET"/>
      <EntryClipSetMap ref="ENTRY_CLIPSET_MAP_PLANE_LUXOR2_REAR_RIGHT"/>
      <ExitClipSetMap ref="EXIT_CLIPSET_MAP_PLANE_LUXOR2_REAR_RIGHT"/>
      <AlternateTryLockedDoorClipId/>
      <AlternateForcedEntryClipId/>
      <AlternateJackFromOutSideClipId/>
      <AlternateBeJackedFromOutSideClipId/>
      <AlternateEntryPointClipSetId/>
      <EnterVehicleMoveNetwork>ENTER_VEHICLE_STD</EnterVehicleMoveNetwork>
      <EntryTranslation x="-2.006000" y="4.530000" z="-0.655000"/>
      <OpenDoorTranslation x="-0.475000" y="0.000000"/>
      <OpenDoorHeadingChange value="0.000000"/>
      <EntryHeadingChange value="-1.570000"/>
      <ExtraZForMPPlaneWarp value="-2.000000"/>
      <EntryPointFlags>UseVehicleRelativeEntryPosition JackIncludesGetIn UseNewPlaneSystem DontCloseDoorInside DontCloseDoorOutside JackVariationsIncludeGetIn DeadJackIncludesGetIn</EntryPointFlags>
      <EntryAnimVariations ref="NULL"/>
      <NMJumpFromVehicleTuningSet/>
    </Item>
    <Item type="CVehicleEntryPointAnimInfo">
      <Name>ENTRY_POINT_ANIM_PLANE_LUXOR2_EXTRA_LEFT_1</Name>
      <CommonClipSetMap ref="COMMON_CLIPSET_MAP_PLANE_JET"/>
      <EntryClipSetMap ref="ENTRY_CLIPSET_MAP_PLANE_LUXOR2_EXTRA_LEFT_1"/>
      <ExitClipSetMap ref="EXIT_CLIPSET_MAP_PLANE_LUXOR2_EXTRA_LEFT_1"/>
      <AlternateTryLockedDoorClipId/>
      <AlternateForcedEntryClipId/>
      <AlternateJackFromOutSideClipId/>
      <AlternateBeJackedFromOutSideClipId/>
      <AlternateEntryPointClipSetId/>
      <EnterVehicleMoveNetwork>ENTER_VEHICLE_STD</EnterVehicleMoveNetwork>
      <EntryTranslation x="-2.006000" y="4.530000" z="-0.655000"/>
      <OpenDoorTranslation x="-0.475000" y="0.000000"/>
      <OpenDoorHeadingChange value="0.000000"/>
      <EntryHeadingChange value="-1.570000"/>
      <ExtraZForMPPlaneWarp value="-2.000000"/>
      <EntryPointFlags>UseVehicleRelativeEntryPosition JackIncludesGetIn UseNewPlaneSystem DontCloseDoorInside DontCloseDoorOutside JackVariationsIncludeGetIn DeadJackIncludesGetIn</EntryPointFlags>
      <EntryAnimVariations ref="NULL"/>
      <NMJumpFromVehicleTuningSet/>
    </Item>
    <Item type="CVehicleEntryPointAnimInfo">
      <Name>ENTRY_POINT_ANIM_PLANE_LUXOR2_EXTRA_RIGHT_1</Name>
      <CommonClipSetMap ref="COMMON_CLIPSET_MAP_PLANE_JET"/>
      <EntryClipSetMap ref="ENTRY_CLIPSET_MAP_PLANE_LUXOR2_EXTRA_RIGHT_1"/>
      <ExitClipSetMap ref="EXIT_CLIPSET_MAP_PLANE_LUXOR2_EXTRA_RIGHT_1"/>
      <AlternateTryLockedDoorClipId/>
      <AlternateForcedEntryClipId/>
      <AlternateJackFromOutSideClipId/>
      <AlternateBeJackedFromOutSideClipId/>
      <AlternateEntryPointClipSetId/>
      <EnterVehicleMoveNetwork>ENTER_VEHICLE_STD</EnterVehicleMoveNetwork>
      <EntryTranslation x="-2.006000" y="4.530000" z="-0.655000"/>
      <OpenDoorTranslation x="-0.475000" y="0.000000"/>
      <OpenDoorHeadingChange value="0.000000"/>
      <EntryHeadingChange value="-1.570000"/>
      <ExtraZForMPPlaneWarp value="-2.000000"/>
      <EntryPointFlags>UseVehicleRelativeEntryPosition JackIncludesGetIn UseNewPlaneSystem DontCloseDoorInside DontCloseDoorOutside JackVariationsIncludeGetIn DeadJackIncludesGetIn</EntryPointFlags>
      <EntryAnimVariations ref="NULL"/>
      <NMJumpFromVehicleTuningSet/>
    </Item>
    <Item type="CVehicleEntryPointAnimInfo">
      <Name>ENTRY_POINT_ANIM_PLANE_LUXOR2_EXTRA_RIGHT_2</Name>
      <CommonClipSetMap ref="COMMON_CLIPSET_MAP_PLANE_JET"/>
      <EntryClipSetMap ref="ENTRY_CLIPSET_MAP_PLANE_LUXOR2_EXTRA_RIGHT_2"/>
      <ExitClipSetMap ref="EXIT_CLIPSET_MAP_PLANE_LUXOR2_EXTRA_RIGHT_2"/>
      <AlternateTryLockedDoorClipId/>
      <AlternateForcedEntryClipId/>
      <AlternateJackFromOutSideClipId/>
      <AlternateBeJackedFromOutSideClipId/>
      <AlternateEntryPointClipSetId/>
      <EnterVehicleMoveNetwork>ENTER_VEHICLE_STD</EnterVehicleMoveNetwork>
      <EntryTranslation x="-2.006000" y="4.530000" z="-0.655000"/>
      <OpenDoorTranslation x="-0.475000" y="0.000000"/>
      <OpenDoorHeadingChange value="0.000000"/>
      <EntryHeadingChange value="-1.570000"/>
      <ExtraZForMPPlaneWarp value="-2.000000"/>
      <EntryPointFlags>UseVehicleRelativeEntryPosition JackIncludesGetIn UseNewPlaneSystem DontCloseDoorInside DontCloseDoorOutside JackVariationsIncludeGetIn DeadJackIncludesGetIn</EntryPointFlags>
      <EntryAnimVariations ref="NULL"/>
      <NMJumpFromVehicleTuningSet/>
    </Item>
    <Item type="CVehicleEntryPointAnimInfo">
      <Name>ENTRY_POINT_ANIM_PLANE_LUXOR2_EXTRA_RIGHT_3</Name>
      <CommonClipSetMap ref="COMMON_CLIPSET_MAP_PLANE_JET"/>
      <EntryClipSetMap ref="ENTRY_CLIPSET_MAP_PLANE_LUXOR2_EXTRA_RIGHT_3"/>
      <ExitClipSetMap ref="EXIT_CLIPSET_MAP_PLANE_LUXOR2_EXTRA_RIGHT_3"/>
      <AlternateTryLockedDoorClipId/>
      <AlternateForcedEntryClipId/>
      <AlternateJackFromOutSideClipId/>
      <AlternateBeJackedFromOutSideClipId/>
      <AlternateEntryPointClipSetId/>
      <EnterVehicleMoveNetwork>ENTER_VEHICLE_STD</EnterVehicleMoveNetwork>
      <EntryTranslation x="-2.006000" y="4.530000" z="-0.655000"/>
      <OpenDoorTranslation x="-0.475000" y="0.000000"/>
      <OpenDoorHeadingChange value="0.000000"/>
      <EntryHeadingChange value="-1.570000"/>
      <ExtraZForMPPlaneWarp value="-2.000000"/>
      <EntryPointFlags>UseVehicleRelativeEntryPosition JackIncludesGetIn UseNewPlaneSystem DontCloseDoorInside DontCloseDoorOutside JackVariationsIncludeGetIn DeadJackIncludesGetIn</EntryPointFlags>
      <EntryAnimVariations ref="NULL"/>
      <NMJumpFromVehicleTuningSet/>
    </Item>
    <Item type="CVehicleEntryPointAnimInfo">
      <Name>ENTRY_POINT_ANIM_BOAT_DINGHY_REAR_LEFT</Name>
      <CommonClipSetMap ref="COMMON_CLIPSET_MAP_BOAT_DINGHY_FRONT_LEFT"/>
      <EntryClipSetMap ref="ENTRY_CLIPSET_MAP_BOAT_DINGHY_REAR_LEFT"/>
      <ExitClipSetMap ref="EXIT_CLIPSET_MAP_BOAT_DINGHY_REAR_LEFT"/>
      <AlternateTryLockedDoorClipId/>
      <AlternateForcedEntryClipId/>
      <AlternateJackFromOutSideClipId/>
      <AlternateBeJackedFromOutSideClipId/>
      <AlternateEntryPointClipSetId/>
      <EnterVehicleMoveNetwork>ENTER_VEHICLE_STD</EnterVehicleMoveNetwork>
      <EntryTranslation x="-1.525000" y="0.195000" z="-0.126000"/>
      <OpenDoorTranslation x="0.000000" y="0.000000"/>
      <OpenDoorHeadingChange value="0.000000"/>
      <EntryHeadingChange value="-1.570000"/>
      <ExtraZForWaterEntry value="1.000000"/>
      <EntryPointFlags>HasGetInFromWater JackIncludesGetIn HasOnVehicleEntry UseGetUpAfterJack JackVariationsIncludeGetIn DeadJackIncludesGetIn UseStandOnGetIn</EntryPointFlags>
      <EntryAnimVariations ref="NULL"/>
    </Item>
    <Item type="CVehicleEntryPointAnimInfo">
      <Name>ENTRY_POINT_ANIM_BOAT_DINGHY_REAR_RIGHT</Name>
      <CommonClipSetMap ref="COMMON_CLIPSET_MAP_BOAT_DINGHY_FRONT_RIGHT"/>
      <EntryClipSetMap ref="ENTRY_CLIPSET_MAP_BOAT_DINGHY_FRONT_RIGHT"/>
      <ExitClipSetMap ref="EXIT_CLIPSET_MAP_BOAT_DINGHY_FRONT_RIGHT"/>
      <AlternateTryLockedDoorClipId/>
      <AlternateForcedEntryClipId/>
      <AlternateJackFromOutSideClipId/>
      <AlternateBeJackedFromOutSideClipId/>
      <AlternateEntryPointClipSetId/>
      <EnterVehicleMoveNetwork>ENTER_VEHICLE_STD</EnterVehicleMoveNetwork>
      <EntryTranslation x="1.466000" y="0.195000" z="-0.126000"/>
      <OpenDoorTranslation x="0.000000" y="0.000000"/>
      <OpenDoorHeadingChange value="0.000000"/>
      <EntryHeadingChange value="1.570000"/>
      <ExtraZForWaterEntry value="1.000000"/>
      <EntryPointFlags>HasGetInFromWater JackIncludesGetIn HasOnVehicleEntry UseGetUpAfterJack JackVariationsIncludeGetIn DeadJackIncludesGetIn UseStandOnGetIn</EntryPointFlags>
      <EntryAnimVariations ref="NULL"/>
    </Item>
    <Item type="CVehicleEntryPointAnimInfo">
      <Name>ENTRY_POINT_ANIM_HELI_SAVAGE_FRONT</Name>
      <CommonClipSetMap ref="COMMON_CLIPSET_MAP_AUTOMOBILE_LEFT"/>
      <EntryClipSetMap ref="ENTRY_CLIPSET_MAP_HELI_SAVAGE_FRONT"/>
      <ExitClipSetMap ref="EXIT_CLIPSET_MAP_HELI_SAVAGE_FRONT"/>
      <AlternateTryLockedDoorClipId/>
      <AlternateForcedEntryClipId/>
      <AlternateJackFromOutSideClipId/>
      <AlternateBeJackedFromOutSideClipId/>
      <AlternateEntryPointClipSetId/>
      <EnterVehicleMoveNetwork>ENTER_VEHICLE_STD</EnterVehicleMoveNetwork>
      <EntryTranslation x="-1.643000" y="0.040000" z="0.000000"/>
      <OpenDoorTranslation x="0.000000" y="0.000000"/>
      <OpenDoorHeadingChange value="0.000000"/>
      <EntryHeadingChange value="-1.570000"/>
      <ExtraZForWaterEntry value="0.000000"/>
      <EntryPointFlags>JackIncludesGetIn UsesNoDoorTransitionForEnter UseNewPlaneSystem DontCloseDoorOutside UseGetUpAfterJack JackVariationsIncludeGetIn DeadJackIncludesGetIn</EntryPointFlags>
      <EntryAnimVariations ref="NULL"/>
    </Item>
    <Item type="CVehicleEntryPointAnimInfo">
      <Name>ENTRY_POINT_ANIM_HELI_SAVAGE_REAR</Name>
      <CommonClipSetMap ref="COMMON_CLIPSET_MAP_AUTOMOBILE_LEFT"/>
      <EntryClipSetMap ref="ENTRY_CLIPSET_MAP_HELI_SAVAGE_REAR"/>
      <ExitClipSetMap ref="EXIT_CLIPSET_MAP_HELI_SAVAGE_REAR"/>
      <AlternateTryLockedDoorClipId/>
      <AlternateForcedEntryClipId/>
      <AlternateJackFromOutSideClipId/>
      <AlternateBeJackedFromOutSideClipId/>
      <AlternateEntryPointClipSetId/>
      <EnterVehicleMoveNetwork>ENTER_VEHICLE_STD</EnterVehicleMoveNetwork>
      <EntryTranslation x="-1.680000" y="0.255000" z="-0.550000"/>
      <OpenDoorTranslation x="0.000000" y="0.000000"/>
      <OpenDoorHeadingChange value="0.000000"/>
      <EntryHeadingChange value="-1.570000"/>
      <ExtraZForWaterEntry value="0.000000"/>
      <EntryPointFlags>JackIncludesGetIn UsesNoDoorTransitionForEnter UseNewPlaneSystem DontCloseDoorOutside UseGetUpAfterJack JackVariationsIncludeGetIn DeadJackIncludesGetIn</EntryPointFlags>
      <EntryAnimVariations ref="NULL"/>
    </Item>
    <Item type="CVehicleEntryPointAnimInfo">
      <Name>ENTRY_POINT_ANIM_HELI_SAVAGE_REAR_LEFT</Name>
      <CommonClipSetMap ref="COMMON_CLIPSET_MAP_TRUCK_LEFT"/>
      <EntryClipSetMap ref="ENTRY_CLIPSET_MAP_HELI_REAR_LEFT"/>
      <ExitClipSetMap ref="EXIT_CLIPSET_MAP_HELI_REAR_LEFT"/>
      <AlternateTryLockedDoorClipId/>
      <AlternateForcedEntryClipId/>
      <AlternateJackFromOutSideClipId/>
      <AlternateBeJackedFromOutSideClipId/>
      <AlternateEntryPointClipSetId/>
      <EnterVehicleMoveNetwork>ENTER_VEHICLE_STD</EnterVehicleMoveNetwork>
      <EntryTranslation x="-1.369200" y="-0.376600" z="-0.180000"/>
      <OpenDoorTranslation x="0.000000" y="0.000000"/>
      <OpenDoorHeadingChange value="1.570000"/>
      <EntryHeadingChange value="-1.570000"/>
      <ExtraZForWaterEntry value="0.000000"/>
      <EntryPointFlags>JackIncludesGetIn FixUpMoverToEntryPointOnExit UseGetUpAfterJack JackVariationsIncludeGetIn DeadJackIncludesGetIn</EntryPointFlags>
      <EntryAnimVariations ref="ENTRY_ANIM_VARIATIONS_HELICOPTER_REAR_LEFT"/>
    </Item>
    <Item type="CVehicleEntryPointAnimInfo">
      <Name>ENTRY_POINT_ANIM_HELI_SAVAGE_REAR_RIGHT</Name>
      <CommonClipSetMap ref="COMMON_CLIPSET_MAP_TRUCK_RIGHT"/>
      <EntryClipSetMap ref="ENTRY_CLIPSET_MAP_HELI_REAR_RIGHT"/>
      <ExitClipSetMap ref="EXIT_CLIPSET_MAP_HELI_REAR_RIGHT"/>
      <AlternateTryLockedDoorClipId/>
      <AlternateForcedEntryClipId/>
      <AlternateJackFromOutSideClipId/>
      <AlternateBeJackedFromOutSideClipId/>
      <AlternateEntryPointClipSetId/>
      <EnterVehicleMoveNetwork>ENTER_VEHICLE_STD</EnterVehicleMoveNetwork>
      <EntryTranslation x="1.369200" y="-0.376600" z="-0.180000"/>
      <OpenDoorTranslation x="0.000000" y="0.000000"/>
      <OpenDoorHeadingChange value="-1.570000"/>
      <EntryHeadingChange value="1.570000"/>
      <ExtraZForWaterEntry value="0.000000"/>
      <EntryPointFlags>JackIncludesGetIn FixUpMoverToEntryPointOnExit UseGetUpAfterJack JackVariationsIncludeGetIn DeadJackIncludesGetIn</EntryPointFlags>
      <EntryAnimVariations ref="ENTRY_ANIM_VARIATIONS_HELICOPTER_REAR_RIGHT"/>
    </Item>
    <Item type="CVehicleEntryPointAnimInfo">
      <Name>ENTRY_POINT_ANIM_PLANE_VELUM2_FRONT_LEFT</Name>
      <CommonClipSetMap ref="COMMON_CLIPSET_MAP_PLANE_FRONT_LEFT"/>
      <EntryClipSetMap ref="ENTRY_CLIPSET_MAP_PLANE_VELUM_FRONT_LEFT"/>
      <ExitClipSetMap ref="EXIT_CLIPSET_MAP_PLANE_VELUM_FRONT_LEFT"/>
      <AlternateTryLockedDoorClipId/>
      <AlternateForcedEntryClipId/>
      <AlternateJackFromOutSideClipId/>
      <AlternateBeJackedFromOutSideClipId/>
      <AlternateEntryPointClipSetId/>
      <EnterVehicleMoveNetwork>ENTER_VEHICLE_STD</EnterVehicleMoveNetwork>
      <EntryTranslation x="-1.020000" y="-1.940000" z="-0.690000"/>
      <OpenDoorTranslation x="0.000000" y="0.000000"/>
      <OpenDoorHeadingChange value="0.000000"/>
      <EntryHeadingChange value="0.000000"/>
      <ExtraZForWaterEntry value="0.000000"/>
      <EntryPointFlags>HasClimbUp JackIncludesGetIn UsesNoDoorTransitionForEnter UsesNoDoorTransitionForExit UseNewPlaneSystem HasOnVehicleEntry HasClimbDown JackVariationsIncludeGetIn DeadJackIncludesGetIn</EntryPointFlags>
      <EntryAnimVariations ref="NULL"/>
    </Item>
    <Item type="CVehicleEntryPointAnimInfo">
      <Name>ENTRY_POINT_ANIM_PLANE_VELUM2_REAR_BACK</Name>
      <CommonClipSetMap ref="COMMON_CLIPSET_MAP_PLANE_JET"/>
      <EntryClipSetMap ref="ENTRY_CLIPSET_MAP_PLANE_VELUM2_REAR_BACK"/>
      <ExitClipSetMap ref="EXIT_CLIPSET_MAP_PLANE_VELUM2_REAR_BACK"/>
      <AlternateTryLockedDoorClipId/>
      <AlternateForcedEntryClipId/>
      <AlternateJackFromOutSideClipId/>
      <AlternateBeJackedFromOutSideClipId/>
      <AlternateEntryPointClipSetId/>
      <EnterVehicleMoveNetwork>ENTER_VEHICLE_STD</EnterVehicleMoveNetwork>
      <EntryTranslation x="-1.900000" y="0.820000" z="-0.500000"/>
      <OpenDoorTranslation x="-0.475000" y="0.000000"/>
      <OpenDoorHeadingChange value="0.000000"/>
      <EntryHeadingChange value="-1.570000"/>
      <ExtraZForWaterEntry value="0.000000"/>
      <EntryPointFlags>JackIncludesGetIn UseNewPlaneSystem DontCloseDoorInside PreventJackInterrupt DontCloseDoorOutside JackVariationsIncludeGetIn DeadJackIncludesGetIn</EntryPointFlags>
      <EntryAnimVariations ref="ENTRY_ANIM_VARIATIONS_PLANE_VELUM2_REAR_BACK"/>
    </Item>
    <Item type="CVehicleEntryPointAnimInfo">
      <Name>ENTRY_POINT_ANIM_PLANE_VELUM2_REAR_LEFT</Name>
      <CommonClipSetMap ref="COMMON_CLIPSET_MAP_PLANE_JET"/>
      <EntryClipSetMap ref="ENTRY_CLIPSET_MAP_PLANE_VELUM2_REAR_LEFT"/>
      <ExitClipSetMap ref="EXIT_CLIPSET_MAP_PLANE_VELUM2_REAR_LEFT"/>
      <AlternateTryLockedDoorClipId/>
      <AlternateForcedEntryClipId/>
      <AlternateJackFromOutSideClipId/>
      <AlternateBeJackedFromOutSideClipId/>
      <AlternateEntryPointClipSetId/>
      <EnterVehicleMoveNetwork>ENTER_VEHICLE_STD</EnterVehicleMoveNetwork>
      <EntryTranslation x="1.400000" y="1.210000" z="-0.500000"/>
      <OpenDoorTranslation x="-0.475000" y="0.000000"/>
      <OpenDoorHeadingChange value="0.000000"/>
      <EntryHeadingChange value="1.570000"/>
      <ExtraZForWaterEntry value="0.000000"/>
      <EntryPointFlags>JackIncludesGetIn UseNewPlaneSystem DontCloseDoorInside PreventJackInterrupt DontCloseDoorOutside JackVariationsIncludeGetIn DeadJackIncludesGetIn</EntryPointFlags>
      <EntryAnimVariations ref="ENTRY_ANIM_VARIATIONS_PLANE_VELUM2_REAR_LEFT"/>
    </Item>
    <Item type="CVehicleEntryPointAnimInfo">
      <Name>ENTRY_POINT_ANIM_PLANE_VELUM2_REAR_RIGHT</Name>
      <CommonClipSetMap ref="COMMON_CLIPSET_MAP_PLANE_JET"/>
      <EntryClipSetMap ref="ENTRY_CLIPSET_MAP_PLANE_VELUM2_REAR_RIGHT"/>
      <ExitClipSetMap ref="EXIT_CLIPSET_MAP_PLANE_VELUM2_REAR_RIGHT"/>
      <AlternateTryLockedDoorClipId/>
      <AlternateForcedEntryClipId/>
      <AlternateJackFromOutSideClipId/>
      <AlternateBeJackedFromOutSideClipId/>
      <AlternateEntryPointClipSetId/>
      <EnterVehicleMoveNetwork>ENTER_VEHICLE_STD</EnterVehicleMoveNetwork>
      <EntryTranslation x="2.010000" y="1.210000" z="-0.500000"/>
      <OpenDoorTranslation x="-0.475000" y="0.000000"/>
      <OpenDoorHeadingChange value="0.000000"/>
      <EntryHeadingChange value="1.570000"/>
      <ExtraZForWaterEntry value="0.000000"/>
      <EntryPointFlags>JackIncludesGetIn UseNewPlaneSystem DontCloseDoorInside PreventJackInterrupt DontCloseDoorOutside JackVariationsIncludeGetIn DeadJackIncludesGetIn</EntryPointFlags>
      <EntryAnimVariations ref="ENTRY_ANIM_VARIATIONS_PLANE_VELUM2_REAR_RIGHT"/>
    </Item>
    <Item type="CVehicleEntryPointAnimInfo">
      <Name>ENTRY_POINT_ANIM_TRUCK_GUARDIAN_FRONT_LEFT</Name>
      <CommonClipSetMap ref="COMMON_CLIPSET_MAP_AUTOMOBILE_LEFT"/>
      <EntryClipSetMap ref="ENTRY_CLIPSET_MAP_TRUCK_MIXER_FRONT_LEFT"/>
      <ExitClipSetMap ref="EXIT_CLIPSET_MAP_TRUCK_MIXER_FRONT_LEFT"/>
      <AlternateTryLockedDoorClipId/>
      <AlternateForcedEntryClipId/>
      <AlternateJackFromOutSideClipId/>
      <AlternateBeJackedFromOutSideClipId/>
      <AlternateEntryPointClipSetId/>
      <EnterVehicleMoveNetwork>ENTER_VEHICLE_STD</EnterVehicleMoveNetwork>
      <EntryTranslation x="-1.444000" y="-0.350000" z="-0.300000"/>
      <OpenDoorTranslation x="0.000000" y="0.000000"/>
      <OpenDoorHeadingChange value="0.000000"/>
      <EntryHeadingChange value="-1.570000"/>
      <ExtraZForWaterEntry value="0.000000"/>
      <EntryPointFlags>JackIncludesGetIn ForcedEntryIncludesGetIn UsesNoDoorTransitionForEnter UsesNoDoorTransitionForExit FixUpMoverToEntryPointOnExit UseGetUpAfterJack JackVariationsIncludeGetIn DeadJackIncludesGetIn</EntryPointFlags>
      <EntryAnimVariations ref="NULL"/>
      <NMJumpFromVehicleTuningSet>SLOWTRUCK</NMJumpFromVehicleTuningSet>
    </Item>
    <Item type="CVehicleEntryPointAnimInfo">
      <Name>ENTRY_POINT_ANIM_TRUCK_GUARDIAN_FRONT_RIGHT</Name>
      <CommonClipSetMap ref="COMMON_CLIPSET_MAP_AUTOMOBILE_RIGHT"/>
      <EntryClipSetMap ref="ENTRY_CLIPSET_MAP_TRUCK_MIXER_FRONT_RIGHT"/>
      <ExitClipSetMap ref="EXIT_CLIPSET_MAP_TRUCK_MIXER_FRONT_RIGHT"/>
      <AlternateTryLockedDoorClipId/>
      <AlternateForcedEntryClipId/>
      <AlternateJackFromOutSideClipId/>
      <AlternateBeJackedFromOutSideClipId/>
      <AlternateEntryPointClipSetId/>
      <EnterVehicleMoveNetwork>ENTER_VEHICLE_STD</EnterVehicleMoveNetwork>
      <EntryTranslation x="1.519000" y="-0.475000" z="-0.300000"/>
      <OpenDoorTranslation x="0.000000" y="0.000000"/>
      <OpenDoorHeadingChange value="0.000000"/>
      <EntryHeadingChange value="1.570000"/>
      <ExtraZForWaterEntry value="0.000000"/>
      <EntryPointFlags>JackIncludesGetIn ForcedEntryIncludesGetIn UsesNoDoorTransitionForEnter UsesNoDoorTransitionForExit UseGetUpAfterJack JackVariationsIncludeGetIn DeadJackIncludesGetIn</EntryPointFlags>
      <EntryAnimVariations ref="NULL"/>
    </Item>
    <Item type="CVehicleEntryPointAnimInfo">
      <Name>ENTRY_POINT_ANIM_TRUCK_GUARDIAN_REAR_LEFT</Name>
      <CommonClipSetMap ref="COMMON_CLIPSET_MAP_AUTOMOBILE_LEFT"/>
      <EntryClipSetMap ref="ENTRY_CLIPSET_MAP_TRUCK_SANDKING_REAR_LEFT"/>
      <ExitClipSetMap ref="EXIT_CLIPSET_MAP_TRUCK_SANDKING_REAR_LEFT"/>
      <AlternateTryLockedDoorClipId/>
      <AlternateForcedEntryClipId/>
      <AlternateJackFromOutSideClipId/>
      <AlternateBeJackedFromOutSideClipId/>
      <AlternateEntryPointClipSetId/>
      <EnterVehicleMoveNetwork>ENTER_VEHICLE_STD</EnterVehicleMoveNetwork>
      <EntryTranslation x="-1.444000" y="-0.550000" z="-0.300000"/>
      <OpenDoorTranslation x="0.000000" y="0.000000"/>
      <OpenDoorHeadingChange value="0.000000"/>
      <EntryHeadingChange value="-1.570000"/>
      <ExtraZForWaterEntry value="0.000000"/>
      <EntryPointFlags>JackIncludesGetIn ForcedEntryIncludesGetIn UsesNoDoorTransitionForEnter UsesNoDoorTransitionForExit FixUpMoverToEntryPointOnExit UseGetUpAfterJack JackVariationsIncludeGetIn DeadJackIncludesGetIn</EntryPointFlags>
      <EntryAnimVariations ref="NULL"/>
    </Item>
    <Item type="CVehicleEntryPointAnimInfo">
      <Name>ENTRY_POINT_ANIM_TRUCK_GUARDIAN_REAR_RIGHT</Name>
      <CommonClipSetMap ref="COMMON_CLIPSET_MAP_AUTOMOBILE_RIGHT"/>
      <EntryClipSetMap ref="ENTRY_CLIPSET_MAP_TRUCK_MIXER_FRONT_RIGHT"/>
      <ExitClipSetMap ref="EXIT_CLIPSET_MAP_TRUCK_MIXER_FRONT_RIGHT"/>
      <AlternateTryLockedDoorClipId/>
      <AlternateForcedEntryClipId/>
      <AlternateJackFromOutSideClipId/>
      <AlternateBeJackedFromOutSideClipId/>
      <AlternateEntryPointClipSetId/>
      <EnterVehicleMoveNetwork>ENTER_VEHICLE_STD</EnterVehicleMoveNetwork>
      <EntryTranslation x="1.569300" y="-0.550000" z="-0.300000"/>
      <OpenDoorTranslation x="0.000000" y="0.000000"/>
      <OpenDoorHeadingChange value="0.000000"/>
      <EntryHeadingChange value="1.570000"/>
      <ExtraZForWaterEntry value="0.000000"/>
      <EntryPointFlags>JackIncludesGetIn ForcedEntryIncludesGetIn UsesNoDoorTransitionForEnter UsesNoDoorTransitionForExit UseGetUpAfterJack JackVariationsIncludeGetIn DeadJackIncludesGetIn</EntryPointFlags>
      <EntryAnimVariations ref="NULL"/>
    </Item>
    <Item type="CVehicleEntryPointAnimInfo">
      <Name>ENTRY_POINT_ANIM_TRUCK_GUARDIAN_BACK_LEFT</Name>
      <CommonClipSetMap ref="COMMON_CLIPSET_MAP_AUTOMOBILE_LEFT"/>
      <EntryClipSetMap ref="ENTRY_CLIPSET_MAP_TRUCK_GUARDIAN_BACK_LEFT"/>
      <ExitClipSetMap ref="EXIT_CLIPSET_MAP_TRUCK_GUARDIAN_BACK_LEFT"/>
      <AlternateTryLockedDoorClipId/>
      <AlternateForcedEntryClipId/>
      <AlternateJackFromOutSideClipId/>
      <AlternateBeJackedFromOutSideClipId/>
      <AlternateEntryPointClipSetId/>
      <EnterVehicleMoveNetwork>ENTER_VEHICLE_STD</EnterVehicleMoveNetwork>
      <EntryTranslation x="0.455000" y="-0.990000" z="-0.425000"/>
      <OpenDoorTranslation x="0.000000" y="0.000000"/>
      <OpenDoorHeadingChange value="0.000000"/>
      <EntryHeadingChange value="0.000000"/>
      <ExtraZForWaterEntry value="0.000000"/>
      <EntryPointFlags>JackIncludesGetIn UseGetUpAfterJack JackVariationsIncludeGetIn DeadJackIncludesGetIn</EntryPointFlags>
      <EntryAnimVariations ref="NULL"/>
    </Item>
    <Item type="CVehicleEntryPointAnimInfo">
      <Name>ENTRY_POINT_ANIM_TRUCK_GUARDIAN_BACK_RIGHT</Name>
      <CommonClipSetMap ref="COMMON_CLIPSET_MAP_AUTOMOBILE_RIGHT"/>
      <EntryClipSetMap ref="ENTRY_CLIPSET_MAP_TRUCK_GUARDIAN_BACK_RIGHT"/>
      <ExitClipSetMap ref="EXIT_CLIPSET_MAP_TRUCK_GUARDIAN_BACK_RIGHT"/>
      <AlternateTryLockedDoorClipId/>
      <AlternateForcedEntryClipId/>
      <AlternateJackFromOutSideClipId/>
      <AlternateBeJackedFromOutSideClipId/>
      <AlternateEntryPointClipSetId/>
      <EnterVehicleMoveNetwork>ENTER_VEHICLE_STD</EnterVehicleMoveNetwork>
      <EntryTranslation x="-0.205000" y="-0.970000" z="-0.400000"/>
      <OpenDoorTranslation x="0.000000" y="0.000000"/>
      <OpenDoorHeadingChange value="0.000000"/>
      <EntryHeadingChange value="0.000000"/>
      <ExtraZForWaterEntry value="0.000000"/>
      <EntryPointFlags>JackIncludesGetIn UseGetUpAfterJack JackVariationsIncludeGetIn DeadJackIncludesGetIn</EntryPointFlags>
      <EntryAnimVariations ref="NULL"/>
    </Item>
    <Item type="CVehicleEntryPointAnimInfo">
      <Name>ENTRY_POINT_ANIM_STD_TECHNICAL_REAR_LEFT_TURRET</Name>
      <CommonClipSetMap ref="COMMON_CLIPSET_MAP_STD_TECHNICAL_TURRET"/>
      <EntryClipSetMap ref="ENTRY_CLIPSET_MAP_STD_TECHNICAL_REAR_LEFT_TURRET"/>
      <ExitClipSetMap ref="EXIT_CLIPSET_MAP_STD_TECHNICAL_REAR_LEFT_TURRET"/>
      <AlternateTryLockedDoorClipId/>
      <AlternateForcedEntryClipId/>
      <AlternateJackFromOutSideClipId/>
      <AlternateBeJackedFromOutSideClipId/>
      <AlternateEntryPointClipSetId/>
      <EnterVehicleMoveNetwork>ENTER_VEHICLE_STD</EnterVehicleMoveNetwork>
      <EntryTranslation x="-1.575" y="-1.325" z="0.500"/>
      <OpenDoorTranslation x="0.000000" y="0.000000"/>
      <OpenDoorHeadingChange value="0.000000"/>
      <EntryHeadingChange value="-1.571"/>
      <ExtraZForWaterEntry value="0.000000"/>
      <EntryPointFlags>PreventJackInterrupt HasClimbUp JackIncludesGetIn UseNewPlaneSystem DontCloseDoorInside HasOnVehicleEntry HasGetOutToVehicle UseGetUpAfterJack JackVariationsIncludeGetIn DeadJackIncludesGetIn UseVehicleRelativeEntryPosition</EntryPointFlags>
      <EntryAnimVariations ref="NULL"/>
    </Item>
    <Item type="CVehicleEntryPointAnimInfo">
      <Name>ENTRY_POINT_ANIM_STD_TECHNICAL_REAR_RIGHT_TURRET</Name>
      <CommonClipSetMap ref="COMMON_CLIPSET_MAP_STD_TECHNICAL_TURRET"/>
      <EntryClipSetMap ref="ENTRY_CLIPSET_MAP_STD_TECHNICAL_REAR_RIGHT_TURRET"/>
      <ExitClipSetMap ref="EXIT_CLIPSET_MAP_STD_TECHNICAL_REAR_RIGHT_TURRET"/>
      <AlternateTryLockedDoorClipId/>
      <AlternateForcedEntryClipId/>
      <AlternateJackFromOutSideClipId/>
      <AlternateBeJackedFromOutSideClipId/>
      <AlternateEntryPointClipSetId/>
      <EnterVehicleMoveNetwork>ENTER_VEHICLE_STD</EnterVehicleMoveNetwork>
      <EntryTranslation x="1.575" y="-1.325" z="0.500"/>
      <OpenDoorTranslation x="0.000000" y="0.000000"/>
      <OpenDoorHeadingChange value="0.000000"/>
      <EntryHeadingChange value="1.571"/>
      <ExtraZForWaterEntry value="0.000000"/>
      <EntryPointFlags>PreventJackInterrupt HasClimbUp JackIncludesGetIn UseNewPlaneSystem DontCloseDoorInside HasOnVehicleEntry HasGetOutToVehicle UseGetUpAfterJack JackVariationsIncludeGetIn DeadJackIncludesGetIn UseVehicleRelativeEntryPosition</EntryPointFlags>
      <EntryAnimVariations ref="NULL"/>
    </Item>
    <Item type="CVehicleEntryPointAnimInfo">
      <Name>ENTRY_POINT_ANIM_STD_TECHNICAL_REAR_TURRET</Name>
      <CommonClipSetMap ref="COMMON_CLIPSET_MAP_STD_TECHNICAL_TURRET"/>
      <EntryClipSetMap ref="ENTRY_CLIPSET_MAP_STD_TECHNICAL_REAR_TURRET"/>
      <ExitClipSetMap ref="EXIT_CLIPSET_MAP_STD_TECHNICAL_REAR_TURRET"/>
      <AlternateTryLockedDoorClipId/>
      <AlternateForcedEntryClipId/>
      <AlternateJackFromOutSideClipId/>
      <AlternateBeJackedFromOutSideClipId/>
      <AlternateEntryPointClipSetId/>
      <EnterVehicleMoveNetwork>ENTER_VEHICLE_STD</EnterVehicleMoveNetwork>
      <EntryTranslation x="0.050" y="-2.900" z="0.500"/>
      <OpenDoorTranslation x="0.000000" y="0.000000"/>
      <OpenDoorHeadingChange value="0.000000"/>
      <EntryHeadingChange value="0.000"/>
      <ExtraZForWaterEntry value="0.000000"/>
      <EntryPointFlags>PreventJackInterrupt HasClimbUp JackIncludesGetIn UseNewPlaneSystem DontCloseDoorInside HasOnVehicleEntry HasGetOutToVehicle UseGetUpAfterJack JackVariationsIncludeGetIn DeadJackIncludesGetIn UseVehicleRelativeEntryPosition</EntryPointFlags>
      <EntryAnimVariations ref="NULL"/>
    </Item>
    <Item type="CVehicleEntryPointAnimInfo">
      <Name>ENTRY_POINT_ANIM_VAN_SLAMVAN_REAR_LEFT</Name>
      <CommonClipSetMap ref="COMMON_CLIPSET_MAP_TRUCK_RIGHT"/>
      <EntryClipSetMap ref="ENTRY_CLIPSET_MAP_VAN_REAR_LEFT"/>
      <ExitClipSetMap ref="EXIT_CLIPSET_MAP_VAN_REAR_LEFT"/>
      <AlternateTryLockedDoorClipId/>
      <AlternateForcedEntryClipId/>
      <AlternateJackFromOutSideClipId/>
      <AlternateBeJackedFromOutSideClipId/>
      <AlternateEntryPointClipSetId/>
      <EnterVehicleMoveNetwork>ENTER_VEHICLE_STD</EnterVehicleMoveNetwork>
      <EntryTranslation x="1.777000" y="-0.100000" z="0.300000"/>
      <OpenDoorTranslation x="0.000000" y="0.000000"/>
      <OpenDoorHeadingChange value="0.000000"/>
      <EntryHeadingChange value="1.570800"/>
      <ExtraZForWaterEntry value="0.000000"/>
      <EntryPointFlags>JackIncludesGetIn UseOpenDoorBlendAnims UseGetUpAfterJack JackVariationsIncludeGetIn DeadJackIncludesGetIn</EntryPointFlags>
      <EntryAnimVariations ref="ENTRY_ANIM_VARIATIONS_VAN_REAR_LEFT"/>
    </Item>
    <Item type="CVehicleEntryPointAnimInfo">
      <Name>ENTRY_POINT_ANIM_VAN_SLAMVAN_REAR_RIGHT</Name>
      <CommonClipSetMap ref="COMMON_CLIPSET_MAP_TRUCK_LEFT"/>
      <EntryClipSetMap ref="ENTRY_CLIPSET_MAP_VAN_REAR_RIGHT"/>
      <ExitClipSetMap ref="EXIT_CLIPSET_MAP_VAN_REAR_RIGHT"/>
      <AlternateTryLockedDoorClipId/>
      <AlternateForcedEntryClipId/>
      <AlternateJackFromOutSideClipId/>
      <AlternateBeJackedFromOutSideClipId/>
      <AlternateEntryPointClipSetId/>
      <EnterVehicleMoveNetwork>ENTER_VEHICLE_STD</EnterVehicleMoveNetwork>
      <EntryTranslation x="-1.770359" y="0.176154" z="0.300000"/>
      <OpenDoorTranslation x="0.000000" y="0.000000"/>
      <OpenDoorHeadingChange value="0.000000"/>
      <EntryHeadingChange value="-1.570800"/>
      <ExtraZForWaterEntry value="0.000000"/>
      <EntryPointFlags>JackIncludesGetIn UseOpenDoorBlendAnims UseGetUpAfterJack JackVariationsIncludeGetIn DeadJackIncludesGetIn</EntryPointFlags>
      <EntryAnimVariations ref="ENTRY_ANIM_VARIATIONS_VAN_REAR_RIGHT"/>
    </Item>
  </VehicleEntryPointAnimInfos>
  <VehicleExplosionInfos/>
  <VehicleLayoutInfos>
    <Item type="CVehicleLayoutInfo">
      <Name>LAYOUT_LOW_LE7B</Name>
      <Seats>
        <Item>
          <SeatInfo ref="SEAT_LOW_FRONT_LEFT"/>
          <SeatAnimInfo ref="SEAT_ANIM_LOW_RESTRICTED_NOAMBIENT_FRONT_LEFT"/>
        </Item>
        <Item>
          <SeatInfo ref="SEAT_LOW_FRONT_RIGHT"/>
          <SeatAnimInfo ref="SEAT_ANIM_LOW_RESTRICTED_NOAMBIENT_FRONT_RIGHT"/>
        </Item>
      </Seats>
      <EntryPoints>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_LOW_FRONT_LEFT"/>
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_LOW_LE7B_FRONT_LEFT"/>
        </Item>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_LOW_FRONT_RIGHT"/>
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_LOW_LE7B_FRONT_RIGHT"/>
        </Item>
      </EntryPoints>
      <LayoutFlags>StreamAnims NoArmIkOnInsideCloseDoor NoArmIkOnOutsideCloseDoor UseLeanSteerAnims UseSteeringWheelIk UseFinerAlignTolerance</LayoutFlags>
      <BicycleInfo ref="NULL"/>
      <AnimRateSet ref="STD_LOW_ANIM_RATE_SET"/>
      <HandsUpClipSetId>busted_vehicle_low</HandsUpClipSetId>
      <SteeringWheelOffset x="0.000000" y="0.350000" z="0.320000"/>
      <MaxXAcceleration value="15.000000"/>
      <BodyLeanXApproachSpeed value="10.000000"/>
      <BodyLeanXSmallDelta value="0.400000"/>
      <LookBackApproachSpeedScale value="1.000000"/>
      <FirstPersonAdditiveIdleClipSets>
        <Item>clipset@veh@low@ds@idle_a</Item>
        <Item>clipset@veh@low@ds@idle_b</Item>
        <Item>clipset@veh@low@ds@idle_c</Item>
        <Item>clipset@veh@low@ds@idle_d</Item>
        <Item>clipset@veh@low@ds@idle_e</Item>
      </FirstPersonAdditiveIdleClipSets>
      <FirstPersonRoadRageClipSets>
        <Item>clipset@veh@low@ds@hit_wheel@idle_a</Item>
        <Item>clipset@veh@low@ds@hit_wheel@idle_b</Item>
        <Item>clipset@veh@low@ds@hit_wheel@idle_c</Item>
      </FirstPersonRoadRageClipSets>
      <CellphoneClipsets>
        <CellphoneClipsetDS>ANIM_GROUP_CELLPHONE_IN_CAR_DS</CellphoneClipsetDS>
        <CellphoneClipsetPS>ANIM_GROUP_CELLPHONE_IN_CAR_PS</CellphoneClipsetPS>
        <CellphoneClipsetDS_FPS>ANIM_GROUP_CELLPHONE_IN_CAR_DS_FPS</CellphoneClipsetDS_FPS>
        <CellphoneClipsetPS_FPS>ANIM_GROUP_CELLPHONE_IN_CAR_DS_FPS</CellphoneClipsetPS_FPS>
      </CellphoneClipsets>
    </Item>
    <Item type="CVehicleLayoutInfo">
      <Name>LAYOUT_STD_BRIOSO</Name>
      <Seats>
        <Item>
          <SeatInfo ref="SEAT_STANDARD_FRONT_LEFT"/>
          <SeatAnimInfo ref="SEAT_ANIM_STD_BRIOSO_FRONT_LEFT"/>
        </Item>
        <Item>
          <SeatInfo ref="SEAT_STANDARD_FRONT_RIGHT"/>
          <SeatAnimInfo ref="SEAT_ANIM_STD_ZTYPE_FRONT_RIGHT"/>
        </Item>
      </Seats>
      <EntryPoints>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_STANDARD_FRONT_LEFT"/>
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_STD_HABANERO_FRONT_LEFT"/>
        </Item>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_STANDARD_FRONT_RIGHT"/>
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_STD_HABANERO_FRONT_RIGHT"/>
        </Item>
      </EntryPoints>
      <LayoutFlags>StreamAnims UseDoorOscillation UseLeanSteerAnims UseSteeringWheelIk UseFinerAlignTolerance</LayoutFlags>
      <BicycleInfo ref="NULL"/>
      <AnimRateSet ref="STD_LOW_ANIM_RATE_SET"/>
      <HandsUpClipSetId>busted_vehicle_std</HandsUpClipSetId>
      <SteeringWheelOffset x="0.000000" y="0.350000" z="0.320000"/>
      <MaxXAcceleration value="4.000000"/>
      <BodyLeanXApproachSpeed value="5.000000"/>
      <BodyLeanXSmallDelta value="0.300000"/>
      <LookBackApproachSpeedScale value="1.000000"/>
      <FirstPersonAdditiveIdleClipSets>
        <Item>clipset@veh@std@ds@idle_a</Item>
        <Item>clipset@veh@std@ds@idle_b</Item>
        <Item>clipset@veh@std@ds@idle_c</Item>
        <Item>clipset@veh@std@ds@idle_d</Item>
        <Item>clipset@veh@std@ds@idle_e</Item>
      </FirstPersonAdditiveIdleClipSets>
      <FirstPersonRoadRageClipSets>
        <Item>clipset@veh@std@ds@hit_wheel@idle_a</Item>
        <Item>clipset@veh@std@ds@hit_wheel@idle_b</Item>
        <Item>clipset@veh@std@ds@hit_wheel@idle_c</Item>
      </FirstPersonRoadRageClipSets>
      <CellphoneClipsets>
        <CellphoneClipsetDS>ANIM_GROUP_CELLPHONE_IN_CAR_DS</CellphoneClipsetDS>
        <CellphoneClipsetPS>ANIM_GROUP_CELLPHONE_IN_CAR_PS</CellphoneClipsetPS>
        <CellphoneClipsetDS_FPS>ANIM_GROUP_CELLPHONE_IN_CAR_DS_FPS</CellphoneClipsetDS_FPS>
        <CellphoneClipsetPS_FPS>ANIM_GROUP_CELLPHONE_IN_CAR_DS_FPS</CellphoneClipsetPS_FPS>
      </CellphoneClipsets>
    </Item>
    <Item type="CVehicleLayoutInfo">
      <Name>LAYOUT_STD_OMNIS</Name>
      <Seats>
        <Item>
          <SeatInfo ref="SEAT_STANDARD_FRONT_LEFT"/>
          <SeatAnimInfo ref="SEAT_ANIM_STD_OMNIS_FRONT_LEFT"/>
        </Item>
        <Item>
          <SeatInfo ref="SEAT_STANDARD_FRONT_RIGHT"/>
          <SeatAnimInfo ref="SEAT_ANIM_STD_OMNIS_FRONT_RIGHT"/>
        </Item>
      </Seats>
      <EntryPoints>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_STANDARD_FRONT_LEFT"/>
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_STANDARD_FRONT_LEFT"/>
        </Item>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_STANDARD_FRONT_RIGHT"/>
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_STANDARD_FRONT_RIGHT"/>
        </Item>
      </EntryPoints>
      <LayoutFlags>StreamAnims UseDoorOscillation UseLeanSteerAnims UseSteeringWheelIk</LayoutFlags>
      <BicycleInfo ref="NULL"/>
      <AnimRateSet ref="STD_LOW_ANIM_RATE_SET"/>
      <HandsUpClipSetId>busted_vehicle_std</HandsUpClipSetId>
      <SteeringWheelOffset x="0.000000" y="0.350000" z="0.320000"/>
      <MaxXAcceleration value="4.000000"/>
      <BodyLeanXApproachSpeed value="5.000000"/>
      <BodyLeanXSmallDelta value="0.300000"/>
      <LookBackApproachSpeedScale value="1.000000"/>
      <FirstPersonAdditiveIdleClipSets>
        <Item>clipset@veh@std@ds@idle_a</Item>
        <Item>clipset@veh@std@ds@idle_b</Item>
        <Item>clipset@veh@std@ds@idle_c</Item>
        <Item>clipset@veh@std@ds@idle_d</Item>
        <Item>clipset@veh@std@ds@idle_e</Item>
      </FirstPersonAdditiveIdleClipSets>
      <FirstPersonRoadRageClipSets>
        <Item>clipset@veh@std@ds@hit_wheel@idle_a</Item>
        <Item>clipset@veh@std@ds@hit_wheel@idle_b</Item>
        <Item>clipset@veh@std@ds@hit_wheel@idle_c</Item>
      </FirstPersonRoadRageClipSets>
      <CellphoneClipsets>
        <CellphoneClipsetDS>ANIM_GROUP_CELLPHONE_IN_CAR_DS</CellphoneClipsetDS>
        <CellphoneClipsetPS>ANIM_GROUP_CELLPHONE_IN_CAR_PS</CellphoneClipsetPS>
        <CellphoneClipsetDS_FPS>ANIM_GROUP_CELLPHONE_IN_CAR_DS_FPS</CellphoneClipsetDS_FPS>
        <CellphoneClipsetPS_FPS>ANIM_GROUP_CELLPHONE_IN_CAR_DS_FPS</CellphoneClipsetPS_FPS>
      </CellphoneClipsets>
    </Item>
    <Item type="CVehicleLayoutInfo">
      <Name>LAYOUT_STD_TROPHY</Name>
      <Seats>
        <Item>
          <SeatInfo ref="SEAT_STANDARD_FRONT_LEFT"/>
          <SeatAnimInfo ref="SEAT_ANIM_STD_HIGHWINDOW_FRONT_LEFT"/>
        </Item>
        <Item>
          <SeatInfo ref="SEAT_STANDARD_FRONT_RIGHT"/>
          <SeatAnimInfo ref="SEAT_ANIM_STD_HIGHWINDOW_FRONT_RIGHT"/>
        </Item>
        <Item>
          <SeatInfo ref="SEAT_STANDARD_REAR_LEFT"/>
          <SeatAnimInfo ref="SEAT_ANIM_STD_HIGHWINDOW_REAR_LEFT"/>
        </Item>
        <Item>
          <SeatInfo ref="SEAT_STANDARD_REAR_RIGHT"/>
          <SeatAnimInfo ref="SEAT_ANIM_STD_HIGHWINDOW_REAR_RIGHT"/>
        </Item>
      </Seats>
      <EntryPoints>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_STANDARD_FRONT_LEFT"/>
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_STD_BOBCAT_FRONT_LEFT"/>
        </Item>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_STANDARD_FRONT_RIGHT"/>
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_STD_BOBCAT_FRONT_RIGHT"/>
        </Item>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_STANDARD_REAR_LEFT"/>
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_STD_BOBCAT_REAR_LEFT"/>
        </Item>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_STANDARD_REAR_RIGHT"/>
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_STD_BOBCAT_REAR_RIGHT"/>
        </Item>
      </EntryPoints>
      <LayoutFlags>StreamAnims UseLeanSteerAnims UseSteeringWheelIk UseFinerAlignTolerance</LayoutFlags>
      <BicycleInfo ref="NULL"/>
      <AnimRateSet ref="STD_LOW_ANIM_RATE_SET"/>
      <HandsUpClipSetId>busted_vehicle_std</HandsUpClipSetId>
      <SteeringWheelOffset x="0.000000" y="0.350000" z="0.320000"/>
      <MaxXAcceleration value="4.000000"/>
      <BodyLeanXApproachSpeed value="5.000000"/>
      <BodyLeanXSmallDelta value="0.300000"/>
      <LookBackApproachSpeedScale value="1.000000"/>
      <FirstPersonAdditiveIdleClipSets>
        <Item>clipset@veh@std@ds@idle_a</Item>
        <Item>clipset@veh@std@ds@idle_b</Item>
        <Item>clipset@veh@std@ds@idle_c</Item>
        <Item>clipset@veh@std@ds@idle_d</Item>
        <Item>clipset@veh@std@ds@idle_e</Item>
      </FirstPersonAdditiveIdleClipSets>
      <FirstPersonRoadRageClipSets>
        <Item>clipset@veh@std@ds@hit_wheel@idle_a</Item>
        <Item>clipset@veh@std@ds@hit_wheel@idle_b</Item>
        <Item>clipset@veh@std@ds@hit_wheel@idle_c</Item>
      </FirstPersonRoadRageClipSets>
      <CellphoneClipsets>
        <CellphoneClipsetDS>ANIM_GROUP_CELLPHONE_IN_CAR_DS</CellphoneClipsetDS>
        <CellphoneClipsetPS>ANIM_GROUP_CELLPHONE_IN_CAR_PS</CellphoneClipsetPS>
        <CellphoneClipsetDS_FPS>ANIM_GROUP_CELLPHONE_IN_CAR_DS_FPS</CellphoneClipsetDS_FPS>
        <CellphoneClipsetPS_FPS>ANIM_GROUP_CELLPHONE_IN_CAR_DS_FPS</CellphoneClipsetPS_FPS>
      </CellphoneClipsets>
    </Item>
    <Item type="CVehicleLayoutInfo">
      <Name>LAYOUT_HELI_SWIFT2</Name>
      <Seats>
        <Item>
          <SeatInfo ref="SEAT_STANDARD_FRONT_LEFT"/>
          <SeatAnimInfo ref="SEAT_ANIM_HELI_FRONT_LEFT"/>
        </Item>
        <Item>
          <SeatInfo ref="SEAT_STANDARD_FRONT_RIGHT"/>
          <SeatAnimInfo ref="SEAT_ANIM_HELI_FRONT_RIGHT"/>
        </Item>
        <Item>
          <SeatInfo ref="SEAT_HELI_SWIFT2_REAR_LEFT"/>
          <SeatAnimInfo ref="SEAT_ANIM_HELI_SWIFT2_REAR_LEFT"/>
        </Item>
        <Item>
          <SeatInfo ref="SEAT_HELI_SWIFT2_REAR_RIGHT"/>
          <SeatAnimInfo ref="SEAT_ANIM_HELI_SWIFT2_REAR_RIGHT"/>
        </Item>
      </Seats>
      <EntryPoints>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_STANDARD_FRONT_LEFT"/>
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_HELI_SWIFT2_FRONT_LEFT"/>
        </Item>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_STANDARD_FRONT_RIGHT"/>
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_HELI_SWIFT2_FRONT_RIGHT"/>
        </Item>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_HELI_SWIFT2_REAR_LEFT"/>
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_HELI_SWIFT2_REAR_LEFT"/>
        </Item>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_HELI_SWIFT2_REAR_RIGHT"/>
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_HELI_SWIFT2_REAR_RIGHT"/>
        </Item>
      </EntryPoints>
      <LayoutFlags>StreamAnims UseFinerAlignTolerance Use2DBodyBlend</LayoutFlags>
      <BicycleInfo ref="NULL"/>
      <AnimRateSet ref="NULL"/>
      <HandsUpClipSetId>busted_vehicle_std</HandsUpClipSetId>
      <SteeringWheelOffset x="0.000000" y="0.350000" z="0.320000"/>
      <MaxXAcceleration value="25.000000"/>
      <BodyLeanXApproachSpeed value="15.000000"/>
      <BodyLeanXSmallDelta value="0.100000"/>
      <LookBackApproachSpeedScale value="1.000000"/>
      <FirstPersonAdditiveIdleClipSets/>
      <FirstPersonRoadRageClipSets/>
      <CellphoneClipsets>
        <CellphoneClipsetDS>ANIM_GROUP_CELLPHONE_IN_CAR_DS</CellphoneClipsetDS>
        <CellphoneClipsetPS>ANIM_GROUP_CELLPHONE_IN_CAR_PS</CellphoneClipsetPS>
        <CellphoneClipsetDS_FPS>ANIM_GROUP_CELLPHONE_IN_CAR_DS_FPS</CellphoneClipsetDS_FPS>
        <CellphoneClipsetPS_FPS>ANIM_GROUP_CELLPHONE_IN_CAR_DS_FPS</CellphoneClipsetPS_FPS>
      </CellphoneClipsets>
    </Item>
    <Item type="CVehicleLayoutInfo">
      <Name>LAYOUT_LOW_FELTZER3</Name>
      <Seats>
        <Item>
          <SeatInfo ref="SEAT_STANDARD_FRONT_LEFT"/>
          <SeatAnimInfo ref="SEAT_ANIM_LOW_RESTRICTED_FRONT_LEFT"/>
        </Item>
        <Item>
          <SeatInfo ref="SEAT_STANDARD_FRONT_RIGHT"/>
          <SeatAnimInfo ref="SEAT_ANIM_LOW_RESTRICTED_FRONT_RIGHT"/>
        </Item>
      </Seats>
      <EntryPoints>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_STANDARD_FRONT_LEFT"/>
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_LOW_FELTZER3_FRONT_LEFT"/>
        </Item>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_STANDARD_FRONT_RIGHT"/>
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_LOW_FELTZER3_FRONT_RIGHT"/>
        </Item>
      </EntryPoints>
      <LayoutFlags>StreamAnims UseLeanSteerAnims UseSteeringWheelIk UseFinerAlignTolerance</LayoutFlags>
      <BicycleInfo ref="NULL"/>
      <AnimRateSet ref="STD_LOW_ANIM_RATE_SET"/>
      <HandsUpClipSetId>busted_vehicle_low</HandsUpClipSetId>
      <SteeringWheelOffset x="0.000000" y="0.350000" z="0.320000"/>
      <MaxXAcceleration value="15.000000"/>
      <BodyLeanXApproachSpeed value="10.000000"/>
      <BodyLeanXSmallDelta value="0.400000"/>
      <LookBackApproachSpeedScale value="1.000000"/>
      <FirstPersonAdditiveIdleClipSets>
        <Item>clipset@veh@low@ds@idle_a</Item>
        <Item>clipset@veh@low@ds@idle_b</Item>
        <Item>clipset@veh@low@ds@idle_c</Item>
        <Item>clipset@veh@low@ds@idle_d</Item>
        <Item>clipset@veh@low@ds@idle_e</Item>
      </FirstPersonAdditiveIdleClipSets>
      <FirstPersonRoadRageClipSets>
        <Item>clipset@veh@low@ds@hit_wheel@idle_a</Item>
        <Item>clipset@veh@low@ds@hit_wheel@idle_b</Item>
        <Item>clipset@veh@low@ds@hit_wheel@idle_c</Item>
      </FirstPersonRoadRageClipSets>
      <CellphoneClipsets>
        <CellphoneClipsetDS>ANIM_GROUP_CELLPHONE_IN_CAR_DS</CellphoneClipsetDS>
        <CellphoneClipsetPS>ANIM_GROUP_CELLPHONE_IN_CAR_PS</CellphoneClipsetPS>
        <CellphoneClipsetDS_FPS>ANIM_GROUP_CELLPHONE_IN_CAR_LOW_FPS</CellphoneClipsetDS_FPS>
        <CellphoneClipsetPS_FPS>ANIM_GROUP_CELLPHONE_IN_CAR_DS_FPS</CellphoneClipsetPS_FPS>
      </CellphoneClipsets>
    </Item>
    <Item type="CVehicleLayoutInfo">
      <Name>LAYOUT_LOW_OSIRIS</Name>
      <Seats>
        <Item>
          <SeatInfo ref="SEAT_STANDARD_FRONT_LEFT"/>
          <SeatAnimInfo ref="SEAT_ANIM_LOW_RESTRICTED_FRONT_LEFT"/>
        </Item>
        <Item>
          <SeatInfo ref="SEAT_STANDARD_FRONT_RIGHT"/>
          <SeatAnimInfo ref="SEAT_ANIM_LOW_RESTRICTED_FRONT_RIGHT"/>
        </Item>
      </Seats>
      <EntryPoints>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_STANDARD_FRONT_LEFT"/>
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_LOW_OSIRIS_FRONT_LEFT"/>
        </Item>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_STANDARD_FRONT_RIGHT"/>
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_LOW_OSIRIS_FRONT_RIGHT"/>
        </Item>
      </EntryPoints>
      <LayoutFlags>StreamAnims NoArmIkOnInsideCloseDoor NoArmIkOnOutsideCloseDoor UseLeanSteerAnims UseSteeringWheelIk UseFinerAlignTolerance AutomaticCloseDoor</LayoutFlags>
      <BicycleInfo ref="NULL"/>
      <AnimRateSet ref="STD_LOW_ANIM_RATE_SET"/>
      <HandsUpClipSetId>busted_vehicle_low</HandsUpClipSetId>
      <SteeringWheelOffset x="0.000000" y="0.350000" z="0.320000"/>
      <MaxXAcceleration value="15.000000"/>
      <BodyLeanXApproachSpeed value="10.000000"/>
      <BodyLeanXSmallDelta value="0.400000"/>
      <LookBackApproachSpeedScale value="1.000000"/>
      <FirstPersonAdditiveIdleClipSets>
        <Item>clipset@veh@low@ds@idle_a</Item>
        <Item>clipset@veh@low@ds@idle_b</Item>
        <Item>clipset@veh@low@ds@idle_c</Item>
        <Item>clipset@veh@low@ds@idle_d</Item>
        <Item>clipset@veh@low@ds@idle_e</Item>
      </FirstPersonAdditiveIdleClipSets>
      <FirstPersonRoadRageClipSets>
        <Item>clipset@veh@low@ds@hit_wheel@idle_a</Item>
        <Item>clipset@veh@low@ds@hit_wheel@idle_b</Item>
        <Item>clipset@veh@low@ds@hit_wheel@idle_c</Item>
      </FirstPersonRoadRageClipSets>
      <CellphoneClipsets>
        <CellphoneClipsetDS>ANIM_GROUP_CELLPHONE_IN_CAR_DS</CellphoneClipsetDS>
        <CellphoneClipsetPS>ANIM_GROUP_CELLPHONE_IN_CAR_PS</CellphoneClipsetPS>
        <CellphoneClipsetDS_FPS>ANIM_GROUP_CELLPHONE_IN_CAR_LOW_FPS</CellphoneClipsetDS_FPS>
        <CellphoneClipsetPS_FPS>ANIM_GROUP_CELLPHONE_IN_CAR_DS_FPS</CellphoneClipsetPS_FPS>
      </CellphoneClipsets>
    </Item>
    <Item type="CVehicleLayoutInfo">
      <Name>LAYOUT_PLANE_LUXOR2</Name>
      <Seats>
        <Item>
          <SeatInfo ref="SEAT_STANDARD_FRONT_LEFT"/>
          <SeatAnimInfo ref="SEAT_ANIM_PLANE_JET_FRONT_LEFT"/>
        </Item>
        <Item>
          <SeatInfo ref="SEAT_STANDARD_FRONT_RIGHT"/>
          <SeatAnimInfo ref="SEAT_ANIM_PLANE_JET_FRONT_RIGHT"/>
        </Item>
        <Item>
          <SeatInfo ref="SEAT_STANDARD_NO_SHUFFLE_REAR_LEFT"/>
          <SeatAnimInfo ref="SEAT_ANIM_PLANE_LUXOR2_REAR_RIGHT"/>
        </Item>
        <Item>
          <SeatInfo ref="SEAT_PLANE_LUXOR2_NO_SHUFFLE_RIGHT_1"/>
          <SeatAnimInfo ref="SEAT_ANIM_PLANE_LUXOR2_REAR_LEFT"/>
        </Item>
        <Item>
          <SeatInfo ref="SEAT_EXTRA_NO_SHUFFLE_LEFT_1"/>
          <SeatAnimInfo ref="SEAT_ANIM_PLANE_LUXOR2_REAR_LEFT"/>
        </Item>
        <Item>
          <SeatInfo ref="SEAT_PLANE_LUXOR2_NO_SHUFFLE_REAR_RIGHT"/>
          <SeatAnimInfo ref="SEAT_ANIM_PLANE_LUXOR2_REAR_RIGHT"/>
        </Item>
        <Item>
          <SeatInfo ref="SEAT_EXTRA_NO_SHUFFLE_RIGHT_2"/>
          <SeatAnimInfo ref="SEAT_ANIM_PLANE_LUXOR2_REAR_LEFT"/>
        </Item>
        <Item>
          <SeatInfo ref="SEAT_EXTRA_NO_SHUFFLE_RIGHT_3"/>
          <SeatAnimInfo ref="SEAT_ANIM_PLANE_LUXOR2_REAR_RIGHT"/>
        </Item>
      </Seats>
      <EntryPoints>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_PLANE_JET_FRONT_LEFT"/>
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_PLANE_JET_FRONT_LEFT"/>
        </Item>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_PLANE_JET_FRONT_RIGHT"/>
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_PLANE_JET_FRONT_RIGHT"/>
        </Item>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_PLANE_LUXOR2_REAR_LEFT"/>
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_PLANE_LUXOR2_REAR_LEFT"/>
        </Item>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_PLANE_LUXOR2_REAR_RIGHT"/>
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_PLANE_LUXOR2_REAR_RIGHT"/>
        </Item>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_PLANE_LUXOR2_EXTRA_LEFT_1"/>
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_PLANE_LUXOR2_EXTRA_LEFT_1"/>
        </Item>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_PLANE_LUXOR2_EXTRA_RIGHT_1"/>
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_PLANE_LUXOR2_EXTRA_RIGHT_1"/>
        </Item>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_PLANE_LUXOR2_EXTRA_RIGHT_2"/>
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_PLANE_LUXOR2_EXTRA_RIGHT_2"/>
        </Item>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_PLANE_LUXOR2_EXTRA_RIGHT_3"/>
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_PLANE_LUXOR2_EXTRA_RIGHT_3"/>
        </Item>
      </EntryPoints>
      <LayoutFlags>StreamAnims AllowEarlyDoorAndSeatUnreservation DisableJackingAndBusting ClimbUpAfterOpenDoor UseFinerAlignTolerance</LayoutFlags>
      <BicycleInfo ref="NULL"/>
      <AnimRateSet ref="NULL"/>
      <HandsUpClipSetId>busted_vehicle_std</HandsUpClipSetId>
      <SteeringWheelOffset x="0.000000" y="0.350000" z="0.320000"/>
      <MaxXAcceleration value="25.000000"/>
      <BodyLeanXApproachSpeed value="15.000000"/>
      <BodyLeanXSmallDelta value="0.100000"/>
      <LookBackApproachSpeedScale value="1.000000"/>
      <FirstPersonAdditiveIdleClipSets/>
      <FirstPersonRoadRageClipSets/>
      <CellphoneClipsets>
        <CellphoneClipsetDS>ANIM_GROUP_CELLPHONE_IN_CAR_DS</CellphoneClipsetDS>
        <CellphoneClipsetPS>ANIM_GROUP_CELLPHONE_IN_CAR_PS</CellphoneClipsetPS>
        <CellphoneClipsetDS_FPS>ANIM_GROUP_CELLPHONE_IN_CAR_DS_FPS</CellphoneClipsetDS_FPS>
        <CellphoneClipsetPS_FPS>ANIM_GROUP_CELLPHONE_IN_CAR_DS_FPS</CellphoneClipsetPS_FPS>
      </CellphoneClipsets>
    </Item>
    <Item type="CVehicleLayoutInfo">
      <Name>LAYOUT_BOAT_DINGHY3</Name>
      <Seats>
        <Item>
          <SeatInfo ref="SEAT_BOAT_FRONT_LEFT"/>
          <SeatAnimInfo ref="SEAT_ANIM_BOAT_DINGHY_FRONT_LEFT"/>
        </Item>
        <Item>
          <SeatInfo ref="SEAT_BOAT_FRONT_RIGHT"/>
          <SeatAnimInfo ref="SEAT_ANIM_BOAT_DINGHY_FRONT_RIGHT"/>
        </Item>
        <Item>
          <SeatInfo ref="SEAT_BOAT_REAR_LEFT"/>
          <SeatAnimInfo ref="SEAT_ANIM_BOAT_DINGHY_FRONT_RIGHT"/>
        </Item>
        <Item>
          <SeatInfo ref="SEAT_BOAT_REAR_RIGHT"/>
          <SeatAnimInfo ref="SEAT_ANIM_BOAT_DINGHY_FRONT_RIGHT"/>
        </Item>
      </Seats>
      <EntryPoints>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_BOAT_DINGHY_FRONT_LEFT"/>
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_BOAT_DINGHY_FRONT_LEFT"/>
        </Item>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_BOAT_DINGHY_FRONT_RIGHT"/>
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_BOAT_DINGHY_FRONT_RIGHT"/>
        </Item>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_BOAT_DINGHY_REAR_LEFT"/>
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_BOAT_DINGHY_REAR_LEFT"/>
        </Item>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_BOAT_DINGHY_REAR_RIGHT"/>
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_BOAT_DINGHY_REAR_RIGHT"/>
        </Item>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_BOAT_DINGHY_REARCLIMB_LEFT"/>
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_BOAT_DINGHY_REARCLIMB_LEFT"/>
        </Item>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_BOAT_DINGHY_REARCLIMB_RIGHT"/>
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_BOAT_DINGHY_REARCLIMB_RIGHT"/>
        </Item>
      </EntryPoints>
      <LayoutFlags>StreamAnims UseLeanSteerAnims DisableJackingAndBusting</LayoutFlags>
      <BicycleInfo ref="NULL"/>
      <AnimRateSet ref="BOAT_ANIM_RATE_SET"/>
      <HandsUpClipSetId>busted_vehicle_std</HandsUpClipSetId>
      <SteeringWheelOffset x="0.000000" y="0.350000" z="0.320000"/>
    </Item>
    <Item type="CVehicleLayoutInfo">
      <Name>LAYOUT_HELI_SAVAGE</Name>
      <Seats>
        <Item>
          <SeatInfo ref="SEAT_SINGLE_FRONT"/>
          <SeatAnimInfo ref="SEAT_ANIM_HELI_SAVAGE_FRONT"/>
        </Item>
        <Item>
          <SeatInfo ref="SEAT_SINGLE_REAR"/>
          <SeatAnimInfo ref="SEAT_ANIM_HELI_SAVAGE_FRONT"/>
        </Item>
        <Item>
          <SeatInfo ref="SEAT_STANDARD_REAR_LEFT"/>
          <SeatAnimInfo ref="SEAT_ANIM_HELI_REAR_LEFT"/>
        </Item>
        <Item>
          <SeatInfo ref="SEAT_STANDARD_REAR_RIGHT"/>
          <SeatAnimInfo ref="SEAT_ANIM_HELI_REAR_RIGHT"/>
        </Item>
      </Seats>
      <EntryPoints>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_HELI_SAVAGE_FRONT"/>
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_HELI_SAVAGE_FRONT"/>
        </Item>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_HELI_SAVAGE_REAR"/>
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_HELI_SAVAGE_REAR"/>
        </Item>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_HELI_REAR_LEFT"/>
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_HELI_SAVAGE_REAR_LEFT"/>
        </Item>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_HELI_REAR_RIGHT"/>
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_HELI_SAVAGE_REAR_RIGHT"/>
        </Item>
      </EntryPoints>
      <LayoutFlags>StreamAnims DisableJackingAndBusting UseFinerAlignTolerance</LayoutFlags>
      <BicycleInfo ref="NULL"/>
      <HandsUpClipSetId>busted_vehicle_std</HandsUpClipSetId>
      <SteeringWheelOffset x="0.000000" y="0.350000" z="0.320000"/>
    </Item>
    <Item type="CVehicleLayoutInfo">
      <Name>LAYOUT_PLANE_VELUM2</Name>
      <Seats>
        <Item>
          <SeatInfo ref="SEAT_STANDARD_FRONT_LEFT"/>
          <SeatAnimInfo ref="SEAT_ANIM_PLANE_CUBAN_FRONT_LEFT"/>
        </Item>
        <Item>
          <SeatInfo ref="SEAT_STANDARD_FRONT_RIGHT"/>
          <SeatAnimInfo ref="SEAT_ANIM_PLANE_CUBAN_FRONT_RIGHT"/>
        </Item>
        <Item>
          <SeatInfo ref="SEAT_STANDARD_NO_SHUFFLE_REAR_LEFT"/>
          <SeatAnimInfo ref="SEAT_ANIM_PLANE_VELUM2_REAR_RIGHT"/>
        </Item>
        <Item>
          <SeatInfo ref="SEAT_EXTRA_NO_SHUFFLE_RIGHT_1"/>
          <SeatAnimInfo ref="SEAT_ANIM_PLANE_VELUM2_REAR_RIGHT"/>
        </Item>
        <Item>
          <SeatInfo ref="SEAT_STANDARD_NO_SHUFFLE_REAR_RIGHT"/>
          <SeatAnimInfo ref="SEAT_ANIM_PLANE_VELUM2_REAR_LEFT"/>
        </Item>
      </Seats>
      <EntryPoints>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_PLANE_VELUM_FRONT_LEFT"/>
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_PLANE_VELUM2_FRONT_LEFT"/>
        </Item>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_PLANE_VELUM_FRONT_RIGHT"/>
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_PLANE_VELUM_FRONT_RIGHT"/>
        </Item>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_PLANE_VELUM2_REAR_RIGHT"/>
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_PLANE_VELUM2_REAR_RIGHT"/>
        </Item>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_PLANE_VELUM2_REAR_BACK"/>
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_PLANE_VELUM2_REAR_BACK"/>
        </Item>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_PLANE_VELUM2_REAR_LEFT"/>
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_PLANE_VELUM2_REAR_LEFT"/>
        </Item>
      </EntryPoints>
      <LayoutFlags>StreamAnims DisableJackingAndBusting UseFinerAlignTolerance</LayoutFlags>
      <BicycleInfo ref="NULL"/>
      <HandsUpClipSetId>busted_vehicle_std</HandsUpClipSetId>
      <SteeringWheelOffset x="0.000000" y="0.350000" z="0.320000"/>
      <MaxXAcceleration value="50.000000"/>
      <BodyLeanXApproachSpeed value="1.500000"/>
      <BodyLeanXSmallDelta value="0.100000"/>
      <LookBackApproachSpeedScale value="1.000000"/>
    </Item>
    <Item type="CVehicleLayoutInfo">
      <Name>LAYOUT_STD_KURUMA</Name>
      <Seats>
        <Item>
          <SeatInfo ref="SEAT_STANDARD_FRONT_LEFT"/>
          <SeatAnimInfo ref="SEAT_ANIM_STANDARD_FRONT_LEFT"/>
        </Item>
        <Item>
          <SeatInfo ref="SEAT_STANDARD_FRONT_RIGHT"/>
          <SeatAnimInfo ref="SEAT_ANIM_STANDARD_FRONT_RIGHT"/>
        </Item>
        <Item>
          <SeatInfo ref="SEAT_STANDARD_REAR_LEFT"/>
          <SeatAnimInfo ref="SEAT_ANIM_STANDARD_REAR_LEFT"/>
        </Item>
        <Item>
          <SeatInfo ref="SEAT_STANDARD_REAR_RIGHT"/>
          <SeatAnimInfo ref="SEAT_ANIM_STANDARD_REAR_RIGHT"/>
        </Item>
      </Seats>
      <EntryPoints>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_STANDARD_FRONT_LEFT"/>
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_STD_EXITFIXUP_FRONT_LEFT"/>
        </Item>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_STANDARD_FRONT_RIGHT"/>
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_STD_EXITFIXUP_FRONT_RIGHT"/>
        </Item>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_STANDARD_REAR_LEFT"/>
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_STD_EXITFIXUP_REAR_LEFT"/>
        </Item>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_STANDARD_REAR_RIGHT"/>
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_STD_EXITFIXUP_REAR_RIGHT"/>
        </Item>
      </EntryPoints>
      <LayoutFlags>StreamAnims UseLeanSteerAnims UseSteeringWheelIk UseFinerAlignTolerance</LayoutFlags>
      <BicycleInfo ref="NULL"/>
      <AnimRateSet ref="STD_LOW_ANIM_RATE_SET"/>
      <HandsUpClipSetId>busted_vehicle_std</HandsUpClipSetId>
      <SteeringWheelOffset x="0.000000" y="0.350000" z="0.320000"/>
      <MaxXAcceleration value="4.00000"/>
      <BodyLeanXApproachSpeed value="5.00000"/>
      <BodyLeanXSmallDelta value="0.30000"/>
      <LookBackApproachSpeedScale value="1.00000"/>
      <FirstPersonAdditiveIdleClipSets>
        <Item>clipset@veh@std@ds@idle_a</Item>
        <Item>clipset@veh@std@ds@idle_b</Item>
        <Item>clipset@veh@std@ds@idle_c</Item>
        <Item>clipset@veh@std@ds@idle_d</Item>
        <Item>clipset@veh@std@ds@idle_e</Item>
      </FirstPersonAdditiveIdleClipSets>
      <FirstPersonRoadRageClipSets>
        <Item>clipset@veh@std@ds@hit_wheel@idle_a</Item>
        <Item>clipset@veh@std@ds@hit_wheel@idle_b</Item>
        <Item>clipset@veh@std@ds@hit_wheel@idle_c</Item>
      </FirstPersonRoadRageClipSets>
    </Item>
    <Item type="CVehicleLayoutInfo">
      <Name>LAYOUT_STD_KURUMA2</Name>
      <Seats>
        <Item>
          <SeatInfo ref="SEAT_STANDARD_FRONT_LEFT"/>
          <SeatAnimInfo ref="SEAT_ANIM_STD_KURUMA2_FRONT_LEFT"/>
        </Item>
        <Item>
          <SeatInfo ref="SEAT_STANDARD_FRONT_RIGHT"/>
          <SeatAnimInfo ref="SEAT_ANIM_STD_KURUMA2_FRONT_RIGHT"/>
        </Item>
        <Item>
          <SeatInfo ref="SEAT_STANDARD_REAR_LEFT"/>
          <SeatAnimInfo ref="SEAT_ANIM_STD_KURUMA2_REAR_LEFT"/>
        </Item>
        <Item>
          <SeatInfo ref="SEAT_STANDARD_REAR_RIGHT"/>
          <SeatAnimInfo ref="SEAT_ANIM_STD_KURUMA2_REAR_RIGHT"/>
        </Item>
      </Seats>
      <EntryPoints>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_STD_NOWINDOW_FRONT_LEFT"/>
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_STANDARD_FRONT_LEFT"/>
        </Item>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_STD_NOWINDOW_FRONT_RIGHT"/>
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_STANDARD_FRONT_RIGHT"/>
        </Item>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_STANDARD_REAR_LEFT"/>
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_STD_EXITFIXUP_REAR_LEFT"/>
        </Item>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_STANDARD_REAR_RIGHT"/>
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_STD_EXITFIXUP_REAR_RIGHT"/>
        </Item>
      </EntryPoints>
      <LayoutFlags>StreamAnims UseDoorOscillation UseLeanSteerAnims UseSteeringWheelIk UseFinerAlignTolerance DisableJackingAndBusting</LayoutFlags>
      <BicycleInfo ref="NULL"/>
      <AnimRateSet ref="STD_LOW_ANIM_RATE_SET"/>
      <HandsUpClipSetId>busted_vehicle_std</HandsUpClipSetId>
      <SteeringWheelOffset x="0.000000" y="0.350000" z="0.320000"/>
      <MaxXAcceleration value="4.00000"/>
      <BodyLeanXApproachSpeed value="5.00000"/>
      <BodyLeanXSmallDelta value="0.30000"/>
      <LookBackApproachSpeedScale value="1.00000"/>
      <FirstPersonAdditiveIdleClipSets>
        <Item>clipset@veh@std@ds@idle_a</Item>
        <Item>clipset@veh@std@ds@idle_b</Item>
        <Item>clipset@veh@std@ds@idle_c</Item>
        <Item>clipset@veh@std@ds@idle_d</Item>
        <Item>clipset@veh@std@ds@idle_e</Item>
      </FirstPersonAdditiveIdleClipSets>
      <FirstPersonRoadRageClipSets>
        <Item>clipset@veh@std@ds@hit_wheel@idle_a</Item>
        <Item>clipset@veh@std@ds@hit_wheel@idle_b</Item>
        <Item>clipset@veh@std@ds@hit_wheel@idle_c</Item>
      </FirstPersonRoadRageClipSets>
    </Item>
    <Item type="CVehicleLayoutInfo">
      <Name>LAYOUT_STD_TECHNICAL</Name>
      <Seats>
        <Item>
          <SeatInfo ref="SEAT_STANDARD_FRONT_LEFT"/>
          <SeatAnimInfo ref="SEAT_ANIM_STD_HIGHWINDOW_FRONT_LEFT"/>
        </Item>
        <Item>
          <SeatInfo ref="SEAT_STANDARD_FRONT_RIGHT"/>
          <SeatAnimInfo ref="SEAT_ANIM_STD_HIGHWINDOW_FRONT_RIGHT"/>
        </Item>
        <Item>
          <SeatInfo ref="SEAT_STD_TECHNICAL_REAR_TURRET"/>
          <SeatAnimInfo ref="SEAT_ANIM_STD_TECHNICAL_REAR_TURRET"/>
        </Item>
      </Seats>
      <EntryPoints>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_STANDARD_FRONT_LEFT"/>
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_STD_BOBCAT_FRONT_LEFT"/>
        </Item>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_STANDARD_FRONT_RIGHT"/>
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_STD_BOBCAT_FRONT_RIGHT"/>
        </Item>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_STD_REAR_LEFT_TURRET"/>
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_STD_TECHNICAL_REAR_LEFT_TURRET"/>
        </Item>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_STD_REAR_RIGHT_TURRET"/>
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_STD_TECHNICAL_REAR_RIGHT_TURRET"/>
        </Item>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_STD_REAR_TURRET"/>
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_STD_TECHNICAL_REAR_TURRET"/>
        </Item>
      </EntryPoints>
      <LayoutFlags>StreamAnims UseDoorOscillation UseLeanSteerAnims UseSteeringWheelIk</LayoutFlags>
      <BicycleInfo ref="NULL"/>
      <AnimRateSet ref="STD_LOW_ANIM_RATE_SET"/>
      <HandsUpClipSetId>busted_vehicle_std</HandsUpClipSetId>
      <SteeringWheelOffset x="0.000000" y="0.350000" z="0.320000"/>
      <MaxXAcceleration value="4.00000"/>
      <BodyLeanXApproachSpeed value="5.00000"/>
      <BodyLeanXSmallDelta value="0.30000"/>
      <LookBackApproachSpeedScale value="1.00000"/>
      <FirstPersonAdditiveIdleClipSets>
        <Item>clipset@veh@std@ds@idle_a</Item>
        <Item>clipset@veh@std@ds@idle_b</Item>
        <Item>clipset@veh@std@ds@idle_c</Item>
        <Item>clipset@veh@std@ds@idle_d</Item>
        <Item>clipset@veh@std@ds@idle_e</Item>
      </FirstPersonAdditiveIdleClipSets>
      <FirstPersonRoadRageClipSets>
        <Item>clipset@veh@std@ds@hit_wheel@idle_a</Item>
        <Item>clipset@veh@std@ds@hit_wheel@idle_b</Item>
        <Item>clipset@veh@std@ds@hit_wheel@idle_c</Item>
      </FirstPersonRoadRageClipSets>
    </Item>
    <Item type="CVehicleLayoutInfo">
      <Name>LAYOUT_TRUCK_GUARDIAN</Name>
      <Seats>
        <Item>
          <SeatInfo ref="SEAT_STANDARD_FRONT_LEFT"/>
          <SeatAnimInfo ref="SEAT_ANIM_TRUCK_FRONT_LEFT"/>
        </Item>
        <Item>
          <SeatInfo ref="SEAT_STANDARD_FRONT_RIGHT"/>
          <SeatAnimInfo ref="SEAT_ANIM_TRUCK_FRONT_RIGHT"/>
        </Item>
        <Item>
          <SeatInfo ref="SEAT_STANDARD_REAR_LEFT"/>
          <SeatAnimInfo ref="SEAT_ANIM_RANGER_SANDKING_REAR_LEFT"/>
        </Item>
        <Item>
          <SeatInfo ref="SEAT_STANDARD_REAR_RIGHT"/>
          <SeatAnimInfo ref="SEAT_ANIM_RANGER_SANDKING_REAR_RIGHT"/>
        </Item>
        <Item>
          <SeatInfo ref="SEAT_VAN_EXTRA_LEFT_2"/>
          <SeatAnimInfo ref="SEAT_ANIM_BODHI_REAR_LEFT"/>
        </Item>
        <Item>
          <SeatInfo ref="SEAT_VAN_EXTRA_RIGHT_2"/>
          <SeatAnimInfo ref="SEAT_ANIM_BODHI_REAR_RIGHT"/>
        </Item>
      </Seats>
      <EntryPoints>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_STANDARD_FRONT_LEFT"/>
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_TRUCK_GUARDIAN_FRONT_LEFT"/>
        </Item>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_STANDARD_FRONT_RIGHT"/>
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_TRUCK_GUARDIAN_FRONT_RIGHT"/>
        </Item>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_STANDARD_REAR_LEFT"/>
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_TRUCK_GUARDIAN_REAR_LEFT"/>
        </Item>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_STANDARD_REAR_RIGHT"/>
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_TRUCK_GUARDIAN_REAR_RIGHT"/>
        </Item>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_VAN_EXTRA_LEFT_2"/>
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_TRUCK_GUARDIAN_BACK_LEFT"/>
        </Item>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_VAN_EXTRA_RIGHT_2"/>
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_TRUCK_GUARDIAN_BACK_RIGHT"/>
        </Item>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_VAN_EXTRA_LEFT_2"/>
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_BODHI_REAR_LEFT_BED"/>
        </Item>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_VAN_EXTRA_RIGHT_2"/>
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_BODHI_REAR_RIGHT_BED"/>
        </Item>
      </EntryPoints>
      <LayoutFlags>StreamAnims UseLeanSteerAnims UseFinerAlignTolerance UseLowerDoorBlockTest</LayoutFlags>
      <BicycleInfo ref="NULL"/>
      <HandsUpClipSetId>busted_vehicle_std</HandsUpClipSetId>
      <SteeringWheelOffset x="0.000000" y="0.350000" z="0.320000"/>
      <MaxXAcceleration value="3.00000"/>
      <BodyLeanXApproachSpeed value="10.00000"/>
      <BodyLeanXSmallDelta value="0.30000"/>
      <FirstPersonAdditiveIdleClipSets>
        <Item>clipset@veh@van@ds@idle_a</Item>
        <Item>clipset@veh@van@ds@idle_b</Item>
        <Item>clipset@veh@van@ds@idle_c</Item>
        <Item>clipset@veh@van@ds@idle_d</Item>
        <Item>clipset@veh@van@ds@idle_e</Item>
      </FirstPersonAdditiveIdleClipSets>
      <FirstPersonRoadRageClipSets>
        <Item>clipset@veh@van@ds@hit_wheel@idle_a</Item>
        <Item>clipset@veh@van@ds@hit_wheel@idle_b</Item>
        <Item>clipset@veh@van@ds@hit_wheel@idle_c</Item>
      </FirstPersonRoadRageClipSets>
    </Item>
    <Item type="CVehicleLayoutInfo">
      <Name>LAYOUT_VAN_SLAMVAN</Name>
      <Seats>
        <Item>
          <SeatInfo ref="SEAT_VAN_FRONT_LEFT"/>
          <SeatAnimInfo ref="SEAT_ANIM_VAN_SLAMVAN_FRONT_LEFT"/>
        </Item>
        <Item>
          <SeatInfo ref="SEAT_VAN_FRONT_RIGHT"/>
          <SeatAnimInfo ref="SEAT_ANIM_VAN_SLAMVAN_FRONT_RIGHT"/>
        </Item>
        <Item>
          <SeatInfo ref="SEAT_VAN_REAR_LEFT"/>
          <SeatAnimInfo ref="SEAT_ANIM_VAN_SLAMVAN_REAR_LEFT"/>
        </Item>
        <Item>
          <SeatInfo ref="SEAT_VAN_REAR_RIGHT"/>
          <SeatAnimInfo ref="SEAT_ANIM_VAN_SLAMVAN_REAR_RIGHT"/>
        </Item>
      </Seats>
      <EntryPoints>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_VAN_FRONT_LEFT"/>
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_STD_RIPLEY_FRONT_LEFT"/>
        </Item>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_VAN_FRONT_RIGHT"/>
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_STD_RIPLEY_FRONT_RIGHT"/>
        </Item>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_VAN_REAR_LEFT"/>
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_VAN_SLAMVAN_REAR_LEFT"/>
        </Item>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_VAN_REAR_RIGHT"/>
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_VAN_SLAMVAN_REAR_RIGHT"/>
        </Item>
      </EntryPoints>
      <LayoutFlags>StreamAnims UseVanOpenDoorBlendParams UseLeanSteerAnims UseSteeringWheelIk UseFinerAlignTolerance</LayoutFlags>
      <BicycleInfo ref="NULL"/>
      <AnimRateSet ref="STD_LOW_ANIM_RATE_SET"/>
      <HandsUpClipSetId>busted_vehicle_std</HandsUpClipSetId>
      <SteeringWheelOffset x="0.000000" y="0.350000" z="0.320000"/>
      <MaxXAcceleration value="4.00000"/>
      <BodyLeanXApproachSpeed value="5.00000"/>
      <BodyLeanXSmallDelta value="0.30000"/>
      <LookBackApproachSpeedScale value="0.80000"/>
      <FirstPersonAdditiveIdleClipSets>
        <Item>clipset@veh@std@ds@idle_a</Item>
        <Item>clipset@veh@std@ds@idle_b</Item>
        <Item>clipset@veh@std@ds@idle_c</Item>
        <Item>clipset@veh@std@ds@idle_d</Item>
        <Item>clipset@veh@std@ds@idle_e</Item>
      </FirstPersonAdditiveIdleClipSets>
      <FirstPersonRoadRageClipSets>
        <Item>clipset@veh@std@ds@hit_wheel@idle_a</Item>
        <Item>clipset@veh@std@ds@hit_wheel@idle_b</Item>
        <Item>clipset@veh@std@ds@hit_wheel@idle_c</Item>
      </FirstPersonRoadRageClipSets>
    </Item>
  </VehicleLayoutInfos>
  <VehicleScenarioLayoutInfos/>
  <SeatOverrideAnimInfos>
    <Item type="CSeatOverrideAnimInfo">
      <Name>SEAT_ANIM_OVERRIDE_HEIST_NARCOTICS_TREVOR_MULE_FRONT_RIGHT</Name>
      <SeatOverrideClipSet>anim@heists@narcotics@finale@ig_5_trevor_in_truck</SeatOverrideClipSet>
      <SeatOverrideAnimFlags/>
    </Item>
  </SeatOverrideAnimInfos>
  <InVehicleOverrideInfos>
    <Item type="CInVehicleOverrideInfo">
      <Name>HEIST_NARCOTICS_TREVOR_MULE_FRONT_RIGHT</Name>
      <SeatOverrideInfos>
        <Item type="CSeatOverrideInfo">
          <SeatAnimInfo ref="SEAT_ANIM_VAN_MULE_FRONT_RIGHT"/>
          <SeatOverrideAnimInfo ref="SEAT_ANIM_OVERRIDE_HEIST_NARCOTICS_TREVOR_MULE_FRONT_RIGHT"/>
        </Item>
      </SeatOverrideInfos>
    </Item>
  </InVehicleOverrideInfos>
  <FirstPersonDriveByLookAroundData>
    <Item type="CFirstPersonDriveByLookAroundData">
      <Name>BIKE_BF400_FRONT</Name>
      <AllowLookback value="true"/>
      <HeadingLimits x="-150.000000" y="150.000000"/>
      <DataLeft>
        <Offsets>
          <Item>
            <Offset value="0.050000"/>
            <AngleToBlendInOffset x="0.000000" y="100.000000"/>
          </Item>
          <Item>
            <Offset value="-0.150000"/>
            <AngleToBlendInOffset x="100.000000" y="135.000000"/>
          </Item>
          <Item>
            <Offset value="0.150000"/>
            <AngleToBlendInOffset x="0.000000" y="50.000000"/>
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-7.500000" y="15.000000"/>
        <AngleToBlendInExtraPitch x="0.000000" y="0.000000"/>
      </DataLeft>
      <DataRight>
        <Offsets>
          <Item>
            <Offset value="0.250000"/>
            <AngleToBlendInOffset x="0.000000" y="135.000000"/>
          </Item>
          <Item>
            <Offset value="-0.000000"/>
            <AngleToBlendInOffset x="100.000000" y="135.000000"/>
          </Item>
          <Item>
            <Offset value="0.150000"/>
            <AngleToBlendInOffset x="0.000000" y="150.000000"/>
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-7.500000" y="15.000000"/>
        <AngleToBlendInExtraPitch x="0.000000" y="0.000000"/>
      </DataRight>
      <WheelClipInfo>
        <HeadingLimitsLeft x="0.380000" y="0.430000"/>
        <HeadingLimitsRight x="0.480000" y="0.530000"/>
        <PitchLimits x="0.470000" y="0.530000"/>
        <PitchOffset x="0.000000" y="0.050000"/>
        <WheelAngleLimits x="-0.700000" y="0.000000"/>
        <WheelAngleOffset x="0.010000" y="0.040000"/>
        <MaxWheelOffsetY value="0.170000"/>
        <WheelClipLerpInRate value="0.150000"/>
        <WheelClipLerpOutRate value="0.180000"/>
      </WheelClipInfo>
    </Item>
    <Item type="CFirstPersonDriveByLookAroundData">
      <Name>LOW_LE7B_FRONT_LEFT</Name>
      <AllowLookback value="false"/>
      <HeadingLimits x="-100.000000" y="110.000000"/>
      <DataLeft>
        <Offsets>
          <Item>
            <Offset value="0.000000"/>
            <AngleToBlendInOffset x="0.000000" y="110.000000"/>
          </Item>
          <Item>
            <Offset value="0.050000"/>
            <AngleToBlendInOffset x="30.000000" y="110.000000"/>
          </Item>
          <Item>
            <Offset value="0.000000"/>
            <AngleToBlendInOffset x="0.000000" y="110.000000"/>
          </Item>
        </Offsets>
        <ExtraRelativePitch x="0.000000" y="0.000000"/>
        <AngleToBlendInExtraPitch x="0.000000" y="90.000000"/>
      </DataLeft>
      <DataRight>
        <Offsets>
          <Item>
            <Offset value="0.050000"/>
            <AngleToBlendInOffset x="0.000000" y="100.000000"/>
          </Item>
          <Item>
            <Offset value="0.125000"/>
            <AngleToBlendInOffset x="0.000000" y="100.000000"/>
          </Item>
          <Item>
            <Offset value="0.000000"/>
            <AngleToBlendInOffset x="0.000000" y="100.000000"/>
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-2.500000" y="-11.000000"/>
        <AngleToBlendInExtraPitch x="0.000000" y="100.000000"/>
      </DataRight>
      <WheelClipInfo>
        <HeadingLimitsLeft x="0.380000" y="0.430000"/>
        <HeadingLimitsRight x="0.480000" y="0.530000"/>
        <PitchLimits x="0.470000" y="0.530000"/>
        <PitchOffset x="0.000000" y="0.050000"/>
        <WheelAngleLimits x="-0.700000" y="0.000000"/>
        <WheelAngleOffset x="0.010000" y="0.040000"/>
        <MaxWheelOffsetY value="0.170000"/>
        <WheelClipLerpInRate value="0.150000"/>
        <WheelClipLerpOutRate value="0.180000"/>
      </WheelClipInfo>
    </Item>
    <Item type="CFirstPersonDriveByLookAroundData">
      <Name>LOW_LE7B_FRONT_RIGHT</Name>
      <AllowLookback value="false"/>
      <HeadingLimits x="-100.000000" y="110.000000"/>
      <DataLeft>
        <Offsets>
          <Item>
            <Offset value="-0.050000"/>
            <AngleToBlendInOffset x="0.000000" y="100.000000"/>
          </Item>
          <Item>
            <Offset value="0.125000"/>
            <AngleToBlendInOffset x="0.000000" y="100.000000"/>
          </Item>
          <Item>
            <Offset value="0.000000"/>
            <AngleToBlendInOffset x="0.000000" y="100.000000"/>
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-2.500000" y="-11.000000"/>
        <AngleToBlendInExtraPitch x="0.000000" y="100.000000"/>
      </DataLeft>
      <DataRight>
        <Offsets>
          <Item>
            <Offset value="0.000000"/>
            <AngleToBlendInOffset x="0.000000" y="110.000000"/>
          </Item>
          <Item>
            <Offset value="0.050000"/>
            <AngleToBlendInOffset x="30.000000" y="110.000000"/>
          </Item>
          <Item>
            <Offset value="0.000000"/>
            <AngleToBlendInOffset x="0.000000" y="110.000000"/>
          </Item>
        </Offsets>
        <ExtraRelativePitch x="0.000000" y="0.000000"/>
        <AngleToBlendInExtraPitch x="0.000000" y="90.000000"/>
      </DataRight>
      <WheelClipInfo>
        <HeadingLimitsLeft x="0.380000" y="0.430000"/>
        <HeadingLimitsRight x="0.480000" y="0.530000"/>
        <PitchLimits x="0.470000" y="0.530000"/>
        <PitchOffset x="0.000000" y="0.050000"/>
        <WheelAngleLimits x="-0.700000" y="0.000000"/>
        <WheelAngleOffset x="0.010000" y="0.040000"/>
        <MaxWheelOffsetY value="0.170000"/>
        <WheelClipLerpInRate value="0.150000"/>
        <WheelClipLerpOutRate value="0.180000"/>
      </WheelClipInfo>
    </Item>
    <Item type="CFirstPersonDriveByLookAroundData">
      <Name>LOW_LYNX_FRONT_LEFT</Name>
      <AllowLookback value="true"/>
      <HeadingLimits x="-190.000000" y="160.000000"/>
      <DataLeft>
        <Offsets>
          <Item>
            <Offset value="-0.275000"/>
            <AngleToBlendInOffset x="40.000000" y="120.000000"/>
          </Item>
          <Item>
            <Offset value="0.000000"/>
            <AngleToBlendInOffset x="0.000000" y="0.000000"/>
          </Item>
          <Item>
            <Offset value="0.000000"/>
            <AngleToBlendInOffset x="0.000000" y="0.000000"/>
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-10.000000" y="0.000000"/>
        <AngleToBlendInExtraPitch x="60.000000" y="100.000000"/>
      </DataLeft>
      <DataRight>
        <Offsets>
          <Item>
            <Offset value="-0.010000"/>
            <AngleToBlendInOffset x="45.000000" y="90.000000"/>
          </Item>
          <Item>
            <Offset value="0.000000"/>
            <AngleToBlendInOffset x="0.000000" y="90.000000"/>
          </Item>
          <Item>
            <Offset value="0.025000"/>
            <AngleToBlendInOffset x="0.000000" y="80.000000"/>
          </Item>
        </Offsets>
        <ExtraRelativePitch x="0.000000" y="-12.500000"/>
        <AngleToBlendInExtraPitch x="0.000000" y="120.000000"/>
      </DataRight>
      <WheelClipInfo>
        <HeadingLimitsLeft x="0.380000" y="0.430000"/>
        <HeadingLimitsRight x="0.480000" y="0.530000"/>
        <PitchLimits x="0.470000" y="0.570000"/>
        <PitchOffset x="0.000000" y="0.050000"/>
        <WheelAngleLimits x="-0.700000" y="0.000000"/>
        <WheelAngleOffset x="0.010000" y="0.040000"/>
        <MaxWheelOffsetY value="0.170000"/>
        <WheelClipLerpInRate value="0.150000"/>
        <WheelClipLerpOutRate value="0.180000"/>
      </WheelClipInfo>
    </Item>
    <Item type="CFirstPersonDriveByLookAroundData">
      <Name>LOW_LYNX_FRONT_RIGHT</Name>
      <AllowLookback value="true"/>
      <HeadingLimits x="-190.000000" y="160.000000"/>
      <DataLeft>
        <Offsets>
          <Item>
            <Offset value="0.040000"/>
            <AngleToBlendInOffset x="45.000000" y="90.000000"/>
          </Item>
          <Item>
            <Offset value="0.000000"/>
            <AngleToBlendInOffset x="0.000000" y="90.000000"/>
          </Item>
          <Item>
            <Offset value="0.025000"/>
            <AngleToBlendInOffset x="0.000000" y="80.000000"/>
          </Item>
        </Offsets>
        <ExtraRelativePitch x="0.000000" y="-12.500000"/>
        <AngleToBlendInExtraPitch x="0.000000" y="120.000000"/>
      </DataLeft>
      <DataRight>
        <Offsets>
          <Item>
            <Offset value="0.275000"/>
            <AngleToBlendInOffset x="40.000000" y="120.000000"/>
          </Item>
          <Item>
            <Offset value="0.000000"/>
            <AngleToBlendInOffset x="0.000000" y="0.000000"/>
          </Item>
          <Item>
            <Offset value="0.000000"/>
            <AngleToBlendInOffset x="0.000000" y="0.000000"/>
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-10.000000" y="0.000000"/>
        <AngleToBlendInExtraPitch x="60.000000" y="100.000000"/>
      </DataRight>
      <WheelClipInfo>
        <HeadingLimitsLeft x="0.380000" y="0.430000"/>
        <HeadingLimitsRight x="0.480000" y="0.530000"/>
        <PitchLimits x="0.470000" y="0.570000"/>
        <PitchOffset x="0.000000" y="0.050000"/>
        <WheelAngleLimits x="-0.700000" y="0.000000"/>
        <WheelAngleOffset x="0.010000" y="0.040000"/>
        <MaxWheelOffsetY value="0.170000"/>
        <WheelClipLerpInRate value="0.150000"/>
        <WheelClipLerpOutRate value="0.180000"/>
      </WheelClipInfo>
    </Item>
    <Item type="CFirstPersonDriveByLookAroundData">
      <Name>LOW_SHEAVA_FRONT_LEFT</Name>
      <AllowLookback value="false"/>
      <HeadingLimits x="-100.000000" y="140.000000"/>
      <DataLeft>
        <Offsets>
          <Item>
            <Offset value="-0.015000"/>
            <AngleToBlendInOffset x="20.000000" y="100.000000"/>
          </Item>
          <Item>
            <Offset value="0.100000"/>
            <AngleToBlendInOffset x="40.000000" y="100.000000"/>
          </Item>
          <Item>
            <Offset value="0.000000"/>
            <AngleToBlendInOffset x="0.000000" y="0.000000"/>
          </Item>
        </Offsets>
        <ExtraRelativePitch x="2.500000" y="-2.500000"/>
        <AngleToBlendInExtraPitch x="0.000000" y="100.000000"/>
      </DataLeft>
      <DataRight>
        <Offsets>
          <Item>
            <Offset value="0.050000"/>
            <AngleToBlendInOffset x="45.000000" y="100.000000"/>
          </Item>
          <Item>
            <Offset value="0.075000"/>
            <AngleToBlendInOffset x="0.000000" y="100.000000"/>
          </Item>
          <Item>
            <Offset value="0.000000"/>
            <AngleToBlendInOffset x="90.000000" y="180.000000"/>
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-2.500000" y="-10.000000"/>
        <AngleToBlendInExtraPitch x="0.000000" y="95.000000"/>
      </DataRight>
      <WheelClipInfo>
        <HeadingLimitsLeft x="0.380000" y="0.430000"/>
        <HeadingLimitsRight x="0.480000" y="0.530000"/>
        <PitchLimits x="0.470000" y="0.530000"/>
        <PitchOffset x="0.000000" y="0.050000"/>
        <WheelAngleLimits x="-0.700000" y="0.000000"/>
        <WheelAngleOffset x="0.010000" y="0.040000"/>
        <MaxWheelOffsetY value="0.170000"/>
        <WheelClipLerpInRate value="0.150000"/>
        <WheelClipLerpOutRate value="0.180000"/>
      </WheelClipInfo>
    </Item>
    <Item type="CFirstPersonDriveByLookAroundData">
      <Name>LOW_SHEAVA_FRONT_RIGHT</Name>
      <AllowLookback value="false"/>
      <HeadingLimits x="-100.000000" y="140.000000"/>
      <DataLeft>
        <Offsets>
          <Item>
            <Offset value="-0.050000"/>
            <AngleToBlendInOffset x="45.000000" y="100.000000"/>
          </Item>
          <Item>
            <Offset value="0.075000"/>
            <AngleToBlendInOffset x="0.000000" y="100.000000"/>
          </Item>
          <Item>
            <Offset value="0.010000"/>
            <AngleToBlendInOffset x="20.000000" y="90.000000"/>
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-2.500000" y="-10.000000"/>
        <AngleToBlendInExtraPitch x="0.000000" y="95.000000"/>
      </DataLeft>
      <DataRight>
        <Offsets>
          <Item>
            <Offset value="0.015000"/>
            <AngleToBlendInOffset x="20.000000" y="100.000000"/>
          </Item>
          <Item>
            <Offset value="0.100000"/>
            <AngleToBlendInOffset x="40.000000" y="100.000000"/>
          </Item>
          <Item>
            <Offset value="0.010000"/>
            <AngleToBlendInOffset x="20.000000" y="90.000000"/>
          </Item>
        </Offsets>
        <ExtraRelativePitch x="2.500000" y="-2.500000"/>
        <AngleToBlendInExtraPitch x="0.000000" y="100.000000"/>
      </DataRight>
      <WheelClipInfo>
        <HeadingLimitsLeft x="0.380000" y="0.430000"/>
        <HeadingLimitsRight x="0.480000" y="0.530000"/>
        <PitchLimits x="0.470000" y="0.530000"/>
        <PitchOffset x="0.000000" y="0.050000"/>
        <WheelAngleLimits x="-0.700000" y="0.000000"/>
        <WheelAngleOffset x="0.010000" y="0.040000"/>
        <MaxWheelOffsetY value="0.170000"/>
        <WheelClipLerpInRate value="0.150000"/>
        <WheelClipLerpOutRate value="0.180000"/>
      </WheelClipInfo>
    </Item>
    <Item type="CFirstPersonDriveByLookAroundData">
      <Name>LOW_TAMPA2_FRONT_LEFT</Name>
      <AllowLookback value="true"/>
      <HeadingLimits x="-190.000000" y="160.000000"/>
      <DataLeft>
        <Offsets>
          <Item>
            <Offset value="-0.300000"/>
            <AngleToBlendInOffset x="45.000000" y="160.000000"/>
          </Item>
          <Item>
            <Offset value="0.100000"/>
            <AngleToBlendInOffset x="45.000000" y="160.000000"/>
          </Item>
          <Item>
            <Offset value="0.025000"/>
            <AngleToBlendInOffset x="0.000000" y="160.000000"/>
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-10.000000" y="5.000000"/>
        <AngleToBlendInExtraPitch x="60.000000" y="100.000000"/>
      </DataLeft>
      <DataRight>
        <Offsets>
          <Item>
            <Offset value="0.000000"/>
            <AngleToBlendInOffset x="0.000000" y="0.000000"/>
          </Item>
          <Item>
            <Offset value="0.000000"/>
            <AngleToBlendInOffset x="0.000000" y="0.000000"/>
          </Item>
          <Item>
            <Offset value="0.050000"/>
            <AngleToBlendInOffset x="90.000000" y="180.000000"/>
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-2.500000" y="-10.000000"/>
        <AngleToBlendInExtraPitch x="0.000000" y="190.000000"/>
      </DataRight>
      <WheelClipInfo>
        <HeadingLimitsLeft x="0.380000" y="0.430000"/>
        <HeadingLimitsRight x="0.480000" y="0.530000"/>
        <PitchLimits x="0.470000" y="0.530000"/>
        <PitchOffset x="0.000000" y="0.050000"/>
        <WheelAngleLimits x="-0.700000" y="0.000000"/>
        <WheelAngleOffset x="0.010000" y="0.040000"/>
        <MaxWheelOffsetY value="0.170000"/>
        <WheelClipLerpInRate value="0.150000"/>
        <WheelClipLerpOutRate value="0.180000"/>
      </WheelClipInfo>
    </Item>
    <Item type="CFirstPersonDriveByLookAroundData">
      <Name>LOW_TAMPA2_FRONT_RIGHT</Name>
      <AllowLookback value="true"/>
      <HeadingLimits x="-190.000000" y="160.000000"/>
      <DataLeft>
        <Offsets>
          <Item>
            <Offset value="0.000000"/>
            <AngleToBlendInOffset x="0.000000" y="0.000000"/>
          </Item>
          <Item>
            <Offset value="0.000000"/>
            <AngleToBlendInOffset x="0.000000" y="0.000000"/>
          </Item>
          <Item>
            <Offset value="0.050000"/>
            <AngleToBlendInOffset x="90.000000" y="180.000000"/>
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-2.500000" y="-10.000000"/>
        <AngleToBlendInExtraPitch x="0.000000" y="190.000000"/>
      </DataLeft>
      <DataRight>
        <Offsets>
          <Item>
            <Offset value="0.300000"/>
            <AngleToBlendInOffset x="45.000000" y="160.000000"/>
          </Item>
          <Item>
            <Offset value="0.100000"/>
            <AngleToBlendInOffset x="45.000000" y="160.000000"/>
          </Item>
          <Item>
            <Offset value="0.025000"/>
            <AngleToBlendInOffset x="0.000000" y="160.000000"/>
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-10.000000" y="5.000000"/>
        <AngleToBlendInExtraPitch x="60.000000" y="100.000000"/>
      </DataRight>
      <WheelClipInfo>
        <HeadingLimitsLeft x="0.380000" y="0.430000"/>
        <HeadingLimitsRight x="0.480000" y="0.530000"/>
        <PitchLimits x="0.470000" y="0.530000"/>
        <PitchOffset x="0.000000" y="0.050000"/>
        <WheelAngleLimits x="-0.700000" y="0.000000"/>
        <WheelAngleOffset x="0.010000" y="0.040000"/>
        <MaxWheelOffsetY value="0.170000"/>
        <WheelClipLerpInRate value="0.150000"/>
        <WheelClipLerpOutRate value="0.180000"/>
      </WheelClipInfo>
    </Item>
    <Item type="CFirstPersonDriveByLookAroundData">
      <Name>LOW_TROPOS_FRONT_LEFT</Name>
      <AllowLookback value="false"/>
      <HeadingLimits x="-100.000000" y="100.000000"/>
      <DataLeft>
        <Offsets>
          <Item>
            <Offset value="0.100000"/>
            <AngleToBlendInOffset x="0.000000" y="90.000000"/>
          </Item>
          <Item>
            <Offset value="0.100000"/>
            <AngleToBlendInOffset x="30.000000" y="90.000000"/>
          </Item>
          <Item>
            <Offset value="0.040000"/>
            <AngleToBlendInOffset x="0.000000" y="90.000000"/>
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-2.500000" y="-7.500000"/>
        <AngleToBlendInExtraPitch x="0.000000" y="90.000000"/>
      </DataLeft>
      <DataRight>
        <Offsets>
          <Item>
            <Offset value="0.100000"/>
            <AngleToBlendInOffset x="0.000000" y="100.000000"/>
          </Item>
          <Item>
            <Offset value="0.100000"/>
            <AngleToBlendInOffset x="0.000000" y="100.000000"/>
          </Item>
          <Item>
            <Offset value="0.025000"/>
            <AngleToBlendInOffset x="0.000000" y="100.000000"/>
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-2.500000" y="-12.500000"/>
        <AngleToBlendInExtraPitch x="0.000000" y="100.000000"/>
      </DataRight>
      <WheelClipInfo>
        <HeadingLimitsLeft x="0.380000" y="0.430000"/>
        <HeadingLimitsRight x="0.480000" y="0.530000"/>
        <PitchLimits x="0.470000" y="0.530000"/>
        <PitchOffset x="0.000000" y="0.050000"/>
        <WheelAngleLimits x="-0.700000" y="0.000000"/>
        <WheelAngleOffset x="0.010000" y="0.040000"/>
        <MaxWheelOffsetY value="0.170000"/>
        <WheelClipLerpInRate value="0.150000"/>
        <WheelClipLerpOutRate value="0.180000"/>
      </WheelClipInfo>
    </Item>
    <Item type="CFirstPersonDriveByLookAroundData">
      <Name>LOW_TROPOS_FRONT_RIGHT</Name>
      <AllowLookback value="false"/>
      <HeadingLimits x="-100.000000" y="100.000000"/>
      <DataLeft>
        <Offsets>
          <Item>
            <Offset value="-0.100000"/>
            <AngleToBlendInOffset x="0.000000" y="100.000000"/>
          </Item>
          <Item>
            <Offset value="0.100000"/>
            <AngleToBlendInOffset x="0.000000" y="100.000000"/>
          </Item>
          <Item>
            <Offset value="0.025000"/>
            <AngleToBlendInOffset x="0.000000" y="100.000000"/>
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-2.500000" y="-12.500000"/>
        <AngleToBlendInExtraPitch x="0.000000" y="100.000000"/>
      </DataLeft>
      <DataRight>
        <Offsets>
          <Item>
            <Offset value="-0.100000"/>
            <AngleToBlendInOffset x="0.000000" y="90.000000"/>
          </Item>
          <Item>
            <Offset value="0.100000"/>
            <AngleToBlendInOffset x="30.000000" y="90.000000"/>
          </Item>
          <Item>
            <Offset value="0.040000"/>
            <AngleToBlendInOffset x="0.000000" y="90.000000"/>
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-2.500000" y="-7.500000"/>
        <AngleToBlendInExtraPitch x="0.000000" y="90.000000"/>
      </DataRight>
      <WheelClipInfo>
        <HeadingLimitsLeft x="0.380000" y="0.430000"/>
        <HeadingLimitsRight x="0.480000" y="0.530000"/>
        <PitchLimits x="0.470000" y="0.530000"/>
        <PitchOffset x="0.000000" y="0.050000"/>
        <WheelAngleLimits x="-0.700000" y="0.000000"/>
        <WheelAngleOffset x="0.010000" y="0.040000"/>
        <MaxWheelOffsetY value="0.170000"/>
        <WheelClipLerpInRate value="0.150000"/>
        <WheelClipLerpOutRate value="0.180000"/>
      </WheelClipInfo>
    </Item>
    <Item type="CFirstPersonDriveByLookAroundData">
      <Name>LOW_TYRUS_FRONT_LEFT</Name>
      <AllowLookback value="false"/>
      <HeadingLimits x="-100.000000" y="150.000000"/>
      <DataLeft>
        <Offsets>
          <Item>
            <Offset value="-0.275000"/>
            <AngleToBlendInOffset x="45.000000" y="120.000000"/>
          </Item>
          <Item>
            <Offset value="-0.075000"/>
            <AngleToBlendInOffset x="5.000000" y="45.000000"/>
          </Item>
          <Item>
            <Offset value="0.080000"/>
            <AngleToBlendInOffset x="5.000000" y="90.000000"/>
          </Item>
        </Offsets>
        <ExtraRelativePitch x="0.000000" y="0.000000"/>
        <AngleToBlendInExtraPitch x="60.000000" y="100.000000"/>
      </DataLeft>
      <DataRight>
        <Offsets>
          <Item>
            <Offset value="0.050000"/>
            <AngleToBlendInOffset x="45.000000" y="100.000000"/>
          </Item>
          <Item>
            <Offset value="0.050000"/>
            <AngleToBlendInOffset x="0.000000" y="100.000000"/>
          </Item>
          <Item>
            <Offset value="0.075000"/>
            <AngleToBlendInOffset x="45.000000" y="100.000000"/>
          </Item>
        </Offsets>
        <ExtraRelativePitch x="0.000000" y="-12.500000"/>
        <AngleToBlendInExtraPitch x="0.000000" y="100.000000"/>
      </DataRight>
      <WheelClipInfo>
        <HeadingLimitsLeft x="0.380000" y="0.430000"/>
        <HeadingLimitsRight x="0.480000" y="0.530000"/>
        <PitchLimits x="0.470000" y="0.530000"/>
        <PitchOffset x="0.000000" y="0.050000"/>
        <WheelAngleLimits x="-0.700000" y="0.000000"/>
        <WheelAngleOffset x="0.010000" y="0.040000"/>
        <MaxWheelOffsetY value="0.170000"/>
        <WheelClipLerpInRate value="0.150000"/>
        <WheelClipLerpOutRate value="0.180000"/>
      </WheelClipInfo>
    </Item>
    <Item type="CFirstPersonDriveByLookAroundData">
      <Name>LOW_TYRUS_FRONT_RIGHT</Name>
      <AllowLookback value="false"/>
      <HeadingLimits x="-100.000000" y="150.000000"/>
      <DataLeft>
        <Offsets>
          <Item>
            <Offset value="-0.050000"/>
            <AngleToBlendInOffset x="45.000000" y="100.000000"/>
          </Item>
          <Item>
            <Offset value="0.050000"/>
            <AngleToBlendInOffset x="0.000000" y="100.000000"/>
          </Item>
          <Item>
            <Offset value="0.020000"/>
            <AngleToBlendInOffset x="45.000000" y="100.000000"/>
          </Item>
        </Offsets>
        <ExtraRelativePitch x="0.000000" y="-12.500000"/>
        <AngleToBlendInExtraPitch x="0.000000" y="100.000000"/>
      </DataLeft>
      <DataRight>
        <Offsets>
          <Item>
            <Offset value="0.275000"/>
            <AngleToBlendInOffset x="45.000000" y="120.000000"/>
          </Item>
          <Item>
            <Offset value="-0.075000"/>
            <AngleToBlendInOffset x="5.000000" y="45.000000"/>
          </Item>
          <Item>
            <Offset value="0.025000"/>
            <AngleToBlendInOffset x="5.000000" y="90.000000"/>
          </Item>
        </Offsets>
        <ExtraRelativePitch x="0.000000" y="0.000000"/>
        <AngleToBlendInExtraPitch x="60.000000" y="100.000000"/>
      </DataRight>
      <WheelClipInfo>
        <HeadingLimitsLeft x="0.380000" y="0.430000"/>
        <HeadingLimitsRight x="0.480000" y="0.530000"/>
        <PitchLimits x="0.470000" y="0.530000"/>
        <PitchOffset x="0.000000" y="0.050000"/>
        <WheelAngleLimits x="-0.700000" y="0.000000"/>
        <WheelAngleOffset x="0.010000" y="0.040000"/>
        <MaxWheelOffsetY value="0.170000"/>
        <WheelClipLerpInRate value="0.150000"/>
        <WheelClipLerpOutRate value="0.180000"/>
      </WheelClipInfo>
    </Item>
    <Item type="CFirstPersonDriveByLookAroundData">
      <Name>RANGER_CONTENDER_FRONT_LEFT</Name>
      <AllowLookback value="true"/>
      <HeadingLimits x="-190.000000" y="170.000000"/>
      <DataLeft>
        <Offsets>
          <Item>
            <Offset value="-0.350000"/>
            <AngleToBlendInOffset x="40.000000" y="120.000000"/>
          </Item>
          <Item>
            <Offset value="-0.000000"/>
            <AngleToBlendInOffset x="0.000000" y="0.000000"/>
          </Item>
          <Item>
            <Offset value="0.000000"/>
            <AngleToBlendInOffset x="0.000000" y="0.000000"/>
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-20.000000" y="10.000000"/>
        <AngleToBlendInExtraPitch x="5.000000" y="110.000000"/>
      </DataLeft>
      <DataRight>
        <Offsets>
          <Item>
            <Offset value="-0.050000"/>
            <AngleToBlendInOffset x="5.000000" y="50.000000"/>
          </Item>
          <Item>
            <Offset value="0.000000"/>
            <AngleToBlendInOffset x="0.000000" y="0.000000"/>
          </Item>
          <Item>
            <Offset value="0.000000"/>
            <AngleToBlendInOffset x="0.000000" y="0.000000"/>
          </Item>
        </Offsets>
        <ExtraRelativePitch x="0.000000" y="-10.500000"/>
        <AngleToBlendInExtraPitch x="0.000000" y="190.000000"/>
      </DataRight>
      <WheelClipInfo>
        <HeadingLimitsLeft x="0.380000" y="0.430000"/>
        <HeadingLimitsRight x="0.480000" y="0.530000"/>
        <PitchLimits x="0.470000" y="0.530000"/>
        <PitchOffset x="0.000000" y="0.010000"/>
        <WheelAngleLimits x="-0.700000" y="0.000000"/>
        <WheelAngleOffset x="0.010000" y="0.020000"/>
        <MaxWheelOffsetY value="0.170000"/>
        <WheelClipLerpInRate value="0.150000"/>
        <WheelClipLerpOutRate value="0.180000"/>
      </WheelClipInfo>
    </Item>
    <Item type="CFirstPersonDriveByLookAroundData">
      <Name>RANGER_CONTENDER_FRONT_RIGHT</Name>
      <AllowLookback value="true"/>
      <HeadingLimits x="-190.000000" y="170.000000"/>
      <DataLeft>
        <Offsets>
          <Item>
            <Offset value="0.050000"/>
            <AngleToBlendInOffset x="5.000000" y="50.000000"/>
          </Item>
          <Item>
            <Offset value="0.000000"/>
            <AngleToBlendInOffset x="0.000000" y="0.000000"/>
          </Item>
          <Item>
            <Offset value="0.000000"/>
            <AngleToBlendInOffset x="0.000000" y="0.000000"/>
          </Item>
        </Offsets>
        <ExtraRelativePitch x="0.000000" y="-10.500000"/>
        <AngleToBlendInExtraPitch x="0.000000" y="190.000000"/>
      </DataLeft>
      <DataRight>
        <Offsets>
          <Item>
            <Offset value="0.350000"/>
            <AngleToBlendInOffset x="40.000000" y="120.000000"/>
          </Item>
          <Item>
            <Offset value="-0.000000"/>
            <AngleToBlendInOffset x="0.000000" y="0.000000"/>
          </Item>
          <Item>
            <Offset value="0.000000"/>
            <AngleToBlendInOffset x="0.000000" y="0.000000"/>
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-20.000000" y="10.000000"/>
        <AngleToBlendInExtraPitch x="5.000000" y="110.000000"/>
      </DataRight>
      <WheelClipInfo>
        <HeadingLimitsLeft x="0.380000" y="0.430000"/>
        <HeadingLimitsRight x="0.480000" y="0.530000"/>
        <PitchLimits x="0.470000" y="0.530000"/>
        <PitchOffset x="0.000000" y="0.010000"/>
        <WheelAngleLimits x="-0.700000" y="0.000000"/>
        <WheelAngleOffset x="0.010000" y="0.020000"/>
        <MaxWheelOffsetY value="0.170000"/>
        <WheelClipLerpInRate value="0.150000"/>
        <WheelClipLerpOutRate value="0.180000"/>
      </WheelClipInfo>
    </Item>
    <Item type="CFirstPersonDriveByLookAroundData">
      <Name>STD_BRIOSO_FRONT_LEFT</Name>
      <AllowLookback value="true"/>
      <HeadingLimits x="-190.000000" y="160.000000"/>
      <DataLeft>
        <Offsets>
          <Item>
            <Offset value="-0.275000"/>
            <AngleToBlendInOffset x="40.000000" y="160.000000"/>
          </Item>
          <Item>
            <Offset value="0.060000"/>
            <AngleToBlendInOffset x="60.000000" y="160.000000"/>
          </Item>
          <Item>
            <Offset value="0.050000"/>
            <AngleToBlendInOffset x="60.000000" y="120.000000"/>
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-15.000000" y="20.000000"/>
        <AngleToBlendInExtraPitch x="50.000000" y="110.000000"/>
      </DataLeft>
      <DataRight>
        <Offsets>
          <Item>
            <Offset value="0.085000"/>
            <AngleToBlendInOffset x="0.000000" y="90.000000"/>
          </Item>
          <Item>
            <Offset value="-0.075000"/>
            <AngleToBlendInOffset x="0.000000" y="90.000000"/>
          </Item>
          <Item>
            <Offset value="0.050000"/>
            <AngleToBlendInOffset x="0.000000" y="90.000000"/>
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-4.000000" y="-15.000000"/>
        <AngleToBlendInExtraPitch x="0.000000" y="195.000000"/>
      </DataRight>
      <WheelClipInfo>
        <HeadingLimitsLeft x="0.380000" y="0.430000"/>
        <HeadingLimitsRight x="0.480000" y="0.530000"/>
        <PitchLimits x="0.470000" y="0.530000"/>
        <PitchOffset x="0.000000" y="0.050000"/>
        <WheelAngleLimits x="-0.700000" y="0.000000"/>
        <WheelAngleOffset x="0.010000" y="0.040000"/>
        <MaxWheelOffsetY value="0.170000"/>
        <WheelClipLerpInRate value="0.150000"/>
        <WheelClipLerpOutRate value="0.180000"/>
      </WheelClipInfo>
    </Item>
    <Item type="CFirstPersonDriveByLookAroundData">
      <Name>STD_BRIOSO_FRONT_RIGHT</Name>
      <AllowLookback value="true"/>
      <HeadingLimits x="-190.000000" y="160.000000"/>
      <DataLeft>
        <Offsets>
          <Item>
            <Offset value="-0.085000"/>
            <AngleToBlendInOffset x="0.000000" y="90.000000"/>
          </Item>
          <Item>
            <Offset value="-0.075000"/>
            <AngleToBlendInOffset x="0.000000" y="90.000000"/>
          </Item>
          <Item>
            <Offset value="0.050000"/>
            <AngleToBlendInOffset x="0.000000" y="90.000000"/>
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-4.000000" y="-15.000000"/>
        <AngleToBlendInExtraPitch x="0.000000" y="195.000000"/>
      </DataLeft>
      <DataRight>
        <Offsets>
          <Item>
            <Offset value="0.275000"/>
            <AngleToBlendInOffset x="40.000000" y="160.000000"/>
          </Item>
          <Item>
            <Offset value="0.060000"/>
            <AngleToBlendInOffset x="60.000000" y="160.000000"/>
          </Item>
          <Item>
            <Offset value="0.050000"/>
            <AngleToBlendInOffset x="60.000000" y="120.000000"/>
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-15.000000" y="20.000000"/>
        <AngleToBlendInExtraPitch x="50.000000" y="110.000000"/>
      </DataRight>
      <WheelClipInfo>
        <HeadingLimitsLeft x="0.380000" y="0.430000"/>
        <HeadingLimitsRight x="0.480000" y="0.530000"/>
        <PitchLimits x="0.470000" y="0.530000"/>
        <PitchOffset x="0.000000" y="0.050000"/>
        <WheelAngleLimits x="-0.700000" y="0.000000"/>
        <WheelAngleOffset x="0.010000" y="0.040000"/>
        <MaxWheelOffsetY value="0.170000"/>
        <WheelClipLerpInRate value="0.150000"/>
        <WheelClipLerpOutRate value="0.180000"/>
      </WheelClipInfo>
    </Item>
    <Item type="CFirstPersonDriveByLookAroundData">
      <Name>STD_OMNIS_FRONT_LEFT</Name>
      <AllowLookback value="false"/>
      <HeadingLimits x="-100.000000" y="160.000000"/>
      <DataLeft>
        <Offsets>
          <Item>
            <Offset value="-0.275000"/>
            <AngleToBlendInOffset x="40.000000" y="160.000000"/>
          </Item>
          <Item>
            <Offset value="0.100000"/>
            <AngleToBlendInOffset x="60.000000" y="160.000000"/>
          </Item>
          <Item>
            <Offset value="0.050000"/>
            <AngleToBlendInOffset x="90.000000" y="160.000000"/>
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-12.500000" y="10.000000"/>
        <AngleToBlendInExtraPitch x="60.000000" y="110.000000"/>
      </DataLeft>
      <DataRight>
        <Offsets>
          <Item>
            <Offset value="0.050000"/>
            <AngleToBlendInOffset x="40.000000" y="100.000000"/>
          </Item>
          <Item>
            <Offset value="0.125000"/>
            <AngleToBlendInOffset x="40.000000" y="100.000000"/>
          </Item>
          <Item>
            <Offset value="0.000000"/>
            <AngleToBlendInOffset x="0.000000" y="0.000000"/>
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-5.000000" y="-9.000000"/>
        <AngleToBlendInExtraPitch x="0.000000" y="100.000000"/>
      </DataRight>
      <WheelClipInfo>
        <HeadingLimitsLeft x="0.380000" y="0.430000"/>
        <HeadingLimitsRight x="0.480000" y="0.530000"/>
        <PitchLimits x="0.470000" y="0.530000"/>
        <PitchOffset x="0.000000" y="0.050000"/>
        <WheelAngleLimits x="-0.700000" y="0.000000"/>
        <WheelAngleOffset x="0.010000" y="0.040000"/>
        <MaxWheelOffsetY value="0.170000"/>
        <WheelClipLerpInRate value="0.150000"/>
        <WheelClipLerpOutRate value="0.180000"/>
      </WheelClipInfo>
    </Item>
    <Item type="CFirstPersonDriveByLookAroundData">
      <Name>STD_OMNIS_FRONT_RIGHT</Name>
      <AllowLookback value="false"/>
      <HeadingLimits x="-100.000000" y="160.000000"/>
      <DataLeft>
        <Offsets>
          <Item>
            <Offset value="-0.050000"/>
            <AngleToBlendInOffset x="40.000000" y="100.000000"/>
          </Item>
          <Item>
            <Offset value="0.125000"/>
            <AngleToBlendInOffset x="40.000000" y="100.000000"/>
          </Item>
          <Item>
            <Offset value="0.000000"/>
            <AngleToBlendInOffset x="0.000000" y="0.000000"/>
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-5.000000" y="-9.000000"/>
        <AngleToBlendInExtraPitch x="0.000000" y="100.000000"/>
      </DataLeft>
      <DataRight>
        <Offsets>
          <Item>
            <Offset value="0.275000"/>
            <AngleToBlendInOffset x="40.000000" y="160.000000"/>
          </Item>
          <Item>
            <Offset value="0.100000"/>
            <AngleToBlendInOffset x="60.000000" y="160.000000"/>
          </Item>
          <Item>
            <Offset value="0.050000"/>
            <AngleToBlendInOffset x="90.000000" y="160.000000"/>
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-12.500000" y="10.000000"/>
        <AngleToBlendInExtraPitch x="60.000000" y="110.000000"/>
      </DataRight>
      <WheelClipInfo>
        <HeadingLimitsLeft x="0.380000" y="0.430000"/>
        <HeadingLimitsRight x="0.480000" y="0.530000"/>
        <PitchLimits x="0.470000" y="0.530000"/>
        <PitchOffset x="0.000000" y="0.050000"/>
        <WheelAngleLimits x="-0.700000" y="0.000000"/>
        <WheelAngleOffset x="0.010000" y="0.040000"/>
        <MaxWheelOffsetY value="0.170000"/>
        <WheelClipLerpInRate value="0.150000"/>
        <WheelClipLerpOutRate value="0.180000"/>
      </WheelClipInfo>
    </Item>
    <Item type="CFirstPersonDriveByLookAroundData">
      <Name>STD_TROPHY_FRONT_LEFT</Name>
      <AllowLookback value="false"/>
      <HeadingLimits x="-95.000000" y="160.000000"/>
      <DataLeft>
        <Offsets>
          <Item>
            <Offset value="-0.350000"/>
            <AngleToBlendInOffset x="40.000000" y="160.000000"/>
          </Item>
          <Item>
            <Offset value="0.150000"/>
            <AngleToBlendInOffset x="60.000000" y="160.000000"/>
          </Item>
          <Item>
            <Offset value="0.060000"/>
            <AngleToBlendInOffset x="60.000000" y="120.000000"/>
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-15.000000" y="20.000000"/>
        <AngleToBlendInExtraPitch x="50.000000" y="110.000000"/>
      </DataLeft>
      <DataRight>
        <Offsets>
          <Item>
            <Offset value="0.100000"/>
            <AngleToBlendInOffset x="45.000000" y="95.000000"/>
          </Item>
          <Item>
            <Offset value="0.050000"/>
            <AngleToBlendInOffset x="0.000000" y="95.000000"/>
          </Item>
          <Item>
            <Offset value="0.000000"/>
            <AngleToBlendInOffset x="0.000000" y="0.000000"/>
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-4.000000" y="-5.000000"/>
        <AngleToBlendInExtraPitch x="0.000000" y="95.000000"/>
      </DataRight>
      <WheelClipInfo>
        <HeadingLimitsLeft x="0.380000" y="0.430000"/>
        <HeadingLimitsRight x="0.480000" y="0.530000"/>
        <PitchLimits x="0.470000" y="0.530000"/>
        <PitchOffset x="0.000000" y="0.050000"/>
        <WheelAngleLimits x="-0.700000" y="0.000000"/>
        <WheelAngleOffset x="0.010000" y="0.040000"/>
        <MaxWheelOffsetY value="0.170000"/>
        <WheelClipLerpInRate value="0.150000"/>
        <WheelClipLerpOutRate value="0.180000"/>
      </WheelClipInfo>
    </Item>
    <Item type="CFirstPersonDriveByLookAroundData">
      <Name>STD_TROPHY_FRONT_RIGHT</Name>
      <AllowLookback value="false"/>
      <HeadingLimits x="-95.000000" y="160.000000"/>
      <DataLeft>
        <Offsets>
          <Item>
            <Offset value="-0.100000"/>
            <AngleToBlendInOffset x="45.000000" y="95.000000"/>
          </Item>
          <Item>
            <Offset value="0.050000"/>
            <AngleToBlendInOffset x="0.000000" y="95.000000"/>
          </Item>
          <Item>
            <Offset value="0.000000"/>
            <AngleToBlendInOffset x="0.000000" y="0.000000"/>
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-4.000000" y="-5.000000"/>
        <AngleToBlendInExtraPitch x="0.000000" y="95.000000"/>
      </DataLeft>
      <DataRight>
        <Offsets>
          <Item>
            <Offset value="0.350000"/>
            <AngleToBlendInOffset x="40.000000" y="160.000000"/>
          </Item>
          <Item>
            <Offset value="0.150000"/>
            <AngleToBlendInOffset x="60.000000" y="160.000000"/>
          </Item>
          <Item>
            <Offset value="0.060000"/>
            <AngleToBlendInOffset x="60.000000" y="120.000000"/>
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-15.000000" y="20.000000"/>
        <AngleToBlendInExtraPitch x="50.000000" y="110.000000"/>
      </DataRight>
      <WheelClipInfo>
        <HeadingLimitsLeft x="0.380000" y="0.430000"/>
        <HeadingLimitsRight x="0.480000" y="0.530000"/>
        <PitchLimits x="0.470000" y="0.530000"/>
        <PitchOffset x="0.000000" y="0.050000"/>
        <WheelAngleLimits x="-0.700000" y="0.000000"/>
        <WheelAngleOffset x="0.010000" y="0.040000"/>
        <MaxWheelOffsetY value="0.170000"/>
        <WheelClipLerpInRate value="0.150000"/>
        <WheelClipLerpOutRate value="0.180000"/>
      </WheelClipInfo>
    </Item>
    <Item type="CFirstPersonDriveByLookAroundData">
      <Name>STD_TROPHY2_FRONT_LEFT</Name>
      <AllowLookback value="false"/>
      <HeadingLimits x="-95.000000" y="140.000000"/>
      <DataLeft>
        <Offsets>
          <Item>
            <Offset value="-0.350000"/>
            <AngleToBlendInOffset x="60.000000" y="140.000000"/>
          </Item>
          <Item>
            <Offset value="0.150000"/>
            <AngleToBlendInOffset x="60.000000" y="140.000000"/>
          </Item>
          <Item>
            <Offset value="0.025000"/>
            <AngleToBlendInOffset x="0.000000" y="140.000000"/>
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-15.000000" y="10.000000"/>
        <AngleToBlendInExtraPitch x="60.000000" y="140.000000"/>
      </DataLeft>
      <DataRight>
        <Offsets>
          <Item>
            <Offset value="0.100000"/>
            <AngleToBlendInOffset x="45.000000" y="95.000000"/>
          </Item>
          <Item>
            <Offset value="0.060000"/>
            <AngleToBlendInOffset x="0.000000" y="95.000000"/>
          </Item>
          <Item>
            <Offset value="0.000000"/>
            <AngleToBlendInOffset x="0.000000" y="0.000000"/>
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-2.500000" y="-1.000000"/>
        <AngleToBlendInExtraPitch x="0.000000" y="95.000000"/>
      </DataRight>
      <WheelClipInfo>
        <HeadingLimitsLeft x="0.380000" y="0.430000"/>
        <HeadingLimitsRight x="0.480000" y="0.530000"/>
        <PitchLimits x="0.470000" y="0.530000"/>
        <PitchOffset x="0.000000" y="0.050000"/>
        <WheelAngleLimits x="-0.700000" y="0.000000"/>
        <WheelAngleOffset x="0.010000" y="0.040000"/>
        <MaxWheelOffsetY value="0.170000"/>
        <WheelClipLerpInRate value="0.150000"/>
        <WheelClipLerpOutRate value="0.180000"/>
      </WheelClipInfo>
    </Item>
    <Item type="CFirstPersonDriveByLookAroundData">
      <Name>STD_TROPHY2_FRONT_RIGHT</Name>
      <AllowLookback value="false"/>
      <HeadingLimits x="-95.000000" y="140.000000"/>
      <DataLeft>
        <Offsets>
          <Item>
            <Offset value="-0.100000"/>
            <AngleToBlendInOffset x="45.000000" y="95.000000"/>
          </Item>
          <Item>
            <Offset value="0.060000"/>
            <AngleToBlendInOffset x="0.000000" y="95.000000"/>
          </Item>
          <Item>
            <Offset value="0.000000"/>
            <AngleToBlendInOffset x="0.000000" y="0.000000"/>
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-2.500000" y="-1.000000"/>
        <AngleToBlendInExtraPitch x="0.000000" y="95.000000"/>
      </DataLeft>
      <DataRight>
        <Offsets>
          <Item>
            <Offset value="0.350000"/>
            <AngleToBlendInOffset x="60.000000" y="140.000000"/>
          </Item>
          <Item>
            <Offset value="0.150000"/>
            <AngleToBlendInOffset x="60.000000" y="140.000000"/>
          </Item>
          <Item>
            <Offset value="0.025000"/>
            <AngleToBlendInOffset x="0.000000" y="140.000000"/>
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-15.000000" y="10.000000"/>
        <AngleToBlendInExtraPitch x="60.000000" y="140.000000"/>
      </DataRight>
      <WheelClipInfo>
        <HeadingLimitsLeft x="0.380000" y="0.430000"/>
        <HeadingLimitsRight x="0.480000" y="0.530000"/>
        <PitchLimits x="0.470000" y="0.530000"/>
        <PitchOffset x="0.000000" y="0.050000"/>
        <WheelAngleLimits x="-0.700000" y="0.000000"/>
        <WheelAngleOffset x="0.010000" y="0.040000"/>
        <MaxWheelOffsetY value="0.170000"/>
        <WheelClipLerpInRate value="0.150000"/>
        <WheelClipLerpOutRate value="0.180000"/>
      </WheelClipInfo>
    </Item>
    <Item type="CFirstPersonDriveByLookAroundData">
      <Name>TRUCK_RALLY_FRONT_LEFT</Name>
      <AllowLookback value="false"/>
      <HeadingLimits x="-90.000000" y="120.000000"/>
      <DataLeft>
        <Offsets>
          <Item>
            <Offset value="0.000000"/>
            <AngleToBlendInOffset x="0.000000" y="0.000000"/>
          </Item>
          <Item>
            <Offset value="0.200000"/>
            <AngleToBlendInOffset x="5.000000" y="120.000000"/>
          </Item>
          <Item>
            <Offset value="0.150000"/>
            <AngleToBlendInOffset x="5.000000" y="120.000000"/>
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-5.000000" y="5.000000"/>
        <AngleToBlendInExtraPitch x="5.000000" y="90.000000"/>
      </DataLeft>
      <DataRight>
        <Offsets>
          <Item>
            <Offset value="0.000000"/>
            <AngleToBlendInOffset x="0.000000" y="0.000000"/>
          </Item>
          <Item>
            <Offset value="0.000000"/>
            <AngleToBlendInOffset x="0.000000" y="0.000000"/>
          </Item>
          <Item>
            <Offset value="0.070000"/>
            <AngleToBlendInOffset x="5.000000" y="50.000000"/>
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-20.000000" y="5.000000"/>
        <AngleToBlendInExtraPitch x="25.000000" y="195.000000"/>
      </DataRight>
      <WheelClipInfo>
        <HeadingLimitsLeft x="0.380000" y="0.430000"/>
        <HeadingLimitsRight x="0.480000" y="0.530000"/>
        <PitchLimits x="0.470000" y="0.530000"/>
        <PitchOffset x="0.000000" y="0.050000"/>
        <WheelAngleLimits x="-0.700000" y="0.000000"/>
        <WheelAngleOffset x="0.010000" y="0.040000"/>
        <MaxWheelOffsetY value="0.170000"/>
        <WheelClipLerpInRate value="0.150000"/>
        <WheelClipLerpOutRate value="0.180000"/>
      </WheelClipInfo>
    </Item>
    <Item type="CFirstPersonDriveByLookAroundData">
      <Name>TRUCK_RALLY_FRONT_RIGHT</Name>
      <AllowLookback value="true"/>
      <HeadingLimits x="-90.000000" y="120.000000"/>
      <DataLeft>
        <Offsets>
          <Item>
            <Offset value="0.000000"/>
            <AngleToBlendInOffset x="0.000000" y="0.000000"/>
          </Item>
          <Item>
            <Offset value="0.000000"/>
            <AngleToBlendInOffset x="0.000000" y="0.000000"/>
          </Item>
          <Item>
            <Offset value="0.000000"/>
            <AngleToBlendInOffset x="0.000000" y="0.000000"/>
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-17.500000" y="0.000000"/>
        <AngleToBlendInExtraPitch x="25.000000" y="195.000000"/>
      </DataLeft>
      <DataRight>
        <Offsets>
          <Item>
            <Offset value="0.000000"/>
            <AngleToBlendInOffset x="0.000000" y="0.000000"/>
          </Item>
          <Item>
            <Offset value="0.150000"/>
            <AngleToBlendInOffset x="5.000000" y="120.000000"/>
          </Item>
          <Item>
            <Offset value="0.065000"/>
            <AngleToBlendInOffset x="5.000000" y="120.000000"/>
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-10.000000" y="5.000000"/>
        <AngleToBlendInExtraPitch x="5.000000" y="90.000000"/>
      </DataRight>
      <WheelClipInfo>
        <HeadingLimitsLeft x="0.380000" y="0.430000"/>
        <HeadingLimitsRight x="0.480000" y="0.530000"/>
        <PitchLimits x="0.470000" y="0.530000"/>
        <PitchOffset x="0.000000" y="0.050000"/>
        <WheelAngleLimits x="-0.700000" y="0.000000"/>
        <WheelAngleOffset x="0.010000" y="0.040000"/>
        <MaxWheelOffsetY value="0.170000"/>
        <WheelClipLerpInRate value="0.150000"/>
        <WheelClipLerpOutRate value="0.180000"/>
      </WheelClipInfo>
    </Item>
    <Item type="CFirstPersonDriveByLookAroundData">
      <Name>PLANE_LUXOR_BACK_RIGHT</Name>
      <AllowLookback value="false"/>
      <HeadingLimits x="-90.000000" y="90.000000"/>
      <DataLeft>
        <Offsets>
          <Item>
            <Offset value="0.000000"/>
            <AngleToBlendInOffset x="0.000000" y="0.000000"/>
          </Item>
          <Item>
            <Offset value="0.000000"/>
            <AngleToBlendInOffset x="0.000000" y="0.000000"/>
          </Item>
          <Item>
            <Offset value="0.000000"/>
            <AngleToBlendInOffset x="0.000000" y="0.000000"/>
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-12.000000" y="4.000000"/>
        <AngleToBlendInExtraPitch x="0.000000" y="0.000000"/>
      </DataLeft>
      <DataRight>
        <Offsets>
          <Item>
            <Offset value="0.000000"/>
            <AngleToBlendInOffset x="0.000000" y="0.000000"/>
          </Item>
          <Item>
            <Offset value="0.000000"/>
            <AngleToBlendInOffset x="0.000000" y="0.000000"/>
          </Item>
          <Item>
            <Offset value="0.000000"/>
            <AngleToBlendInOffset x="0.000000" y="0.000000"/>
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-12.000000" y="4.000000"/>
        <AngleToBlendInExtraPitch x="0.000000" y="0.000000"/>
      </DataRight>
      <WheelClipInfo>
        <HeadingLimitsLeft x="0.380000" y="0.430000"/>
        <HeadingLimitsRight x="0.480000" y="0.530000"/>
        <PitchLimits x="0.470000" y="0.530000"/>
        <PitchOffset x="0.000000" y="0.050000"/>
        <WheelAngleLimits x="-0.700000" y="0.000000"/>
        <WheelAngleOffset x="0.010000" y="0.040000"/>
        <MaxWheelOffsetY value="0.170000"/>
        <WheelClipLerpInRate value="0.150000"/>
        <WheelClipLerpOutRate value="0.180000"/>
      </WheelClipInfo>
    </Item>
    <Item type="CFirstPersonDriveByLookAroundData">
      <Name>PLANE_LUXOR_BACK_LEFT</Name>
      <AllowLookback value="false"/>
      <HeadingLimits x="-90.000000" y="90.000000"/>
      <DataLeft>
        <Offsets>
          <Item>
            <Offset value="0.000000"/>
            <AngleToBlendInOffset x="0.000000" y="0.000000"/>
          </Item>
          <Item>
            <Offset value="0.000000"/>
            <AngleToBlendInOffset x="0.000000" y="0.000000"/>
          </Item>
          <Item>
            <Offset value="0.000000"/>
            <AngleToBlendInOffset x="0.000000" y="0.000000"/>
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-9.000000" y="5.000000"/>
        <AngleToBlendInExtraPitch x="0.000000" y="0.000000"/>
      </DataLeft>
      <DataRight>
        <Offsets>
          <Item>
            <Offset value="0.000000"/>
            <AngleToBlendInOffset x="0.000000" y="0.000000"/>
          </Item>
          <Item>
            <Offset value="0.000000"/>
            <AngleToBlendInOffset x="0.000000" y="0.000000"/>
          </Item>
          <Item>
            <Offset value="0.000000"/>
            <AngleToBlendInOffset x="0.000000" y="0.000000"/>
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-9.000000" y="5.000000"/>
        <AngleToBlendInExtraPitch x="0.000000" y="0.000000"/>
      </DataRight>
      <WheelClipInfo>
        <HeadingLimitsLeft x="0.380000" y="0.430000"/>
        <HeadingLimitsRight x="0.480000" y="0.530000"/>
        <PitchLimits x="0.470000" y="0.530000"/>
        <PitchOffset x="0.000000" y="0.050000"/>
        <WheelAngleLimits x="-0.700000" y="0.000000"/>
        <WheelAngleOffset x="0.010000" y="0.040000"/>
        <MaxWheelOffsetY value="0.170000"/>
        <WheelClipLerpInRate value="0.150000"/>
        <WheelClipLerpOutRate value="0.180000"/>
      </WheelClipInfo>
    </Item>
    <Item type="CFirstPersonDriveByLookAroundData">
      <Name>LOW_FELTZER3_FRONT_LEFT</Name>
      <AllowLookback value="false"/>
      <HeadingLimits x="-100.000000" y="160.000000"/>
      <DataLeft>
        <Offsets>
          <Item>
            <Offset value="-0.275000"/>
            <AngleToBlendInOffset x="30.000000" y="100.000000"/>
          </Item>
          <Item>
            <Offset value="0.175000"/>
            <AngleToBlendInOffset x="10.000000" y="90.000000"/>
          </Item>
          <Item>
            <Offset value="0.050000"/>
            <AngleToBlendInOffset x="0.000000" y="140.000000"/>
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-10.000000" y="10.000000"/>
        <AngleToBlendInExtraPitch x="50.000000" y="100.000000"/>
      </DataLeft>
      <DataRight>
        <Offsets>
          <Item>
            <Offset value="0.000000"/>
            <AngleToBlendInOffset x="0.000000" y="1.000000"/>
          </Item>
          <Item>
            <Offset value="0.125000"/>
            <AngleToBlendInOffset x="0.000000" y="75.000000"/>
          </Item>
          <Item>
            <Offset value="0.050000"/>
            <AngleToBlendInOffset x="0.000000" y="75.000000"/>
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-7.500000" y="-12.500000"/>
        <AngleToBlendInExtraPitch x="35.000000" y="75.000000"/>
      </DataRight>
      <WheelClipInfo>
        <HeadingLimitsLeft x="0.380000" y="0.430000"/>
        <HeadingLimitsRight x="0.480000" y="0.530000"/>
        <PitchLimits x="0.470000" y="0.530000"/>
        <PitchOffset x="0.000000" y="0.050000"/>
        <WheelAngleLimits x="-0.700000" y="0.000000"/>
        <WheelAngleOffset x="0.010000" y="0.040000"/>
        <MaxWheelOffsetY value="0.170000"/>
        <WheelClipLerpInRate value="0.150000"/>
        <WheelClipLerpOutRate value="0.180000"/>
      </WheelClipInfo>
    </Item>
    <Item type="CFirstPersonDriveByLookAroundData">
      <Name>LOW_FELTZER3_FRONT_RIGHT</Name>
      <AllowLookback value="false"/>
      <HeadingLimits x="-100.000000" y="160.000000"/>
      <DataLeft>
        <Offsets>
          <Item>
            <Offset value="0.000000"/>
            <AngleToBlendInOffset x="0.000000" y="1.000000"/>
          </Item>
          <Item>
            <Offset value="0.125000"/>
            <AngleToBlendInOffset x="0.000000" y="75.000000"/>
          </Item>
          <Item>
            <Offset value="0.050000"/>
            <AngleToBlendInOffset x="0.000000" y="75.000000"/>
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-7.500000" y="-12.500000"/>
        <AngleToBlendInExtraPitch x="35.000000" y="75.000000"/>
      </DataLeft>
      <DataRight>
        <Offsets>
          <Item>
            <Offset value="0.275000"/>
            <AngleToBlendInOffset x="30.000000" y="100.000000"/>
          </Item>
          <Item>
            <Offset value="0.175000"/>
            <AngleToBlendInOffset x="10.000000" y="90.000000"/>
          </Item>
          <Item>
            <Offset value="0.050000"/>
            <AngleToBlendInOffset x="0.000000" y="140.000000"/>
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-10.000000" y="10.000000"/>
        <AngleToBlendInExtraPitch x="50.000000" y="100.000000"/>
      </DataRight>
      <WheelClipInfo>
        <HeadingLimitsLeft x="0.380000" y="0.430000"/>
        <HeadingLimitsRight x="0.480000" y="0.530000"/>
        <PitchLimits x="0.470000" y="0.530000"/>
        <PitchOffset x="0.000000" y="0.050000"/>
        <WheelAngleLimits x="-0.700000" y="0.000000"/>
        <WheelAngleOffset x="0.010000" y="0.040000"/>
        <MaxWheelOffsetY value="0.170000"/>
        <WheelClipLerpInRate value="0.150000"/>
        <WheelClipLerpOutRate value="0.180000"/>
      </WheelClipInfo>
    </Item>
    <Item type="CFirstPersonDriveByLookAroundData">
      <Name>LOW_OSIRIS_FRONT_LEFT</Name>
      <AllowLookback value="false"/>
      <HeadingLimits x="-95.000000" y="100.000000"/>
      <DataLeft>
        <Offsets>
          <Item>
            <Offset value="0.075000"/>
            <AngleToBlendInOffset x="20.000000" y="100.000000"/>
          </Item>
          <Item>
            <Offset value="-0.250000"/>
            <AngleToBlendInOffset x="0.000000" y="55.000000"/>
          </Item>
          <Item>
            <Offset value="0.030000"/>
            <AngleToBlendInOffset x="45.000000" y="100.000000"/>
          </Item>
        </Offsets>
        <ExtraRelativePitch x="0.000000" y="-7.500000"/>
        <AngleToBlendInExtraPitch x="0.000000" y="0.000000"/>
      </DataLeft>
      <DataRight>
        <Offsets>
          <Item>
            <Offset value="0.000000"/>
            <AngleToBlendInOffset x="0.000000" y="0.000000"/>
          </Item>
          <Item>
            <Offset value="-0.150000"/>
            <AngleToBlendInOffset x="0.000000" y="65.000000"/>
          </Item>
          <Item>
            <Offset value="0.000000"/>
            <AngleToBlendInOffset x="0.000000" y="0.000000"/>
          </Item>
        </Offsets>
        <ExtraRelativePitch x="0.000000" y="-7.500000"/>
        <AngleToBlendInExtraPitch x="0.000000" y="0.000000"/>
      </DataRight>
      <WheelClipInfo>
        <HeadingLimitsLeft x="0.380000" y="0.430000"/>
        <HeadingLimitsRight x="0.480000" y="0.530000"/>
        <PitchLimits x="0.470000" y="0.530000"/>
        <PitchOffset x="0.000000" y="0.050000"/>
        <WheelAngleLimits x="-0.700000" y="0.000000"/>
        <WheelAngleOffset x="0.010000" y="0.040000"/>
        <MaxWheelOffsetY value="0.000000"/>
        <WheelClipLerpInRate value="0.150000"/>
        <WheelClipLerpOutRate value="0.180000"/>
      </WheelClipInfo>
    </Item>
    <Item type="CFirstPersonDriveByLookAroundData">
      <Name>LOW_OSIRIS_FRONT_RIGHT</Name>
      <AllowLookback value="false"/>
      <HeadingLimits x="-95.000000" y="100.000000"/>
      <DataLeft>
        <Offsets>
          <Item>
            <Offset value="0.000000"/>
            <AngleToBlendInOffset x="0.000000" y="0.000000"/>
          </Item>
          <Item>
            <Offset value="-0.100000"/>
            <AngleToBlendInOffset x="0.000000" y="65.000000"/>
          </Item>
          <Item>
            <Offset value="0.000000"/>
            <AngleToBlendInOffset x="0.000000" y="0.000000"/>
          </Item>
        </Offsets>
        <ExtraRelativePitch x="0.000000" y="-7.500000"/>
        <AngleToBlendInExtraPitch x="0.000000" y="0.000000"/>
      </DataLeft>
      <DataRight>
        <Offsets>
          <Item>
            <Offset value="-0.075000"/>
            <AngleToBlendInOffset x="20.000000" y="100.000000"/>
          </Item>
          <Item>
            <Offset value="-0.250000"/>
            <AngleToBlendInOffset x="0.000000" y="55.000000"/>
          </Item>
          <Item>
            <Offset value="0.030000"/>
            <AngleToBlendInOffset x="45.000000" y="100.000000"/>
          </Item>
        </Offsets>
        <ExtraRelativePitch x="0.000000" y="-7.500000"/>
        <AngleToBlendInExtraPitch x="0.000000" y="0.000000"/>
      </DataRight>
      <WheelClipInfo>
        <HeadingLimitsLeft x="0.380000" y="0.430000"/>
        <HeadingLimitsRight x="0.480000" y="0.530000"/>
        <PitchLimits x="0.470000" y="0.530000"/>
        <PitchOffset x="0.000000" y="0.050000"/>
        <WheelAngleLimits x="-0.700000" y="0.000000"/>
        <WheelAngleOffset x="0.010000" y="0.040000"/>
        <MaxWheelOffsetY value="0.000000"/>
        <WheelClipLerpInRate value="0.150000"/>
        <WheelClipLerpOutRate value="0.180000"/>
      </WheelClipInfo>
    </Item>
    <Item type="CFirstPersonDriveByLookAroundData">
      <Name>STD_DOMINATOR_FRONT_LEFT</Name>
      <AllowLookback value="true"/>
      <HeadingLimits x="-190.000000" y="160.000000"/>
      <DataLeft>
        <Offsets>
          <Item>
            <Offset value="-0.280000"/>
            <AngleToBlendInOffset x="40.000000" y="120.000000"/>
          </Item>
          <Item>
            <Offset value="0.050000"/>
            <AngleToBlendInOffset x="60.000000" y="160.000000"/>
          </Item>
          <Item>
            <Offset value="0.110000"/>
            <AngleToBlendInOffset x="5.000000" y="50.000000"/>
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-8.000000" y="20.000000"/>
        <AngleToBlendInExtraPitch x="45.000000" y="110.000000"/>
      </DataLeft>
      <DataRight>
        <Offsets>
          <Item>
            <Offset value="0.000000"/>
            <AngleToBlendInOffset x="45.000000" y="90.000000"/>
          </Item>
          <Item>
            <Offset value="0.000000"/>
            <AngleToBlendInOffset x="0.000000" y="0.000000"/>
          </Item>
          <Item>
            <Offset value="0.145000"/>
            <AngleToBlendInOffset x="5.000000" y="50.000000"/>
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-2.500000" y="-12.500000"/>
        <AngleToBlendInExtraPitch x="0.000000" y="190.000000"/>
      </DataRight>
    </Item>
    <Item type="CFirstPersonDriveByLookAroundData">
      <Name>STD_DOMINATOR_FRONT_RIGHT</Name>
      <AllowLookback value="true"/>
      <HeadingLimits x="-190.000000" y="160.000000"/>
      <DataLeft>
        <Offsets>
          <Item>
            <Offset value="0.000000"/>
            <AngleToBlendInOffset x="45.000000" y="90.000000"/>
          </Item>
          <Item>
            <Offset value="0.000000"/>
            <AngleToBlendInOffset x="0.000000" y="0.000000"/>
          </Item>
          <Item>
            <Offset value="0.075000"/>
            <AngleToBlendInOffset x="90.000000" y="180.000000"/>
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-2.500000" y="-12.500000"/>
        <AngleToBlendInExtraPitch x="0.000000" y="190.000000"/>
      </DataLeft>
      <DataRight>
        <Offsets>
          <Item>
            <Offset value="0.280000"/>
            <AngleToBlendInOffset x="40.000000" y="120.000000"/>
          </Item>
          <Item>
            <Offset value="0.050000"/>
            <AngleToBlendInOffset x="60.000000" y="160.000000"/>
          </Item>
          <Item>
            <Offset value="0.040000"/>
            <AngleToBlendInOffset x="30.000000" y="90.000000"/>
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-8.000000" y="20.000000"/>
        <AngleToBlendInExtraPitch x="45.000000" y="110.000000"/>
      </DataRight>
    </Item>
    <Item type="CFirstPersonDriveByLookAroundData">
      <Name>STD_KURUMA2_FRONT_LEFT</Name>
      <AllowLookback value="true"/>
      <HeadingLimits x="-180.000000" y="90.000000"/>
      <DataLeft>
        <Offsets>
          <Item>
            <Offset value="0.165000"/>
            <AngleToBlendInOffset x="10.000000" y="40.000000"/>
          </Item>
          <Item>
            <Offset value="0.000000"/>
            <AngleToBlendInOffset x="0.000000" y="65.000000"/>
          </Item>
          <Item>
            <Offset value="0.038000"/>
            <AngleToBlendInOffset x="60.000000" y="90.000000"/>
          </Item>
        </Offsets>
        <ExtraRelativePitch x="0.000000" y="-10.000000"/>
        <AngleToBlendInExtraPitch x="0.000000" y="0.000000"/>
      </DataLeft>
      <DataRight>
        <Offsets>
          <Item>
            <Offset value="0.100000"/>
            <AngleToBlendInOffset x="0.000000" y="180.000000"/>
          </Item>
          <Item>
            <Offset value="0.000000"/>
            <AngleToBlendInOffset x="0.000000" y="180.000000"/>
          </Item>
          <Item>
            <Offset value="0.000000"/>
            <AngleToBlendInOffset x="90.000000" y="180.000000"/>
          </Item>
        </Offsets>
        <ExtraRelativePitch x="0.000000" y="-10.000000"/>
        <AngleToBlendInExtraPitch x="0.000000" y="0.000000"/>
      </DataRight>
    </Item>
    <Item type="CFirstPersonDriveByLookAroundData">
      <Name>STD_KURUMA2_FRONT_RIGHT</Name>
      <AllowLookback value="true"/>
      <HeadingLimits x="-180.000000" y="90.000000"/>
      <DataLeft>
        <Offsets>
          <Item>
            <Offset value="-0.060000"/>
            <AngleToBlendInOffset x="0.000000" y="180.000000"/>
          </Item>
          <Item>
            <Offset value="-0.020000"/>
            <AngleToBlendInOffset x="90.000000" y="140.000000"/>
          </Item>
          <Item>
            <Offset value="0.020000"/>
            <AngleToBlendInOffset x="90.000000" y="180.000000"/>
          </Item>
        </Offsets>
        <ExtraRelativePitch x="0.000000" y="-10.000000"/>
        <AngleToBlendInExtraPitch x="0.000000" y="0.000000"/>
      </DataLeft>
      <DataRight>
        <Offsets>
          <Item>
            <Offset value="-0.165000"/>
            <AngleToBlendInOffset x="10.000000" y="40.000000"/>
          </Item>
          <Item>
            <Offset value="0.000000"/>
            <AngleToBlendInOffset x="0.000000" y="65.000000"/>
          </Item>
          <Item>
            <Offset value="0.028000"/>
            <AngleToBlendInOffset x="60.000000" y="90.000000"/>
          </Item>
        </Offsets>
        <ExtraRelativePitch x="0.000000" y="-10.000000"/>
        <AngleToBlendInExtraPitch x="0.000000" y="0.000000"/>
      </DataRight>
    </Item>
    <Item type="CFirstPersonDriveByLookAroundData">
      <Name>STD_KURUMA2_REAR_LEFT</Name>
      <AllowLookback value="true"/>
      <HeadingLimits x="-210.000000" y="95.000000"/>
      <DataLeft>
        <Offsets>
          <Item>
            <Offset value="0.160000"/>
            <AngleToBlendInOffset x="10.000000" y="80.000000"/>
          </Item>
          <Item>
            <Offset value="0.000000"/>
            <AngleToBlendInOffset x="0.000000" y="80.000000"/>
          </Item>
          <Item>
            <Offset value="0.000000"/>
            <AngleToBlendInOffset x="0.000000" y="80.000000"/>
          </Item>
        </Offsets>
        <ExtraRelativePitch x="0.000000" y="-10.000000"/>
        <AngleToBlendInExtraPitch x="0.000000" y="0.000000"/>
      </DataLeft>
      <DataRight>
        <Offsets>
          <Item>
            <Offset value="-0.100000"/>
            <AngleToBlendInOffset x="0.000000" y="190.000000"/>
          </Item>
          <Item>
            <Offset value="0.000000"/>
            <AngleToBlendInOffset x="0.000000" y="190.000000"/>
          </Item>
          <Item>
            <Offset value="0.000000"/>
            <AngleToBlendInOffset x="0.000000" y="190.000000"/>
          </Item>
        </Offsets>
        <ExtraRelativePitch x="0.000000" y="-10.000000"/>
        <AngleToBlendInExtraPitch x="0.000000" y="0.000000"/>
      </DataRight>
    </Item>
    <Item type="CFirstPersonDriveByLookAroundData">
      <Name>STD_KURUMA2_REAR_RIGHT</Name>
      <AllowLookback value="true"/>
      <HeadingLimits x="-210.000000" y="95.000000"/>
      <DataLeft>
        <Offsets>
          <Item>
            <Offset value="0.150000"/>
            <AngleToBlendInOffset x="0.000000" y="190.000000"/>
          </Item>
          <Item>
            <Offset value="0.000000"/>
            <AngleToBlendInOffset x="0.000000" y="190.000000"/>
          </Item>
          <Item>
            <Offset value="0.000000"/>
            <AngleToBlendInOffset x="0.000000" y="190.000000"/>
          </Item>
        </Offsets>
        <ExtraRelativePitch x="0.000000" y="-10.000000"/>
        <AngleToBlendInExtraPitch x="0.000000" y="0.000000"/>
      </DataLeft>
      <DataRight>
        <Offsets>
          <Item>
            <Offset value="-0.160000"/>
            <AngleToBlendInOffset x="10.000000" y="80.000000"/>
          </Item>
          <Item>
            <Offset value="0.000000"/>
            <AngleToBlendInOffset x="0.000000" y="80.000000"/>
          </Item>
          <Item>
            <Offset value="0.000000"/>
            <AngleToBlendInOffset x="0.000000" y="80.000000"/>
          </Item>
        </Offsets>
        <ExtraRelativePitch x="0.000000" y="-10.000000"/>
        <AngleToBlendInExtraPitch x="0.000000" y="0.000000"/>
      </DataRight>
    </Item>
    <Item type="CFirstPersonDriveByLookAroundData">
      <Name>VAN_SLAMVAN2_FRONT_LEFT</Name>
      <AllowLookback value="false"/>
      <HeadingLimits x="-95.000000" y="170.000000"/>
      <DataLeft>
        <Offsets>
          <Item>
            <Offset value="-0.350000"/>
            <AngleToBlendInOffset x="45.000000" y="120.000000"/>
          </Item>
          <Item>
            <Offset value="0.075000"/>
            <AngleToBlendInOffset x="45.000000" y="170.000000"/>
          </Item>
          <Item>
            <Offset value="0.080000"/>
            <AngleToBlendInOffset x="10.000000" y="60.000000"/>
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-20.000000" y="10.000000"/>
        <AngleToBlendInExtraPitch x="5.000000" y="90.000000"/>
      </DataLeft>
      <DataRight>
        <Offsets>
          <Item>
            <Offset value="0.000000"/>
            <AngleToBlendInOffset x="0.000000" y="0.000000"/>
          </Item>
          <Item>
            <Offset value="0.000000"/>
            <AngleToBlendInOffset x="0.000000" y="0.000000"/>
          </Item>
          <Item>
            <Offset value="0.070000"/>
            <AngleToBlendInOffset x="5.000000" y="60.000000"/>
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-7.500000" y="-5.000000"/>
        <AngleToBlendInExtraPitch x="0.000000" y="95.000000"/>
      </DataRight>
    </Item>
    <Item type="CFirstPersonDriveByLookAroundData">
      <Name>VAN_SLAMVAN2_FRONT_RIGHT</Name>
      <AllowLookback value="false"/>
      <HeadingLimits x="-95.000000" y="170.000000"/>
      <DataLeft>
        <Offsets>
          <Item>
            <Offset value="0.000000"/>
            <AngleToBlendInOffset x="0.000000" y="0.000000"/>
          </Item>
          <Item>
            <Offset value="0.000000"/>
            <AngleToBlendInOffset x="0.000000" y="0.000000"/>
          </Item>
          <Item>
            <Offset value="0.070000"/>
            <AngleToBlendInOffset x="5.000000" y="60.000000"/>
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-7.500000" y="-5.000000"/>
        <AngleToBlendInExtraPitch x="0.000000" y="95.000000"/>
      </DataLeft>
      <DataRight>
        <Offsets>
          <Item>
            <Offset value="0.350000"/>
            <AngleToBlendInOffset x="45.000000" y="120.000000"/>
          </Item>
          <Item>
            <Offset value="0.075000"/>
            <AngleToBlendInOffset x="45.000000" y="170.000000"/>
          </Item>
          <Item>
            <Offset value="0.080000"/>
            <AngleToBlendInOffset x="10.000000" y="60.000000"/>
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-20.000000" y="10.000000"/>
        <AngleToBlendInExtraPitch x="5.000000" y="90.000000"/>
      </DataRight>
    </Item>
    <Item type="CFirstPersonDriveByLookAroundData">
      <Name>VAN_SLAMVAN2_REAR_LEFT</Name>
      <AllowLookback value="true"/>
      <HeadingLimits x="-110.000000" y="-20.000000"/>
      <DataLeft>
        <Offsets>
          <Item>
            <Offset value="0.00000"/>
            <AngleToBlendInOffset x="0.000000" y="0.000000"/>
          </Item>
          <Item>
            <Offset value="0.000000"/>
            <AngleToBlendInOffset x="0.000000" y="0.000000"/>
          </Item>
          <Item>
            <Offset value="0.000000"/>
            <AngleToBlendInOffset x="0.000000" y="0.000000"/>
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-17.000000" y="10.000000"/>
        <AngleToBlendInExtraPitch x="0.000000" y="0.000000"/>
      </DataLeft>
      <DataRight>
        <Offsets>
          <Item>
            <Offset value="-0.250000"/>
            <AngleToBlendInOffset x="0.000000" y="50.000000"/>
          </Item>
          <Item>
            <Offset value="0.050000"/>
            <AngleToBlendInOffset x="30.000000" y="50.000000"/>
          </Item>
          <Item>
            <Offset value="0.000000"/>
            <AngleToBlendInOffset x="0.000000" y="0.000000"/>
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-17.000000" y="10.000000"/>
        <AngleToBlendInExtraPitch x="0.000000" y="0.000000"/>
      </DataRight>
    </Item>
    <Item type="CFirstPersonDriveByLookAroundData">
      <Name>VAN_SLAMVAN2_REAR_RIGHT</Name>
      <AllowLookback value="true"/>
      <HeadingLimits x="-110.000000" y="-20.000000"/>
      <DataLeft>
        <Offsets>
          <Item>
            <Offset value="0.200000"/>
            <AngleToBlendInOffset x="50.000000" y="110.000000"/>
          </Item>
          <Item>
            <Offset value="0.000000"/>
            <AngleToBlendInOffset x="30.000000" y="50.000000"/>
          </Item>
          <Item>
            <Offset value="0.000000"/>
            <AngleToBlendInOffset x="0.000000" y="0.000000"/>
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-17.000000" y="10.000000"/>
        <AngleToBlendInExtraPitch x="0.000000" y="0.000000"/>
      </DataLeft>
      <DataRight>
        <Offsets>
          <Item>
            <Offset value="0.00000"/>
            <AngleToBlendInOffset x="0.000000" y="0.000000"/>
          </Item>
          <Item>
            <Offset value="0.000000"/>
            <AngleToBlendInOffset x="0.000000" y="0.000000"/>
          </Item>
          <Item>
            <Offset value="0.000000"/>
            <AngleToBlendInOffset x="0.000000" y="0.000000"/>
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-17.000000" y="10.000000"/>
        <AngleToBlendInExtraPitch x="0.000000" y="0.000000"/>
      </DataRight>
    </Item>
  </FirstPersonDriveByLookAroundData>
</CVehicleMetadataMgr>



















