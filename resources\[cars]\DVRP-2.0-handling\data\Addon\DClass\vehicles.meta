<?xml version="1.0" encoding="UTF-8"?>
<CVehicleModelInfo__InitDataList>
  <residentTxd>vehshare</residentTxd>
  <residentAnims/>
  <InitDatas>
    <Item>
      <modelName>dcerberus</modelName>
      <txdName>dcerberus</txdName>
      <handlingId>DCERBERUS</handlingId>
      <gameName>DCERBERUS</gameName>
      <vehicleMakeName>MTL</vehicleMakeName>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected/>
      <ptfxAssetName>null</ptfxAssetName>
      <audioNameHash>CERBERUS</audioNameHash>
      <layout>LAYOUT_TRUCK</layout>
      <coverBoundOffsets>PHANTOM_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_TRUCK</explosionInfo>
      <scenarioLayout/>
      <cameraName>FOLLOW_ARTIC_CAMERA</cameraName>
      <aimCameraName>ARTIC_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>VEHICLE_BONNET_CAMERA_STANDARD_LONG</bonnetCameraName>
      <povCameraName>DEFAULT_POV_CAMERA_NO_REVERSE_LOOKAROUND_MID</povCameraName>
      <FirstPersonDriveByIKOffset x="-0.003000" y="-0.033000" z="-0.018000"/>
      <FirstPersonDriveByUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonProjectileDriveByIKOffset x="0.055000" y="-0.083000" z="0.000000"/>
      <FirstPersonProjectileDriveByPassengerIKOffset x="0.023000" y="0.030000" z="-0.045000"/>
      <FirstPersonProjectileDriveByRearLeftIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonProjectileDriveByRearRightIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonDriveByLeftPassengerIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonDriveByRightPassengerIKOffset x="-0.003000" y="-0.033000" z="-0.018000"/>
      <FirstPersonDriveByRightRearPassengerIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonDriveByLeftPassengerUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonDriveByRightPassengerUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonMobilePhoneOffset x="0.155000" y="0.370000" z="0.433000"/>
      <FirstPersonPassengerMobilePhoneOffset x="0.209000" y="0.276000" z="0.420000"/>
      <PovCameraOffset x="0.000000" y="-0.075000" z="0.580000"/>
      <PovCameraVerticalAdjustmentForRollCage value="0.000000"/>
      <PovPassengerCameraOffset x="-0.035000" y="0.000000" z="0.070000"/>
      <PovRearPassengerCameraOffset x="-0.035000" y="0.000000" z="0.070000"/>
      <vfxInfoName>VFXVEHICLEINFO_TRUCK_RIG</vfxInfoName>
      <shouldUseCinematicViewMode value="true"/>
      <shouldCameraTransitionOnClimbUpDown value="false"/>
      <shouldCameraIgnoreExiting value="false"/>
      <AllowPretendOccupants value="true"/>
      <AllowJoyriding value="false"/>
      <AllowSundayDriving value="false"/>
      <AllowBodyColorMapping value="true"/>
      <wheelScale value="0.450000"/>
      <wheelScaleRear value="0.450000"/>
      <dirtLevelMin value="0.700000"/>
      <dirtLevelMax value="1.000000"/>
      <envEffScaleMin value="0.000000"/>
      <envEffScaleMax value="1.000000"/>
      <envEffScaleMin2 value="0.000000"/>
      <envEffScaleMax2 value="1.000000"/>
      <damageMapScale value="0.600000"/>
      <damageOffsetScale value="1.000000"/>
      <diffuseTint value="0x00FFFFFF"/>
      <steerWheelMult value="1.000000"/>
      <HDTextureDist value="5.000000"/>
      <lodDistances content="float_array">
        25.000000	
        50.000000	
        100.000000	
        200.000000	
        750.000000	
        750.000000
      </lodDistances>
      <minSeatHeight value="1.182"/>
      <identicalModelSpawnDistance value="20"/>
      <maxNumOfSameColor value="10"/>
      <defaultBodyHealth value="1000.000000"/>
      <pretendOccupantsScale value="1.000000"/>
      <visibleSpawnDistScale value="1.000000"/>
      <trackerPathWidth value="2.000000"/>
      <weaponForceMult value="1.000000"/>
      <frequency value="100"/>
      <swankness>SWANKNESS_3</swankness>
      <maxNum value="5"/>
      <flags>FLAG_BIG FLAG_CAN_HONK_WHEN_FLEEING FLAG_AVOID_TURNS FLAG_EXTRAS_STRONG FLAG_IS_BULKY FLAG_BLOCK_FROM_ATTRACTOR_SCENARIO FLAG_ATTACH_TRAILER_ON_HIGHWAY FLAG_ATTACH_TRAILER_IN_CITY FLAG_USE_STRICTER_EXIT_COLLISION_TESTS</flags>
      <type>VEHICLE_TYPE_CAR</type>
      <plateType>VPT_FRONT_AND_BACK_PLATES</plateType>
      <dashboardType>VDT_TRUCK</dashboardType>
      <vehicleClass>VC_COMMERCIAL</vehicleClass>
      <wheelType>VWT_SPORT</wheelType>
      <trailers>
        <Item>dtanker</Item>
        <Item>dtanker</Item>
      </trailers>
      <additionalTrailers>
        <Item>tr2</Item>
        <Item>tr4</Item>
      </additionalTrailers>
      <drivers>
        <Item>
          <driverName>S_M_M_Trucker_01</driverName>
          <npcName/>
        </Item>
      </drivers>
      <extraIncludes/>
      <doorsWithCollisionWhenClosed>
        <Item>VEH_EXT_BONNET</Item>
      </doorsWithCollisionWhenClosed>
      <driveableDoors/>
      <bumpersNeedToCollideWithMap value="true"/>
      <needsRopeTexture value="false"/>
      <requiredExtras/>
      <rewards/>
      <cinematicPartCamera>
        <Item>WHEEL_WIDE_REAR_RIGHT_CAMERA</Item>
        <Item>WHEEL_WIDE_REAR_LEFT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet>Truck</NmBraceOverrideSet>
      <buoyancySphereOffset x="0.000000" y="0.000000" z="-0.600000"/>
      <buoyancySphereSizeScale value="0.800000"/>
      <pOverrideRagdollThreshold type="NULL"/>
      <firstPersonDrivebyData>
        <Item>TRUCK_PHANTOM_FRONT_LEFT</Item>
        <Item>TRUCK_PACKER_FRONT_RIGHT</Item>
      </firstPersonDrivebyData>
    </Item>
    <Item>
      <modelName>dtanker</modelName>
      <txdName>dtanker</txdName>
      <handlingId>DTANKER</handlingId>
      <gameName>TRAILER</gameName>
      <vehicleMakeName/>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected/>
      <ptfxAssetName>null</ptfxAssetName>
      <audioNameHash>TANKER</audioNameHash>
      <layout>LAYOUT_TRUCK</layout>
      <coverBoundOffsets>TANKER_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_TANKER</explosionInfo>
      <scenarioLayout/>
      <cameraName>FOLLOW_ARTIC_CAMERA</cameraName>
      <aimCameraName>ARTIC_AIM_CAMERA</aimCameraName>
      <bonnetCameraName/>
      <povCameraName/>
      <FirstPersonDriveByIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonDriveByUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonProjectileDriveByIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonProjectileDriveByPassengerIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonDriveByLeftPassengerIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonDriveByRightPassengerIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonDriveByLeftPassengerUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonDriveByRightPassengerUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonMobilePhoneOffset x="0.155000" y="0.315000" z="0.520000"/>
      <PovPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000"/>
      <vfxInfoName>VFXVEHICLEINFO_TRAILER_TANKER</vfxInfoName>
      <shouldUseCinematicViewMode value="true"/>
      <shouldCameraTransitionOnClimbUpDown value="false"/>
      <shouldCameraIgnoreExiting value="false"/>
      <AllowPretendOccupants value="true"/>
      <AllowJoyriding value="true"/>
      <AllowSundayDriving value="true"/>
      <AllowBodyColorMapping value="true"/>
      <wheelScale value="0.350314"/>
      <wheelScaleRear value="0.350314"/>
      <dirtLevelMin value="0.800000"/>
      <dirtLevelMax value="0.90000"/>
      <envEffScaleMin value="0.300000"/>
      <envEffScaleMax value="0.500000"/>
      <envEffScaleMin2 value="0.300000"/>
      <envEffScaleMax2 value="0.500000"/>
      <damageMapScale value="0.600000"/>
      <damageOffsetScale value="1.000000"/>
      <diffuseTint value="0x00FFFFFF"/>
      <steerWheelMult value="1.000000"/>
      <HDTextureDist value="5.000000"/>
      <lodDistances content="float_array">
        25.000000	
        50.000000	
        100.000000	
        200.000000	
        750.000000	
        750.000000
      </lodDistances>
      <identicalModelSpawnDistance value="20"/>
      <maxNumOfSameColor value="10"/>
      <defaultBodyHealth value="120.000000"/>
      <pretendOccupantsScale value="1.000000"/>
      <visibleSpawnDistScale value="1.000000"/>
      <trackerPathWidth value="2.000000"/>
      <weaponForceMult value="1.000000"/>
      <frequency value="10"/>
      <swankness>SWANKNESS_1</swankness>
      <maxNum value="999"/>
      <flags>FLAG_EXTRAS_REQUIRE FLAG_HAS_LIVERY FLAG_EXTRAS_STRONG FLAG_PEDS_CAN_STAND_ON_TOP FLAG_GEN_NAVMESH FLAG_EXPLODE_ON_CONTACT FLAG_DONT_SPAWN_AS_AMBIENT FLAG_SPRAY_PETROL_BEFORE_EXPLOSION</flags>
      <type>VEHICLE_TYPE_TRAILER</type>
      <plateType>VPT_NONE</plateType>
      <vehicleClass>VC_UTILITY</vehicleClass>
      <wheelType>VWT_SPORT</wheelType>
      <trailers/>
      <additionalTrailers/>
      <drivers/>
      <extraIncludes/>
      <doorsWithCollisionWhenClosed/>
      <driveableDoors/>
      <bumpersNeedToCollideWithMap value="true"/>
      <needsRopeTexture value="false"/>
      <requiredExtras/>
      <rewards/>
      <cinematicPartCamera/>
      <NmBraceOverrideSet/>
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000"/>
      <buoyancySphereSizeScale value="1.000000"/>
      <pOverrideRagdollThreshold type="NULL"/>
    </Item>
  </InitDatas>
  <txdRelationships>
    <Item>
      <parent>vehshare_truck</parent>
      <child>dcerberus</child>
    </Item>
    <Item>
      <parent>vehshare_army</parent>
      <child>dtanker</child>
    </Item>
  </txdRelationships>
</CVehicleModelInfo__InitDataList>

