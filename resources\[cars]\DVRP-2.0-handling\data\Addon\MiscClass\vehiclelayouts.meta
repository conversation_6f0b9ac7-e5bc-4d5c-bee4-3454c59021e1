<?xml version="1.0" encoding="UTF-8"?>
<CVehicleMetadataMgr>
  <VehicleCoverBoundOffsetInfos>
    <Item type="CVehicleCoverBoundOffsetInfo">
      <Name>FOODCAR_COVER_OFFSET_INFO</Name>
      <ExtraSideOffset value="-0.025000"/>
      <ExtraForwardOffset value="-0.175000"/>
      <ExtraBackwardOffset value="-0.050000"/>
      <ExtraZOffset value="0.150000"/>
      <CoverBoundInfos/>
    </Item>
  </VehicleCoverBoundOffsetInfos>
  <VehicleLayoutInfos>
    <Item type="CVehicleLayoutInfo">
      <Name>LAYOUT_STD_FOODCAR</Name>
      <Seats>
        <Item>
          <SeatInfo ref="SEAT_STANDARD_FRONT_LEFT"/>
          <SeatAnimInfo ref="SEAT_ANIM_STD_ZTYPE_FRONT_LEFT"/>
        </Item>
        <Item>
          <SeatInfo ref="SEAT_STANDARD_FRONT_RIGHT"/>
          <SeatAnimInfo ref="SEAT_ANIM_STD_ZTYPE_FRONT_RIGHT"/>
        </Item>
      </Seats>
      <EntryPoints>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_STANDARD_FRONT_LEFT"/>
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_STD_EXITFIXUP_FRONT_LEFT"/>
        </Item>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_STANDARD_FRONT_RIGHT"/>
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_STD_EXITFIXUP_FRONT_RIGHT"/>
        </Item>
      </EntryPoints>
      <LayoutFlags>StreamAnims UseDoorOscillation UseLeanSteerAnims UseSteeringWheelIk UseFinerAlignTolerance</LayoutFlags>
      <BicycleInfo ref="NULL"/>
      <AnimRateSet ref="STD_LOW_ANIM_RATE_SET"/>
      <HandsUpClipSetId>busted_vehicle_std</HandsUpClipSetId>
      <SteeringWheelOffset x="0.000000" y="0.350000" z="0.320000"/>
      <MaxXAcceleration value="4.000000"/>
      <BodyLeanXApproachSpeed value="5.000000"/>
      <BodyLeanXSmallDelta value="0.300000"/>
      <LookBackApproachSpeedScale value="1.000000"/>
      <FirstPersonAdditiveIdleClipSets/>
      <FirstPersonRoadRageClipSets/>
      <CellphoneClipsets>
        <CellphoneClipsetDS>ANIM_GROUP_CELLPHONE_IN_CAR_DS</CellphoneClipsetDS>
        <CellphoneClipsetPS>ANIM_GROUP_CELLPHONE_IN_CAR_PS</CellphoneClipsetPS>
        <CellphoneClipsetDS_FPS>ANIM_GROUP_CELLPHONE_IN_CAR_DS_FPS</CellphoneClipsetDS_FPS>
        <CellphoneClipsetPS_FPS>ANIM_GROUP_CELLPHONE_IN_CAR_DS_FPS</CellphoneClipsetPS_FPS>
      </CellphoneClipsets>
    </Item>
  </VehicleLayoutInfos>
  <VehicleScenarioLayoutInfos/>
  <SeatOverrideAnimInfos/>
  <InVehicleOverrideInfos/>
  <FirstPersonDriveByLookAroundData/>
  <AnimRateSets/>
  <ClipSetMaps/>
  <BicycleInfos/>
  <POVTuningInfos/>
  <EntryAnimVariations/>
  <VehicleExtraPointsInfos/>
  <DrivebyWeaponGroups/>
  <VehicleDriveByAnimInfos/>
  <VehicleDriveByInfos/>
  <VehicleSeatInfos/>
  <VehicleSeatAnimInfos/>
  <VehicleEntryPointInfos/>
  <VehicleEntryPointAnimInfos/>
  <VehicleExplosionInfos/>
</CVehicleMetadataMgr>



















