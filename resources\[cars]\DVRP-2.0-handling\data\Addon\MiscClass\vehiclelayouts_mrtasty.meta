<?xml version="1.0" encoding="UTF-8"?>
<CVehicleMetadataMgr>
  <VehicleLayoutInfos>
    <Item type="CVehicleLayoutInfo">
      <Name>LAYOUT_VAN_BOXVILLE_1_SEAT</Name>
      <Seats>
        <Item>
          <SeatInfo ref="SEAT_VAN_FRONT_LEFT" />
          <SeatAnimInfo ref="SEAT_ANIM_RIOT_VAN_FRONT_LEFT" />
        </Item>
      </Seats>
      <EntryPoints>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_VAN_FRONT_LEFT" />
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_VAN_FRONT_LEFT" />
        </Item>
      </EntryPoints>
      <LayoutFlags>StreamAnims UseVanOpenDoorBlendParams UseDoorOscillation UseLeanSteerAnims</LayoutFlags>
      <BicycleInfo ref="NULL" />
      <HandsUpClipSetId>busted_vehicle_van</HandsUpClipSetId>
      <SteeringWheelOffset x="0.000000" y="0.350000" z="0.320000" />
      <MaxXAcceleration value="4.00000" />
      <BodyLeanXApproachSpeed value="5.00000" />
      <BodyLeanXSmallDelta value="0.30000" />
      <FirstPersonAdditiveIdleClipSets>
        <Item>clipset@veh@van@ds@idle_a</Item>
        <Item>clipset@veh@van@ds@idle_b</Item>
        <Item>clipset@veh@van@ds@idle_c</Item>
        <Item>clipset@veh@van@ds@idle_d</Item>
        <Item>clipset@veh@van@ds@idle_e</Item>
      </FirstPersonAdditiveIdleClipSets>
      <FirstPersonRoadRageClipSets>
        <Item>clipset@veh@van@ds@hit_wheel@idle_a</Item>
        <Item>clipset@veh@van@ds@hit_wheel@idle_b</Item>
        <Item>clipset@veh@van@ds@hit_wheel@idle_c</Item>
      </FirstPersonRoadRageClipSets>
    </Item>
  </VehicleLayoutInfos>
</CVehicleMetadataMgr>