<?xml version="1.0" encoding="UTF-8"?>
<CVehicleModelInfo__InitDataList>
  <residentTxd>vehshare</residentTxd>
  <residentAnims/>
  <InitDatas>
    <Item>
      <modelName>caracara2tow</modelName>
      <txdName>caracara2</txdName>
      <handlingId>CARACARA2TOW</handlingId>
      <gameName>caracara2tow</gameName>
      <vehicleMakeName>VAPID</vehicleMakeName>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected/>
      <ptfxAssetName>null</ptfxAssetName>
      <audioNameHash/>
      <layout>LAYOUT_RANGER_CARACARA2</layout>
      <coverBoundOffsets>CARACARA2_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout/>
      <cameraName>DEFAULT_FOLLOW_VEHICLE_CAMERA</cameraName>
      <aimCameraName>MID_BOX_VEHICLE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>VEHICLE_BONNET_CAMERA_STANDARD_LONG_EXTRA_LOW</bonnetCameraName>
      <povCameraName>DEFAULT_POV_CAMERA_LOOKAROUND_MID</povCameraName>
      <FirstPersonDriveByIKOffset x="0.060000" y="-0.060000" z="-0.050000"/>
      <FirstPersonDriveByUnarmedIKOffset x="0.000000" y="0.000000" z="-0.020000"/>
      <FirstPersonProjectileDriveByIKOffset x="0.025000" y="-0.130000" z="-0.060000"/>
      <FirstPersonProjectileDriveByPassengerIKOffset x="-0.025000" y="-0.130000" z="-0.060000"/>
      <FirstPersonProjectileDriveByRearLeftIKOffset x="0.000000" y="-0.050000" z="-0.050000"/>
      <FirstPersonProjectileDriveByRearRightIKOffset x="0.000000" y="-0.050000" z="-0.050000"/>
      <FirstPersonDriveByLeftPassengerIKOffset x="0.070000" y="-0.050000" z="-0.060000"/>
      <FirstPersonDriveByRightPassengerIKOffset x="-0.060000" y="-0.060000" z="-0.050000"/>
      <FirstPersonDriveByRightRearPassengerIKOffset x="-0.070000" y="-0.050000" z="-0.060000"/>
      <FirstPersonDriveByLeftPassengerUnarmedIKOffset x="0.020000" y="-0.080000" z="-0.020000"/>
      <FirstPersonDriveByRightPassengerUnarmedIKOffset x="-0.020000" y="-0.040000" z="-0.030000"/>
      <FirstPersonMobilePhoneOffset x="0.130000" y="0.158000" z="0.535000"/>
      <FirstPersonPassengerMobilePhoneOffset x="0.230000" y="0.163000" z="0.475000"/>
      <FirstPersonMobilePhoneSeatIKOffset>
        <Item>
          <Offset x="0.230000" y="0.353000" z="0.440000"/>
          <SeatIndex value="2"/>
        </Item>
        <Item>
          <Offset x="0.230000" y="0.353000" z="0.440000"/>
          <SeatIndex value="3"/>
        </Item>
      </FirstPersonMobilePhoneSeatIKOffset>
      <PovCameraOffset x="0.000000" y="-0.195000" z="0.650000"/>
      <PovCameraVerticalAdjustmentForRollCage value="0.000000"/>
      <PovPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000"/>
      <PovRearPassengerCameraOffset x="0.000000" y="0.195000" z="0.030000"/>
      <vfxInfoName>VFXVEHICLEINFO_CAR_OFFROAD</vfxInfoName>
      <shouldUseCinematicViewMode value="true"/>
      <shouldCameraTransitionOnClimbUpDown value="false"/>
      <shouldCameraIgnoreExiting value="false"/>
      <AllowPretendOccupants value="true"/>
      <AllowJoyriding value="true"/>
      <AllowSundayDriving value="true"/>
      <AllowBodyColorMapping value="true"/>
      <wheelScale value="0.263500"/>
      <wheelScaleRear value="0.263500"/>
      <dirtLevelMin value="0.000000"/>
      <dirtLevelMax value="0.850000"/>
      <envEffScaleMin value="0.000000"/>
      <envEffScaleMax value="1.000000"/>
      <envEffScaleMin2 value="0.000000"/>
      <envEffScaleMax2 value="1.000000"/>
      <damageMapScale value="0.400000"/>
      <damageOffsetScale value="0.400000"/>
      <diffuseTint value="0x00FFFFFF"/>
      <steerWheelMult value="1.000000"/>
      <HDTextureDist value="5.000000"/>
      <lodDistances content="float_array">
        20.000000
        35.000000
        90.000000
        180.000000
        500.000000
        500.000000
      </lodDistances>
      <minSeatHeight value="1.006"/>
      <identicalModelSpawnDistance value="20"/>
      <maxNumOfSameColor value="10"/>
      <defaultBodyHealth value="1000.000000"/>
      <pretendOccupantsScale value="1.000000"/>
      <visibleSpawnDistScale value="1.000000"/>
      <trackerPathWidth value="2.000000"/>
      <weaponForceMult value="1.000000"/>
      <frequency value="30"/>
      <swankness>SWANKNESS_1</swankness>
      <maxNum value="5"/>
      <flags>FLAG_AVERAGE_CAR FLAG_POOR_CAR FLAG_IS_OFFROAD_VEHICLE FLAG_HEADLIGHTS_USE_ACTUAL_BONE_POS FLAG_IS_BULKY FLAG_HAS_INTERIOR_EXTRAS</flags>
      <type>VEHICLE_TYPE_CAR</type>
      <plateType>VPT_BACK_PLATES</plateType>
      <dashboardType>VDT_CAVALCADE</dashboardType>
      <vehicleClass>VC_OFF_ROAD</vehicleClass>
      <wheelType>VWT_SUV</wheelType>
      <trailers/>
      <additionalTrailers/>
      <drivers/>
      <extraIncludes/>
      <doorsWithCollisionWhenClosed>
        <Item>VEH_EXT_BOOT</Item>
      </doorsWithCollisionWhenClosed>
      <driveableDoors/>
      <bumpersNeedToCollideWithMap value="false"/>
      <needsRopeTexture value="false"/>
      <requiredExtras>EXTRA_1</requiredExtras>
      <rewards/>
      <cinematicPartCamera>
        <Item>WHEEL_WIDE_REAR_RIGHT_CAMERA</Item>
        <Item>WHEEL_WIDE_REAR_LEFT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet>Truck</NmBraceOverrideSet>
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000"/>
      <buoyancySphereSizeScale value="1.000000"/>
      <pOverrideRagdollThreshold type="NULL"/>
      <firstPersonDrivebyData>
        <Item>RANGER_CARACARA2_FRONT_LEFT</Item>
        <Item>RANGER_CARACARA2_FRONT_RIGHT</Item>
        <Item>RANGER_CARACARA2_REAR_LEFT</Item>
        <Item>RANGER_CARACARA2_REAR_RIGHT</Item>
      </firstPersonDrivebyData>
    </Item>
    <Item>
      <modelName>towflat</modelName>
      <txdName>towflat</txdName>
      <handlingId>towflat</handlingId>
      <gameName>FLATBED</gameName>
      <vehicleMakeName>MTL</vehicleMakeName>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected/>
      <ptfxAssetName>null</ptfxAssetName>
      <audioNameHash>flatbed</audioNameHash>
      <layout>LAYOUT_TRUCK</layout>
      <coverBoundOffsets>FLATBED_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_TRUCK</explosionInfo>
      <scenarioLayout/>
      <cameraName>DEFAULT_FOLLOW_VEHICLE_CAMERA</cameraName>
      <aimCameraName>MID_BOX_VEHICLE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>VEHICLE_BONNET_CAMERA_MID</bonnetCameraName>
      <povCameraName>DEFAULT_POV_CAMERA</povCameraName>
      <FirstPersonDriveByIKOffset x="-0.055000" y="-0.050000" z="-0.020000"/>
      <FirstPersonDriveByUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonProjectileDriveByIKOffset x="0.063000" y="-0.058000" z="0.000000"/>
      <FirstPersonProjectileDriveByPassengerIKOffset x="-0.055000" y="-0.083000" z="0.033000"/>
      <FirstPersonDriveByLeftPassengerIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonDriveByRightPassengerIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonDriveByLeftPassengerUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonDriveByRightPassengerUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonMobilePhoneOffset x="0.155000" y="0.370000" z="0.508000"/>
      <FirstPersonPassengerMobilePhoneOffset x="0.136000" y="0.223000" z="0.425000"/>
      <PovCameraOffset x="0.000000" y="-0.081000" z="0.655000"/>
      <PovCameraVerticalAdjustmentForRollCage value="0.000000"/>
      <PovPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000"/>
      <vfxInfoName>VFXVEHICLEINFO_TRUCK_HIDDEN_EXHAUST</vfxInfoName>
      <shouldUseCinematicViewMode value="true"/>
      <shouldCameraTransitionOnClimbUpDown value="false"/>
      <shouldCameraIgnoreExiting value="false"/>
      <AllowPretendOccupants value="true"/>
      <AllowJoyriding value="false"/>
      <AllowSundayDriving value="false"/>
      <AllowBodyColorMapping value="true"/>
      <wheelScale value="0.354000"/>
      <wheelScaleRear value="0.320000"/>
      <dirtLevelMin value="0.300000"/>
      <dirtLevelMax value="1.000000"/>
      <envEffScaleMin value="0.000000"/>
      <envEffScaleMax value="1.000000"/>
      <envEffScaleMin2 value="0.000000"/>
      <envEffScaleMax2 value="1.000000"/>
      <damageMapScale value="0.600000"/>
      <damageOffsetScale value="1.000000"/>
      <diffuseTint value="0x00FFFFFF"/>
      <steerWheelMult value="1.000000"/>
      <HDTextureDist value="5.000000"/>
      <lodDistances content="float_array">
        20.000000   
        90.000000   
        130.000000  
        260.000000  
        750.000000  
        750.000000
      </lodDistances>
      <minSeatHeight value="1.213"/>
      <identicalModelSpawnDistance value="20"/>
      <maxNumOfSameColor value="10"/>
      <defaultBodyHealth value="1000.000000"/>
      <pretendOccupantsScale value="1.000000"/>
      <visibleSpawnDistScale value="1.000000"/>
      <trackerPathWidth value="2.000000"/>
      <weaponForceMult value="1.000000"/>
      <frequency value="10"/>
      <swankness>SWANKNESS_1</swankness>
      <maxNum value="999"/>
      <flags>FLAG_BIG FLAG_CAN_HONK_WHEN_FLEEING FLAG_AVOID_TURNS FLAG_PEDS_CAN_STAND_ON_TOP FLAG_USE_FAT_INTERIOR_LIGHT FLAG_IS_BULKY</flags>
      <type>VEHICLE_TYPE_CAR</type>
      <plateType>VPT_FRONT_PLATES</plateType>
      <dashboardType>VDT_TRUCK</dashboardType>
      <vehicleClass>VC_INDUSTRIAL</vehicleClass>
      <wheelType>VWT_SPORT</wheelType>
      <trailers/>
      <additionalTrailers/>
      <drivers>
        <Item>
          <driverName>S_M_M_Trucker_01</driverName>
          <npcName/>
        </Item>
      </drivers>
      <extraIncludes/>
      <doorsWithCollisionWhenClosed>
        <Item>VEH_EXT_BONNET</Item>
      </doorsWithCollisionWhenClosed>
      <driveableDoors/>
      <bumpersNeedToCollideWithMap value="true"/>
      <needsRopeTexture value="false"/>
      <requiredExtras/>
      <rewards/>
      <cinematicPartCamera>
        <Item>WHEEL_WIDE_REAR_RIGHT_CAMERA</Item>
        <Item>WHEEL_WIDE_REAR_LEFT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet>Truck</NmBraceOverrideSet>
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000"/>
      <buoyancySphereSizeScale value="1.000000"/>
      <pOverrideRagdollThreshold type="NULL"/>
      <firstPersonDrivebyData>
        <Item>TRUCK_PACKER_FRONT_LEFT</Item>
        <Item>TRUCK_PACKER_FRONT_RIGHT</Item>
      </firstPersonDrivebyData>
    </Item>
    <Item>
      <modelName>chimerac</modelName>
      <txdName>chimerac</txdName>
      <handlingId>chimerac</handlingId>
      <gameName>chimerac</gameName>
      <vehicleMakeName>WESTERN</vehicleMakeName>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected/>
      <ptfxAssetName>null</ptfxAssetName>
      <audioNameHash>CLIFFHANGER</audioNameHash>
      <layout>LAYOUT_BIKE_FREEWAY</layout>
      <coverBoundOffsets>NIGHTBLADE_COVER_OFFSET_INFO</coverBoundOffsets>
      <POVTuningInfo>DAEMON_POV_TUNING</POVTuningInfo>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout/>
      <cameraName>FOLLOW_UPRIGHT_BIKE_CAMERA</cameraName>
      <aimCameraName>BIKE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>BIKE_ZOMBIE_POV_CAMERA</bonnetCameraName>
      <povCameraName>BIKE_ZOMBIE_POV_CAMERA</povCameraName>
      <FirstPersonDriveByIKOffset x="0.000000" y="-0.060000" z="0.000000"/>
      <FirstPersonDriveByUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonProjectileDriveByIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonProjectileDriveByPassengerIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonProjectileDriveByRearLeftIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonProjectileDriveByRearRightIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonDriveByLeftPassengerIKOffset x="0.000000" y="-0.060000" z="-0.030000"/>
      <FirstPersonDriveByRightPassengerIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonDriveByRightRearPassengerIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonDriveByLeftPassengerUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonDriveByRightPassengerUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonMobilePhoneOffset x="0.155000" y="0.315000" z="0.520000"/>
      <FirstPersonPassengerMobilePhoneOffset x="0.136000" y="0.223000" z="0.425000"/>
      <PovCameraOffset x="0.000000" y="-0.040000" z="0.000000"/>
      <PovCameraVerticalAdjustmentForRollCage value="0.000000"/>
      <PovPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000"/>
      <PovRearPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000"/>
      <vfxInfoName>VFXVEHICLEINFO_MOTORBIKE_GENERIC</vfxInfoName>
      <shouldUseCinematicViewMode value="true"/>
      <shouldCameraTransitionOnClimbUpDown value="false"/>
      <shouldCameraIgnoreExiting value="false"/>
      <AllowPretendOccupants value="false"/>
      <AllowJoyriding value="true"/>
      <AllowSundayDriving value="true"/>
      <AllowBodyColorMapping value="true"/>
      <wheelScale value="0.216400"/>
      <wheelScaleRear value="0.216000"/>
      <dirtLevelMin value="0.000000"/>
      <dirtLevelMax value="0.550000"/>
      <envEffScaleMin value="0.000000"/>
      <envEffScaleMax value="1.000000"/>
      <envEffScaleMin2 value="0.000000"/>
      <envEffScaleMax2 value="1.000000"/>
      <damageMapScale value="0.500000"/>
      <damageOffsetScale value="0.100000"/>
      <diffuseTint value="0x00FFFFFF"/>
      <steerWheelMult value="0.400000"/>
      <HDTextureDist value="5.000000"/>
      <lodDistances content="float_array">
        15.000000
        30.000000
        50.000000
        100.000000
        500.000000
        500.000000
      </lodDistances>
      <minSeatHeight value="10"/>
      <identicalModelSpawnDistance value="20"/>
      <maxNumOfSameColor value="10"/>
      <defaultBodyHealth value="1000.000000"/>
      <pretendOccupantsScale value="1.000000"/>
      <visibleSpawnDistScale value="1.000000"/>
      <trackerPathWidth value="2.000000"/>
      <weaponForceMult value="1.000000"/>
      <frequency value="20"/>
      <swankness>SWANKNESS_3</swankness>
      <maxNum value="4"/>
      <flags>FLAG_HAS_LIVERY FLAG_NO_BOOT FLAG_IGNORE_ON_SIDE_CHECK FLAG_AVERAGE_CAR FLAG_HEADLIGHTS_USE_ACTUAL_BONE_POS FLAG_CAN_HAVE_NEONS</flags>
      <type>VEHICLE_TYPE_BIKE</type>
      <plateType>VPT_NONE</plateType>
      <dashboardType>VDT_BANSHEE</dashboardType>
      <vehicleClass>VC_MOTORCYCLE</vehicleClass>
      <wheelType>VWT_BIKE</wheelType>
      <trailers/>
      <additionalTrailers/>
      <drivers/>
      <extraIncludes/>
      <extraIncludes/>
      <doorsWithCollisionWhenClosed/>
      <driveableDoors/>
      <bumpersNeedToCollideWithMap value="false"/>
      <needsRopeTexture value="false"/>
      <rewards/>
      <cinematicPartCamera>
        <Item>WHEEL_REAR_LEFT_CAMERA</Item>
        <Item>WHEEL_REAR_RIGHT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet>Bike</NmBraceOverrideSet>
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000"/>
      <buoyancySphereSizeScale value="1.000000"/>
      <pOverrideRagdollThreshold type="NULL"/>
      <firstPersonDrivebyData>
        <Item>BIKE_ZOMBIE_FRONT</Item>
      </firstPersonDrivebyData>
    </Item>
    <Item>
      <modelName>polremustwo</modelName>
      <txdName>polremustwo</txdName>
      <handlingId>POLREMUSTWO</handlingId>
      <gameName>POLREMUSTWO</gameName>
      <vehicleMakeName>ANNIS</vehicleMakeName>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected/>
      <ptfxAssetName>null</ptfxAssetName>
      <audioNameHash>remustwo</audioNameHash>
      <layout>LAYOUT_LOW</layout>
      <coverBoundOffsets>REMUS_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout/>
      <cameraName>FOLLOW_CHEETAH_CAMERA</cameraName>
      <aimCameraName>DEFAULT_THIRD_PERSON_VEHICLE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>VEHICLE_BONNET_CAMERA_STANDARD</bonnetCameraName>
      <povCameraName>REDUCED_NEAR_CLIP_POV_CAMERA</povCameraName>
      <FirstPersonDriveByIKOffset x="0.03000" y="-0.090000" z="-0.020000"/>
      <FirstPersonDriveByUnarmedIKOffset x="0.000000" y="-0.060000" z="-0.020000"/>
      <FirstPersonProjectileDriveByIKOffset x="-0.025000" y="-0.130000" z="-0.040000"/>
      <FirstPersonProjectileDriveByPassengerIKOffset x="0.025000" y="-0.130000" z="-0.040000"/>
      <FirstPersonDriveByRightPassengerIKOffset x="0.020000" y="-0.080000" z="-0.020000"/>
      <FirstPersonDriveByRightPassengerUnarmedIKOffset x="0.000000" y="-0.060000" z="-0.020000"/>
      <FirstPersonMobilePhoneOffset x="0.206000" y="0.152000" z="0.514000"/>
      <FirstPersonPassengerMobilePhoneOffset x="0.216000" y="0.148000" z="0.380000"/>
      <PovCameraOffset x="0.000000" y="-0.300000" z="0.700000"/>
      <PovCameraVerticalAdjustmentForRollCage value="-0.030000"/>
      <vfxInfoName>VFXVEHICLEINFO_CAR_GENERIC</vfxInfoName>
      <shouldUseCinematicViewMode value="true"/>
      <shouldCameraTransitionOnClimbUpDown value="false"/>
      <shouldCameraIgnoreExiting value="false"/>
      <AllowPretendOccupants value="true"/>
      <AllowJoyriding value="true"/>
      <AllowSundayDriving value="true"/>
      <AllowBodyColorMapping value="true"/>
      <wheelScale value="0.220774"/>
      <wheelScaleRear value="0.220774"/>
      <dirtLevelMin value="0.000000"/>
      <dirtLevelMax value="0.550000"/>
      <envEffScaleMin value="0.000000"/>
      <envEffScaleMax value="1.000000"/>
      <envEffScaleMin2 value="0.000000"/>
      <envEffScaleMax2 value="1.000000"/>
      <damageMapScale value="0.500000"/>
      <damageOffsetScale value="0.500000"/>
      <diffuseTint value="0x00FFFFFF"/>
      <steerWheelMult value="1.000000"/>
      <HDTextureDist value="5.000000"/>
      <lodDistances content="float_array">
        15.000000
        30.000000
        70.000000	
        140.000000	
        300.000000	
        300.000000
      </lodDistances>
      <minSeatHeight value="0.871"/>
      <identicalModelSpawnDistance value="20"/>
      <maxNumOfSameColor value="1"/>
      <defaultBodyHealth value="1000.000000"/>
      <pretendOccupantsScale value="1.000000"/>
      <visibleSpawnDistScale value="1.000000"/>
      <trackerPathWidth value="2.000000"/>
      <weaponForceMult value="1.000000"/>
      <frequency value="10"/>
      <swankness>SWANKNESS_3</swankness>
      <maxNum value="10"/>
      <flags>FLAG_SPORTS FLAG_LAW_ENFORCEMENT FLAG_EMERGENCY_SERVICE FLAG_DONT_SPAWN_IN_CARGEN FLAG_NO_BROKEN_DOWN_SCENARIO FLAG_REPORT_CRIME_IF_STANDING_ON FLAG_HAS_SUPERCHARGER</flags>
      <type>VEHICLE_TYPE_CAR</type>
      <plateType>VPT_FRONT_AND_BACK_PLATES</plateType>
      <dashboardType>VDT_RACE</dashboardType>
      <vehicleClass>VC_EMERGENCY</vehicleClass>
      <wheelType>VWT_SPORT</wheelType>
      <trailers/>
      <additionalTrailers/>
      <drivers/>
      <extraIncludes/>
      <doorsWithCollisionWhenClosed/>
      <driveableDoors/>
      <bumpersNeedToCollideWithMap value="false"/>
      <needsRopeTexture value="false"/>
      <requiredExtras/>
      <rewards/>
      <cinematicPartCamera>
        <Item>WHEEL_FRONT_RIGHT_CAMERA</Item>
        <Item>WHEEL_FRONT_LEFT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet/>
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000"/>
      <buoyancySphereSizeScale value="1.000000"/>
      <pOverrideRagdollThreshold type="NULL"/>
      <firstPersonDrivebyData>
        <Item>LOW_REMUS_FRONT_LEFT</Item>
        <Item>LOW_REMUS_FRONT_RIGHT</Item>
      </firstPersonDrivebyData>
    </Item>
    <Item>
      <modelName>saltflat</modelName>
      <txdName>saltflat</txdName>
      <handlingId>saltflat</handlingId>
      <gameName>saltflat</gameName>
      <vehicleMakeName>WESTERN</vehicleMakeName>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected/>
      <ptfxAssetName>null</ptfxAssetName>
      <audioNameHash>CLIFFHANGER</audioNameHash>
      <layout>LAYOUT_BIKE_DIRT</layout>
      <coverBoundOffsets>NIGHTBLADE_COVER_OFFSET_INFO</coverBoundOffsets>
      <POVTuningInfo>DAEMON_POV_TUNING</POVTuningInfo>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout/>
      <cameraName>FOLLOW_UPRIGHT_BIKE_CAMERA</cameraName>
      <aimCameraName>BIKE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>BIKE_ZOMBIE_POV_CAMERA</bonnetCameraName>
      <povCameraName>BIKE_ZOMBIE_POV_CAMERA</povCameraName>
      <FirstPersonDriveByIKOffset x="0.000000" y="-0.060000" z="0.000000"/>
      <FirstPersonDriveByUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonProjectileDriveByIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonProjectileDriveByPassengerIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonProjectileDriveByRearLeftIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonProjectileDriveByRearRightIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonDriveByLeftPassengerIKOffset x="0.000000" y="-0.060000" z="-0.030000"/>
      <FirstPersonDriveByRightPassengerIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonDriveByRightRearPassengerIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonDriveByLeftPassengerUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonDriveByRightPassengerUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonMobilePhoneOffset x="0.155000" y="0.315000" z="0.520000"/>
      <FirstPersonPassengerMobilePhoneOffset x="0.136000" y="0.223000" z="0.425000"/>
      <PovCameraOffset x="0.000000" y="-0.040000" z="0.000000"/>
      <PovCameraVerticalAdjustmentForRollCage value="0.000000"/>
      <PovPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000"/>
      <PovRearPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000"/>
      <vfxInfoName>VFXVEHICLEINFO_MOTORBIKE_GENERIC</vfxInfoName>
      <shouldUseCinematicViewMode value="true"/>
      <shouldCameraTransitionOnClimbUpDown value="false"/>
      <shouldCameraIgnoreExiting value="false"/>
      <AllowPretendOccupants value="false"/>
      <AllowJoyriding value="true"/>
      <AllowSundayDriving value="true"/>
      <AllowBodyColorMapping value="true"/>
      <wheelScale value="0.216400"/>
      <wheelScaleRear value="0.196000"/>
      <dirtLevelMin value="0.000000"/>
      <dirtLevelMax value="0.550000"/>
      <envEffScaleMin value="0.000000"/>
      <envEffScaleMax value="1.000000"/>
      <envEffScaleMin2 value="0.000000"/>
      <envEffScaleMax2 value="1.000000"/>
      <damageMapScale value="0.500000"/>
      <damageOffsetScale value="0.100000"/>
      <diffuseTint value="0x00FFFFFF"/>
      <steerWheelMult value="0.400000"/>
      <HDTextureDist value="5.000000"/>
      <lodDistances content="float_array">
        15.000000
        30.000000
        50.000000
        100.000000
        500.000000
        500.000000
      </lodDistances>
      <minSeatHeight value="10"/>
      <identicalModelSpawnDistance value="20"/>
      <maxNumOfSameColor value="10"/>
      <defaultBodyHealth value="1000.000000"/>
      <pretendOccupantsScale value="1.000000"/>
      <visibleSpawnDistScale value="1.000000"/>
      <trackerPathWidth value="2.000000"/>
      <weaponForceMult value="1.000000"/>
      <frequency value="20"/>
      <swankness>SWANKNESS_3</swankness>
      <maxNum value="4"/>
      <flags>FLAG_HAS_LIVERY FLAG_NO_BOOT FLAG_IGNORE_ON_SIDE_CHECK FLAG_AVERAGE_CAR FLAG_HEADLIGHTS_USE_ACTUAL_BONE_POS FLAG_CAN_HAVE_NEONS</flags>
      <type>VEHICLE_TYPE_BIKE</type>
      <plateType>VPT_NONE</plateType>
      <dashboardType>VDT_BANSHEE</dashboardType>
      <vehicleClass>VC_MOTORCYCLE</vehicleClass>
      <wheelType>VWT_BIKE</wheelType>
      <trailers/>
      <additionalTrailers/>
      <drivers/>
      <extraIncludes/>
      <extraIncludes/>
      <doorsWithCollisionWhenClosed/>
      <driveableDoors/>
      <bumpersNeedToCollideWithMap value="false"/>
      <needsRopeTexture value="false"/>
      <rewards/>
      <cinematicPartCamera>
        <Item>WHEEL_REAR_LEFT_CAMERA</Item>
        <Item>WHEEL_REAR_RIGHT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet>Bike</NmBraceOverrideSet>
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000"/>
      <buoyancySphereSizeScale value="1.000000"/>
      <pOverrideRagdollThreshold type="NULL"/>
      <firstPersonDrivebyData>
        <Item>BIKE_ZOMBIE_FRONT</Item>
      </firstPersonDrivebyData>
    </Item>
    <Item>
      <modelName>shinobio</modelName>
      <txdName>shinobio</txdName>
      <handlingId>SHINOBIO</handlingId>
      <gameName>SHINOBIO</gameName>
      <vehicleMakeName>NAGASAKI</vehicleMakeName>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected/>
      <ptfxAssetName>null</ptfxAssetName>
      <audioNameHash>shinobi</audioNameHash>
      <layout>LAYOUT_BIKE_SPORT</layout>
      <coverBoundOffsets>NIGHTBLADE_COVER_OFFSET_INFO</coverBoundOffsets>
      <POVTuningInfo>PCJ_POV_TUNING</POVTuningInfo>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout/>
      <cameraName>FOLLOW_BIKE_CAMERA</cameraName>
      <aimCameraName>BIKE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>BIKE_DEFILER_POV_CAMERA</bonnetCameraName>
      <povCameraName>BIKE_DEFILER_POV_CAMERA</povCameraName>
      <FirstPersonDriveByIKOffset x="0.000000" y="-0.060000" z="0.000000"/>
      <FirstPersonDriveByUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonProjectileDriveByIKOffset x="0.000000" y="-0.060000" z="0.000000"/>
      <FirstPersonMobilePhoneOffset x="0.155000" y="0.315000" z="0.520000"/>
      <FirstPersonPassengerMobilePhoneOffset x="0.136000" y="0.223000" z="0.425000"/>
      <PovCameraOffset x="0.000000" y="0.000000" z="-0.030000"/>
      <PovCameraVerticalAdjustmentForRollCage value="0.000000"/>
      <PovPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000"/>
      <PovRearPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000"/>
      <vfxInfoName>VFXVEHICLEINFO_MOTORBIKE_GENERIC</vfxInfoName>
      <shouldUseCinematicViewMode value="true"/>
      <shouldCameraTransitionOnClimbUpDown value="false"/>
      <shouldCameraIgnoreExiting value="false"/>
      <AllowPretendOccupants value="false"/>
      <AllowJoyriding value="true"/>
      <AllowSundayDriving value="true"/>
      <AllowBodyColorMapping value="true"/>
      <wheelScale value="0.217500"/>
      <wheelScaleRear value="0.217500"/>
      <dirtLevelMin value="0.300000"/>
      <dirtLevelMax value="1.000000"/>
      <envEffScaleMin value="0.000000"/>
      <envEffScaleMax value="0.000000"/>
      <envEffScaleMin2 value="0.000000"/>
      <envEffScaleMax2 value="0.000000"/>
      <damageMapScale value="0.500000"/>
      <damageOffsetScale value="0.100000"/>
      <diffuseTint value="0x00FFFFFF"/>
      <steerWheelMult value="0.500000"/>
      <HDTextureDist value="5.000000"/>
      <lodDistances content="float_array">
        10.000000
        25.000000
        50.000000
        100.000000
        500.000000
        500.000000
      </lodDistances>
      <minSeatHeight value="10"/>
      <identicalModelSpawnDistance value="80"/>
      <maxNumOfSameColor value="10"/>
      <defaultBodyHealth value="1000.000000"/>
      <pretendOccupantsScale value="1.000000"/>
      <visibleSpawnDistScale value="1.000000"/>
      <trackerPathWidth value="2.000000"/>
      <weaponForceMult value="1.000000"/>
      <frequency value="60"/>
      <swankness>SWANKNESS_0</swankness>
      <maxNum value="5"/>
      <flags>FLAG_NO_BOOT FLAG_IGNORE_ON_SIDE_CHECK FLAG_POOR_CAR FLAG_IS_OFFROAD_VEHICLE FLAG_HEADLIGHTS_USE_ACTUAL_BONE_POS</flags>
      <type>VEHICLE_TYPE_BIKE</type>
      <plateType>VPT_BACK_PLATES</plateType>
      <dashboardType>VDT_SPORTBK</dashboardType>
      <vehicleClass>VC_MOTORCYCLE</vehicleClass>
      <wheelType>VWT_BIKE</wheelType>
      <trailers/>
      <additionalTrailers/>
      <drivers/>
      <extraIncludes/>
      <doorsWithCollisionWhenClosed/>
      <driveableDoors/>
      <bumpersNeedToCollideWithMap value="false"/>
      <needsRopeTexture value="false"/>
      <requiredExtras/>
      <rewards/>
      <cinematicPartCamera>
        <Item>WHEEL_REAR_LEFT_CAMERA</Item>
        <Item>WHEEL_REAR_RIGHT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet>Bike</NmBraceOverrideSet>
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000"/>
      <buoyancySphereSizeScale value="1.000000"/>
      <pOverrideRagdollThreshold type="NULL"/>
      <firstPersonDrivebyData>
        <Item>BIKE_SHINOBI_FRONT</Item>
      </firstPersonDrivebyData>
      <numSeatsOverride value="1"/>
    </Item>
    <Item>
      <modelName>sombrero</modelName>
      <txdName>sombrero</txdName>
      <handlingId>sombrero</handlingId>
      <gameName>sombrero,</gameName>
      <vehicleMakeName>WESTERN</vehicleMakeName>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected/>
      <ptfxAssetName>null</ptfxAssetName>
      <audioNameHash>cliffhanger</audioNameHash>
      <layout>LAYOUT_BIKE_FREEWAY</layout>
      <coverBoundOffsets>NIGHTBLADE_COVER_OFFSET_INFO</coverBoundOffsets>
      <POVTuningInfo>DAEMON_POV_TUNING</POVTuningInfo>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout/>
      <cameraName>FOLLOW_UPRIGHT_BIKE_CAMERA</cameraName>
      <aimCameraName>BIKE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>BIKE_ZOMBIE_POV_CAMERA</bonnetCameraName>
      <povCameraName>BIKE_ZOMBIE_POV_CAMERA</povCameraName>
      <FirstPersonDriveByIKOffset x="0.000000" y="-0.060000" z="0.000000"/>
      <FirstPersonDriveByUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonProjectileDriveByIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonProjectileDriveByPassengerIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonProjectileDriveByRearLeftIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonProjectileDriveByRearRightIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonDriveByLeftPassengerIKOffset x="0.000000" y="-0.060000" z="-0.030000"/>
      <FirstPersonDriveByRightPassengerIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonDriveByRightRearPassengerIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonDriveByLeftPassengerUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonDriveByRightPassengerUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonMobilePhoneOffset x="0.155000" y="0.315000" z="0.520000"/>
      <FirstPersonPassengerMobilePhoneOffset x="0.136000" y="0.223000" z="0.425000"/>
      <PovCameraOffset x="0.000000" y="-0.040000" z="0.000000"/>
      <PovCameraVerticalAdjustmentForRollCage value="0.000000"/>
      <PovPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000"/>
      <PovRearPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000"/>
      <vfxInfoName>VFXVEHICLEINFO_MOTORBIKE_GENERIC</vfxInfoName>
      <shouldUseCinematicViewMode value="true"/>
      <shouldCameraTransitionOnClimbUpDown value="false"/>
      <shouldCameraIgnoreExiting value="false"/>
      <AllowPretendOccupants value="false"/>
      <AllowJoyriding value="true"/>
      <AllowSundayDriving value="true"/>
      <AllowBodyColorMapping value="true"/>
      <wheelScale value="0.216400"/>
      <wheelScaleRear value="0.196000"/>
      <dirtLevelMin value="0.000000"/>
      <dirtLevelMax value="0.550000"/>
      <envEffScaleMin value="0.000000"/>
      <envEffScaleMax value="1.000000"/>
      <envEffScaleMin2 value="0.000000"/>
      <envEffScaleMax2 value="1.000000"/>
      <damageMapScale value="0.500000"/>
      <damageOffsetScale value="0.100000"/>
      <diffuseTint value="0x00FFFFFF"/>
      <steerWheelMult value="0.400000"/>
      <HDTextureDist value="5.000000"/>
      <lodDistances content="float_array">
        15.000000
        30.000000
        50.000000
        100.000000
        500.000000
        500.000000
      </lodDistances>
      <minSeatHeight value="10"/>
      <identicalModelSpawnDistance value="20"/>
      <maxNumOfSameColor value="10"/>
      <defaultBodyHealth value="1000.000000"/>
      <pretendOccupantsScale value="1.000000"/>
      <visibleSpawnDistScale value="1.000000"/>
      <trackerPathWidth value="2.000000"/>
      <weaponForceMult value="1.000000"/>
      <frequency value="20"/>
      <swankness>SWANKNESS_3</swankness>
      <maxNum value="4"/>
      <flags>FLAG_HAS_LIVERY FLAG_NO_BOOT FLAG_IGNORE_ON_SIDE_CHECK FLAG_AVERAGE_CAR FLAG_HEADLIGHTS_USE_ACTUAL_BONE_POS FLAG_CAN_HAVE_NEONS</flags>
      <type>VEHICLE_TYPE_BIKE</type>
      <plateType>VPT_NONE</plateType>
      <dashboardType>VDT_BANSHEE</dashboardType>
      <vehicleClass>VC_MOTORCYCLE</vehicleClass>
      <wheelType>VWT_BIKE</wheelType>
      <trailers/>
      <additionalTrailers/>
      <drivers/>
      <extraIncludes/>
      <extraIncludes/>
      <doorsWithCollisionWhenClosed/>
      <driveableDoors/>
      <bumpersNeedToCollideWithMap value="false"/>
      <needsRopeTexture value="false"/>
      <rewards/>
      <cinematicPartCamera>
        <Item>WHEEL_REAR_LEFT_CAMERA</Item>
        <Item>WHEEL_REAR_RIGHT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet>Bike</NmBraceOverrideSet>
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000"/>
      <buoyancySphereSizeScale value="1.000000"/>
      <pOverrideRagdollThreshold type="NULL"/>
      <firstPersonDrivebyData>
        <Item>BIKE_ZOMBIE_FRONT</Item>
      </firstPersonDrivebyData>
    </Item>
    <Item>
      <modelName>taco2</modelName>
      <txdName>taco2</txdName>
      <handlingId>TACO</handlingId>
      <gameName>TACO</gameName>
      <vehicleMakeName/>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected/>
      <ptfxAssetName>null</ptfxAssetName>
      <audioNameHash>TACO</audioNameHash>
      <layout>LAYOUT_ONE_DOOR_VAN</layout>
      <coverBoundOffsets>TACO_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_TRUCK</explosionInfo>
      <scenarioLayout/>
      <cameraName>FOLLOW_TACO_CAMERA</cameraName>
      <aimCameraName>TACO_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>DEFAULT_VEHICLE_BONNET_CAMERA</bonnetCameraName>
      <povCameraName>DEFAULT_POV_CAMERA_NO_REVERSE</povCameraName>
      <FirstPersonDriveByIKOffset x="0.000000" y="-0.055000" z="-0.083000"/>
      <FirstPersonDriveByUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonProjectileDriveByIKOffset x="0.113000" y="0.000000" z="0.000000"/>
      <FirstPersonProjectileDriveByPassengerIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonProjectileDriveByRearLeftIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonProjectileDriveByRearRightIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonDriveByLeftPassengerIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonDriveByRightPassengerIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonDriveByRightRearPassengerIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonDriveByLeftPassengerUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonDriveByRightPassengerUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonMobilePhoneOffset x="0.155000" y="0.388000" z="0.578000"/>
      <FirstPersonPassengerMobilePhoneOffset x="0.136000" y="0.223000" z="0.425000"/>
      <PovCameraOffset x="0.000000" y="0.010000" z="0.680000"/>
      <PovCameraVerticalAdjustmentForRollCage value="0.000000"/>
      <PovPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000"/>
      <PovRearPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000"/>
      <vfxInfoName>VFXVEHICLEINFO_TRUCK_HIDDEN_EXHAUST</vfxInfoName>
      <shouldUseCinematicViewMode value="true"/>
      <shouldCameraTransitionOnClimbUpDown value="false"/>
      <shouldCameraIgnoreExiting value="false"/>
      <AllowPretendOccupants value="true"/>
      <AllowJoyriding value="false"/>
      <AllowSundayDriving value="false"/>
      <AllowBodyColorMapping value="true"/>
      <wheelScale value="0.231861"/>
      <wheelScaleRear value="0.231861"/>
      <dirtLevelMin value="0.300000"/>
      <dirtLevelMax value="0.850000"/>
      <envEffScaleMin value="0.000000"/>
      <envEffScaleMax value="1.000000"/>
      <envEffScaleMin2 value="0.000000"/>
      <envEffScaleMax2 value="1.000000"/>
      <damageMapScale value="0.600000"/>
      <damageOffsetScale value="1.000000"/>
      <diffuseTint value="0x00FFFFFF"/>
      <steerWheelMult value="1.000000"/>
      <HDTextureDist value="5.000000"/>
      <lodDistances content="float_array">
        20.000000
        35.000000
        90.000000
        180.000000
        650.000000
        650.000000
      </lodDistances>
      <minSeatHeight value="1.559"/>
      <identicalModelSpawnDistance value="20"/>
      <maxNumOfSameColor value="10"/>
      <defaultBodyHealth value="1000.000000"/>
      <pretendOccupantsScale value="1.000000"/>
      <visibleSpawnDistScale value="1.000000"/>
      <trackerPathWidth value="2.000000"/>
      <weaponForceMult value="1.000000"/>
      <frequency value="5"/>
      <swankness>SWANKNESS_1</swankness>
      <maxNum value="2"/>
      <flags>FLAG_BIG FLAG_AVOID_TURNS FLAG_HAS_LIVERY FLAG_DELIVERY FLAG_PEDS_CAN_STAND_ON_TOP FLAG_DONT_SPAWN_IN_CARGEN FLAG_IS_BULKY FLAG_BLOCK_FROM_ATTRACTOR_SCENARIO</flags>
      <type>VEHICLE_TYPE_CAR</type>
      <plateType>VPT_BACK_PLATES</plateType>
      <dashboardType>VDT_TRUCK</dashboardType>
      <vehicleClass>VC_VAN</vehicleClass>
      <wheelType>VWT_SPORT</wheelType>
      <trailers/>
      <additionalTrailers/>
      <drivers>
        <Item>
          <driverName>S_M_M_StrVend_01</driverName>
          <npcName/>
        </Item>
      </drivers>
      <extraIncludes/>
      <doorsWithCollisionWhenClosed/>
      <driveableDoors/>
      <bumpersNeedToCollideWithMap value="false"/>
      <needsRopeTexture value="false"/>
      <requiredExtras/>
      <rewards/>
      <cinematicPartCamera>
        <Item>WHEEL_WIDE_REAR_RIGHT_CAMERA</Item>
        <Item>WHEEL_WIDE_REAR_LEFT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet>Truck</NmBraceOverrideSet>
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000"/>
      <buoyancySphereSizeScale value="1.000000"/>
      <pOverrideRagdollThreshold type="NULL"/>
      <firstPersonDrivebyData>
        <Item>VAN_TACO_FRONT_LEFT</Item>
      </firstPersonDrivebyData>
    </Item>
    <Item>
      <modelName>mssdtr1</modelName>
      <txdName>tr3</txdName>
      <handlingId>TRAILER</handlingId>
      <gameName>TRAILER</gameName>
      <vehicleMakeName/>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected/>
      <ptfxAssetName>null</ptfxAssetName>
      <audioNameHash/>
      <layout>LAYOUT_TRUCK</layout>
      <coverBoundOffsets>TRAILERS_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout/>
      <cameraName>FOLLOW_ARTIC_CAMERA</cameraName>
      <aimCameraName>ARTIC_AIM_CAMERA</aimCameraName>
      <bonnetCameraName/>
      <povCameraName/>
      <FirstPersonDriveByIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonDriveByUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonProjectileDriveByIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonProjectileDriveByPassengerIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonDriveByLeftPassengerIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonDriveByRightPassengerIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonDriveByLeftPassengerUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonDriveByRightPassengerUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonMobilePhoneOffset x="0.155000" y="0.315000" z="0.520000"/>
      <PovPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000"/>
      <vfxInfoName>VFXVEHICLEINFO_TRAILER_GENERIC</vfxInfoName>
      <shouldUseCinematicViewMode value="true"/>
      <shouldCameraTransitionOnClimbUpDown value="false"/>
      <shouldCameraIgnoreExiting value="false"/>
      <AllowPretendOccupants value="true"/>
      <AllowJoyriding value="true"/>
      <AllowSundayDriving value="true"/>
      <AllowBodyColorMapping value="true"/>
      <wheelScale value="0.317100"/>
      <wheelScaleRear value="0.319700"/>
      <dirtLevelMin value="0.000000"/>
      <dirtLevelMax value="0.850000"/>
      <envEffScaleMin value="0.000000"/>
      <envEffScaleMax value="1.000000"/>
      <envEffScaleMin2 value="0.000000"/>
      <envEffScaleMax2 value="1.000000"/>
      <damageMapScale value="0.600000"/>
      <damageOffsetScale value="1.000000"/>
      <diffuseTint value="0x00FFFFFF"/>
      <steerWheelMult value="1.000000"/>
      <HDTextureDist value="5.000000"/>
      <lodDistances content="float_array">
        25.000000
        40.000000
        70.000000
        140.000000
        750.000000
        750.000000
      </lodDistances>
      <identicalModelSpawnDistance value="20"/>
      <maxNumOfSameColor value="10"/>
      <defaultBodyHealth value="1000.000000"/>
      <pretendOccupantsScale value="1.000000"/>
      <visibleSpawnDistScale value="1.000000"/>
      <trackerPathWidth value="2.000000"/>
      <weaponForceMult value="1.000000"/>
      <frequency value="10"/>
      <swankness>SWANKNESS_1</swankness>
      <maxNum value="999"/>
      <flags>FLAG_EXTRAS_REQUIRE FLAG_EXTRAS_STRONG FLAG_PEDS_CAN_STAND_ON_TOP FLAG_GEN_NAVMESH FLAG_DONT_SPAWN_AS_AMBIENT</flags>
      <type>VEHICLE_TYPE_TRAILER</type>
      <plateType>VPT_NONE</plateType>
      <vehicleClass>VC_UTILITY</vehicleClass>
      <wheelType>VWT_SPORT</wheelType>
      <trailers/>
      <additionalTrailers/>
      <drivers/>
      <extraIncludes/>
      <doorsWithCollisionWhenClosed/>
      <driveableDoors/>
      <bumpersNeedToCollideWithMap value="true"/>
      <needsRopeTexture value="false"/>
      <requiredExtras/>
      <rewards/>
      <cinematicPartCamera/>
      <NmBraceOverrideSet/>
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000"/>
      <buoyancySphereSizeScale value="1.000000"/>
      <pOverrideRagdollThreshold type="NULL"/>
    </Item>
    <Item>
      <modelName>ventoso</modelName>
      <txdName>ventoso</txdName>
      <handlingId>VENTOSO</handlingId>
      <gameName>VENTOSO</gameName>
      <vehicleMakeName>DINKA</vehicleMakeName>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected/>
      <ptfxAssetName>null</ptfxAssetName>
      <audioNameHash>faggio2</audioNameHash>
      <layout>LAYOUT_BIKE_SPORT</layout>
      <coverBoundOffsets>BIKE_COVER_OFFSET_INFO</coverBoundOffsets>
      <POVTuningInfo>DOUBLE_POV_TUNING</POVTuningInfo>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout/>
      <cameraName>FOLLOW_BIKE_CAMERA</cameraName>
      <aimCameraName>BIKE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>BIKE_DOUBLE_POV_CAMERA</bonnetCameraName>
      <povCameraName>BIKE_DOUBLE_POV_CAMERA</povCameraName>
      <FirstPersonDriveByIKOffset x="0.000000" y="-0.060000" z="0.000000"/>
      <FirstPersonDriveByUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonProjectileDriveByIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonProjectileDriveByPassengerIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonProjectileDriveByRearLeftIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonProjectileDriveByRearRightIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonDriveByLeftPassengerIKOffset x="0.000000" y="-0.060000" z="-0.030000"/>
      <FirstPersonDriveByRightPassengerIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonDriveByRightRearPassengerIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonDriveByLeftPassengerUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonDriveByRightPassengerUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonMobilePhoneOffset x="0.155000" y="0.315000" z="0.520000"/>
      <FirstPersonPassengerMobilePhoneOffset x="0.136000" y="0.223000" z="0.425000"/>
      <PovCameraOffset x="0.000000" y="0.000000" z="0.000000"/>
      <PovCameraVerticalAdjustmentForRollCage value="0.000000"/>
      <PovPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000"/>
      <PovRearPassengerCameraOffset x="0.000000" y="0.100000" z="0.210000"/>
      <vfxInfoName>VFXVEHICLEINFO_MOTORBIKE_GENERIC</vfxInfoName>
      <shouldUseCinematicViewMode value="true"/>
      <shouldCameraTransitionOnClimbUpDown value="false"/>
      <shouldCameraIgnoreExiting value="false"/>
      <AllowPretendOccupants value="false"/>
      <AllowJoyriding value="true"/>
      <AllowSundayDriving value="true"/>
      <AllowBodyColorMapping value="true"/>
      <wheelScale value="0.150000"/>
      <wheelScaleRear value="0.150000"/>
      <dirtLevelMin value="0.000000"/>
      <dirtLevelMax value="0.700000"/>
      <envEffScaleMin value="0.000000"/>
      <envEffScaleMax value="1.000000"/>
      <envEffScaleMin2 value="0.000000"/>
      <envEffScaleMax2 value="1.000000"/>
      <damageMapScale value="0.600000"/>
      <damageOffsetScale value="1.000000"/>
      <diffuseTint value="0xAA000000"/>
      <steerWheelMult value="0.600000"/>
      <HDTextureDist value="5.000000"/>
      <lodDistances content="float_array">
        500.000000
        500.000000
        500.000000
        500.000000
        500.000000
        500.000000
      </lodDistances>
      <minSeatHeight value="10"/>
      <identicalModelSpawnDistance value="80"/>
      <maxNumOfSameColor value="1"/>
      <defaultBodyHealth value="1000.000000"/>
      <pretendOccupantsScale value="1.000000"/>
      <visibleSpawnDistScale value="1.000000"/>
      <trackerPathWidth value="2.000000"/>
      <weaponForceMult value="1.000000"/>
      <frequency value="5"/>
      <swankness>SWANKNESS_4</swankness>
      <maxNum value="1"/>
      <flags>FLAG_NO_BOOT FLAG_IGNORE_ON_SIDE_CHECK FLAG_POOR_CAR FLAG_HEADLIGHTS_USE_ACTUAL_BONE_POS FLAG_INCREASE_PED_COMMENTS</flags>
      <type>VEHICLE_TYPE_BIKE</type>
      <plateType>VPT_BACK_PLATES</plateType>
      <dashboardType>VDT_RACE</dashboardType>
      <vehicleClass>VC_MOTORCYCLE</vehicleClass>
      <wheelType>VWT_BIKE</wheelType>
      <trailers/>
      <additionalTrailers/>
      <drivers/>
      <extraIncludes/>
      <doorsWithCollisionWhenClosed/>
      <driveableDoors/>
      <bumpersNeedToCollideWithMap value="false"/>
      <needsRopeTexture value="false"/>
      <requiredExtras/>
      <rewards/>
      <cinematicPartCamera>
        <Item>WHEEL_REAR_LEFT_CAMERA</Item>
        <Item>WHEEL_REAR_RIGHT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet>Bike</NmBraceOverrideSet>
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000"/>
      <buoyancySphereSizeScale value="1.000000"/>
      <pOverrideRagdollThreshold type="NULL"/>
      <firstPersonDrivebyData>
        <Item>BIKE_DOUBLE_FRONT</Item>
        <Item>BIKE_BATI_REAR</Item>
      </firstPersonDrivebyData>
    </Item>
    <Item>
      <modelName>foodcar</modelName>
      <txdName>foodcar</txdName>
      <handlingId>blista2</handlingId>
      <gameName>FOODCAR</gameName>
      <vehicleMakeName/>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected/>
      <ptfxAssetName>null</ptfxAssetName>
      <audioNameHash>BLISTA2</audioNameHash>
      <layout>LAYOUT_STD_FOODCAR</layout>
      <coverBoundOffsets>FOODCAR_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout/>
      <cameraName>DEFAULT_FOLLOW_VEHICLE_CAMERA</cameraName>
      <aimCameraName>DEFAULT_THIRD_PERSON_VEHICLE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>VEHICLE_BONNET_CAMERA_MID</bonnetCameraName>
      <povCameraName>DEFAULT_POV_CAMERA_LOOKAROUND</povCameraName>
      <FirstPersonDriveByIKOffset x="0.000000" y="-0.060000" z="-0.020000"/>
      <FirstPersonDriveByUnarmedIKOffset x="0.000000" y="-0.025000" z="0.000000"/>
      <FirstPersonProjectileDriveByIKOffset x="0.000000" y="-0.100000" z="0.030000"/>
      <FirstPersonProjectileDriveByPassengerIKOffset x="0.000000" y="-0.100000" z="0.030000"/>
      <FirstPersonProjectileDriveByRearLeftIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonProjectileDriveByRearRightIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonDriveByLeftPassengerIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonDriveByRightPassengerIKOffset x="0.000000" y="-0.060000" z="-0.020000"/>
      <FirstPersonDriveByRightRearPassengerIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonDriveByLeftPassengerUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonDriveByRightPassengerUnarmedIKOffset x="0.000000" y="-0.035000" z="0.000000"/>
      <FirstPersonMobilePhoneOffset x="0.100000" y="0.210000" z="0.503000"/>
      <FirstPersonPassengerMobilePhoneOffset x="0.135000" y="0.165000" z="0.425000"/>
      <PovCameraOffset x="0.000000" y="-0.175000" z="0.615000"/>
      <PovCameraVerticalAdjustmentForRollCage value="0.000000"/>
      <PovPassengerCameraOffset x="0.000000" y="0.000000" z="0.030000"/>
      <PovRearPassengerCameraOffset x="0.000000" y="0.000000" z="0.030000"/>
      <vfxInfoName>VFXVEHICLEINFO_CAR_GENERIC</vfxInfoName>
      <shouldUseCinematicViewMode value="true"/>
      <shouldCameraTransitionOnClimbUpDown value="false"/>
      <shouldCameraIgnoreExiting value="false"/>
      <AllowPretendOccupants value="true"/>
      <AllowJoyriding value="true"/>
      <AllowSundayDriving value="true"/>
      <AllowBodyColorMapping value="true"/>
      <wheelScale value="0.228400"/>
      <wheelScaleRear value="0.228400"/>
      <dirtLevelMin value="0.000000"/>
      <dirtLevelMax value="0.400000"/>
      <envEffScaleMin value="0.000000"/>
      <envEffScaleMax value="1.000000"/>
      <envEffScaleMin2 value="0.000000"/>
      <envEffScaleMax2 value="1.000000"/>
      <damageMapScale value="0.600000"/>
      <damageOffsetScale value="1.000000"/>
      <diffuseTint value="0x00FFFFFF"/>
      <steerWheelMult value="1.000000"/>
      <HDTextureDist value="5.000000"/>
      <lodDistances content="float_array">
        15.000000
        30.000000
        60.000000	
        120.000000	
        500.000000	
        500.000000
      </lodDistances>
      <identicalModelSpawnDistance value="20"/>
      <maxNumOfSameColor value="10"/>
      <defaultBodyHealth value="1000.000000"/>
      <pretendOccupantsScale value="1.000000"/>
      <visibleSpawnDistScale value="1.000000"/>
      <trackerPathWidth value="2.000000"/>
      <weaponForceMult value="1.000000"/>
      <frequency value="30"/>
      <swankness>SWANKNESS_1</swankness>
      <maxNum value="10"/>
      <flags>FLAG_CAN_HAVE_NEONS FLAG_EXTRAS_REQUIRE FLAG_HAS_LIVERY FLAG_POOR_CAR</flags>
      <type>VEHICLE_TYPE_CAR</type>
      <dashboardType>VDT_FUTO</dashboardType>
      <plateType>VPT_FRONT_AND_BACK_PLATES</plateType>
      <vehicleClass>VC_SPORT</vehicleClass>
      <wheelType>VWT_TUNER</wheelType>
      <trailers/>
      <additionalTrailers/>
      <drivers>
        <Item>
          <driverName>S_M_Y_Delivery_01</driverName>
          <npcName/>
        </Item>
      </drivers>
      <extraIncludes/>
      <doorsWithCollisionWhenClosed/>
      <driveableDoors/>
      <bumpersNeedToCollideWithMap value="false"/>
      <needsRopeTexture value="false"/>
      <requiredExtras/>
      <rewards/>
      <cinematicPartCamera>
        <Item>WHEEL_FRONT_RIGHT_CAMERA</Item>
        <Item>WHEEL_FRONT_LEFT_CAMERA</Item>
        <Item>WHEEL_REAR_RIGHT_CAMERA</Item>
        <Item>WHEEL_REAR_LEFT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet/>
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000"/>
      <buoyancySphereSizeScale value="1.000000"/>
      <pOverrideRagdollThreshold type="NULL"/>
      <firstPersonDrivebyData>
        <Item>STD_BLISTA2_FRONT_LEFT</Item>
        <Item>STD_BLISTA_FRONT_RIGHT</Item>
      </firstPersonDrivebyData>
    </Item>
    <Item>
      <modelName>foodcar2</modelName>
      <txdName>foodcar2</txdName>
      <handlingId>BLISTA</handlingId>
      <gameName>FOODCAR2</gameName>
      <vehicleMakeName/>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected/>
      <ptfxAssetName>null</ptfxAssetName>
      <audioNameHash>BLISTA</audioNameHash>
      <layout>LAYOUT_STD_LOWROOF</layout>
      <coverBoundOffsets>BLISTA_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout/>
      <cameraName>DEFAULT_FOLLOW_VEHICLE_CAMERA</cameraName>
      <aimCameraName>DEFAULT_THIRD_PERSON_VEHICLE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>VEHICLE_BONNET_CAMERA_LOW</bonnetCameraName>
      <povCameraName>DEFAULT_POV_CAMERA</povCameraName>
      <FirstPersonDriveByIKOffset x="0.000000" y="-0.060000" z="-0.020000"/>
      <FirstPersonDriveByUnarmedIKOffset x="0.000000" y="-0.025000" z="0.000000"/>
      <FirstPersonProjectileDriveByIKOffset x="0.000000" y="-0.100000" z="0.030000"/>
      <FirstPersonProjectileDriveByPassengerIKOffset x="0.000000" y="-0.100000" z="0.030000"/>
      <FirstPersonProjectileDriveByRearLeftIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonProjectileDriveByRearRightIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonDriveByLeftPassengerIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonDriveByRightPassengerIKOffset x="0.000000" y="-0.060000" z="-0.020000"/>
      <FirstPersonDriveByRightRearPassengerIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonDriveByLeftPassengerUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonDriveByRightPassengerUnarmedIKOffset x="0.000000" y="-0.035000" z="0.000000"/>
      <FirstPersonMobilePhoneOffset x="0.100000" y="0.210000" z="0.530000"/>
      <FirstPersonPassengerMobilePhoneOffset x="0.135000" y="0.165000" z="0.425000"/>
      <PovCameraOffset x="0.000000" y="-0.175000" z="0.645000"/>
      <PovCameraVerticalAdjustmentForRollCage value="0.000000"/>
      <PovPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000"/>
      <PovRearPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000"/>
      <vfxInfoName>VFXVEHICLEINFO_CAR_GENERIC</vfxInfoName>
      <shouldUseCinematicViewMode value="true"/>
      <shouldCameraTransitionOnClimbUpDown value="false"/>
      <shouldCameraIgnoreExiting value="false"/>
      <AllowPretendOccupants value="true"/>
      <AllowJoyriding value="true"/>
      <AllowSundayDriving value="true"/>
      <AllowBodyColorMapping value="true"/>
      <wheelScale value="0.265000"/>
      <wheelScaleRear value="0.265000"/>
      <dirtLevelMin value="0.000000"/>
      <dirtLevelMax value="0.700000"/>
      <envEffScaleMin value="0.000000"/>
      <envEffScaleMax value="1.000000"/>
      <envEffScaleMin2 value="0.000000"/>
      <envEffScaleMax2 value="1.000000"/>
      <damageMapScale value="0.600000"/>
      <damageOffsetScale value="1.000000"/>
      <diffuseTint value="0x00FFFFFF"/>
      <steerWheelMult value="1.000000"/>
      <HDTextureDist value="5.000000"/>
      <lodDistances content="float_array">
        15.000000
        30.000000
        65.000000	
        130.000000	
        500.000000	
        500.000000
      </lodDistances>
      <minSeatHeight value="0.881"/>
      <identicalModelSpawnDistance value="20"/>
      <maxNumOfSameColor value="10"/>
      <defaultBodyHealth value="1000.000000"/>
      <pretendOccupantsScale value="1.000000"/>
      <visibleSpawnDistScale value="1.000000"/>
      <trackerPathWidth value="2.000000"/>
      <weaponForceMult value="1.000000"/>
      <frequency value="50"/>
      <swankness>SWANKNESS_2</swankness>
      <maxNum value="20"/>
      <flags>FLAG_CAN_HAVE_NEONS FLAG_EXTRAS_REQUIRE FLAG_HAS_LIVERY FLAG_AVERAGE_CAR FLAG_HAS_INTERIOR_EXTRAS</flags>
      <type>VEHICLE_TYPE_CAR</type>
      <plateType>VPT_FRONT_AND_BACK_PLATES</plateType>
      <dashboardType>VDT_TAILGATER</dashboardType>
      <vehicleClass>VC_COMPACT</vehicleClass>
      <wheelType>VWT_SPORT</wheelType>
      <trailers/>
      <additionalTrailers/>
      <drivers>
        <Item>
          <driverName>S_M_Y_Delivery_01</driverName>
          <npcName/>
        </Item>
      </drivers>
      <extraIncludes/>
      <doorsWithCollisionWhenClosed/>
      <driveableDoors/>
      <bumpersNeedToCollideWithMap value="false"/>
      <needsRopeTexture value="false"/>
      <requiredExtras/>
      <rewards/>
      <cinematicPartCamera>
        <Item>WHEEL_FRONT_RIGHT_CAMERA</Item>
        <Item>WHEEL_FRONT_LEFT_CAMERA</Item>
        <Item>WHEEL_REAR_RIGHT_CAMERA</Item>
        <Item>WHEEL_REAR_LEFT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet/>
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000"/>
      <buoyancySphereSizeScale value="1.000000"/>
      <pOverrideRagdollThreshold type="NULL"/>
      <firstPersonDrivebyData>
        <Item>STD_BLISTA_FRONT_LEFT</Item>
        <Item>STD_BLISTA_FRONT_RIGHT</Item>
      </firstPersonDrivebyData>
      <numSeatsOverride value="2"/>
    </Item>
    <Item>
      <modelName>foodcar3</modelName>
      <txdName>foodcar3</txdName>
      <handlingId>DILETTANTE</handlingId>
      <gameName>FOODCAR3</gameName>
      <vehicleMakeName/>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected/>
      <ptfxAssetName>null</ptfxAssetName>
      <audioNameHash>DILETTANTE</audioNameHash>
      <layout>LAYOUT_STANDARD</layout>
      <coverBoundOffsets>DILETTANTE_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout/>
      <cameraName>DEFAULT_FOLLOW_VEHICLE_CAMERA</cameraName>
      <aimCameraName>DEFAULT_THIRD_PERSON_VEHICLE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>DEFAULT_VEHICLE_BONNET_CAMERA</bonnetCameraName>
      <povCameraName>DEFAULT_POV_CAMERA</povCameraName>
      <FirstPersonDriveByIKOffset x="0.000000" y="-0.040000" z="-0.020000"/>
      <FirstPersonDriveByUnarmedIKOffset x="0.000000" y="0.000000" z="-0.020000"/>
      <FirstPersonProjectileDriveByIKOffset x="0.055000" y="-0.080000" z="0.000000"/>
      <FirstPersonProjectileDriveByPassengerIKOffset x="-0.120000" y="-0.080000" z="0.000000"/>
      <FirstPersonProjectileDriveByRearLeftIKOffset x="0.000000" y="0.020000" z="0.000000"/>
      <FirstPersonProjectileDriveByRearRightIKOffset x="0.000000" y="0.020000" z="0.000000"/>
      <FirstPersonDriveByLeftPassengerIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonDriveByRightPassengerIKOffset x="0.000000" y="-0.040000" z="0.010000"/>
      <FirstPersonDriveByRightRearPassengerIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonDriveByLeftPassengerUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonDriveByRightPassengerUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonMobilePhoneOffset x="0.140000" y="0.285000" z="0.548000"/>
      <FirstPersonPassengerMobilePhoneOffset x="0.136000" y="0.223000" z="0.445000"/>
      <FirstPersonMobilePhoneSeatIKOffset>
        <Item>
          <Offset x="0.136000" y="0.186000" z="0.455000"/>
          <SeatIndex value="2"/>
        </Item>
        <Item>
          <Offset x="0.136000" y="0.186000" z="0.455000"/>
          <SeatIndex value="3"/>
        </Item>
      </FirstPersonMobilePhoneSeatIKOffset>
      <PovCameraOffset x="0.000000" y="-0.100000" z="0.670000"/>
      <PovCameraVerticalAdjustmentForRollCage value="0.000000"/>
      <PovPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000"/>
      <PovRearPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000"/>
      <vfxInfoName>VFXVEHICLEINFO_CAR_GENERIC</vfxInfoName>
      <shouldUseCinematicViewMode value="true"/>
      <shouldCameraTransitionOnClimbUpDown value="false"/>
      <shouldCameraIgnoreExiting value="false"/>
      <AllowPretendOccupants value="true"/>
      <AllowJoyriding value="true"/>
      <AllowSundayDriving value="true"/>
      <AllowBodyColorMapping value="true"/>
      <wheelScale value="0.203000"/>
      <wheelScaleRear value="0.203000"/>
      <dirtLevelMin value="0.000000"/>
      <dirtLevelMax value="0.850000"/>
      <envEffScaleMin value="0.000000"/>
      <envEffScaleMax value="1.000000"/>
      <envEffScaleMin2 value="0.000000"/>
      <envEffScaleMax2 value="1.000000"/>
      <damageMapScale value="1.000000"/>
      <damageOffsetScale value="0.550000"/>
      <diffuseTint value="0x00FFFFFF"/>
      <steerWheelMult value="0.700000"/>
      <HDTextureDist value="5.000000"/>
      <lodDistances content="float_array">
        15.000000
        30.000000	
        70.000000	
        140.000000	
        500.000000	
        500.000000
      </lodDistances>
      <minSeatHeight value="0.871"/>
      <identicalModelSpawnDistance value="20"/>
      <maxNumOfSameColor value="10"/>
      <defaultBodyHealth value="1000.000000"/>
      <pretendOccupantsScale value="1.000000"/>
      <visibleSpawnDistScale value="1.000000"/>
      <trackerPathWidth value="2.000000"/>
      <weaponForceMult value="1.000000"/>
      <frequency value="100"/>
      <swankness>SWANKNESS_1</swankness>
      <maxNum value="999"/>
      <flags>FLAG_AVERAGE_CAR FLAG_HAS_INTERIOR_EXTRAS FLAG_HAS_LIVERY FLAG_EXTRAS_REQUIRE</flags>
      <type>VEHICLE_TYPE_CAR</type>
      <plateType>VPT_FRONT_AND_BACK_PLATES</plateType>
      <dashboardType>VDT_GENTAXI</dashboardType>
      <vehicleClass>VC_COMPACT</vehicleClass>
      <wheelType>VWT_SPORT</wheelType>
      <trailers/>
      <additionalTrailers/>
      <drivers>
        <Item>
          <driverName>A_M_Y_Hipster_01</driverName>
          <npcName/>
        </Item>
        <Item>
          <driverName>A_F_Y_Hipster_04</driverName>
          <npcName/>
        </Item>
      </drivers>
      <extraIncludes/>
      <doorsWithCollisionWhenClosed/>
      <driveableDoors/>
      <bumpersNeedToCollideWithMap value="true"/>
      <needsRopeTexture value="false"/>
      <requiredExtras/>
      <rewards/>
      <cinematicPartCamera>
        <Item>WHEEL_FRONT_RIGHT_CAMERA</Item>
        <Item>WHEEL_FRONT_LEFT_CAMERA</Item>
        <Item>WHEEL_REAR_RIGHT_CAMERA</Item>
        <Item>WHEEL_REAR_LEFT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet/>
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000"/>
      <buoyancySphereSizeScale value="1.000000"/>
      <pOverrideRagdollThreshold type="NULL"/>
      <firstPersonDrivebyData>
        <Item>STD_DILETTANTE_FRONT_LEFT</Item>
        <Item>STD_DILETTANTE_FRONT_RIGHT</Item>
        <Item>STD_DILETTANTE_REAR_LEFT</Item>
        <Item>STD_DILETTANTE_REAR_RIGHT</Item>
      </firstPersonDrivebyData>
    </Item>
    <Item>
      <modelName>foodcar4</modelName>
      <txdName>foodcar4</txdName>
      <handlingId>PANTO</handlingId>
      <gameName>FOODCAR4</gameName>
      <vehicleMakeName/>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected/>
      <ptfxAssetName>null</ptfxAssetName>
      <audioNameHash>PANTO</audioNameHash>
      <layout>LAYOUT_STD_PANTO</layout>
      <coverBoundOffsets>PANTO_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout/>
      <cameraName>DEFAULT_FOLLOW_VEHICLE_CAMERA</cameraName>
      <aimCameraName>TRACTOR_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>VEHICLE_BONNET_CAMERA_MID</bonnetCameraName>
      <povCameraName>DEFAULT_POV_CAMERA</povCameraName>
      <FirstPersonDriveByIKOffset x="0.000000" y="-0.020000" z="-0.040000"/>
      <FirstPersonDriveByUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonProjectileDriveByIKOffset x="0.088000" y="-0.103000" z="-0.080000"/>
      <FirstPersonProjectileDriveByPassengerIKOffset x="-0.063000" y="-0.115000" z="-0.035000"/>
      <FirstPersonProjectileDriveByRearLeftIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonProjectileDriveByRearRightIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonDriveByLeftPassengerIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonDriveByRightPassengerIKOffset x="0.000000" y="-0.035000" z="-0.030000"/>
      <FirstPersonDriveByRightRearPassengerIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonDriveByLeftPassengerUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonDriveByRightPassengerUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonPassengerMobilePhoneOffset x="0.153000" y="0.228000" z="0.460000"/>
      <FirstPersonMobilePhoneOffset x="0.103000" y="0.270000" z="0.545000"/>
      <PovCameraOffset x="0.000000" y="-0.125000" z="0.685000"/>
      <PovCameraVerticalAdjustmentForRollCage value="0.000000"/>
      <PovPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000"/>
      <PovRearPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000"/>
      <vfxInfoName>VFXVEHICLEINFO_CAR_GENERIC</vfxInfoName>
      <shouldUseCinematicViewMode value="true"/>
      <shouldCameraTransitionOnClimbUpDown value="false"/>
      <shouldCameraIgnoreExiting value="false"/>
      <AllowPretendOccupants value="true"/>
      <AllowJoyriding value="true"/>
      <AllowSundayDriving value="true"/>
      <AllowBodyColorMapping value="true"/>
      <wheelScale value="0.191700"/>
      <wheelScaleRear value="0.191700"/>
      <dirtLevelMin value="0.000000"/>
      <dirtLevelMax value="0.800000"/>
      <envEffScaleMin value="0.000000"/>
      <envEffScaleMax value="1.000000"/>
      <envEffScaleMin2 value="0.000000"/>
      <envEffScaleMax2 value="1.000000"/>
      <damageMapScale value="0.300000"/>
      <damageOffsetScale value="0.300000"/>
      <diffuseTint value="0x00FFFFFF"/>
      <steerWheelMult value="1.000000"/>
      <HDTextureDist value="5.000000"/>
      <lodDistances content="float_array">
        15.000000
        30.000000
        60.000000	
        120.000000	
        500.000000	
        500.000000
      </lodDistances>
      <minSeatHeight value="0.884"/>
      <identicalModelSpawnDistance value="20"/>
      <maxNumOfSameColor value="10"/>
      <defaultBodyHealth value="1000.000000"/>
      <pretendOccupantsScale value="1.000000"/>
      <visibleSpawnDistScale value="1.000000"/>
      <trackerPathWidth value="2.000000"/>
      <weaponForceMult value="1.000000"/>
      <frequency value="30"/>
      <swankness>SWANKNESS_1</swankness>
      <maxNum value="10"/>
      <flags>FLAG_EXTRAS_REQUIRE FLAG_HAS_LIVERY FLAG_POOR_CAR</flags>
      <type>VEHICLE_TYPE_CAR</type>
      <plateType>VPT_FRONT_AND_BACK_PLATES</plateType>
      <dashboardType>VDT_CAVALCADE</dashboardType>
      <vehicleClass>VC_COMPACT</vehicleClass>
      <wheelType>VWT_TUNER</wheelType>
      <trailers/>
      <additionalTrailers/>
      <drivers>
        <Item>
          <driverName>S_M_Y_Delivery_01</driverName>
          <npcName/>
        </Item>
      </drivers>
      <extraIncludes/>
      <doorsWithCollisionWhenClosed/>
      <driveableDoors/>
      <bumpersNeedToCollideWithMap value="false"/>
      <needsRopeTexture value="false"/>
      <requiredExtras/>
      <rewards/>
      <cinematicPartCamera>
        <Item>WHEEL_FRONT_RIGHT_CAMERA</Item>
        <Item>WHEEL_FRONT_LEFT_CAMERA</Item>
        <Item>WHEEL_REAR_RIGHT_CAMERA</Item>
        <Item>WHEEL_REAR_LEFT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet/>
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000"/>
      <buoyancySphereSizeScale value="1.000000"/>
      <pOverrideRagdollThreshold type="NULL"/>
      <firstPersonDrivebyData>
        <Item>PANTO_FRONT_LEFT</Item>
        <Item>PANTO_FRONT_RIGHT</Item>
      </firstPersonDrivebyData>
    </Item>
    <Item>
      <modelName>foodcar5</modelName>
      <txdName>foodcar5</txdName>
      <handlingId>FOODCAR5</handlingId>
      <gameName>FOODCAR5</gameName>
      <vehicleMakeName/>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected/>
      <ptfxAssetName>null</ptfxAssetName>
      <audioNameHash>PREMIER</audioNameHash>
      <layout>LAYOUT_STANDARD</layout>
      <coverBoundOffsets>PRIMO_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout/>
      <cameraName>DEFAULT_FOLLOW_VEHICLE_CAMERA</cameraName>
      <aimCameraName>DEFAULT_THIRD_PERSON_VEHICLE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>VEHICLE_BONNET_CAMERA_NEAR_HIGH</bonnetCameraName>
      <povCameraName>DEFAULT_POV_CAMERA</povCameraName>
      <FirstPersonDriveByIKOffset x="0.000000" y="-0.060000" z="-0.030000"/>
      <FirstPersonDriveByUnarmedIKOffset x="0.000000" y="0.000000" z="0.-020000"/>
      <FirstPersonProjectileDriveByIKOffset x="0.000000" y="-0.055000" z="-0.045000"/>
      <FirstPersonProjectileDriveByPassengerIKOffset x="0.000000" y="-0.055000" z="-0.045000"/>
      <FirstPersonDriveByLeftPassengerIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonDriveByRightPassengerIKOffset x="0.000000" y="-0.060000" z="-0.030000"/>
      <FirstPersonDriveByLeftPassengerUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonDriveByRightPassengerUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonMobilePhoneOffset x="0.138000" y="0.228000" z="0.543000"/>
      <FirstPersonPassengerMobilePhoneOffset x="0.136000" y="0.223000" z="0.415000"/>
      <FirstPersonMobilePhoneSeatIKOffset>
        <Item>
          <Offset x="-0.136000" y="-0.156000" z="0.655000"/>
          <SeatIndex value="2"/>
        </Item>
        <Item>
          <Offset x="-0.136000" y="-0.156000" z="0.655000"/>
          <SeatIndex value="3"/>
        </Item>
      </FirstPersonMobilePhoneSeatIKOffset>
      <PovCameraOffset x="0.000000" y="-0.180000" z="0.639000"/>
      <PovCameraVerticalAdjustmentForRollCage value="0.000000"/>
      <PovPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000"/>
      <vfxInfoName>VFXVEHICLEINFO_CAR_GENERIC</vfxInfoName>
      <shouldUseCinematicViewMode value="true"/>
      <shouldCameraTransitionOnClimbUpDown value="false"/>
      <shouldCameraIgnoreExiting value="false"/>
      <AllowPretendOccupants value="true"/>
      <AllowJoyriding value="true"/>
      <AllowSundayDriving value="true"/>
      <AllowBodyColorMapping value="true"/>
      <wheelScale value="0.260800"/>
      <wheelScaleRear value="0.260800"/>
      <dirtLevelMin value="0.000000"/>
      <dirtLevelMax value="0.850000"/>
      <envEffScaleMin value="0.000000"/>
      <envEffScaleMax value="1.000000"/>
      <envEffScaleMin2 value="0.000000"/>
      <envEffScaleMax2 value="1.000000"/>
      <damageMapScale value="0.600000"/>
      <damageOffsetScale value="1.000000"/>
      <diffuseTint value="0x00FFFFFF"/>
      <steerWheelMult value="1.000000"/>
      <HDTextureDist value="5.000000"/>
      <lodDistances content="float_array">
        10.000000
        25.000000
        60.000000
        120.000000
        500.000000
        500.000000
      </lodDistances>
      <minSeatHeight value="0.795"/>
      <identicalModelSpawnDistance value="40"/>
      <maxNumOfSameColor value="1"/>
      <defaultBodyHealth value="1000.000000"/>
      <pretendOccupantsScale value="1.000000"/>
      <visibleSpawnDistScale value="1.000000"/>
      <trackerPathWidth value="2.000000"/>
      <weaponForceMult value="1.000000"/>
      <frequency value="50"/>
      <swankness>SWANKNESS_1</swankness>
      <maxNum value="5"/>
      <flags>FLAG_PARKING_SENSORS FLAG_EXTRAS_REQUIRE FLAG_HAS_INTERIOR_EXTRAS FLAG_HAS_LIVERY FLAG_AVERAGE_CAR</flags>
      <type>VEHICLE_TYPE_CAR</type>
      <plateType>VPT_FRONT_AND_BACK_PLATES</plateType>
      <dashboardType>VDT_TAILGATER</dashboardType>
      <vehicleClass>VC_SEDAN</vehicleClass>
      <wheelType>VWT_SPORT</wheelType>
      <trailers/>
      <additionalTrailers/>
      <drivers>
        <Item>
          <driverName>S_M_Y_Delivery_01</driverName>
          <npcName/>
        </Item>
      </drivers>
      <extraIncludes/>
      <doorsWithCollisionWhenClosed/>
      <driveableDoors/>
      <bumpersNeedToCollideWithMap value="true"/>
      <needsRopeTexture value="false"/>
      <requiredExtras/>
      <rewards/>
      <cinematicPartCamera>
        <Item>WHEEL_FRONT_RIGHT_CAMERA</Item>
        <Item>WHEEL_FRONT_LEFT_CAMERA</Item>
        <Item>WHEEL_REAR_RIGHT_CAMERA</Item>
        <Item>WHEEL_REAR_LEFT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet/>
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000"/>
      <buoyancySphereSizeScale value="1.000000"/>
      <pOverrideRagdollThreshold type="NULL"/>
      <firstPersonDrivebyData>
        <Item>STD_PRIMO_FRONT_LEFT</Item>
        <Item>STD_PRIMO_FRONT_RIGHT</Item>
      </firstPersonDrivebyData>
    </Item>
    <Item>
      <modelName>foodcar6</modelName>
      <txdName>foodcar6</txdName>
      <handlingId>FOODCAR6</handlingId>
      <gameName>FOODCAR6</gameName>
      <vehicleMakeName/>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected/>
      <ptfxAssetName>null</ptfxAssetName>
      <audioNameHash>REBEL</audioNameHash>
      <layout>LAYOUT_STD_HIGHWINDOW</layout>
      <coverBoundOffsets>REBEL_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout/>
      <cameraName>FOLLOW_JEEP_CAMERA</cameraName>
      <aimCameraName>JEEP_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>VEHICLE_BONNET_CAMERA_NEAR_LOW</bonnetCameraName>
      <povCameraName>DEFAULT_POV_CAMERA</povCameraName>
      <FirstPersonDriveByIKOffset x="0.000000" y="-0.060000" z="0.015000"/>
      <FirstPersonDriveByUnarmedIKOffset x="0.000000" y="-0.025000" z="0.000000"/>
      <FirstPersonProjectileDriveByIKOffset x="0.060000" y="-0.060000" z="0.000000"/>
      <FirstPersonProjectileDriveByPassengerIKOffset x="-0.060000" y="-0.060000" z="0.000000"/>
      <FirstPersonProjectileDriveByRearLeftIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonProjectileDriveByRearRightIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonDriveByLeftPassengerIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonDriveByRightPassengerIKOffset x="0.000000" y="-0.060000" z="0.015000"/>
      <FirstPersonDriveByRightRearPassengerIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonDriveByLeftPassengerUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonDriveByRightPassengerUnarmedIKOffset x="0.000000" y="-0.025000" z="0.000000"/>
      <FirstPersonMobilePhoneOffset x="0.173000" y="0.243000" z="0.503000"/>
      <FirstPersonPassengerMobilePhoneOffset x="0.135000" y="0.135000" z="0.455000"/>
      <PovCameraOffset x="0.000000" y="-0.175000" z="0.655000"/>
      <PovCameraVerticalAdjustmentForRollCage value="-0.045000"/>
      <PovPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000"/>
      <PovRearPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000"/>
      <vfxInfoName>VFXVEHICLEINFO_CAR_OFFROAD</vfxInfoName>
      <shouldUseCinematicViewMode value="true"/>
      <shouldCameraTransitionOnClimbUpDown value="false"/>
      <shouldCameraIgnoreExiting value="false"/>
      <AllowPretendOccupants value="true"/>
      <AllowJoyriding value="true"/>
      <AllowSundayDriving value="true"/>
      <AllowBodyColorMapping value="true"/>
      <wheelScale value="0.225000"/>
      <wheelScaleRear value="0.225000"/>
      <dirtLevelMin value="0.300000"/>
      <dirtLevelMax value="0.850000"/>
      <envEffScaleMin value="0.500000"/>
      <envEffScaleMax value="1.000000"/>
      <envEffScaleMin2 value="0.000000"/>
      <envEffScaleMax2 value="1.000000"/>
      <damageMapScale value="0.600000"/>
      <damageOffsetScale value="1.000000"/>
      <diffuseTint value="0x00FFFFFF"/>
      <steerWheelMult value="0.700000"/>
      <HDTextureDist value="5.000000"/>
      <lodDistances content="float_array">
        15.000000
        35.000000
        80.000000
        160.000000
        500.000000
        500.000000
      </lodDistances>
      <minSeatHeight value="0.87"/>
      <identicalModelSpawnDistance value="20"/>
      <maxNumOfSameColor value="10"/>
      <defaultBodyHealth value="1000.000000"/>
      <pretendOccupantsScale value="1.000000"/>
      <visibleSpawnDistScale value="1.000000"/>
      <trackerPathWidth value="2.000000"/>
      <weaponForceMult value="1.000000"/>
      <frequency value="10"/>
      <swankness>SWANKNESS_1</swankness>
      <maxNum value="2"/>
      <flags>FLAG_EXTRAS_REQUIRE FLAG_AVERAGE_CAR FLAG_POOR_CAR FLAG_HAS_LIVERY FLAG_IS_OFFROAD_VEHICLE FLAG_HAS_LIVERY FLAG_HEADLIGHTS_USE_ACTUAL_BONE_POS FLAG_IS_BULKY</flags>
      <type>VEHICLE_TYPE_CAR</type>
      <plateType>VPT_FRONT_AND_BACK_PLATES</plateType>
      <dashboardType>VDT_BOBCAT</dashboardType>
      <vehicleClass>VC_OFF_ROAD</vehicleClass>
      <wheelType>VWT_OFFROAD</wheelType>
      <trailers/>
      <additionalTrailers/>
      <drivers>
        <Item>
          <driverName>S_M_Y_Delivery_01</driverName>
          <npcName/>
        </Item>
      </drivers>
      <extraIncludes/>
      <doorsWithCollisionWhenClosed>
        <Item>VEH_EXT_BOOT</Item>
      </doorsWithCollisionWhenClosed>
      <driveableDoors/>
      <bumpersNeedToCollideWithMap value="false"/>
      <needsRopeTexture value="false"/>
      <requiredExtras/>
      <rewards/>
      <cinematicPartCamera>
        <Item>WHEEL_FRONT_RIGHT_CAMERA</Item>
        <Item>WHEEL_FRONT_LEFT_CAMERA</Item>
        <Item>WHEEL_REAR_RIGHT_CAMERA</Item>
        <Item>WHEEL_REAR_LEFT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet>Truck</NmBraceOverrideSet>
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000"/>
      <buoyancySphereSizeScale value="1.000000"/>
      <pOverrideRagdollThreshold type="NULL"/>
      <firstPersonDrivebyData>
        <Item>STD_DILETTANTE_FRONT_LEFT</Item>
        <Item>STD_DILETTANTE_FRONT_RIGHT</Item>
      </firstPersonDrivebyData>
    </Item>
    <Item>
      <modelName>foodcar7</modelName>
      <txdName>foodcar7</txdName>
      <handlingId>FUTO</handlingId>
      <gameName>FOODCAR7</gameName>
      <vehicleMakeName/>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected/>
      <ptfxAssetName>null</ptfxAssetName>
      <audioNameHash>FUTO</audioNameHash>
      <layout>LAYOUT_STANDARD</layout>
      <coverBoundOffsets>FUTO_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout/>
      <cameraName>DEFAULT_FOLLOW_VEHICLE_CAMERA</cameraName>
      <aimCameraName>DEFAULT_THIRD_PERSON_VEHICLE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>VEHICLE_BONNET_CAMERA_MID</bonnetCameraName>
      <povCameraName>DEFAULT_POV_CAMERA</povCameraName>
      <FirstPersonDriveByIKOffset x="0.000000" y="-0.060000" z="-0.020000"/>
      <FirstPersonDriveByUnarmedIKOffset x="0.000000" y="-0.025000" z="0.000000"/>
      <FirstPersonProjectileDriveByIKOffset x="0.000000" y="-0.080000" z="-0.020000"/>
      <FirstPersonProjectileDriveByPassengerIKOffset x="0.000000" y="-0.080000" z="-0.020000"/>
      <FirstPersonProjectileDriveByRearLeftIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonProjectileDriveByRearRightIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonDriveByLeftPassengerIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonDriveByRightPassengerIKOffset x="0.000000" y="-0.060000" z="-0.020000"/>
      <FirstPersonDriveByRightRearPassengerIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonDriveByLeftPassengerUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonDriveByRightPassengerUnarmedIKOffset x="0.000000" y="-0.035000" z="0.000000"/>
      <FirstPersonMobilePhoneOffset x="0.155000" y="0.235000" z="0.550000"/>
      <FirstPersonPassengerMobilePhoneOffset x="0.135000" y="0.155000" z="0.395000"/>
      <PovCameraOffset x="0.000000" y="-0.195000" z="0.610000"/>
      <PovCameraVerticalAdjustmentForRollCage value="0.000000"/>
      <PovPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000"/>
      <PovRearPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000"/>
      <vfxInfoName>VFXVEHICLEINFO_CAR_GENERIC</vfxInfoName>
      <shouldUseCinematicViewMode value="true"/>
      <shouldCameraTransitionOnClimbUpDown value="false"/>
      <shouldCameraIgnoreExiting value="false"/>
      <AllowPretendOccupants value="true"/>
      <AllowJoyriding value="true"/>
      <AllowSundayDriving value="true"/>
      <AllowBodyColorMapping value="true"/>
      <wheelScale value="0.200500"/>
      <wheelScaleRear value="0.200500"/>
      <dirtLevelMin value="0.000000"/>
      <dirtLevelMax value="0.850000"/>
      <envEffScaleMin value="0.000000"/>
      <envEffScaleMax value="1.000000"/>
      <envEffScaleMin2 value="0.000000"/>
      <envEffScaleMax2 value="1.000000"/>
      <damageMapScale value="0.600000"/>
      <damageOffsetScale value="1.000000"/>
      <diffuseTint value="0x00FFFFFF"/>
      <steerWheelMult value="1.000000"/>
      <HDTextureDist value="5.000000"/>
      <lodDistances content="float_array">
        15.000000
        30.000000
        60.000000	
        120.000000	
        500.000000	
        500.000000
      </lodDistances>
      <minSeatHeight value="0.791"/>
      <identicalModelSpawnDistance value="20"/>
      <maxNumOfSameColor value="10"/>
      <defaultBodyHealth value="1000.000000"/>
      <pretendOccupantsScale value="1.000000"/>
      <visibleSpawnDistScale value="1.000000"/>
      <trackerPathWidth value="2.000000"/>
      <weaponForceMult value="1.000000"/>
      <frequency value="30"/>
      <swankness>SWANKNESS_1</swankness>
      <maxNum value="10"/>
      <flags>FLAG_CAN_HAVE_NEONS FLAG_HAS_LIVERY FLAG_EXTRAS_STRONG FLAG_POOR_CAR FLAG_INCREASE_CAMBER_WITH_SUSPENSION_MOD</flags>
      <type>VEHICLE_TYPE_CAR</type>
      <dashboardType>VDT_FUTO</dashboardType>
      <plateType>VPT_FRONT_AND_BACK_PLATES</plateType>
      <vehicleClass>VC_SPORT</vehicleClass>
      <wheelType>VWT_TUNER</wheelType>
      <trailers/>
      <additionalTrailers/>
      <drivers>
        <Item>
          <driverName>A_M_Y_KTown_01</driverName>
          <npcName/>
        </Item>
        <Item>
          <driverName>A_M_Y_KTown_02</driverName>
          <npcName/>
        </Item>
      </drivers>
      <extraIncludes/>
      <doorsWithCollisionWhenClosed/>
      <driveableDoors/>
      <bumpersNeedToCollideWithMap value="false"/>
      <needsRopeTexture value="false"/>
      <requiredExtras/>
      <rewards/>
      <cinematicPartCamera>
        <Item>WHEEL_FRONT_RIGHT_CAMERA</Item>
        <Item>WHEEL_FRONT_LEFT_CAMERA</Item>
        <Item>WHEEL_REAR_RIGHT_CAMERA</Item>
        <Item>WHEEL_REAR_LEFT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet/>
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000"/>
      <buoyancySphereSizeScale value="1.000000"/>
      <pOverrideRagdollThreshold type="NULL"/>
      <firstPersonDrivebyData>
        <Item>STD_FUTO_FRONT_LEFT</Item>
        <Item>STD_FUTO_FRONT_RIGHT</Item>
      </firstPersonDrivebyData>
      <numSeatsOverride value="2"/>
    </Item>
    <Item>
      <modelName>foodbike</modelName>
      <txdName>foodbike</txdName>
      <handlingId>FAGGION</handlingId>
      <gameName>FOODBIKE</gameName>
      <vehicleMakeName/>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected/>
      <ptfxAssetName>null</ptfxAssetName>
      <audioNameHash>FAGGIO</audioNameHash>
      <layout>LAYOUT_BIKE_SCOOTER</layout>
      <coverBoundOffsets>BIKE_COVER_OFFSET_INFO</coverBoundOffsets>
      <POVTuningInfo>FAGGIO3_POV_TUNING</POVTuningInfo>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout/>
      <cameraName>FOLLOW_UPRIGHT_BIKE_CAMERA</cameraName>
      <aimCameraName>BIKE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>BIKE_FAGGIO_POV_CAMERA</bonnetCameraName>
      <povCameraName>BIKE_FAGGIO_POV_CAMERA</povCameraName>
      <FirstPersonDriveByIKOffset x="0.000000" y="-0.020000" z="-0.030000"/>
      <FirstPersonDriveByUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonProjectileDriveByIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonProjectileDriveByPassengerIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonProjectileDriveByRearLeftIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonProjectileDriveByRearRightIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonDriveByLeftPassengerIKOffset x="0.000000" y="-0.060000" z="-0.030000"/>
      <FirstPersonDriveByRightPassengerIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonDriveByRightRearPassengerIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonDriveByLeftPassengerUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonDriveByRightPassengerUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonVisorSwitchIKOffset x="-0.025000" y="0.045000" z="-0.0300000"/>
      <FirstPersonMobilePhoneOffset x="0.155000" y="0.315000" z="0.520000"/>
      <FirstPersonPassengerMobilePhoneOffset x="0.136000" y="0.223000" z="0.425000"/>
      <PovCameraOffset x="0.000000" y="0.000000" z="0.000000"/>
      <PovCameraVerticalAdjustmentForRollCage value="0.000000"/>
      <PovPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000"/>
      <PovRearPassengerCameraOffset x="0.000000" y="0.050000" z="0.400000"/>
      <vfxInfoName>VFXVEHICLEINFO_MOTORBIKE_FAGGIO</vfxInfoName>
      <shouldUseCinematicViewMode value="true"/>
      <shouldCameraTransitionOnClimbUpDown value="false"/>
      <shouldCameraIgnoreExiting value="false"/>
      <AllowPretendOccupants value="false"/>
      <AllowJoyriding value="true"/>
      <AllowSundayDriving value="true"/>
      <AllowBodyColorMapping value="true"/>
      <wheelScale value="0.149800"/>
      <wheelScaleRear value="0.149800"/>
      <dirtLevelMin value="0.000000"/>
      <dirtLevelMax value="0.550000"/>
      <envEffScaleMin value="0.000000"/>
      <envEffScaleMax value="1.000000"/>
      <envEffScaleMin2 value="0.000000"/>
      <envEffScaleMax2 value="1.000000"/>
      <damageMapScale value="0.600000"/>
      <damageOffsetScale value="1.000000"/>
      <diffuseTint value="0x00FFFFFF"/>
      <steerWheelMult value="0.600000"/>
      <firstPersonSteerWheelMult value="0.30000"/>
      <HDTextureDist value="5.000000"/>
      <lodDistances content="float_array">
        15.000000
        30.000000
        50.000000
        100.000000
        500.000000
        500.000000
      </lodDistances>
      <minSeatHeight value="10"/>
      <identicalModelSpawnDistance value="80"/>
      <maxNumOfSameColor value="10"/>
      <defaultBodyHealth value="1000.000000"/>
      <pretendOccupantsScale value="1.000000"/>
      <visibleSpawnDistScale value="1.000000"/>
      <trackerPathWidth value="2.000000"/>
      <weaponForceMult value="1.000000"/>
      <frequency value="30"/>
      <swankness>SWANKNESS_3</swankness>
      <maxNum value="5"/>
      <flags>FLAG_NO_BOOT FLAG_IGNORE_ON_SIDE_CHECK FLAG_RICH_CAR FLAG_HAS_LIVERY FLAG_AVERAGE_CAR FLAG_HEADLIGHTS_USE_ACTUAL_BONE_POS</flags>
      <type>VEHICLE_TYPE_BIKE</type>
      <plateType>VPT_BACK_PLATES</plateType>
      <dashboardType>VDT_ZTYPE</dashboardType>
      <vehicleClass>VC_MOTORCYCLE</vehicleClass>
      <wheelType>VWT_BIKE</wheelType>
      <trailers/>
      <additionalTrailers/>
      <drivers>
        <Item>
          <driverName>S_M_Y_Delivery_01</driverName>
          <npcName/>
        </Item>
      </drivers>
      <extraIncludes/>
      <doorsWithCollisionWhenClosed/>
      <driveableDoors/>
      <bumpersNeedToCollideWithMap value="false"/>
      <needsRopeTexture value="false"/>
      <requiredExtras/>
      <rewards/>
      <cinematicPartCamera>
        <Item>WHEEL_REAR_LEFT_CAMERA</Item>
        <Item>WHEEL_REAR_RIGHT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet>Bike</NmBraceOverrideSet>
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000"/>
      <buoyancySphereSizeScale value="1.000000"/>
      <pOverrideRagdollThreshold type="NULL"/>
      <firstPersonDrivebyData>
        <Item>BIKE_FAGGIO_FRONT</Item>
      </firstPersonDrivebyData>
    </Item>
    <Item>
      <modelName>foodbike2</modelName>
      <txdName>foodbike2</txdName>
      <handlingId>FAGGIO</handlingId>
      <gameName>FOODBIKE2</gameName>
      <vehicleMakeName/>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected/>
      <ptfxAssetName>null</ptfxAssetName>
      <audioNameHash>FAGGIO2</audioNameHash>
      <layout>LAYOUT_BIKE_SCOOTER</layout>
      <coverBoundOffsets>BIKE_COVER_OFFSET_INFO</coverBoundOffsets>
      <POVTuningInfo>FAGGIO_POV_TUNING</POVTuningInfo>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout/>
      <cameraName>FOLLOW_UPRIGHT_BIKE_CAMERA</cameraName>
      <aimCameraName>BIKE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>BIKE_FAGGIO_POV_CAMERA</bonnetCameraName>
      <povCameraName>BIKE_FAGGIO_POV_CAMERA</povCameraName>
      <FirstPersonDriveByIKOffset x="0.000000" y="-0.020000" z="-0.030000"/>
      <FirstPersonDriveByUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonProjectileDriveByIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonProjectileDriveByPassengerIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonProjectileDriveByRearLeftIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonProjectileDriveByRearRightIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonDriveByLeftPassengerIKOffset x="0.000000" y="-0.060000" z="-0.030000"/>
      <FirstPersonDriveByRightPassengerIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonDriveByRightRearPassengerIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonDriveByLeftPassengerUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonDriveByRightPassengerUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonVisorSwitchIKOffset x="-0.025000" y="0.045000" z="-0.0300000"/>
      <FirstPersonMobilePhoneOffset x="0.155000" y="0.315000" z="0.520000"/>
      <FirstPersonPassengerMobilePhoneOffset x="0.136000" y="0.223000" z="0.425000"/>
      <PovCameraOffset x="0.000000" y="0.000000" z="0.000000"/>
      <PovCameraVerticalAdjustmentForRollCage value="0.000000"/>
      <PovPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000"/>
      <PovRearPassengerCameraOffset x="0.000000" y="0.050000" z="0.400000"/>
      <vfxInfoName>VFXVEHICLEINFO_MOTORBIKE_FAGGIO</vfxInfoName>
      <shouldUseCinematicViewMode value="true"/>
      <shouldCameraTransitionOnClimbUpDown value="false"/>
      <shouldCameraIgnoreExiting value="false"/>
      <AllowPretendOccupants value="false"/>
      <AllowJoyriding value="true"/>
      <AllowSundayDriving value="true"/>
      <AllowBodyColorMapping value="true"/>
      <wheelScale value="0.151000"/>
      <wheelScaleRear value="0.151000"/>
      <dirtLevelMin value="0.000000"/>
      <dirtLevelMax value="0.550000"/>
      <envEffScaleMin value="0.000000"/>
      <envEffScaleMax value="1.000000"/>
      <envEffScaleMin2 value="0.000000"/>
      <envEffScaleMax2 value="1.000000"/>
      <damageMapScale value="0.600000"/>
      <damageOffsetScale value="1.000000"/>
      <diffuseTint value="0x00FFFFFF"/>
      <steerWheelMult value="0.600000"/>
      <firstPersonSteerWheelMult value="0.30000"/>
      <HDTextureDist value="5.000000"/>
      <lodDistances content="float_array">
        15.000000
        30.000000
        50.000000
        100.000000
        500.000000
        500.000000
      </lodDistances>
      <minSeatHeight value="10"/>
      <identicalModelSpawnDistance value="80"/>
      <maxNumOfSameColor value="10"/>
      <defaultBodyHealth value="1000.000000"/>
      <pretendOccupantsScale value="1.000000"/>
      <visibleSpawnDistScale value="1.000000"/>
      <trackerPathWidth value="2.000000"/>
      <weaponForceMult value="1.000000"/>
      <frequency value="30"/>
      <swankness>SWANKNESS_3</swankness>
      <maxNum value="5"/>
      <flags>FLAG_NO_BOOT FLAG_IGNORE_ON_SIDE_CHECK FLAG_RICH_CAR FLAG_HAS_LIVERY FLAG_AVERAGE_CAR FLAG_HEADLIGHTS_USE_ACTUAL_BONE_POS</flags>
      <type>VEHICLE_TYPE_BIKE</type>
      <plateType>VPT_BACK_PLATES</plateType>
      <dashboardType>VDT_ZTYPE</dashboardType>
      <vehicleClass>VC_MOTORCYCLE</vehicleClass>
      <wheelType>VWT_BIKE</wheelType>
      <trailers/>
      <additionalTrailers/>
      <drivers>
        <Item>
          <driverName>S_M_Y_Delivery_01</driverName>
          <npcName/>
        </Item>
      </drivers>
      <extraIncludes/>
      <doorsWithCollisionWhenClosed/>
      <driveableDoors/>
      <bumpersNeedToCollideWithMap value="false"/>
      <needsRopeTexture value="false"/>
      <requiredExtras/>
      <rewards/>
      <cinematicPartCamera>
        <Item>WHEEL_REAR_LEFT_CAMERA</Item>
        <Item>WHEEL_REAR_RIGHT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet>Bike</NmBraceOverrideSet>
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000"/>
      <buoyancySphereSizeScale value="1.000000"/>
      <pOverrideRagdollThreshold type="NULL"/>
      <firstPersonDrivebyData>
        <Item>BIKE_FAGGIO_FRONT</Item>
      </firstPersonDrivebyData>
    </Item>
    <Item>
      <modelName>foxutillitruck</modelName>
      <txdName>foxutillitruck</txdName>
      <handlingId>UTILTRUC</handlingId>
      <gameName>UTILTRUC</gameName>
      <vehicleMakeName/>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>va_foxutillitruck</animConvRoofDictName>
      <animConvRoofName>crane</animConvRoofName>
      <animConvRoofWindowsAffected/>
      <ptfxAssetName>null</ptfxAssetName>
      <audioNameHash>UTILLITRUCK</audioNameHash>
      <layout>LAYOUT_VAN_MULE</layout>
      <coverBoundOffsets>UTILITRUCK_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_TRUCK</explosionInfo>
      <scenarioLayout/>
      <cameraName>DEFAULT_FOLLOW_VEHICLE_CAMERA</cameraName>
      <aimCameraName>CHERRYPICKER_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>VEHICLE_BONNET_CAMERA_LOW</bonnetCameraName>
      <povCameraName>DEFAULT_POV_CAMERA_NO_REVERSE</povCameraName>
      <FirstPersonDriveByIKOffset x="0.000000" y="-0.035000" z="-0.035000"/>
      <FirstPersonDriveByUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonProjectileDriveByIKOffset x="0.053000" y="-0.033000" z="-0.048000"/>
      <FirstPersonProjectileDriveByPassengerIKOffset x="0.120000" y="-0.010000" z="-0.093000"/>
      <FirstPersonProjectileDriveByRearLeftIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonProjectileDriveByRearRightIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonDriveByLeftPassengerIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonDriveByRightPassengerIKOffset x="-0.050000" y="-0.005000" z="-0.063000"/>
      <FirstPersonDriveByRightRearPassengerIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonDriveByLeftPassengerUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonDriveByRightPassengerUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonMobilePhoneOffset x="0.155000" y="0.315000" z="0.503000"/>
      <FirstPersonPassengerMobilePhoneOffset x="0.136000" y="0.223000" z="0.425000"/>
      <PovCameraOffset x="0.000000" y="-0.150000" z="0.670000"/>
      <PovCameraVerticalAdjustmentForRollCage value="0.000000"/>
      <PovPassengerCameraOffset x="0.000000" y="0.050000" z="0.000000"/>
      <PovRearPassengerCameraOffset x="0.000000" y="0.050000" z="0.000000"/>
      <vfxInfoName>VFXVEHICLEINFO_TRUCK_HIDDEN_EXHAUST</vfxInfoName>
      <shouldUseCinematicViewMode value="true"/>
      <shouldCameraTransitionOnClimbUpDown value="false"/>
      <shouldCameraIgnoreExiting value="false"/>
      <AllowPretendOccupants value="true"/>
      <AllowJoyriding value="false"/>
      <AllowSundayDriving value="false"/>
      <AllowBodyColorMapping value="true"/>
      <wheelScale value="0.293500"/>
      <wheelScaleRear value="0.293500"/>
      <dirtLevelMin value="0.300000"/>
      <dirtLevelMax value="1.000000"/>
      <envEffScaleMin value="0.000000"/>
      <envEffScaleMax value="1.000000"/>
      <envEffScaleMin2 value="0.000000"/>
      <envEffScaleMax2 value="1.000000"/>
      <damageMapScale value="0.600000"/>
      <damageOffsetScale value="1.000000"/>
      <diffuseTint value="0x00FFFFFF"/>
      <steerWheelMult value="1.000000"/>
      <HDTextureDist value="5.000000"/>
      <lodDistances content="float_array">
        15.000000
        35.000000
        80.000000
        160.000000
        500.000000
        500.000000
      </lodDistances>
      <minSeatHeight value="1.076"/>
      <identicalModelSpawnDistance value="20"/>
      <maxNumOfSameColor value="10"/>
      <defaultBodyHealth value="1000.000000"/>
      <pretendOccupantsScale value="1.000000"/>
      <visibleSpawnDistScale value="1.000000"/>
      <trackerPathWidth value="2.000000"/>
      <weaponForceMult value="1.000000"/>
      <frequency value="10"/>
      <swankness>SWANKNESS_1</swankness>
      <maxNum value="2"/>
      <flags>FLAG_HAS_LIVERY FLAG_BIG FLAG_NO_BOOT FLAG_AVOID_TURNS FLAG_DELIVERY FLAG_EXTRAS_REQUIRE FLAG_EXTRAS_STRONG FLAG_IS_BULKY FLAG_BLOCK_FROM_ATTRACTOR_SCENARIO</flags>
      <type>VEHICLE_TYPE_CAR</type>
      <plateType>VPT_FRONT_AND_BACK_PLATES</plateType>
      <dashboardType>VDT_TRUCK</dashboardType>
      <vehicleClass>VC_UTILITY</vehicleClass>
      <wheelType>VWT_SPORT</wheelType>
      <trailers/>
      <additionalTrailers/>
      <drivers>
        <Item>
          <driverName>S_M_M_AutoShop_01</driverName>
          <npcName/>
        </Item>
        <Item>
          <driverName>S_M_M_AutoShop_02</driverName>
          <npcName/>
        </Item>
        <Item>
          <driverName>S_M_M_Trucker_01</driverName>
          <npcName/>
        </Item>
      </drivers>
      <extraIncludes/>
      <doorsWithCollisionWhenClosed>
        <Item>VEH_EXT_BONNET</Item>
      </doorsWithCollisionWhenClosed>
      <driveableDoors/>
      <bumpersNeedToCollideWithMap value="true"/>
      <needsRopeTexture value="false"/>
      <requiredExtras/>
      <rewards/>
      <cinematicPartCamera>
        <Item>WHEEL_WIDE_REAR_RIGHT_CAMERA</Item>
        <Item>WHEEL_WIDE_REAR_LEFT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet>Truck</NmBraceOverrideSet>
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000"/>
      <buoyancySphereSizeScale value="1.000000"/>
      <pOverrideRagdollThreshold type="NULL"/>
      <firstPersonDrivebyData>
        <Item>VAN_PONY_FRONT_LEFT</Item>
        <Item>VAN_PONY_FRONT_RIGHT</Item>
      </firstPersonDrivebyData>
      <numSeatsOverride value="2"/>
    </Item>
    <Item>
      <modelName>lpchopper</modelName>
      <txdName>lpchopper</txdName>
      <handlingId>avarus</handlingId>
      <gameName>lpchopper</gameName>
      <vehicleMakeName>WESTERN</vehicleMakeName>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected/>
      <ptfxAssetName>null</ptfxAssetName>
      <audioNameHash>cliffhanger</audioNameHash>
      <layout>LAYOUT_BIKE_FREEWAY</layout>
      <coverBoundOffsets>NIGHTBLADE_COVER_OFFSET_INFO</coverBoundOffsets>
      <POVTuningInfo>DAEMON_POV_TUNING</POVTuningInfo>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout/>
      <cameraName>FOLLOW_UPRIGHT_BIKE_CAMERA</cameraName>
      <aimCameraName>BIKE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>BIKE_ZOMBIE_POV_CAMERA</bonnetCameraName>
      <povCameraName>BIKE_ZOMBIE_POV_CAMERA</povCameraName>
      <FirstPersonDriveByIKOffset x="0.000000" y="-0.060000" z="0.000000"/>
      <FirstPersonDriveByUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonProjectileDriveByIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonProjectileDriveByPassengerIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonProjectileDriveByRearLeftIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonProjectileDriveByRearRightIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonDriveByLeftPassengerIKOffset x="0.000000" y="-0.060000" z="-0.030000"/>
      <FirstPersonDriveByRightPassengerIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonDriveByRightRearPassengerIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonDriveByLeftPassengerUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonDriveByRightPassengerUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonMobilePhoneOffset x="0.155000" y="0.315000" z="0.520000"/>
      <FirstPersonPassengerMobilePhoneOffset x="0.136000" y="0.223000" z="0.425000"/>
      <PovCameraOffset x="0.000000" y="-0.040000" z="0.000000"/>
      <PovCameraVerticalAdjustmentForRollCage value="0.000000"/>
      <PovPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000"/>
      <PovRearPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000"/>
      <vfxInfoName>VFXVEHICLEINFO_MOTORBIKE_GENERIC</vfxInfoName>
      <shouldUseCinematicViewMode value="true"/>
      <shouldCameraTransitionOnClimbUpDown value="false"/>
      <shouldCameraIgnoreExiting value="false"/>
      <AllowPretendOccupants value="false"/>
      <AllowJoyriding value="true"/>
      <AllowSundayDriving value="true"/>
      <AllowBodyColorMapping value="true"/>
      <wheelScale value="0.216400"/>
      <wheelScaleRear value="0.196000"/>
      <dirtLevelMin value="0.000000"/>
      <dirtLevelMax value="0.550000"/>
      <envEffScaleMin value="0.000000"/>
      <envEffScaleMax value="1.000000"/>
      <envEffScaleMin2 value="0.000000"/>
      <envEffScaleMax2 value="1.000000"/>
      <damageMapScale value="0.500000"/>
      <damageOffsetScale value="0.100000"/>
      <diffuseTint value="0x00FFFFFF"/>
      <steerWheelMult value="0.400000"/>
      <HDTextureDist value="5.000000"/>
      <lodDistances content="float_array">
        15.000000
        30.000000
        50.000000
        100.000000
        500.000000
        500.000000
      </lodDistances>
      <minSeatHeight value="10"/>
      <identicalModelSpawnDistance value="20"/>
      <maxNumOfSameColor value="10"/>
      <defaultBodyHealth value="1000.000000"/>
      <pretendOccupantsScale value="1.000000"/>
      <visibleSpawnDistScale value="1.000000"/>
      <trackerPathWidth value="2.000000"/>
      <weaponForceMult value="1.000000"/>
      <frequency value="20"/>
      <swankness>SWANKNESS_3</swankness>
      <maxNum value="4"/>
      <flags>FLAG_HAS_LIVERY FLAG_NO_BOOT FLAG_IGNORE_ON_SIDE_CHECK FLAG_AVERAGE_CAR FLAG_HEADLIGHTS_USE_ACTUAL_BONE_POS FLAG_CAN_HAVE_NEONS</flags>
      <type>VEHICLE_TYPE_BIKE</type>
      <plateType>VPT_NONE</plateType>
      <dashboardType>VDT_BANSHEE</dashboardType>
      <vehicleClass>VC_MOTORCYCLE</vehicleClass>
      <wheelType>VWT_BIKE</wheelType>
      <trailers/>
      <additionalTrailers/>
      <drivers/>
      <extraIncludes/>
      <extraIncludes/>
      <Item>EXTRA_2</Item>
      <Item>EXTRA_3</Item>
      <doorsWithCollisionWhenClosed/>
      <driveableDoors/>
      <bumpersNeedToCollideWithMap value="false"/>
      <needsRopeTexture value="false"/>
      <requiredExtras>EXTRA_3 EXTRA_2</requiredExtras>
      <rewards/>
      <cinematicPartCamera>
        <Item>WHEEL_REAR_LEFT_CAMERA</Item>
        <Item>WHEEL_REAR_RIGHT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet>Bike</NmBraceOverrideSet>
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000"/>
      <buoyancySphereSizeScale value="1.000000"/>
      <pOverrideRagdollThreshold type="NULL"/>
      <firstPersonDrivebyData>
        <Item>BIKE_ZOMBIE_FRONT</Item>
      </firstPersonDrivebyData>
    </Item>
    <Item>
      <modelName>trailers2a</modelName>
      <txdName>trailers2a</txdName>
      <handlingId>TRAILER</handlingId>
      <gameName>TRAILER</gameName>
      <vehicleMakeName/>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected/>
      <ptfxAssetName>null</ptfxAssetName>
      <audioNameHash>TRAILERS2</audioNameHash>
      <layout>LAYOUT_TRUCK</layout>
      <coverBoundOffsets>TRAILERS2_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout/>
      <cameraName>FOLLOW_ARTIC_CAMERA</cameraName>
      <aimCameraName>ARTIC_AIM_CAMERA</aimCameraName>
      <bonnetCameraName/>
      <povCameraName/>
      <FirstPersonDriveByIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonDriveByUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonProjectileDriveByIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonProjectileDriveByPassengerIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonProjectileDriveByRearLeftIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonProjectileDriveByRearRightIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonDriveByLeftPassengerIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonDriveByRightPassengerIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonDriveByRightRearPassengerIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonDriveByLeftPassengerUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonDriveByRightPassengerUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonMobilePhoneOffset x="0.155000" y="0.315000" z="0.520000"/>
      <PovPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000"/>
      <PovRearPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000"/>
      <vfxInfoName>VFXVEHICLEINFO_TRAILER_GENERIC</vfxInfoName>
      <shouldUseCinematicViewMode value="true"/>
      <shouldCameraTransitionOnClimbUpDown value="false"/>
      <shouldCameraIgnoreExiting value="false"/>
      <AllowPretendOccupants value="true"/>
      <AllowJoyriding value="true"/>
      <AllowSundayDriving value="true"/>
      <AllowBodyColorMapping value="true"/>
      <wheelScale value="0.317100"/>
      <wheelScaleRear value="0.319700"/>
      <dirtLevelMin value="0.000000"/>
      <dirtLevelMax value="0.850000"/>
      <envEffScaleMin value="0.000000"/>
      <envEffScaleMax value="1.000000"/>
      <envEffScaleMin2 value="0.000000"/>
      <envEffScaleMax2 value="1.000000"/>
      <damageMapScale value="0.600000"/>
      <damageOffsetScale value="1.000000"/>
      <diffuseTint value="0x00FFFFFF"/>
      <steerWheelMult value="1.000000"/>
      <HDTextureDist value="5.000000"/>
      <lodDistances content="float_array">
        25.000000
        40.000000
        70.000000
        140.000000
        750.000000
        750.000000
      </lodDistances>
      <identicalModelSpawnDistance value="20"/>
      <maxNumOfSameColor value="10"/>
      <defaultBodyHealth value="1000.000000"/>
      <pretendOccupantsScale value="1.000000"/>
      <visibleSpawnDistScale value="1.000000"/>
      <trackerPathWidth value="2.000000"/>
      <weaponForceMult value="1.000000"/>
      <frequency value="10"/>
      <swankness>SWANKNESS_1</swankness>
      <maxNum value="999"/>
      <flags>FLAG_HAS_LIVERY FLAG_EXTRAS_REQUIRE FLAG_EXTRAS_STRONG FLAG_PEDS_CAN_STAND_ON_TOP FLAG_GEN_NAVMESH FLAG_DONT_SPAWN_AS_AMBIENT</flags>
      <type>VEHICLE_TYPE_TRAILER</type>
      <plateType>VPT_NONE</plateType>
      <vehicleClass>VC_UTILITY</vehicleClass>
      <wheelType>VWT_SPORT</wheelType>
      <trailers/>
      <additionalTrailers/>
      <drivers/>
      <extraIncludes/>
      <doorsWithCollisionWhenClosed>
        <Item>VEH_EXT_DOOR_DSIDE_R</Item>
        <Item>VEH_EXT_DOOR_PSIDE_R</Item>
      </doorsWithCollisionWhenClosed>
      <driveableDoors/>
      <bumpersNeedToCollideWithMap value="true"/>
      <needsRopeTexture value="false"/>
      <requiredExtras/>
      <rewards/>
      <cinematicPartCamera/>
      <NmBraceOverrideSet/>
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000"/>
      <buoyancySphereSizeScale value="1.000000"/>
      <pOverrideRagdollThreshold type="NULL"/>
      <numSeatsOverride value="0"/>
    </Item>
    <Item>
      <modelName>watertanker</modelName>
      <txdName>watertanker</txdName>
      <handlingId>TANKER</handlingId>
      <gameName>TRAILER</gameName>
      <vehicleMakeName/>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected/>
      <ptfxAssetName>null</ptfxAssetName>
      <audioNameHash>TANKER</audioNameHash>
      <layout>LAYOUT_TRUCK</layout>
      <coverBoundOffsets>TANKER_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_TANKER</explosionInfo>
      <scenarioLayout/>
      <cameraName>FOLLOW_ARTIC_CAMERA</cameraName>
      <aimCameraName>ARTIC_AIM_CAMERA</aimCameraName>
      <bonnetCameraName/>
      <povCameraName/>
      <FirstPersonDriveByIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonDriveByUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonProjectileDriveByIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonProjectileDriveByPassengerIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonProjectileDriveByRearLeftIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonProjectileDriveByRearRightIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonDriveByLeftPassengerIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonDriveByRightPassengerIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonDriveByRightRearPassengerIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonDriveByLeftPassengerUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonDriveByRightPassengerUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonMobilePhoneOffset x="0.155000" y="0.315000" z="0.520000"/>
      <PovPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000"/>
      <PovRearPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000"/>
      <vfxInfoName>VFXVEHICLEINFO_TRAILER_TANKER</vfxInfoName>
      <shouldUseCinematicViewMode value="true"/>
      <shouldCameraTransitionOnClimbUpDown value="false"/>
      <shouldCameraIgnoreExiting value="false"/>
      <AllowPretendOccupants value="true"/>
      <AllowJoyriding value="true"/>
      <AllowSundayDriving value="true"/>
      <AllowBodyColorMapping value="true"/>
      <wheelScale value="0.350314"/>
      <wheelScaleRear value="0.350314"/>
      <dirtLevelMin value="0.300000"/>
      <dirtLevelMax value="0.850000"/>
      <envEffScaleMin value="0.300000"/>
      <envEffScaleMax value="0.500000"/>
      <envEffScaleMin2 value="0.300000"/>
      <envEffScaleMax2 value="0.500000"/>
      <damageMapScale value="0.600000"/>
      <damageOffsetScale value="1.000000"/>
      <diffuseTint value="0x00FFFFFF"/>
      <steerWheelMult value="1.000000"/>
      <HDTextureDist value="5.000000"/>
      <lodDistances content="float_array">
        15.000000
        30.000000
        70.000000
        140.000000
        750.000000
        750.000000
      </lodDistances>
      <identicalModelSpawnDistance value="20"/>
      <maxNumOfSameColor value="10"/>
      <defaultBodyHealth value="120.000000"/>
      <pretendOccupantsScale value="1.000000"/>
      <visibleSpawnDistScale value="1.000000"/>
      <trackerPathWidth value="2.000000"/>
      <weaponForceMult value="1.000000"/>
      <frequency value="10"/>
      <swankness>SWANKNESS_1</swankness>
      <maxNum value="999"/>
      <flags>FLAG_EXTRAS_REQUIRE FLAG_HAS_LIVERY FLAG_EXTRAS_STRONG FLAG_PEDS_CAN_STAND_ON_TOP FLAG_GEN_NAVMESH FLAG_DONT_SPAWN_AS_AMBIENT</flags>
      <type>VEHICLE_TYPE_TRAILER</type>
      <plateType>VPT_NONE</plateType>
      <vehicleClass>VC_UTILITY</vehicleClass>
      <wheelType>VWT_SPORT</wheelType>
      <trailers/>
      <additionalTrailers/>
      <drivers/>
      <extraIncludes/>
      <doorsWithCollisionWhenClosed/>
      <driveableDoors/>
      <bumpersNeedToCollideWithMap value="true"/>
      <needsRopeTexture value="false"/>
      <requiredExtras/>
      <rewards/>
      <cinematicPartCamera/>
      <NmBraceOverrideSet/>
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000"/>
      <buoyancySphereSizeScale value="1.000000"/>
      <pOverrideRagdollThreshold type="NULL"/>
      <numSeatsOverride value="0"/>
    </Item>
    <Item>
      <modelName>utilliflatbed</modelName>
      <txdName>utilliflatbed</txdName>
      <handlingId>utilliflatbed</handlingId>
      <gameName>utilliflatbed</gameName>
      <vehicleMakeName/>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected/>
      <ptfxAssetName>null</ptfxAssetName>
      <audioNameHash>flatbed</audioNameHash>
      <layout>LAYOUT_VAN_MULE</layout>
      <coverBoundOffsets>UTILITRUCK2_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_TRUCK</explosionInfo>
      <scenarioLayout/>
      <cameraName>DEFAULT_FOLLOW_VEHICLE_CAMERA</cameraName>
      <aimCameraName>DEFAULT_THIRD_PERSON_VEHICLE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>VEHICLE_BONNET_CAMERA_LOW</bonnetCameraName>
      <povCameraName>DEFAULT_POV_CAMERA_NO_REVERSE</povCameraName>
      <FirstPersonDriveByIKOffset x="0.000000" y="-0.035000" z="-0.035000"/>
      <FirstPersonDriveByUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonProjectileDriveByIKOffset x="0.053000" y="-0.033000" z="-0.048000"/>
      <FirstPersonProjectileDriveByPassengerIKOffset x="0.120000" y="-0.010000" z="-0.093000"/>
      <FirstPersonDriveByLeftPassengerIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonDriveByRightPassengerIKOffset x="-0.050000" y="-0.005000" z="-0.063000"/>
      <FirstPersonDriveByLeftPassengerUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonDriveByRightPassengerUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonMobilePhoneOffset x="0.155000" y="0.315000" z="0.503000"/>
      <FirstPersonPassengerMobilePhoneOffset x="0.136000" y="0.223000" z="0.425000"/>
      <PovCameraOffset x="0.000000" y="-0.165000" z="0.650000"/>
      <PovCameraVerticalAdjustmentForRollCage value="0.000000"/>
      <PovPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000"/>
      <vfxInfoName>VFXVEHICLEINFO_TRUCK_HIDDEN_EXHAUST</vfxInfoName>
      <shouldUseCinematicViewMode value="true"/>
      <shouldCameraTransitionOnClimbUpDown value="false"/>
      <shouldCameraIgnoreExiting value="false"/>
      <AllowPretendOccupants value="true"/>
      <AllowJoyriding value="false"/>
      <AllowSundayDriving value="false"/>
      <AllowBodyColorMapping value="true"/>
      <wheelScale value="0.293100"/>
      <wheelScaleRear value="0.293100"/>
      <dirtLevelMin value="0.300000"/>
      <dirtLevelMax value="1.000000"/>
      <envEffScaleMin value="0.000000"/>
      <envEffScaleMax value="1.000000"/>
      <envEffScaleMin2 value="0.000000"/>
      <envEffScaleMax2 value="1.000000"/>
      <damageMapScale value="0.600000"/>
      <damageOffsetScale value="1.000000"/>
      <diffuseTint value="0x00FFFFFF"/>
      <steerWheelMult value="1.000000"/>
      <HDTextureDist value="5.000000"/>
      <lodDistances content="float_array">
        20.000000
        35.000000
        80.000000
        160.000000
        500.000000
        500.000000
      </lodDistances>
      <minSeatHeight value="1.076"/>
      <identicalModelSpawnDistance value="20"/>
      <maxNumOfSameColor value="10"/>
      <defaultBodyHealth value="1000.000000"/>
      <pretendOccupantsScale value="1.000000"/>
      <visibleSpawnDistScale value="1.000000"/>
      <trackerPathWidth value="2.000000"/>
      <weaponForceMult value="1.000000"/>
      <frequency value="40"/>
      <swankness>SWANKNESS_1</swankness>
      <maxNum value="2"/>
      <flags>FLAG_BIG FLAG_NO_BOOT FLAG_AVOID_TURNS FLAG_DELIVERY FLAG_EXTRAS_REQUIRE FLAG_EXTRAS_STRONG FLAG_IS_BULKY FLAG_BLOCK_FROM_ATTRACTOR_SCENARIO</flags>
      <type>VEHICLE_TYPE_CAR</type>
      <plateType>VPT_FRONT_AND_BACK_PLATES</plateType>
      <dashboardType>VDT_TRUCKDIGI</dashboardType>
      <vehicleClass>VC_UTILITY</vehicleClass>
      <wheelType>VWT_SPORT</wheelType>
      <trailers/>
      <additionalTrailers/>
      <drivers>
        <Item>
          <driverName>S_M_M_AutoShop_01</driverName>
          <npcName/>
        </Item>
        <Item>
          <driverName>S_M_M_AutoShop_02</driverName>
          <npcName/>
        </Item>
        <Item>
          <driverName>S_M_M_Trucker_01</driverName>
          <npcName/>
        </Item>
      </drivers>
      <extraIncludes/>
      <doorsWithCollisionWhenClosed/>
      <driveableDoors/>
      <bumpersNeedToCollideWithMap value="true"/>
      <needsRopeTexture value="false"/>
      <requiredExtras/>
      <rewards/>
      <cinematicPartCamera>
        <Item>WHEEL_WIDE_REAR_RIGHT_CAMERA</Item>
        <Item>WHEEL_WIDE_REAR_LEFT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet>Truck</NmBraceOverrideSet>
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000"/>
      <buoyancySphereSizeScale value="1.000000"/>
      <pOverrideRagdollThreshold type="NULL"/>
      <firstPersonDrivebyData>
        <Item>VAN_PONY_FRONT_LEFT</Item>
        <Item>VAN_PONY_FRONT_RIGHT</Item>
      </firstPersonDrivebyData>
    </Item>
    <Item>
      <modelName>utillitruck3</modelName>
      <txdName>utillitruck3</txdName>
      <handlingId>UTILTRUC3</handlingId>
      <gameName>UTILTRUC</gameName>
      <vehicleMakeName/>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected/>
      <ptfxAssetName>null</ptfxAssetName>
      <audioNameHash/>
      <layout>LAYOUT_VAN</layout>
      <coverBoundOffsets>UTILITRUCK3_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_TRUCK</explosionInfo>
      <scenarioLayout/>
      <cameraName>DEFAULT_FOLLOW_VEHICLE_CAMERA</cameraName>
      <aimCameraName>MID_BOX_VEHICLE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>VEHICLE_BONNET_CAMERA_STANDARD_LONG</bonnetCameraName>
      <povCameraName>DEFAULT_POV_CAMERA</povCameraName>
      <FirstPersonDriveByIKOffset x="0.000000" y="-0.035000" z="-0.035000"/>
      <FirstPersonDriveByUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonProjectileDriveByIKOffset x="0.005000" y="-0.015000" z="0.030000"/>
      <FirstPersonProjectileDriveByPassengerIKOffset x="0.005000" y="-0.015000" z="0.030000"/>
      <FirstPersonDriveByLeftPassengerIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonDriveByRightPassengerIKOffset x="-0.050000" y="-0.005000" z="-0.063000"/>
      <FirstPersonDriveByLeftPassengerUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonDriveByRightPassengerUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonMobilePhoneOffset x="0.155000" y="0.315000" z="0.520000"/>
      <FirstPersonPassengerMobilePhoneOffset x="0.136000" y="0.223000" z="0.425000"/>
      <PovCameraOffset x="0.000000" y="-0.125000" z="0.650000"/>
      <PovCameraVerticalAdjustmentForRollCage value="0.000000"/>
      <PovPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000"/>
      <vfxInfoName>VFXVEHICLEINFO_TRUCK_HIDDEN_EXHAUST</vfxInfoName>
      <shouldUseCinematicViewMode value="true"/>
      <shouldCameraTransitionOnClimbUpDown value="false"/>
      <shouldCameraIgnoreExiting value="false"/>
      <AllowPretendOccupants value="true"/>
      <AllowJoyriding value="false"/>
      <AllowSundayDriving value="false"/>
      <AllowBodyColorMapping value="true"/>
      <wheelScale value="0.281500"/>
      <wheelScaleRear value="0.281500"/>
      <dirtLevelMin value="0.300000"/>
      <dirtLevelMax value="1.000000"/>
      <envEffScaleMin value="0.000000"/>
      <envEffScaleMax value="1.000000"/>
      <envEffScaleMin2 value="0.000000"/>
      <envEffScaleMax2 value="1.000000"/>
      <damageMapScale value="0.600000"/>
      <damageOffsetScale value="1.000000"/>
      <diffuseTint value="0x00FFFFFF"/>
      <steerWheelMult value="1.000000"/>
      <HDTextureDist value="5.000000"/>
      <lodDistances content="float_array">
        15.000000
        35.000000
        80.000000
        160.000000
        500.000000
        500.000000
      </lodDistances>
      <minSeatHeight value="0.861"/>
      <identicalModelSpawnDistance value="20"/>
      <maxNumOfSameColor value="10"/>
      <defaultBodyHealth value="1000.000000"/>
      <pretendOccupantsScale value="1.000000"/>
      <visibleSpawnDistScale value="1.000000"/>
      <trackerPathWidth value="2.000000"/>
      <weaponForceMult value="1.000000"/>
      <frequency value="50"/>
      <swankness>SWANKNESS_1</swankness>
      <maxNum value="2"/>
      <flags>FLAG_BIG FLAG_NO_BOOT FLAG_AVOID_TURNS FLAG_DELIVERY FLAG_EXTRAS_REQUIRE FLAG_EXTRAS_STRONG FLAG_IS_BULKY FLAG_BLOCK_FROM_ATTRACTOR_SCENARIO</flags>
      <type>VEHICLE_TYPE_CAR</type>
      <plateType>VPT_FRONT_AND_BACK_PLATES</plateType>
      <dashboardType>VDT_TRUCKDIGI</dashboardType>
      <vehicleClass>VC_UTILITY</vehicleClass>
      <wheelType>VWT_SPORT</wheelType>
      <trailers/>
      <additionalTrailers/>
      <drivers>
        <Item>
          <driverName>S_M_M_AutoShop_01</driverName>
          <npcName/>
        </Item>
        <Item>
          <driverName>S_M_M_AutoShop_02</driverName>
          <npcName/>
        </Item>
        <Item>
          <driverName>S_M_M_Trucker_01</driverName>
          <npcName/>
        </Item>
      </drivers>
      <extraIncludes/>
      <doorsWithCollisionWhenClosed/>
      <driveableDoors/>
      <bumpersNeedToCollideWithMap value="true"/>
      <needsRopeTexture value="false"/>
      <requiredExtras/>
      <rewards/>
      <cinematicPartCamera>
        <Item>WHEEL_WIDE_REAR_RIGHT_CAMERA</Item>
        <Item>WHEEL_WIDE_REAR_LEFT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet>Truck</NmBraceOverrideSet>
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000"/>
      <buoyancySphereSizeScale value="1.000000"/>
      <pOverrideRagdollThreshold type="NULL"/>
      <firstPersonDrivebyData>
        <Item>VAN_RUMPO_FRONT_LEFT</Item>
        <Item>VAN_RUMPO_FRONT_RIGHT</Item>
      </firstPersonDrivebyData>
    </Item>
    <Item>
      <modelName>pdgystockade</modelName>
      <txdName>pdgystockade</txdName>
      <handlingId>PDGYSTOCK</handlingId>
      <gameName>PDGYSTOCK</gameName>
      <vehicleMakeName>BRUTE</vehicleMakeName>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected/>
      <ptfxAssetName>null</ptfxAssetName>
      <audioNameHash>stockade</audioNameHash>
      <layout>LAYOUT_RIOT_VAN</layout>
      <coverBoundOffsets>STOCKADE_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_TRUCK</explosionInfo>
      <scenarioLayout/>
      <cameraName>DEFAULT_FOLLOW_VEHICLE_CAMERA</cameraName>
      <aimCameraName>BOX_VEHICLE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>VEHICLE_BONNET_CAMERA_STOCKADE</bonnetCameraName>
      <povCameraName>DEFAULT_POV_CAMERA_NO_REVERSE_LOOKAROUND</povCameraName>
      <FirstPersonDriveByIKOffset x="0.000000" y="0.000000" z="-0.053000"/>
      <FirstPersonDriveByUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonProjectileDriveByIKOffset x="0.153000" y="0.028000" z="-0.078000"/>
      <FirstPersonProjectileDriveByPassengerIKOffset x="-0.030000" y="0.081000" z="-0.103000"/>
      <FirstPersonProjectileDriveByRearLeftIKOffset x="0.095000" y="0.025000" z="-0.075000"/>
      <FirstPersonProjectileDriveByRearRightIKOffset x="0.095000" y="0.025000" z="-0.075000"/>
      <FirstPersonDriveByLeftPassengerIKOffset x="-0.015000" y="0.015000" z="-0.080000"/>
      <FirstPersonDriveByRightPassengerIKOffset x="0.000000" y="0.000000" z="-0.053000"/>
      <FirstPersonDriveByRightRearPassengerIKOffset x="-0.015000" y="0.015000" z="-0.080000"/>
      <FirstPersonDriveByLeftPassengerUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonDriveByRightPassengerUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonMobilePhoneOffset x="0.155000" y="0.333000" z="0.535000"/>
      <FirstPersonPassengerMobilePhoneOffset x="0.136000" y="0.313000" z="0.435000"/>
      <FirstPersonMobilePhoneSeatIKOffset>
        <Item>
          <Offset x="0.281000" y="0.491000" z="0.566000"/>
          <SeatIndex value="2"/>
        </Item>
        <Item>
          <Offset x="0.156000" y="0.533000" z="0.566000"/>
          <SeatIndex value="3"/>
        </Item>
      </FirstPersonMobilePhoneSeatIKOffset>
      <PovCameraOffset x="0.000000" y="-0.100000" z="0.800000"/>
      <PovCameraVerticalAdjustmentForRollCage value="0.000000"/>
      <PovPassengerCameraOffset x="0.000000" y="0.000000" z="0.800000"/>
      <PovRearPassengerCameraOffset x="0.000000" y="0.000000" z="0.030000"/>
      <vfxInfoName>VFXVEHICLEINFO_TRUCK_HIDDEN_EXHAUST</vfxInfoName>
      <shouldUseCinematicViewMode value="true"/>
      <shouldCameraTransitionOnClimbUpDown value="false"/>
      <shouldCameraIgnoreExiting value="false"/>
      <AllowPretendOccupants value="true"/>
      <AllowJoyriding value="false"/>
      <AllowSundayDriving value="false"/>
      <AllowBodyColorMapping value="true"/>
      <wheelScale value="0.328400"/>
      <wheelScaleRear value="0.328400"/>
      <dirtLevelMin value="0.300000"/>
      <dirtLevelMax value="0.850000"/>
      <envEffScaleMin value="0.000000"/>
      <envEffScaleMax value="1.000000"/>
      <envEffScaleMin2 value="0.000000"/>
      <envEffScaleMax2 value="1.000000"/>
      <damageMapScale value="0.600000"/>
      <damageOffsetScale value="1.000000"/>
      <diffuseTint value="0xAA0A0A0A"/>
      <steerWheelMult value="1.000000"/>
      <HDTextureDist value="5.000000"/>
      <lodDistances content="float_array">
        20.000000
        35.000000
        90.000000
        180.000000
        500.000000
        500.000000
      </lodDistances>
      <minSeatHeight value="1.178"/>
      <identicalModelSpawnDistance value="20"/>
      <maxNumOfSameColor value="10"/>
      <defaultBodyHealth value="1000.000000"/>
      <pretendOccupantsScale value="1.000000"/>
      <visibleSpawnDistScale value="1.000000"/>
      <trackerPathWidth value="2.000000"/>
      <weaponForceMult value="1.000000"/>
      <frequency value="10"/>
      <swankness>SWANKNESS_1</swankness>
      <maxNum value="5"/>
      <flags>FLAG_BIG FLAG_NO_BOOT FLAG_AVOID_TURNS FLAG_DELIVERY FLAG_PEDS_CAN_STAND_ON_TOP FLAG_DAMPEN_STICKBOMB_DAMAGE FLAG_DONT_SPAWN_IN_CARGEN FLAG_USE_FAT_INTERIOR_LIGHT FLAG_IS_BULKY FLAG_BLOCK_FROM_ATTRACTOR_SCENARIO FLAG_FLEE_FROM_COMBAT FLAG_HAS_BULLET_RESISTANT_GLASS</flags>
      <type>VEHICLE_TYPE_CAR</type>
      <plateType>VPT_BACK_PLATES</plateType>
      <dashboardType>VDT_TRUCKDIGI</dashboardType>
      <vehicleClass>VC_COMMERCIAL</vehicleClass>
      <wheelType>VWT_SPORT</wheelType>
      <trailers/>
      <additionalTrailers/>
      <drivers>
        <Item>
          <driverName>S_M_M_Armoured_01</driverName>
          <npcName/>
        </Item>
        <Item>
          <driverName>S_M_M_Armoured_02</driverName>
          <npcName/>
        </Item>
      </drivers>
      <extraIncludes/>
      <doorsWithCollisionWhenClosed>
        <Item>VEH_EXT_DOOR_DSIDE_R</Item>
        <Item>VEH_EXT_DOOR_PSIDE_R</Item>
        <Item>VEH_EXT_BONNET</Item>
      </doorsWithCollisionWhenClosed>
      <driveableDoors/>
      <bumpersNeedToCollideWithMap value="false"/>
      <needsRopeTexture value="false"/>
      <requiredExtras>EXTRA_1</requiredExtras>
      <rewards/>
      <cinematicPartCamera>
        <Item>WHEEL_WIDE_REAR_RIGHT_CAMERA</Item>
        <Item>WHEEL_WIDE_REAR_LEFT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet>Truck</NmBraceOverrideSet>
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000"/>
      <buoyancySphereSizeScale value="1.000000"/>
      <pOverrideRagdollThreshold type="NULL"/>
      <firstPersonDrivebyData>
        <Item>VAN_STOCKADE_FRONT_LEFT</Item>
        <Item>VAN_STOCKADE_FRONT_RIGHT</Item>
        <Item>VAN_STOCKADE_REAR_LEFT</Item>
        <Item>VAN_STOCKADE_REAR_RIGHT</Item>
      </firstPersonDrivebyData>
      <numSeatsOverride value="4"/>
    </Item>
    <Item>
      <modelName>polimpaler</modelName>
      <txdName>polimpaler</txdName>
      <handlingId>polimpaler</handlingId>
      <gameName>polimpaler</gameName>
      <vehicleMakeName>VAPID</vehicleMakeName>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected/>
      <ptfxAssetName>null</ptfxAssetName>
      <audioNameHash>POLICE</audioNameHash>
      <layout>LAYOUT_STANDARD</layout>
      <coverBoundOffsets>POLICE_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout/>
      <cameraName>DEFAULT_FOLLOW_VEHICLE_CAMERA</cameraName>
      <aimCameraName>DEFAULT_THIRD_PERSON_VEHICLE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>VEHICLE_BONNET_CAMERA_LOW_CALICO</bonnetCameraName>
      <povCameraName>DEFAULT_POV_CAMERA</povCameraName>
      <FirstPersonDriveByIKOffset x="0.000000" y="-0.060000" z="-0.050000"/>
      <FirstPersonDriveByUnarmedIKOffset x="0.000000" y="-0.050000" z="-0.050000"/>
      <FirstPersonProjectileDriveByIKOffset x="0.000000" y="-0.075000" z="-0.045000"/>
      <FirstPersonProjectileDriveByPassengerIKOffset x="0.000000" y="-0.075000" z="-0.045000"/>
      <FirstPersonProjectileDriveByRearLeftIKOffset x="0.000000" y="0.020000" z="0.000000"/>
      <FirstPersonProjectileDriveByRearRightIKOffset x="0.000000" y="0.020000" z="0.000000"/>
      <FirstPersonDriveByLeftPassengerIKOffset x="0.000000" y="0.000000" z="-0.030000"/>
      <FirstPersonDriveByRightPassengerIKOffset x="0.000000" y="-0.155000" z="-0.060000"/>
      <FirstPersonDriveByRightRearPassengerIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonDriveByLeftPassengerUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000"/>
      <FirstPersonDriveByRightPassengerUnarmedIKOffset x="0.000000" y="-0.090000" z="-0.050000"/>
      <FirstPersonMobilePhoneOffset x="0.125000" y="0.293000" z="0.516000"/>
      <FirstPersonPassengerMobilePhoneOffset x="0.136000" y="0.223000" z="0.415000"/>
      <FirstPersonMobilePhoneSeatIKOffset>
        <Item>
          <Offset x="0.136000" y="0.146000" z="0.435000"/>
          <SeatIndex value="2"/>
        </Item>
        <Item>
          <Offset x="0.136000" y="0.146000" z="0.435000"/>
          <SeatIndex value="3"/>
        </Item>
      </FirstPersonMobilePhoneSeatIKOffset>
      <PovCameraOffset x="0.000000" y="-0.140000" z="0.665000"/>
      <PovCameraVerticalAdjustmentForRollCage value="0.000000"/>
      <PovPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000"/>
      <PovRearPassengerCameraOffset x="0.000000" y="0.000000" z="0.015000"/>
      <vfxInfoName>VFXVEHICLEINFO_CAR_GENERIC</vfxInfoName>
      <shouldUseCinematicViewMode value="true"/>
      <shouldCameraTransitionOnClimbUpDown value="false"/>
      <shouldCameraIgnoreExiting value="false"/>
      <AllowPretendOccupants value="true"/>
      <AllowJoyriding value="false"/>
      <AllowSundayDriving value="false"/>
      <AllowBodyColorMapping value="true"/>
      <wheelScale value="0.236900"/>
      <wheelScaleRear value="0.236900"/>
      <dirtLevelMin value="0.000000"/>
      <dirtLevelMax value="0.300000"/>
      <envEffScaleMin value="0.000000"/>
      <envEffScaleMax value="1.000000"/>
      <envEffScaleMin2 value="0.000000"/>
      <envEffScaleMax2 value="1.000000"/>
      <damageMapScale value="0.600000"/>
      <damageOffsetScale value="0.600000"/>
      <diffuseTint value="0x00FFFFFF"/>
      <steerWheelMult value="1.000000"/>
      <HDTextureDist value="5.000000"/>
      <lodDistances content="float_array">
        15.000000
        30.000000
        60.000000   
        120.000000  
        500.000000  
        500.000000
      </lodDistances>
      <minSeatHeight value="0.839"/>
      <identicalModelSpawnDistance value="20"/>
      <maxNumOfSameColor value="10"/>
      <defaultBodyHealth value="1000.000000"/>
      <pretendOccupantsScale value="1.000000"/>
      <visibleSpawnDistScale value="1.000000"/>
      <trackerPathWidth value="2.000000"/>
      <weaponForceMult value="1.000000"/>
      <frequency value="100"/>
      <swankness>SWANKNESS_1</swankness>
      <maxNum value="2"/>
      <flags>FLAG_EXTRAS_REQUIRE FLAG_EXTRAS_STRONG FLAG_LAW_ENFORCEMENT FLAG_EMERGENCY_SERVICE FLAG_DONT_SPAWN_IN_CARGEN FLAG_HAS_LIVERY</flags>
      <type>VEHICLE_TYPE_CAR</type>
      <plateType>VPT_FRONT_AND_BACK_PLATES</plateType>
      <dashboardType>VDT_GENTAXI</dashboardType>
      <vehicleClass>VC_EMERGENCY</vehicleClass>
      <wheelType>VWT_MUSCLE</wheelType>
      <trailers/>
      <additionalTrailers/>
      <drivers>
        <Item>
          <driverName>S_M_Y_Cop_01</driverName>
          <npcName/>
        </Item>
      </drivers>
      <extraIncludes/>
      <doorsWithCollisionWhenClosed/>
      <driveableDoors/>
      <bumpersNeedToCollideWithMap value="false"/>
      <needsRopeTexture value="false"/>
      <requiredExtras/>
      <rewards/>
      <cinematicPartCamera>
        <Item>WHEEL_FRONT_RIGHT_CAMERA</Item>
        <Item>WHEEL_FRONT_LEFT_CAMERA</Item>
        <Item>WHEEL_REAR_RIGHT_CAMERA</Item>
        <Item>WHEEL_REAR_LEFT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet/>
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000"/>
      <buoyancySphereSizeScale value="1.000000"/>
      <pOverrideRagdollThreshold type="NULL"/>
      <firstPersonDrivebyData>
        <Item>STD_POLICE_FRONT_LEFT</Item>
        <Item>STD_POLICE_FRONT_RIGHT</Item>
        <Item>STD_POLICE_REAR_LEFT</Item>
        <Item>STD_POLICE_REAR_RIGHT</Item>
      </firstPersonDrivebyData>
    </Item>
	<Item>
      <modelName>wintergreen</modelName>
      <txdName>wintergreen</txdName>
      <handlingId>POLICEB</handlingId>
      <gameName>WGREEN</gameName>
      <vehicleMakeName>WESTERN</vehicleMakeName>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected />
      <ptfxAssetName>null</ptfxAssetName>
      <audioNameHash>SOVEREIGN</audioNameHash>
      <layout>LAYOUT_BIKE_POLICE</layout>
      <coverBoundOffsets>BIKE2_COVER_OFFSET_INFO</coverBoundOffsets>
      <POVTuningInfo>SOVEREIGN_POV_TUNING</POVTuningInfo>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout />
      <cameraName>FOLLOW_UPRIGHT_BIKE_CAMERA</cameraName>
      <aimCameraName>BIKE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>BIKE_POLICEB_POV_CAMERA</bonnetCameraName>
      <povCameraName>BIKE_POLICEB_POV_CAMERA</povCameraName>
      <FirstPersonDriveByIKOffset x="0.000000" y="-0.060000" z="0.000000" />
      <FirstPersonDriveByUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonProjectileDriveByIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonProjectileDriveByPassengerIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonDriveByLeftPassengerIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonDriveByRightPassengerIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonDriveByLeftPassengerUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonDriveByRightPassengerUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonMobilePhoneOffset x="0.155000" y="0.315000" z="0.520000" />
      <FirstPersonPassengerMobilePhoneOffset x="0.136000" y="0.223000" z="0.425000" />
      <PovCameraOffset x="0.000000" y="0.000000" z="0.000000" />
      <PovCameraVerticalAdjustmentForRollCage value="0.000000" />
      <PovPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000" />
      <vfxInfoName>VFXVEHICLEINFO_MOTORBIKE_GENERIC</vfxInfoName>
      <shouldUseCinematicViewMode value="true" />
      <shouldCameraTransitionOnClimbUpDown value="false" />
      <shouldCameraIgnoreExiting value="false" />
      <AllowPretendOccupants value="false" />
      <AllowJoyriding value="true" />
      <AllowSundayDriving value="true" />
      <AllowBodyColorMapping value="true" />
      <wheelScale value="0.229500" />
      <wheelScaleRear value="0.200000" />
      <dirtLevelMin value="0.000000" />
      <dirtLevelMax value="0.200000" />
      <envEffScaleMin value="0.000000" />
      <envEffScaleMax value="1.000000" />
      <envEffScaleMin2 value="0.000000" />
      <envEffScaleMax2 value="1.000000" />
      <damageMapScale value="0.600000" />
      <damageOffsetScale value="1.000000" />
      <diffuseTint value="0x88000000" />
      <steerWheelMult value="0.600000" />
      <HDTextureDist value="5.000000" />
      <lodDistances content="float_array">
        10
        25
        70
        150
        500
        500
      </lodDistances>
      <minSeatHeight value="0.10" />
      <identicalModelSpawnDistance value="20" />
      <maxNumOfSameColor value="10" />
      <defaultBodyHealth value="1000.000000" />
      <pretendOccupantsScale value="1.000000" />
      <visibleSpawnDistScale value="1.000000" />
      <trackerPathWidth value="2.000000" />
      <weaponForceMult value="1.000000" />
      <frequency value="30" />
      <swankness>SWANKNESS_3</swankness>
      <maxNum value="3" />
      <flags>FLAG_NO_BOOT FLAG_IGNORE_ON_SIDE_CHECK FLAG_HEADLIGHTS_USE_ACTUAL_BONE_POS FLAG_BIKE_CLAMP_PICKUP_LEAN_RATE FLAG_HAS_INTERIOR_EXTRAS</flags>
      <type>VEHICLE_TYPE_BIKE</type>
      <plateType>VPT_BACK_PLATES</plateType>
      <dashboardType>VDT_ZTYPE</dashboardType>
      <vehicleClass>VC_MOTORCYCLE</vehicleClass>
      <wheelType>VWT_BIKE</wheelType>
      <trailers />
      <additionalTrailers />
      <drivers />
      <extraIncludes />
      <doorsWithCollisionWhenClosed />
      <driveableDoors />
      <bumpersNeedToCollideWithMap value="false" />
      <needsRopeTexture value="false" />
      <requiredExtras />
      <rewards />
      <cinematicPartCamera>
        <Item>WHEEL_REAR_LEFT_CAMERA</Item>
        <Item>WHEEL_REAR_RIGHT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet>Bike</NmBraceOverrideSet>
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000" />
      <buoyancySphereSizeScale value="1.000000" />
      <pOverrideRagdollThreshold type="NULL" />
      <firstPersonDrivebyData>
        <Item>BIKE_POLICEB_FRONT</Item>
      </firstPersonDrivebyData>
    </Item>
  </InitDatas>
  <txdRelationships>
    <Item>
      <parent>vehshare</parent>
      <child>vehicles_wintergreen_interior</child>
    </Item>  
    <Item>
      <parent>vehicles_wintergreen_interior</parent>
      <child>wintergreen</child>
    </Item>   
    <Item>
      <parent>vehicles_chopperbk_interior</parent>
      <child>sovereign</child>
    </Item>
    <Item>
      <parent>vehshare</parent>
      <child>vehshare_truck</child>
    </Item>
    <Item>
      <parent>vehshare</parent>
      <child>vehicles_comet_interior</child>
    </Item>
    <Item>
      <parent>vehshare</parent>
      <child>vehicles_btype_interior</child>
    </Item>
    <Item>
      <parent>vehshare_truck</parent>
      <child>vehshare_worn</child>
    </Item>
    <Item>
      <parent>vehshare_truck</parent>
      <child>vehshare_army</child>
    </Item>
    <Item>
      <parent>vehshare</parent>
      <child>vehicles_blista2_interior</child>
    </Item>
    <Item>
      <parent>vehshare</parent>
      <child>vehicles_futo_interior</child>
    </Item>
    <Item>
      <parent>vehshare</parent>
      <child>vehicles_monster_interior</child>
    </Item>
    <Item>
      <parent>vehshare</parent>
      <child>vehicles_journey_interior</child>
    </Item>
    <Item>
      <parent>vehshare_truck</parent>
      <child>vehicles_van_interior</child>
    </Item>
    <Item>
      <parent>vehshare</parent>
      <child>vehicles_elegy_interior</child>
    </Item>
    <Item>
      <parent>vehshare</parent>
      <child>vehicles_feltzer_interior</child>
    </Item>
    <Item>
      <parent>vehshare</parent>
      <child>vehicles_bodhi_interior</child>
    </Item>
    <Item>
      <parent>vehshare</parent>
      <child>vehicles_peyote_interior</child>
    </Item>
    <Item>
      <parent>vehshare</parent>
      <child>vehicles_voodoo_interior</child>
    </Item>
    <Item>
      <parent>vehshare</parent>
      <child>vehicles_sportbk_interior</child>
    </Item>
    <Item>
      <parent>vehshare</parent>
      <child>vehicles_vacca_interior</child>
    </Item>
    <Item>
      <parent>vehshare_truck</parent>
      <child>vehicles_worn_van</child>
    </Item>
    <Item>
      <parent>vehshare_truck</parent>
      <child>vehicles_boat_interior</child>
    </Item>
    <Item>
      <parent>vehshare</parent>
      <child>vehicles_flyer_interior</child>
    </Item>
    <Item>
      <parent>vehshare</parent>
      <child>vehicles_wornflyer_interior</child>
    </Item>
    <Item>
      <parent>vehshare</parent>
      <child>vehicles_chopperbk_interior</child>
    </Item>
    <Item>
      <parent>vehshare</parent>
      <child>vehicles_worn_army_flyer_interior</child>
    </Item>
    <Item>
      <parent>vehshare</parent>
      <child>vehicles_stinger_interior</child>
    </Item>
    <Item>
      <parent>vehshare</parent>
      <child>vehicles_monroe_interior</child>
    </Item>
    <Item>
      <parent>vehicles_wornflyer_interior</parent>
      <child>vehicles_jet_interior</child>
    </Item>
    <Item>
      <parent>vehshare</parent>
      <child>vehicles_cav_interior</child>
    </Item>
    <Item>
      <parent>vehshare</parent>
      <child>vehicles_supergt_interior</child>
    </Item>
    <Item>
      <parent>vehshare</parent>
      <child>vehicles_bob_interior</child>
    </Item>
    <Item>
      <parent>vehshare</parent>
      <child>vehicles_feroci_interior</child>
    </Item>
    <Item>
      <parent>vehshare</parent>
      <child>vehicles_coquette_interior</child>
    </Item>
    <Item>
      <parent>vehshare</parent>
      <child>vehicles_bob_brown_interior</child>
    </Item>
    <Item>
      <parent>vehshare</parent>
      <child>vehicles_sultan_interior</child>
    </Item>
    <Item>
      <parent>vehshare</parent>
      <child>vehicles_tornado_interior</child>
    </Item>
    <Item>
      <parent>vehshare</parent>
      <child>vehicles_banshee_interior</child>
    </Item>
    <Item>
      <parent>vehshare</parent>
      <child>vehicles_speedo_interior</child>
    </Item>
    <Item>
      <parent>vehshare_worn</parent>
      <child>vehicles_speedo2_interior</child>
    </Item>
    <Item>
      <parent>vehshare_army</parent>
      <child>vehicles_cavarmy_interior</child>
    </Item>
    <Item>
      <parent>vehshare</parent>
      <child>vehicles_schaf_interior</child>
    </Item>
    <Item>
      <parent>vehshare</parent>
      <child>vehicles_schaf_brown_interior</child>
    </Item>
    <Item>
      <parent>vehshare</parent>
      <child>vehicles_poltax_interior</child>
    </Item>
    <Item>
      <parent>vehshare</parent>
      <child>vehicles_dom_interior</child>
    </Item>
    <Item>
      <parent>vehshare_worn</parent>
      <child>caracara2tow</child>
    </Item>
    <Item>
      <parent>vehicles_cav_interior</parent>
      <child>caracara2tow</child>
    </Item>
    <Item>
      <parent>vehshare_worn</parent>
      <child>towflat</child>
    </Item>
    <Item>
      <parent>vehshare_truck</parent>
      <child>towflat</child>
    </Item>
    <Item>
      <parent>vehicles_biker_shared</parent>
      <child>chimerac</child>
    </Item>
    <Item>
      <parent>vehshare</parent>
      <child>vehicles_remustwo_interior</child>
    </Item>
    <Item>
      <parent>vehicles_remustwo_interior</parent>
      <child>polremustwo</child>
    </Item>
    <Item>
      <parent>vehicles_biker_shared</parent>
      <child>saltflat</child>
    </Item>
    <Item>
      <parent>vehicles_biker_shared</parent>
      <child>shinobio</child>
    </Item>
    <Item>
      <parent>vehicles_biker_shared</parent>
      <child>sombrero</child>
    </Item>
    <Item>
      <parent>vehicles_van_interior</parent>
      <child>taco2</child>
    </Item>
    <Item>
      <parent>vehshare_truck</parent>
      <child>mssdtr1</child>
    </Item>
    <Item>
      <parent>hauler</parent>
      <child>mssdtr1</child>
    </Item>
    <Item>
      <parent>vehshare_worn</parent>
      <child>ventoso</child>
    </Item>
    <Item>
      <parent>vehicles_blista2_interior</parent>
      <child>foodcar</child>
    </Item>
    <Item>
      <parent>vehicles_schaf_interior</parent>
      <child>foodcar2</child>
    </Item>
    <Item>
      <parent>vehicles_poltax_interior</parent>
      <child>foodcar3</child>
    </Item>
    <Item>
      <parent>vehicles_cav_interior</parent>
      <child>foodcar4</child>
    </Item>
    <Item>
      <parent>vehicles_schaf_interior</parent>
      <child>foodcar5</child>
    </Item>
    <Item>
      <parent>vehicles_bob_interior</parent>
      <child>foodcar6</child>
    </Item>
    <Item>
      <parent>vehicles_futo_interior</parent>
      <child>foodcar7</child>
    </Item>
    <Item>
      <parent>vehicles_biker_shared</parent>
      <child>foodbike</child>
    </Item>
    <Item>
      <parent>vehicles_chopperbk_interior</parent>
      <child>foodbike2</child>
    </Item>
    <Item>
      <parent>vehshare_truck</parent>
      <child>foxutillitruck</child>
    </Item>
    <Item>
      <parent>vehicles_biker_shared</parent>
      <child>lpchopper</child>
    </Item>
    <Item>
      <parent>vehshare_truck</parent>
      <child>trailers2a</child>
    </Item>
    <Item>
      <parent>vehshare_army</parent>
      <child>watertanker</child>
    </Item>
    <Item>
      <parent>vehshare</parent>
      <child>vehshare_truck</child>
    </Item>
    <Item>
      <parent>vehshare_truck</parent>
      <child>vehshare_worn</child>
    </Item>
    <Item>
      <parent>vehshare_truck</parent>
      <child>vehshare_army</child>
    </Item>
    <Item>
      <parent>vehshare</parent>
      <child>vehicles_blista2_interior</child>
    </Item>
    <Item>
      <parent>vehshare</parent>
      <child>vehicles_futo_interior</child>
    </Item>
    <Item>
      <parent>vehshare</parent>
      <child>vehicles_monster_interior</child>
    </Item>
    <Item>
      <parent>vehshare</parent>
      <child>vehicles_journey_interior</child>
    </Item>
    <Item>
      <parent>vehshare_truck</parent>
      <child>vehicles_van_interior</child>
    </Item>
    <Item>
      <parent>vehshare</parent>
      <child>vehicles_elegy_interior</child>
    </Item>
    <Item>
      <parent>vehshare</parent>
      <child>vehicles_feltzer_interior</child>
    </Item>
    <Item>
      <parent>vehshare</parent>
      <child>vehicles_bodhi_interior</child>
    </Item>
    <Item>
      <parent>vehshare</parent>
      <child>vehicles_peyote_interior</child>
    </Item>
    <Item>
      <parent>vehshare</parent>
      <child>vehicles_voodoo_interior</child>
    </Item>
    <Item>
      <parent>vehshare</parent>
      <child>vehicles_sportbk_interior</child>
    </Item>
    <Item>
      <parent>vehshare</parent>
      <child>vehicles_vacca_interior</child>
    </Item>
    <Item>
      <parent>vehshare_truck</parent>
      <child>vehicles_worn_van</child>
    </Item>
    <Item>
      <parent>vehshare_truck</parent>
      <child>vehicles_boat_interior</child>
    </Item>
    <Item>
      <parent>vehshare</parent>
      <child>vehicles_flyer_interior</child>
    </Item>
    <Item>
      <parent>vehshare</parent>
      <child>vehicles_wornflyer_interior</child>
    </Item>
    <Item>
      <parent>vehshare</parent>
      <child>vehicles_chopperbk_interior</child>
    </Item>
    <Item>
      <parent>vehshare</parent>
      <child>vehicles_worn_army_flyer_interior</child>
    </Item>
    <Item>
      <parent>vehshare</parent>
      <child>vehicles_stinger_interior</child>
    </Item>
    <Item>
      <parent>vehshare</parent>
      <child>vehicles_monroe_interior</child>
    </Item>
    <Item>
      <parent>vehicles_wornflyer_interior</parent>
      <child>vehicles_jet_interior</child>
    </Item>
    <Item>
      <parent>vehshare</parent>
      <child>vehicles_cav_interior</child>
    </Item>
    <Item>
      <parent>vehshare</parent>
      <child>vehicles_supergt_interior</child>
    </Item>
    <Item>
      <parent>vehshare</parent>
      <child>vehicles_bob_interior</child>
    </Item>
    <Item>
      <parent>vehshare</parent>
      <child>vehicles_feroci_interior</child>
    </Item>
    <Item>
      <parent>vehshare</parent>
      <child>vehicles_coquette_interior</child>
    </Item>
    <Item>
      <parent>vehshare</parent>
      <child>vehicles_bob_brown_interior</child>
    </Item>
    <Item>
      <parent>vehshare</parent>
      <child>vehicles_sultan_interior</child>
    </Item>
    <Item>
      <parent>vehshare</parent>
      <child>vehicles_tornado_interior</child>
    </Item>
    <Item>
      <parent>vehshare</parent>
      <child>vehicles_banshee_interior</child>
    </Item>
    <Item>
      <parent>vehshare</parent>
      <child>vehicles_speedo_interior</child>
    </Item>
    <Item>
      <parent>vehshare_worn</parent>
      <child>vehicles_speedo2_interior</child>
    </Item>
    <Item>
      <parent>vehshare_army</parent>
      <child>vehicles_cavarmy_interior</child>
    </Item>
    <Item>
      <parent>vehshare</parent>
      <child>vehicles_schaf_interior</child>
    </Item>
    <Item>
      <parent>vehshare</parent>
      <child>vehicles_schaf_brown_interior</child>
    </Item>
    <Item>
      <parent>vehshare</parent>
      <child>vehicles_poltax_interior</child>
    </Item>
    <Item>
      <parent>vehshare</parent>
      <child>vehicles_dom_interior</child>
    </Item>
    <Item>
      <parent>vehicles_worn_van</parent>
      <child>utillitruck3</child>
    </Item>
    <Item>
      <parent>vehicles_van_interior</parent>
      <child>utilliflatbed</child>
    </Item>
    <Item>
      <parent>vehicles_worn_van</parent>
      <child>utillitruck</child>
    </Item>
    <Item>
      <parent>vehshare_truck</parent>
      <child>utilliflatbed</child>
    </Item>
    <Item>
      <parent>flatbed</parent>
      <child>utilliflatbed</child>
    </Item>
    <Item>
      <parent>vehshare_truck</parent>
      <child>pdgystockade</child>
    </Item>
    <Item>
      <parent>vehicles_poltax_interior</parent>
      <child>polimpaler</child>
    </Item>
  </txdRelationships>
</CVehicleModelInfo__InitDataList>

