<?xml version="1.0" encoding="UTF-8"?>
<CVehicleMetadataMgr>
  <ClipSetMaps>
    <Item type="CClipSetMap">
      <Name>ENTRY_CLIPSET_MAP_ELEGYRHD_STD_FRONT_RIGHT</Name>
      <Maps>
        <Item>
          <ClipSetId>clipset@veh@std@ps@enter_exit</ClipSetId>
          <VarClipSetId>EntryVarClipSet</VarClipSetId>
          <ConditionFlags/>
          <InformationFlags/>
        </Item>
        <Item>
          <ClipSetId>clipset@veh@break_in@0h@p_m_zero@</ClipSetId>
          <VarClipSetId>EntryVarClipSet</VarClipSetId>
          <ConditionFlags>CF_BreakInAnims</ConditionFlags>
          <InformationFlags/>
        </Item>
        <Item>
          <ClipSetId>clipset@veh@break_in@0h@p_m_one@</ClipSetId>
          <VarClipSetId>EntryVarClipSet</VarClipSetId>
          <ConditionFlags>CF_BreakInAnims</ConditionFlags>
          <InformationFlags/>
        </Item>
        <Item>
          <ClipSetId>clipset@veh@break_in@0h@p_m_two@</ClipSetId>
          <VarClipSetId>EntryVarClipSet</VarClipSetId>
          <ConditionFlags>CF_BreakInAnims</ConditionFlags>
          <InformationFlags/>
        </Item>
        <Item>
          <ClipSetId>clipset@veh@jacking@0h@p_m_zero@</ClipSetId>
          <VarClipSetId>EntryVarClipSet</VarClipSetId>
          <ConditionFlags>CF_JackingAnims</ConditionFlags>
          <InformationFlags/>
        </Item>
        <Item>
          <ClipSetId>clipset@veh@jacking@0h@p_m_one@</ClipSetId>
          <VarClipSetId>EntryVarClipSet</VarClipSetId>
          <ConditionFlags>CF_JackingAnims</ConditionFlags>
          <InformationFlags/>
        </Item>
        <Item>
          <ClipSetId>clipset@veh@jacking@0h@p_m_two@</ClipSetId>
          <VarClipSetId>EntryVarClipSet</VarClipSetId>
          <ConditionFlags>CF_JackingAnims</ConditionFlags>
          <InformationFlags/>
        </Item>
      </Maps>
    </Item>
    <Item type="CClipSetMap">
      <Name>ENTRY_CLIPSET_MAP_ELEGYRHD_STD_FRONT_LEFT</Name>
      <Maps>
        <Item>
          <ClipSetId>clipset@veh@std@rds@enter_exit</ClipSetId>
          <VarClipSetId>EntryVarClipSet</VarClipSetId>
          <ConditionFlags/>
          <InformationFlags/>
        </Item>
        <Item>
          <ClipSetId>clipset@veh@break_in@0h@p_m_zero@</ClipSetId>
          <VarClipSetId>EntryVarClipSet</VarClipSetId>
          <ConditionFlags>CF_BreakInAnims</ConditionFlags>
          <InformationFlags/>
        </Item>
        <Item>
          <ClipSetId>clipset@veh@break_in@0h@p_m_one@</ClipSetId>
          <VarClipSetId>EntryVarClipSet</VarClipSetId>
          <ConditionFlags>CF_BreakInAnims</ConditionFlags>
          <InformationFlags/>
        </Item>
        <Item>
          <ClipSetId>clipset@veh@break_in@0h@p_m_two@</ClipSetId>
          <VarClipSetId>EntryVarClipSet</VarClipSetId>
          <ConditionFlags>CF_BreakInAnims</ConditionFlags>
          <InformationFlags/>
        </Item>
        <Item>
          <ClipSetId>clipset@veh@jacking@0h@p_m_zero@</ClipSetId>
          <VarClipSetId>EntryVarClipSet</VarClipSetId>
          <ConditionFlags>CF_JackingAnims</ConditionFlags>
          <InformationFlags>IF_JackedPedExitsWillingly</InformationFlags>
        </Item>
        <Item>
          <ClipSetId>clipset@veh@jacking@0h@p_m_one@</ClipSetId>
          <VarClipSetId>EntryVarClipSet</VarClipSetId>
          <ConditionFlags>CF_JackingAnims</ConditionFlags>
          <InformationFlags/>
        </Item>
        <Item>
          <ClipSetId>clipset@veh@jacking@0h@p_m_two@</ClipSetId>
          <VarClipSetId>EntryVarClipSet</VarClipSetId>
          <ConditionFlags>CF_JackingAnims</ConditionFlags>
          <InformationFlags/>
        </Item>
      </Maps>
    </Item>
    <Item type="CClipSetMap">
      <Name>EXIT_CLIPSET_MAP_ELEGYRHD_STD_FRONT_RIGHT</Name>
      <Maps>
        <Item>
          <ClipSetId>clipset@veh@std@ps@enter_exit</ClipSetId>
          <VarClipSetId>ExitVarClipSet</VarClipSetId>
          <ConditionFlags/>
          <InformationFlags/>
        </Item>
      </Maps>
    </Item>
    <Item type="CClipSetMap">
      <Name>EXIT_CLIPSET_MAP_ELEGYRHD_STD_FRONT_LEFT</Name>
      <Maps>
        <Item>
          <ClipSetId>clipset@veh@std@rds@enter_exit</ClipSetId>
          <VarClipSetId>ExitVarClipSet</VarClipSetId>
          <ConditionFlags/>
          <InformationFlags/>
        </Item>
      </Maps>
    </Item>
    <Item type="CClipSetMap">
      <Name>ENTRY_CLIPSET_MAP_ELEGYIMPORT_STD_FRONT_RIGHT</Name>
      <Maps>
        <Item>
          <ClipSetId>clipset@veh@std@ps@enter_exit</ClipSetId>
          <VarClipSetId>EntryVarClipSet</VarClipSetId>
          <ConditionFlags/>
          <InformationFlags/>
        </Item>
        <Item>
          <ClipSetId>clipset@veh@break_in@0h@p_m_zero@</ClipSetId>
          <VarClipSetId>EntryVarClipSet</VarClipSetId>
          <ConditionFlags>CF_BreakInAnims</ConditionFlags>
          <InformationFlags/>
        </Item>
        <Item>
          <ClipSetId>clipset@veh@break_in@0h@p_m_one@</ClipSetId>
          <VarClipSetId>EntryVarClipSet</VarClipSetId>
          <ConditionFlags>CF_BreakInAnims</ConditionFlags>
          <InformationFlags/>
        </Item>
        <Item>
          <ClipSetId>clipset@veh@break_in@0h@p_m_two@</ClipSetId>
          <VarClipSetId>EntryVarClipSet</VarClipSetId>
          <ConditionFlags>CF_BreakInAnims</ConditionFlags>
          <InformationFlags/>
        </Item>
        <Item>
          <ClipSetId>clipset@veh@jacking@0h@p_m_zero@</ClipSetId>
          <VarClipSetId>EntryVarClipSet</VarClipSetId>
          <ConditionFlags>CF_JackingAnims</ConditionFlags>
          <InformationFlags/>
        </Item>
        <Item>
          <ClipSetId>clipset@veh@jacking@0h@p_m_one@</ClipSetId>
          <VarClipSetId>EntryVarClipSet</VarClipSetId>
          <ConditionFlags>CF_JackingAnims</ConditionFlags>
          <InformationFlags/>
        </Item>
        <Item>
          <ClipSetId>clipset@veh@jacking@0h@p_m_two@</ClipSetId>
          <VarClipSetId>EntryVarClipSet</VarClipSetId>
          <ConditionFlags>CF_JackingAnims</ConditionFlags>
          <InformationFlags/>
        </Item>
      </Maps>
    </Item>
    <Item type="CClipSetMap">
      <Name>ENTRY_CLIPSET_MAP_ELEGYIMPORT_STD_FRONT_LEFT</Name>
      <Maps>
        <Item>
          <ClipSetId>clipset@veh@std@rds@enter_exit</ClipSetId>
          <VarClipSetId>EntryVarClipSet</VarClipSetId>
          <ConditionFlags/>
          <InformationFlags/>
        </Item>
        <Item>
          <ClipSetId>clipset@veh@break_in@0h@p_m_zero@</ClipSetId>
          <VarClipSetId>EntryVarClipSet</VarClipSetId>
          <ConditionFlags>CF_BreakInAnims</ConditionFlags>
          <InformationFlags/>
        </Item>
        <Item>
          <ClipSetId>clipset@veh@break_in@0h@p_m_one@</ClipSetId>
          <VarClipSetId>EntryVarClipSet</VarClipSetId>
          <ConditionFlags>CF_BreakInAnims</ConditionFlags>
          <InformationFlags/>
        </Item>
        <Item>
          <ClipSetId>clipset@veh@break_in@0h@p_m_two@</ClipSetId>
          <VarClipSetId>EntryVarClipSet</VarClipSetId>
          <ConditionFlags>CF_BreakInAnims</ConditionFlags>
          <InformationFlags/>
        </Item>
        <Item>
          <ClipSetId>clipset@veh@jacking@0h@p_m_zero@</ClipSetId>
          <VarClipSetId>EntryVarClipSet</VarClipSetId>
          <ConditionFlags>CF_JackingAnims</ConditionFlags>
          <InformationFlags>IF_JackedPedExitsWillingly</InformationFlags>
        </Item>
        <Item>
          <ClipSetId>clipset@veh@jacking@0h@p_m_one@</ClipSetId>
          <VarClipSetId>EntryVarClipSet</VarClipSetId>
          <ConditionFlags>CF_JackingAnims</ConditionFlags>
          <InformationFlags/>
        </Item>
        <Item>
          <ClipSetId>clipset@veh@jacking@0h@p_m_two@</ClipSetId>
          <VarClipSetId>EntryVarClipSet</VarClipSetId>
          <ConditionFlags>CF_JackingAnims</ConditionFlags>
          <InformationFlags/>
        </Item>
      </Maps>
    </Item>
    <Item type="CClipSetMap">
      <Name>EXIT_CLIPSET_MAP_ELEGYIMPORT_STD_FRONT_RIGHT</Name>
      <Maps>
        <Item>
          <ClipSetId>clipset@veh@std@ps@enter_exit</ClipSetId>
          <VarClipSetId>ExitVarClipSet</VarClipSetId>
          <ConditionFlags/>
          <InformationFlags/>
        </Item>
      </Maps>
    </Item>
    <Item type="CClipSetMap">
      <Name>EXIT_CLIPSET_MAP_ELEGYIMPORT_STD_FRONT_LEFT</Name>
      <Maps>
        <Item>
          <ClipSetId>clipset@veh@std@rds@enter_exit</ClipSetId>
          <VarClipSetId>ExitVarClipSet</VarClipSetId>
          <ConditionFlags/>
          <InformationFlags/>
        </Item>
      </Maps>
    </Item>
  </ClipSetMaps>
  <VehicleDriveByInfos>
    <Item type="CVehicleDriveByInfo">
      <Name>DRIVEBY_ELEGYRHD_LOW_TIGHT_FRONT_RIGHT</Name>
      <MinAimSweepHeadingAngleDegs value="-190.000000"/>
      <MaxAimSweepHeadingAngleDegs value="190.000000"/>
      <FirstPersonMinAimSweepHeadingAngleDegs value="-180.000000"/>
      <FirstPersonMaxAimSweepHeadingAngleDegs value="180.000000"/>
      <FirstPersonUnarmedMinAimSweepHeadingAngleDegs value="-180.000000"/>
      <FirstPersonUnarmedMaxAimSweepHeadingAngleDegs value="180.000000"/>
      <MinRestrictedAimSweepHeadingAngleDegs value="-190.000000"/>
      <MaxRestrictedAimSweepHeadingAngleDegs value="190.000000"/>
      <MinSmashWindowAngleDegs value="0.000000"/>
      <MaxSmashWindowAngleDegs value="180.000000"/>
      <MinSmashWindowAngleFirstPersonDegs value="25.000000"/>
      <MaxSmashWindowAngleFirstPersonDegs value="180.000000"/>
      <MaxSpeedParam value="0.500000"/>
      <MaxLongitudinalLeanBlendWeightDelta value="0.250000"/>
      <MaxLateralLeanBlendWeightDelta value="0.250000"/>
      <ApproachSpeedToWithinMaxBlendDelta value="1.000000"/>
      <SpineAdditiveBlendInDelay value="0.050000"/>
      <SpineAdditiveBlendInDurationStill value="0.150000"/>
      <SpineAdditiveBlendInDuration value="0.750000"/>
      <SpineAdditiveBlendOutDelay value="0.000000"/>
      <SpineAdditiveBlendOutDuration value="0.500000"/>
      <MinUnarmedDrivebyYawIfWindowRolledUp value="-3.140000"/>
      <MaxUnarmedDrivebyYawIfWindowRolledUp value="0.800000"/>
      <DriveByAnimInfos>
        <Item ref="ELEGYRHD_LOW_DB_ANIM_INFO_UNARMED_DS"/>
        <Item ref="ELEGYRHD_LOW_DB_ANIM_INFO_ONE_HANDED_DS"/>
        <Item ref="ELEGYRHD_LOW_DB_ANIM_INFO_THROW_DS"/>
      </DriveByAnimInfos>
      <DriveByCamera/>
      <DriveByFlags>UseMountedProjectileTask LeftHandedUnarmedFirstPersonAnims</DriveByFlags>
    </Item>
    <Item type="CVehicleDriveByInfo">
      <Name>DRIVEBY_ELEGYRHD_LOW_TIGHT_FRONT_LEFT</Name>
      <MinAimSweepHeadingAngleDegs value="-190.000000"/>
      <MaxAimSweepHeadingAngleDegs value="190.000000"/>
      <FirstPersonMinAimSweepHeadingAngleDegs value="-180.000000"/>
      <FirstPersonMaxAimSweepHeadingAngleDegs value="180.000000"/>
      <FirstPersonUnarmedMinAimSweepHeadingAngleDegs value="-180.000000"/>
      <FirstPersonUnarmedMaxAimSweepHeadingAngleDegs value="180.000000"/>
      <MinRestrictedAimSweepHeadingAngleDegs value="-190.000000"/>
      <MaxRestrictedAimSweepHeadingAngleDegs value="190.000000"/>
      <MinSmashWindowAngleDegs value="-180.000000"/>
      <MaxSmashWindowAngleDegs value="0.000000"/>
      <MinSmashWindowAngleFirstPersonDegs value="-180.000000"/>
      <MaxSmashWindowAngleFirstPersonDegs value="-25.000000"/>
      <MaxSpeedParam value="0.500000"/>
      <MaxLongitudinalLeanBlendWeightDelta value="0.250000"/>
      <MaxLateralLeanBlendWeightDelta value="0.250000"/>
      <ApproachSpeedToWithinMaxBlendDelta value="1.000000"/>
      <SpineAdditiveBlendInDelay value="0.050000"/>
      <SpineAdditiveBlendInDurationStill value="0.150000"/>
      <SpineAdditiveBlendInDuration value="0.750000"/>
      <SpineAdditiveBlendOutDelay value="0.000000"/>
      <SpineAdditiveBlendOutDuration value="0.500000"/>
      <MinUnarmedDrivebyYawIfWindowRolledUp value="-0.800000"/>
      <MaxUnarmedDrivebyYawIfWindowRolledUp value="3.140000"/>
      <DriveByAnimInfos>
        <Item ref="ELEGYRHD_LOW_DB_ANIM_INFO_UNARMED_PS"/>
        <Item ref="ELEGYRHD_LOW_DB_ANIM_INFO_ONE_HANDED_PS"/>
        <Item ref="ELEGYRHD_LOW_DB_ANIM_INFO_THROW_PS"/>
      </DriveByAnimInfos>
      <DriveByCamera/>
      <DriveByFlags>UseMountedProjectileTask LeftHandedProjctiles LeftHandedUnarmedFirstPersonAnims</DriveByFlags>
    </Item>
    <Item type="CVehicleDriveByInfo">
      <Name>DRIVEBY_ELEGYIMPORT_LOW_TIGHT_FRONT_RIGHT</Name>
      <MinAimSweepHeadingAngleDegs value="-190.000000"/>
      <MaxAimSweepHeadingAngleDegs value="190.000000"/>
      <FirstPersonMinAimSweepHeadingAngleDegs value="-180.000000"/>
      <FirstPersonMaxAimSweepHeadingAngleDegs value="180.000000"/>
      <FirstPersonUnarmedMinAimSweepHeadingAngleDegs value="-180.000000"/>
      <FirstPersonUnarmedMaxAimSweepHeadingAngleDegs value="180.000000"/>
      <MinRestrictedAimSweepHeadingAngleDegs value="-190.000000"/>
      <MaxRestrictedAimSweepHeadingAngleDegs value="190.000000"/>
      <MinSmashWindowAngleDegs value="0.000000"/>
      <MaxSmashWindowAngleDegs value="180.000000"/>
      <MinSmashWindowAngleFirstPersonDegs value="25.000000"/>
      <MaxSmashWindowAngleFirstPersonDegs value="180.000000"/>
      <MaxSpeedParam value="0.500000"/>
      <MaxLongitudinalLeanBlendWeightDelta value="0.250000"/>
      <MaxLateralLeanBlendWeightDelta value="0.250000"/>
      <ApproachSpeedToWithinMaxBlendDelta value="1.000000"/>
      <SpineAdditiveBlendInDelay value="0.050000"/>
      <SpineAdditiveBlendInDurationStill value="0.150000"/>
      <SpineAdditiveBlendInDuration value="0.750000"/>
      <SpineAdditiveBlendOutDelay value="0.000000"/>
      <SpineAdditiveBlendOutDuration value="0.500000"/>
      <MinUnarmedDrivebyYawIfWindowRolledUp value="-3.140000"/>
      <MaxUnarmedDrivebyYawIfWindowRolledUp value="0.800000"/>
      <DriveByAnimInfos>
        <Item ref="ELEGYIMPORT_LOW_DB_ANIM_INFO_UNARMED_DS"/>
        <Item ref="ELEGYIMPORT_LOW_DB_ANIM_INFO_ONE_HANDED_DS"/>
        <Item ref="ELEGYIMPORT_LOW_DB_ANIM_INFO_THROW_DS"/>
      </DriveByAnimInfos>
      <DriveByCamera/>
      <DriveByFlags>UseMountedProjectileTask LeftHandedUnarmedFirstPersonAnims</DriveByFlags>
    </Item>
    <Item type="CVehicleDriveByInfo">
      <Name>DRIVEBY_ELEGYIMPORT_LOW_TIGHT_FRONT_LEFT</Name>
      <MinAimSweepHeadingAngleDegs value="-190.000000"/>
      <MaxAimSweepHeadingAngleDegs value="190.000000"/>
      <FirstPersonMinAimSweepHeadingAngleDegs value="-180.000000"/>
      <FirstPersonMaxAimSweepHeadingAngleDegs value="180.000000"/>
      <FirstPersonUnarmedMinAimSweepHeadingAngleDegs value="-180.000000"/>
      <FirstPersonUnarmedMaxAimSweepHeadingAngleDegs value="180.000000"/>
      <MinRestrictedAimSweepHeadingAngleDegs value="-190.000000"/>
      <MaxRestrictedAimSweepHeadingAngleDegs value="190.000000"/>
      <MinSmashWindowAngleDegs value="-180.000000"/>
      <MaxSmashWindowAngleDegs value="0.000000"/>
      <MinSmashWindowAngleFirstPersonDegs value="-180.000000"/>
      <MaxSmashWindowAngleFirstPersonDegs value="-25.000000"/>
      <MaxSpeedParam value="0.500000"/>
      <MaxLongitudinalLeanBlendWeightDelta value="0.250000"/>
      <MaxLateralLeanBlendWeightDelta value="0.250000"/>
      <ApproachSpeedToWithinMaxBlendDelta value="1.000000"/>
      <SpineAdditiveBlendInDelay value="0.050000"/>
      <SpineAdditiveBlendInDurationStill value="0.150000"/>
      <SpineAdditiveBlendInDuration value="0.750000"/>
      <SpineAdditiveBlendOutDelay value="0.000000"/>
      <SpineAdditiveBlendOutDuration value="0.500000"/>
      <MinUnarmedDrivebyYawIfWindowRolledUp value="-0.800000"/>
      <MaxUnarmedDrivebyYawIfWindowRolledUp value="3.140000"/>
      <DriveByAnimInfos>
        <Item ref="ELEGYIMPORT_LOW_DB_ANIM_INFO_UNARMED_PS"/>
        <Item ref="ELEGYIMPORT_LOW_DB_ANIM_INFO_ONE_HANDED_PS"/>
        <Item ref="ELEGYIMPORT_LOW_DB_ANIM_INFO_THROW_PS"/>
      </DriveByAnimInfos>
      <DriveByCamera/>
      <DriveByFlags>UseMountedProjectileTask LeftHandedProjctiles LeftHandedUnarmedFirstPersonAnims</DriveByFlags>
    </Item>
  </VehicleDriveByInfos>
  <VehicleDriveByAnimInfos>
    <Item type="CVehicleDriveByAnimInfo">
      <Name>ELEGYRHD_LOW_DB_ANIM_INFO_UNARMED_PS</Name>
      <WeaponGroup ref="DRIVEBY_DEFAULT_UNARMED"/>
      <DriveByClipSet>drive_by@std_rds_unarmed</DriveByClipSet>
      <FirstPersonDriveByClipSet>driveby@first_person@passenger_right_handed@unarmed</FirstPersonDriveByClipSet>
      <AltFirstPersonDriveByClipSets/>
      <Network>STD_CAR_DRIVEBY</Network>
    </Item>
    <Item type="CVehicleDriveByAnimInfo">
      <Name>ELEGYRHD_LOW_DB_ANIM_INFO_ONE_HANDED_PS</Name>
      <WeaponGroup ref="DRIVEBY_DEFAULT_ONE_HANDED"/>
      <DriveByClipSet>drive_by@std_rds</DriveByClipSet>
      <FirstPersonDriveByClipSet>driveby@first_person@passenger_right_handed@1h</FirstPersonDriveByClipSet>
      <AltFirstPersonDriveByClipSets>
        <Item>
          <Weapons>
            <Item>WEAPON_MICROSMG</Item>
          </Weapons>
          <ClipSet>driveby@first_person@passenger_right_handed@smg</ClipSet>
        </Item>
      </AltFirstPersonDriveByClipSets>
      <Network>STD_CAR_DRIVEBY</Network>
    </Item>
    <Item type="CVehicleDriveByAnimInfo">
      <Name>ELEGYRHD_LOW_DB_ANIM_INFO_THROW_PS</Name>
      <WeaponGroup ref="DRIVEBY_THROW"/>
      <DriveByClipSet>drive_by@std_rds_grenades</DriveByClipSet>
      <FirstPersonDriveByClipSet>driveby@first_person@driver@throw</FirstPersonDriveByClipSet>
      <AltFirstPersonDriveByClipSets/>
      <Network>MOUNTED_THROW</Network>
    </Item>
    <Item type="CVehicleDriveByAnimInfo">
      <Name>ELEGYRHD_LOW_DB_ANIM_INFO_UNARMED_DS</Name>
      <WeaponGroup ref="DRIVEBY_DEFAULT_UNARMED"/>
      <DriveByClipSet>drive_by@low_ps_unarmed</DriveByClipSet>
      <FirstPersonDriveByClipSet>driveby@first_person@driver@unarmed</FirstPersonDriveByClipSet>
      <AltFirstPersonDriveByClipSets/>
      <Network>STD_CAR_DRIVEBY</Network>
    </Item>
    <Item type="CVehicleDriveByAnimInfo">
      <Name>ELEGYRHD_LOW_DB_ANIM_INFO_ONE_HANDED_DS</Name>
      <WeaponGroup ref="DRIVEBY_DEFAULT_ONE_HANDED"/>
      <DriveByClipSet>drive_by@low_ps</DriveByClipSet>
      <FirstPersonDriveByClipSet>driveby@first_person@passenger_left_handed@1h</FirstPersonDriveByClipSet>
      <AltFirstPersonDriveByClipSets>
        <Item>
          <Weapons>
            <Item>WEAPON_MICROSMG</Item>
          </Weapons>
          <ClipSet>driveby@first_person@passenger_right_handed@smg</ClipSet>
        </Item>
      </AltFirstPersonDriveByClipSets>
      <Network>STD_CAR_DRIVEBY</Network>
    </Item>
    <Item type="CVehicleDriveByAnimInfo">
      <Name>ELEGYRHD_LOW_DB_ANIM_INFO_THROW_DS</Name>
      <WeaponGroup ref="DRIVEBY_THROW"/>
      <DriveByClipSet>drive_by@low_ps_grenades</DriveByClipSet>
      <FirstPersonDriveByClipSet>driveby@first_person@passenger_right_handed@throw</FirstPersonDriveByClipSet>
      <AltFirstPersonDriveByClipSets/>
      <Network>MOUNTED_THROW</Network>
    </Item>
    <Item type="CVehicleDriveByAnimInfo">
      <Name>ELEGYIMPORT_LOW_DB_ANIM_INFO_UNARMED_PS</Name>
      <WeaponGroup ref="DRIVEBY_DEFAULT_UNARMED"/>
      <DriveByClipSet>drive_by@std_rds_unarmed</DriveByClipSet>
      <FirstPersonDriveByClipSet>driveby@first_person@passenger_right_handed@unarmed</FirstPersonDriveByClipSet>
      <AltFirstPersonDriveByClipSets/>
      <Network>STD_CAR_DRIVEBY</Network>
    </Item>
    <Item type="CVehicleDriveByAnimInfo">
      <Name>ELEGYIMPORT_LOW_DB_ANIM_INFO_ONE_HANDED_PS</Name>
      <WeaponGroup ref="DRIVEBY_DEFAULT_ONE_HANDED"/>
      <DriveByClipSet>drive_by@std_rds</DriveByClipSet>
      <FirstPersonDriveByClipSet>driveby@first_person@passenger_right_handed@1h</FirstPersonDriveByClipSet>
      <AltFirstPersonDriveByClipSets>
        <Item>
          <Weapons>
            <Item>WEAPON_MICROSMG</Item>
          </Weapons>
          <ClipSet>driveby@first_person@passenger_right_handed@smg</ClipSet>
        </Item>
      </AltFirstPersonDriveByClipSets>
      <Network>STD_CAR_DRIVEBY</Network>
    </Item>
    <Item type="CVehicleDriveByAnimInfo">
      <Name>ELEGYIMPORT_LOW_DB_ANIM_INFO_THROW_PS</Name>
      <WeaponGroup ref="DRIVEBY_THROW"/>
      <DriveByClipSet>drive_by@std_rds_grenades</DriveByClipSet>
      <FirstPersonDriveByClipSet>driveby@first_person@driver@throw</FirstPersonDriveByClipSet>
      <AltFirstPersonDriveByClipSets/>
      <Network>MOUNTED_THROW</Network>
    </Item>
    <Item type="CVehicleDriveByAnimInfo">
      <Name>ELEGYIMPORT_LOW_DB_ANIM_INFO_UNARMED_DS</Name>
      <WeaponGroup ref="DRIVEBY_DEFAULT_UNARMED"/>
      <DriveByClipSet>drive_by@low_ps_unarmed</DriveByClipSet>
      <FirstPersonDriveByClipSet>driveby@first_person@driver@unarmed</FirstPersonDriveByClipSet>
      <AltFirstPersonDriveByClipSets/>
      <Network>STD_CAR_DRIVEBY</Network>
    </Item>
    <Item type="CVehicleDriveByAnimInfo">
      <Name>ELEGYIMPORT_LOW_DB_ANIM_INFO_ONE_HANDED_DS</Name>
      <WeaponGroup ref="DRIVEBY_DEFAULT_ONE_HANDED"/>
      <DriveByClipSet>drive_by@low_ps</DriveByClipSet>
      <FirstPersonDriveByClipSet>driveby@first_person@passenger_left_handed@1h</FirstPersonDriveByClipSet>
      <AltFirstPersonDriveByClipSets>
        <Item>
          <Weapons>
            <Item>WEAPON_MICROSMG</Item>
          </Weapons>
          <ClipSet>driveby@first_person@passenger_right_handed@smg</ClipSet>
        </Item>
      </AltFirstPersonDriveByClipSets>
      <Network>STD_CAR_DRIVEBY</Network>
    </Item>
    <Item type="CVehicleDriveByAnimInfo">
      <Name>ELEGYIMPORT_LOW_DB_ANIM_INFO_THROW_DS</Name>
      <WeaponGroup ref="DRIVEBY_THROW"/>
      <DriveByClipSet>drive_by@low_ps_grenades</DriveByClipSet>
      <FirstPersonDriveByClipSet>driveby@first_person@passenger_right_handed@throw</FirstPersonDriveByClipSet>
      <AltFirstPersonDriveByClipSets/>
      <Network>MOUNTED_THROW</Network>
    </Item>
  </VehicleDriveByAnimInfos>
  <VehicleCoverBoundOffsetInfos>
    <Item type="CVehicleCoverBoundOffsetInfo">
      <Name>ELEGYRHD_COVER_OFFSET_INFO</Name>
      <ExtraSideOffset value="-0.050000"/>
      <ExtraForwardOffset value="-0.050000"/>
      <ExtraBackwardOffset value="-0.200000"/>
      <ExtraZOffset value="0.100000"/>
      <CoverBoundInfos/>
    </Item>
    <Item type="CVehicleCoverBoundOffsetInfo">
      <Name>ELEGYIMPORT_COVER_OFFSET_INFO</Name>
      <ExtraSideOffset value="-0.050000"/>
      <ExtraForwardOffset value="-0.050000"/>
      <ExtraBackwardOffset value="-0.200000"/>
      <ExtraZOffset value="0.100000"/>
      <CoverBoundInfos/>
    </Item>
  </VehicleCoverBoundOffsetInfos>
  <VehicleSeatInfos>
    <Item type="CVehicleSeatInfo">
      <Name>SEAT_ELEGYRHD_FRONT_RIGHT</Name>
      <SeatBoneName>seat_dside_f</SeatBoneName>
      <ShuffleLink>SEAT_ELEGYRHD_FRONT_LEFT</ShuffleLink>
      <RearSeatLink/>
      <DefaultCarTask>TASK_DRIVE_WANDER</DefaultCarTask>
      <SeatFlags>IsFrontSeat</SeatFlags>
      <HairScale value="-0.2750000"/>
    </Item>
    <Item type="CVehicleSeatInfo">
      <Name>SEAT_ELEGYRHD_FRONT_LEFT</Name>
      <SeatBoneName>seat_pside_f</SeatBoneName>
      <ShuffleLink>SEAT_ELEGYRHD_FRONT_RIGHT</ShuffleLink>
      <RearSeatLink/>
      <DefaultCarTask>TASK_DRIVE_WANDER</DefaultCarTask>
      <SeatFlags>IsFrontSeat</SeatFlags>
      <HairScale value="-0.2750000"/>
    </Item>
    <Item type="CVehicleSeatInfo">
      <Name>SEAT_ELEGYIMPORT_FRONT_RIGHT</Name>
      <SeatBoneName>seat_dside_f</SeatBoneName>
      <ShuffleLink>SEAT_ELEGYIMPORT_FRONT_LEFT</ShuffleLink>
      <RearSeatLink/>
      <DefaultCarTask>TASK_DRIVE_WANDER</DefaultCarTask>
      <SeatFlags>IsFrontSeat</SeatFlags>
      <HairScale value="-0.2750000"/>
    </Item>
    <Item type="CVehicleSeatInfo">
      <Name>SEAT_ELEGYIMPORT_FRONT_LEFT</Name>
      <SeatBoneName>seat_pside_f</SeatBoneName>
      <ShuffleLink>SEAT_ELEGYIMPORT_FRONT_RIGHT</ShuffleLink>
      <RearSeatLink/>
      <DefaultCarTask>TASK_DRIVE_WANDER</DefaultCarTask>
      <SeatFlags>IsFrontSeat</SeatFlags>
      <HairScale value="-0.2750000"/>
    </Item>
  </VehicleSeatInfos>
  <VehicleSeatAnimInfos>
    <Item type="CVehicleSeatAnimInfo">
      <Name>SEAT_ANIM_ELEGYRHD_FRONT_LEFT</Name>
      <DriveByInfo ref="DRIVEBY_ELEGYRHD_LOW_TIGHT_FRONT_LEFT"/>
      <InsideClipSetMap ref="INSIDE_CLIPSET_MAP_LOW_FRONT_RIGHT"/>
      <PanicClipSet>clipset@veh@low@ps@idle_panic</PanicClipSet>
      <AgitatedClipSet/>
      <DuckedClipSet>clipset@veh@low@ps@idle_duck</DuckedClipSet>
      <LowLODIdleAnim>LOW_CAR_FRONT_PS_IDLE</LowLODIdleAnim>
      <SeatAmbientContext>IN_CAR_LOW</SeatAmbientContext>
      <InVehicleMoveNetwork>VEHICLE_DEFAULT</InVehicleMoveNetwork>
      <SeatAnimFlags>UseStandardInVehicleAnims UseCloseDoorBlendAnims HasPanicAnims PreventShuffleJack</SeatAnimFlags>
      <SteeringSmoothing value="0.040000"/>
      <ExitToAimInfoName>LOW</ExitToAimInfoName>
      <MaleGestureClipSetId>ANIM_GROUP_GESTURE_M_CAR_LOW_PS</MaleGestureClipSetId>
      <FemaleGestureClipSetId>ANIM_GROUP_GESTURE_F_CAR_LOW_PS</FemaleGestureClipSetId>
    </Item>
    <Item type="CVehicleSeatAnimInfo">
      <Name>SEAT_ANIM_ELEGYRHD_FRONT_RIGHT</Name>
      <DriveByInfo ref="DRIVEBY_ELEGYRHD_LOW_TIGHT_FRONT_RIGHT"/>
      <InsideClipSetMap ref="INSIDE_CLIPSET_MAP_LOW_FRONT_LEFT"/>
      <PanicClipSet>clipset@veh@low@ds@idle_panic</PanicClipSet>
      <AgitatedClipSet>clipset@veh@low@ds@idle_agitated</AgitatedClipSet>
      <DuckedClipSet>clipset@veh@low@ds@idle_duck</DuckedClipSet>
      <LowLODIdleAnim>LOW_CAR_FRONT_DS_IDLE</LowLODIdleAnim>
      <SeatAmbientContext>IN_CAR_LOW</SeatAmbientContext>
      <InVehicleMoveNetwork>VEHICLE_DEFAULT</InVehicleMoveNetwork>
      <SeatAnimFlags>UseStandardInVehicleAnims UseCloseDoorBlendAnims WeaponAttachedToLeftHand</SeatAnimFlags>
      <SteeringSmoothing value="0.100000"/>
      <ExitToAimInfoName>LOW</ExitToAimInfoName>
      <MaleGestureClipSetId>ANIM_GROUP_GESTURE_M_CAR_LOW_DS</MaleGestureClipSetId>
      <FemaleGestureClipSetId>ANIM_GROUP_GESTURE_F_CAR_LOW_DS</FemaleGestureClipSetId>
    </Item>
    <Item type="CVehicleSeatAnimInfo">
      <Name>SEAT_ANIM_ELEGYIMPORT_FRONT_LEFT</Name>
      <DriveByInfo ref="DRIVEBY_ELEGYIMPORT_LOW_TIGHT_FRONT_LEFT"/>
      <InsideClipSetMap ref="INSIDE_CLIPSET_MAP_LOW_FRONT_RIGHT"/>
      <PanicClipSet>clipset@veh@low@ps@idle_panic</PanicClipSet>
      <AgitatedClipSet/>
      <DuckedClipSet>clipset@veh@low@ps@idle_duck</DuckedClipSet>
      <LowLODIdleAnim>LOW_CAR_FRONT_PS_IDLE</LowLODIdleAnim>
      <SeatAmbientContext>IN_CAR_LOW</SeatAmbientContext>
      <InVehicleMoveNetwork>VEHICLE_DEFAULT</InVehicleMoveNetwork>
      <SeatAnimFlags>UseStandardInVehicleAnims UseCloseDoorBlendAnims HasPanicAnims PreventShuffleJack</SeatAnimFlags>
      <SteeringSmoothing value="0.040000"/>
      <ExitToAimInfoName>LOW</ExitToAimInfoName>
      <MaleGestureClipSetId>ANIM_GROUP_GESTURE_M_CAR_LOW_PS</MaleGestureClipSetId>
      <FemaleGestureClipSetId>ANIM_GROUP_GESTURE_F_CAR_LOW_PS</FemaleGestureClipSetId>
    </Item>
    <Item type="CVehicleSeatAnimInfo">
      <Name>SEAT_ANIM_ELEGYIMPORT_FRONT_RIGHT</Name>
      <DriveByInfo ref="DRIVEBY_ELEGYIMPORT_LOW_TIGHT_FRONT_RIGHT"/>
      <InsideClipSetMap ref="INSIDE_CLIPSET_MAP_LOW_FRONT_LEFT"/>
      <PanicClipSet>clipset@veh@low@ds@idle_panic</PanicClipSet>
      <AgitatedClipSet>clipset@veh@low@ds@idle_agitated</AgitatedClipSet>
      <DuckedClipSet>clipset@veh@low@ds@idle_duck</DuckedClipSet>
      <LowLODIdleAnim>LOW_CAR_FRONT_DS_IDLE</LowLODIdleAnim>
      <SeatAmbientContext>IN_CAR_LOW</SeatAmbientContext>
      <InVehicleMoveNetwork>VEHICLE_DEFAULT</InVehicleMoveNetwork>
      <SeatAnimFlags>UseStandardInVehicleAnims UseCloseDoorBlendAnims WeaponAttachedToLeftHand</SeatAnimFlags>
      <SteeringSmoothing value="0.100000"/>
      <ExitToAimInfoName>LOW</ExitToAimInfoName>
      <MaleGestureClipSetId>ANIM_GROUP_GESTURE_M_CAR_LOW_DS</MaleGestureClipSetId>
      <FemaleGestureClipSetId>ANIM_GROUP_GESTURE_F_CAR_LOW_DS</FemaleGestureClipSetId>
    </Item>
  </VehicleSeatAnimInfos>
  <VehicleEntryPointInfos>
    <Item type="CVehicleEntryPointInfo">
      <Name>ENTRY_POINT_ELEGYRHD_FRONT_RIGHT</Name>
      <DoorBoneName>door_dside_f</DoorBoneName>
      <SecondDoorBoneName/>
      <DoorHandleBoneName>handle_dside_f</DoorHandleBoneName>
      <WindowId>FRONT_RIGHT</WindowId>
      <VehicleSide>SIDE_RIGHT</VehicleSide>
      <AccessableSeats>
        <Item ref="SEAT_ELEGYRHD_FRONT_RIGHT"/>
      </AccessableSeats>
      <VehicleExtraPointsInfo ref="EXTRA_VEHICLE_POINTS_STD_RIGHT"/>
      <Flags>BlockJackReactionUntilJackerIsReady</Flags>
      <BlockJackReactionSides>
        <Item>SIDE_LEFT</Item>
      </BlockJackReactionSides>
    </Item>
    <Item type="CVehicleEntryPointInfo">
      <Name>ENTRY_POINT_ELEGYRHD_FRONT_LEFT</Name>
      <DoorBoneName>door_pside_f</DoorBoneName>
      <SecondDoorBoneName/>
      <DoorHandleBoneName>handle_pside_f</DoorHandleBoneName>
      <WindowId>FRONT_LEFT</WindowId>
      <VehicleSide>SIDE_LEFT</VehicleSide>
      <AccessableSeats>
        <Item ref="SEAT_ELEGYRHD_FRONT_LEFT"/>
      </AccessableSeats>
      <VehicleExtraPointsInfo ref="EXTRA_VEHICLE_POINTS_STD_LEFT"/>
      <Flags>BlockJackReactionUntilJackerIsReady</Flags>
      <BlockJackReactionSides>
        <Item>SIDE_RIGHT</Item>
      </BlockJackReactionSides>
    </Item>
    <Item type="CVehicleEntryPointInfo">
      <Name>ENTRY_POINT_ELEGYIMPORT_FRONT_RIGHT</Name>
      <DoorBoneName>door_dside_f</DoorBoneName>
      <SecondDoorBoneName/>
      <DoorHandleBoneName>handle_dside_f</DoorHandleBoneName>
      <WindowId>FRONT_RIGHT</WindowId>
      <VehicleSide>SIDE_RIGHT</VehicleSide>
      <AccessableSeats>
        <Item ref="SEAT_ELEGYIMPORT_FRONT_RIGHT"/>
      </AccessableSeats>
      <VehicleExtraPointsInfo ref="EXTRA_VEHICLE_POINTS_STD_RIGHT"/>
      <Flags>BlockJackReactionUntilJackerIsReady</Flags>
      <BlockJackReactionSides>
        <Item>SIDE_LEFT</Item>
      </BlockJackReactionSides>
    </Item>
    <Item type="CVehicleEntryPointInfo">
      <Name>ENTRY_POINT_ELEGYIMPORT_FRONT_LEFT</Name>
      <DoorBoneName>door_pside_f</DoorBoneName>
      <SecondDoorBoneName/>
      <DoorHandleBoneName>handle_pside_f</DoorHandleBoneName>
      <WindowId>FRONT_LEFT</WindowId>
      <VehicleSide>SIDE_LEFT</VehicleSide>
      <AccessableSeats>
        <Item ref="SEAT_ELEGYIMPORT_FRONT_LEFT"/>
      </AccessableSeats>
      <VehicleExtraPointsInfo ref="EXTRA_VEHICLE_POINTS_STD_LEFT"/>
      <Flags>BlockJackReactionUntilJackerIsReady</Flags>
      <BlockJackReactionSides>
        <Item>SIDE_RIGHT</Item>
      </BlockJackReactionSides>
    </Item>
  </VehicleEntryPointInfos>
  <VehicleEntryPointAnimInfos>
    <Item type="CVehicleEntryPointAnimInfo">
      <Name>ENTRY_POINT_ANIM_STD_ELEGYRHD_FRONT_LEFT</Name>
      <CommonClipSetMap ref="COMMON_CLIPSET_MAP_AUTOMOBILE_LEFT"/>
      <EntryClipSetMap ref="ENTRY_CLIPSET_MAP_ELEGYRHD_STD_FRONT_LEFT"/>
      <ExitClipSetMap ref="EXIT_CLIPSET_MAP_ELEGYRHD_STD_FRONT_LEFT"/>
      <AlternateTryLockedDoorClipId>STD_Locked_PS</AlternateTryLockedDoorClipId>
      <AlternateForcedEntryClipId>STD_Force_Entry_PS</AlternateForcedEntryClipId>
      <AlternateJackFromOutSideClipId>std_perp_ps</AlternateJackFromOutSideClipId>
      <AlternateBeJackedFromOutSideClipId>std_victim_ps</AlternateBeJackedFromOutSideClipId>
      <AlternateEntryPointClipSetId>clipset@veh@std@ps@female@enter_exit</AlternateEntryPointClipSetId>
      <EnterVehicleMoveNetwork>ENTER_VEHICLE_STD</EnterVehicleMoveNetwork>
      <EntryTranslation x="-0.842000" y="-0.551000" z="0.500000"/>
      <OpenDoorTranslation x="-0.050000" y="0.150000"/>
      <OpenDoorHeadingChange value="1.570000"/>
      <EntryHeadingChange value="-1.570000"/>
      <ExtraZForWaterEntry value="0.000000"/>
      <EntryPointFlags>JackIncludesGetIn UsesNoDoorTransitionForExit UseOpenDoorBlendAnims FixUpMoverToEntryPointOnExit UseGetUpAfterJack JackVariationsIncludeGetIn HasCombatEntry</EntryPointFlags>
      <EntryAnimVariations ref="ENTRY_ANIM_VARIATIONS_LOW_FRONT_LEFT"/>
    </Item>
    <Item type="CVehicleEntryPointAnimInfo">
      <Name>ENTRY_POINT_ANIM_STD_ELEGYRHD_FRONT_RIGHT</Name>
      <CommonClipSetMap ref="COMMON_CLIPSET_MAP_AUTOMOBILE_RIGHT"/>
      <EntryClipSetMap ref="ENTRY_CLIPSET_MAP_ELEGYRHD_STD_FRONT_RIGHT"/>
      <ExitClipSetMap ref="EXIT_CLIPSET_MAP_ELEGYRHD_STD_FRONT_RIGHT"/>
      <AlternateTryLockedDoorClipId>STD_Locked_DS</AlternateTryLockedDoorClipId>
      <AlternateForcedEntryClipId>STD_Force_Entry_DS</AlternateForcedEntryClipId>
      <AlternateJackFromOutSideClipId>std_perp_ds</AlternateJackFromOutSideClipId>
      <AlternateBeJackedFromOutSideClipId>std_victim_ds</AlternateBeJackedFromOutSideClipId>
      <AlternateEntryPointClipSetId>clipset@veh@std@ds@female@enter_exit</AlternateEntryPointClipSetId>
      <EnterVehicleMoveNetwork>ENTER_VEHICLE_STD</EnterVehicleMoveNetwork>
      <EntryTranslation x="0.822600" y="-0.551700" z="0.500000"/>
      <OpenDoorTranslation x="0.050000" y="0.150000"/>
      <OpenDoorHeadingChange value="-1.570000"/>
      <EntryHeadingChange value="1.570000"/>
      <ExtraZForWaterEntry value="0.000000"/>
      <EntryPointFlags>JackIncludesGetIn UsesNoDoorTransitionForExit UseOpenDoorBlendAnims FixUpMoverToEntryPointOnExit UseGetUpAfterJack JackVariationsIncludeGetIn HasCombatEntry</EntryPointFlags>
      <EntryAnimVariations ref="ENTRY_ANIM_VARIATIONS_LOW_FRONT_RIGHT"/>
      <NMJumpFromVehicleTuningSet>LOWCAR</NMJumpFromVehicleTuningSet>
    </Item>
    <Item type="CVehicleEntryPointAnimInfo">
      <Name>ENTRY_POINT_ANIM_STD_ELEGYIMPORT_FRONT_LEFT</Name>
      <CommonClipSetMap ref="COMMON_CLIPSET_MAP_AUTOMOBILE_LEFT"/>
      <EntryClipSetMap ref="ENTRY_CLIPSET_MAP_ELEGYIMPORT_STD_FRONT_LEFT"/>
      <ExitClipSetMap ref="EXIT_CLIPSET_MAP_ELEGYIMPORT_STD_FRONT_LEFT"/>
      <AlternateTryLockedDoorClipId>STD_Locked_PS</AlternateTryLockedDoorClipId>
      <AlternateForcedEntryClipId>STD_Force_Entry_PS</AlternateForcedEntryClipId>
      <AlternateJackFromOutSideClipId>std_perp_ps</AlternateJackFromOutSideClipId>
      <AlternateBeJackedFromOutSideClipId>std_victim_ps</AlternateBeJackedFromOutSideClipId>
      <AlternateEntryPointClipSetId>clipset@veh@std@ps@female@enter_exit</AlternateEntryPointClipSetId>
      <EnterVehicleMoveNetwork>ENTER_VEHICLE_STD</EnterVehicleMoveNetwork>
      <EntryTranslation x="-0.842000" y="-0.551000" z="0.500000"/>
      <OpenDoorTranslation x="-0.050000" y="0.150000"/>
      <OpenDoorHeadingChange value="1.570000"/>
      <EntryHeadingChange value="-1.570000"/>
      <ExtraZForWaterEntry value="0.000000"/>
      <EntryPointFlags>JackIncludesGetIn UsesNoDoorTransitionForExit UseOpenDoorBlendAnims FixUpMoverToEntryPointOnExit UseGetUpAfterJack JackVariationsIncludeGetIn HasCombatEntry</EntryPointFlags>
      <EntryAnimVariations ref="ENTRY_ANIM_VARIATIONS_LOW_FRONT_LEFT"/>
    </Item>
    <Item type="CVehicleEntryPointAnimInfo">
      <Name>ENTRY_POINT_ANIM_STD_ELEGYIMPORT_FRONT_RIGHT</Name>
      <CommonClipSetMap ref="COMMON_CLIPSET_MAP_AUTOMOBILE_RIGHT"/>
      <EntryClipSetMap ref="ENTRY_CLIPSET_MAP_ELEGYIMPORT_STD_FRONT_RIGHT"/>
      <ExitClipSetMap ref="EXIT_CLIPSET_MAP_ELEGYIMPORT_STD_FRONT_RIGHT"/>
      <AlternateTryLockedDoorClipId>STD_Locked_DS</AlternateTryLockedDoorClipId>
      <AlternateForcedEntryClipId>STD_Force_Entry_DS</AlternateForcedEntryClipId>
      <AlternateJackFromOutSideClipId>std_perp_ds</AlternateJackFromOutSideClipId>
      <AlternateBeJackedFromOutSideClipId>std_victim_ds</AlternateBeJackedFromOutSideClipId>
      <AlternateEntryPointClipSetId>clipset@veh@std@ds@female@enter_exit</AlternateEntryPointClipSetId>
      <EnterVehicleMoveNetwork>ENTER_VEHICLE_STD</EnterVehicleMoveNetwork>
      <EntryTranslation x="0.822600" y="-0.551700" z="0.500000"/>
      <OpenDoorTranslation x="0.050000" y="0.150000"/>
      <OpenDoorHeadingChange value="-1.570000"/>
      <EntryHeadingChange value="1.570000"/>
      <ExtraZForWaterEntry value="0.000000"/>
      <EntryPointFlags>JackIncludesGetIn UsesNoDoorTransitionForExit UseOpenDoorBlendAnims FixUpMoverToEntryPointOnExit UseGetUpAfterJack JackVariationsIncludeGetIn HasCombatEntry</EntryPointFlags>
      <EntryAnimVariations ref="ENTRY_ANIM_VARIATIONS_LOW_FRONT_RIGHT"/>
      <NMJumpFromVehicleTuningSet>LOWCAR</NMJumpFromVehicleTuningSet>
    </Item>
  </VehicleEntryPointAnimInfos>
  <VehicleLayoutInfos>
    <Item type="CVehicleLayoutInfo">
      <Name>LAYOUT_STD_ELEGYRHD</Name>
      <Seats>
        <Item>
          <SeatInfo ref="SEAT_ELEGYRHD_FRONT_RIGHT"/>
          <SeatAnimInfo ref="SEAT_ANIM_ELEGYRHD_FRONT_RIGHT"/>
        </Item>
        <Item>
          <SeatInfo ref="SEAT_ELEGYRHD_FRONT_LEFT"/>
          <SeatAnimInfo ref="SEAT_ANIM_ELEGYRHD_FRONT_LEFT"/>
        </Item>
      </Seats>
      <EntryPoints>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_ELEGYRHD_FRONT_LEFT"/>
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_STD_ELEGYRHD_FRONT_LEFT"/>
        </Item>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_ELEGYRHD_FRONT_RIGHT"/>
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_STD_ELEGYRHD_FRONT_RIGHT"/>
        </Item>
      </EntryPoints>
      <LayoutFlags>StreamAnims UseDoorOscillation UseLeanSteerAnims UseSteeringWheelIk UseFinerAlignTolerance</LayoutFlags>
      <BicycleInfo ref="NULL"/>
      <AnimRateSet ref="STD_LOW_ANIM_RATE_SET"/>
      <HandsUpClipSetId>busted_vehicle_std</HandsUpClipSetId>
      <SteeringWheelOffset x="0.000000" y="0.350000" z="0.320000"/>
      <MaxXAcceleration value="4.00000"/>
      <BodyLeanXApproachSpeed value="5.00000"/>
      <BodyLeanXSmallDelta value="0.30000"/>
      <LookBackApproachSpeedScale value="1.00000"/>
      <FirstPersonAdditiveIdleClipSets>
        <Item>clipset@veh@std@ds@idle_a</Item>
        <Item>clipset@veh@std@ds@idle_b</Item>
        <Item>clipset@veh@std@ds@idle_c</Item>
        <Item>clipset@veh@std@ds@idle_d</Item>
        <Item>clipset@veh@std@ds@idle_e</Item>
      </FirstPersonAdditiveIdleClipSets>
      <FirstPersonRoadRageClipSets>
        <Item>clipset@veh@std@ds@hit_wheel@idle_a</Item>
        <Item>clipset@veh@std@ds@hit_wheel@idle_b</Item>
        <Item>clipset@veh@std@ds@hit_wheel@idle_c</Item>
      </FirstPersonRoadRageClipSets>
    </Item>
    <Item type="CVehicleLayoutInfo">
      <Name>LAYOUT_STD_ELEGYIMPORT</Name>
      <Seats>
        <Item>
          <SeatInfo ref="SEAT_ELEGYIMPORT_FRONT_RIGHT"/>
          <SeatAnimInfo ref="SEAT_ANIM_ELEGYIMPORT_FRONT_RIGHT"/>
        </Item>
        <Item>
          <SeatInfo ref="SEAT_ELEGYIMPORT_FRONT_LEFT"/>
          <SeatAnimInfo ref="SEAT_ANIM_ELEGYIMPORT_FRONT_LEFT"/>
        </Item>
      </Seats>
      <EntryPoints>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_ELEGYIMPORT_FRONT_LEFT"/>
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_STD_ELEGYIMPORT_FRONT_LEFT"/>
        </Item>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_ELEGYIMPORT_FRONT_RIGHT"/>
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_STD_ELEGYIMPORT_FRONT_RIGHT"/>
        </Item>
      </EntryPoints>
      <LayoutFlags>StreamAnims UseDoorOscillation UseLeanSteerAnims UseSteeringWheelIk UseFinerAlignTolerance</LayoutFlags>
      <BicycleInfo ref="NULL"/>
      <AnimRateSet ref="STD_LOW_ANIM_RATE_SET"/>
      <HandsUpClipSetId>busted_vehicle_std</HandsUpClipSetId>
      <SteeringWheelOffset x="0.000000" y="0.350000" z="0.320000"/>
      <MaxXAcceleration value="4.00000"/>
      <BodyLeanXApproachSpeed value="5.00000"/>
      <BodyLeanXSmallDelta value="0.30000"/>
      <LookBackApproachSpeedScale value="1.00000"/>
      <FirstPersonAdditiveIdleClipSets>
        <Item>clipset@veh@std@ds@idle_a</Item>
        <Item>clipset@veh@std@ds@idle_b</Item>
        <Item>clipset@veh@std@ds@idle_c</Item>
        <Item>clipset@veh@std@ds@idle_d</Item>
        <Item>clipset@veh@std@ds@idle_e</Item>
      </FirstPersonAdditiveIdleClipSets>
      <FirstPersonRoadRageClipSets>
        <Item>clipset@veh@std@ds@hit_wheel@idle_a</Item>
        <Item>clipset@veh@std@ds@hit_wheel@idle_b</Item>
        <Item>clipset@veh@std@ds@hit_wheel@idle_c</Item>
      </FirstPersonRoadRageClipSets>
    </Item>
  </VehicleLayoutInfos>
  <AnimRateSets/>
  <BicycleInfos/>
  <POVTuningInfos/>
  <EntryAnimVariations/>
  <VehicleExtraPointsInfos/>
  <DrivebyWeaponGroups/>
  <VehicleExplosionInfos/>
  <VehicleScenarioLayoutInfos/>
  <SeatOverrideAnimInfos/>
  <InVehicleOverrideInfos/>
  <FirstPersonDriveByLookAroundData/>
</CVehicleMetadataMgr>



















