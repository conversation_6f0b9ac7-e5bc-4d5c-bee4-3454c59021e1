<?xml version="1.0" encoding="UTF-8"?>
<CVehicleModelInfo__InitDataList>
  <residentTxd>vehshare</residentTxd>
  <residentAnims />
  <InitDatas>
    <Item>
      <modelName>benito2020</modelName>
      <txdName>benito2020</txdName>
      <handlingId>BENITO2020</handlingId>
      <gameName>BENITO2020</gameName>
      <vehicleMakeName>BF</vehicleMakeName>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected />
      <ptfxAssetName>veh_deluxo</ptfxAssetName>
      <audioNameHash>dynasty</audioNameHash>
      <layout>LAYOUT_LOW</layout>
      <coverBoundOffsets>COMET_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout />
      <cameraName>DEFAULT_FOLLOW_VEHICLE_CAMERA</cameraName>
      <aimCameraName>DEFAULT_THIRD_PERSON_VEHICLE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>VEHICLE_BONNET_CAMERA_NEAR_EXTRA_HIGH</bonnetCameraName>
      <povCameraName>DEFAULT_POV_CAMERA</povCameraName>
      <FirstPersonDriveByIKOffset x="0.000000" y="-0.055000" z="0.020000" />
      <FirstPersonDriveByUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonProjectileDriveByIKOffset x="0.020000" y="-0.080000" z="0.000000" />
      <FirstPersonProjectileDriveByPassengerIKOffset x="-0.120000" y="-0.080000" z="0.000000" />
      <FirstPersonProjectileDriveByRearLeftIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonProjectileDriveByRearRightIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonDriveByLeftPassengerIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonDriveByRightPassengerIKOffset x="0.000000" y="-0.055000" z="0.020000" />
      <FirstPersonDriveByRightRearPassengerIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonDriveByLeftPassengerUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonDriveByRightPassengerUnarmedIKOffset x="0.000000" y="-0.120000" z="-0.025000" />
      <FirstPersonMobilePhoneOffset x="0.140000" y="0.230000" z="0.510000" />
      <FirstPersonPassengerMobilePhoneOffset x="0.136000" y="0.118000" z="0.443000" />
      <PovCameraOffset x="0.000000" y="-0.190000" z="0.600000" />
      <PovCameraVerticalAdjustmentForRollCage value="0.000000" />
      <PovPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000" />
      <PovRearPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000" />
      <vfxInfoName>VFXVEHICLEINFO_CAR_DELUXO</vfxInfoName>
      <shouldUseCinematicViewMode value="true" />
      <shouldCameraTransitionOnClimbUpDown value="false" />
      <shouldCameraIgnoreExiting value="false" />
      <AllowPretendOccupants value="true" />
      <AllowJoyriding value="true" />
      <AllowSundayDriving value="true" />
      <AllowBodyColorMapping value="true" />
      <wheelScale value="0.2113" />
      <wheelScaleRear value="0.2113" />
      <dirtLevelMin value="0.000000" />
      <dirtLevelMax value="0.300000" />
      <envEffScaleMin value="0.000000" />
      <envEffScaleMax value="1.000000" />
      <envEffScaleMin2 value="0.000000" />
      <envEffScaleMax2 value="1.000000" />
      <damageMapScale value="0.600000" />
      <damageOffsetScale value="1.000000" />
      <diffuseTint value="0x00FFFFFF" />
      <steerWheelMult value="1.000000" />
      <HDTextureDist value="5.000000" />
      <lodDistances content="float_array">
        450.000000
        460.000000
        470.000000
        480.000000
        500.000000
        500.000000
      </lodDistances>
      <minSeatHeight value="0.881" />
      <identicalModelSpawnDistance value="20" />
      <maxNumOfSameColor value="1" />
      <defaultBodyHealth value="1000.000000" />
      <pretendOccupantsScale value="1.000000" />
      <visibleSpawnDistScale value="1.000000" />
      <trackerPathWidth value="2.000000" />
      <weaponForceMult value="1.000000" />
      <frequency value="30" />
      <swankness>SWANKNESS_5</swankness>
      <maxNum value="5" />
      <flags>FLAG_CAN_HAVE_NEONS FLAG_EXTRAS_STRONG FLAG_AVERAGE_CAR FLAG_POOR_CAR
        FLAG_RECESSED_HEADLIGHT_CORONAS FLAG_RECESSED_TAILLIGHT_CORONAS FLAG_HAS_INTERIOR_EXTRAS
        FLAG_USE_AIRCRAFT_STYLE_WEAPON_TARGETING FLAG_HAS_GULL_WING_DOORS
        FLAG_USE_ROOT_AS_BASE_LOCKON_POS FLAG_DONT_TIMESLICE_WHEELS FLAG_ATTACH_TRAILER_ON_HIGHWAY
        FLAG_ATTACH_TRAILER_IN_CITY</flags>
      <type>VEHICLE_TYPE_CAR</type>
      <plateType>VPT_BACK_PLATES</plateType>
      <dashboardType>VDT_RACE</dashboardType>
      <vehicleClass>VC_SPORT_CLASSIC</vehicleClass>
      <wheelType>VWT_SPORT</wheelType>
      <trailers>
        <Item>boattrailer</Item>
        <Item>trailersmall</Item>
        <Item>trailersmall2</Item>
      </trailers>
      <additionalTrailers>
        <Item>trailersmall2</Item>
      </additionalTrailers>
      <drivers />
      <extraIncludes />
      <doorsWithCollisionWhenClosed />
      <driveableDoors />
      <bumpersNeedToCollideWithMap value="false" />
      <needsRopeTexture value="false" />
      <requiredExtras />
      <rewards />
      <cinematicPartCamera>
        <Item>WHEEL_FRONT_RIGHT_CAMERA</Item>
        <Item>WHEEL_FRONT_LEFT_CAMERA</Item>
        <Item>WHEEL_REAR_RIGHT_CAMERA</Item>
        <Item>WHEEL_REAR_LEFT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet />
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000" />
      <buoyancySphereSizeScale value="1.000000" />
      <pOverrideRagdollThreshold type="NULL" />
      <firstPersonDrivebyData>
        <Item>LOW_RAPIDGT_FRONT_LEFT</Item>
        <Item>LOW_RAPIDGT_FRONT_RIGHT</Item>
      </firstPersonDrivebyData>
    </Item>
    <Item>
      <modelName>elegyx</modelName>
      <txdName>elegyx</txdName>
      <handlingId>ELEGYX</handlingId>
      <gameName>ELEGYX</gameName>
      <vehicleMakeName>ANNIS</vehicleMakeName>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected />
      <ptfxAssetName>null</ptfxAssetName>
      <audioNameHash>ELEGYX</audioNameHash>
      <layout>LAYOUT_STD_EXITFIXUP</layout>
      <coverBoundOffsets>ELEGY_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout />
      <cameraName>DEFAULT_FOLLOW_VEHICLE_CAMERA</cameraName>
      <aimCameraName>DEFAULT_THIRD_PERSON_VEHICLE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>VEHICLE_BONNET_CAMERA_NEAR_EXTRA_HIGH</bonnetCameraName>
      <povCameraName>DEFAULT_POV_CAMERA</povCameraName>
      <FirstPersonDriveByIKOffset x="0.000000" y="-0.020000" z="0.000000" />
      <FirstPersonDriveByUnarmedIKOffset x="0.000000" y="-0.035000" z="0.000000" />
      <FirstPersonProjectileDriveByIKOffset x="0.000000" y="0.000000" z="-0.010000" />
      <FirstPersonProjectileDriveByPassengerIKOffset x="0.000000" y="0.000000" z="-0.010000" />
      <FirstPersonDriveByLeftPassengerIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonDriveByRightPassengerIKOffset x="0.000000" y="-0.020000" z="0.000000" />
      <FirstPersonDriveByLeftPassengerUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonDriveByRightPassengerUnarmedIKOffset x="0.000000" y="-0.045000" z="0.000000" />
      <FirstPersonMobilePhoneOffset x="0.118000" y="0.288000" z="0.558000" />
      <FirstPersonPassengerMobilePhoneOffset x="0.135000" y="0.205000" z="0.475000" />
      <PovCameraOffset x="0.000000" y="-0.100000" z="0.680000" />
      <PovCameraVerticalAdjustmentForRollCage value="0.000000" />
      <PovPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000" />
      <vfxInfoName>VFXVEHICLEINFO_CAR_GENERIC</vfxInfoName>
      <shouldUseCinematicViewMode value="true" />
      <shouldCameraTransitionOnClimbUpDown value="false" />
      <shouldCameraIgnoreExiting value="false" />
      <AllowPretendOccupants value="true" />
      <AllowJoyriding value="true" />
      <AllowSundayDriving value="true" />
      <AllowBodyColorMapping value="true" />
      <wheelScale value="0.325100" />
      <wheelScaleRear value="0.325100" />
      <dirtLevelMin value="0.000000" />
      <dirtLevelMax value="0.300000" />
      <envEffScaleMin value="0.000000" />
      <envEffScaleMax value="1.000000" />
      <envEffScaleMin2 value="0.000000" />
      <envEffScaleMax2 value="1.000000" />
      <damageMapScale value="0.600000" />
      <damageOffsetScale value="1.000000" />
      <diffuseTint value="0x00FFFFFF" />
      <steerWheelMult value="1.000000" />
      <HDTextureDist value="5.000000" />
      <lodDistances content="float_array">
        20.000000
        40.000000
        80.000000
        120.000000
        500.000000
        500.000000
      </lodDistances>
      <minSeatHeight value="0.871" />
      <identicalModelSpawnDistance value="80" />
      <maxNumOfSameColor value="1" />
      <defaultBodyHealth value="1000.000000" />
      <pretendOccupantsScale value="1.000000" />
      <visibleSpawnDistScale value="1.000000" />
      <trackerPathWidth value="2.000000" />
      <weaponForceMult value="1.000000" />
      <frequency value="10" />
      <swankness>SWANKNESS_5</swankness>
      <maxNum value="1" />
      <flags>FLAG_SPORTS FLAG_RICH_CAR FLAG_NO_BROKEN_DOWN_SCENARIO</flags>
      <type>VEHICLE_TYPE_CAR</type>
      <plateType>VPT_BACK_PLATES</plateType>
      <dashboardType>VDT_BANSHEE</dashboardType>
      <vehicleClass>VC_SPORT</vehicleClass>
      <wheelType>VWT_SPORT</wheelType>
      <trailers />
      <additionalTrailers />
      <drivers />
      <extraIncludes />
      <doorsWithCollisionWhenClosed />
      <driveableDoors />
      <bumpersNeedToCollideWithMap value="false" />
      <needsRopeTexture value="false" />
      <requiredExtras />
      <rewards />
      <cinematicPartCamera>
        <Item>WHEEL_FRONT_RIGHT_CAMERA</Item>
        <Item>WHEEL_FRONT_LEFT_CAMERA</Item>
        <Item>WHEEL_REAR_RIGHT_CAMERA</Item>
        <Item>WHEEL_REAR_LEFT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet />
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000" />
      <buoyancySphereSizeScale value="1.000000" />
      <pOverrideRagdollThreshold type="NULL" />
      <firstPersonDrivebyData>
        <Item>STD_ELEGY_FRONT_LEFT</Item>
        <Item>STD_ELEGY_FRONT_RIGHT</Item>
      </firstPersonDrivebyData>
    </Item>
    <Item>
      <modelName>ellie6str</modelName>
      <txdName>ellie6str</txdName>
      <handlingId>ELLIE6STR</handlingId>
      <gameName>ELLIE6STR</gameName>
      <vehicleMakeName>VAPID</vehicleMakeName>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected />
      <ptfxAssetName>null</ptfxAssetName>
      <audioNameHash>trophytruck</audioNameHash>
      <layout>LAYOUT_LOW</layout>
      <coverBoundOffsets>ELLIE_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout />
      <cameraName>FOLLOW_CHEETAH_CAMERA</cameraName>
      <aimCameraName>MID_BOX_VEHICLE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>VEHICLE_BONNET_CAMERA_STANDARD_LONG</bonnetCameraName>
      <povCameraName>DEFAULT_POV_CAMERA</povCameraName>
      <FirstPersonDriveByIKOffset x="0.000000" y="-0.040000" z="-0.040000" />
      <FirstPersonDriveByUnarmedIKOffset x="-0.010000" y="-0.070000" z="-0.010000" />
      <FirstPersonProjectileDriveByIKOffset x="0.000000" y="-0.100000" z="-0.050000" />
      <FirstPersonProjectileDriveByPassengerIKOffset x="0.020000" y="-0.100000" z="-0.050000" />
      <FirstPersonDriveByRightPassengerIKOffset x="-0.020000" y="-0.020000" z="-0.040000" />
      <FirstPersonDriveByRightPassengerUnarmedIKOffset x="-0.025000" y="-0.050000" z="0.000000" />
      <FirstPersonMobilePhoneOffset x="0.120000" y="0.090000" z="0.575000" />
      <FirstPersonPassengerMobilePhoneOffset x="0.225000" y="0.050000" z="0.460000" />
      <PovCameraOffset x="0.000000" y="-0.310000" z="0.655000" />
      <PovCameraVerticalAdjustmentForRollCage value="0.000000" />
      <PovPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000" />
      <PovRearPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000" />
      <vfxInfoName>VFXVEHICLEINFO_CAR_GENERIC</vfxInfoName>
      <shouldUseCinematicViewMode value="true" />
      <shouldCameraTransitionOnClimbUpDown value="false" />
      <shouldCameraIgnoreExiting value="false" />
      <AllowPretendOccupants value="true" />
      <AllowJoyriding value="true" />
      <AllowSundayDriving value="true" />
      <AllowBodyColorMapping value="true" />
      <wheelScale value="0.255000" />
      <wheelScaleRear value="0.255000" />
      <dirtLevelMin value="0.000000" />
      <dirtLevelMax value="0.450000" />
      <envEffScaleMin value="0.000000" />
      <envEffScaleMax value="1.000000" />
      <envEffScaleMin2 value="0.000000" />
      <envEffScaleMax2 value="1.000000" />
      <damageMapScale value="0.400000" />
      <damageOffsetScale value="0.400000" />
      <diffuseTint value="0x00FFFFFF" />
      <steerWheelMult value="0.700000" />
      <HDTextureDist value="5.000000" />
      <lodDistances content="float_array">
        10.000000
        25.000000
        60.000000
        120.000000
        500.000000
        500.000000
      </lodDistances>
      <minSeatHeight value="0.844" />
      <identicalModelSpawnDistance value="20" />
      <maxNumOfSameColor value="1" />
      <defaultBodyHealth value="1000.000000" />
      <pretendOccupantsScale value="1.000000" />
      <visibleSpawnDistScale value="1.000000" />
      <trackerPathWidth value="2.000000" />
      <weaponForceMult value="1.000000" />
      <frequency value="30" />
      <swankness>SWANKNESS_4</swankness>
      <maxNum value="10" />
      <flags>FLAG_CAN_HAVE_NEONS FLAG_RECESSED_TAILLIGHT_CORONAS FLAG_RECESSED_HEADLIGHT_CORONAS
        FLAG_INCREASE_CAMBER_WITH_SUSPENSION_MOD FLAG_HAS_INTERIOR_EXTRAS</flags>
      <type>VEHICLE_TYPE_CAR</type>
      <plateType>VPT_BACK_PLATES</plateType>
      <dashboardType>VDT_DUKES</dashboardType>
      <vehicleClass>VC_MUSCLE</vehicleClass>
      <wheelType>VWT_MUSCLE</wheelType>
      <trailers />
      <additionalTrailers />
      <drivers />
      <extraIncludes />
      <doorsWithCollisionWhenClosed />
      <driveableDoors />
      <bumpersNeedToCollideWithMap value="false" />
      <needsRopeTexture value="false" />
      <requiredExtras />
      <rewards />
      <cinematicPartCamera>
        <Item>WHEEL_FRONT_RIGHT_CAMERA</Item>
        <Item>WHEEL_FRONT_LEFT_CAMERA</Item>
        <Item>WHEEL_REAR_RIGHT_CAMERA</Item>
        <Item>WHEEL_REAR_LEFT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet />
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000" />
      <buoyancySphereSizeScale value="1.000000" />
      <pOverrideRagdollThreshold type="NULL" />
      <firstPersonDrivebyData>
        <Item>LOW_ELLIE_FRONT_LEFT</Item>
        <Item>LOW_ELLIE_FRONT_RIGHT</Item>
      </firstPersonDrivebyData>
    </Item>
    <Item>
      <modelName>elytron</modelName>
      <txdName>elytron</txdName>
      <handlingId>ELYTRON</handlingId>
      <gameName>ELYTRON</gameName>
      <vehicleMakeName>BF</vehicleMakeName>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected />
      <ptfxAssetName>null</ptfxAssetName>
      <audioNameHash>bfinjection</audioNameHash>
      <layout>LAYOUT_LOW_LE7B</layout>
      <coverBoundOffsets>COMET6_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout />
      <cameraName>FOLLOW_CHEETAH_CAMERA</cameraName>
      <aimCameraName>DEFAULT_THIRD_PERSON_VEHICLE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>VEHICLE_BONNET_CAMERA_LOW_LOW</bonnetCameraName>
      <povCameraName>REDUCED_NEAR_CLIP_POV_CAMERA</povCameraName>
      <FirstPersonDriveByIKOffset x="0.040000" y="-0.120000" z="-0.035000" />
      <FirstPersonDriveByUnarmedIKOffset x="0.03000" y="-0.010000" z="-0.035000" />
      <FirstPersonProjectileDriveByIKOffset x="0.070000" y="-0.110000" z="-0.080000" />
      <FirstPersonProjectileDriveByPassengerIKOffset x="-0.120000" y="-0.1100000" z="-0.070000" />
      <FirstPersonDriveByRightPassengerIKOffset x="0.000000" y="-0.10000" z="-0.035000" />
      <FirstPersonDriveByRightPassengerUnarmedIKOffset x="0.000000" y="-0.060000" z="-0.020000" />
      <FirstPersonMobilePhoneOffset x="0.165000" y="0.128000" z="0.553000" />
      <FirstPersonPassengerMobilePhoneOffset x="0.221000" y="0.093000" z="0.441000" />
      <PovCameraOffset x="0.000000" y="-0.300000" z="0.650000" />
      <PovCameraVerticalAdjustmentForRollCage value="0.000000" />
      <PovPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000" />
      <vfxInfoName>VFXVEHICLEINFO_CAR_GENERIC</vfxInfoName>
      <shouldUseCinematicViewMode value="true" />
      <shouldCameraTransitionOnClimbUpDown value="false" />
      <shouldCameraIgnoreExiting value="false" />
      <AllowPretendOccupants value="true" />
      <AllowJoyriding value="true" />
      <AllowSundayDriving value="true" />
      <AllowBodyColorMapping value="true" />
      <wheelScale value="0.271500" />
      <wheelScaleRear value="0.271500" />
      <dirtLevelMin value="0.000000" />
      <dirtLevelMax value="0.300000" />
      <envEffScaleMin value="0.000000" />
      <envEffScaleMax value="1.000000" />
      <envEffScaleMin2 value="0.000000" />
      <envEffScaleMax2 value="1.000000" />
      <damageMapScale value="0.600000" />
      <damageOffsetScale value="0.600000" />
      <diffuseTint value="0x00FFFFFF" />
      <steerWheelMult value="0.600000" />
      <HDTextureDist value="5.000000" />
      <lodDistances content="float_array">
        10.000000
        25.000000
        60.000000
        120.000000
        500.000000
        500.000000
      </lodDistances>
      <minSeatHeight value="0.881" />
      <identicalModelSpawnDistance value="20" />
      <maxNumOfSameColor value="1" />
      <defaultBodyHealth value="1000.000000" />
      <pretendOccupantsScale value="1.000000" />
      <visibleSpawnDistScale value="1.000000" />
      <trackerPathWidth value="2.000000" />
      <weaponForceMult value="1.000000" />
      <frequency value="30" />
      <swankness>SWANKNESS_5</swankness>
      <maxNum value="5" />
      <flags>FLAG_SPORTS FLAG_SPAWN_ON_TRAILER FLAG_PARKING_SENSORS FLAG_RICH_CAR
        FLAG_NO_BROKEN_DOWN_SCENARIO FLAG_CAN_HAVE_NEONS</flags>
      <type>VEHICLE_TYPE_CAR</type>
      <plateType>VPT_BACK_PLATES</plateType>
      <dashboardType>VDT_RACE</dashboardType>
      <vehicleClass>VC_SPORT</vehicleClass>
      <wheelType>VWT_SPORT</wheelType>
      <trailers />
      <additionalTrailers />
      <drivers />
      <extraIncludes />
      <doorsWithCollisionWhenClosed />
      <driveableDoors />
      <bumpersNeedToCollideWithMap value="false" />
      <needsRopeTexture value="false" />
      <requiredExtras />
      <rewards />
      <cinematicPartCamera>
        <Item>WHEEL_FRONT_RIGHT_CAMERA</Item>
        <Item>WHEEL_FRONT_LEFT_CAMERA</Item>
        <Item>WHEEL_REAR_RIGHT_CAMERA</Item>
        <Item>WHEEL_REAR_LEFT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet />
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000" />
      <buoyancySphereSizeScale value="1.000000" />
      <pOverrideRagdollThreshold type="NULL" />
      <firstPersonDrivebyData>
        <Item>LOW_COMET6_FRONT_LEFT</Item>
        <Item>LOW_COMET6_FRONT_RIGHT</Item>
      </firstPersonDrivebyData>
    </Item>
    <Item>
      <modelName>hachurac</modelName>
      <txdName>hachurac</txdName>
      <handlingId>HACHURAC</handlingId>
      <gameName>HACHURAC</gameName>
      <vehicleMakeName>VULCAR</vehicleMakeName>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected />
      <ptfxAssetName>null</ptfxAssetName>
      <audioNameHash>jester4</audioNameHash>
      <layout>LAYOUT_LOW</layout>
      <coverBoundOffsets>IMORGON_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout />
      <cameraName>FOLLOW_CHEETAH_CAMERA</cameraName>
      <aimCameraName>DEFAULT_THIRD_PERSON_VEHICLE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>VEHICLE_BONNET_CAMERA_LOW</bonnetCameraName>
      <povCameraName>REDUCED_NEAR_CLIP_POV_CAMERA</povCameraName>
      <FirstPersonDriveByIKOffset x="0.000000" y="-0.100000" z="0.010000" />
      <FirstPersonDriveByUnarmedIKOffset x="0.000000" y="-0.120000" z="0.010000" />
      <FirstPersonProjectileDriveByIKOffset x="0.090000" y="-0.140000" z="0.000000" />
      <FirstPersonProjectileDriveByPassengerIKOffset x="-0.115000" y="-0.140000" z="0.000000" />
      <FirstPersonDriveByLeftPassengerIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonDriveByRightPassengerIKOffset x="0.000000" y="-0.100000" z="0.010000" />
      <FirstPersonDriveByLeftPassengerUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonDriveByRightPassengerUnarmedIKOffset x="0.000000" y="-0.135000" z="0.015000" />
      <FirstPersonMobilePhoneOffset x="0.164000" y="0.091000" z="0.507000" />
      <FirstPersonPassengerMobilePhoneOffset x="0.164000" y="0.051000" z="0.407000" />
      <PovCameraOffset x="0.010000" y="-0.280000" z="0.645000" />
      <PovCameraVerticalAdjustmentForRollCage value="0.000000" />
      <PovPassengerCameraOffset x="-0.020000" y="0.000000" z="0.000000" />
      <vfxInfoName>VFXVEHICLEINFO_CAR_ELECTRIC</vfxInfoName>
      <shouldUseCinematicViewMode value="true" />
      <shouldCameraTransitionOnClimbUpDown value="false" />
      <shouldCameraIgnoreExiting value="false" />
      <AllowPretendOccupants value="true" />
      <AllowJoyriding value="true" />
      <AllowSundayDriving value="true" />
      <AllowBodyColorMapping value="true" />
      <wheelScale value="0.293900" />
      <wheelScaleRear value="0.293900" />
      <dirtLevelMin value="0.000000" />
      <dirtLevelMax value="0.300000" />
      <envEffScaleMin value="0.000000" />
      <envEffScaleMax value="1.000000" />
      <envEffScaleMin2 value="0.000000" />
      <envEffScaleMax2 value="1.000000" />
      <damageMapScale value="0.500000" />
      <damageOffsetScale value="0.500000" />
      <diffuseTint value="0x00FFFFFF" />
      <steerWheelMult value="1.000000" />
      <HDTextureDist value="5.000000" />
      <lodDistances content="float_array">
        20.000000
        40.000000
        80.000000
        120.000000
        500.000000
        500.000000
      </lodDistances>
      <minSeatHeight value="0.784" />
      <identicalModelSpawnDistance value="80" />
      <maxNumOfSameColor value="1" />
      <defaultBodyHealth value="1000.000000" />
      <pretendOccupantsScale value="1.000000" />
      <visibleSpawnDistScale value="1.000000" />
      <trackerPathWidth value="2.000000" />
      <weaponForceMult value="1.000000" />
      <frequency value="5" />
      <swankness>SWANKNESS_5</swankness>
      <maxNum value="1" />
      <flags>FLAG_HAS_LIVERY FLAG_SPORTS FLAG_RICH_CAR FLAG_INCREASE_CAMBER_WITH_SUSPENSION_MOD
        FLAG_NO_BROKEN_DOWN_SCENARIO FLAG_RECESSED_HEADLIGHT_CORONAS FLAG_RECESSED_TAILLIGHT_CORONAS
        FLAG_HAS_INTERIOR_EXTRAS FLAG_IS_ELECTRIC</flags>
      <type>VEHICLE_TYPE_CAR</type>
      <plateType>VPT_FRONT_AND_BACK_PLATES</plateType>
      <dashboardType>VDT_BANSHEE</dashboardType>
      <vehicleClass>VC_SPORT</vehicleClass>
      <wheelType>VWT_SPORT</wheelType>
      <trailers />
      <additionalTrailers />
      <drivers />
      <extraIncludes />
      <doorsWithCollisionWhenClosed />
      <driveableDoors />
      <bumpersNeedToCollideWithMap value="false" />
      <needsRopeTexture value="false" />
      <requiredExtras />
      <rewards />
      <cinematicPartCamera>
        <Item>WHEEL_FRONT_RIGHT_CAMERA</Item>
        <Item>WHEEL_FRONT_LEFT_CAMERA</Item>
        <Item>WHEEL_REAR_RIGHT_CAMERA</Item>
        <Item>WHEEL_REAR_LEFT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet />
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000" />
      <buoyancySphereSizeScale value="1.000000" />
      <pOverrideRagdollThreshold type="NULL" />
      <firstPersonDrivebyData>
        <Item>STD_IMORGON_FRONT_LEFT</Item>
        <Item>STD_IMORGON_FRONT_RIGHT</Item>
      </firstPersonDrivebyData>
    </Item>
    <Item>
      <modelName>hustlerf132</modelName>
      <txdName>hustler</txdName>
      <handlingId>hustlerf132</handlingId>
      <gameName>HUSTLER</gameName>
      <vehicleMakeName>VAPID</vehicleMakeName>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected />
      <ptfxAssetName>null</ptfxAssetName>
      <audioNameHash>formula</audioNameHash>
      <layout>LAYOUT_STD_HUSTLER</layout>
      <coverBoundOffsets>HUSTLER_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout />
      <cameraName>DEFAULT_FOLLOW_VEHICLE_CAMERA</cameraName>
      <aimCameraName>BOX_VEHICLE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>VEHICLE_BONNET_CAMERA_STANDARD_LONG_EXTRA_LOW</bonnetCameraName>
      <povCameraName>DEFAULT_POV_CAMERA</povCameraName>
      <FirstPersonDriveByIKOffset x="-0.020000" y="-0.040000" z="-0.030000" />
      <FirstPersonDriveByUnarmedIKOffset x="-0.020000" y="-0.090000" z="-0.020000" />
      <FirstPersonProjectileDriveByIKOffset x="0.005000" y="-0.160000" z="-0.052000" />
      <FirstPersonProjectileDriveByPassengerIKOffset x="-0.012000" y="-0.160000" z="-0.049000" />
      <FirstPersonDriveByRightPassengerIKOffset x="-0.020000" y="-0.030000" z="-0.050000" />
      <FirstPersonDriveByRightPassengerUnarmedIKOffset x="-0.020000" y="-0.090000" z="-0.040000" />
      <FirstPersonMobilePhoneOffset x="0.115000" y="0.110000" z="0.590000" />
      <FirstPersonPassengerMobilePhoneOffset x="0.195000" y="0.110000" z="0.460000" />
      <PovCameraOffset x="0.000000" y="-0.210000" z="0.684000" />
      <PovCameraVerticalAdjustmentForRollCage value="0.000000" />
      <PovPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000" />
      <PovRearPassengerCameraOffset x="0.000000" y="0.000000" z="0.020000" />
      <vfxInfoName>VFXVEHICLEINFO_CAR_GENERIC</vfxInfoName>
      <shouldUseCinematicViewMode value="true" />
      <shouldCameraTransitionOnClimbUpDown value="false" />
      <shouldCameraIgnoreExiting value="false" />
      <AllowPretendOccupants value="true" />
      <AllowJoyriding value="false" />
      <AllowSundayDriving value="false" />
      <AllowBodyColorMapping value="true" />
      <wheelScale value="0.217600" />
      <wheelScaleRear value="0.217600" />
      <dirtLevelMin value="0.000000" />
      <dirtLevelMax value="1.000000" />
      <envEffScaleMin value="0.300000" />
      <envEffScaleMax value="0.400000" />
      <envEffScaleMin2 value="0.000000" />
      <envEffScaleMax2 value="1.000000" />
      <damageMapScale value="0.300000" />
      <damageOffsetScale value="0.300000" />
      <diffuseTint value="0x00FFFFFF" />
      <steerWheelMult value="0.800000" />
      <HDTextureDist value="5.000000" />
      <lodDistances content="float_array">
        170.000000
        200.000000
        300.000000
        400.000000
        500.000000
        500.000000
      </lodDistances>
      <minSeatHeight value="0.988" />
      <identicalModelSpawnDistance value="20" />
      <maxNumOfSameColor value="10" />
      <defaultBodyHealth value="1000.000000" />
      <pretendOccupantsScale value="1.000000" />
      <visibleSpawnDistScale value="1.000000" />
      <trackerPathWidth value="2.000000" />
      <weaponForceMult value="1.000000" />
      <frequency value="5" />
      <swankness>SWANKNESS_1</swankness>
      <maxNum value="1" />
      <flags>FLAG_EXTRAS_ALL FLAG_EXTRAS_STRONG FLAG_USE_STRICTER_EXIT_COLLISION_TESTS
        FLAG_USE_RESTRICTED_DRIVEBY_HEIGHT</flags>
      <type>VEHICLE_TYPE_CAR</type>
      <plateType>VPT_BACK_PLATES</plateType>
      <dashboardType>VDT_RACE</dashboardType>
      <vehicleClass>VC_MUSCLE</vehicleClass>
      <wheelType>VWT_MUSCLE</wheelType>
      <trailers />
      <additionalTrailers />
      <drivers />
      <extraIncludes />
      <doorsWithCollisionWhenClosed>
        <Item>VEH_EXT_BOOT</Item>
      </doorsWithCollisionWhenClosed>
      <driveableDoors />
      <bumpersNeedToCollideWithMap value="false" />
      <needsRopeTexture value="true" />
      <requiredExtras />
      <rewards />
      <cinematicPartCamera>
        <Item>WHEEL_WIDE_REAR_RIGHT_CAMERA</Item>
        <Item>WHEEL_WIDE_REAR_LEFT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet>Truck</NmBraceOverrideSet>
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000" />
      <buoyancySphereSizeScale value="1.000000" />
      <pOverrideRagdollThreshold type="NULL" />
      <firstPersonDrivebyData>
        <Item>STD_HUSTLER_FRONT_LEFT</Item>
        <Item>STD_HUSTLER_FRONT_RIGHT</Item>
      </firstPersonDrivebyData>
    </Item>
    <Item>
      <modelName>italigtr</modelName>
      <txdName>italigtr</txdName>
      <handlingId>ITALIGTR</handlingId>
      <gameName>ITALIGTR</gameName>
      <vehicleMakeName>GROTTI</vehicleMakeName>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected />
      <ptfxAssetName>null</ptfxAssetName>
      <audioNameHash>italigto</audioNameHash>
      <layout>LAYOUT_LOW_ITALIGTO</layout>
      <coverBoundOffsets>ITALIGTO_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout />
      <cameraName>FOLLOW_CHEETAH_CAMERA</cameraName>
      <aimCameraName>DEFAULT_THIRD_PERSON_VEHICLE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>VEHICLE_BONNET_CAMERA_STANDARD_LONG_EXTRA_LOW</bonnetCameraName>
      <povCameraName>REDUCED_NEAR_CLIP_POV_CAMERA</povCameraName>
      <FirstPersonDriveByIKOffset x="0.000000" y="-0.100000" z="-0.040000" />
      <FirstPersonDriveByUnarmedIKOffset x="0.000000" y="-0.160000" z="-0.025000" />
      <FirstPersonProjectileDriveByIKOffset x="0.010000" y="-0.140000" z="-0.060000" />
      <FirstPersonProjectileDriveByPassengerIKOffset x="-0.020000" y="-0.140000" z="-0.060000" />
      <FirstPersonDriveByRightPassengerIKOffset x="0.000000" y="-0.100000" z="-0.040000" />
      <FirstPersonDriveByRightPassengerUnarmedIKOffset x="0.000000" y="-0.160000" z="-0.025000" />
      <FirstPersonMobilePhoneOffset x="0.133000" y="0.133000" z="0.526000" />
      <FirstPersonPassengerMobilePhoneOffset x="0.209000" y="0.124000" z="0.401000" />
      <PovCameraOffset x="0.000000" y="-0.285000" z="0.650000" />
      <PovCameraVerticalAdjustmentForRollCage value="0.000000" />
      <PovPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000" />
      <PovRearPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000" />
      <vfxInfoName>VFXVEHICLEINFO_CAR_GENERIC</vfxInfoName>
      <shouldUseCinematicViewMode value="true" />
      <shouldCameraTransitionOnClimbUpDown value="false" />
      <shouldCameraIgnoreExiting value="false" />
      <AllowPretendOccupants value="true" />
      <AllowJoyriding value="true" />
      <AllowSundayDriving value="true" />
      <AllowBodyColorMapping value="true" />
      <wheelScale value="0.274500" />
      <wheelScaleRear value="0.274500" />
      <dirtLevelMin value="0.000000" />
      <dirtLevelMax value="0.400000" />
      <envEffScaleMin value="0.000000" />
      <envEffScaleMax value="1.000000" />
      <envEffScaleMin2 value="0.000000" />
      <envEffScaleMax2 value="1.000000" />
      <damageMapScale value="0.40000" />
      <damageOffsetScale value="0.40000" />
      <diffuseTint value="0x00FFFFFF" />
      <steerWheelMult value="1.000000" />
      <HDTextureDist value="5.000000" />
      <lodDistances content="float_array">
        10.000000
        25.000000
        60.000000
        120.000000
        500.000000
        500.000000
      </lodDistances>
      <identicalModelSpawnDistance value="20" />
      <maxNumOfSameColor value="1" />
      <defaultBodyHealth value="1000.000000" />
      <pretendOccupantsScale value="1.000000" />
      <visibleSpawnDistScale value="1.000000" />
      <trackerPathWidth value="2.000000" />
      <weaponForceMult value="1.000000" />
      <frequency value="5" />
      <swankness>SWANKNESS_5</swankness>
      <maxNum value="1" />
      <flags>FLAG_SPORTS FLAG_RICH_CAR FLAG_RECESSED_HEADLIGHT_CORONAS FLAG_NO_BROKEN_DOWN_SCENARIO
        FLAG_COUNT_AS_FACEBOOK_DRIVEN</flags>
      <type>VEHICLE_TYPE_CAR</type>
      <plateType>VPT_BACK_PLATES</plateType>
      <dashboardType>VDT_RACE</dashboardType>
      <vehicleClass>VC_SPORT</vehicleClass>
      <wheelType>VWT_HIEND</wheelType>
      <trailers />
      <additionalTrailers />
      <drivers />
      <extraIncludes />
      <doorsWithCollisionWhenClosed />
      <driveableDoors />
      <bumpersNeedToCollideWithMap value="false" />
      <needsRopeTexture value="false" />
      <requiredExtras />
      <rewards />
      <cinematicPartCamera>
        <Item>WHEEL_FRONT_RIGHT_CAMERA</Item>
        <Item>WHEEL_FRONT_LEFT_CAMERA</Item>
        <Item>WHEEL_REAR_RIGHT_CAMERA</Item>
        <Item>WHEEL_REAR_LEFT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet />
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000" />
      <buoyancySphereSizeScale value="1.000000" />
      <pOverrideRagdollThreshold type="NULL" />
      <firstPersonDrivebyData>
        <Item>LOW_ITALIGTO_FRONT_LEFT</Item>
        <Item>LOW_ITALIGTO_FRONT_RIGHT</Item>
      </firstPersonDrivebyData>
    </Item>
    <Item>
      <modelName>Jackgpr</modelName>
      <txdName>Jackgpr</txdName>
      <handlingId>JACKGPR</handlingId>
      <gameName>JACKGPR</gameName>
      <vehicleMakeName>OCELOT</vehicleMakeName>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected />
      <ptfxAssetName>null</ptfxAssetName>
      <audioNameHash>RAPIDGT3</audioNameHash>
      <layout>LAYOUT_STD_LOWROOF</layout>
      <coverBoundOffsets>JACKAL_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout />
      <cameraName>DEFAULT_FOLLOW_VEHICLE_CAMERA</cameraName>
      <aimCameraName>DEFAULT_THIRD_PERSON_VEHICLE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>VEHICLE_BONNET_CAMERA_MID</bonnetCameraName>
      <povCameraName>DEFAULT_POV_CAMERA</povCameraName>
      <FirstPersonDriveByIKOffset x="0.000000" y="-0.055000" z="-0.030000" />
      <FirstPersonDriveByUnarmedIKOffset x="0.000000" y="-0.010000" z="-0.020000" />
      <FirstPersonProjectileDriveByIKOffset x="0.000000" y="-0.100000" z="-0.060000" />
      <FirstPersonProjectileDriveByPassengerIKOffset x="-0.080000" y="-0.100000" z="-0.060000" />
      <FirstPersonProjectileDriveByRearLeftIKOffset x="0.000000" y="0.020000" z="0.000000" />
      <FirstPersonProjectileDriveByRearRightIKOffset x="0.000000" y="0.020000" z="0.000000" />
      <FirstPersonDriveByLeftPassengerIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonDriveByRightPassengerIKOffset x="0.000000" y="-0.055000" z="-0.030000" />
      <FirstPersonDriveByRightRearPassengerIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonDriveByLeftPassengerUnarmedIKOffset x="0.030000" y="-0.020000" z="0.020000" />
      <FirstPersonDriveByRightPassengerUnarmedIKOffset x="0.000000" y="-0.020000" z="0.020000" />
      <FirstPersonMobilePhoneOffset x="0.125000" y="0.253000" z="0.505000" />
      <FirstPersonPassengerMobilePhoneOffset x="0.136000" y="0.223000" z="0.385000" />
      <FirstPersonMobilePhoneSeatIKOffset>
        <Item>
          <Offset x="0.136000" y="0.106000" z="0.425000" />
          <SeatIndex value="2" />
        </Item>
        <Item>
          <Offset x="0.136000" y="0.106000" z="0.425000" />
          <SeatIndex value="3" />
        </Item>
      </FirstPersonMobilePhoneSeatIKOffset>
      <PovCameraOffset x="0.000000" y="-0.200000" z="0.640000" />
      <PovCameraVerticalAdjustmentForRollCage value="0.000000" />
      <PovPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000" />
      <PovRearPassengerCameraOffset x="0.000000" y="-0.050000" z="0.025000" />
      <vfxInfoName>VFXVEHICLEINFO_CAR_GENERIC</vfxInfoName>
      <shouldUseCinematicViewMode value="true" />
      <shouldCameraTransitionOnClimbUpDown value="false" />
      <shouldCameraIgnoreExiting value="false" />
      <AllowPretendOccupants value="true" />
      <AllowJoyriding value="true" />
      <AllowSundayDriving value="true" />
      <AllowBodyColorMapping value="true" />
      <wheelScale value="0.274000" />
      <wheelScaleRear value="0.274000" />
      <dirtLevelMin value="0.000000" />
      <dirtLevelMax value="0.550000" />
      <envEffScaleMin value="0.000000" />
      <envEffScaleMax value="1.000000" />
      <envEffScaleMin2 value="0.000000" />
      <envEffScaleMax2 value="1.000000" />
      <damageMapScale value="0.300000" />
      <damageOffsetScale value="1.000000" />
      <diffuseTint value="0x00FFFFFF" />
      <steerWheelMult value="1.000000" />
      <HDTextureDist value="5.000000" />
      <lodDistances content="float_array">
        15.000000
        30.000000
        75.000000
        200.000000
        500.000000
        500.000000
      </lodDistances>
      <minSeatHeight value="0.814" />
      <identicalModelSpawnDistance value="20" />
      <maxNumOfSameColor value="10" />
      <defaultBodyHealth value="1000.000000" />
      <pretendOccupantsScale value="1.000000" />
      <visibleSpawnDistScale value="1.000000" />
      <trackerPathWidth value="2.000000" />
      <weaponForceMult value="1.000000" />
      <frequency value="100" />
      <swankness>SWANKNESS_3</swankness>
      <maxNum value="999" />
      <flags>FLAG_SPORTS FLAG_SPAWN_ON_TRAILER FLAG_RICH_CAR FLAG_AVERAGE_CAR
        FLAG_HAS_INTERIOR_EXTRAS</flags>
      <type>VEHICLE_TYPE_CAR</type>
      <plateType>VPT_BACK_PLATES</plateType>
      <dashboardType>VDT_TAILGATER</dashboardType>
      <vehicleClass>VC_COUPE</vehicleClass>
      <wheelType>VWT_SPORT</wheelType>
      <trailers />
      <additionalTrailers />
      <drivers />
      <extraIncludes />
      <doorsWithCollisionWhenClosed />
      <driveableDoors />
      <bumpersNeedToCollideWithMap value="false" />
      <needsRopeTexture value="false" />
      <requiredExtras />
      <rewards />
      <cinematicPartCamera>
        <Item>WHEEL_FRONT_RIGHT_CAMERA</Item>
        <Item>WHEEL_FRONT_LEFT_CAMERA</Item>
        <Item>WHEEL_REAR_RIGHT_CAMERA</Item>
        <Item>WHEEL_REAR_LEFT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet />
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000" />
      <buoyancySphereSizeScale value="1.000000" />
      <pOverrideRagdollThreshold type="NULL" />
      <firstPersonDrivebyData>
        <Item>STD_PRIMO_FRONT_LEFT</Item>
        <Item>STD_PRIMO_FRONT_RIGHT</Item>
        <Item>STD_FELON_REAR_LEFT</Item>
        <Item>STD_FELON_REAR_RIGHT</Item>
      </firstPersonDrivebyData>
    </Item>
    <Item>
      <modelName>jestgpr</modelName>
      <txdName>jestgpr</txdName>
      <handlingId>JESTGPR</handlingId>
      <gameName>JESTGPR</gameName>
      <vehicleMakeName>DINKA</vehicleMakeName>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected />
      <ptfxAssetName>null</ptfxAssetName>
      <audioNameHash>JESTER4</audioNameHash>
      <layout>LAYOUT_LOW</layout>
      <coverBoundOffsets>JESTER3_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout />
      <cameraName>FOLLOW_CHEETAH_CAMERA</cameraName>
      <aimCameraName>BOX_VEHICLE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>VEHICLE_BONNET_CAMERA_LOW</bonnetCameraName>
      <povCameraName>REDUCED_NEAR_CLIP_POV_CAMERA</povCameraName>
      <FirstPersonDriveByIKOffset x="0.000000" y="-0.160000" z="-0.030000" />
      <FirstPersonDriveByUnarmedIKOffset x="0.000000" y="0.020000" z="-0.020000" />
      <FirstPersonDriveByRightPassengerUnarmedIKOffset x="0.000000" y="-0.030000" z="0.020000" />
      <FirstPersonDriveByRightPassengerUnarmedIKOffset x="0.000000" y="0.020000" z="-0.020000" />
      <FirstPersonProjectileDriveByIKOffset x="0.090000" y="-0.130000" z="-0.070000" />
      <FirstPersonProjectileDriveByPassengerIKOffset x="-0.130000" y="-0.140000" z="-0.050000" />
      <FirstPersonDriveByRightPassengerIKOffset x="-0.020000" y="-0.05000" z="0.000000" />
      <FirstPersonMobilePhoneOffset x="0.195000" y="0.160000" z="0.500000" />
      <FirstPersonPassengerMobilePhoneOffset x="0.235000" y="0.140000" z="0.390000" />
      <FirstPersonMobilePhoneSeatIKOffset />
      <PovCameraOffset x="0.025000" y="-0.245000" z="0.600000" />
      <PovCameraVerticalAdjustmentForRollCage value="-0.00900" />
      <PovPassengerCameraOffset x="0.000000" y="-0.050000" z="0.000000" />
      <vfxInfoName>VFXVEHICLEINFO_CAR_GENERIC</vfxInfoName>
      <shouldUseCinematicViewMode value="true" />
      <shouldCameraTransitionOnClimbUpDown value="false" />
      <shouldCameraIgnoreExiting value="false" />
      <AllowPretendOccupants value="true" />
      <AllowJoyriding value="false" />
      <AllowSundayDriving value="false" />
      <AllowBodyColorMapping value="true" />
      <wheelScale value="0.281000" />
      <wheelScaleRear value="0.281000" />
      <dirtLevelMin value="0.000000" />
      <dirtLevelMax value="1.000000" />
      <envEffScaleMin value="0.300000" />
      <envEffScaleMax value="0.400000" />
      <envEffScaleMin2 value="0.000000" />
      <envEffScaleMax2 value="1.000000" />
      <damageMapScale value="0.400000" />
      <damageOffsetScale value="0.400000" />
      <diffuseTint value="0x00FFFFFF" />
      <steerWheelMult value="0.50000" />
      <HDTextureDist value="5.000000" />
      <lodDistances content="float_array">
        15.000000
        25.000000
        70.000000
        140.000000
        500.000000
        500.000000
      </lodDistances>
      <minSeatHeight value="0.988" />
      <identicalModelSpawnDistance value="20" />
      <maxNumOfSameColor value="10" />
      <defaultBodyHealth value="1000.000000" />
      <pretendOccupantsScale value="1.000000" />
      <visibleSpawnDistScale value="1.000000" />
      <trackerPathWidth value="2.000000" />
      <weaponForceMult value="1.000000" />
      <frequency value="5" />
      <swankness>SWANKNESS_1</swankness>
      <maxNum value="1" />
      <flags>FLAG_SPORTS FLAG_SPAWN_ON_TRAILER FLAG_RICH_CAR FLAG_NO_BROKEN_DOWN_SCENARIO
        FLAG_CAN_HAVE_NEONS FLAG_INCREASE_CAMBER_WITH_SUSPENSION_MOD</flags>
      <type>VEHICLE_TYPE_CAR</type>
      <plateType>VPT_BACK_PLATES</plateType>
      <dashboardType>VDT_BANSHEE</dashboardType>
      <vehicleClass>VC_SPORT</vehicleClass>
      <wheelType>VWT_MUSCLE</wheelType>
      <trailers />
      <additionalTrailers />
      <drivers />
      <extraIncludes />
      <doorsWithCollisionWhenClosed>
        <Item>VEH_EXT_BOOT</Item>
      </doorsWithCollisionWhenClosed>
      <driveableDoors />
      <bumpersNeedToCollideWithMap value="false" />
      <needsRopeTexture value="true" />
      <requiredExtras />
      <rewards />
      <cinematicPartCamera>
        <Item>WHEEL_WIDE_REAR_RIGHT_CAMERA</Item>
        <Item>WHEEL_WIDE_REAR_LEFT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet>Truck</NmBraceOverrideSet>
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000" />
      <buoyancySphereSizeScale value="1.000000" />
      <pOverrideRagdollThreshold type="NULL" />
      <firstPersonDrivebyData>
        <Item>LOW_JESTER4_FRONT_LEFT</Item>
        <Item>LOW_JESTER4_FRONT_RIGHT</Item>
      </firstPersonDrivebyData>
      <numSeatsOverride value="2" />
    </Item>
    <Item>
      <modelName>lynxgpr</modelName>
      <txdName>lynxgpr</txdName>
      <handlingId>LYNXGPR</handlingId>
      <gameName>LYNXGPR</gameName>
      <vehicleMakeName>OCELOT</vehicleMakeName>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected />
      <ptfxAssetName>null</ptfxAssetName>
      <audioNameHash>XA21</audioNameHash>
      <layout>LAYOUT_LOW</layout>
      <coverBoundOffsets>LYNX_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout />
      <cameraName>FOLLOW_CHEETAH_CAMERA</cameraName>
      <aimCameraName>DEFAULT_THIRD_PERSON_VEHICLE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>VEHICLE_BONNET_CAMERA_LOW_LOW</bonnetCameraName>
      <povCameraName>DEFAULT_POV_CAMERA</povCameraName>
      <FirstPersonDriveByIKOffset x="0.000000" y="-0.060000" z="-0.035000" />
      <FirstPersonDriveByUnarmedIKOffset x="-0.050000" y="-0.100000" z="0.000000" />
      <FirstPersonProjectileDriveByIKOffset x="0.050000" y="-0.100000" z="-0.030000" />
      <FirstPersonProjectileDriveByPassengerIKOffset x="-0.050000" y="-0.100000" z="-0.030000" />
      <FirstPersonProjectileDriveByRearLeftIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonProjectileDriveByRearRightIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonDriveByLeftPassengerIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonDriveByRightPassengerIKOffset x="0.000000" y="-0.060000" z="-0.035000" />
      <FirstPersonDriveByRightRearPassengerIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonDriveByLeftPassengerUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonDriveByRightPassengerUnarmedIKOffset x="0.050000" y="-0.100000" z="0.000000" />
      <FirstPersonMobilePhoneOffset x="0.158000" y="0.143000" z="0.496000" />
      <FirstPersonPassengerMobilePhoneOffset x="0.159000" y="0.126000" z="0.393000" />
      <PovCameraOffset x="0.000000" y="-0.260000" z="0.605000" />
      <PovCameraVerticalAdjustmentForRollCage value="0.000000" />
      <PovPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000" />
      <PovRearPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000" />
      <vfxInfoName>VFXVEHICLEINFO_CAR_GENERIC</vfxInfoName>
      <shouldUseCinematicViewMode value="true" />
      <shouldCameraTransitionOnClimbUpDown value="false" />
      <shouldCameraIgnoreExiting value="false" />
      <AllowPretendOccupants value="true" />
      <AllowJoyriding value="true" />
      <AllowSundayDriving value="true" />
      <AllowBodyColorMapping value="true" />
      <wheelScale value="0.279000" />
      <wheelScaleRear value="0.279000" />
      <dirtLevelMin value="0.000000" />
      <dirtLevelMax value="0.400000" />
      <envEffScaleMin value="0.000000" />
      <envEffScaleMax value="1.000000" />
      <envEffScaleMin2 value="0.000000" />
      <envEffScaleMax2 value="1.000000" />
      <damageMapScale value="0.40000" />
      <damageOffsetScale value="0.40000" />
      <diffuseTint value="0x00FFFFFF" />
      <steerWheelMult value="1.000000" />
      <HDTextureDist value="5.000000" />
      <lodDistances content="float_array">
        30.000000
        60.000000
        90.000000
        120.000000
        500.000000
        500.000000
      </lodDistances>
      <identicalModelSpawnDistance value="20" />
      <maxNumOfSameColor value="1" />
      <defaultBodyHealth value="1000.000000" />
      <pretendOccupantsScale value="1.000000" />
      <visibleSpawnDistScale value="1.000000" />
      <trackerPathWidth value="2.000000" />
      <weaponForceMult value="1.000000" />
      <frequency value="30" />
      <swankness>SWANKNESS_5</swankness>
      <maxNum value="4" />
      <flags>FLAG_SPORTS FLAG_RICH_CAR FLAG_RECESSED_HEADLIGHT_CORONAS FLAG_NO_BROKEN_DOWN_SCENARIO
        FLAG_INCREASE_CAMBER_WITH_SUSPENSION_MOD</flags>
      <type>VEHICLE_TYPE_CAR</type>
      <plateType>VPT_BACK_PLATES</plateType>
      <dashboardType>VDT_SUPERGT</dashboardType>
      <vehicleClass>VC_SPORT</vehicleClass>
      <wheelType>VWT_HIEND</wheelType>
      <trailers />
      <additionalTrailers />
      <drivers />
      <extraIncludes />
      <doorsWithCollisionWhenClosed />
      <driveableDoors />
      <bumpersNeedToCollideWithMap value="false" />
      <needsRopeTexture value="false" />
      <requiredExtras />
      <rewards />
      <cinematicPartCamera>
        <Item>WHEEL_FRONT_RIGHT_CAMERA</Item>
        <Item>WHEEL_FRONT_LEFT_CAMERA</Item>
        <Item>WHEEL_REAR_RIGHT_CAMERA</Item>
        <Item>WHEEL_REAR_LEFT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet />
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000" />
      <buoyancySphereSizeScale value="1.000000" />
      <pOverrideRagdollThreshold type="NULL" />
      <firstPersonDrivebyData>
        <Item>LOW_LYNX_FRONT_LEFT</Item>
        <Item>LOW_LYNX_FRONT_RIGHT</Item>
      </firstPersonDrivebyData>
    </Item>
    <Item>
      <modelName>coquette4c</modelName>
      <txdName>coquette4c</txdName>
      <handlingId>COQUETTE4C</handlingId>
      <gameName>COQUETTE4C</gameName>
      <vehicleMakeName>INVERTO</vehicleMakeName>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected />
      <ptfxAssetName>weap_xs_vehicle_weapons</ptfxAssetName>
      <audioNameHash>sultanrsv8</audioNameHash>
      <layout>LAYOUT_LOW_RESTRICTED</layout>
      <coverBoundOffsets>COQUETTE4_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout />
      <cameraName>FOLLOW_CHEETAH_CAMERA</cameraName>
      <aimCameraName>DEFAULT_THIRD_PERSON_VEHICLE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>VEHICLE_BONNET_CAMERA_STANDARD_LONG_EXTRA_LOW</bonnetCameraName>
      <povCameraName>REDUCED_NEAR_CLIP_POV_CAMERA</povCameraName>
      <FirstPersonDriveByIKOffset x="0.020000" y="-0.100000" z="-0.010000" />
      <FirstPersonDriveByUnarmedIKOffset x="0.000000" y="-0.130000" z="-0.020000" />
      <FirstPersonProjectileDriveByIKOffset x="0.060000" y="-0.180000" z="-0.040000" />
      <FirstPersonProjectileDriveByPassengerIKOffset x="-0.060000" y="-0.180000" z="-0.040000" />
      <FirstPersonDriveByRightPassengerIKOffset x="-0.020000" y="-0.100000" z="-0.010000" />
      <FirstPersonDriveByRightPassengerUnarmedIKOffset x="-0.020000" y="-0.140000" z="-0.020000" />
      <FirstPersonMobilePhoneOffset x="0.133000" y="0.115000" z="0.495000" />
      <FirstPersonPassengerMobilePhoneOffset x="0.200000" y="0.060000" z="0.385000" />
      <FirstPersonMobilePhoneSeatIKOffset />
      <PovCameraOffset x="0.000000" y="-0.305000" z="0.613000" />
      <PovCameraVerticalAdjustmentForRollCage value="0.000000" />
      <PovPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000" />
      <vfxInfoName>VFXVEHICLEINFO_CAR_GENERIC</vfxInfoName>
      <shouldUseCinematicViewMode value="true" />
      <shouldCameraTransitionOnClimbUpDown value="false" />
      <shouldCameraIgnoreExiting value="false" />
      <AllowPretendOccupants value="true" />
      <AllowJoyriding value="true" />
      <AllowSundayDriving value="true" />
      <AllowBodyColorMapping value="true" />
      <wheelScale value="0.263890" />
      <wheelScaleRear value="0.280690" />
      <dirtLevelMin value="0.000000" />
      <dirtLevelMax value="0.300000" />
      <envEffScaleMin value="0.000000" />
      <envEffScaleMax value="1.000000" />
      <envEffScaleMin2 value="0.000000" />
      <envEffScaleMax2 value="1.000000" />
      <damageMapScale value="0.350000" />
      <damageOffsetScale value="0.350000" />
      <diffuseTint value="0x00FFFFFF" />
      <steerWheelMult value="1.000000" />
      <HDTextureDist value="5.000000" />
      <lodDistances content="float_array">
        15.000000
        30.000000
        60.000000
        120.000000
        500.000000
        500.000000
      </lodDistances>
      <minSeatHeight value="0.844" />
      <identicalModelSpawnDistance value="20" />
      <maxNumOfSameColor value="1" />
      <defaultBodyHealth value="1000.000000" />
      <pretendOccupantsScale value="1.000000" />
      <visibleSpawnDistScale value="1.000000" />
      <trackerPathWidth value="2.000000" />
      <weaponForceMult value="1.000000" />
      <frequency value="20" />
      <swankness>SWANKNESS_5</swankness>
      <maxNum value="3" />
      <flags>FLAG_SPORTS FLAG_RICH_CAR FLAG_PARKING_SENSORS FLAG_EXTRAS_CONVERTIBLE
        FLAG_RECESSED_HEADLIGHT_CORONAS FLAG_NO_BROKEN_DOWN_SCENARIO FLAG_COUNT_AS_FACEBOOK_DRIVEN
        FLAG_CAN_HAVE_NEONS FLAG_BOOT_IN_FRONT FLAG_EXTRAS_STRONG</flags>
      <type>VEHICLE_TYPE_CAR</type>
      <plateType>VPT_BACK_PLATES</plateType>
      <dashboardType>VDT_RACE</dashboardType>
      <vehicleClass>VC_SPORT</vehicleClass>
      <wheelType>VWT_SUPERMOD5</wheelType>
      <trailers />
      <additionalTrailers />
      <drivers />
      <extraIncludes />
      <doorsWithCollisionWhenClosed />
      <driveableDoors />
      <bumpersNeedToCollideWithMap value="false" />
      <needsRopeTexture value="false" />
      <requiredExtras />
      <rewards />
      <cinematicPartCamera>
        <Item>WHEEL_FRONT_RIGHT_CAMERA</Item>
        <Item>WHEEL_FRONT_LEFT_CAMERA</Item>
        <Item>WHEEL_REAR_RIGHT_CAMERA</Item>
        <Item>WHEEL_REAR_LEFT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet />
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000" />
      <buoyancySphereSizeScale value="1.000000" />
      <pOverrideRagdollThreshold type="NULL" />
      <firstPersonDrivebyData>
        <Item>LOW_COQUETTE4_FRONT_LEFT</Item>
        <Item>LOW_COQUETTE4_FRONT_RIGHT</Item>
      </firstPersonDrivebyData>
    </Item>
    <Item>
      <modelName>majimagt</modelName>
      <txdName>majimagt</txdName>
      <handlingId>MAJIMAGT</handlingId>
      <gameName>MAJIMAGT</gameName>
      <vehicleMakeName>ANNIS</vehicleMakeName>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected />
      <ptfxAssetName>null</ptfxAssetName>
      <audioNameHash>MAJIMAGT</audioNameHash>
      <layout>LAYOUT_LOW</layout>
      <coverBoundOffsets>TYRUS_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout />
      <cameraName>FOLLOW_CHEETAH_CAMERA</cameraName>
      <aimCameraName>DEFAULT_THIRD_PERSON_VEHICLE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>VEHICLE_BONNET_CAMERA_LOW_LOW</bonnetCameraName>
      <povCameraName>TYRUS_POV_CAMERA</povCameraName>
      <FirstPersonDriveByIKOffset x="0.000000" y="-0.060000" z="-0.020000" />
      <FirstPersonDriveByUnarmedIKOffset x="-0.050000" y="-0.100000" z="0.000000" />
      <FirstPersonProjectileDriveByIKOffset x="0.070000" y="-0.140000" z="-0.030000" />
      <FirstPersonProjectileDriveByPassengerIKOffset x="-0.160000" y="-0.100000" z="-0.030000" />
      <FirstPersonProjectileDriveByRearLeftIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonProjectileDriveByRearRightIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonDriveByLeftPassengerIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonDriveByRightPassengerIKOffset x="0.000000" y="-0.060000" z="-0.035000" />
      <FirstPersonDriveByRightRearPassengerIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonDriveByLeftPassengerUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonDriveByRightPassengerUnarmedIKOffset x="0.050000" y="-0.100000" z="0.000000" />
      <FirstPersonMobilePhoneOffset x="0.148000" y="0.188000" z="0.491000" />
      <FirstPersonPassengerMobilePhoneOffset x="0.159000" y="0.126000" z="0.393000" />
      <PovCameraOffset x="0.000000" y="-0.260000" z="0.670000" />
      <PovCameraVerticalAdjustmentForRollCage value="0.000000" />
      <PovPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000" />
      <PovRearPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000" />
      <vfxInfoName>VFXVEHICLEINFO_CAR_GENERIC</vfxInfoName>
      <shouldUseCinematicViewMode value="true" />
      <shouldCameraTransitionOnClimbUpDown value="false" />
      <shouldCameraIgnoreExiting value="false" />
      <AllowPretendOccupants value="true" />
      <AllowJoyriding value="true" />
      <AllowSundayDriving value="true" />
      <AllowBodyColorMapping value="true" />
      <wheelScale value="0.25200" />
      <wheelScaleRear value="0.252000" />
      <dirtLevelMin value="0.000000" />
      <dirtLevelMax value="0.400000" />
      <envEffScaleMin value="0.000000" />
      <envEffScaleMax value="1.000000" />
      <envEffScaleMin2 value="0.000000" />
      <envEffScaleMax2 value="1.000000" />
      <damageMapScale value="0.40000" />
      <damageOffsetScale value="0.40000" />
      <diffuseTint value="0x00FFFFFF" />
      <steerWheelMult value="1.000000" />
      <HDTextureDist value="5.000000" />
      <lodDistances content="float_array">
        20.000000
        40.000000
        80.000000
        120.000000
        500.000000
        500.000000
      </lodDistances>
      <identicalModelSpawnDistance value="80" />
      <maxNumOfSameColor value="1" />
      <defaultBodyHealth value="1000.000000" />
      <pretendOccupantsScale value="1.000000" />
      <visibleSpawnDistScale value="1.000000" />
      <trackerPathWidth value="2.000000" />
      <weaponForceMult value="1.000000" />
      <frequency value="5" />
      <swankness>SWANKNESS_5</swankness>
      <maxNum value="1" />
      <flags>FLAG_SPORTS FLAG_RICH_CAR FLAG_NO_BROKEN_DOWN_SCENARIO</flags>
      <type>VEHICLE_TYPE_CAR</type>
      <plateType>VPT_BACK_PLATES</plateType>
      <dashboardType>VDT_FELTZER</dashboardType>
      <vehicleClass>VC_SUPER</vehicleClass>
      <wheelType>VWT_HIEND</wheelType>
      <trailers />
      <additionalTrailers />
      <drivers />
      <extraIncludes />
      <doorsWithCollisionWhenClosed />
      <driveableDoors />
      <bumpersNeedToCollideWithMap value="false" />
      <needsRopeTexture value="false" />
      <requiredExtras>EXTRA_2</requiredExtras>
      <rewards />
      <cinematicPartCamera>
        <Item>WHEEL_FRONT_RIGHT_CAMERA</Item>
        <Item>WHEEL_FRONT_LEFT_CAMERA</Item>
        <Item>WHEEL_REAR_RIGHT_CAMERA</Item>
        <Item>WHEEL_REAR_LEFT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet />
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000" />
      <buoyancySphereSizeScale value="1.000000" />
      <pOverrideRagdollThreshold type="NULL" />
      <firstPersonDrivebyData>
        <Item>LOW_TYRUS_FRONT_LEFT</Item>
        <Item>LOW_TYRUS_FRONT_RIGHT</Item>
      </firstPersonDrivebyData>
    </Item>
    <Item>
      <modelName>meteor</modelName>
      <txdName>meteor</txdName>
      <handlingId>METEOR</handlingId>
      <gameName>METEOR</gameName>
      <vehicleMakeName>PFISTER</vehicleMakeName>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected />
      <ptfxAssetName>null</ptfxAssetName>
      <audioNameHash>sultanrsv8</audioNameHash>
      <layout>LAYOUT_LOW_RESTRICTED</layout>
      <coverBoundOffsets>811_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout />
      <cameraName>DEFAULT_FOLLOW_VEHICLE_CAMERA</cameraName>
      <aimCameraName>DEFAULT_THIRD_PERSON_VEHICLE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>VEHICLE_BONNET_CAMERA_RAPIDGT</bonnetCameraName>
      <povCameraName>REDUCED_NEAR_CLIP_POV_CAMERA_NO_REVERSE</povCameraName>
      <FirstPersonDriveByIKOffset x="0.000000" y="-0.060000" z="-0.030000" />
      <FirstPersonDriveByUnarmedIKOffset x="0.000000" y="-0.120000" z="-0.020000" />
      <FirstPersonProjectileDriveByIKOffset x="0.000000" y="-0.060000" z="0.000000" />
      <FirstPersonProjectileDriveByPassengerIKOffset x="0.000000" y="-0.060000" z="0.000000" />
      <FirstPersonDriveByLeftPassengerIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonDriveByRightPassengerIKOffset x="0.000000" y="-0.060000" z="-0.030000" />
      <FirstPersonDriveByLeftPassengerUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonDriveByRightPassengerUnarmedIKOffset x="0.000000" y="-0.120000" z="-0.020000" />
      <FirstPersonMobilePhoneOffset x="0.146000" y="0.221000" z="0.549000" />
      <FirstPersonPassengerMobilePhoneOffset x="0.161000" y="0.146000" z="0.453000" />
      <PovCameraOffset x="0.000000" y="-0.230000" z="0.680000" />
      <PovCameraVerticalAdjustmentForRollCage value="0.000000" />
      <PovPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000" />
      <vfxInfoName>VFXVEHICLEINFO_CAR_GENERIC</vfxInfoName>
      <shouldUseCinematicViewMode value="true" />
      <shouldCameraTransitionOnClimbUpDown value="false" />
      <shouldCameraIgnoreExiting value="false" />
      <AllowPretendOccupants value="true" />
      <AllowJoyriding value="true" />
      <AllowSundayDriving value="true" />
      <AllowBodyColorMapping value="true" />
      <wheelScale value="0.296600" />
      <wheelScaleRear value="0.296600" />
      <dirtLevelMin value="0.000000" />
      <dirtLevelMax value="0.300000" />
      <envEffScaleMin value="0.000000" />
      <envEffScaleMax value="1.000000" />
      <envEffScaleMin2 value="0.000000" />
      <envEffScaleMax2 value="1.000000" />
      <damageMapScale value="0.500000" />
      <damageOffsetScale value="0.500000" />
      <diffuseTint value="0x00FFFFFF" />
      <steerWheelMult value="1.000000" />
      <HDTextureDist value="5.000000" />
      <lodDistances content="float_array">
        20.000000
        40.000000
        80.000000
        120.000000
        500.000000
        500.000000
      </lodDistances>
      <minSeatHeight value="0.784" />
      <identicalModelSpawnDistance value="80" />
      <maxNumOfSameColor value="1" />
      <defaultBodyHealth value="1000.000000" />
      <pretendOccupantsScale value="1.000000" />
      <visibleSpawnDistScale value="1.000000" />
      <trackerPathWidth value="2.000000" />
      <weaponForceMult value="1.000000" />
      <frequency value="5" />
      <swankness>SWANKNESS_5</swankness>
      <maxNum value="1" />
      <flags>FLAG_SPORTS FLAG_RICH_CAR FLAG_NO_BROKEN_DOWN_SCENARIO FLAG_RECESSED_HEADLIGHT_CORONAS
        FLAG_RECESSED_TAILLIGHT_CORONAS FLAG_EXTRAS_CONVERTIBLE FLAG_EXTRAS_STRONG FLAG_FRONT_BOOT</flags>
      <type>VEHICLE_TYPE_CAR</type>
      <plateType>VPT_BACK_PLATES</plateType>
      <dashboardType>VDT_BANSHEE</dashboardType>
      <vehicleClass>VC_SUPER</vehicleClass>
      <wheelType>VWT_HIEND</wheelType>
      <trailers />
      <additionalTrailers />
      <drivers />
      <extraIncludes />
      <doorsWithCollisionWhenClosed />
      <driveableDoors />
      <bumpersNeedToCollideWithMap value="false" />
      <needsRopeTexture value="false" />
      <requiredExtras />
      <rewards />
      <cinematicPartCamera>
        <Item>WHEEL_FRONT_RIGHT_CAMERA</Item>
        <Item>WHEEL_FRONT_LEFT_CAMERA</Item>
        <Item>WHEEL_REAR_RIGHT_CAMERA</Item>
        <Item>WHEEL_REAR_LEFT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet />
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000" />
      <buoyancySphereSizeScale value="1.000000" />
      <pOverrideRagdollThreshold type="NULL" />
      <firstPersonDrivebyData>
        <Item>LOW_811_FRONT_LEFT</Item>
        <Item>LOW_811_FRONT_RIGHT</Item>
      </firstPersonDrivebyData>
    </Item>
    <Item>
      <modelName>pantor</modelName>
      <txdName>pantor</txdName>
      <handlingId>PANTOR</handlingId>
      <gameName>PANTOR</gameName>
      <vehicleMakeName>BENEFAC</vehicleMakeName>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected />
      <ptfxAssetName>null</ptfxAssetName>
      <audioNameHash>PANTO</audioNameHash>
      <layout>LAYOUT_STD_PANTO</layout>
      <coverBoundOffsets>PANTO_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout />
      <cameraName>DEFAULT_FOLLOW_VEHICLE_CAMERA</cameraName>
      <aimCameraName>DEFAULT_THIRD_PERSON_VEHICLE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>VEHICLE_BONNET_CAMERA_MID</bonnetCameraName>
      <povCameraName>DEFAULT_POV_CAMERA</povCameraName>
      <FirstPersonDriveByIKOffset x="0.000000" y="-0.020000" z="-0.040000" />
      <FirstPersonDriveByUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonProjectileDriveByIKOffset x="0.088000" y="-0.103000" z="-0.080000" />
      <FirstPersonProjectileDriveByPassengerIKOffset x="-0.063000" y="-0.115000" z="-0.035000" />
      <FirstPersonDriveByLeftPassengerIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonDriveByRightPassengerIKOffset x="0.000000" y="-0.035000" z="-0.030000" />
      <FirstPersonDriveByLeftPassengerUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonDriveByRightPassengerUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonPassengerMobilePhoneOffset x="0.153000" y="0.228000" z="0.460000" />
      <FirstPersonMobilePhoneOffset x="0.103000" y="0.270000" z="0.545000" />
      <PovCameraOffset x="0.000000" y="-0.125000" z="0.685000" />
      <PovCameraVerticalAdjustmentForRollCage value="0.000000" />
      <PovPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000" />
      <vfxInfoName>VFXVEHICLEINFO_CAR_GENERIC</vfxInfoName>
      <shouldUseCinematicViewMode value="true" />
      <shouldCameraTransitionOnClimbUpDown value="false" />
      <shouldCameraIgnoreExiting value="false" />
      <AllowPretendOccupants value="true" />
      <AllowJoyriding value="true" />
      <AllowSundayDriving value="true" />
      <AllowBodyColorMapping value="true" />
      <wheelScale value="0.211247" />
      <wheelScaleRear value="0.211247" />
      <dirtLevelMin value="0.000000" />
      <dirtLevelMax value="0.800000" />
      <envEffScaleMin value="0.000000" />
      <envEffScaleMax value="1.000000" />
      <envEffScaleMin2 value="0.000000" />
      <envEffScaleMax2 value="1.000000" />
      <damageMapScale value="0.300000" />
      <damageOffsetScale value="0.300000" />
      <diffuseTint value="0x00FFFFFF" />
      <steerWheelMult value="1.000000" />
      <HDTextureDist value="5.000000" />
      <lodDistances content="float_array">
        10.000000
        25.000000
        60.000000
        120.000000
        500.000000
        500.000000
      </lodDistances>
      <minSeatHeight value="0.884" />
      <identicalModelSpawnDistance value="20" />
      <maxNumOfSameColor value="10" />
      <defaultBodyHealth value="1000.000000" />
      <pretendOccupantsScale value="1.000000" />
      <visibleSpawnDistScale value="1.000000" />
      <trackerPathWidth value="2.000000" />
      <weaponForceMult value="1.000000" />
      <frequency value="30" />
      <swankness>SWANKNESS_1</swankness>
      <maxNum value="10" />
      <flags>FLAG_EXTRAS_ALL FLAG_EXTRAS_STRONG FLAG_POOR_CAR</flags>
      <type>VEHICLE_TYPE_CAR</type>
      <plateType>VPT_FRONT_AND_BACK_PLATES</plateType>
      <dashboardType>VDT_CAVALCADE</dashboardType>
      <vehicleClass>VC_COMPACT</vehicleClass>
      <wheelType>VWT_TUNER</wheelType>
      <trailers />
      <additionalTrailers />
      <drivers />
      <extraIncludes />
      <doorsWithCollisionWhenClosed />
      <driveableDoors />
      <bumpersNeedToCollideWithMap value="false" />
      <needsRopeTexture value="false" />
      <requiredExtras />
      <rewards />
      <cinematicPartCamera>
        <Item>WHEEL_FRONT_RIGHT_CAMERA</Item>
        <Item>WHEEL_FRONT_LEFT_CAMERA</Item>
        <Item>WHEEL_REAR_RIGHT_CAMERA</Item>
        <Item>WHEEL_REAR_LEFT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet />
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000" />
      <buoyancySphereSizeScale value="1.000000" />
      <pOverrideRagdollThreshold type="NULL" />
      <firstPersonDrivebyData>
        <Item>PANTO_FRONT_LEFT</Item>
        <Item>PANTO_FRONT_RIGHT</Item>
      </firstPersonDrivebyData>
    </Item>
    <Item>
      <modelName>pentro</modelName>
      <txdName>pentro</txdName>
      <handlingId>PENTRO</handlingId>
      <gameName>PENTRO</gameName>
      <vehicleMakeName>MAIBATSU</vehicleMakeName>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected />
      <ptfxAssetName>null</ptfxAssetName>
      <audioNameHash>FUTO</audioNameHash>
      <layout>LAYOUT_LOW</layout>
      <coverBoundOffsets>PENUMBRA_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout />
      <cameraName>DEFAULT_FOLLOW_VEHICLE_CAMERA</cameraName>
      <aimCameraName>DEFAULT_THIRD_PERSON_VEHICLE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>VEHICLE_BONNET_CAMERA_NEAR_HIGH</bonnetCameraName>
      <povCameraName>DEFAULT_POV_CAMERA_LOOKAROUND</povCameraName>
      <FirstPersonDriveByIKOffset x="0.000000" y="-0.055000" z="0.010000" />
      <FirstPersonDriveByUnarmedIKOffset x="0.000000" y="-0.100000" z="0.000000" />
      <FirstPersonProjectileDriveByIKOffset x="0.020000" y="-0.100000" z="-0.025000" />
      <FirstPersonProjectileDriveByPassengerIKOffset x="-0.050000" y="-0.005000" z="-0.038000" />
      <FirstPersonProjectileDriveByRearLeftIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonProjectileDriveByRearRightIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonDriveByLeftPassengerIKOffset x="0.000000" y="-0.175000" z="-0.078000" />
      <FirstPersonDriveByRightPassengerIKOffset x="0.000000" y="-0.055000" z="0.000000" />
      <FirstPersonDriveByRightRearPassengerIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonDriveByLeftPassengerUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonDriveByRightPassengerUnarmedIKOffset x="0.000000" y="-0.100000" z="0.000000" />
      <FirstPersonMobilePhoneOffset x="0.120000" y="0.275000" z="0.523000" />
      <FirstPersonPassengerMobilePhoneOffset x="0.136000" y="0.158000" z="0.445000" />
      <PovCameraOffset x="0.000000" y="-0.200000" z="0.630000" />
      <PovCameraVerticalAdjustmentForRollCage value="-0.025000" />
      <PovPassengerCameraOffset x="0.000000" y="-0.100000" z="0.030000" />
      <PovRearPassengerCameraOffset x="0.000000" y="0.000000" z="0.030000" />
      <vfxInfoName>VFXVEHICLEINFO_CAR_GENERIC</vfxInfoName>
      <shouldUseCinematicViewMode value="true" />
      <shouldCameraTransitionOnClimbUpDown value="false" />
      <shouldCameraIgnoreExiting value="false" />
      <AllowPretendOccupants value="true" />
      <AllowJoyriding value="true" />
      <AllowSundayDriving value="true" />
      <AllowBodyColorMapping value="true" />
      <wheelScale value="0.280000" />
      <wheelScaleRear value="0.280000" />
      <dirtLevelMin value="0.000000" />
      <dirtLevelMax value="0.700000" />
      <envEffScaleMin value="0.000000" />
      <envEffScaleMax value="1.000000" />
      <envEffScaleMin2 value="0.000000" />
      <envEffScaleMax2 value="1.000000" />
      <damageMapScale value="0.800000" />
      <damageOffsetScale value="0.750000" />
      <diffuseTint value="0x00FFFFFF" />
      <steerWheelMult value="1.000000" />
      <HDTextureDist value="5.000000" />
      <lodDistances content="float_array">
        20.000000
        35.000000
        70.000000
        200.000000
        500.000000
        500.000000
      </lodDistances>
      <minSeatHeight value="0.816" />
      <identicalModelSpawnDistance value="20" />
      <maxNumOfSameColor value="10" />
      <defaultBodyHealth value="1000.000000" />
      <pretendOccupantsScale value="1.000000" />
      <visibleSpawnDistScale value="1.000000" />
      <trackerPathWidth value="2.000000" />
      <weaponForceMult value="1.000000" />
      <frequency value="100" />
      <swankness>SWANKNESS_2</swankness>
      <maxNum value="999" />
      <flags>FLAG_SPAWN_ON_TRAILER FLAG_AVERAGE_CAR FLAG_EXTRAS_ALL</flags>
      <type>VEHICLE_TYPE_CAR</type>
      <plateType>VPT_BACK_PLATES</plateType>
      <dashboardType>VDT_RACE</dashboardType>
      <vehicleClass>VC_SPORT</vehicleClass>
      <wheelType>VWT_SPORT</wheelType>
      <trailers />
      <additionalTrailers />
      <drivers />
      <extraIncludes />
      <doorsWithCollisionWhenClosed />
      <driveableDoors />
      <bumpersNeedToCollideWithMap value="false" />
      <needsRopeTexture value="false" />
      <requiredExtras />
      <rewards />
      <cinematicPartCamera>
        <Item>WHEEL_FRONT_RIGHT_CAMERA</Item>
        <Item>WHEEL_FRONT_LEFT_CAMERA</Item>
        <Item>WHEEL_REAR_RIGHT_CAMERA</Item>
        <Item>WHEEL_REAR_LEFT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet />
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000" />
      <buoyancySphereSizeScale value="1.000000" />
      <pOverrideRagdollThreshold type="NULL" />
      <firstPersonDrivebyData>
        <Item>STD_PENUMBRA_FRONT_LEFT</Item>
        <Item>STD_PENUMBRA_FRONT_RIGHT</Item>
      </firstPersonDrivebyData>
    </Item>
    <Item>
      <modelName>pentro2</modelName>
      <txdName>pentro</txdName>
      <handlingId>PENTRO</handlingId>
      <gameName>PENTRO2</gameName>
      <vehicleMakeName>MAIBATSU</vehicleMakeName>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected />
      <ptfxAssetName>null</ptfxAssetName>
      <audioNameHash>FUTO</audioNameHash>
      <layout>LAYOUT_PENTRO_RHD</layout>
      <coverBoundOffsets>PENTRO_RHD_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout />
      <cameraName>DEFAULT_FOLLOW_VEHICLE_CAMERA</cameraName>
      <aimCameraName>DEFAULT_THIRD_PERSON_VEHICLE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>VEHICLE_BONNET_CAMERA_NEAR_HIGH</bonnetCameraName>
      <povCameraName>DEFAULT_POV_CAMERA_LOOKAROUND</povCameraName>
      <FirstPersonDriveByIKOffset x="0.000000" y="-0.055000" z="0.010000" />
      <FirstPersonDriveByUnarmedIKOffset x="0.000000" y="-0.100000" z="0.000000" />
      <FirstPersonProjectileDriveByIKOffset x="0.020000" y="-0.100000" z="-0.025000" />
      <FirstPersonProjectileDriveByPassengerIKOffset x="-0.050000" y="-0.005000" z="-0.038000" />
      <FirstPersonProjectileDriveByRearLeftIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonProjectileDriveByRearRightIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonDriveByLeftPassengerIKOffset x="0.000000" y="-0.175000" z="-0.078000" />
      <FirstPersonDriveByRightPassengerIKOffset x="0.000000" y="-0.055000" z="0.000000" />
      <FirstPersonDriveByRightRearPassengerIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonDriveByLeftPassengerUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonDriveByRightPassengerUnarmedIKOffset x="0.000000" y="-0.100000" z="0.000000" />
      <FirstPersonMobilePhoneOffset x="0.120000" y="0.275000" z="0.523000" />
      <FirstPersonPassengerMobilePhoneOffset x="0.136000" y="0.158000" z="0.445000" />
      <PovCameraOffset x="0.000000" y="-0.200000" z="0.630000" />
      <PovCameraVerticalAdjustmentForRollCage value="-0.025000" />
      <PovPassengerCameraOffset x="0.000000" y="-0.100000" z="0.030000" />
      <PovRearPassengerCameraOffset x="0.000000" y="0.000000" z="0.030000" />
      <vfxInfoName>VFXVEHICLEINFO_CAR_GENERIC</vfxInfoName>
      <shouldUseCinematicViewMode value="true" />
      <shouldCameraTransitionOnClimbUpDown value="false" />
      <shouldCameraIgnoreExiting value="false" />
      <AllowPretendOccupants value="true" />
      <AllowJoyriding value="true" />
      <AllowSundayDriving value="true" />
      <AllowBodyColorMapping value="true" />
      <wheelScale value="0.280000" />
      <wheelScaleRear value="0.280000" />
      <dirtLevelMin value="0.000000" />
      <dirtLevelMax value="0.700000" />
      <envEffScaleMin value="0.000000" />
      <envEffScaleMax value="1.000000" />
      <envEffScaleMin2 value="0.000000" />
      <envEffScaleMax2 value="1.000000" />
      <damageMapScale value="0.800000" />
      <damageOffsetScale value="0.750000" />
      <diffuseTint value="0x00FFFFFF" />
      <steerWheelMult value="1.000000" />
      <HDTextureDist value="5.000000" />
      <lodDistances content="float_array">
        20.000000
        35.000000
        70.000000
        200.000000
        500.000000
        500.000000
      </lodDistances>
      <minSeatHeight value="0.816" />
      <identicalModelSpawnDistance value="20" />
      <maxNumOfSameColor value="10" />
      <defaultBodyHealth value="1000.000000" />
      <pretendOccupantsScale value="1.000000" />
      <visibleSpawnDistScale value="1.000000" />
      <trackerPathWidth value="2.000000" />
      <weaponForceMult value="1.000000" />
      <frequency value="100" />
      <swankness>SWANKNESS_2</swankness>
      <maxNum value="999" />
      <flags>FLAG_SPAWN_ON_TRAILER FLAG_AVERAGE_CAR FLAG_EXTRAS_ALL</flags>
      <type>VEHICLE_TYPE_CAR</type>
      <plateType>VPT_BACK_PLATES</plateType>
      <dashboardType>VDT_RACE</dashboardType>
      <vehicleClass>VC_SPORT</vehicleClass>
      <wheelType>VWT_SPORT</wheelType>
      <trailers />
      <additionalTrailers />
      <drivers />
      <extraIncludes />
      <doorsWithCollisionWhenClosed />
      <driveableDoors />
      <bumpersNeedToCollideWithMap value="false" />
      <needsRopeTexture value="false" />
      <requiredExtras />
      <rewards />
      <cinematicPartCamera>
        <Item>WHEEL_FRONT_RIGHT_CAMERA</Item>
        <Item>WHEEL_FRONT_LEFT_CAMERA</Item>
        <Item>WHEEL_REAR_RIGHT_CAMERA</Item>
        <Item>WHEEL_REAR_LEFT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet />
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000" />
      <buoyancySphereSizeScale value="1.000000" />
      <pOverrideRagdollThreshold type="NULL" />
      <firstPersonDrivebyData>
        <Item>STD_PENUMBRA_FRONT_LEFT</Item>
        <Item>STD_PENUMBRA_FRONT_RIGHT</Item>
      </firstPersonDrivebyData>
    </Item>
    <Item>
      <modelName>pentro3</modelName>
      <txdName>pentro</txdName>
      <handlingId>PENTRO</handlingId>
      <gameName>PENTRO3</gameName>
      <vehicleMakeName>MAIBATSU</vehicleMakeName>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected />
      <ptfxAssetName>null</ptfxAssetName>
      <audioNameHash>FUTO</audioNameHash>
      <layout>LAYOUT_LOW</layout>
      <coverBoundOffsets>PENUMBRA_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout />
      <cameraName>DEFAULT_FOLLOW_VEHICLE_CAMERA</cameraName>
      <aimCameraName>DEFAULT_THIRD_PERSON_VEHICLE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>VEHICLE_BONNET_CAMERA_NEAR_HIGH</bonnetCameraName>
      <povCameraName>DEFAULT_POV_CAMERA_LOOKAROUND</povCameraName>
      <FirstPersonDriveByIKOffset x="0.000000" y="-0.055000" z="0.010000" />
      <FirstPersonDriveByUnarmedIKOffset x="0.000000" y="-0.100000" z="0.000000" />
      <FirstPersonProjectileDriveByIKOffset x="0.020000" y="-0.100000" z="-0.025000" />
      <FirstPersonProjectileDriveByPassengerIKOffset x="-0.050000" y="-0.005000" z="-0.038000" />
      <FirstPersonProjectileDriveByRearLeftIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonProjectileDriveByRearRightIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonDriveByLeftPassengerIKOffset x="0.000000" y="-0.175000" z="-0.078000" />
      <FirstPersonDriveByRightPassengerIKOffset x="0.000000" y="-0.055000" z="0.000000" />
      <FirstPersonDriveByRightRearPassengerIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonDriveByLeftPassengerUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonDriveByRightPassengerUnarmedIKOffset x="0.000000" y="-0.100000" z="0.000000" />
      <FirstPersonMobilePhoneOffset x="0.120000" y="0.275000" z="0.523000" />
      <FirstPersonPassengerMobilePhoneOffset x="0.136000" y="0.158000" z="0.445000" />
      <PovCameraOffset x="0.000000" y="-0.200000" z="0.630000" />
      <PovCameraVerticalAdjustmentForRollCage value="-0.025000" />
      <PovPassengerCameraOffset x="0.000000" y="-0.100000" z="0.030000" />
      <PovRearPassengerCameraOffset x="0.000000" y="0.000000" z="0.030000" />
      <vfxInfoName>VFXVEHICLEINFO_CAR_GENERIC</vfxInfoName>
      <shouldUseCinematicViewMode value="true" />
      <shouldCameraTransitionOnClimbUpDown value="false" />
      <shouldCameraIgnoreExiting value="false" />
      <AllowPretendOccupants value="true" />
      <AllowJoyriding value="true" />
      <AllowSundayDriving value="true" />
      <AllowBodyColorMapping value="true" />
      <wheelScale value="0.280000" />
      <wheelScaleRear value="0.280000" />
      <dirtLevelMin value="0.000000" />
      <dirtLevelMax value="0.700000" />
      <envEffScaleMin value="0.000000" />
      <envEffScaleMax value="1.000000" />
      <envEffScaleMin2 value="0.000000" />
      <envEffScaleMax2 value="1.000000" />
      <damageMapScale value="0.800000" />
      <damageOffsetScale value="0.750000" />
      <diffuseTint value="0x00FFFFFF" />
      <steerWheelMult value="1.000000" />
      <HDTextureDist value="5.000000" />
      <lodDistances content="float_array">
        20.000000
        35.000000
        70.000000
        200.000000
        500.000000
        500.000000
      </lodDistances>
      <minSeatHeight value="0.816" />
      <identicalModelSpawnDistance value="20" />
      <maxNumOfSameColor value="10" />
      <defaultBodyHealth value="1000.000000" />
      <pretendOccupantsScale value="1.000000" />
      <visibleSpawnDistScale value="1.000000" />
      <trackerPathWidth value="2.000000" />
      <weaponForceMult value="1.000000" />
      <frequency value="100" />
      <swankness>SWANKNESS_2</swankness>
      <maxNum value="999" />
      <flags>FLAG_SPAWN_ON_TRAILER FLAG_AVERAGE_CAR FLAG_EXTRAS_ALL FLAG_EXTRAS_CONVERTIBLE
        FLAG_EXTRAS_STRONG</flags>
      <type>VEHICLE_TYPE_CAR</type>
      <plateType>VPT_BACK_PLATES</plateType>
      <dashboardType>VDT_RACE</dashboardType>
      <vehicleClass>VC_COUPE</vehicleClass>
      <wheelType>VWT_SPORT</wheelType>
      <trailers />
      <additionalTrailers />
      <drivers />
      <extraIncludes />
      <doorsWithCollisionWhenClosed />
      <driveableDoors />
      <bumpersNeedToCollideWithMap value="false" />
      <needsRopeTexture value="false" />
      <requiredExtras />
      <rewards />
      <cinematicPartCamera>
        <Item>WHEEL_FRONT_RIGHT_CAMERA</Item>
        <Item>WHEEL_FRONT_LEFT_CAMERA</Item>
        <Item>WHEEL_REAR_RIGHT_CAMERA</Item>
        <Item>WHEEL_REAR_LEFT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet />
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000" />
      <buoyancySphereSizeScale value="1.000000" />
      <pOverrideRagdollThreshold type="NULL" />
      <firstPersonDrivebyData>
        <Item>STD_PENUMBRA_FRONT_LEFT</Item>
        <Item>STD_PENUMBRA_FRONT_RIGHT</Item>
      </firstPersonDrivebyData>
    </Item>
    <Item>
      <modelName>pentrogpr</modelName>
      <txdName>pentro</txdName>
      <handlingId>PENTROGPR</handlingId>
      <gameName>PENTROGPR</gameName>
      <vehicleMakeName>MAIBATSU</vehicleMakeName>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected />
      <ptfxAssetName>null</ptfxAssetName>
      <audioNameHash>SULTAN</audioNameHash>
      <layout>LAYOUT_LOW</layout>
      <coverBoundOffsets>PENUMBRA_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout />
      <cameraName>DEFAULT_FOLLOW_VEHICLE_CAMERA</cameraName>
      <aimCameraName>DEFAULT_THIRD_PERSON_VEHICLE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>VEHICLE_BONNET_CAMERA_NEAR_HIGH</bonnetCameraName>
      <povCameraName>DEFAULT_POV_CAMERA_LOOKAROUND</povCameraName>
      <FirstPersonDriveByIKOffset x="0.000000" y="-0.055000" z="0.010000" />
      <FirstPersonDriveByUnarmedIKOffset x="0.000000" y="-0.100000" z="0.000000" />
      <FirstPersonProjectileDriveByIKOffset x="0.020000" y="-0.100000" z="-0.025000" />
      <FirstPersonProjectileDriveByPassengerIKOffset x="-0.050000" y="-0.005000" z="-0.038000" />
      <FirstPersonProjectileDriveByRearLeftIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonProjectileDriveByRearRightIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonDriveByLeftPassengerIKOffset x="0.000000" y="-0.175000" z="-0.078000" />
      <FirstPersonDriveByRightPassengerIKOffset x="0.000000" y="-0.055000" z="0.000000" />
      <FirstPersonDriveByRightRearPassengerIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonDriveByLeftPassengerUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonDriveByRightPassengerUnarmedIKOffset x="0.000000" y="-0.100000" z="0.000000" />
      <FirstPersonMobilePhoneOffset x="0.120000" y="0.275000" z="0.523000" />
      <FirstPersonPassengerMobilePhoneOffset x="0.136000" y="0.158000" z="0.445000" />
      <PovCameraOffset x="0.000000" y="-0.200000" z="0.630000" />
      <PovCameraVerticalAdjustmentForRollCage value="-0.025000" />
      <PovPassengerCameraOffset x="0.000000" y="-0.100000" z="0.030000" />
      <PovRearPassengerCameraOffset x="0.000000" y="0.000000" z="0.030000" />
      <vfxInfoName>VFXVEHICLEINFO_CAR_GENERIC</vfxInfoName>
      <shouldUseCinematicViewMode value="true" />
      <shouldCameraTransitionOnClimbUpDown value="false" />
      <shouldCameraIgnoreExiting value="false" />
      <AllowPretendOccupants value="true" />
      <AllowJoyriding value="true" />
      <AllowSundayDriving value="true" />
      <AllowBodyColorMapping value="true" />
      <wheelScale value="0.280000" />
      <wheelScaleRear value="0.280000" />
      <dirtLevelMin value="0.000000" />
      <dirtLevelMax value="0.700000" />
      <envEffScaleMin value="0.000000" />
      <envEffScaleMax value="1.000000" />
      <envEffScaleMin2 value="0.000000" />
      <envEffScaleMax2 value="1.000000" />
      <damageMapScale value="0.800000" />
      <damageOffsetScale value="0.750000" />
      <diffuseTint value="0x00FFFFFF" />
      <steerWheelMult value="1.000000" />
      <HDTextureDist value="5.000000" />
      <lodDistances content="float_array">
        20.000000
        35.000000
        70.000000
        200.000000
        500.000000
        500.000000
      </lodDistances>
      <minSeatHeight value="0.816" />
      <identicalModelSpawnDistance value="20" />
      <maxNumOfSameColor value="10" />
      <defaultBodyHealth value="1000.000000" />
      <pretendOccupantsScale value="1.000000" />
      <visibleSpawnDistScale value="1.000000" />
      <trackerPathWidth value="2.000000" />
      <weaponForceMult value="1.000000" />
      <frequency value="100" />
      <swankness>SWANKNESS_2</swankness>
      <maxNum value="999" />
      <flags>FLAG_SPAWN_ON_TRAILER FLAG_AVERAGE_CAR FLAG_EXTRAS_ALL</flags>
      <type>VEHICLE_TYPE_CAR</type>
      <plateType>VPT_BACK_PLATES</plateType>
      <dashboardType>VDT_RACE</dashboardType>
      <vehicleClass>VC_SUPER</vehicleClass>
      <wheelType>VWT_SPORT</wheelType>
      <trailers />
      <additionalTrailers />
      <drivers />
      <extraIncludes />
      <doorsWithCollisionWhenClosed />
      <driveableDoors />
      <bumpersNeedToCollideWithMap value="false" />
      <needsRopeTexture value="false" />
      <requiredExtras />
      <rewards />
      <cinematicPartCamera>
        <Item>WHEEL_FRONT_RIGHT_CAMERA</Item>
        <Item>WHEEL_FRONT_LEFT_CAMERA</Item>
        <Item>WHEEL_REAR_RIGHT_CAMERA</Item>
        <Item>WHEEL_REAR_LEFT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet />
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000" />
      <buoyancySphereSizeScale value="1.000000" />
      <pOverrideRagdollThreshold type="NULL" />
      <firstPersonDrivebyData>
        <Item>STD_PENUMBRA_FRONT_LEFT</Item>
        <Item>STD_PENUMBRA_FRONT_RIGHT</Item>
      </firstPersonDrivebyData>
    </Item>
    <Item>
      <modelName>pentrogpr2</modelName>
      <txdName>pentro</txdName>
      <handlingId>PENTROGPR</handlingId>
      <gameName>PENTROGPR2</gameName>
      <vehicleMakeName>MAIBATSU</vehicleMakeName>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected />
      <ptfxAssetName>null</ptfxAssetName>
      <audioNameHash>SULTAN</audioNameHash>
      <layout>LAYOUT_LOW</layout>
      <coverBoundOffsets>PENUMBRA_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout />
      <cameraName>DEFAULT_FOLLOW_VEHICLE_CAMERA</cameraName>
      <aimCameraName>DEFAULT_THIRD_PERSON_VEHICLE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>VEHICLE_BONNET_CAMERA_NEAR_HIGH</bonnetCameraName>
      <povCameraName>DEFAULT_POV_CAMERA_LOOKAROUND</povCameraName>
      <FirstPersonDriveByIKOffset x="0.000000" y="-0.055000" z="0.010000" />
      <FirstPersonDriveByUnarmedIKOffset x="0.000000" y="-0.100000" z="0.000000" />
      <FirstPersonProjectileDriveByIKOffset x="0.020000" y="-0.100000" z="-0.025000" />
      <FirstPersonProjectileDriveByPassengerIKOffset x="-0.050000" y="-0.005000" z="-0.038000" />
      <FirstPersonProjectileDriveByRearLeftIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonProjectileDriveByRearRightIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonDriveByLeftPassengerIKOffset x="0.000000" y="-0.175000" z="-0.078000" />
      <FirstPersonDriveByRightPassengerIKOffset x="0.000000" y="-0.055000" z="0.000000" />
      <FirstPersonDriveByRightRearPassengerIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonDriveByLeftPassengerUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonDriveByRightPassengerUnarmedIKOffset x="0.000000" y="-0.100000" z="0.000000" />
      <FirstPersonMobilePhoneOffset x="0.120000" y="0.275000" z="0.523000" />
      <FirstPersonPassengerMobilePhoneOffset x="0.136000" y="0.158000" z="0.445000" />
      <PovCameraOffset x="0.000000" y="-0.200000" z="0.630000" />
      <PovCameraVerticalAdjustmentForRollCage value="-0.025000" />
      <PovPassengerCameraOffset x="0.000000" y="-0.100000" z="0.030000" />
      <PovRearPassengerCameraOffset x="0.000000" y="0.000000" z="0.030000" />
      <vfxInfoName>VFXVEHICLEINFO_CAR_GENERIC</vfxInfoName>
      <shouldUseCinematicViewMode value="true" />
      <shouldCameraTransitionOnClimbUpDown value="false" />
      <shouldCameraIgnoreExiting value="false" />
      <AllowPretendOccupants value="true" />
      <AllowJoyriding value="true" />
      <AllowSundayDriving value="true" />
      <AllowBodyColorMapping value="true" />
      <wheelScale value="0.280000" />
      <wheelScaleRear value="0.280000" />
      <dirtLevelMin value="0.000000" />
      <dirtLevelMax value="0.700000" />
      <envEffScaleMin value="0.000000" />
      <envEffScaleMax value="1.000000" />
      <envEffScaleMin2 value="0.000000" />
      <envEffScaleMax2 value="1.000000" />
      <damageMapScale value="0.800000" />
      <damageOffsetScale value="0.750000" />
      <diffuseTint value="0x00FFFFFF" />
      <steerWheelMult value="1.000000" />
      <HDTextureDist value="5.000000" />
      <lodDistances content="float_array">
        20.000000
        35.000000
        70.000000
        200.000000
        500.000000
        500.000000
      </lodDistances>
      <minSeatHeight value="0.816" />
      <identicalModelSpawnDistance value="20" />
      <maxNumOfSameColor value="10" />
      <defaultBodyHealth value="1000.000000" />
      <pretendOccupantsScale value="1.000000" />
      <visibleSpawnDistScale value="1.000000" />
      <trackerPathWidth value="2.000000" />
      <weaponForceMult value="1.000000" />
      <frequency value="100" />
      <swankness>SWANKNESS_2</swankness>
      <maxNum value="999" />
      <flags>FLAG_SPAWN_ON_TRAILER FLAG_AVERAGE_CAR FLAG_EXTRAS_ALL FLAG_EXTRAS_CONVERTIBLE
        FLAG_EXTRAS_STRONG</flags>
      <type>VEHICLE_TYPE_CAR</type>
      <plateType>VPT_BACK_PLATES</plateType>
      <dashboardType>VDT_RACE</dashboardType>
      <vehicleClass>VC_SUPER</vehicleClass>
      <wheelType>VWT_SPORT</wheelType>
      <trailers />
      <additionalTrailers />
      <drivers />
      <extraIncludes />
      <doorsWithCollisionWhenClosed />
      <driveableDoors />
      <bumpersNeedToCollideWithMap value="false" />
      <needsRopeTexture value="false" />
      <requiredExtras />
      <rewards />
      <cinematicPartCamera>
        <Item>WHEEL_FRONT_RIGHT_CAMERA</Item>
        <Item>WHEEL_FRONT_LEFT_CAMERA</Item>
        <Item>WHEEL_REAR_RIGHT_CAMERA</Item>
        <Item>WHEEL_REAR_LEFT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet />
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000" />
      <buoyancySphereSizeScale value="1.000000" />
      <pOverrideRagdollThreshold type="NULL" />
      <firstPersonDrivebyData>
        <Item>STD_PENUMBRA_FRONT_LEFT</Item>
        <Item>STD_PENUMBRA_FRONT_RIGHT</Item>
      </firstPersonDrivebyData>
    </Item>
    <Item>
      <modelName>picadorexr</modelName>
      <txdName>picadorexr</txdName>
      <handlingId>EXR</handlingId>
      <gameName>PICADOREXR</gameName>
      <vehicleMakeName>CHEVAL</vehicleMakeName>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected />
      <ptfxAssetName>null</ptfxAssetName>
      <audioNameHash>vigero2</audioNameHash>
      <layout>LAYOUT_STANDARD</layout>
      <coverBoundOffsets>GAUNTLET4_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout />
      <cameraName>FOLLOW_CHEETAH_CAMERA</cameraName>
      <aimCameraName>DEFAULT_THIRD_PERSON_VEHICLE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>DEFAULT_VEHICLE_BONNET_CAMERA</bonnetCameraName>
      <povCameraName>REDUCED_NEAR_CLIP_POV_CAMERA</povCameraName>
      <FirstPersonDriveByIKOffset x="0.050000" y="-0.060000" z="-0.035000" />
      <FirstPersonDriveByUnarmedIKOffset x="-0.050000" y="-0.080000" z="-0.020000" />
      <FirstPersonProjectileDriveByIKOffset x="0.000000" y="-0.140000" z="-0.050000" />
      <FirstPersonProjectileDriveByPassengerIKOffset x="0.000000" y="-0.140000" z="-0.050000" />
      <FirstPersonDriveByRightPassengerIKOffset x="-0.050000" y="-0.060000" z="-0.035000" />
      <FirstPersonDriveByRightPassengerUnarmedIKOffset x="-0.010000" y="-0.100000" z="-0.020000" />
      <FirstPersonMobilePhoneOffset x="0.145000" y="0.203000" z="0.560000" />
      <FirstPersonPassengerMobilePhoneOffset x="0.216000" y="0.148000" z="0.450000" />
      <PovCameraOffset x="0.000000" y="-0.250000" z="0.630000" />
      <PovCameraVerticalAdjustmentForRollCage value="0.000000" />
      <PovPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000" />
      <vfxInfoName>VFXVEHICLEINFO_CAR_GENERIC</vfxInfoName>
      <shouldUseCinematicViewMode value="true" />
      <shouldCameraTransitionOnClimbUpDown value="false" />
      <shouldCameraIgnoreExiting value="false" />
      <AllowPretendOccupants value="true" />
      <AllowJoyriding value="true" />
      <AllowSundayDriving value="true" />
      <AllowBodyColorMapping value="true" />
      <wheelScale value="0.261000" />
      <wheelScaleRear value="0.261000" />
      <dirtLevelMin value="0.000000" />
      <dirtLevelMax value="0.450000" />
      <envEffScaleMin value="0.000000" />
      <envEffScaleMax value="1.000000" />
      <envEffScaleMin2 value="0.000000" />
      <envEffScaleMax2 value="1.000000" />
      <damageMapScale value="0.40000" />
      <damageOffsetScale value="0.40000" />
      <diffuseTint value="0x00FFFFFF" />
      <steerWheelMult value="0.700000" />
      <HDTextureDist value="5.000000" />
      <lodDistances content="float_array">
        15.000000
        30.000000
        60.000000
        120.000000
        500.000000
        500.000000
      </lodDistances>
      <minSeatHeight value="0.844" />
      <identicalModelSpawnDistance value="20" />
      <maxNumOfSameColor value="1" />
      <defaultBodyHealth value="1000.000000" />
      <pretendOccupantsScale value="1.000000" />
      <visibleSpawnDistScale value="1.000000" />
      <trackerPathWidth value="2.000000" />
      <weaponForceMult value="1.000000" />
      <frequency value="30" />
      <swankness>SWANKNESS_4</swankness>
      <maxNum value="10" />
      <flags>FLAG_EXTRAS_STRONG FLAG_AVERAGE_CAR FLAG_HAS_INTERIOR_EXTRAS FLAG_CAN_HAVE_NEONS FLAG_HAS_SUPERCHARGER</flags>
      <type>VEHICLE_TYPE_CAR</type>
      <plateType>VPT_FRONT_AND_BACK_PLATES</plateType>
      <dashboardType>VDT_BANSHEE</dashboardType>
      <vehicleClass>VC_MUSCLE</vehicleClass>
      <wheelType>VWT_MUSCLE</wheelType>
      <trailers />
      <additionalTrailers />
      <drivers />
      <extraIncludes />
      <doorsWithCollisionWhenClosed>
        <Item>VEH_EXT_BOOT</Item>
        <Item>VEH_EXT_CARGODOOR</Item>
      </doorsWithCollisionWhenClosed>
      <driveableDoors />
      <bumpersNeedToCollideWithMap value="false" />
      <needsRopeTexture value="false" />
      <requiredExtras />
      <rewards />
      <cinematicPartCamera>
        <Item>WHEEL_FRONT_RIGHT_CAMERA</Item>
        <Item>WHEEL_FRONT_LEFT_CAMERA</Item>
        <Item>WHEEL_REAR_RIGHT_CAMERA</Item>
        <Item>WHEEL_REAR_LEFT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet />
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000" />
      <buoyancySphereSizeScale value="1.000000" />
      <pOverrideRagdollThreshold type="NULL" />
      <firstPersonDrivebyData>
        <Item>STD_GAUNTLET4_FRONT_LEFT</Item>
        <Item>STD_GAUNTLET4_FRONT_RIGHT</Item>
      </firstPersonDrivebyData>
      <numSeatsOverride value="2" />
    </Item>
    <Item>
      <modelName>playboy</modelName>
      <txdName>playboy</txdName>
      <handlingId>PLAYBOY</handlingId>
      <gameName>PLAYBOY</gameName>
      <vehicleMakeName>VAPID</vehicleMakeName>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected />
      <ptfxAssetName>null</ptfxAssetName>
      <audioNameHash>REEVER</audioNameHash>
      <layout>LAYOUT_STD_ISSI3</layout>
      <coverBoundOffsets>VIGERO_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout />
      <cameraName>DEFAULT_FOLLOW_VEHICLE_CAMERA</cameraName>
      <aimCameraName>DEFAULT_THIRD_PERSON_VEHICLE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>VEHICLE_BONNET_CAMERA_STANDARD_HIGH</bonnetCameraName>
      <povCameraName>DEFAULT_POV_CAMERA</povCameraName>
      <FirstPersonDriveByIKOffset x="0.000000" y="-0.030000" z="0.000000" />
      <FirstPersonDriveByUnarmedIKOffset x="0.000000" y="-0.100000" z="0.000000" />
      <FirstPersonProjectileDriveByIKOffset x="0.000000" y="0.000000" z="-0.015000" />
      <FirstPersonProjectileDriveByPassengerIKOffset x="-0.050000" y="0.000000" z="-0.015000" />
      <FirstPersonDriveByLeftPassengerIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonDriveByRightPassengerIKOffset x="0.000000" y="-0.030000" z="0.000000" />
      <FirstPersonDriveByLeftPassengerUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonDriveByRightPassengerUnarmedIKOffset x="0.000000" y="-0.100000" z="0.000000" />
      <FirstPersonMobilePhoneOffset x="0.155000" y="0.178000" z="0.566000" />
      <FirstPersonPassengerMobilePhoneOffset x="0.136000" y="0.128000" z="0.483000" />
      <PovCameraOffset x="0.000000" y="-0.225000" z="0.670000" />
      <PovCameraVerticalAdjustmentForRollCage value="-0.035000" />
      <PovPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000" />
      <vfxInfoName>VFXVEHICLEINFO_CAR_GENERIC</vfxInfoName>
      <shouldUseCinematicViewMode value="true" />
      <shouldCameraTransitionOnClimbUpDown value="false" />
      <shouldCameraIgnoreExiting value="false" />
      <AllowPretendOccupants value="true" />
      <AllowJoyriding value="true" />
      <AllowSundayDriving value="true" />
      <AllowBodyColorMapping value="true" />
      <wheelScale value="0.186806" />
      <wheelScaleRear value="0.186806" />
      <dirtLevelMin value="0.000000" />
      <dirtLevelMax value="0.700000" />
      <envEffScaleMin value="0.000000" />
      <envEffScaleMax value="1.000000" />
      <envEffScaleMin2 value="0.000000" />
      <envEffScaleMax2 value="1.000000" />
      <damageMapScale value="0.600000" />
      <damageOffsetScale value="1.000000" />
      <diffuseTint value="0x00FFFFFF" />
      <steerWheelMult value="1.000000" />
      <HDTextureDist value="5.000000" />
      <lodDistances content="float_array">
        10.000000
        25.000000
        60.000000
        120.000000
        500.000000
        500.000000
      </lodDistances>
      <minSeatHeight value="0.837" />
      <identicalModelSpawnDistance value="20" />
      <maxNumOfSameColor value="10" />
      <defaultBodyHealth value="1000.000000" />
      <pretendOccupantsScale value="1.000000" />
      <visibleSpawnDistScale value="1.000000" />
      <trackerPathWidth value="2.000000" />
      <weaponForceMult value="1.000000" />
      <frequency value="50" />
      <swankness>SWANKNESS_2</swankness>
      <maxNum value="50" />
      <flags>FLAG_RICH_CAR FLAG_EXTRAS_ALL FLAG_EXTRAS_STRONG FLAG_RECESSED_TAILLIGHT_CORONAS</flags>
      <type>VEHICLE_TYPE_CAR</type>
      <plateType>VPT_BACK_PLATES</plateType>
      <dashboardType>VDT_RACE</dashboardType>
      <vehicleClass>VC_SPORT</vehicleClass>
      <wheelType>VWT_SPORT</wheelType>
      <trailers />
      <additionalTrailers />
      <drivers />
      <extraIncludes />
      <doorsWithCollisionWhenClosed />
      <driveableDoors />
      <bumpersNeedToCollideWithMap value="false" />
      <needsRopeTexture value="false" />
      <requiredExtras />
      <rewards />
      <cinematicPartCamera>
        <Item>WHEEL_FRONT_RIGHT_CAMERA</Item>
        <Item>WHEEL_FRONT_LEFT_CAMERA</Item>
        <Item>WHEEL_REAR_RIGHT_CAMERA</Item>
        <Item>WHEEL_REAR_LEFT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet />
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000" />
      <buoyancySphereSizeScale value="1.000000" />
      <pOverrideRagdollThreshold type="NULL" />
      <firstPersonDrivebyData>
        <Item>STD_TORNADO_FRONT_LEFT</Item>
        <Item>STD_TORNADO_FRONT_RIGHT</Item>
      </firstPersonDrivebyData>
    </Item>
    <Item>
      <modelName>reagpr</modelName>
      <txdName>reagpr</txdName>
      <handlingId>REAGPR</handlingId>
      <gameName>REAGPR</gameName>
      <vehicleMakeName>PEGASSI</vehicleMakeName>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected />
      <ptfxAssetName>null</ptfxAssetName>
      <audioNameHash>ITALIGTB2</audioNameHash>
      <layout>LAYOUT_LOW_RESTRICTED</layout>
      <coverBoundOffsets>REAPER_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout />
      <cameraName>DEFAULT_FOLLOW_VEHICLE_CAMERA</cameraName>
      <aimCameraName>DEFAULT_THIRD_PERSON_VEHICLE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>VEHICLE_BONNET_CAMERA_MID_NEAR</bonnetCameraName>
      <povCameraName>REDUCED_NEAR_CLIP_POV_CAMERA_NO_REVERSE</povCameraName>
      <FirstPersonDriveByIKOffset x="0.000000" y="-0.055000" z="-0.050000" />
      <FirstPersonDriveByUnarmedIKOffset x="0.000000" y="-0.100000" z="0.000000" />
      <FirstPersonProjectileDriveByIKOffset x="0.055000" y="-0.098000" z="-0.045000" />
      <FirstPersonProjectileDriveByPassengerIKOffset x="-0.055000" y="-0.098000" z="-0.045000" />
      <FirstPersonProjectileDriveByRearLeftIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonProjectileDriveByRearRightIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonDriveByLeftPassengerIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonDriveByRightPassengerIKOffset x="0.000000" y="-0.055000" z="-0.020000" />
      <FirstPersonDriveByRightRearPassengerIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonDriveByLeftPassengerUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonDriveByRightPassengerUnarmedIKOffset x="0.000000" y="-0.100000" z="0.000000" />
      <FirstPersonMobilePhoneOffset x="0.130000" y="0.161000" z="0.550000" />
      <FirstPersonPassengerMobilePhoneOffset x="0.161000" y="0.114000" z="0.446000" />
      <PovCameraOffset x="0.000000" y="-0.250000" z="0.645000" />
      <PovCameraVerticalAdjustmentForRollCage value="0.000000" />
      <PovPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000" />
      <PovRearPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000" />
      <vfxInfoName>VFXVEHICLEINFO_CAR_GENERIC</vfxInfoName>
      <shouldUseCinematicViewMode value="true" />
      <shouldCameraTransitionOnClimbUpDown value="false" />
      <shouldCameraIgnoreExiting value="false" />
      <AllowPretendOccupants value="true" />
      <AllowJoyriding value="true" />
      <AllowSundayDriving value="true" />
      <AllowBodyColorMapping value="true" />
      <wheelScale value="0.295000" />
      <wheelScaleRear value="0.295000" />
      <dirtLevelMin value="0.000000" />
      <dirtLevelMax value="0.800000" />
      <envEffScaleMin value="0.000000" />
      <envEffScaleMax value="1.000000" />
      <envEffScaleMin2 value="0.000000" />
      <envEffScaleMax2 value="1.000000" />
      <damageMapScale value="0.40000" />
      <damageOffsetScale value="0.40000" />
      <diffuseTint value="0x00FFFFFF" />
      <steerWheelMult value="1.000000" />
      <HDTextureDist value="5.000000" />
      <lodDistances content="float_array">
        30.000000
        60.000000
        90.000000
        140.000000
        350.000000
        350.000000
      </lodDistances>
      <identicalModelSpawnDistance value="20" />
      <maxNumOfSameColor value="1" />
      <defaultBodyHealth value="1000.000000" />
      <pretendOccupantsScale value="1.000000" />
      <visibleSpawnDistScale value="1.000000" />
      <trackerPathWidth value="2.000000" />
      <weaponForceMult value="1.000000" />
      <frequency value="30" />
      <swankness>SWANKNESS_5</swankness>
      <maxNum value="4" />
      <flags>FLAG_SPORTS FLAG_RICH_CAR FLAG_RECESSED_HEADLIGHT_CORONAS FLAG_NO_BROKEN_DOWN_SCENARIO
        FLAG_HAS_GULL_WING_DOORS FLAG_EXTRAS_STRONG FLAG_PARKING_SENSORS FLAG_HAS_NITROUS
        FLAG_INCREASE_CAMBER_WITH_SUSPENSION_MOD</flags>
      <type>VEHICLE_TYPE_CAR</type>
      <plateType>VPT_BACK_PLATES</plateType>
      <dashboardType>VDT_BANSHEE</dashboardType>
      <vehicleClass>VC_SUPER</vehicleClass>
      <wheelType>VWT_HIEND</wheelType>
      <trailers />
      <additionalTrailers />
      <drivers />
      <extraIncludes />
      <doorsWithCollisionWhenClosed />
      <driveableDoors />
      <bumpersNeedToCollideWithMap value="false" />
      <needsRopeTexture value="false" />
      <requiredExtras />
      <rewards />
      <cinematicPartCamera>
        <Item>WHEEL_FRONT_RIGHT_CAMERA</Item>
        <Item>WHEEL_FRONT_LEFT_CAMERA</Item>
        <Item>WHEEL_REAR_RIGHT_CAMERA</Item>
        <Item>WHEEL_REAR_LEFT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet />
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000" />
      <buoyancySphereSizeScale value="1.000000" />
      <pOverrideRagdollThreshold type="NULL" />
      <firstPersonDrivebyData>
        <Item>LOW_REAPER_FRONT_LEFT</Item>
        <Item>LOW_REAPER_FRONT_RIGHT</Item>
      </firstPersonDrivebyData>
    </Item>
    <Item>
      <modelName>remustwo</modelName>
      <txdName>remustwo</txdName>
      <handlingId>REMUSTWO</handlingId>
      <gameName>REMUSTWO</gameName>
      <vehicleMakeName>ANNIS</vehicleMakeName>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected />
      <ptfxAssetName>null</ptfxAssetName>
      <audioNameHash />
      <layout>LAYOUT_LOW</layout>
      <coverBoundOffsets>REMUS_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout />
      <cameraName>FOLLOW_CHEETAH_CAMERA</cameraName>
      <aimCameraName>DEFAULT_THIRD_PERSON_VEHICLE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>VEHICLE_BONNET_CAMERA_STANDARD</bonnetCameraName>
      <povCameraName>REDUCED_NEAR_CLIP_POV_CAMERA</povCameraName>
      <FirstPersonDriveByIKOffset x="0.03000" y="-0.090000" z="-0.020000" />
      <FirstPersonDriveByUnarmedIKOffset x="0.000000" y="-0.060000" z="-0.020000" />
      <FirstPersonProjectileDriveByIKOffset x="-0.025000" y="-0.130000" z="-0.040000" />
      <FirstPersonProjectileDriveByPassengerIKOffset x="0.025000" y="-0.130000" z="-0.040000" />
      <FirstPersonDriveByRightPassengerIKOffset x="0.020000" y="-0.080000" z="-0.020000" />
      <FirstPersonDriveByRightPassengerUnarmedIKOffset x="0.000000" y="-0.060000" z="-0.020000" />
      <FirstPersonMobilePhoneOffset x="0.206000" y="0.152000" z="0.514000" />
      <FirstPersonPassengerMobilePhoneOffset x="0.216000" y="0.148000" z="0.380000" />
      <PovCameraOffset x="0.000000" y="-0.300000" z="0.700000" />
      <PovCameraVerticalAdjustmentForRollCage value="-0.030000" />
      <vfxInfoName>VFXVEHICLEINFO_CAR_GENERIC</vfxInfoName>
      <shouldUseCinematicViewMode value="true" />
      <shouldCameraTransitionOnClimbUpDown value="false" />
      <shouldCameraIgnoreExiting value="false" />
      <AllowPretendOccupants value="true" />
      <AllowJoyriding value="true" />
      <AllowSundayDriving value="true" />
      <AllowBodyColorMapping value="true" />
      <wheelScale value="0.220774" />
      <wheelScaleRear value="0.220774" />
      <dirtLevelMin value="0.000000" />
      <dirtLevelMax value="0.550000" />
      <envEffScaleMin value="0.000000" />
      <envEffScaleMax value="1.000000" />
      <envEffScaleMin2 value="0.000000" />
      <envEffScaleMax2 value="1.000000" />
      <damageMapScale value="0.500000" />
      <damageOffsetScale value="0.500000" />
      <diffuseTint value="0x00FFFFFF" />
      <steerWheelMult value="1.000000" />
      <HDTextureDist value="5.000000" />
      <lodDistances content="float_array">
        15.000000
        30.000000
        70.000000
        140.000000
        300.000000
        300.000000
      </lodDistances>
      <minSeatHeight value="0.871" />
      <identicalModelSpawnDistance value="20" />
      <maxNumOfSameColor value="1" />
      <defaultBodyHealth value="1000.000000" />
      <pretendOccupantsScale value="1.000000" />
      <visibleSpawnDistScale value="1.000000" />
      <trackerPathWidth value="2.000000" />
      <weaponForceMult value="1.000000" />
      <frequency value="10" />
      <swankness>SWANKNESS_3</swankness>
      <maxNum value="10" />
      <flags>FLAG_SPORTS FLAG_NO_BROKEN_DOWN_SCENARIO FLAG_CAN_HAVE_NEONS FLAG_HAS_SUPERCHARGER</flags>
      <type>VEHICLE_TYPE_CAR</type>
      <plateType>VPT_FRONT_AND_BACK_PLATES</plateType>
      <dashboardType>VDT_RACE</dashboardType>
      <vehicleClass>VC_SPORT</vehicleClass>
      <wheelType>VWT_SPORT</wheelType>
      <trailers />
      <additionalTrailers />
      <drivers />
      <extraIncludes />
      <doorsWithCollisionWhenClosed />
      <driveableDoors />
      <bumpersNeedToCollideWithMap value="false" />
      <needsRopeTexture value="false" />
      <requiredExtras />
      <rewards />
      <cinematicPartCamera>
        <Item>WHEEL_FRONT_RIGHT_CAMERA</Item>
        <Item>WHEEL_FRONT_LEFT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet />
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000" />
      <buoyancySphereSizeScale value="1.000000" />
      <pOverrideRagdollThreshold type="NULL" />
      <firstPersonDrivebyData>
        <Item>LOW_REMUS_FRONT_LEFT</Item>
        <Item>LOW_REMUS_FRONT_RIGHT</Item>
      </firstPersonDrivebyData>
    </Item>
    <Item>
      <modelName>requiemzr1</modelName>
      <txdName>requiemzr1</txdName>
      <handlingId>REQUIEMZR2</handlingId>
      <gameName>REQUIZR1</gameName>
      <vehicleMakeName>ANNIS</vehicleMakeName>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected />
      <ptfxAssetName>veh_rqzr</ptfxAssetName>
      <audioNameHash>zr350</audioNameHash>
      <layout>LAYOUT_STD_ARENA_1HONLY</layout>
      <coverBoundOffsets>ZR380_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout />
      <cameraName>ZR380_FOLLOW_CAMERA</cameraName>
      <aimCameraName>DEFAULT_THIRD_PERSON_VEHICLE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>VEHICLE_BONNET_CAMERA_STOCKADE</bonnetCameraName>
      <povCameraName>DEFAULT_POV_CAMERA_NO_REVERSE_ZR380</povCameraName>
      <FirstPersonDriveByIKOffset x="0.070000" y="-0.200000" z="-0.010000" />
      <FirstPersonDriveByUnarmedIKOffset x="0.000000" y="-0.100000" z="-0.020000" />
      <FirstPersonProjectileDriveByIKOffset x="0.000000" y="-0.130000" z="-0.050000" />
      <FirstPersonProjectileDriveByPassengerIKOffset x="0.000000" y="-0.130000" z="-0.050000" />
      <FirstPersonDriveByRightPassengerIKOffset x="-0.070000" y="-0.200000" z="-0.010000" />
      <FirstPersonDriveByRightPassengerUnarmedIKOffset x="0.000000" y="-0.100000" z="-0.020000" />
      <FirstPersonMobilePhoneOffset x="0.133000" y="0.228000" z="0.563000" />
      <FirstPersonPassengerMobilePhoneOffset x="0.226000" y="0.223000" z="0.435000" />
      <PovCameraOffset x="0.000000" y="-0.165000" z="0.670000" />
      <PovCameraVerticalAdjustmentForRollCage value="0.000000" />
      <PovPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000" />
      <vfxInfoName>vfxvehicleinfo_car_rqzr</vfxInfoName>
      <shouldUseCinematicViewMode value="true" />
      <shouldCameraTransitionOnClimbUpDown value="false" />
      <shouldCameraIgnoreExiting value="false" />
      <AllowPretendOccupants value="true" />
      <AllowJoyriding value="true" />
      <AllowSundayDriving value="true" />
      <AllowBodyColorMapping value="true" />
      <wheelScale value="0.262000" />
      <wheelScaleRear value="0.262000" />
      <dirtLevelMin value="0.000000" />
      <dirtLevelMax value="0.800000" />
      <envEffScaleMin value="1.000000" />
      <envEffScaleMax value="1.000000" />
      <envEffScaleMin2 value="1.000000" />
      <envEffScaleMax2 value="1.000000" />
      <damageMapScale value="0.600000" />
      <damageOffsetScale value="1.000000" />
      <diffuseTint value="0x00FFFFFF" />
      <steerWheelMult value="1.000000" />
      <HDTextureDist value="5.000000" />
      <lodDistances content="float_array">
        12.000000
        30.000000
        60.000000
        120.000000
        500.000000
        500.000000
      </lodDistances>
      <identicalModelSpawnDistance value="8" />
      <maxNumOfSameColor value="10" />
      <defaultBodyHealth value="1000.000000" />
      <pretendOccupantsScale value="1.000000" />
      <visibleSpawnDistScale value="1.000000" />
      <trackerPathWidth value="2.000000" />
      <weaponForceMult value="1.000000" />
      <frequency value="10" />
      <swankness>SWANKNESS_2</swankness>
      <maxNum value="5" />
      <flags>FLAG_PARKING_SENSORS FLAG_AVERAGE_CAR FLAG_RECESSED_TAILLIGHT_CORONAS
        FLAG_HAS_NITROUS_MOD</flags>
      <type>VEHICLE_TYPE_CAR</type>
      <plateType>VPT_BACK_PLATES</plateType>
      <dashboardType>VDT_RACE</dashboardType>
      <vehicleClass>VC_SPORT</vehicleClass>
      <wheelType>VWT_MUSCLE</wheelType>
      <trailers />
      <additionalTrailers />
      <drivers />
      <extraIncludes>
        <Item>EXTRA_4 EXTRA_5</Item>
        <Item>EXTRA_6 EXTRA_7</Item>
        <Item>EXTRA_8 EXTRA_9</Item>
      </extraIncludes>
      <doorsWithCollisionWhenClosed />
      <driveableDoors />
      <bumpersNeedToCollideWithMap value="false" />
      <needsRopeTexture value="false" />
      <requiredExtras />
      <rewards />
      <cinematicPartCamera>
        <Item>WHEEL_FRONT_RIGHT_CAMERA</Item>
        <Item>WHEEL_FRONT_LEFT_CAMERA</Item>
        <Item>WHEEL_REAR_RIGHT_CAMERA</Item>
        <Item>WHEEL_REAR_LEFT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet />
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000" />
      <buoyancySphereSizeScale value="1.000000" />
      <pOverrideRagdollThreshold type="NULL" />
      <firstPersonDrivebyData>
        <Item>STD_ZR380_FRONT_LEFT</Item>
        <Item>STD_ZR380_FRONT_RIGHT</Item>
      </firstPersonDrivebyData>
      <numSeatsOverride value="2" />
    </Item>
    <Item>
      <modelName>requiemzr3</modelName>
      <txdName>requiemzr3</txdName>
      <handlingId>REQUIEMZR2</handlingId>
      <gameName>REQUIZR3</gameName>
      <vehicleMakeName>ANNIS</vehicleMakeName>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected />
      <ptfxAssetName>veh_rqzr</ptfxAssetName>
      <audioNameHash>zr350</audioNameHash>
      <layout>LAYOUT_ZR380RHD</layout>
      <coverBoundOffsets>ZR380RHD_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout />
      <cameraName>ZR380_FOLLOW_CAMERA</cameraName>
      <aimCameraName>DEFAULT_THIRD_PERSON_VEHICLE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>VEHICLE_BONNET_CAMERA_STOCKADE</bonnetCameraName>
      <povCameraName>DEFAULT_POV_CAMERA_NO_REVERSE_ZR380</povCameraName>
      <FirstPersonDriveByIKOffset x="0.070000" y="-0.200000" z="-0.010000" />
      <FirstPersonDriveByUnarmedIKOffset x="0.000000" y="-0.100000" z="-0.020000" />
      <FirstPersonProjectileDriveByIKOffset x="0.000000" y="-0.130000" z="-0.050000" />
      <FirstPersonProjectileDriveByPassengerIKOffset x="0.000000" y="-0.130000" z="-0.050000" />
      <FirstPersonDriveByRightPassengerIKOffset x="-0.070000" y="-0.200000" z="-0.010000" />
      <FirstPersonDriveByRightPassengerUnarmedIKOffset x="0.000000" y="-0.100000" z="-0.020000" />
      <FirstPersonMobilePhoneOffset x="0.133000" y="0.228000" z="0.563000" />
      <FirstPersonPassengerMobilePhoneOffset x="0.226000" y="0.223000" z="0.435000" />
      <PovCameraOffset x="0.000000" y="-0.165000" z="0.670000" />
      <PovCameraVerticalAdjustmentForRollCage value="0.000000" />
      <PovPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000" />
      <vfxInfoName>vfxvehicleinfo_car_rqzr</vfxInfoName>
      <shouldUseCinematicViewMode value="true" />
      <shouldCameraTransitionOnClimbUpDown value="false" />
      <shouldCameraIgnoreExiting value="false" />
      <AllowPretendOccupants value="true" />
      <AllowJoyriding value="true" />
      <AllowSundayDriving value="true" />
      <AllowBodyColorMapping value="true" />
      <wheelScale value="0.262000" />
      <wheelScaleRear value="0.262000" />
      <dirtLevelMin value="0.000000" />
      <dirtLevelMax value="0.800000" />
      <envEffScaleMin value="1.000000" />
      <envEffScaleMax value="1.000000" />
      <envEffScaleMin2 value="1.000000" />
      <envEffScaleMax2 value="1.000000" />
      <damageMapScale value="0.600000" />
      <damageOffsetScale value="1.000000" />
      <diffuseTint value="0x00FFFFFF" />
      <steerWheelMult value="1.000000" />
      <HDTextureDist value="5.000000" />
      <lodDistances content="float_array">
        12.000000
        30.000000
        60.000000
        120.000000
        500.000000
        500.000000
      </lodDistances>
      <identicalModelSpawnDistance value="8" />
      <maxNumOfSameColor value="10" />
      <defaultBodyHealth value="1000.000000" />
      <pretendOccupantsScale value="1.000000" />
      <visibleSpawnDistScale value="1.000000" />
      <trackerPathWidth value="2.000000" />
      <weaponForceMult value="1.000000" />
      <frequency value="10" />
      <swankness>SWANKNESS_2</swankness>
      <maxNum value="5" />
      <flags>FLAG_PARKING_SENSORS FLAG_AVERAGE_CAR FLAG_RECESSED_TAILLIGHT_CORONAS
        FLAG_HAS_NITROUS_MOD</flags>
      <type>VEHICLE_TYPE_CAR</type>
      <plateType>VPT_BACK_PLATES</plateType>
      <dashboardType>VDT_RACE</dashboardType>
      <vehicleClass>VC_SPORT</vehicleClass>
      <wheelType>VWT_MUSCLE</wheelType>
      <trailers />
      <additionalTrailers />
      <drivers />
      <extraIncludes>
        <Item>EXTRA_4 EXTRA_5</Item>
        <Item>EXTRA_6 EXTRA_7</Item>
        <Item>EXTRA_8 EXTRA_9</Item>
      </extraIncludes>
      <doorsWithCollisionWhenClosed />
      <driveableDoors />
      <bumpersNeedToCollideWithMap value="false" />
      <needsRopeTexture value="false" />
      <requiredExtras />
      <rewards />
      <cinematicPartCamera>
        <Item>WHEEL_FRONT_RIGHT_CAMERA</Item>
        <Item>WHEEL_FRONT_LEFT_CAMERA</Item>
        <Item>WHEEL_REAR_RIGHT_CAMERA</Item>
        <Item>WHEEL_REAR_LEFT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet />
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000" />
      <buoyancySphereSizeScale value="1.000000" />
      <pOverrideRagdollThreshold type="NULL" />
      <firstPersonDrivebyData>
        <Item>ZR380RHD_FRONT_LEFT</Item>
        <Item>ZR380RHD_FRONT_RIGHT</Item>
      </firstPersonDrivebyData>
      <numSeatsOverride value="2" />
    </Item>
    <Item>
      <modelName>cypherwb</modelName>
      <txdName>cypherwb</txdName>
      <handlingId>cypherwb</handlingId>
      <gameName>cypherwb</gameName>
      <vehicleMakeName>UBERMACHT</vehicleMakeName>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected />
      <ptfxAssetName>null</ptfxAssetName>
      <audioNameHash>cypher</audioNameHash>
      <layout>LAYOUT_STANDARD</layout>
      <coverBoundOffsets>CYPHER_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout />
      <cameraName>FOLLOW_CHEETAH_CAMERA</cameraName>
      <aimCameraName>DEFAULT_THIRD_PERSON_VEHICLE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>VEHICLE_BONNET_CAMERA_STANDARD_LONG_EXTRA_LOW</bonnetCameraName>
      <povCameraName>REDUCED_NEAR_CLIP_POV_CAMERA</povCameraName>
      <FirstPersonDriveByIKOffset x="0.020000" y="-0.150000" z="-0.060000" />
      <FirstPersonDriveByUnarmedIKOffset x="-0.050000" y="-0.020000" z="0.000000" />
      <FirstPersonProjectileDriveByIKOffset x="0.030000" y="-0.105000" z="-0.050000" />
      <FirstPersonProjectileDriveByPassengerIKOffset x="-0.030000" y="-0.085000" z="-0.050000" />
      <FirstPersonDriveByRightPassengerIKOffset x="-0.020000" y="-0.150000" z="-0.060000" />
      <FirstPersonDriveByRightPassengerUnarmedIKOffset x="0.050000" y="-0.040000" z="0.000000" />
      <FirstPersonMobilePhoneOffset x="0.140000" y="0.195000" z="0.535000" />
      <FirstPersonPassengerMobilePhoneOffset x="0.185000" y="0.185000" z="0.425000" />
      <PovCameraOffset x="0.000000" y="-0.230000" z="0.660000" />
      <PovCameraVerticalAdjustmentForRollCage value="-0.0450000" />
      <PovPassengerCameraOffset x="0.000000" y="0.050000" z="0.000000" />
      <PovRearPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000" />
      <vfxInfoName>VFXVEHICLEINFO_CAR_GENERIC</vfxInfoName>
      <shouldUseCinematicViewMode value="true" />
      <shouldCameraTransitionOnClimbUpDown value="false" />
      <shouldCameraIgnoreExiting value="false" />
      <AllowPretendOccupants value="true" />
      <AllowJoyriding value="true" />
      <AllowSundayDriving value="true" />
      <AllowBodyColorMapping value="true" />
      <wheelScale value="0.273000" />
      <wheelScaleRear value="0.273000" />
      <dirtLevelMin value="0.000000" />
      <dirtLevelMax value="0.300000" />
      <envEffScaleMin value="0.000000" />
      <envEffScaleMax value="1.000000" />
      <envEffScaleMin2 value="0.000000" />
      <envEffScaleMax2 value="1.000000" />
      <damageMapScale value="0.400000" />
      <damageOffsetScale value="0.400000" />
      <diffuseTint value="0x00FFFFFF" />
      <steerWheelMult value="0.55000" />
      <HDTextureDist value="5.000000" />
      <lodDistances content="float_array">
        15.000000
        30.000000
        70.000000
        140.000000
        300.000000
        300.000000
      </lodDistances>
      <minSeatHeight value="0.481" />
      <identicalModelSpawnDistance value="20" />
      <maxNumOfSameColor value="1" />
      <defaultBodyHealth value="1000.000000" />
      <pretendOccupantsScale value="1.000000" />
      <visibleSpawnDistScale value="1.000000" />
      <trackerPathWidth value="2.000000" />
      <weaponForceMult value="1.000000" />
      <frequency value="50" />
      <swankness>SWANKNESS_5</swankness>
      <maxNum value="5" />
      <flags>FLAG_EXTRAS_STRONG FLAG_SPORTS FLAG_RICH_CAR FLAG_NO_BROKEN_DOWN_SCENARIO
        FLAG_RECESSED_TAILLIGHT_CORONAS FLAG_HAS_INTERIOR_EXTRAS FLAG_CAN_HAVE_NEONS
        FLAG_INCREASE_CAMBER_WITH_SUSPENSION_MOD</flags>
      <type>VEHICLE_TYPE_CAR</type>
      <plateType>VPT_BACK_PLATES</plateType>
      <dashboardType>VDT_RACE</dashboardType>
      <vehicleClass>VC_SPORT</vehicleClass>
      <wheelType>VWT_HIEND</wheelType>
      <trailers />
      <additionalTrailers />
      <drivers />
      <extraIncludes />
      <doorsWithCollisionWhenClosed />
      <driveableDoors />
      <bumpersNeedToCollideWithMap value="false" />
      <needsRopeTexture value="false" />
      <requiredExtras />
      <rewards />
      <cinematicPartCamera>
        <Item>WHEEL_FRONT_RIGHT_CAMERA</Item>
        <Item>WHEEL_FRONT_LEFT_CAMERA</Item>
        <Item>WHEEL_REAR_RIGHT_CAMERA</Item>
        <Item>WHEEL_REAR_LEFT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet />
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000" />
      <buoyancySphereSizeScale value="1.000000" />
      <pOverrideRagdollThreshold type="NULL" />
      <firstPersonDrivebyData>
        <Item>STD_CYPHER_FRONT_LEFT</Item>
        <Item>STD_CYPHER_FRONT_RIGHT</Item>
      </firstPersonDrivebyData>
      <numSeatsOverride value="2" />
    </Item>
    <Item>
      <modelName>roxanne</modelName>
      <txdName>roxanne</txdName>
      <handlingId>ROXANNE</handlingId>
      <gameName>ROXANNE</gameName>
      <vehicleMakeName>ANNIS</vehicleMakeName>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected />
      <ptfxAssetName>null</ptfxAssetName>
      <audioNameHash>roxanne</audioNameHash>
      <layout>LAYOUT_LOW_RESTRICTED</layout>
      <coverBoundOffsets>RUINER_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout />
      <cameraName>DEFAULT_FOLLOW_VEHICLE_CAMERA</cameraName>
      <aimCameraName>MID_BOX_VEHICLE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>VEHICLE_BONNET_CAMERA_STANDARD_HIGH</bonnetCameraName>
      <povCameraName>DEFAULT_POV_CAMERA_LOOKAROUND_MID</povCameraName>
      <FirstPersonDriveByIKOffset x="0.000000" y="-0.055000" z="-0.005000" />
      <FirstPersonDriveByUnarmedIKOffset x="0.000000" y="-0.100000" z="0.000000" />
      <FirstPersonProjectileDriveByIKOffset x="0.000000" y="-0.030000" z="-0.020000" />
      <FirstPersonProjectileDriveByPassengerIKOffset x="0.000000" y="-0.030000" z="-0.020000" />
      <FirstPersonProjectileDriveByRearLeftIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonProjectileDriveByRearRightIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonDriveByLeftPassengerIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonDriveByRightPassengerIKOffset x="0.000000" y="-0.055000" z="-0.005000" />
      <FirstPersonDriveByRightRearPassengerIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonDriveByLeftPassengerUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonDriveByRightPassengerUnarmedIKOffset x="0.000000" y="-0.100000" z="0.000000" />
      <FirstPersonMobilePhoneOffset x="0.143000" y="0.160000" z="0.503000" />
      <FirstPersonPassengerMobilePhoneOffset x="0.136000" y="0.126000" z="0.485000" />
      <PovCameraOffset x="0.000000" y="-0.265000" z="0.635000" />
      <PovCameraVerticalAdjustmentForRollCage value="0.000000" />
      <PovPassengerCameraOffset x="0.000000" y="0.000000" z="0.055000" />
      <PovRearPassengerCameraOffset x="0.000000" y="0.000000" z="0.055000" />
      <vfxInfoName>VFXVEHICLEINFO_CAR_GENERIC</vfxInfoName>
      <shouldUseCinematicViewMode value="true" />
      <shouldCameraTransitionOnClimbUpDown value="false" />
      <shouldCameraIgnoreExiting value="false" />
      <AllowPretendOccupants value="true" />
      <AllowJoyriding value="true" />
      <AllowSundayDriving value="true" />
      <AllowBodyColorMapping value="true" />
      <wheelScale value="0.244797" />
      <wheelScaleRear value="0.244797" />
      <dirtLevelMin value="0.000000" />
      <dirtLevelMax value="0.700000" />
      <envEffScaleMin value="0.000000" />
      <envEffScaleMax value="1.000000" />
      <envEffScaleMin2 value="0.000000" />
      <envEffScaleMax2 value="1.000000" />
      <damageMapScale value="0.600000" />
      <damageOffsetScale value="1.000000" />
      <diffuseTint value="0x00FFFFFF" />
      <steerWheelMult value="1.000000" />
      <HDTextureDist value="5.000000" />
      <lodDistances content="float_array">
        15.000000
        30.000000
        50.000000
        100.000000
        500.000000
        500.000000
      </lodDistances>
      <minSeatHeight value="0.852" />
      <identicalModelSpawnDistance value="20" />
      <maxNumOfSameColor value="10" />
      <defaultBodyHealth value="1000.000000" />
      <pretendOccupantsScale value="1.000000" />
      <visibleSpawnDistScale value="1.000000" />
      <trackerPathWidth value="2.000000" />
      <weaponForceMult value="1.000000" />
      <frequency value="50" />
      <swankness>SWANKNESS_2</swankness>
      <maxNum value="10" />
      <flags>FLAG_CAN_HAVE_NEONS FLAG_EXTRAS_STRONG FLAG_AVERAGE_CAR FLAG_POOR_CAR FLAG_HAS_NO_ROOF
        FLAG_RECESSED_HEADLIGHT_CORONAS FLAG_RECESSED_TAILLIGHT_CORONAS FLAG_HAS_INTERIOR_EXTRAS</flags>
      <type>VEHICLE_TYPE_CAR</type>
      <plateType>VPT_FRONT_AND_BACK_PLATES</plateType>
      <dashboardType>VDT_SUPERGT</dashboardType>
      <vehicleClass>VC_SPORT</vehicleClass>
      <wheelType>VWT_SPORT</wheelType>
      <trailers />
      <additionalTrailers />
      <drivers />
      <extraIncludes />
      <doorsWithCollisionWhenClosed />
      <driveableDoors />
      <bumpersNeedToCollideWithMap value="false" />
      <needsRopeTexture value="false" />
      <requiredExtras />
      <rewards />
      <cinematicPartCamera>
        <Item>WHEEL_FRONT_RIGHT_CAMERA</Item>
        <Item>WHEEL_FRONT_LEFT_CAMERA</Item>
        <Item>WHEEL_REAR_RIGHT_CAMERA</Item>
        <Item>WHEEL_REAR_LEFT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet />
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000" />
      <buoyancySphereSizeScale value="1.000000" />
      <pOverrideRagdollThreshold type="NULL" />
      <firstPersonDrivebyData>
        <Item>STD_RUINER_FRONT_LEFT</Item>
        <Item>STD_RUINER_FRONT_RIGHT</Item>
      </firstPersonDrivebyData>
    </Item>
    <Item>
      <modelName>s230</modelName>
      <txdName>s230</txdName>
      <handlingId>S230</handlingId>
      <gameName>S230</gameName>
      <vehicleMakeName>ANNIS</vehicleMakeName>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected />
      <ptfxAssetName>null</ptfxAssetName>
      <audioNameHash>ELEGY</audioNameHash>
      <layout>LAYOUT_LOW</layout>
      <coverBoundOffsets>SAVESTRA_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout />
      <cameraName>DEFAULT_FOLLOW_VEHICLE_CAMERA</cameraName>
      <aimCameraName>MID_BOX_VEHICLE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>VEHICLE_BONNET_CAMERA_LOW</bonnetCameraName>
      <povCameraName>DEFAULT_POV_CAMERA</povCameraName>
      <FirstPersonDriveByIKOffset x="0.060000" y="-0.060000" z="-0.060000" />
      <FirstPersonDriveByUnarmedIKOffset x="0.000000" y="-0.070000" z="0.000000" />
      <FirstPersonProjectileDriveByIKOffset x="0.000000" y="-0.130000" z="-0.020000" />
      <FirstPersonProjectileDriveByPassengerIKOffset x="-0.010000" y="-0.130000" z="0.000000" />
      <FirstPersonDriveByRightPassengerIKOffset x="-0.040000" y="-0.050000" z="-0.060000" />
      <FirstPersonDriveByRightPassengerUnarmedIKOffset x="0.000000" y="-0.090000" z="0.000000" />
      <FirstPersonMobilePhoneOffset x="0.138000" y="0.150000" z="0.535000" />
      <FirstPersonPassengerMobilePhoneOffset x="0.190000" y="0.133000" z="0.434000" />
      <PovCameraOffset x="0.000000" y="-0.245000" z="0.655000" />
      <PovCameraVerticalAdjustmentForRollCage value="0.000000" />
      <PovPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000" />
      <PovRearPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000" />
      <vfxInfoName>VFXVEHICLEINFO_CAR_GENERIC</vfxInfoName>
      <shouldUseCinematicViewMode value="true" />
      <shouldCameraTransitionOnClimbUpDown value="false" />
      <shouldCameraIgnoreExiting value="false" />
      <AllowPretendOccupants value="true" />
      <AllowJoyriding value="true" />
      <AllowSundayDriving value="true" />
      <AllowBodyColorMapping value="true" />
      <wheelScale value="0.221600" />
      <wheelScaleRear value="0.221600" />
      <dirtLevelMin value="0.000000" />
      <dirtLevelMax value="0.450000" />
      <envEffScaleMin value="0.000000" />
      <envEffScaleMax value="1.000000" />
      <envEffScaleMin2 value="0.000000" />
      <envEffScaleMax2 value="1.000000" />
      <damageMapScale value="0.350000" />
      <damageOffsetScale value="0.350000" />
      <diffuseTint value="0x00FFFFFF" />
      <steerWheelMult value="1.000000" />
      <HDTextureDist value="5.000000" />
      <lodDistances content="float_array">
        40.000000
        60.000000
        100.000000
        180.000000
        500.000000
        500.000000
      </lodDistances>
      <minSeatHeight value="0.844" />
      <identicalModelSpawnDistance value="80" />
      <maxNumOfSameColor value="1" />
      <defaultBodyHealth value="1000.000000" />
      <pretendOccupantsScale value="1.000000" />
      <visibleSpawnDistScale value="1.000000" />
      <trackerPathWidth value="2.000000" />
      <weaponForceMult value="1.000000" />
      <frequency value="5" />
      <swankness>SWANKNESS_4</swankness>
      <maxNum value="1" />
      <flags>FLAG_EXTRAS_STRONG FLAG_EXTRAS_ALL FLAG_SPORTS FLAG_AVERAGE_CAR
        FLAG_INCREASE_PED_COMMENTS FLAG_SPAWN_ON_TRAILER</flags>
      <type>VEHICLE_TYPE_CAR</type>
      <plateType>VPT_FRONT_AND_BACK_PLATES</plateType>
      <dashboardType>VDT_DUKES</dashboardType>
      <vehicleClass>VC_SPORT</vehicleClass>
      <wheelType>VWT_SPORT</wheelType>
      <trailers />
      <additionalTrailers />
      <drivers />
      <extraIncludes>
        <Item>extra_2</Item>
        <Item>extra_3</Item>
        <Item>extra_5</Item>
      </extraIncludes>
      <doorsWithCollisionWhenClosed />
      <driveableDoors />
      <bumpersNeedToCollideWithMap value="false" />
      <needsRopeTexture value="false" />
      <requiredExtras />
      <rewards />
      <cinematicPartCamera>
        <Item>WHEEL_FRONT_RIGHT_CAMERA</Item>
        <Item>WHEEL_FRONT_LEFT_CAMERA</Item>
        <Item>WHEEL_REAR_RIGHT_CAMERA</Item>
        <Item>WHEEL_REAR_LEFT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet />
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000" />
      <buoyancySphereSizeScale value="1.000000" />
      <pOverrideRagdollThreshold type="NULL" />
      <firstPersonDrivebyData>
        <Item>STD_SAVESTRA_FRONT_LEFT</Item>
        <Item>STD_SAVESTRA_FRONT_RIGHT</Item>
      </firstPersonDrivebyData>
      <numSeatsOverride value="2" />
    </Item>
    <Item>
      <modelName>schlagenstr</modelName>
      <txdName>schlagenstr</txdName>
      <handlingId>SCHLAGENSTR</handlingId>
      <gameName>SCHLAGENSTR</gameName>
      <vehicleMakeName>BENEFACTOR</vehicleMakeName>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected />
      <ptfxAssetName>null</ptfxAssetName>
      <audioNameHash>schlagen</audioNameHash>
      <layout>LAYOUT_LOW_RESTRICTED_SCHLAGEN</layout>
      <coverBoundOffsets>SCHLAGEN_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout />
      <cameraName>FOLLOW_CHEETAH_CAMERA</cameraName>
      <aimCameraName>DEFAULT_THIRD_PERSON_VEHICLE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>DEFAULT_VEHICLE_BONNET_CAMERA</bonnetCameraName>
      <vfxInfoName>VFXVEHICLEINFO_CAR_GENERIC</vfxInfoName>
      <povCameraName>REDUCED_NEAR_CLIP_POV_CAMERA</povCameraName>
      <FirstPersonDriveByIKOffset x="0.040000" y="-0.120000" z="-0.030000" />
      <FirstPersonDriveByUnarmedIKOffset x="-0.020000" y="-0.060000" z="-0.025000" />
      <FirstPersonProjectileDriveByIKOffset x="0.000000" y="-0.110000" z="-0.060000" />
      <FirstPersonProjectileDriveByPassengerIKOffset x="0.000000" y="-0.110000" z="-0.060000" />
      <FirstPersonDriveByRightPassengerIKOffset x="-0.040000" y="-0.12000" z="-0.030000" />
      <FirstPersonDriveByRightPassengerUnarmedIKOffset x="0.000000" y="-0.120000" z="-0.025000" />
      <FirstPersonMobilePhoneOffset x="0.141000" y="0.166000" z="0.560000" />
      <FirstPersonPassengerMobilePhoneOffset x="0.233000" y="0.138000" z="0.440000" />
      <PovCameraOffset x="0.000000" y="-0.275000" z="0.690000" />
      <PovCameraVerticalAdjustmentForRollCage value="0.000000" />
      <PovPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000" />
      <shouldUseCinematicViewMode value="true" />
      <shouldCameraTransitionOnClimbUpDown value="false" />
      <shouldCameraIgnoreExiting value="false" />
      <AllowPretendOccupants value="true" />
      <AllowJoyriding value="true" />
      <AllowSundayDriving value="true" />
      <AllowBodyColorMapping value="true" />
      <wheelScale value="0.280000" />
      <wheelScaleRear value="0.280000" />
      <dirtLevelMin value="0.000000" />
      <dirtLevelMax value="0.800000" />
      <envEffScaleMin value="0.000000" />
      <envEffScaleMax value="1.000000" />
      <envEffScaleMin2 value="0.000000" />
      <envEffScaleMax2 value="1.000000" />
      <damageMapScale value="0.350000" />
      <damageOffsetScale value="0.350000" />
      <diffuseTint value="0x00FFFFFF" />
      <steerWheelMult value="1.000000" />
      <HDTextureDist value="5.000000" />
      <lodDistances content="float_array">
        10.000000
        25.000000
        60.000000
        120.000000
        500.000000
        500.000000
      </lodDistances>
      <minSeatHeight value="0.422" />
      <identicalModelSpawnDistance value="20" />
      <maxNumOfSameColor value="1" />
      <defaultBodyHealth value="1000.000000" />
      <pretendOccupantsScale value="1.000000" />
      <visibleSpawnDistScale value="1.000000" />
      <trackerPathWidth value="2.000000" />
      <weaponForceMult value="1.000000" />
      <frequency value="2" />
      <swankness>SWANKNESS_5</swankness>
      <maxNum value="2" />
      <flags>FLAG_SPORTS FLAG_RICH_CAR FLAG_NO_BROKEN_DOWN_SCENARIO FLAG_RECESSED_TAILLIGHT_CORONAS
        FLAG_COUNT_AS_FACEBOOK_DRIVEN FLAG_EXTRAS_ALL</flags>
      <type>VEHICLE_TYPE_CAR</type>
      <plateType>VPT_BACK_PLATES</plateType>
      <dashboardType>VDT_RACE</dashboardType>
      <vehicleClass>VC_SPORT</vehicleClass>
      <wheelType>VWT_HIEND</wheelType>
      <trailers />
      <additionalTrailers />
      <drivers />
      <extraIncludes />
      <doorsWithCollisionWhenClosed />
      <driveableDoors />
      <bumpersNeedToCollideWithMap value="false" />
      <needsRopeTexture value="false" />
      <requiredExtras />
      <rewards />
      <cinematicPartCamera>
        <Item>WHEEL_FRONT_RIGHT_CAMERA</Item>
        <Item>WHEEL_FRONT_LEFT_CAMERA</Item>
        <Item>WHEEL_REAR_RIGHT_CAMERA</Item>
        <Item>WHEEL_REAR_LEFT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet />
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000" />
      <buoyancySphereSizeScale value="1.000000" />
      <pOverrideRagdollThreshold type="NULL" />
      <firstPersonDrivebyData>
        <Item>LOW_SCHLAGEN_FRONT_LEFT</Item>
        <Item>LOW_SCHLAGEN_FRONT_RIGHT</Item>
      </firstPersonDrivebyData>
    </Item>
    <Item>
      <modelName>sheavas</modelName>
      <txdName>sheavas</txdName>
      <handlingId>SHEAVAS</handlingId>
      <gameName>SHEAVAS</gameName>
      <vehicleMakeName>EMPEROR</vehicleMakeName>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected />
      <ptfxAssetName>null</ptfxAssetName>
      <audioNameHash>lfasound</audioNameHash>
      <layout>LAYOUT_LOW</layout>
      <coverBoundOffsets>COQUETTE_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout />
      <cameraName>FOLLOW_CHEETAH_CAMERA</cameraName>
      <aimCameraName>DEFAULT_THIRD_PERSON_VEHICLE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>VEHICLE_BONNET_CAMERA_LOW_LOW</bonnetCameraName>
      <povCameraName>REDUCED_NEAR_CLIP_POV_CAMERA</povCameraName>
      <FirstPersonDriveByIKOffset x="0.000000" y="-0.080000" z="0.000000" />
      <FirstPersonDriveByUnarmedIKOffset x="-0.050000" y="-0.100000" z="0.000000" />
      <FirstPersonProjectileDriveByIKOffset x="0.070000" y="-0.140000" z="-0.030000" />
      <FirstPersonProjectileDriveByPassengerIKOffset x="-0.070000" y="-0.140000" z="-0.030000" />
      <FirstPersonProjectileDriveByRearLeftIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonProjectileDriveByRearRightIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonDriveByLeftPassengerIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonDriveByRightPassengerIKOffset x="0.000000" y="-0.080000" z="0.000000" />
      <FirstPersonDriveByRightRearPassengerIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonDriveByLeftPassengerUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonDriveByRightPassengerUnarmedIKOffset x="0.050000" y="-0.100000" z="0.000000" />
      <FirstPersonMobilePhoneOffset x="0.148000" y="0.136000" z="0.519000" />
      <FirstPersonPassengerMobilePhoneOffset x="0.179000" y="0.074000" z="0.416000" />
      <PovCameraOffset x="0.000000" y="-0.285000" z="0.630000" />
      <PovCameraVerticalAdjustmentForRollCage value="0.000000" />
      <PovPassengerCameraOffset x="0.000000" y="0.000000" z="-0.010000" />
      <PovRearPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000" />
      <vfxInfoName>VFXVEHICLEINFO_CAR_GENERIC</vfxInfoName>
      <shouldUseCinematicViewMode value="true" />
      <shouldCameraTransitionOnClimbUpDown value="false" />
      <shouldCameraIgnoreExiting value="false" />
      <AllowPretendOccupants value="true" />
      <AllowJoyriding value="true" />
      <AllowSundayDriving value="true" />
      <AllowBodyColorMapping value="true" />
      <wheelScale value="0.306000" />
      <wheelScaleRear value="0.306000" />
      <dirtLevelMin value="0.000000" />
      <dirtLevelMax value="0.300000" />
      <envEffScaleMin value="0.000000" />
      <envEffScaleMax value="1.000000" />
      <envEffScaleMin2 value="0.000000" />
      <envEffScaleMax2 value="1.000000" />
      <damageMapScale value="0.40000" />
      <damageOffsetScale value="0.40000" />
      <diffuseTint value="0x00FFFFFF" />
      <steerWheelMult value="1.000000" />
      <HDTextureDist value="5.000000" />
      <lodDistances content="float_array">
        15.000000
        30.000000
        60.000000
        120.000000
        500.000000
        500.000000
      </lodDistances>
      <identicalModelSpawnDistance value="20" />
      <maxNumOfSameColor value="1" />
      <defaultBodyHealth value="1000.000000" />
      <pretendOccupantsScale value="1.000000" />
      <visibleSpawnDistScale value="1.000000" />
      <trackerPathWidth value="2.000000" />
      <weaponForceMult value="1.000000" />
      <frequency value="20" />
      <swankness>SWANKNESS_5</swankness>
      <maxNum value="3" />
      <flags>FLAG_EXTRAS_ALL FLAG_EXTRAS_REQUIRE FLAG_EXTRAS_STRONG FLAG_SPORTS FLAG_RICH_CAR
        FLAG_RECESSED_HEADLIGHT_CORONAS FLAG_NO_BROKEN_DOWN_SCENARIO FLAG_COUNT_AS_FACEBOOK_DRIVEN
        FLAG_PARKING_SENSORS</flags>
      <type>VEHICLE_TYPE_CAR</type>
      <plateType>VPT_BACK_PLATES</plateType>
      <dashboardType>VDT_RACE</dashboardType>
      <vehicleClass>VC_SUPER</vehicleClass>
      <wheelType>VWT_HIEND</wheelType>
      <trailers />
      <additionalTrailers />
      <drivers />
      <extraIncludes />
      <doorsWithCollisionWhenClosed />
      <driveableDoors />
      <bumpersNeedToCollideWithMap value="false" />
      <needsRopeTexture value="false" />
      <requiredExtras />
      <rewards />
      <cinematicPartCamera>
        <Item>WHEEL_FRONT_RIGHT_CAMERA</Item>
        <Item>WHEEL_FRONT_LEFT_CAMERA</Item>
        <Item>WHEEL_REAR_RIGHT_CAMERA</Item>
        <Item>WHEEL_REAR_LEFT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet />
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000" />
      <buoyancySphereSizeScale value="1.000000" />
      <pOverrideRagdollThreshold type="NULL" />
      <firstPersonDrivebyData>
        <Item>LOW_COQUETTE_FRONT_LEFT</Item>
        <Item>LOW_COQUETTE_FRONT_RIGHT</Item>
      </firstPersonDrivebyData>
    </Item>
    <Item>
      <modelName>stratumc</modelName>
      <txdName>stratumc</txdName>
      <handlingId>STRATUMC</handlingId>
      <gameName>STRATUMC</gameName>
      <vehicleMakeName />
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected />
      <ptfxAssetName>weap_xs_vehicle_weapons</ptfxAssetName>
      <audioNameHash>elegyx</audioNameHash>
      <layout>LAYOUT_STD_STRATUM</layout>
      <coverBoundOffsets>STRATUM_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout />
      <cameraName>DEFAULT_FOLLOW_VEHICLE_CAMERA</cameraName>
      <aimCameraName>MID_BOX_VEHICLE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>VEHICLE_BONNET_CAMERA_MID_EXTRA_HIGH</bonnetCameraName>
      <povCameraName>DEFAULT_POV_CAMERA</povCameraName>
      <FirstPersonDriveByIKOffset x="0.000000" y="-0.060000" z="-0.010000" />
      <FirstPersonDriveByUnarmedIKOffset x="0.000000" y="-0.025000" z="0.000000" />
      <FirstPersonProjectileDriveByIKOffset x="0.070000" y="-0.080000" z="-0.035000" />
      <FirstPersonProjectileDriveByPassengerIKOffset x="-0.070000" y="-0.080000" z="-0.035000" />
      <FirstPersonDriveByLeftPassengerIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonDriveByRightPassengerIKOffset x="0.000000" y="-0.060000" z="-0.010000" />
      <FirstPersonDriveByLeftPassengerUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonDriveByRightPassengerUnarmedIKOffset x="0.000000" y="-0.100000" z="0.000000" />
      <FirstPersonMobilePhoneOffset x="0.113000" y="0.168000" z="0.515000" />
      <FirstPersonPassengerMobilePhoneOffset x="0.135000" y="0.095000" z="0.425000" />
      <FirstPersonMobilePhoneSeatIKOffset>
        <Item>
          <Offset x="0.136000" y="0.066000" z="0.415000" />
          <SeatIndex value="2" />
        </Item>
        <Item>
          <Offset x="0.136000" y="0.066000" z="0.415000" />
          <SeatIndex value="3" />
        </Item>
      </FirstPersonMobilePhoneSeatIKOffset>
      <PovCameraOffset x="0.000000" y="-0.270000" z="0.625000" />
      <PovCameraVerticalAdjustmentForRollCage value="0.000000" />
      <PovPassengerCameraOffset x="0.000000" y="0.025000" z="0.000000" />
      <vfxInfoName>VFXVEHICLEINFO_CAR_GENERIC</vfxInfoName>
      <shouldUseCinematicViewMode value="true" />
      <shouldCameraTransitionOnClimbUpDown value="false" />
      <shouldCameraIgnoreExiting value="false" />
      <AllowPretendOccupants value="true" />
      <AllowJoyriding value="true" />
      <AllowSundayDriving value="true" />
      <AllowBodyColorMapping value="true" />
      <wheelScale value="0.286000" />
      <wheelScaleRear value="0.286000" />
      <dirtLevelMin value="0.000000" />
      <dirtLevelMax value="0.300000" />
      <envEffScaleMin value="0.000000" />
      <envEffScaleMax value="1.000000" />
      <envEffScaleMin2 value="0.000000" />
      <envEffScaleMax2 value="1.000000" />
      <damageMapScale value="0.600000" />
      <damageOffsetScale value="1.000000" />
      <diffuseTint value="0x00FFFFFF" />
      <steerWheelMult value="1.000000" />
      <HDTextureDist value="5.000000" />
      <lodDistances content="float_array">
        20.000000
        40.000000
        80.000000
        120.000000
        500.000000
        500.000000
      </lodDistances>
      <minSeatHeight value="0.787" />
      <identicalModelSpawnDistance value="80" />
      <maxNumOfSameColor value="1" />
      <defaultBodyHealth value="1000.000000" />
      <pretendOccupantsScale value="1.000000" />
      <visibleSpawnDistScale value="1.000000" />
      <trackerPathWidth value="2.000000" />
      <weaponForceMult value="1.000000" />
      <frequency value="3" />
      <swankness>SWANKNESS_4</swankness>
      <maxNum value="1" />
      <flags>FLAG_EXTRAS_SUNROOF FLAG_NO_BROKEN_DOWN_SCENARIO FLAG_DROP_SUSPENSION_WHEN_STOPPED
        FLAG_EXTRAS_STRONG FLAG_HAS_NITROUS_MOD</flags>
      <type>VEHICLE_TYPE_CAR</type>
      <plateType>VPT_BACK_PLATES</plateType>
      <dashboardType>VDT_FEROCI</dashboardType>
      <vehicleClass>VC_SPORT</vehicleClass>
      <wheelType>VWT_SPORT</wheelType>
      <trailers />
      <additionalTrailers />
      <drivers />
      <extraIncludes />
      <doorsWithCollisionWhenClosed />
      <driveableDoors />
      <bumpersNeedToCollideWithMap value="true" />
      <needsRopeTexture value="false" />
      <requiredExtras />
      <rewards />
      <cinematicPartCamera>
        <Item>WHEEL_FRONT_RIGHT_CAMERA</Item>
        <Item>WHEEL_FRONT_LEFT_CAMERA</Item>
        <Item>WHEEL_REAR_RIGHT_CAMERA</Item>
        <Item>WHEEL_REAR_LEFT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet />
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000" />
      <buoyancySphereSizeScale value="1.000000" />
      <pOverrideRagdollThreshold type="NULL" />
      <firstPersonDrivebyData>
        <Item>STD_PREMIER_FRONT_LEFT</Item>
        <Item>STD_PREMIER_FRONT_RIGHT</Item>
      </firstPersonDrivebyData>
    </Item>
    <Item>
      <modelName>sultanrsv8</modelName>
      <txdName>sultanrsv8</txdName>
      <handlingId>SULTANRSV8</handlingId>
      <gameName>SULTANRSV8</gameName>
      <vehicleMakeName>KARIN</vehicleMakeName>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected />
      <ptfxAssetName>null</ptfxAssetName>
      <audioNameHash>sultanrsv8</audioNameHash>
      <layout>LAYOUT_STD_EXITFIXUP</layout>
      <coverBoundOffsets>SULTANRS_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout />
      <cameraName>DEFAULT_FOLLOW_VEHICLE_CAMERA</cameraName>
      <aimCameraName>MID_BOX_VEHICLE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>VEHICLE_BONNET_CAMERA_NEAR</bonnetCameraName>
      <povCameraName>DEFAULT_POV_CAMERA</povCameraName>
      <FirstPersonDriveByIKOffset x="0.000000" y="-0.060000" z="-0.060000" />
      <FirstPersonDriveByUnarmedIKOffset x="0.000000" y="-0.025000" z="0.000000" />
      <FirstPersonProjectileDriveByIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonProjectileDriveByPassengerIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonProjectileDriveByRearLeftIKOffset x="0.000000" y="0.020000" z="0.000000" />
      <FirstPersonProjectileDriveByRearRightIKOffset x="0.000000" y="0.020000" z="0.000000" />
      <FirstPersonDriveByLeftPassengerIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonDriveByRightPassengerIKOffset x="0.000000" y="-0.060000" z="-0.060000" />
      <FirstPersonDriveByRightRearPassengerIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonDriveByLeftPassengerUnarmedIKOffset x="0.000000" y="-0.080000" z="0.000000" />
      <FirstPersonDriveByRightPassengerUnarmedIKOffset x="0.000000" y="-0.080000" z="0.000000" />
      <FirstPersonMobilePhoneOffset x="0.110000" y="0.215000" z="0.518000" />
      <FirstPersonPassengerMobilePhoneOffset x="0.185000" y="0.125000" z="0.433000" />
      <FirstPersonMobilePhoneSeatIKOffset />
      <PovCameraOffset x="0.000000" y="-0.210000" z="0.645000" />
      <PovCameraVerticalAdjustmentForRollCage value="-0.045000" />
      <PovPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000" />
      <PovRearPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000" />
      <vfxInfoName>VFXVEHICLEINFO_CAR_GENERIC</vfxInfoName>
      <shouldUseCinematicViewMode value="true" />
      <shouldCameraTransitionOnClimbUpDown value="false" />
      <shouldCameraIgnoreExiting value="false" />
      <AllowPretendOccupants value="true" />
      <AllowJoyriding value="true" />
      <AllowSundayDriving value="true" />
      <AllowBodyColorMapping value="true" />
      <wheelScale value="0.238100" />
      <wheelScaleRear value="0.238100" />
      <dirtLevelMin value="0.000000" />
      <dirtLevelMax value="0.700000" />
      <envEffScaleMin value="0.000000" />
      <envEffScaleMax value="1.000000" />
      <envEffScaleMin2 value="0.000000" />
      <envEffScaleMax2 value="1.000000" />
      <damageMapScale value="0.400000" />
      <damageOffsetScale value="0.400000" />
      <diffuseTint value="0x00FFFFFF" />
      <steerWheelMult value="0.700000" />
      <HDTextureDist value="5.000000" />
      <lodDistances content="float_array">
        10.000000
        25.000000
        60.000000
        120.000000
        500.000000
        500.000000
      </lodDistances>
      <minSeatHeight value="0.832" />
      <identicalModelSpawnDistance value="20" />
      <maxNumOfSameColor value="10" />
      <defaultBodyHealth value="1000.000000" />
      <pretendOccupantsScale value="1.000000" />
      <visibleSpawnDistScale value="1.000000" />
      <trackerPathWidth value="2.000000" />
      <weaponForceMult value="1.000000" />
      <frequency value="50" />
      <swankness>SWANKNESS_2</swankness>
      <maxNum value="50" />
      <flags>FLAG_AVERAGE_CAR FLAG_POOR_CAR FLAG_HAS_INTERIOR_EXTRAS</flags>
      <type>VEHICLE_TYPE_CAR</type>
      <plateType>VPT_BACK_PLATES</plateType>
      <dashboardType>VDT_DUKES</dashboardType>
      <vehicleClass>VC_SUPER</vehicleClass>
      <wheelType>VWT_SPORT</wheelType>
      <trailers />
      <additionalTrailers />
      <drivers />
      <extraIncludes />
      <extraIncludes>
        <Item>EXTRA_1 EXTRA_2</Item>
      </extraIncludes>
      <doorsWithCollisionWhenClosed />
      <driveableDoors />
      <bumpersNeedToCollideWithMap value="false" />
      <needsRopeTexture value="false" />
      <rewards />
      <cinematicPartCamera>
        <Item>WHEEL_FRONT_RIGHT_CAMERA</Item>
        <Item>WHEEL_FRONT_LEFT_CAMERA</Item>
        <Item>WHEEL_REAR_RIGHT_CAMERA</Item>
        <Item>WHEEL_REAR_LEFT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet />
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000" />
      <buoyancySphereSizeScale value="1.000000" />
      <pOverrideRagdollThreshold type="NULL" />
      <firstPersonDrivebyData>
        <Item>STD_SULTANRS_FRONT_LEFT</Item>
        <Item>STD_SULTANRS_FRONT_RIGHT</Item>
      </firstPersonDrivebyData>
    </Item>
    <Item>
      <modelName>tampax2</modelName>
      <txdName>tampax2</txdName>
      <handlingId>TAMPAX</handlingId>
      <gameName>TAMPAX2</gameName>
      <vehicleMakeName>DECLASSE</vehicleMakeName>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected />
      <ptfxAssetName>null</ptfxAssetName>
      <audioNameHash>ignus</audioNameHash>
      <layout>LAYOUT_LOW</layout>
      <coverBoundOffsets>TAMPA2_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout />
      <cameraName>FOLLOW_CHEETAH_CAMERA</cameraName>
      <aimCameraName>MID_BOX_VEHICLE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>VEHICLE_BONNET_CAMERA_STANDARD_LONG</bonnetCameraName>
      <povCameraName>DEFAULT_POV_CAMERA</povCameraName>
      <FirstPersonDriveByIKOffset x="0.000000" y="-0.040000" z="0.000000" />
      <FirstPersonDriveByUnarmedIKOffset x="0.000000" y="-0.050000" z="0.000000" />
      <FirstPersonProjectileDriveByIKOffset x="0.000000" y="-0.100000" z="0.000000" />
      <FirstPersonProjectileDriveByPassengerIKOffset x="0.000000" y="-0.100000" z="0.000000" />
      <FirstPersonProjectileDriveByRearLeftIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonProjectileDriveByRearRightIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonDriveByLeftPassengerIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonDriveByRightPassengerIKOffset x="0.000000" y="-0.040000" z="0.000000" />
      <FirstPersonDriveByRightRearPassengerIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonDriveByLeftPassengerUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonDriveByRightPassengerUnarmedIKOffset x="0.000000" y="-0.050000" z="0.000000" />
      <FirstPersonMobilePhoneOffset x="0.143000" y="0.101000" z="0.546000" />
      <FirstPersonPassengerMobilePhoneOffset x="0.161000" y="0.069000" z="0.459000" />
      <PovCameraOffset x="0.000000" y="-0.330000" z="0.650000" />
      <PovCameraVerticalAdjustmentForRollCage value="0.000000" />
      <PovPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000" />
      <PovRearPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000" />
      <vfxInfoName>VFXVEHICLEINFO_CAR_GENERIC</vfxInfoName>
      <shouldUseCinematicViewMode value="true" />
      <shouldCameraTransitionOnClimbUpDown value="false" />
      <shouldCameraIgnoreExiting value="false" />
      <AllowPretendOccupants value="true" />
      <AllowJoyriding value="true" />
      <AllowSundayDriving value="true" />
      <AllowBodyColorMapping value="true" />
      <wheelScale value="0.201000" />
      <wheelScaleRear value="0.201000" />
      <dirtLevelMin value="0.000000" />
      <dirtLevelMax value="0.450000" />
      <envEffScaleMin value="0.000000" />
      <envEffScaleMax value="1.000000" />
      <envEffScaleMin2 value="0.000000" />
      <envEffScaleMax2 value="1.000000" />
      <damageMapScale value="0.350000" />
      <damageOffsetScale value="0.350000" />
      <diffuseTint value="0x00FFFFFF" />
      <steerWheelMult value="1.000000" />
      <HDTextureDist value="5.000000" />
      <lodDistances content="float_array">
        15.000000
        30.000000
        60.000000
        120.000000
        500.000000
        500.000000
      </lodDistances>
      <minSeatHeight value="0.844" />
      <identicalModelSpawnDistance value="20" />
      <maxNumOfSameColor value="1" />
      <defaultBodyHealth value="1000.000000" />
      <pretendOccupantsScale value="1.000000" />
      <visibleSpawnDistScale value="1.000000" />
      <trackerPathWidth value="2.000000" />
      <weaponForceMult value="1.000000" />
      <frequency value="30" />
      <swankness>SWANKNESS_4</swankness>
      <maxNum value="10" />
      <flags>FLAG_EXTRAS_STRONG FLAG_AVERAGE_CAR FLAG_HAS_INTERIOR_EXTRAS FLAG_CAN_HAVE_NEONS</flags>
      <type>VEHICLE_TYPE_CAR</type>
      <plateType>VPT_BACK_PLATES</plateType>
      <dashboardType>VDT_RACE</dashboardType>
      <vehicleClass>VC_SPORT</vehicleClass>
      <wheelType>VWT_MUSCLE</wheelType>
      <trailers />
      <additionalTrailers />
      <drivers />
      <extraIncludes />
      <doorsWithCollisionWhenClosed />
      <driveableDoors />
      <bumpersNeedToCollideWithMap value="false" />
      <needsRopeTexture value="false" />
      <requiredExtras />
      <rewards />
      <cinematicPartCamera>
        <Item>WHEEL_FRONT_RIGHT_CAMERA</Item>
        <Item>WHEEL_FRONT_LEFT_CAMERA</Item>
        <Item>WHEEL_REAR_RIGHT_CAMERA</Item>
        <Item>WHEEL_REAR_LEFT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet />
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000" />
      <buoyancySphereSizeScale value="1.000000" />
      <pOverrideRagdollThreshold type="NULL" />
      <firstPersonDrivebyData>
        <Item>LOW_TAMPA2_FRONT_LEFT</Item>
        <Item>LOW_TAMPA2_FRONT_RIGHT</Item>
      </firstPersonDrivebyData>
    </Item>
    <Item>
      <modelName>tampax</modelName>
      <txdName>tampax</txdName>
      <handlingId>TAMPAX</handlingId>
      <gameName>TAMPAX</gameName>
      <vehicleMakeName>DECLASSE</vehicleMakeName>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected />
      <ptfxAssetName>null</ptfxAssetName>
      <audioNameHash>ignus</audioNameHash>
      <layout>LAYOUT_LOW</layout>
      <coverBoundOffsets>TAMPA2_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout />
      <cameraName>FOLLOW_CHEETAH_CAMERA</cameraName>
      <aimCameraName>MID_BOX_VEHICLE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>VEHICLE_BONNET_CAMERA_STANDARD_LONG</bonnetCameraName>
      <povCameraName>DEFAULT_POV_CAMERA</povCameraName>
      <FirstPersonDriveByIKOffset x="0.000000" y="-0.040000" z="0.000000" />
      <FirstPersonDriveByUnarmedIKOffset x="0.000000" y="-0.050000" z="0.000000" />
      <FirstPersonProjectileDriveByIKOffset x="0.000000" y="-0.100000" z="0.000000" />
      <FirstPersonProjectileDriveByPassengerIKOffset x="0.000000" y="-0.100000" z="0.000000" />
      <FirstPersonProjectileDriveByRearLeftIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonProjectileDriveByRearRightIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonDriveByLeftPassengerIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonDriveByRightPassengerIKOffset x="0.000000" y="-0.040000" z="0.000000" />
      <FirstPersonDriveByRightRearPassengerIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonDriveByLeftPassengerUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonDriveByRightPassengerUnarmedIKOffset x="0.000000" y="-0.050000" z="0.000000" />
      <FirstPersonMobilePhoneOffset x="0.143000" y="0.101000" z="0.546000" />
      <FirstPersonPassengerMobilePhoneOffset x="0.161000" y="0.069000" z="0.459000" />
      <PovCameraOffset x="0.000000" y="-0.330000" z="0.650000" />
      <PovCameraVerticalAdjustmentForRollCage value="0.000000" />
      <PovPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000" />
      <PovRearPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000" />
      <vfxInfoName>VFXVEHICLEINFO_CAR_GENERIC</vfxInfoName>
      <shouldUseCinematicViewMode value="true" />
      <shouldCameraTransitionOnClimbUpDown value="false" />
      <shouldCameraIgnoreExiting value="false" />
      <AllowPretendOccupants value="true" />
      <AllowJoyriding value="true" />
      <AllowSundayDriving value="true" />
      <AllowBodyColorMapping value="true" />
      <wheelScale value="0.201000" />
      <wheelScaleRear value="0.201000" />
      <dirtLevelMin value="0.000000" />
      <dirtLevelMax value="0.450000" />
      <envEffScaleMin value="0.000000" />
      <envEffScaleMax value="1.000000" />
      <envEffScaleMin2 value="0.000000" />
      <envEffScaleMax2 value="1.000000" />
      <damageMapScale value="0.350000" />
      <damageOffsetScale value="0.350000" />
      <diffuseTint value="0x00FFFFFF" />
      <steerWheelMult value="1.000000" />
      <HDTextureDist value="5.000000" />
      <lodDistances content="float_array">
        15.000000
        30.000000
        60.000000
        120.000000
        500.000000
        500.000000
      </lodDistances>
      <minSeatHeight value="0.844" />
      <identicalModelSpawnDistance value="20" />
      <maxNumOfSameColor value="1" />
      <defaultBodyHealth value="1000.000000" />
      <pretendOccupantsScale value="1.000000" />
      <visibleSpawnDistScale value="1.000000" />
      <trackerPathWidth value="2.000000" />
      <weaponForceMult value="1.000000" />
      <frequency value="30" />
      <swankness>SWANKNESS_4</swankness>
      <maxNum value="10" />
      <flags>FLAG_EXTRAS_STRONG FLAG_AVERAGE_CAR FLAG_HAS_INTERIOR_EXTRAS FLAG_CAN_HAVE_NEONS</flags>
      <type>VEHICLE_TYPE_CAR</type>
      <plateType>VPT_BACK_PLATES</plateType>
      <dashboardType>VDT_RACE</dashboardType>
      <vehicleClass>VC_SPORT</vehicleClass>
      <wheelType>VWT_MUSCLE</wheelType>
      <trailers />
      <additionalTrailers />
      <drivers />
      <extraIncludes />
      <doorsWithCollisionWhenClosed />
      <driveableDoors />
      <bumpersNeedToCollideWithMap value="false" />
      <needsRopeTexture value="false" />
      <requiredExtras />
      <rewards />
      <cinematicPartCamera>
        <Item>WHEEL_FRONT_RIGHT_CAMERA</Item>
        <Item>WHEEL_FRONT_LEFT_CAMERA</Item>
        <Item>WHEEL_REAR_RIGHT_CAMERA</Item>
        <Item>WHEEL_REAR_LEFT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet />
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000" />
      <buoyancySphereSizeScale value="1.000000" />
      <pOverrideRagdollThreshold type="NULL" />
      <firstPersonDrivebyData>
        <Item>LOW_TAMPA2_FRONT_LEFT</Item>
        <Item>LOW_TAMPA2_FRONT_RIGHT</Item>
      </firstPersonDrivebyData>
    </Item>
    <Item>
      <modelName>omnisegt</modelName>
      <txdName>omnisegt</txdName>
      <handlingId>OMNISEGT</handlingId>
      <gameName>OMNISEGT</gameName>
      <vehicleMakeName>OBEY</vehicleMakeName>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected />
      <ptfxAssetName>null</ptfxAssetName>
      <audioNameHash />
      <layout>LAYOUT_STANDARD</layout>
      <coverBoundOffsets>KOMODA_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout />
      <cameraName>FOLLOW_CHEETAH_CAMERA</cameraName>
      <aimCameraName>DEFAULT_THIRD_PERSON_VEHICLE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>VEHICLE_BONNET_CAMERA_STOCKADE</bonnetCameraName>
      <povCameraName>REDUCED_NEAR_CLIP_POV_CAMERA</povCameraName>
      <FirstPersonDriveByIKOffset x="0.010000" y="-0.110000" z="-0.050000" />
      <FirstPersonDriveByUnarmedIKOffset x="0.000000" y="-0.080000" z="-0.020000" />
      <FirstPersonProjectileDriveByIKOffset x="0.080000" y="-0.150000" z="-0.020000" />
      <FirstPersonProjectileDriveByPassengerIKOffset x="-0.100000" y="-0.160000" z="-0.030000" />
      <FirstPersonProjectileDriveByRearLeftIKOffset x="-0.010000" y="-0.100000" z="-0.050000" />
      <FirstPersonProjectileDriveByRearRightIKOffset x="0.010000" y="-0.100000" z="-0.050000" />
      <FirstPersonDriveByLeftPassengerIKOffset x="-0.020000" y="-0.030000" z="-0.010000" />
      <FirstPersonDriveByRightPassengerIKOffset x="-0.010000" y="-0.110000" z="-0.050000" />
      <FirstPersonDriveByRightRearPassengerIKOffset x="0.000000" y="-0.020000" z="-0.010000" />
      <FirstPersonDriveByLeftPassengerUnarmedIKOffset x="0.020000" y="-0.200000" z="-0.020000" />
      <FirstPersonDriveByRightPassengerUnarmedIKOffset x="0.000000" y="-0.140000" z="-0.040000" />
      <FirstPersonMobilePhoneOffset x="0.153000" y="0.173000" z="0.533000" />
      <FirstPersonPassengerMobilePhoneOffset x="0.231000" y="0.133000" z="0.420000" />
      <FirstPersonMobilePhoneSeatIKOffset>
        <Item>
          <Offset x="0.211000" y="0.103000" z="0.420000" />
          <SeatIndex value="2" />
        </Item>
        <Item>
          <Offset x="0.211000" y="0.103000" z="0.425000" />
          <SeatIndex value="3" />
        </Item>
      </FirstPersonMobilePhoneSeatIKOffset>
      <PovCameraOffset x="0.000000" y="-0.230000" z="0.660000" />
      <PovCameraVerticalAdjustmentForRollCage value="0.000000" />
      <PovPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000" />
      <PovRearPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000" />
      <vfxInfoName>VFXVEHICLEINFO_CAR_GENERIC</vfxInfoName>
      <shouldUseCinematicViewMode value="true" />
      <shouldCameraTransitionOnClimbUpDown value="false" />
      <shouldCameraIgnoreExiting value="false" />
      <AllowPretendOccupants value="true" />
      <AllowJoyriding value="true" />
      <AllowSundayDriving value="true" />
      <AllowBodyColorMapping value="true" />
      <wheelScale value="0.299000" />
      <wheelScaleRear value="0.299000" />
      <dirtLevelMin value="0.000000" />
      <dirtLevelMax value="0.300000" />
      <envEffScaleMin value="0.000000" />
      <envEffScaleMax value="1.000000" />
      <envEffScaleMin2 value="0.000000" />
      <envEffScaleMax2 value="1.000000" />
      <damageMapScale value="0.400000" />
      <damageOffsetScale value="0.400000" />
      <diffuseTint value="0x00FFFFFF" />
      <steerWheelMult value="1.000000" />
      <HDTextureDist value="5.000000" />
      <lodDistances content="float_array">
        15.000000
        30.000000
        70.000000
        140.000000
        300.000000
        300.000000
      </lodDistances>
      <minSeatHeight value="0.481" />
      <identicalModelSpawnDistance value="20" />
      <maxNumOfSameColor value="1" />
      <defaultBodyHealth value="1000.000000" />
      <pretendOccupantsScale value="1.000000" />
      <visibleSpawnDistScale value="1.000000" />
      <trackerPathWidth value="2.000000" />
      <weaponForceMult value="1.000000" />
      <frequency value="50" />
      <swankness>SWANKNESS_5</swankness>
      <maxNum value="5" />
      <flags>FLAG_SPORTS FLAG_RICH_CAR FLAG_NO_BROKEN_DOWN_SCENARIO FLAG_CAN_HAVE_NEONS
        FLAG_IS_ELECTRIC FLAG_INCREASE_CAMBER_WITH_SUSPENSION_MOD</flags>
      <type>VEHICLE_TYPE_CAR</type>
      <plateType>VPT_BACK_PLATES</plateType>
      <dashboardType>VDT_RACE</dashboardType>
      <vehicleClass>VC_SPORT</vehicleClass>
      <wheelType>VWT_HIEND</wheelType>
      <trailers />
      <additionalTrailers />
      <drivers />
      <extraIncludes />
      <doorsWithCollisionWhenClosed />
      <driveableDoors />
      <bumpersNeedToCollideWithMap value="false" />
      <needsRopeTexture value="false" />
      <requiredExtras />
      <rewards />
      <cinematicPartCamera>
        <Item>WHEEL_FRONT_RIGHT_CAMERA</Item>
        <Item>WHEEL_FRONT_LEFT_CAMERA</Item>
        <Item>WHEEL_REAR_RIGHT_CAMERA</Item>
        <Item>WHEEL_REAR_LEFT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet />
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000" />
      <buoyancySphereSizeScale value="1.000000" />
      <pOverrideRagdollThreshold type="NULL" />
      <firstPersonDrivebyData>
        <Item>STD_OMNISEGT_FRONT_LEFT</Item>
        <Item>STD_OMNISEGT_FRONT_RIGHT</Item>
        <Item>STD_OMNISEGT_REAR_LEFT</Item>
        <Item>STD_OMNISEGT_REAR_RIGHT</Item>
      </firstPersonDrivebyData>
    </Item>
    <Item>
      <modelName>corsita</modelName>
      <txdName>corsita</txdName>
      <handlingId>CORSITA</handlingId>
      <gameName>CORSITA</gameName>
      <vehicleMakeName>LAMPADA</vehicleMakeName>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected />
      <ptfxAssetName>null</ptfxAssetName>
      <audioNameHash />
      <layout>LAYOUT_LOW_CORSITA</layout>
      <coverBoundOffsets>CORSITA_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout />
      <cameraName>FOLLOW_CHEETAH_CAMERA</cameraName>
      <aimCameraName>DEFAULT_THIRD_PERSON_VEHICLE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>VEHICLE_BONNET_CAMERA_LOW_CALICO</bonnetCameraName>
      <povCameraName>REDUCED_NEAR_CLIP_POV_CAMERA_NO_REVERSE</povCameraName>
      <FirstPersonDriveByIKOffset x="0.000000" y="-0.100000" z="-0.040000" />
      <FirstPersonDriveByUnarmedIKOffset x="0.000000" y="-0.160000" z="-0.025000" />
      <FirstPersonProjectileDriveByIKOffset x="0.010000" y="-0.140000" z="-0.060000" />
      <FirstPersonProjectileDriveByPassengerIKOffset x="-0.020000" y="-0.140000" z="-0.060000" />
      <FirstPersonDriveByRightPassengerIKOffset x="0.000000" y="-0.100000" z="-0.040000" />
      <FirstPersonDriveByRightPassengerUnarmedIKOffset x="0.000000" y="-0.160000" z="-0.025000" />
      <FirstPersonMobilePhoneOffset x="0.133000" y="0.133000" z="0.526000" />
      <FirstPersonPassengerMobilePhoneOffset x="0.209000" y="0.124000" z="0.401000" />
      <PovCameraOffset x="0.000000" y="-0.285000" z="0.600000" />
      <PovCameraVerticalAdjustmentForRollCage value="0.000000" />
      <PovPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000" />
      <PovRearPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000" />
      <vfxInfoName>VFXVEHICLEINFO_CAR_GENERIC</vfxInfoName>
      <shouldUseCinematicViewMode value="true" />
      <shouldCameraTransitionOnClimbUpDown value="false" />
      <shouldCameraIgnoreExiting value="false" />
      <AllowPretendOccupants value="true" />
      <AllowJoyriding value="true" />
      <AllowSundayDriving value="true" />
      <AllowBodyColorMapping value="true" />
      <wheelScale value="0.297000" />
      <wheelScaleRear value="0.297000" />
      <dirtLevelMin value="0.000000" />
      <dirtLevelMax value="0.400000" />
      <envEffScaleMin value="0.000000" />
      <envEffScaleMax value="1.000000" />
      <envEffScaleMin2 value="0.000000" />
      <envEffScaleMax2 value="1.000000" />
      <damageMapScale value="0.40000" />
      <damageOffsetScale value="0.40000" />
      <diffuseTint value="0x00FFFFFF" />
      <steerWheelMult value="1.000000" />
      <HDTextureDist value="5.000000" />
      <lodDistances content="float_array">
        10.000000
        25.000000
        60.000000
        120.000000
        500.000000
        500.000000
      </lodDistances>
      <identicalModelSpawnDistance value="20" />
      <maxNumOfSameColor value="1" />
      <defaultBodyHealth value="1000.000000" />
      <pretendOccupantsScale value="1.000000" />
      <visibleSpawnDistScale value="1.000000" />
      <trackerPathWidth value="2.000000" />
      <weaponForceMult value="1.000000" />
      <frequency value="30" />
      <swankness>SWANKNESS_5</swankness>
      <maxNum value="4" />
      <flags>FLAG_SPORTS FLAG_RICH_CAR FLAG_RECESSED_HEADLIGHT_CORONAS FLAG_NO_BROKEN_DOWN_SCENARIO
        FLAG_COUNT_AS_FACEBOOK_DRIVEN</flags>
      <type>VEHICLE_TYPE_CAR</type>
      <plateType>VPT_BACK_PLATES</plateType>
      <dashboardType>VDT_RACE</dashboardType>
      <vehicleClass>VC_SPORT</vehicleClass>
      <wheelType>VWT_HIEND</wheelType>
      <trailers />
      <additionalTrailers />
      <drivers />
      <extraIncludes />
      <doorsWithCollisionWhenClosed />
      <driveableDoors />
      <bumpersNeedToCollideWithMap value="false" />
      <needsRopeTexture value="false" />
      <requiredExtras />
      <rewards />
      <cinematicPartCamera>
        <Item>WHEEL_FRONT_RIGHT_CAMERA</Item>
        <Item>WHEEL_FRONT_LEFT_CAMERA</Item>
        <Item>WHEEL_REAR_RIGHT_CAMERA</Item>
        <Item>WHEEL_REAR_LEFT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet />
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000" />
      <buoyancySphereSizeScale value="1.000000" />
      <pOverrideRagdollThreshold type="NULL" />
      <firstPersonDrivebyData>
        <Item>LOW_CORSITA_FRONT_LEFT</Item>
        <Item>LOW_CORSITA_FRONT_RIGHT</Item>
      </firstPersonDrivebyData>
    </Item>
    <Item>
      <modelName>draugur</modelName>
      <txdName>draugur</txdName>
      <handlingId>DRAUGUR</handlingId>
      <gameName>DRAUGUR</gameName>
      <vehicleMakeName>DECLASSE</vehicleMakeName>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected />
      <ptfxAssetName>null</ptfxAssetName>
      <audioNameHash />
      <layout>LAYOUT_STD_DRAUGUR</layout>
      <coverBoundOffsets>DRAUGUR_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout />
      <cameraName>FOLLOW_JEEP_CAMERA</cameraName>
      <aimCameraName>JEEP_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>VEHICLE_BONNET_CAMERA_NEAR_LOW</bonnetCameraName>
      <povCameraName>REDUCED_NEAR_CLIP_POV_CAMERA</povCameraName>
      <FirstPersonDriveByIKOffset x="0.100000" y="-0.110000" z="-0.020000" />
      <FirstPersonDriveByUnarmedIKOffset x="0.000000" y="-0.025000" z="0.000000" />
      <FirstPersonProjectileDriveByIKOffset x="0.000000" y="-0.060000" z="0.000000" />
      <FirstPersonProjectileDriveByPassengerIKOffset x="0.000000" y="-0.040000" z="0.000000" />
      <FirstPersonProjectileDriveByRearLeftIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonProjectileDriveByRearRightIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonDriveByLeftPassengerIKOffset x="-0.060000" y="0.000000" z="0.000000" />
      <FirstPersonDriveByRightPassengerIKOffset x="0.100000" y="-0.080000" z="-0.020000" />
      <FirstPersonDriveByRightRearPassengerIKOffset x="0.060000" y="0.000000" z="0.000000" />
      <FirstPersonDriveByLeftPassengerUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonDriveByRightPassengerUnarmedIKOffset x="0.000000" y="-0.025000" z="0.000000" />
      <FirstPersonMobilePhoneOffset x="0.143000" y="0.171000" z="0.536000" />
      <FirstPersonPassengerMobilePhoneOffset x="0.190000" y="0.138000" z="0.445000" />
      <PovCameraOffset x="-0.080000" y="-0.190000" z="0.615000" />
      <PovCameraVerticalAdjustmentForRollCage value="0.000000" />
      <PovPassengerCameraOffset x="0.000000" y="0.020000" z="0.040000" />
      <PovRearPassengerCameraOffset x="0.100000" y="-0.070000" z="0.020000" />
      <vfxInfoName>VFXVEHICLEINFO_CAR_OFFROAD</vfxInfoName>
      <shouldUseCinematicViewMode value="true" />
      <shouldCameraTransitionOnClimbUpDown value="false" />
      <shouldCameraIgnoreExiting value="false" />
      <AllowPretendOccupants value="true" />
      <AllowJoyriding value="true" />
      <AllowSundayDriving value="true" />
      <AllowBodyColorMapping value="true" />
      <wheelScale value="0.295000" />
      <wheelScaleRear value="0.295000" />
      <dirtLevelMin value="0.900000" />
      <dirtLevelMax value="0.990000" />
      <envEffScaleMin value="0.500000" />
      <envEffScaleMax value="1.000000" />
      <envEffScaleMin2 value="0.000000" />
      <envEffScaleMax2 value="1.000000" />
      <damageMapScale value="0.350000" />
      <damageOffsetScale value="0.350000" />
      <diffuseTint value="0x00FFFFFF" />
      <steerWheelMult value="0.700000" />
      <HDTextureDist value="5.000000" />
      <lodDistances content="float_array">
        15.000000
        35.000000
        90.000000
        180.000000
        500.000000
        500.000000
      </lodDistances>
      <minSeatHeight value="0.87" />
      <identicalModelSpawnDistance value="20" />
      <maxNumOfSameColor value="10" />
      <defaultBodyHealth value="1000.000000" />
      <pretendOccupantsScale value="1.000000" />
      <visibleSpawnDistScale value="1.000000" />
      <trackerPathWidth value="2.000000" />
      <weaponForceMult value="1.000000" />
      <frequency value="50" />
      <swankness>SWANKNESS_4</swankness>
      <maxNum value="10" />
      <flags>FLAG_NO_BOOT FLAG_EXTRAS_ALL FLAG_AVERAGE_CAR FLAG_POOR_CAR FLAG_IS_OFFROAD_VEHICLE
        FLAG_HEADLIGHTS_USE_ACTUAL_BONE_POS FLAG_IS_BULKY FLAG_HAS_INTERIOR_EXTRAS</flags>
      <type>VEHICLE_TYPE_CAR</type>
      <dashboardType>VDT_RACE</dashboardType>
      <plateType>VPT_NONE</plateType>
      <vehicleClass>VC_OFF_ROAD</vehicleClass>
      <wheelType>VWT_OFFROAD</wheelType>
      <trailers />
      <additionalTrailers />
      <drivers />
      <extraIncludes />
      <doorsWithCollisionWhenClosed>
        <Item>VEH_EXT_BOOT</Item>
        <Item>VEH_EXT_BONNET</Item>
      </doorsWithCollisionWhenClosed>
      <driveableDoors />
      <bumpersNeedToCollideWithMap value="false" />
      <needsRopeTexture value="false" />
      <requiredExtras />
      <rewards />
      <cinematicPartCamera>
        <Item>WHEEL_FRONT_RIGHT_CAMERA</Item>
        <Item>WHEEL_FRONT_LEFT_CAMERA</Item>
        <Item>WHEEL_REAR_RIGHT_CAMERA</Item>
        <Item>WHEEL_REAR_LEFT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet>Truck</NmBraceOverrideSet>
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000" />
      <buoyancySphereSizeScale value="1.000000" />
      <pOverrideRagdollThreshold type="NULL" />
      <firstPersonDrivebyData>
        <Item>STD_DRAUGUR_FRONT_LEFT</Item>
        <Item>STD_DRAUGUR_FRONT_RIGHT</Item>
        <Item>STD_DRAUGUR_REAR_LEFT</Item>
        <Item>STD_DRAUGUR_REAR_RIGHT</Item>
      </firstPersonDrivebyData>
    </Item>
    <Item>
      <modelName>postlude</modelName>
      <txdName>postlude</txdName>
      <handlingId>POSTLUDE</handlingId>
      <gameName>POSTLUDE</gameName>
      <vehicleMakeName>DINKA</vehicleMakeName>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected />
      <ptfxAssetName>null</ptfxAssetName>
      <audioNameHash />
      <layout>LAYOUT_LOW_RESTRICTED_POSTLUDE</layout>
      <coverBoundOffsets>POSTLUDE_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout />
      <cameraName>FOLLOW_CHEETAH_CAMERA</cameraName>
      <aimCameraName>DEFAULT_THIRD_PERSON_VEHICLE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>VEHICLE_BONNET_CAMERA_STANDARD_LONG_EXTRA_LOW</bonnetCameraName>
      <povCameraName>REDUCED_NEAR_CLIP_POV_CAMERA</povCameraName>
      <FirstPersonDriveByIKOffset x="0.050000" y="-0.110000" z="-0.040000" />
      <FirstPersonDriveByUnarmedIKOffset x="-0.010000" y="-0.100000" z="-0.010000" />
      <FirstPersonProjectileDriveByIKOffset x="0.060000" y="-0.125000" z="-0.040000" />
      <FirstPersonProjectileDriveByPassengerIKOffset x="-0.060000" y="-0.145000" z="-0.040000" />
      <FirstPersonDriveByRightPassengerIKOffset x="-0.050000" y="-0.070000" z="-0.040000" />
      <FirstPersonDriveByRightPassengerUnarmedIKOffset x="-0.010000" y="-0.100000" z="-0.010000" />
      <FirstPersonMobilePhoneOffset x="0.150000" y="0.080000" z="0.525000" />
      <FirstPersonPassengerMobilePhoneOffset x="0.201000" y="0.060000" z="0.402000" />
      <PovCameraOffset x="0.000000" y="-0.335000" z="0.620000" />
      <PovCameraVerticalAdjustmentForRollCage value="0.000000" />
      <PovPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000" />
      <vfxInfoName>VFXVEHICLEINFO_CAR_GENERIC</vfxInfoName>
      <shouldUseCinematicViewMode value="true" />
      <shouldCameraTransitionOnClimbUpDown value="false" />
      <shouldCameraIgnoreExiting value="false" />
      <AllowPretendOccupants value="true" />
      <AllowJoyriding value="true" />
      <AllowSundayDriving value="true" />
      <AllowBodyColorMapping value="true" />
      <wheelScale value="0.209000" />
      <wheelScaleRear value="0.209000" />
      <dirtLevelMin value="0.000000" />
      <dirtLevelMax value="0.450000" />
      <envEffScaleMin value="0.000000" />
      <envEffScaleMax value="0.000000" />
      <envEffScaleMin2 value="0.000000" />
      <envEffScaleMax2 value="0.000000" />
      <damageMapScale value="0.40000" />
      <damageOffsetScale value="0.40000" />
      <diffuseTint value="0x00FFFFFF" />
      <steerWheelMult value="0.350000" />
      <HDTextureDist value="5.000000" />
      <lodDistances content="float_array">
        15.000000
        30.000000
        60.000000
        120.000000
        500.000000
        500.000000
      </lodDistances>
      <minSeatHeight value="0.844" />
      <identicalModelSpawnDistance value="20" />
      <maxNumOfSameColor value="1" />
      <defaultBodyHealth value="1000.000000" />
      <pretendOccupantsScale value="1.000000" />
      <visibleSpawnDistScale value="1.000000" />
      <trackerPathWidth value="2.000000" />
      <weaponForceMult value="1.000000" />
      <frequency value="30" />
      <swankness>SWANKNESS_2</swankness>
      <maxNum value="10" />
      <flags>FLAG_EXTRAS_STRONG FLAG_AVERAGE_CAR FLAG_EXTRAS_REQUIRE FLAG_CAN_HAVE_NEONS
        FLAG_INCREASE_CAMBER_WITH_SUSPENSION_MOD</flags>
      <type>VEHICLE_TYPE_CAR</type>
      <plateType>VPT_FRONT_AND_BACK_PLATES</plateType>
      <dashboardType>VDT_DUKES</dashboardType>
      <vehicleClass>VC_COUPE</vehicleClass>
      <wheelType>VWT_TUNER</wheelType>
      <trailers />
      <additionalTrailers />
      <drivers />
      <extraIncludes />
      <doorsWithCollisionWhenClosed />
      <driveableDoors />
      <bumpersNeedToCollideWithMap value="false" />
      <needsRopeTexture value="false" />
      <requiredExtras />
      <rewards />
      <cinematicPartCamera>
        <Item>WHEEL_FRONT_RIGHT_CAMERA</Item>
        <Item>WHEEL_FRONT_LEFT_CAMERA</Item>
        <Item>WHEEL_REAR_RIGHT_CAMERA</Item>
        <Item>WHEEL_REAR_LEFT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet />
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000" />
      <buoyancySphereSizeScale value="1.000000" />
      <pOverrideRagdollThreshold type="NULL" />
      <firstPersonDrivebyData>
        <Item>LOW_POSTLUDE_FRONT_LEFT</Item>
        <Item>LOW_POSTLUDE_FRONT_RIGHT</Item>
      </firstPersonDrivebyData>
    </Item>
    <Item>
      <modelName>torero2</modelName>
      <txdName>torero2</txdName>
      <handlingId>TORERO2</handlingId>
      <gameName>TORERO2</gameName>
      <vehicleMakeName>PEGASSI</vehicleMakeName>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected />
      <ptfxAssetName>null</ptfxAssetName>
      <audioNameHash />
      <layout>LAYOUT_LOW_TORERO2</layout>
      <coverBoundOffsets>IGNUS_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout />
      <cameraName>FOLLOW_CHEETAH_CAMERA</cameraName>
      <aimCameraName>DEFAULT_THIRD_PERSON_VEHICLE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>VEHICLE_BONNET_CAMERA_IGNUS</bonnetCameraName>
      <povCameraName>REDUCED_NEAR_CLIP_POV_CAMERA</povCameraName>
      <FirstPersonDriveByIKOffset x="0.040000" y="-0.170000" z="-0.050000" />
      <FirstPersonDriveByUnarmedIKOffset x="0.000000" y="-0.110000" z="-0.030000" />
      <FirstPersonProjectileDriveByIKOffset x="0.020000" y="-0.140000" z="-0.060000" />
      <FirstPersonProjectileDriveByPassengerIKOffset x="0.020000" y="-0.140000" z="-0.060000" />
      <FirstPersonDriveByRightPassengerIKOffset x="-0.000000" y="-0.180000" z="-0.050000" />
      <FirstPersonDriveByRightPassengerUnarmedIKOffset x="-0.020000" y="-0.160000" z="-0.030000" />
      <FirstPersonMobilePhoneOffset x="0.138000" y="0.151000" z="0.509000" />
      <FirstPersonPassengerMobilePhoneOffset x="0.214000" y="0.084000" z="0.396000" />
      <PovCameraOffset x="0.000000" y="-0.320000" z="0.620000" />
      <PovCameraVerticalAdjustmentForRollCage value="0.000000" />
      <PovPassengerCameraOffset x="0.010000" y="0.000000" z="0.000000" />
      <vfxInfoName>VFXVEHICLEINFO_CAR_GENERIC</vfxInfoName>
      <shouldUseCinematicViewMode value="true" />
      <shouldCameraTransitionOnClimbUpDown value="false" />
      <shouldCameraIgnoreExiting value="false" />
      <AllowPretendOccupants value="true" />
      <AllowJoyriding value="true" />
      <AllowSundayDriving value="true" />
      <AllowBodyColorMapping value="true" />
      <wheelScale value="0.310000" />
      <wheelScaleRear value="0.310000" />
      <dirtLevelMin value="0.000000" />
      <dirtLevelMax value="0.400000" />
      <envEffScaleMin value="0.000000" />
      <envEffScaleMax value="0.000000" />
      <envEffScaleMin2 value="0.000000" />
      <envEffScaleMax2 value="0.000000" />
      <damageMapScale value="0.40000" />
      <damageOffsetScale value="0.40000" />
      <diffuseTint value="0x00FFFFFF" />
      <steerWheelMult value="1.000000" />
      <HDTextureDist value="5.000000" />
      <lodDistances content="float_array">
        15.000000
        30.000000
        60.000000
        120.000000
        500.000000
        500.000000
      </lodDistances>
      <identicalModelSpawnDistance value="20" />
      <maxNumOfSameColor value="1" />
      <defaultBodyHealth value="1000.000000" />
      <pretendOccupantsScale value="1.000000" />
      <visibleSpawnDistScale value="1.000000" />
      <trackerPathWidth value="2.000000" />
      <weaponForceMult value="1.000000" />
      <frequency value="30" />
      <swankness>SWANKNESS_5</swankness>
      <maxNum value="4" />
      <flags>FLAG_SPORTS FLAG_RICH_CAR FLAG_RECESSED_HEADLIGHT_CORONAS FLAG_NO_BROKEN_DOWN_SCENARIO
        FLAG_COUNT_AS_FACEBOOK_DRIVEN FLAG_EXTRAS_STRONG FLAG_EXTRAS_REQUIRE
        FLAG_USE_RESTRICTED_DRIVEBY_HEIGHT_MID_ONLY</flags>
      <type>VEHICLE_TYPE_CAR</type>
      <plateType>VPT_BACK_PLATES</plateType>
      <dashboardType>VDT_RACE</dashboardType>
      <vehicleClass>VC_SUPER</vehicleClass>
      <wheelType>VWT_HIEND</wheelType>
      <trailers />
      <additionalTrailers />
      <drivers />
      <extraIncludes />
      <doorsWithCollisionWhenClosed />
      <driveableDoors />
      <bumpersNeedToCollideWithMap value="false" />
      <needsRopeTexture value="false" />
      <requiredExtras />
      <rewards />
      <cinematicPartCamera>
        <Item>WHEEL_FRONT_RIGHT_CAMERA</Item>
        <Item>WHEEL_FRONT_LEFT_CAMERA</Item>
        <Item>WHEEL_REAR_RIGHT_CAMERA</Item>
        <Item>WHEEL_REAR_LEFT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet />
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000" />
      <buoyancySphereSizeScale value="1.000000" />
      <pOverrideRagdollThreshold type="NULL" />
      <firstPersonDrivebyData>
        <Item>LOW_TORERO2_FRONT_LEFT</Item>
        <Item>LOW_TORERO2_FRONT_RIGHT</Item>
      </firstPersonDrivebyData>
    </Item>
    <Item>
      <modelName>lm87</modelName>
      <txdName>lm87</txdName>
      <handlingId>LM87</handlingId>
      <gameName>LM87</gameName>
      <vehicleMakeName>BENEFAC</vehicleMakeName>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected />
      <ptfxAssetName>null</ptfxAssetName>
      <audioNameHash />
      <layout>LAYOUT_LOW_LM87</layout>
      <coverBoundOffsets>S80_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout />
      <cameraName>FOLLOW_SCRAMJET_CAMERA</cameraName>
      <aimCameraName>DEFAULT_THIRD_PERSON_VEHICLE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>VEHICLE_BONNET_CAMERA_LOW_S80</bonnetCameraName>
      <povCameraName>REDUCED_NEAR_CLIP_POV_CAMERA_NO_REVERSE</povCameraName>
      <FirstPersonDriveByIKOffset x="0.000000" y="-0.150000" z="-0.050000" />
      <FirstPersonDriveByUnarmedIKOffset x="-0.020000" y="-0.150000" z="-0.030000" />
      <FirstPersonProjectileDriveByIKOffset x="0.196000" y="-0.040000" z="-0.100000" />
      <FirstPersonMobilePhoneOffset x="0.140000" y="0.183000" z="0.478000" />
      <PovCameraOffset x="0.000000" y="-0.290000" z="0.625000" />
      <PovCameraVerticalAdjustmentForRollCage value="0.000000" />
      <vfxInfoName>VFXVEHICLEINFO_CAR_GENERIC</vfxInfoName>
      <shouldUseCinematicViewMode value="true" />
      <shouldCameraTransitionOnClimbUpDown value="false" />
      <shouldCameraIgnoreExiting value="false" />
      <AllowPretendOccupants value="true" />
      <AllowJoyriding value="true" />
      <AllowSundayDriving value="true" />
      <AllowBodyColorMapping value="true" />
      <wheelScale value="0.265000" />
      <wheelScaleRear value="0.257000" />
      <dirtLevelMin value="0.000000" />
      <dirtLevelMax value="0.400000" />
      <envEffScaleMin value="0.000000" />
      <envEffScaleMax value="1.000000" />
      <envEffScaleMin2 value="0.000000" />
      <envEffScaleMax2 value="1.000000" />
      <damageMapScale value="0.40000" />
      <damageOffsetScale value="0.40000" />
      <diffuseTint value="0x00FFFFFF" />
      <steerWheelMult value="0.650000" />
      <HDTextureDist value="5.000000" />
      <lodDistances content="float_array">
        15.000000
        25.000000
        70.000000
        140.000000
        350.000000
        350.000000
      </lodDistances>
      <identicalModelSpawnDistance value="20" />
      <maxNumOfSameColor value="1" />
      <defaultBodyHealth value="1000.000000" />
      <pretendOccupantsScale value="1.000000" />
      <visibleSpawnDistScale value="1.000000" />
      <trackerPathWidth value="2.000000" />
      <weaponForceMult value="1.000000" />
      <frequency value="30" />
      <swankness>SWANKNESS_5</swankness>
      <maxNum value="4" />
      <flags>FLAG_EXTRAS_REQUIRE FLAG_EXTRAS_STRONG FLAG_FRONT_BOOT FLAG_SPORTS FLAG_RICH_CAR
        FLAG_RECESSED_HEADLIGHT_CORONAS FLAG_NO_BROKEN_DOWN_SCENARIO FLAG_COUNT_AS_FACEBOOK_DRIVEN
        FLAG_HAS_GULL_WING_DOORS FLAG_EXTRAS_STRONG FLAG_USE_RESTRICTED_DRIVEBY_HEIGHT_MID_ONLY</flags>
      <type>VEHICLE_TYPE_CAR</type>
      <plateType>VPT_BACK_PLATES</plateType>
      <dashboardType>VDT_RACE</dashboardType>
      <vehicleClass>VC_SUPER</vehicleClass>
      <wheelType>VWT_HIEND</wheelType>
      <trailers />
      <additionalTrailers />
      <drivers />
      <extraIncludes />
      <doorsWithCollisionWhenClosed />
      <driveableDoors />
      <bumpersNeedToCollideWithMap value="false" />
      <needsRopeTexture value="false" />
      <requiredExtras />
      <rewards />
      <cinematicPartCamera>
        <Item>WHEEL_FRONT_RIGHT_CAMERA</Item>
        <Item>WHEEL_FRONT_LEFT_CAMERA</Item>
        <Item>WHEEL_REAR_RIGHT_CAMERA</Item>
        <Item>WHEEL_REAR_LEFT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet />
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000" />
      <buoyancySphereSizeScale value="1.000000" />
      <pOverrideRagdollThreshold type="NULL" />
      <firstPersonDrivebyData>
        <Item>LOW_LM87_FRONT</Item>
      </firstPersonDrivebyData>
    </Item>
    <Item>
      <modelName>tenf</modelName>
      <txdName>tenf</txdName>
      <handlingId>TENF</handlingId>
      <gameName>TENF</gameName>
      <vehicleMakeName>OBEY</vehicleMakeName>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected />
      <ptfxAssetName>null</ptfxAssetName>
      <audioNameHash>czr1eng</audioNameHash>
      <layout>LAYOUT_LOW_RESTRICTED</layout>
      <coverBoundOffsets>TENF_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout />
      <cameraName>FOLLOW_CHEETAH_CAMERA</cameraName>
      <aimCameraName>DEFAULT_THIRD_PERSON_VEHICLE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>VEHICLE_BONNET_CAMERA_STANDARD_LONG_EXTRA_LOW</bonnetCameraName>
      <povCameraName>REDUCED_NEAR_CLIP_POV_CAMERA</povCameraName>
      <FirstPersonDriveByIKOffset x="0.020000" y="-0.100000" z="-0.010000" />
      <FirstPersonDriveByUnarmedIKOffset x="0.000000" y="-0.130000" z="-0.020000" />
      <FirstPersonProjectileDriveByIKOffset x="0.060000" y="-0.180000" z="-0.040000" />
      <FirstPersonProjectileDriveByPassengerIKOffset x="-0.060000" y="-0.180000" z="-0.040000" />
      <FirstPersonDriveByRightPassengerIKOffset x="-0.020000" y="-0.100000" z="-0.010000" />
      <FirstPersonDriveByRightPassengerUnarmedIKOffset x="-0.020000" y="-0.140000" z="-0.020000" />
      <FirstPersonMobilePhoneOffset x="0.133000" y="0.115000" z="0.495000" />
      <FirstPersonPassengerMobilePhoneOffset x="0.200000" y="0.060000" z="0.385000" />
      <FirstPersonMobilePhoneSeatIKOffset />
      <PovCameraOffset x="0.000000" y="-0.305000" z="0.613000" />
      <PovCameraVerticalAdjustmentForRollCage value="0.000000" />
      <PovPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000" />
      <vfxInfoName>VFXVEHICLEINFO_CAR_GENERIC</vfxInfoName>
      <shouldUseCinematicViewMode value="true" />
      <shouldCameraTransitionOnClimbUpDown value="false" />
      <shouldCameraIgnoreExiting value="false" />
      <AllowPretendOccupants value="true" />
      <AllowJoyriding value="true" />
      <AllowSundayDriving value="true" />
      <AllowBodyColorMapping value="true" />
      <wheelScale value="0.282000" />
      <wheelScaleRear value="0.282000" />
      <dirtLevelMin value="0.000000" />
      <dirtLevelMax value="0.450000" />
      <envEffScaleMin value="0.000000" />
      <envEffScaleMax value="1.000000" />
      <envEffScaleMin2 value="0.000000" />
      <envEffScaleMax2 value="1.000000" />
      <damageMapScale value="0.350000" />
      <damageOffsetScale value="0.350000" />
      <diffuseTint value="0x00FFFFFF" />
      <steerWheelMult value="1.000000" />
      <HDTextureDist value="5.000000" />
      <lodDistances content="float_array">
        15.000000
        30.000000
        60.000000
        120.000000
        500.000000
        500.000000
      </lodDistances>
      <minSeatHeight value="0.844" />
      <identicalModelSpawnDistance value="20" />
      <maxNumOfSameColor value="1" />
      <defaultBodyHealth value="1000.000000" />
      <pretendOccupantsScale value="1.000000" />
      <visibleSpawnDistScale value="1.000000" />
      <trackerPathWidth value="2.000000" />
      <weaponForceMult value="1.000000" />
      <frequency value="30" />
      <swankness>SWANKNESS_4</swankness>
      <maxNum value="10" />
      <flags>FLAG_AVERAGE_CAR FLAG_HAS_INTERIOR_EXTRAS FLAG_CAN_HAVE_NEONS FLAG_EXTRAS_ALL
        FLAG_BOOT_IN_FRONT FLAG_EXTRAS_STRONG</flags>
      <type>VEHICLE_TYPE_CAR</type>
      <plateType>VPT_BACK_PLATES</plateType>
      <dashboardType>VDT_RACE</dashboardType>
      <vehicleClass>VC_SPORT</vehicleClass>
      <wheelType>VWT_HIEND</wheelType>
      <trailers />
      <additionalTrailers />
      <drivers />
      <extraIncludes />
      <doorsWithCollisionWhenClosed />
      <driveableDoors />
      <bumpersNeedToCollideWithMap value="false" />
      <needsRopeTexture value="false" />
      <requiredExtras />
      <rewards />
      <cinematicPartCamera>
        <Item>WHEEL_FRONT_RIGHT_CAMERA</Item>
        <Item>WHEEL_FRONT_LEFT_CAMERA</Item>
        <Item>WHEEL_REAR_RIGHT_CAMERA</Item>
        <Item>WHEEL_REAR_LEFT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet />
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000" />
      <buoyancySphereSizeScale value="1.000000" />
      <pOverrideRagdollThreshold type="NULL" />
      <firstPersonDrivebyData>
        <Item>LOW_TENF_FRONT_LEFT</Item>
        <Item>LOW_TENF_FRONT_RIGHT</Item>
      </firstPersonDrivebyData>
    </Item>
    <Item>
      <modelName>tenf2</modelName>
      <txdName>tenf2</txdName>
      <handlingId>TENF2</handlingId>
      <gameName>TENF2</gameName>
      <vehicleMakeName>OBEY</vehicleMakeName>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected />
      <ptfxAssetName>null</ptfxAssetName>
      <audioNameHash />
      <layout>LAYOUT_LOW_RESTRICTED_TENF2</layout>
      <coverBoundOffsets>TENF2_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout />
      <cameraName>FOLLOW_CHEETAH_CAMERA</cameraName>
      <aimCameraName>DEFAULT_THIRD_PERSON_VEHICLE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>VEHICLE_BONNET_CAMERA_STANDARD_LONG_EXTRA_LOW</bonnetCameraName>
      <povCameraName>REDUCED_NEAR_CLIP_POV_CAMERA</povCameraName>
      <FirstPersonDriveByIKOffset x="0.020000" y="-0.100000" z="-0.010000" />
      <FirstPersonDriveByUnarmedIKOffset x="0.000000" y="-0.130000" z="-0.020000" />
      <FirstPersonProjectileDriveByIKOffset x="0.060000" y="-0.180000" z="-0.040000" />
      <FirstPersonProjectileDriveByPassengerIKOffset x="-0.060000" y="-0.180000" z="-0.040000" />
      <FirstPersonDriveByRightPassengerIKOffset x="-0.020000" y="-0.100000" z="-0.010000" />
      <FirstPersonDriveByRightPassengerUnarmedIKOffset x="-0.020000" y="-0.140000" z="-0.020000" />
      <FirstPersonMobilePhoneOffset x="0.133000" y="0.115000" z="0.495000" />
      <FirstPersonPassengerMobilePhoneOffset x="0.200000" y="0.060000" z="0.385000" />
      <FirstPersonMobilePhoneSeatIKOffset />
      <PovCameraOffset x="0.000000" y="-0.305000" z="0.613000" />
      <PovCameraVerticalAdjustmentForRollCage value="0.000000" />
      <PovPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000" />
      <vfxInfoName>VFXVEHICLEINFO_CAR_GENERIC</vfxInfoName>
      <shouldUseCinematicViewMode value="true" />
      <shouldCameraTransitionOnClimbUpDown value="false" />
      <shouldCameraIgnoreExiting value="false" />
      <AllowPretendOccupants value="true" />
      <AllowJoyriding value="true" />
      <AllowSundayDriving value="true" />
      <AllowBodyColorMapping value="true" />
      <wheelScale value="0.287000" />
      <wheelScaleRear value="0.287000" />
      <dirtLevelMin value="0.000000" />
      <dirtLevelMax value="0.450000" />
      <envEffScaleMin value="0.000000" />
      <envEffScaleMax value="1.000000" />
      <envEffScaleMin2 value="0.000000" />
      <envEffScaleMax2 value="1.000000" />
      <damageMapScale value="0.350000" />
      <damageOffsetScale value="0.350000" />
      <diffuseTint value="0x00FFFFFF" />
      <steerWheelMult value="1.000000" />
      <HDTextureDist value="5.000000" />
      <lodDistances content="float_array">
        15.000000
        30.000000
        60.000000
        120.000000
        500.000000
        500.000000
      </lodDistances>
      <minSeatHeight value="0.844" />
      <identicalModelSpawnDistance value="20" />
      <maxNumOfSameColor value="1" />
      <defaultBodyHealth value="1000.000000" />
      <pretendOccupantsScale value="1.000000" />
      <visibleSpawnDistScale value="1.000000" />
      <trackerPathWidth value="2.000000" />
      <weaponForceMult value="1.000000" />
      <frequency value="30" />
      <swankness>SWANKNESS_4</swankness>
      <maxNum value="10" />
      <flags>FLAG_AVERAGE_CAR FLAG_HAS_INTERIOR_EXTRAS FLAG_CAN_HAVE_NEONS FLAG_EXTRAS_ALL
        FLAG_BOOT_IN_FRONT FLAG_EXTRAS_STRONG FLAG_INCREASE_CAMBER_WITH_SUSPENSION_MOD</flags>
      <type>VEHICLE_TYPE_CAR</type>
      <plateType>VPT_BACK_PLATES</plateType>
      <dashboardType>VDT_RACE</dashboardType>
      <vehicleClass>VC_SPORT</vehicleClass>
      <wheelType>VWT_HIEND</wheelType>
      <trailers />
      <additionalTrailers />
      <drivers />
      <extraIncludes />
      <doorsWithCollisionWhenClosed />
      <driveableDoors />
      <bumpersNeedToCollideWithMap value="false" />
      <needsRopeTexture value="false" />
      <requiredExtras />
      <rewards />
      <cinematicPartCamera>
        <Item>WHEEL_FRONT_RIGHT_CAMERA</Item>
        <Item>WHEEL_FRONT_LEFT_CAMERA</Item>
        <Item>WHEEL_REAR_RIGHT_CAMERA</Item>
        <Item>WHEEL_REAR_LEFT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet />
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000" />
      <buoyancySphereSizeScale value="1.000000" />
      <pOverrideRagdollThreshold type="NULL" />
      <firstPersonDrivebyData>
        <Item>LOW_TENF_FRONT_LEFT</Item>
        <Item>LOW_TENF_FRONT_RIGHT</Item>
      </firstPersonDrivebyData>
    </Item>
    <Item>
      <modelName>conada</modelName>
      <txdName>conada</txdName>
      <handlingId>CONADA</handlingId>
      <gameName>Conada</gameName>
      <vehicleMakeName>BUCKING</vehicleMakeName>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected />
      <ptfxAssetName>null</ptfxAssetName>
      <audioNameHash />
      <layout>LAYOUT_HELI_CONADA</layout>
      <coverBoundOffsets>CONADA_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout />
      <cameraName>FOLLOW_HELI_CAMERA</cameraName>
      <aimCameraName>HELI_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>DEFAULT_VEHICLE_BONNET_CAMERA</bonnetCameraName>
      <povCameraName>HELI_SWIFT_POV_CAMERA</povCameraName>
      <FirstPersonDriveByIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonDriveByUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonProjectileDriveByIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonProjectileDriveByPassengerIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonProjectileDriveByRearLeftIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonProjectileDriveByRearRightIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonDriveByLeftPassengerIKOffset x="0.000000" y="0.000000" z="-0.123000" />
      <FirstPersonDriveByRightPassengerIKOffset x="-0.053000" y="-0.053000" z="-0.068000" />
      <FirstPersonDriveByRightRearPassengerIKOffset x="0.000000" y="0.000000" z="-0.123000" />
      <FirstPersonDriveByLeftPassengerUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonDriveByRightPassengerUnarmedIKOffset x="0.000000" y="-0.080000" z="-0.033000" />
      <FirstPersonPassengerMobilePhoneOffset x="0.173000" y="0.273000" z="0.518000" />
      <FirstPersonMobilePhoneOffset x="0.183000" y="0.383000" z="0.553000" />
      <PovCameraOffset x="0.000000" y="-0.030000" z="0.725000" />
      <PovCameraVerticalAdjustmentForRollCage value="0.000000" />
      <PovPassengerCameraOffset x="0.000000" y="-0.050000" z="0.000000" />
      <PovRearPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000" />
      <vfxInfoName>VFXVEHICLEINFO_HELI_GENERIC</vfxInfoName>
      <shouldUseCinematicViewMode value="true" />
      <shouldCameraTransitionOnClimbUpDown value="false" />
      <shouldCameraIgnoreExiting value="false" />
      <AllowPretendOccupants value="false" />
      <AllowJoyriding value="true" />
      <AllowSundayDriving value="true" />
      <AllowBodyColorMapping value="true" />
      <wheelScale value="0.137000" />
      <wheelScaleRear value="0.144000" />
      <dirtLevelMin value="0.000000" />
      <dirtLevelMax value="0.0050000" />
      <envEffScaleMin value="0.000000" />
      <envEffScaleMax value="1.000000" />
      <envEffScaleMin2 value="0.000000" />
      <envEffScaleMax2 value="1.000000" />
      <damageMapScale value="0.400000" />
      <damageOffsetScale value="0.400000" />
      <diffuseTint value="0xcf101010" />
      <steerWheelMult value="1.000000" />
      <HDTextureDist value="5.000000" />
      <lodDistances content="float_array">
        20.000000
        40.000000
        100.000000
        200.000000
        1000.000000
        1000.000000
      </lodDistances>
      <minSeatHeight value="1.24" />
      <identicalModelSpawnDistance value="20" />
      <maxNumOfSameColor value="10" />
      <defaultBodyHealth value="1000.000000" />
      <pretendOccupantsScale value="1.000000" />
      <visibleSpawnDistScale value="1.350000" />
      <trackerPathWidth value="2.000000" />
      <weaponForceMult value="1.000000" />
      <frequency value="100" />
      <swankness>SWANKNESS_3</swankness>
      <maxNum value="999" />
      <flags>FLAG_DONT_ROTATE_TAIL_ROTOR FLAG_NO_BOOT FLAG_DRIVER_NO_DRIVE_BY FLAG_AVERAGE_CAR
        FLAG_DONT_SPAWN_IN_CARGEN FLAG_DONT_SPAWN_AS_AMBIENT FLAG_DONT_TIMESLICE_WHEELS</flags>
      <type>VEHICLE_TYPE_HELI</type>
      <plateType>VPT_NONE</plateType>
      <dashboardType>VDT_MAVERICK</dashboardType>
      <vehicleClass>VC_HELICOPTER</vehicleClass>
      <wheelType>VWT_SPORT</wheelType>
      <trailers />
      <additionalTrailers />
      <drivers>
        <Item>
          <driverName>S_M_M_Gentransport</driverName>
          <npcName>Pilot</npcName>
        </Item>
      </drivers>
      <extraIncludes />
      <doorsWithCollisionWhenClosed />
      <driveableDoors />
      <bumpersNeedToCollideWithMap value="false" />
      <needsRopeTexture value="true" />
      <requiredExtras />
      <rewards />
      <cinematicPartCamera>
        <Item>HELI_REAR_ROTOR_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet />
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000" />
      <buoyancySphereSizeScale value="1.000000" />
      <pOverrideRagdollThreshold type="NULL" />
      <firstPersonDrivebyData>
        <Item>STD_FRONT_LEFT</Item>
        <Item>HELI_CONADA_FRONT_RIGHT</Item>
        <Item>HELI_FROGGER_REAR_LEFT</Item>
        <Item>HELI_FROGGER_REAR_RIGHT</Item>
      </firstPersonDrivebyData>
    </Item>
    <Item>
      <modelName>greenwood</modelName>
      <txdName>greenwood</txdName>
      <handlingId>GREENWOOD</handlingId>
      <gameName>Greenwood</gameName>
      <vehicleMakeName>BRAVADO</vehicleMakeName>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected />
      <ptfxAssetName>null</ptfxAssetName>
      <audioNameHash />
      <layout>LAYOUT_STD_GREENWOOD</layout>
      <coverBoundOffsets>GREENWOOD_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout />
      <cameraName>FOLLOW_CHEETAH_CAMERA</cameraName>
      <aimCameraName>BOX_VEHICLE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>VEHICLE_BONNET_CAMERA_STANDARD_LONG_EXTRA_LOW</bonnetCameraName>
      <povCameraName>REDUCED_NEAR_CLIP_POV_CAMERA</povCameraName>
      <FirstPersonDriveByIKOffset x="0.050000" y="-0.100000" z="-0.050000" />
      <FirstPersonDriveByUnarmedIKOffset x="0.000000" y="-0.020000" z="-0.010000" />
      <FirstPersonProjectileDriveByIKOffset x="0.120000" y="-0.050000" z="-0.100000" />
      <FirstPersonProjectileDriveByPassengerIKOffset x="-0.140000" y="-0.070000" z="-0.070000" />
      <FirstPersonDriveByRightPassengerIKOffset x="-0.050000" y="-0.090000" z="-0.050000" />
      <FirstPersonDriveByLeftPassengerIKOffset x="0.040000" y="-0.080000" z="-0.040000" />
      <FirstPersonDriveByRightRearPassengerIKOffset x="0.000000" y="-0.060000" z="-0.020000" />
      <FirstPersonDriveByRightPassengerUnarmedIKOffset x="0.000000" y="-0.040000" z="-0.020000" />
      <FirstPersonProjectileDriveByRearLeftIKOffset x="0.000000" y="-0.090000" z="-0.060000" />
      <FirstPersonProjectileDriveByRearRightIKOffset x="-0.060000" y="-0.090000" z="-0.060000" />
      <FirstPersonMobilePhoneOffset x="0.125000" y="0.160000" z="0.550000" />
      <FirstPersonPassengerMobilePhoneOffset x="0.223000" y="0.160000" z="0.415000" />
      <FirstPersonMobilePhoneSeatIKOffset />
      <PovCameraOffset x="0.000000" y="-0.190000" z="0.6450000" />
      <PovCameraVerticalAdjustmentForRollCage value="0.000000" />
      <PovPassengerCameraOffset x="0.000000" y="0.000000" z="0.00000" />
      <PovRearPassengerCameraOffset x="0.000000" y="-0.010000" z="0.000000" />
      <vfxInfoName>VFXVEHICLEINFO_CAR_GENERIC</vfxInfoName>
      <shouldUseCinematicViewMode value="true" />
      <shouldCameraTransitionOnClimbUpDown value="false" />
      <shouldCameraIgnoreExiting value="false" />
      <AllowPretendOccupants value="true" />
      <AllowJoyriding value="false" />
      <AllowSundayDriving value="false" />
      <AllowBodyColorMapping value="true" />
      <wheelScale value="0.208500" />
      <wheelScaleRear value="0.208500" />
      <dirtLevelMin value="0.000000" />
      <dirtLevelMax value="1.000000" />
      <envEffScaleMin value="0.300000" />
      <envEffScaleMax value="0.400000" />
      <envEffScaleMin2 value="0.000000" />
      <envEffScaleMax2 value="1.000000" />
      <damageMapScale value="0.400000" />
      <damageOffsetScale value="0.400000" />
      <diffuseTint value="0x00FFFFFF" />
      <steerWheelMult value="0.450000" />
      <HDTextureDist value="5.000000" />
      <lodDistances content="float_array">
        15.000000
        25.000000
        70.000000
        140.000000
        500.000000
        500.000000
      </lodDistances>
      <minSeatHeight value="0.5" />
      <identicalModelSpawnDistance value="20" />
      <maxNumOfSameColor value="10" />
      <defaultBodyHealth value="1000.000000" />
      <pretendOccupantsScale value="1.000000" />
      <visibleSpawnDistScale value="1.000000" />
      <trackerPathWidth value="2.000000" />
      <weaponForceMult value="1.000000" />
      <frequency value="5" />
      <swankness>SWANKNESS_1</swankness>
      <maxNum value="1" />
      <flags>FLAG_SPORTS FLAG_SPAWN_ON_TRAILER FLAG_RICH_CAR FLAG_NO_BROKEN_DOWN_SCENARIO
        FLAG_CAN_HAVE_NEONS FLAG_INCREASE_CAMBER_WITH_SUSPENSION_MOD</flags>
      <type>VEHICLE_TYPE_CAR</type>
      <plateType>VPT_BACK_PLATES</plateType>
      <dashboardType>VDT_DUKES</dashboardType>
      <vehicleClass>VC_MUSCLE</vehicleClass>
      <wheelType>VWT_HIEND</wheelType>
      <trailers />
      <additionalTrailers />
      <drivers />
      <extraIncludes />
      <doorsWithCollisionWhenClosed>
        <Item>VEH_EXT_BOOT</Item>
      </doorsWithCollisionWhenClosed>
      <driveableDoors />
      <bumpersNeedToCollideWithMap value="false" />
      <needsRopeTexture value="true" />
      <requiredExtras />
      <rewards />
      <cinematicPartCamera>
        <Item>WHEEL_WIDE_REAR_RIGHT_CAMERA</Item>
        <Item>WHEEL_WIDE_REAR_LEFT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet>Truck</NmBraceOverrideSet>
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000" />
      <buoyancySphereSizeScale value="1.000000" />
      <pOverrideRagdollThreshold type="NULL" />
      <firstPersonDrivebyData>
        <Item>STD_GREENWOOD_FRONT_LEFT</Item>
        <Item>STD_GREENWOOD_FRONT_RIGHT</Item>
        <Item>STD_GREENWOOD_REAR_LEFT</Item>
        <Item>STD_GREENWOOD_REAR_RIGHT</Item>
      </firstPersonDrivebyData>
      <numSeatsOverride value="2" />
    </Item>
    <Item>
      <modelName>sm722</modelName>
      <txdName>sm722</txdName>
      <handlingId>SM722</handlingId>
      <gameName>SM722</gameName>
      <vehicleMakeName>BENEFAC</vehicleMakeName>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected />
      <ptfxAssetName>null</ptfxAssetName>
      <audioNameHash />
      <layout>LAYOUT_STANDARD</layout>
      <coverBoundOffsets>SM722_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout />
      <cameraName>FOLLOW_CHEETAH_CAMERA</cameraName>
      <aimCameraName>BOX_VEHICLE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>VEHICLE_BONNET_CAMERA_STANDARD_LONG_EXTRA_LOW</bonnetCameraName>
      <povCameraName>DEFAULT_POV_CAMERA</povCameraName>
      <FirstPersonDriveByIKOffset x="0.050000" y="-0.100000" z="-0.050000" />
      <FirstPersonDriveByUnarmedIKOffset x="0.000000" y="-0.020000" z="-0.010000" />
      <FirstPersonProjectileDriveByIKOffset x="0.080000" y="-0.110000" z="-0.070000" />
      <FirstPersonProjectileDriveByPassengerIKOffset x="-0.050000" y="-0.140000" z="-0.050000" />
      <FirstPersonDriveByRightPassengerIKOffset x="-0.050000" y="-0.090000" z="-0.050000" />
      <FirstPersonDriveByRightPassengerUnarmedIKOffset x="0.000000" y="-0.040000" z="-0.020000" />
      <FirstPersonMobilePhoneOffset x="0.125000" y="0.160000" z="0.550000" />
      <FirstPersonPassengerMobilePhoneOffset x="0.223000" y="0.160000" z="0.415000" />
      <FirstPersonMobilePhoneSeatIKOffset />
      <PovCameraOffset x="0.000000" y="-0.215000" z="0.6650000" />
      <PovCameraVerticalAdjustmentForRollCage value="-0.025000" />
      <PovPassengerCameraOffset x="0.000000" y="0.060000" z="0.00000" />
      <PovRearPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000" />
      <vfxInfoName>VFXVEHICLEINFO_CAR_GENERIC</vfxInfoName>
      <shouldUseCinematicViewMode value="true" />
      <shouldCameraTransitionOnClimbUpDown value="false" />
      <shouldCameraIgnoreExiting value="false" />
      <AllowPretendOccupants value="true" />
      <AllowJoyriding value="false" />
      <AllowSundayDriving value="false" />
      <AllowBodyColorMapping value="true" />
      <wheelScale value="0.288000" />
      <wheelScaleRear value="0.288000" />
      <dirtLevelMin value="0.000000" />
      <dirtLevelMax value="1.000000" />
      <envEffScaleMin value="0.300000" />
      <envEffScaleMax value="0.400000" />
      <envEffScaleMin2 value="0.000000" />
      <envEffScaleMax2 value="1.000000" />
      <damageMapScale value="0.400000" />
      <damageOffsetScale value="0.400000" />
      <diffuseTint value="0x00FFFFFF" />
      <steerWheelMult value="0.450000" />
      <HDTextureDist value="5.000000" />
      <lodDistances content="float_array">
        15.000000
        25.000000
        70.000000
        140.000000
        500.000000
        500.000000
      </lodDistances>
      <minSeatHeight value="0.988" />
      <identicalModelSpawnDistance value="20" />
      <maxNumOfSameColor value="10" />
      <defaultBodyHealth value="1000.000000" />
      <pretendOccupantsScale value="1.000000" />
      <visibleSpawnDistScale value="1.000000" />
      <trackerPathWidth value="2.000000" />
      <weaponForceMult value="1.000000" />
      <frequency value="5" />
      <swankness>SWANKNESS_5</swankness>
      <maxNum value="1" />
      <flags>FLAG_SPORTS FLAG_SPAWN_ON_TRAILER FLAG_RICH_CAR FLAG_NO_BROKEN_DOWN_SCENARIO
        FLAG_CAN_HAVE_NEONS FLAG_INCREASE_CAMBER_WITH_SUSPENSION_MOD</flags>
      <type>VEHICLE_TYPE_CAR</type>
      <plateType>VPT_BACK_PLATES</plateType>
      <dashboardType>VDT_RACE</dashboardType>
      <vehicleClass>VC_SPORT</vehicleClass>
      <wheelType>VWT_HIEND</wheelType>
      <trailers />
      <additionalTrailers />
      <drivers />
      <extraIncludes />
      <doorsWithCollisionWhenClosed>
        <Item>VEH_EXT_BOOT</Item>
      </doorsWithCollisionWhenClosed>
      <driveableDoors />
      <bumpersNeedToCollideWithMap value="false" />
      <needsRopeTexture value="true" />
      <requiredExtras />
      <rewards />
      <cinematicPartCamera>
        <Item>WHEEL_WIDE_REAR_RIGHT_CAMERA</Item>
        <Item>WHEEL_WIDE_REAR_LEFT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet>Truck</NmBraceOverrideSet>
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000" />
      <buoyancySphereSizeScale value="1.000000" />
      <pOverrideRagdollThreshold type="NULL" />
      <firstPersonDrivebyData>
        <Item>STD_SM722_FRONT_LEFT</Item>
        <Item>STD_SM722_FRONT_RIGHT</Item>
      </firstPersonDrivebyData>
      <numSeatsOverride value="2" />
    </Item>
    <Item>
      <modelName>weevil2</modelName>
      <txdName>weevil2</txdName>
      <handlingId>WEEVIL2</handlingId>
      <gameName>WEEVIL2</gameName>
      <vehicleMakeName>BF</vehicleMakeName>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected />
      <ptfxAssetName>veh_sm_car_small</ptfxAssetName>
      <audioNameHash />
      <layout>LAYOUT_LOW_WEEVIL2</layout>
      <coverBoundOffsets>WEEVIL2_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout />
      <cameraName>FOLLOW_CHEETAH_CAMERA</cameraName>
      <aimCameraName>DEFAULT_THIRD_PERSON_VEHICLE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>VEHICLE_BONNET_CAMERA_STANDARD_LONG_EXTRA_LOW</bonnetCameraName>
      <povCameraName>REDUCED_NEAR_CLIP_POV_CAMERA_NO_REVERSE</povCameraName>
      <FirstPersonDriveByIKOffset x="0.040000" y="-0.075000" z="-0.030000" />
      <FirstPersonDriveByUnarmedIKOffset x="-0.030000" y="-0.140000" z="-0.010000" />
      <FirstPersonProjectileDriveByIKOffset x="0.110000" y="-0.100000" z="-0.065000" />
      <FirstPersonProjectileDriveByPassengerIKOffset x="-0.110000" y="-0.100000" z="-0.065000" />
      <FirstPersonDriveByRightPassengerIKOffset x="0.000000" y="-0.080000" z="-0.020000" />
      <FirstPersonDriveByRightPassengerUnarmedIKOffset x="0.000000" y="-0.120000" z="-0.025000" />
      <FirstPersonMobilePhoneOffset x="0.125000" y="0.180000" z="0.525000" />
      <FirstPersonPassengerMobilePhoneOffset x="0.201000" y="0.120000" z="0.423000" />
      <PovCameraOffset x="0.000000" y="-0.235000" z="0.605000" />
      <PovCameraVerticalAdjustmentForRollCage value="0.000000" />
      <PovPassengerCameraOffset x="0.000000" y="-0.020000" z="0.000000" />
      <vfxInfoName>VFXVEHICLEINFO_CAR_GENERIC_SMALL</vfxInfoName>
      <shouldUseCinematicViewMode value="true" />
      <shouldCameraTransitionOnClimbUpDown value="false" />
      <shouldCameraIgnoreExiting value="false" />
      <AllowPretendOccupants value="true" />
      <AllowJoyriding value="true" />
      <AllowSundayDriving value="true" />
      <AllowBodyColorMapping value="true" />
      <wheelScale value="0.227700" />
      <wheelScaleRear value="0.227700" />
      <dirtLevelMin value="0.000000" />
      <dirtLevelMax value="0.300000" />
      <envEffScaleMin value="0.000000" />
      <envEffScaleMax value="0.000000" />
      <envEffScaleMin2 value="0.000000" />
      <envEffScaleMax2 value="0.000000" />
      <damageMapScale value="0.10000" />
      <damageOffsetScale value="0.10000" />
      <diffuseTint value="0x00FFFFFF" />
      <steerWheelMult value="0.500000" />
      <HDTextureDist value="5.000000" />
      <lodDistances content="float_array">
        10.000000
        25.000000
        60.000000
        120.000000
        500.000000
        500.000000
      </lodDistances>
      <minSeatHeight value="0.881" />
      <identicalModelSpawnDistance value="20" />
      <maxNumOfSameColor value="1" />
      <defaultBodyHealth value="1000.000000" />
      <pretendOccupantsScale value="1.000000" />
      <visibleSpawnDistScale value="1.000000" />
      <trackerPathWidth value="2.000000" />
      <weaponForceMult value="1.000000" />
      <frequency value="30" />
      <swankness>SWANKNESS_5</swankness>
      <maxNum value="5" />
      <flags>FLAG_EXTRAS_ALL FLAG FLAG_AVERAGE_CAR FLAG_HAS_INTERIOR_EXTRAS FLAG_CAN_HAVE_NEONS
        FLAG_HAS_SUPERCHARGER FLAG_INCREASE_CAMBER_WITH_SUSPENSION_MOD</flags>
      <type>VEHICLE_TYPE_CAR</type>
      <plateType>VPT_BACK_PLATES</plateType>
      <dashboardType>VDT_DUKES</dashboardType>
      <vehicleClass>VC_MUSCLE</vehicleClass>
      <wheelType>VWT_MUSCLE</wheelType>
      <trailers />
      <additionalTrailers />
      <drivers />
      <extraIncludes />
      <doorsWithCollisionWhenClosed />
      <driveableDoors />
      <bumpersNeedToCollideWithMap value="false" />
      <needsRopeTexture value="false" />
      <requiredExtras />
      <rewards />
      <cinematicPartCamera>
        <Item>WHEEL_FRONT_RIGHT_CAMERA</Item>
        <Item>WHEEL_FRONT_LEFT_CAMERA</Item>
        <Item>WHEEL_REAR_RIGHT_CAMERA</Item>
        <Item>WHEEL_REAR_LEFT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet />
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000" />
      <buoyancySphereSizeScale value="1.000000" />
      <pOverrideRagdollThreshold type="NULL" />
      <firstPersonDrivebyData>
        <Item>STD_WEEVIL2_FRONT_LEFT</Item>
        <Item>STD_WEEVIL2_FRONT_RIGHT</Item>
      </firstPersonDrivebyData>
      <numSeatsOverride value="2" />
    </Item>
    <Item>
      <modelName>deimos</modelName>
      <txdName>deimos</txdName>
      <handlingId>DEIMOS</handlingId>
      <gameName>deimos</gameName>
      <vehicleMakeName>GROTTI</vehicleMakeName>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected />
      <ptfxAssetName>null</ptfxAssetName>
      <audioNameHash>cheetah2</audioNameHash>
      <layout>LAYOUT_LOW_RESTRICTED</layout>
      <coverBoundOffsets>CHEETAH2_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout />
      <cameraName>FOLLOW_CHEETAH_CAMERA</cameraName>
      <aimCameraName>DEFAULT_THIRD_PERSON_NO_LOCKON_VEHICLE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>VEHICLE_BONNET_CAMERA_LOW_LOW</bonnetCameraName>
      <povCameraName>REDUCED_NEAR_CLIP_POV_CAMERA</povCameraName>
      <FirstPersonDriveByIKOffset x="0.000000" y="-0.060000" z="-0.030000" />
      <FirstPersonDriveByUnarmedIKOffset x="0.000000" y="-0.100000" z="0.000000" />
      <FirstPersonProjectileDriveByIKOffset x="0.000000" y="-0.030000" z="-0.075000" />
      <FirstPersonProjectileDriveByPassengerIKOffset x="0.000000" y="-0.030000" z="-0.020000" />
      <FirstPersonProjectileDriveByRearLeftIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonProjectileDriveByRearRightIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonDriveByLeftPassengerIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonDriveByRightPassengerIKOffset x="0.000000" y="-0.060000" z="-0.030000" />
      <FirstPersonDriveByRightRearPassengerIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonDriveByLeftPassengerUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonDriveByRightPassengerUnarmedIKOffset x="0.000000" y="-0.100000" z="0.000000" />
      <FirstPersonMobilePhoneOffset x="0.146000" y="0.108000" z="0.511000" />
      <FirstPersonPassengerMobilePhoneOffset x="0.181000" y="0.066000" z="0.421000" />
      <PovCameraOffset x="0.000000" y="-0.320000" z="0.630000" />
      <PovCameraVerticalAdjustmentForRollCage value="0.000000" />
      <PovPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000" />
      <PovRearPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000" />
      <vfxInfoName>VFXVEHICLEINFO_CAR_GENERIC</vfxInfoName>
      <shouldUseCinematicViewMode value="true" />
      <shouldCameraTransitionOnClimbUpDown value="false" />
      <shouldCameraIgnoreExiting value="false" />
      <AllowPretendOccupants value="true" />
      <AllowJoyriding value="true" />
      <AllowSundayDriving value="true" />
      <AllowBodyColorMapping value="true" />
      <wheelScale value="0.248900" />
      <wheelScaleRear value="0.248900" />
      <dirtLevelMin value="0.000000" />
      <dirtLevelMax value="0.700000" />
      <envEffScaleMin value="0.000000" />
      <envEffScaleMax value="1.000000" />
      <envEffScaleMin2 value="0.000000" />
      <envEffScaleMax2 value="1.000000" />
      <damageMapScale value="0.300000" />
      <damageOffsetScale value="0.300000" />
      <diffuseTint value="0x00FFFFFF" />
      <steerWheelMult value="1.000000" />
      <HDTextureDist value="5.000000" />
      <lodDistances content="float_array">
        15.000000
        30.000000
        50.000000
        100.000000
        500.000000
        500.000000
      </lodDistances>
      <minSeatHeight value="0.852" />
      <identicalModelSpawnDistance value="20" />
      <maxNumOfSameColor value="10" />
      <defaultBodyHealth value="1000.000000" />
      <pretendOccupantsScale value="1.000000" />
      <visibleSpawnDistScale value="1.000000" />
      <trackerPathWidth value="2.000000" />
      <weaponForceMult value="1.000000" />
      <frequency value="10" />
      <swankness>SWANKNESS_2</swankness>
      <maxNum value="3" />
      <flags>FLAG_CAN_HAVE_NEONS FLAG_EXTRAS_STRONG FLAG_RECESSED_HEADLIGHT_CORONAS FLAG_FRONT_BOOT</flags>
      <type>VEHICLE_TYPE_CAR</type>
      <plateType>VPT_FRONT_AND_BACK_PLATES</plateType>
      <dashboardType>VDT_DUKES</dashboardType>
      <vehicleClass>VC_SPORT_CLASSIC</vehicleClass>
      <wheelType>VWT_MUSCLE</wheelType>
      <trailers />
      <additionalTrailers />
      <drivers />
      <extraIncludes />
      <doorsWithCollisionWhenClosed />
      <driveableDoors />
      <bumpersNeedToCollideWithMap value="false" />
      <needsRopeTexture value="false" />
      <requiredExtras />
      <rewards />
      <cinematicPartCamera>
        <Item>WHEEL_FRONT_RIGHT_CAMERA</Item>
        <Item>WHEEL_FRONT_LEFT_CAMERA</Item>
        <Item>WHEEL_REAR_RIGHT_CAMERA</Item>
        <Item>WHEEL_REAR_LEFT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet />
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000" />
      <buoyancySphereSizeScale value="1.000000" />
      <pOverrideRagdollThreshold type="NULL" />
      <firstPersonDrivebyData>
        <Item>LOW_F620_FRONT_LEFT</Item>
        <Item>LOW_F620_FRONT_RIGHT</Item>
      </firstPersonDrivebyData>
    </Item>
    <Item>
      <modelName>torerod</modelName>
      <txdName>torerod</txdName>
      <handlingId>TOREROD</handlingId>
      <gameName>TOREROD</gameName>
      <vehicleMakeName>PEGASSI</vehicleMakeName>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected />
      <ptfxAssetName>null</ptfxAssetName>
      <audioNameHash>torero</audioNameHash>
      <layout>LAYOUT_LOW_TORERO</layout>
      <coverBoundOffsets>TORERO_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout />
      <cameraName>FOLLOW_CHEETAH_CAMERA</cameraName>
      <aimCameraName>DEFAULT_THIRD_PERSON_VEHICLE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>VEHICLE_BONNET_CAMERA_LOW_LOW</bonnetCameraName>
      <povCameraName>REDUCED_NEAR_CLIP_POV_CAMERA</povCameraName>
      <FirstPersonDriveByIKOffset x="0.000000" y="-0.080000" z="-0.030000" />
      <FirstPersonDriveByUnarmedIKOffset x="0.000000" y="-0.120000" z="-0.020000" />
      <FirstPersonProjectileDriveByIKOffset x="0.000000" y="-0.150000" z="-0.015000" />
      <FirstPersonProjectileDriveByPassengerIKOffset x="-0.050000" y="-0.150000" z="-0.015000" />
      <FirstPersonProjectileDriveByRearLeftIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonProjectileDriveByRearRightIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonDriveByLeftPassengerIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonDriveByRightPassengerIKOffset x="0.000000" y="-0.080000" z="-0.030000" />
      <FirstPersonDriveByRightRearPassengerIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonDriveByLeftPassengerUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonDriveByRightPassengerUnarmedIKOffset x="0.000000" y="-0.120000" z="-0.020000" />
      <FirstPersonMobilePhoneOffset x="0.158000" y="0.133000" z="0.540000" />
      <FirstPersonPassengerMobilePhoneOffset x="0.184000" y="0.044000" z="0.459000" />
      <PovCameraOffset x="0.000000" y="-0.340000" z="0.670000" />
      <PovCameraVerticalAdjustmentForRollCage value="0.000000" />
      <PovPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000" />
      <PovRearPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000" />
      <vfxInfoName>VFXVEHICLEINFO_CAR_GENERIC</vfxInfoName>
      <shouldUseCinematicViewMode value="true" />
      <shouldCameraTransitionOnClimbUpDown value="false" />
      <shouldCameraIgnoreExiting value="false" />
      <AllowPretendOccupants value="true" />
      <AllowJoyriding value="true" />
      <AllowSundayDriving value="true" />
      <AllowBodyColorMapping value="true" />
      <wheelScale value="0.212000" />
      <wheelScaleRear value="0.212000" />
      <dirtLevelMin value="0.000000" />
      <dirtLevelMax value="0.700000" />
      <envEffScaleMin value="0.000000" />
      <envEffScaleMax value="1.000000" />
      <envEffScaleMin2 value="0.000000" />
      <envEffScaleMax2 value="1.000000" />
      <damageMapScale value="0.300000" />
      <damageOffsetScale value="0.300000" />
      <diffuseTint value="0x00FFFFFF" />
      <steerWheelMult value="1.000000" />
      <HDTextureDist value="5.000000" />
      <lodDistances content="float_array">
        15.000000
        30.000000
        50.000000
        100.000000
        500.000000
        500.000000
      </lodDistances>
      <minSeatHeight value="0.796" />
      <identicalModelSpawnDistance value="20" />
      <maxNumOfSameColor value="1" />
      <defaultBodyHealth value="1000.000000" />
      <pretendOccupantsScale value="1.000000" />
      <visibleSpawnDistScale value="1.000000" />
      <trackerPathWidth value="2.000000" />
      <weaponForceMult value="1.000000" />
      <frequency value="20" />
      <swankness>SWANKNESS_5</swankness>
      <maxNum value="3" />
      <flags>FLAG_SPORTS FLAG_RICH_CAR FLAG_NO_BROKEN_DOWN_SCENARIO FLAG_CAN_HAVE_NEONS
        FLAG_RECESSED_HEADLIGHT_CORONAS FLAG_EXTRAS_REQUIRE FLAG_EXTRAS_STRONG
        FLAG_HAS_GULL_WING_DOORS FLAG_FRONT_BOOT</flags>
      <type>VEHICLE_TYPE_CAR</type>
      <plateType>VPT_BACK_PLATES</plateType>
      <dashboardType>VDT_BANSHEE</dashboardType>
      <vehicleClass>VC_SPORT_CLASSIC</vehicleClass>
      <wheelType>VWT_HIEND</wheelType>
      <trailers />
      <additionalTrailers />
      <drivers />
      <extraIncludes />
      <doorsWithCollisionWhenClosed />
      <driveableDoors />
      <bumpersNeedToCollideWithMap value="false" />
      <needsRopeTexture value="false" />
      <requiredExtras />
      <rewards />
      <cinematicPartCamera>
        <Item>WHEEL_FRONT_RIGHT_CAMERA</Item>
        <Item>WHEEL_FRONT_LEFT_CAMERA</Item>
        <Item>WHEEL_REAR_RIGHT_CAMERA</Item>
        <Item>WHEEL_REAR_LEFT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet />
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000" />
      <buoyancySphereSizeScale value="1.000000" />
      <pOverrideRagdollThreshold type="NULL" />
      <firstPersonDrivebyData>
        <Item>LOW_TORERO_FRONT_LEFT</Item>
        <Item>LOW_TORERO_FRONT_RIGHT</Item>
      </firstPersonDrivebyData>
    </Item>
    <Item>
      <modelName>turismoo</modelName>
      <txdName>turismoo</txdName>
      <handlingId>TURISMOO</handlingId>
      <gameName>TURISMOO</gameName>
      <vehicleMakeName>GROTTI</vehicleMakeName>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected />
      <ptfxAssetName>null</ptfxAssetName>
      <audioNameHash>BESTIAGTS</audioNameHash>
      <layout>LAYOUT_LOW</layout>
      <coverBoundOffsets>RAPIDGT_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout />
      <cameraName>DEFAULT_FOLLOW_VEHICLE_CAMERA</cameraName>
      <aimCameraName>DEFAULT_THIRD_PERSON_VEHICLE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>VEHICLE_BONNET_CAMERA_RAPIDGT</bonnetCameraName>
      <povCameraName>REDUCED_NEAR_CLIP_POV_CAMERA</povCameraName>
      <FirstPersonDriveByIKOffset x="0.000000" y="-0.070000" z="-0.040000" />
      <FirstPersonDriveByUnarmedIKOffset x="0.000000" y="-0.120000" z="-0.020000" />
      <FirstPersonProjectileDriveByIKOffset x="0.000000" y="-0.065000" z="-0.085000" />
      <FirstPersonProjectileDriveByPassengerIKOffset x="0.000000" y="-0.065000" z="-0.085000" />
      <FirstPersonProjectileDriveByRearLeftIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonProjectileDriveByRearRightIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonDriveByLeftPassengerIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonDriveByRightPassengerIKOffset x="0.000000" y="-0.120000" z="-0.070000" />
      <FirstPersonDriveByRightRearPassengerIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonDriveByLeftPassengerUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonDriveByRightPassengerUnarmedIKOffset x="0.000000" y="-0.120000" z="-0.020000" />
      <FirstPersonMobilePhoneOffset x="0.146000" y="0.196000" z="0.539000" />
      <FirstPersonPassengerMobilePhoneOffset x="0.136000" y="0.223000" z="0.425000" />
      <PovCameraOffset x="0.000000" y="-0.230000" z="0.660000" />
      <PovCameraVerticalAdjustmentForRollCage value="-0.030000" />
      <PovPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000" />
      <PovRearPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000" />
      <vfxInfoName>VFXVEHICLEINFO_CAR_GENERIC</vfxInfoName>
      <shouldUseCinematicViewMode value="true" />
      <shouldCameraTransitionOnClimbUpDown value="false" />
      <shouldCameraIgnoreExiting value="false" />
      <AllowPretendOccupants value="true" />
      <AllowJoyriding value="true" />
      <AllowSundayDriving value="true" />
      <AllowBodyColorMapping value="true" />
      <wheelScale value="0.274000" />
      <wheelScaleRear value="0.274000" />
      <dirtLevelMin value="0.000000" />
      <dirtLevelMax value="0.300000" />
      <envEffScaleMin value="0.000000" />
      <envEffScaleMax value="1.000000" />
      <envEffScaleMin2 value="0.000000" />
      <envEffScaleMax2 value="1.000000" />
      <damageMapScale value="0.600000" />
      <damageOffsetScale value="1.000000" />
      <diffuseTint value="0x00FFFFFF" />
      <steerWheelMult value="1.000000" />
      <HDTextureDist value="15.000000" />
      <lodDistances content="float_array">
        20.000000
        40.000000
        80.000000
        120.000000
        500.000000
        500.000000
      </lodDistances>
      <minSeatHeight value="0.784" />
      <identicalModelSpawnDistance value="80" />
      <maxNumOfSameColor value="1" />
      <defaultBodyHealth value="1000.000000" />
      <pretendOccupantsScale value="1.000000" />
      <visibleSpawnDistScale value="1.000000" />
      <trackerPathWidth value="2.000000" />
      <weaponForceMult value="1.000000" />
      <frequency value="5" />
      <swankness>SWANKNESS_5</swankness>
      <maxNum value="1" />
      <flags>FLAG_SPORTS FLAG_RICH_CAR FLAG_NO_BROKEN_DOWN_SCENARIO FLAG_HAS_INTERIOR_EXTRAS</flags>
      <type>VEHICLE_TYPE_CAR</type>
      <plateType>VPT_BACK_PLATES</plateType>
      <dashboardType>VDT_FELTZER</dashboardType>
      <vehicleClass>VC_SPORT</vehicleClass>
      <wheelType>VWT_HIEND</wheelType>
      <trailers />
      <additionalTrailers />
      <drivers />
      <extraIncludes />
      <doorsWithCollisionWhenClosed />
      <driveableDoors />
      <bumpersNeedToCollideWithMap value="false" />
      <needsRopeTexture value="false" />
      <requiredExtras />
      <rewards />
      <cinematicPartCamera>
        <Item>WHEEL_FRONT_RIGHT_CAMERA</Item>
        <Item>WHEEL_FRONT_LEFT_CAMERA</Item>
        <Item>WHEEL_REAR_RIGHT_CAMERA</Item>
        <Item>WHEEL_REAR_LEFT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet />
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000" />
      <buoyancySphereSizeScale value="1.000000" />
      <pOverrideRagdollThreshold type="NULL" />
      <firstPersonDrivebyData>
        <Item>LOW_RAPIDGT_FRONT_LEFT</Item>
        <Item>LOW_RAPIDGT_FRONT_RIGHT</Item>
      </firstPersonDrivebyData>
    </Item>
    <Item>
      <modelName>turismoc</modelName>
      <txdName>turismoc</txdName>
      <handlingId>TURISMO2</handlingId>
      <gameName>TURISMO2</gameName>
      <vehicleMakeName>GROTTI</vehicleMakeName>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected />
      <ptfxAssetName>null</ptfxAssetName>
      <audioNameHash>prototipo</audioNameHash>
      <layout>LAYOUT_LOW_RESTRICTED</layout>
      <coverBoundOffsets>TURISMO2_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout />
      <cameraName>FOLLOW_CHEETAH_CAMERA</cameraName>
      <aimCameraName>DEFAULT_THIRD_PERSON_VEHICLE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>VEHICLE_BONNET_CAMERA_LOW_LOW</bonnetCameraName>
      <povCameraName>REDUCED_NEAR_CLIP_POV_CAMERA_NO_REVERSE</povCameraName>
      <FirstPersonDriveByIKOffset x="0.000000" y="-0.080000" z="-0.030000" />
      <FirstPersonDriveByUnarmedIKOffset x="0.000000" y="-0.120000" z="-0.020000" />
      <FirstPersonProjectileDriveByIKOffset x="0.000000" y="-0.130000" z="-0.015000" />
      <FirstPersonProjectileDriveByPassengerIKOffset x="-0.050000" y="-0.130000" z="-0.015000" />
      <FirstPersonProjectileDriveByRearLeftIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonProjectileDriveByRearRightIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonDriveByLeftPassengerIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonDriveByRightPassengerIKOffset x="0.000000" y="-0.080000" z="-0.030000" />
      <FirstPersonDriveByRightRearPassengerIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonDriveByLeftPassengerUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonDriveByRightPassengerUnarmedIKOffset x="0.000000" y="-0.120000" z="-0.020000" />
      <FirstPersonMobilePhoneOffset x="0.140000" y="0.160000" z="0.495000000" />
      <FirstPersonPassengerMobilePhoneOffset x="0.174000" y="0.109000" z="0.411000" />
      <PovCameraOffset x="0.000000" y="-0.270000" z="0.625000" />
      <PovCameraVerticalAdjustmentForRollCage value="0.000000" />
      <PovPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000" />
      <PovRearPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000" />
      <vfxInfoName>VFXVEHICLEINFO_CAR_GENERIC</vfxInfoName>
      <shouldUseCinematicViewMode value="true" />
      <shouldCameraTransitionOnClimbUpDown value="false" />
      <shouldCameraIgnoreExiting value="false" />
      <AllowPretendOccupants value="true" />
      <AllowJoyriding value="true" />
      <AllowSundayDriving value="true" />
      <AllowBodyColorMapping value="true" />
      <wheelScale value="0.247" />
      <wheelScaleRear value="0.247" />
      <dirtLevelMin value="0.000000" />
      <dirtLevelMax value="0.700000" />
      <envEffScaleMin value="0.000000" />
      <envEffScaleMax value="1.000000" />
      <envEffScaleMin2 value="0.000000" />
      <envEffScaleMax2 value="1.000000" />
      <damageMapScale value="0.300000" />
      <damageOffsetScale value="0.300000" />
      <diffuseTint value="0x00FFFFFF" />
      <steerWheelMult value="1.000000" />
      <HDTextureDist value="5.000000" />
      <lodDistances content="float_array">
        15.000000
        30.000000
        50.000000
        100.000000
        500.000000
        500.000000
      </lodDistances>
      <minSeatHeight value="0.796" />
      <identicalModelSpawnDistance value="20" />
      <maxNumOfSameColor value="1" />
      <defaultBodyHealth value="1000.000000" />
      <pretendOccupantsScale value="1.000000" />
      <visibleSpawnDistScale value="1.000000" />
      <trackerPathWidth value="2.000000" />
      <weaponForceMult value="1.000000" />
      <frequency value="20" />
      <swankness>SWANKNESS_5</swankness>
      <maxNum value="3" />
      <flags>FLAG_SPORTS FLAG_SPAWN_ON_TRAILER FLAG_RICH_CAR FLAG_NO_BROKEN_DOWN_SCENARIO
        FLAG_CAN_HAVE_NEONS FLAG_RECESSED_HEADLIGHT_CORONAS FLAG_EXTRAS_REQUIRE FLAG_EXTRAS_STRONG
        FLAG_FRONT_BOOT</flags>
      <type>VEHICLE_TYPE_CAR</type>
      <plateType>VPT_BACK_PLATES</plateType>
      <dashboardType>VDT_DUKES</dashboardType>
      <vehicleClass>VC_SPORT_CLASSIC</vehicleClass>
      <wheelType>VWT_HIEND</wheelType>
      <trailers />
      <additionalTrailers />
      <drivers />
      <extraIncludes />
      <doorsWithCollisionWhenClosed />
      <driveableDoors />
      <bumpersNeedToCollideWithMap value="false" />
      <needsRopeTexture value="false" />
      <requiredExtras />
      <rewards />
      <cinematicPartCamera>
        <Item>WHEEL_FRONT_RIGHT_CAMERA</Item>
        <Item>WHEEL_FRONT_LEFT_CAMERA</Item>
        <Item>WHEEL_REAR_RIGHT_CAMERA</Item>
        <Item>WHEEL_REAR_LEFT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet />
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000" />
      <buoyancySphereSizeScale value="1.000000" />
      <pOverrideRagdollThreshold type="NULL" />
      <firstPersonDrivebyData>
        <Item>LOW_TURISMO2_FRONT_LEFT</Item>
        <Item>LOW_TURISMO2_FRONT_RIGHT</Item>
      </firstPersonDrivebyData>
    </Item>
    <Item>
      <modelName>turismo2lm</modelName>
      <txdName>turismo2lm</txdName>
      <handlingId>TURISMO2LM</handlingId>
      <gameName>TURISMO2LM</gameName>
      <vehicleMakeName>GROTTI</vehicleMakeName>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected />
      <ptfxAssetName>null</ptfxAssetName>
      <audioNameHash>feltzer3</audioNameHash>
      <layout>LAYOUT_LOW_RESTRICTED</layout>
      <coverBoundOffsets>TURISMO2_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout />
      <cameraName>FOLLOW_CHEETAH_CAMERA</cameraName>
      <aimCameraName>DEFAULT_THIRD_PERSON_VEHICLE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>VEHICLE_BONNET_CAMERA_LOW_LOW</bonnetCameraName>
      <povCameraName>REDUCED_NEAR_CLIP_POV_CAMERA_NO_REVERSE</povCameraName>
      <FirstPersonDriveByIKOffset x="0.000000" y="-0.080000" z="-0.030000" />
      <FirstPersonDriveByUnarmedIKOffset x="0.000000" y="-0.120000" z="-0.020000" />
      <FirstPersonProjectileDriveByIKOffset x="0.000000" y="-0.130000" z="-0.015000" />
      <FirstPersonProjectileDriveByPassengerIKOffset x="-0.050000" y="-0.130000" z="-0.015000" />
      <FirstPersonProjectileDriveByRearLeftIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonProjectileDriveByRearRightIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonDriveByLeftPassengerIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonDriveByRightPassengerIKOffset x="0.000000" y="-0.080000" z="-0.030000" />
      <FirstPersonDriveByRightRearPassengerIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonDriveByLeftPassengerUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonDriveByRightPassengerUnarmedIKOffset x="0.000000" y="-0.120000" z="-0.020000" />
      <FirstPersonMobilePhoneOffset x="0.140000" y="0.160000" z="0.495000000" />
      <FirstPersonPassengerMobilePhoneOffset x="0.174000" y="0.109000" z="0.411000" />
      <PovCameraOffset x="0.000000" y="-0.270000" z="0.625000" />
      <PovCameraVerticalAdjustmentForRollCage value="0.000000" />
      <PovPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000" />
      <PovRearPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000" />
      <vfxInfoName>VFXVEHICLEINFO_CAR_GENERIC</vfxInfoName>
      <shouldUseCinematicViewMode value="true" />
      <shouldCameraTransitionOnClimbUpDown value="false" />
      <shouldCameraIgnoreExiting value="false" />
      <AllowPretendOccupants value="true" />
      <AllowJoyriding value="true" />
      <AllowSundayDriving value="true" />
      <AllowBodyColorMapping value="true" />
      <wheelScale value="0.247" />
      <wheelScaleRear value="0.247" />
      <dirtLevelMin value="0.000000" />
      <dirtLevelMax value="0.700000" />
      <envEffScaleMin value="0.000000" />
      <envEffScaleMax value="1.000000" />
      <envEffScaleMin2 value="0.000000" />
      <envEffScaleMax2 value="1.000000" />
      <damageMapScale value="0.300000" />
      <damageOffsetScale value="0.300000" />
      <diffuseTint value="0x00FFFFFF" />
      <steerWheelMult value="1.000000" />
      <HDTextureDist value="5.000000" />
      <lodDistances content="float_array">
        15.000000
        30.000000
        50.000000
        100.000000
        500.000000
        500.000000
      </lodDistances>
      <minSeatHeight value="0.796" />
      <identicalModelSpawnDistance value="20" />
      <maxNumOfSameColor value="1" />
      <defaultBodyHealth value="1000.000000" />
      <pretendOccupantsScale value="1.000000" />
      <visibleSpawnDistScale value="1.000000" />
      <trackerPathWidth value="2.000000" />
      <weaponForceMult value="1.000000" />
      <frequency value="2" />
      <swankness>SWANKNESS_5</swankness>
      <maxNum value="1" />
      <flags>FLAG_SPORTS FLAG_RICH_CAR FLAG_NO_BROKEN_DOWN_SCENARIO FLAG_CAN_HAVE_NEONS
        FLAG_RECESSED_HEADLIGHT_CORONAS FLAG_EXTRAS_REQUIRE FLAG_EXTRAS_ALL</flags>
      <type>VEHICLE_TYPE_CAR</type>
      <plateType>VPT_BACK_PLATES</plateType>
      <dashboardType>VDT_DUKES</dashboardType>
      <vehicleClass>VC_SPORT_CLASSIC</vehicleClass>
      <wheelType>VWT_HIEND</wheelType>
      <trailers />
      <additionalTrailers />
      <drivers />
      <extraIncludes />
      <doorsWithCollisionWhenClosed />
      <driveableDoors />
      <bumpersNeedToCollideWithMap value="false" />
      <needsRopeTexture value="false" />
      <requiredExtras />
      <rewards />
      <cinematicPartCamera>
        <Item>WHEEL_FRONT_RIGHT_CAMERA</Item>
        <Item>WHEEL_FRONT_LEFT_CAMERA</Item>
        <Item>WHEEL_REAR_RIGHT_CAMERA</Item>
        <Item>WHEEL_REAR_LEFT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet />
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000" />
      <buoyancySphereSizeScale value="1.000000" />
      <pOverrideRagdollThreshold type="NULL" />
      <firstPersonDrivebyData>
        <Item>LOW_TURISMO2_FRONT_LEFT</Item>
        <Item>LOW_TURISMO2_FRONT_RIGHT</Item>
      </firstPersonDrivebyData>
    </Item>
    <Item>
      <modelName>vulture</modelName>
      <txdName>vulture</txdName>
      <handlingId>VULTURE</handlingId>
      <gameName>VULTURE</gameName>
      <vehicleMakeName>BRAVADO</vehicleMakeName>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected />
      <ptfxAssetName>null</ptfxAssetName>
      <audioNameHash>vigero2</audioNameHash>
      <layout>LAYOUT_LOW_RESTRICTED_GAUNTLET3</layout>
      <coverBoundOffsets>GAUNTLET3_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout />
      <cameraName>FOLLOW_CHEETAH_CAMERA</cameraName>
      <aimCameraName>DEFAULT_THIRD_PERSON_VEHICLE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>VEHICLE_BONNET_CAMERA_MID</bonnetCameraName>
      <povCameraName>DEFAULT_POV_CAMERA</povCameraName>
      <FirstPersonDriveByIKOffset x="0.050000" y="-0.070000" z="-0.045000" />
      <FirstPersonDriveByUnarmedIKOffset x="-0.050000" y="-0.080000" z="-0.020000" />
      <FirstPersonProjectileDriveByIKOffset x="0.000000" y="-0.140000" z="-0.050000" />
      <FirstPersonProjectileDriveByPassengerIKOffset x="0.000000" y="-0.140000" z="-0.050000" />
      <FirstPersonDriveByRightPassengerIKOffset x="-0.050000" y="-0.070000" z="-0.045000" />
      <FirstPersonDriveByRightPassengerUnarmedIKOffset x="-0.010000" y="-0.100000" z="-0.020000" />
      <FirstPersonMobilePhoneOffset x="0.145000" y="0.083000" z="0.535000" />
      <FirstPersonPassengerMobilePhoneOffset x="0.216000" y="0.048000" z="0.420000" />
      <PovCameraOffset x="0.000000" y="-0.345000" z="0.645000" />
      <PovCameraVerticalAdjustmentForRollCage value="0.000000" />
      <PovPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000" />
      <vfxInfoName>VFXVEHICLEINFO_CAR_GENERIC</vfxInfoName>
      <shouldUseCinematicViewMode value="true" />
      <shouldCameraTransitionOnClimbUpDown value="false" />
      <shouldCameraIgnoreExiting value="false" />
      <AllowPretendOccupants value="true" />
      <AllowJoyriding value="true" />
      <AllowSundayDriving value="true" />
      <AllowBodyColorMapping value="true" />
      <wheelScale value="0.220000" />
      <wheelScaleRear value="0.220000" />
      <dirtLevelMin value="0.000000" />
      <dirtLevelMax value="0.800000" />
      <envEffScaleMin value="0.000000" />
      <envEffScaleMax value="1.000000" />
      <envEffScaleMin2 value="0.000000" />
      <envEffScaleMax2 value="1.000000" />
      <damageMapScale value="0.500000" />
      <damageOffsetScale value="0.500000" />
      <diffuseTint value="0x00FFFFFF" />
      <steerWheelMult value="0.700000" />
      <HDTextureDist value="5.000000" />
      <lodDistances content="float_array">
        15.000000
        30.000000
        70.000000
        140.000000
        500.000000
        500.000000
      </lodDistances>
      <identicalModelSpawnDistance value="20" />
      <maxNumOfSameColor value="1" />
      <defaultBodyHealth value="1000.000000" />
      <pretendOccupantsScale value="1.000000" />
      <visibleSpawnDistScale value="1.000000" />
      <trackerPathWidth value="2.000000" />
      <weaponForceMult value="1.000000" />
      <frequency value="45" />
      <swankness>SWANKNESS_2</swankness>
      <maxNum value="4" />
      <flags>FLAG_AVERAGE_CAR FLAG_HAS_INTERIOR_EXTRAS</flags>
      <type>VEHICLE_TYPE_CAR</type>
      <plateType>VPT_FRONT_AND_BACK_PLATES</plateType>
      <vehicleClass>VC_MUSCLE</vehicleClass>
      <dashboardType>VDT_DUKES</dashboardType>
      <wheelType>VWT_MUSCLE</wheelType>
      <trailers />
      <additionalTrailers />
      <drivers />
      <extraIncludes />
      <doorsWithCollisionWhenClosed />
      <driveableDoors />
      <bumpersNeedToCollideWithMap value="false" />
      <needsRopeTexture value="false" />
      <requiredExtras />
      <rewards />
      <cinematicPartCamera>
        <Item>WHEEL_FRONT_RIGHT_CAMERA</Item>
        <Item>WHEEL_FRONT_LEFT_CAMERA</Item>
        <Item>WHEEL_REAR_RIGHT_CAMERA</Item>
        <Item>WHEEL_REAR_LEFT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet />
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000" />
      <buoyancySphereSizeScale value="1.000000" />
      <pOverrideRagdollThreshold type="NULL" />
      <firstPersonDrivebyData>
        <Item>LOW_GAUNTLET3_FRONT_LEFT</Item>
        <Item>LOW_GAUNTLET3_FRONT_RIGHT</Item>
      </firstPersonDrivebyData>
    </Item>
    <Item>
      <modelName>ziongtc</modelName>
      <txdName>ziongtc</txdName>
      <handlingId>ZIONGTC</handlingId>
      <gameName>ZIONGTC</gameName>
      <vehicleMakeName>UBERMACH</vehicleMakeName>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected />
      <ptfxAssetName>null</ptfxAssetName>
      <audioNameHash>torero2</audioNameHash>
      <layout>LAYOUT_LOW</layout>
      <coverBoundOffsets>SENTINEL_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout />
      <cameraName>DEFAULT_FOLLOW_VEHICLE_CAMERA</cameraName>
      <aimCameraName>MID_BOX_VEHICLE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>VEHICLE_BONNET_CAMERA_MID_NEAR</bonnetCameraName>
      <povCameraName>DEFAULT_POV_CAMERA</povCameraName>
      <FirstPersonDriveByIKOffset x="0.000000" y="-0.040000" z="-0.040000" />
      <FirstPersonDriveByUnarmedIKOffset x="-0.050000" y="-0.070000" z="0.000000" />
      <FirstPersonProjectileDriveByIKOffset x="0.000000" y="-0.120000" z="0.000000" />
      <FirstPersonProjectileDriveByPassengerIKOffset x="-0.080000" y="-0.100000" z="0.015000" />
      <FirstPersonProjectileDriveByRearLeftIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonProjectileDriveByRearRightIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonDriveByLeftPassengerIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonDriveByRightPassengerIKOffset x="0.000000" y="-0.055000" z="-0.040000" />
      <FirstPersonDriveByRightRearPassengerIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonDriveByLeftPassengerUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonDriveByRightPassengerUnarmedIKOffset x="0.050000" y="-0.150000" z="0.000000" />
      <FirstPersonMobilePhoneOffset x="0.141000" y="0.191000" z="0.568000" />
      <FirstPersonPassengerMobilePhoneOffset x="0.136000" y="0.148000" z="0.473000" />
      <PovCameraOffset x="0.000000" y="-0.260000" z="0.670000" />
      <PovCameraVerticalAdjustmentForRollCage value="-0.030000" />
      <PovPassengerCameraOffset x="0.000000" y="-0.050000" z="0.000000" />
      <PovRearPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000" />
      <vfxInfoName>VFXVEHICLEINFO_CAR_GENERIC</vfxInfoName>
      <shouldUseCinematicViewMode value="true" />
      <shouldCameraTransitionOnClimbUpDown value="false" />
      <shouldCameraIgnoreExiting value="false" />
      <AllowPretendOccupants value="true" />
      <AllowJoyriding value="true" />
      <AllowSundayDriving value="true" />
      <AllowBodyColorMapping value="true" />
      <wheelScale value="0.310000" />
      <wheelScaleRear value="0.310000" />
      <dirtLevelMin value="0.000000" />
      <dirtLevelMax value="0.550000" />
      <envEffScaleMin value="0.000000" />
      <envEffScaleMax value="1.000000" />
      <envEffScaleMin2 value="0.000000" />
      <envEffScaleMax2 value="1.000000" />
      <damageMapScale value="0.660000" />
      <damageOffsetScale value="1.000000" />
      <diffuseTint value="0x00FFFFFF" />
      <steerWheelMult value="0.600000" />
      <HDTextureDist value="5.000000" />
      <lodDistances content="float_array">
        20.000000
        40.000000
        80.000000
        120.000000
        500.000000
        500.000000
      </lodDistances>
      <minSeatHeight value="0.868" />
      <identicalModelSpawnDistance value="80" />
      <maxNumOfSameColor value="1" />
      <defaultBodyHealth value="1000.000000" />
      <pretendOccupantsScale value="1.000000" />
      <visibleSpawnDistScale value="1.000000" />
      <trackerPathWidth value="2.000000" />
      <weaponForceMult value="1.000000" />
      <frequency value="5" />
      <swankness>SWANKNESS_5</swankness>
      <maxNum value="1" />
      <flags>FLAG_SPORTS FLAG_INCREASE_PED_COMMENTS FLAG_ALLOW_HATS_NO_ROOF
        FLAG_DISABLE_THROUGH_WINDSCREEN FLAG_RICH_CAR</flags>
      <type>VEHICLE_TYPE_CAR</type>
      <plateType>VPT_BACK_PLATES</plateType>
      <dashboardType>VDT_RACE</dashboardType>
      <vehicleClass>VC_SPORT</vehicleClass>
      <wheelType>VWT_SPORT</wheelType>
      <trailers />
      <additionalTrailers />
      <drivers />
      <extraIncludes />
      <doorsWithCollisionWhenClosed />
      <driveableDoors />
      <bumpersNeedToCollideWithMap value="false" />
      <needsRopeTexture value="false" />
      <requiredExtras />
      <rewards />
      <cinematicPartCamera>
        <Item>WHEEL_FRONT_RIGHT_CAMERA</Item>
        <Item>WHEEL_FRONT_LEFT_CAMERA</Item>
        <Item>WHEEL_REAR_RIGHT_CAMERA</Item>
        <Item>WHEEL_REAR_LEFT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet />
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000" />
      <buoyancySphereSizeScale value="1.000000" />
      <pOverrideRagdollThreshold type="NULL" />
      <firstPersonDrivebyData>
        <Item>STD_EXEMPLAR_FRONT_LEFT</Item>
        <Item>STD_SENTINEL_FRONT_RIGHT</Item>
      </firstPersonDrivebyData>
    </Item>
    <Item>
      <modelName>zrgpr</modelName>
      <txdName>zrgpr</txdName>
      <handlingId>ZRGPR</handlingId>
      <gameName>ZRGPR</gameName>
      <vehicleMakeName>ANNIS</vehicleMakeName>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected />
      <ptfxAssetName>null</ptfxAssetName>
      <audioNameHash>ZR350</audioNameHash>
      <layout>LAYOUT_LOW_ZR350</layout>
      <coverBoundOffsets>ZR350_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout />
      <cameraName>DEFAULT_FOLLOW_VEHICLE_CAMERA</cameraName>
      <aimCameraName>MID_BOX_VEHICLE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>VEHICLE_BONNET_CAMERA_STANDARD_LONG_REBLA</bonnetCameraName>
      <povCameraName>REDUCED_NEAR_CLIP_POV_CAMERA</povCameraName>
      <FirstPersonDriveByIKOffset x="0.050000" y="-0.140000" z="-0.060000" />
      <FirstPersonDriveByUnarmedIKOffset x="-0.010000" y="-0.030000" z="0.000000" />
      <FirstPersonProjectileDriveByIKOffset x="0.040000" y="-0.110000" z="-0.060000" />
      <FirstPersonProjectileDriveByPassengerIKOffset x="-0.090000" y="-0.070000" z="-0.070000" />
      <FirstPersonDriveByRightPassengerIKOffset x="-0.020000" y="-0.130000" z="-0.040000" />
      <FirstPersonDriveByRightPassengerUnarmedIKOffset x="0.000000" y="-0.100000" z="0.000000" />
      <FirstPersonMobilePhoneOffset x="0.190000" y="0.225000" z="0.483000" />
      <FirstPersonPassengerMobilePhoneOffset x="0.191000" y="0.245000" z="0.353000" />
      <PovCameraOffset x="0.025000" y="-0.245000" z="0.600000" />
      <PovCameraVerticalAdjustmentForRollCage value="-0.009000" />
      <PovPassengerCameraOffset x="-0.030000" y="-0.020000" z="-0.005000" />
      <vfxInfoName>VFXVEHICLEINFO_CAR_GENERIC</vfxInfoName>
      <shouldUseCinematicViewMode value="true" />
      <shouldCameraTransitionOnClimbUpDown value="false" />
      <shouldCameraIgnoreExiting value="false" />
      <AllowPretendOccupants value="true" />
      <AllowJoyriding value="true" />
      <AllowSundayDriving value="true" />
      <AllowBodyColorMapping value="true" />
      <wheelScale value="0.286700" />
      <wheelScaleRear value="0.286700" />
      <dirtLevelMin value="0.300000" />
      <dirtLevelMax value="1.000000" />
      <envEffScaleMin value="0.000000" />
      <envEffScaleMax value="1.000000" />
      <envEffScaleMin2 value="0.000000" />
      <envEffScaleMax2 value="1.000000" />
      <damageMapScale value="0.500000" />
      <damageOffsetScale value="0.500000" />
      <diffuseTint value="0x00FFFFFF" />
      <steerWheelMult value="0.600000" />
      <HDTextureDist value="5.000000" />
      <lodDistances content="float_array">
        30.000000
        60.000000
        90.000000
        150.000000
        500.000000
        500.000000
      </lodDistances>
      <minSeatHeight value="0.84" />
      <identicalModelSpawnDistance value="20" />
      <maxNumOfSameColor value="10" />
      <defaultBodyHealth value="1000.000000" />
      <pretendOccupantsScale value="1.000000" />
      <visibleSpawnDistScale value="1.000000" />
      <trackerPathWidth value="2.000000" />
      <weaponForceMult value="1.000000" />
      <frequency value="100" />
      <swankness>SWANKNESS_1</swankness>
      <maxNum value="999" />
      <flags>FLAG_EXTRAS_STRONG FLAG_EXTRAS_REQUIRE FLAG_EXTRAS_ALL FLAG_AVERAGE_CAR
        FLAG_CAN_HAVE_NEONS FLAG_INCREASE_CAMBER_WITH_SUSPENSION_MOD FLAG_RECESSED_TAILLIGHT_CORONAS</flags>
      <type>VEHICLE_TYPE_CAR</type>
      <plateType>VPT_BACK_PLATES</plateType>
      <dashboardType>VDT_DUKES</dashboardType>
      <vehicleClass>VC_SPORT</vehicleClass>
      <wheelType>VWT_SPORT</wheelType>
      <trailers />
      <additionalTrailers />
      <drivers />
      <extraIncludes />
      <doorsWithCollisionWhenClosed />
      <driveableDoors />
      <bumpersNeedToCollideWithMap value="false" />
      <needsRopeTexture value="false" />
      <requiredExtras />
      <rewards />
      <cinematicPartCamera>
        <Item>WHEEL_FRONT_RIGHT_CAMERA</Item>
        <Item>WHEEL_FRONT_LEFT_CAMERA</Item>
        <Item>WHEEL_REAR_RIGHT_CAMERA</Item>
        <Item>WHEEL_REAR_LEFT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet />
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000" />
      <buoyancySphereSizeScale value="1.000000" />
      <pOverrideRagdollThreshold type="NULL" />
      <firstPersonDrivebyData>
        <Item>LOW_ZR350_FRONT_LEFT</Item>
        <Item>LOW_ZR350_FRONT_RIGHT</Item>
      </firstPersonDrivebyData>
      <numSeatsOverride value="2" />
    </Item>
    <Item>
      <modelName>domgtcoupe</modelName>
      <txdName>domgtcoupe</txdName>
      <handlingId>DOMGTCOUPE</handlingId>
      <gameName>DOMGTCOUPE</gameName>
      <vehicleMakeName>VAPID</vehicleMakeName>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName />
      <animConvRoofName />
      <animConvRoofWindowsAffected />
      <ptfxAssetName>null</ptfxAssetName>
      <audioNameHash>DOMINATOR7</audioNameHash>
      <layout>LAYOUT_STD_DOMINATOR9</layout>
      <coverBoundOffsets>DOMINATOR7_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout />
      <cameraName>FOLLOW_CHEETAH_CAMERA</cameraName>
      <aimCameraName>MID_BOX_VEHICLE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>VEHICLE_BONNET_CAMERA_STANDARD_LONG_REBLA</bonnetCameraName>
      <povCameraName>REDUCED_NEAR_CLIP_POV_CAMERA</povCameraName>
      <FirstPersonDriveByIKOffset x="0.070000" y="-0.100000" z="-0.0450000" />
      <FirstPersonDriveByUnarmedIKOffset x="0.000000" y="-0.025000" z="0.000000" />
      <FirstPersonProjectileDriveByIKOffset x="0.000000" y="-0.110000" z="-0.050000" />
      <FirstPersonProjectileDriveByPassengerIKOffset x="-0.040000" y="-0.110000" z="-0.070000" />
      <FirstPersonDriveByRightPassengerIKOffset x="-0.020000" y="-0.100000" z="-0.060000" />
      <FirstPersonDriveByRightPassengerUnarmedIKOffset x="0.000000" y="-0.080000" z="0.000000" />
      <FirstPersonMobilePhoneOffset x="0.160000" y="0.205000" z="0.538000" />
      <FirstPersonPassengerMobilePhoneOffset x="0.245000" y="0.155000" z="0.423000" />
      <FirstPersonMobilePhoneSeatIKOffset />
      <PovCameraOffset x="0.000000" y="-0.210000" z="0.695000" />
      <PovCameraVerticalAdjustmentForRollCage value="-0.015000" />
      <PovPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000" />
      <PovRearPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000" />
      <vfxInfoName>VFXVEHICLEINFO_CAR_GENERIC</vfxInfoName>
      <shouldUseCinematicViewMode value="true" />
      <shouldCameraTransitionOnClimbUpDown value="false" />
      <shouldCameraIgnoreExiting value="false" />
      <AllowPretendOccupants value="true" />
      <AllowJoyriding value="true" />
      <AllowSundayDriving value="true" />
      <AllowBodyColorMapping value="true" />
      <wheelScale value="0.275000" />
      <wheelScaleRear value="0.275000" />
      <dirtLevelMin value="0.000000" />
      <dirtLevelMax value="0.700000" />
      <envEffScaleMin value="0.000000" />
      <envEffScaleMax value="1.000000" />
      <envEffScaleMin2 value="0.000000" />
      <envEffScaleMax2 value="1.000000" />
      <damageMapScale value="0.400000" />
      <damageOffsetScale value="0.400000" />
      <diffuseTint value="0x00FFFFFF" />
      <steerWheelMult value="0.650000" />
      <HDTextureDist value="5.000000" />
      <lodDistances content="float_array">
        50.000000
        110.000000
        120.000000
        200.000000
        350.000000
        500.000000
      </lodDistances>
      <minSeatHeight value="0.832" />
      <identicalModelSpawnDistance value="20" />
      <maxNumOfSameColor value="10" />
      <defaultBodyHealth value="1000.000000" />
      <pretendOccupantsScale value="1.000000" />
      <visibleSpawnDistScale value="1.000000" />
      <trackerPathWidth value="2.000000" />
      <weaponForceMult value="1.000000" />
      <frequency value="50" />
      <swankness>SWANKNESS_2</swankness>
      <maxNum value="50" />
      <flags>FLAG_SPORTS FLAG_RICH_CAR FLAG_NO_BROKEN_DOWN_SCENARIO FLAG_RECESSED_TAILLIGHT_CORONAS</flags>
      <type>VEHICLE_TYPE_CAR</type>
      <plateType>VPT_BACK_PLATES</plateType>
      <dashboardType>VDT_RACE</dashboardType>
      <vehicleClass>VC_MUSCLE</vehicleClass>
      <wheelType>VWT_HIEND</wheelType>
      <trailers />
      <additionalTrailers />
      <drivers />
      <extraIncludes />
      <doorsWithCollisionWhenClosed />
      <driveableDoors />
      <bumpersNeedToCollideWithMap value="false" />
      <needsRopeTexture value="false" />
      <requiredExtras />
      <rewards />
      <cinematicPartCamera>
        <Item>WHEEL_FRONT_RIGHT_CAMERA</Item>
        <Item>WHEEL_FRONT_LEFT_CAMERA</Item>
        <Item>WHEEL_REAR_RIGHT_CAMERA</Item>
        <Item>WHEEL_REAR_LEFT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet />
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000" />
      <buoyancySphereSizeScale value="1.000000" />
      <pOverrideRagdollThreshold type="NULL" />
      <firstPersonDrivebyData>
        <Item>LOW_DOMINATOR9_FRONT_LEFT</Item>
        <Item>LOW_DOMINATOR9_FRONT_RIGHT</Item>
      </firstPersonDrivebyData>
      <numSeatsOverride value="2" />
    </Item>
    <Item>
      <modelName>draftgpr</modelName>
      <txdName>draftgpr</txdName>
      <handlingId>DRAFTGPR</handlingId>
      <gameName>DRAFTGPR</gameName>
      <vehicleMakeName>OBEY</vehicleMakeName>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected />
      <ptfxAssetName>null</ptfxAssetName>
      <audioNameHash>DRAFTER</audioNameHash>
      <layout>LAYOUT_STD_DRAFTER</layout>
      <coverBoundOffsets>DRAFTER_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout />
      <cameraName>DEFAULT_FOLLOW_VEHICLE_CAMERA</cameraName>
      <aimCameraName>DEFAULT_THIRD_PERSON_VEHICLE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>VEHICLE_BONNET_CAMERA_STANDARD_LONG_EXTRA_LOW</bonnetCameraName>
      <povCameraName>REDUCED_NEAR_CLIP_POV_CAMERA</povCameraName>
      <FirstPersonDriveByIKOffset x="0.050000" y="-0.120000" z="-0.040000" />
      <FirstPersonDriveByUnarmedIKOffset x="-0.020000" y="-0.130000" z="0.010000" />
      <FirstPersonProjectileDriveByIKOffset x="0.020000" y="-0.130000" z="-0.060000" />
      <FirstPersonProjectileDriveByPassengerIKOffset x="-0.020000" y="-0.130000" z="-0.060000" />
      <FirstPersonDriveByRightPassengerIKOffset x="-0.050000" y="-0.120000" z="-0.040000" />
      <FirstPersonDriveByRightPassengerUnarmedIKOffset x="-0.020000" y="-0.130000" z="0.010000" />
      <FirstPersonMobilePhoneOffset x="0.153000" y="0.143000" z="0.563000" />
      <FirstPersonPassengerMobilePhoneOffset x="0.241000" y="0.193000" z="0.430000" />
      <PovCameraOffset x="0.000000" y="-0.230000" z="0.680000" />
      <PovCameraVerticalAdjustmentForRollCage value="0.000000" />
      <PovPassengerCameraOffset x="0.000000" y="0.050000" z="0.000000" />
      <PovRearPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000" />
      <vfxInfoName>VFXVEHICLEINFO_CAR_GENERIC</vfxInfoName>
      <shouldUseCinematicViewMode value="true" />
      <shouldCameraTransitionOnClimbUpDown value="false" />
      <shouldCameraIgnoreExiting value="false" />
      <AllowPretendOccupants value="true" />
      <AllowJoyriding value="true" />
      <AllowSundayDriving value="true" />
      <AllowBodyColorMapping value="true" />
      <wheelScale value="0.276500" />
      <wheelScaleRear value="0.276500" />
      <dirtLevelMin value="0.000000" />
      <dirtLevelMax value="0.300000" />
      <envEffScaleMin value="0.000000" />
      <envEffScaleMax value="1.000000" />
      <envEffScaleMin2 value="0.000000" />
      <envEffScaleMax2 value="1.000000" />
      <damageMapScale value="0.400000" />
      <damageOffsetScale value="0.400000" />
      <diffuseTint value="0x00FFFFFF" />
      <steerWheelMult value="1.000000" />
      <HDTextureDist value="5.000000" />
      <lodDistances content="float_array">
        15.000000
        30.000000
        70.000000
        140.000000
        300.000000
        300.000000
      </lodDistances>
      <minSeatHeight value="0.481" />
      <identicalModelSpawnDistance value="20" />
      <maxNumOfSameColor value="1" />
      <defaultBodyHealth value="1000.000000" />
      <pretendOccupantsScale value="1.000000" />
      <visibleSpawnDistScale value="1.000000" />
      <trackerPathWidth value="2.000000" />
      <weaponForceMult value="1.000000" />
      <frequency value="50" />
      <swankness>SWANKNESS_5</swankness>
      <maxNum value="5" />
      <flags>FLAG_SPORTS FLAG_RICH_CAR FLAG_NO_BROKEN_DOWN_SCENARIO FLAG_RECESSED_TAILLIGHT_CORONAS
        FLAG_HAS_INTERIOR_EXTRAS FLAG_CAN_HAVE_NEONS FLAG_INCREASE_CAMBER_WITH_SUSPENSION_MOD</flags>
      <type>VEHICLE_TYPE_CAR</type>
      <plateType>VPT_BACK_PLATES</plateType>
      <dashboardType>VDT_RACE</dashboardType>
      <vehicleClass>VC_SPORT</vehicleClass>
      <wheelType>VWT_HIEND</wheelType>
      <trailers />
      <additionalTrailers />
      <drivers />
      <extraIncludes />
      <doorsWithCollisionWhenClosed />
      <driveableDoors />
      <bumpersNeedToCollideWithMap value="false" />
      <needsRopeTexture value="false" />
      <requiredExtras />
      <rewards />
      <cinematicPartCamera>
        <Item>WHEEL_FRONT_RIGHT_CAMERA</Item>
        <Item>WHEEL_FRONT_LEFT_CAMERA</Item>
        <Item>WHEEL_REAR_RIGHT_CAMERA</Item>
        <Item>WHEEL_REAR_LEFT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet />
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000" />
      <buoyancySphereSizeScale value="1.000000" />
      <pOverrideRagdollThreshold type="NULL" />
      <firstPersonDrivebyData>
        <Item>STD_DRAFTER_FRONT_LEFT</Item>
        <Item>STD_DRAFTER_FRONT_RIGHT</Item>
      </firstPersonDrivebyData>
    </Item>
    <Item>
      <modelName>elegyr</modelName>
      <txdName>elegyr</txdName>
      <handlingId>ELEGYR</handlingId>
      <gameName>ELEGYR</gameName>
      <vehicleMakeName>ANNIS</vehicleMakeName>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected />
      <ptfxAssetName>null</ptfxAssetName>
      <audioNameHash>ELEGY</audioNameHash>
      <layout>LAYOUT_STD_ELEGYRHD</layout>
      <coverBoundOffsets>ELEGYRHD_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout />
      <cameraName>DEFAULT_FOLLOW_VEHICLE_CAMERA</cameraName>
      <aimCameraName>DEFAULT_THIRD_PERSON_VEHICLE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>VEHICLE_BONNET_CAMERA_STANDARD</bonnetCameraName>
      <povCameraName>REDUCED_NEAR_CLIP_POV_CAMERA</povCameraName>
      <FirstPersonDriveByIKOffset x="0.000000" y="-0.060000" z="-0.040000" />
      <FirstPersonDriveByUnarmedIKOffset x="0.000000" y="-0.060000" z="-0.020000" />
      <FirstPersonProjectileDriveByIKOffset x="-0.025000" y="-0.130000" z="-0.040000" />
      <FirstPersonProjectileDriveByPassengerIKOffset x="0.025000" y="-0.130000" z="-0.040000" />
      <FirstPersonProjectileDriveByRearLeftIKOffset x="0.000000" y="0.000000" z="-0.010000" />
      <FirstPersonProjectileDriveByRearRightIKOffset x="0.000000" y="-0.020000" z="-0.010000" />
      <FirstPersonDriveByLeftPassengerIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonDriveByRightPassengerIKOffset x="0.000000" y="-0.060000" z="-0.040000" />
      <FirstPersonDriveByRightRearPassengerIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonDriveByLeftPassengerUnarmedIKOffset x="0.000000" y="-0.010000" z="0.020000" />
      <FirstPersonDriveByRightPassengerUnarmedIKOffset x="0.000000" y="-0.060000" z="-0.020000" />
      <FirstPersonMobilePhoneOffset x="0.146000" y="0.152000" z="0.554000" />
      <FirstPersonPassengerMobilePhoneOffset x="0.176000" y="0.068000" z="0.465000" />
      <PovCameraOffset x="0.000000" y="-0.300000" z="0.680000" />
      <PovCameraVerticalAdjustmentForRollCage value="-0.030000" />
      <PovPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000" />
      <PovRearPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000" />
      <vfxInfoName>VFXVEHICLEINFO_CAR_GENERIC</vfxInfoName>
      <shouldUseCinematicViewMode value="true" />
      <shouldCameraTransitionOnClimbUpDown value="false" />
      <shouldCameraIgnoreExiting value="false" />
      <AllowPretendOccupants value="true" />
      <AllowJoyriding value="true" />
      <AllowSundayDriving value="true" />
      <AllowBodyColorMapping value="true" />
      <wheelScale value="0.242500" />
      <wheelScaleRear value="0.242500" />
      <dirtLevelMin value="0.000000" />
      <dirtLevelMax value="0.550000" />
      <envEffScaleMin value="0.000000" />
      <envEffScaleMax value="1.000000" />
      <envEffScaleMin2 value="0.000000" />
      <envEffScaleMax2 value="1.000000" />
      <damageMapScale value="0.500000" />
      <damageOffsetScale value="0.500000" />
      <diffuseTint value="0x00FFFFFF" />
      <steerWheelMult value="0.850000" />
      <HDTextureDist value="5.000000" />
      <lodDistances content="float_array">
        20.000000
        40.000000
        80.000000
        120.000000
        300.000000
        300.000000
      </lodDistances>
      <minSeatHeight value="0.871" />
      <identicalModelSpawnDistance value="80" />
      <maxNumOfSameColor value="1" />
      <defaultBodyHealth value="1000.000000" />
      <pretendOccupantsScale value="1.000000" />
      <visibleSpawnDistScale value="1.000000" />
      <trackerPathWidth value="2.000000" />
      <weaponForceMult value="1.000000" />
      <frequency value="5" />
      <swankness>SWANKNESS_5</swankness>
      <maxNum value="1" />
      <flags>FLAG_HAS_LIVERY FLAG_SPORTS FLAG_NO_BROKEN_DOWN_SCENARIO FLAG_INCREASE_PED_COMMENTS
        FLAG_DRIVER_SHOULD_BE_MALE FLAG_EXTRAS_STRONG FLAG_IS_OFFROAD_VEHICLE</flags>
      <type>VEHICLE_TYPE_CAR</type>
      <plateType>VPT_FRONT_AND_BACK_PLATES</plateType>
      <dashboardType>VDT_RACE</dashboardType>
      <vehicleClass>VC_SPORT</vehicleClass>
      <wheelType>VWT_SPORT</wheelType>
      <trailers />
      <additionalTrailers />
      <drivers />
      <extraIncludes />
      <doorsWithCollisionWhenClosed />
      <driveableDoors />
      <bumpersNeedToCollideWithMap value="false" />
      <needsRopeTexture value="false" />
      <requiredExtras />
      <rewards />
      <cinematicPartCamera>
        <Item>WHEEL_FRONT_RIGHT_CAMERA</Item>
        <Item>WHEEL_FRONT_LEFT_CAMERA</Item>
        <Item>WHEEL_REAR_RIGHT_CAMERA</Item>
        <Item>WHEEL_REAR_LEFT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet />
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000" />
      <buoyancySphereSizeScale value="1.000000" />
      <pOverrideRagdollThreshold type="NULL" />
      <firstPersonDrivebyData>
        <Item>STD_EXEMPLAR_FRONT_LEFT</Item>
        <Item>STD_EXEMPLAR_FRONT_RIGHT</Item>
      </firstPersonDrivebyData>
    </Item>
    <Item>
      <modelName>elegyheritage</modelName>
      <txdName>elegyheritage</txdName>
      <handlingId>elegyheritage</handlingId>
      <gameName>ELEGYH</gameName>
      <vehicleMakeName>ANNIS</vehicleMakeName>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected />
      <ptfxAssetName>null</ptfxAssetName>
      <audioNameHash>ELEGYRH7</audioNameHash>
      <layout>LAYOUT_LOW</layout>
      <coverBoundOffsets>ELEGYRETRO_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout />
      <cameraName>DEFAULT_FOLLOW_VEHICLE_CAMERA</cameraName>
      <aimCameraName>DEFAULT_THIRD_PERSON_VEHICLE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>VEHICLE_BONNET_CAMERA_STANDARD</bonnetCameraName>
      <povCameraName>REDUCED_NEAR_CLIP_POV_CAMERA</povCameraName>
      <FirstPersonDriveByIKOffset x="0.000000" y="-0.060000" z="-0.040000" />
      <FirstPersonDriveByUnarmedIKOffset x="0.000000" y="-0.060000" z="-0.020000" />
      <FirstPersonProjectileDriveByIKOffset x="-0.025000" y="-0.130000" z="-0.040000" />
      <FirstPersonProjectileDriveByPassengerIKOffset x="0.025000" y="-0.130000" z="-0.040000" />
      <FirstPersonProjectileDriveByRearLeftIKOffset x="0.000000" y="0.000000" z="-0.010000" />
      <FirstPersonProjectileDriveByRearRightIKOffset x="0.000000" y="-0.020000" z="-0.010000" />
      <FirstPersonDriveByLeftPassengerIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonDriveByRightPassengerIKOffset x="0.000000" y="-0.060000" z="-0.040000" />
      <FirstPersonDriveByRightRearPassengerIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonDriveByLeftPassengerUnarmedIKOffset x="0.000000" y="-0.010000" z="0.020000" />
      <FirstPersonDriveByRightPassengerUnarmedIKOffset x="0.000000" y="-0.060000" z="-0.020000" />
      <FirstPersonMobilePhoneOffset x="0.146000" y="0.152000" z="0.554000" />
      <FirstPersonPassengerMobilePhoneOffset x="0.176000" y="0.068000" z="0.465000" />
      <PovCameraOffset x="0.000000" y="-0.300000" z="0.680000" />
      <PovCameraVerticalAdjustmentForRollCage value="-0.030000" />
      <PovPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000" />
      <PovRearPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000" />
      <vfxInfoName>VFXVEHICLEINFO_CAR_GENERIC</vfxInfoName>
      <shouldUseCinematicViewMode value="true" />
      <shouldCameraTransitionOnClimbUpDown value="false" />
      <shouldCameraIgnoreExiting value="false" />
      <AllowPretendOccupants value="true" />
      <AllowJoyriding value="true" />
      <AllowSundayDriving value="true" />
      <AllowBodyColorMapping value="true" />
      <wheelScale value="0.242500" />
      <wheelScaleRear value="0.242500" />
      <dirtLevelMin value="0.000000" />
      <dirtLevelMax value="0.550000" />
      <envEffScaleMin value="0.000000" />
      <envEffScaleMax value="1.000000" />
      <envEffScaleMin2 value="0.000000" />
      <envEffScaleMax2 value="1.000000" />
      <damageMapScale value="0.500000" />
      <damageOffsetScale value="0.500000" />
      <diffuseTint value="0x00FFFFFF" />
      <steerWheelMult value="1.0000" />
      <HDTextureDist value="5.000000" />
      <lodDistances content="float_array">
        15.000000
        30.000000
        70.000000
        140.000000
        300.000000
        300.000000
      </lodDistances>
      <minSeatHeight value="0.871" />
      <identicalModelSpawnDistance value="20" />
      <maxNumOfSameColor value="1" />
      <defaultBodyHealth value="1000.000000" />
      <pretendOccupantsScale value="1.000000" />
      <visibleSpawnDistScale value="1.000000" />
      <trackerPathWidth value="2.000000" />
      <weaponForceMult value="1.000000" />
      <frequency value="10" />
      <swankness>SWANKNESS_3</swankness>
      <maxNum value="10" />
      <flags>FLAG_SPORTS FLAG_AVERAGE_CAR FLAG_HAS_INTERIOR_EXTRAS FLAG_RECESSED_TAILLIGHT_CORONAS FLAG_EXTRAS_ALL</flags>
      <type>VEHICLE_TYPE_CAR</type>
      <plateType>VPT_FRONT_AND_BACK_PLATES</plateType>
      <dashboardType>VDT_RACE</dashboardType>
      <vehicleClass>VC_SPORT</vehicleClass>
      <wheelType>VWT_SPORT</wheelType>
      <trailers />
      <additionalTrailers />
      <drivers />
      <extraIncludes />
      <doorsWithCollisionWhenClosed />
      <driveableDoors />
      <bumpersNeedToCollideWithMap value="false" />
      <needsRopeTexture value="false" />
      <requiredExtras />
      <rewards />
      <cinematicPartCamera>
        <Item>WHEEL_FRONT_RIGHT_CAMERA</Item>
        <Item>WHEEL_FRONT_LEFT_CAMERA</Item>
        <Item>WHEEL_REAR_RIGHT_CAMERA</Item>
        <Item>WHEEL_REAR_LEFT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet />
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000" />
      <buoyancySphereSizeScale value="1.000000" />
      <pOverrideRagdollThreshold type="NULL" />
      <firstPersonDrivebyData>
        <Item>STD_EXEMPLAR_FRONT_LEFT</Item>
        <Item>STD_EXEMPLAR_FRONT_RIGHT</Item>
      </firstPersonDrivebyData>
    </Item>
    <Item>
      <modelName>elegyrace</modelName>
      <txdName>elegyrace</txdName>
      <handlingId>ELEGYHER</handlingId>
      <gameName>ELEGYR</gameName>
      <vehicleMakeName>ANNIS</vehicleMakeName>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected />
      <ptfxAssetName>null</ptfxAssetName>
      <audioNameHash>ELEGY</audioNameHash>
      <layout>LAYOUT_LOW</layout>
      <coverBoundOffsets>ELEGYRETRO_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout />
      <cameraName>DEFAULT_FOLLOW_VEHICLE_CAMERA</cameraName>
      <aimCameraName>DEFAULT_THIRD_PERSON_VEHICLE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>VEHICLE_BONNET_CAMERA_STANDARD</bonnetCameraName>
      <povCameraName>REDUCED_NEAR_CLIP_POV_CAMERA</povCameraName>
      <FirstPersonDriveByIKOffset x="0.000000" y="-0.060000" z="-0.040000" />
      <FirstPersonDriveByUnarmedIKOffset x="0.000000" y="-0.060000" z="-0.020000" />
      <FirstPersonProjectileDriveByIKOffset x="-0.025000" y="-0.130000" z="-0.040000" />
      <FirstPersonProjectileDriveByPassengerIKOffset x="0.025000" y="-0.130000" z="-0.040000" />
      <FirstPersonProjectileDriveByRearLeftIKOffset x="0.000000" y="0.000000" z="-0.010000" />
      <FirstPersonProjectileDriveByRearRightIKOffset x="0.000000" y="-0.020000" z="-0.010000" />
      <FirstPersonDriveByLeftPassengerIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonDriveByRightPassengerIKOffset x="0.000000" y="-0.060000" z="-0.040000" />
      <FirstPersonDriveByRightRearPassengerIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonDriveByLeftPassengerUnarmedIKOffset x="0.000000" y="-0.010000" z="0.020000" />
      <FirstPersonDriveByRightPassengerUnarmedIKOffset x="0.000000" y="-0.060000" z="-0.020000" />
      <FirstPersonMobilePhoneOffset x="0.146000" y="0.152000" z="0.554000" />
      <FirstPersonPassengerMobilePhoneOffset x="0.176000" y="0.068000" z="0.465000" />
      <PovCameraOffset x="0.000000" y="-0.300000" z="0.680000" />
      <PovCameraVerticalAdjustmentForRollCage value="-0.030000" />
      <PovPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000" />
      <PovRearPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000" />
      <vfxInfoName>VFXVEHICLEINFO_CAR_GENERIC</vfxInfoName>
      <shouldUseCinematicViewMode value="true" />
      <shouldCameraTransitionOnClimbUpDown value="false" />
      <shouldCameraIgnoreExiting value="false" />
      <AllowPretendOccupants value="true" />
      <AllowJoyriding value="true" />
      <AllowSundayDriving value="true" />
      <AllowBodyColorMapping value="true" />
      <wheelScale value="0.242500" />
      <wheelScaleRear value="0.242500" />
      <dirtLevelMin value="0.000000" />
      <dirtLevelMax value="0.550000" />
      <envEffScaleMin value="0.000000" />
      <envEffScaleMax value="1.000000" />
      <envEffScaleMin2 value="0.000000" />
      <envEffScaleMax2 value="1.000000" />
      <damageMapScale value="0.500000" />
      <damageOffsetScale value="0.500000" />
      <diffuseTint value="0x00FFFFFF" />
      <steerWheelMult value="1.0000" />
      <HDTextureDist value="5.000000" />
      <lodDistances content="float_array">
        15.000000
        30.000000
        70.000000
        140.000000
        300.000000
        300.000000
      </lodDistances>
      <minSeatHeight value="0.871" />
      <identicalModelSpawnDistance value="20" />
      <maxNumOfSameColor value="1" />
      <defaultBodyHealth value="1000.000000" />
      <pretendOccupantsScale value="1.000000" />
      <visibleSpawnDistScale value="1.000000" />
      <trackerPathWidth value="2.000000" />
      <weaponForceMult value="1.000000" />
      <frequency value="10" />
      <swankness>SWANKNESS_3</swankness>
      <maxNum value="10" />
      <flags>FLAG_SPORTS FLAG_AVERAGE_CAR FLAG_HAS_INTERIOR_EXTRAS FLAG_RECESSED_TAILLIGHT_CORONAS</flags>
      <type>VEHICLE_TYPE_CAR</type>
      <plateType>VPT_FRONT_AND_BACK_PLATES</plateType>
      <dashboardType>VDT_RACE</dashboardType>
      <vehicleClass>VC_SPORT</vehicleClass>
      <wheelType>VWT_SPORT</wheelType>
      <trailers />
      <additionalTrailers />
      <drivers />
      <extraIncludes />
      <doorsWithCollisionWhenClosed />
      <driveableDoors />
      <bumpersNeedToCollideWithMap value="false" />
      <needsRopeTexture value="false" />
      <requiredExtras />
      <rewards />
      <cinematicPartCamera>
        <Item>WHEEL_FRONT_RIGHT_CAMERA</Item>
        <Item>WHEEL_FRONT_LEFT_CAMERA</Item>
        <Item>WHEEL_REAR_RIGHT_CAMERA</Item>
        <Item>WHEEL_REAR_LEFT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet />
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000" />
      <buoyancySphereSizeScale value="1.000000" />
      <pOverrideRagdollThreshold type="NULL" />
      <firstPersonDrivebyData>
        <Item>STD_EXEMPLAR_FRONT_LEFT</Item>
        <Item>STD_EXEMPLAR_FRONT_RIGHT</Item>
      </firstPersonDrivebyData>
    </Item>
    <Item>
      <modelName>elegyute</modelName>
      <txdName>elegyute</txdName>
      <handlingId>ELEGY</handlingId>
      <gameName>ELEGYUTE</gameName>
      <vehicleMakeName>ANNIS</vehicleMakeName>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected />
      <ptfxAssetName>null</ptfxAssetName>
      <audioNameHash>ELEGY</audioNameHash>
      <layout>LAYOUT_LOW</layout>
      <coverBoundOffsets>ELEGYRETRO_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout />
      <cameraName>DEFAULT_FOLLOW_VEHICLE_CAMERA</cameraName>
      <aimCameraName>DEFAULT_THIRD_PERSON_VEHICLE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>VEHICLE_BONNET_CAMERA_STANDARD</bonnetCameraName>
      <povCameraName>REDUCED_NEAR_CLIP_POV_CAMERA</povCameraName>
      <FirstPersonDriveByIKOffset x="0.000000" y="-0.060000" z="-0.040000" />
      <FirstPersonDriveByUnarmedIKOffset x="0.000000" y="-0.060000" z="-0.020000" />
      <FirstPersonProjectileDriveByIKOffset x="-0.025000" y="-0.130000" z="-0.040000" />
      <FirstPersonProjectileDriveByPassengerIKOffset x="0.025000" y="-0.130000" z="-0.040000" />
      <FirstPersonProjectileDriveByRearLeftIKOffset x="0.000000" y="0.000000" z="-0.010000" />
      <FirstPersonProjectileDriveByRearRightIKOffset x="0.000000" y="-0.020000" z="-0.010000" />
      <FirstPersonDriveByLeftPassengerIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonDriveByRightPassengerIKOffset x="0.000000" y="-0.060000" z="-0.040000" />
      <FirstPersonDriveByRightRearPassengerIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonDriveByLeftPassengerUnarmedIKOffset x="0.000000" y="-0.010000" z="0.020000" />
      <FirstPersonDriveByRightPassengerUnarmedIKOffset x="0.000000" y="-0.060000" z="-0.020000" />
      <FirstPersonMobilePhoneOffset x="0.146000" y="0.152000" z="0.554000" />
      <FirstPersonPassengerMobilePhoneOffset x="0.176000" y="0.068000" z="0.465000" />
      <PovCameraOffset x="0.000000" y="-0.300000" z="0.680000" />
      <PovCameraVerticalAdjustmentForRollCage value="-0.030000" />
      <PovPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000" />
      <PovRearPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000" />
      <vfxInfoName>VFXVEHICLEINFO_CAR_GENERIC</vfxInfoName>
      <shouldUseCinematicViewMode value="true" />
      <shouldCameraTransitionOnClimbUpDown value="false" />
      <shouldCameraIgnoreExiting value="false" />
      <AllowPretendOccupants value="true" />
      <AllowJoyriding value="true" />
      <AllowSundayDriving value="true" />
      <AllowBodyColorMapping value="true" />
      <wheelScale value="0.242500" />
      <wheelScaleRear value="0.242500" />
      <dirtLevelMin value="0.000000" />
      <dirtLevelMax value="0.550000" />
      <envEffScaleMin value="0.000000" />
      <envEffScaleMax value="1.000000" />
      <envEffScaleMin2 value="0.000000" />
      <envEffScaleMax2 value="1.000000" />
      <damageMapScale value="0.500000" />
      <damageOffsetScale value="0.500000" />
      <diffuseTint value="0x00FFFFFF" />
      <steerWheelMult value="0.850000" />
      <HDTextureDist value="5.000000" />
      <lodDistances content="float_array">
        15.000000
        30.000000
        70.000000
        140.000000
        300.000000
        300.000000
      </lodDistances>
      <minSeatHeight value="0.871" />
      <identicalModelSpawnDistance value="20" />
      <maxNumOfSameColor value="1" />
      <defaultBodyHealth value="1000.000000" />
      <pretendOccupantsScale value="1.000000" />
      <visibleSpawnDistScale value="1.000000" />
      <trackerPathWidth value="2.000000" />
      <weaponForceMult value="1.000000" />
      <frequency value="10" />
      <swankness>SWANKNESS_3</swankness>
      <maxNum value="10" />
      <flags>FLAG_SPORTS FLAG_AVERAGE_CAR FLAG_HAS_INTERIOR_EXTRAS</flags>
      <type>VEHICLE_TYPE_CAR</type>
      <plateType>VPT_FRONT_AND_BACK_PLATES</plateType>
      <dashboardType>VDT_RACE</dashboardType>
      <vehicleClass>VC_SPORT</vehicleClass>
      <wheelType>VWT_SPORT</wheelType>
      <trailers />
      <additionalTrailers />
      <drivers />
      <extraIncludes />
      <doorsWithCollisionWhenClosed />
      <driveableDoors />
      <bumpersNeedToCollideWithMap value="false" />
      <needsRopeTexture value="false" />
      <requiredExtras />
      <rewards />
      <cinematicPartCamera>
        <Item>WHEEL_FRONT_RIGHT_CAMERA</Item>
        <Item>WHEEL_FRONT_LEFT_CAMERA</Item>
        <Item>WHEEL_REAR_RIGHT_CAMERA</Item>
        <Item>WHEEL_REAR_LEFT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet />
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000" />
      <buoyancySphereSizeScale value="1.000000" />
      <pOverrideRagdollThreshold type="NULL" />
      <firstPersonDrivebyData>
        <Item>STD_EXEMPLAR_FRONT_LEFT</Item>
        <Item>STD_EXEMPLAR_FRONT_RIGHT</Item>
      </firstPersonDrivebyData>
    </Item>
  </InitDatas>
  <txdRelationships>
    <Item>
      <parent>vehicles_elegy_interior</parent>
      <child>elegyx</child>
    </Item>
    <Item>
      <parent>vehicles_race_generic</parent>
      <child>ellie6str</child>
    </Item>
    <Item>
      <parent>vehicles_race_interior</parent>
      <child>ellie6str</child>
    </Item>
    <Item>
      <parent>vehicles_dom_interior</parent>
      <child>ellie6str</child>
    </Item>
    <Item>
      <parent>vehicles_pfister_race_interior</parent>
      <child>elytron</child>
    </Item>
    <Item>
      <parent>vehicles_race_generic</parent>
      <child>vehicles_race_interior</child>
    </Item>
    <Item>
      <parent>vehicles_race_interior</parent>
      <child>vehicles_fmj_interior</child>
    </Item>
    <Item>
      <parent>vehicles_fmj_interior</parent>
      <child>hachurac</child>
    </Item>
    <Item>
      <parent>vehicles_race_generic</parent>
      <child>hustler</child>
    </Item>
    <Item>
      <parent>vehicles_racecar</parent>
      <child>hustler</child>
    </Item>
    <Item>
      <parent>vehshare</parent>
      <child>vehicles_racecar</child>
    </Item>
    <Item>
      <parent>vehshare</parent>
      <child>vehicles_itali_w_interior</child>
    </Item>
    <Item>
      <parent>vehicles_itali_w_interior</parent>
      <child>italigtr</child>
    </Item>
    <Item>
      <parent>vehicles_schaf_interior</parent>
      <child>jackgpr</child>
    </Item>
    <Item>
      <parent>vehshare</parent>
      <child>vehicles_fmj_w_interior</child>
    </Item>
    <Item>
      <parent>vehicles_race_generic</parent>
      <child>vehicles_fmj_w_race_interior</child>
    </Item>
    <Item>
      <parent>vehicles_fmj_w_race_interior</parent>
      <child>jestgpr</child>
    </Item>
    <Item>
      <parent>vehicles_worn_race</parent>
      <child>vehicles_race_interior</child>
    </Item>
    <Item>
      <parent>vehicles_race_interior</parent>
      <child>vehicles_race_generic</child>
    </Item>
    <Item>
      <parent>vehicles_race_generic</parent>
      <child>vehicles_supergt_interior</child>
    </Item>
    <Item>
      <parent>vehicles_supergt_interior</parent>
      <child>lynxgpr</child>
    </Item>
    <Item>
      <parent>vehicles_nero_w_interior</parent>
      <child>coquette4c</child>
    </Item>
    <Item>
      <parent>vehicles_race_generic</parent>
      <child>majimagt</child>
    </Item>
    <Item>
      <parent>vehicles_comet_interior</parent>
      <child>meteor</child>
    </Item>
    <Item>
      <parent>vehicles_muscle_o_w_interior</parent>
      <child>pantor</child>
    </Item>
    <Item>
      <parent>vehicles_worn_race</parent>
      <child>vehicles_race_interior</child>
    </Item>
    <Item>
      <parent>vehicles_race_interior</parent>
      <child>vehicles_race_generic</child>
    </Item>
    <Item>
      <parent>vehicles_race_generic</parent>
      <child>vehicles_feroci_interior</child>
    </Item>
    <Item>
      <parent>vehicles_feroci_interior</parent>
      <child>pentro</child>
    </Item>
    <Item>
      <parent>vehshare</parent>
      <child>vehicles_exr_interior</child>
    </Item>
    <Item>
      <parent>vehicles_muscle_supercharger</parent>
      <child>vehicles_exr_interior</child>
    </Item>
    <Item>
      <parent>vehicles_exr_interior</parent>
      <child>picadorexr</child>
    </Item>
    <Item>
      <parent>vehshare</parent>
      <child>playboy</child>
    </Item>
    <Item>
      <parent>vehicles_worn_race</parent>
      <child>vehicles_race_interior</child>
    </Item>
    <Item>
      <parent>vehicles_race_interior</parent>
      <child>vehicles_race_generic</child>
    </Item>
    <Item>
      <parent>vehicles_race_generic</parent>
      <child>vehicles_vacca_interior</child>
    </Item>
    <Item>
      <parent>vehicles_vacca_interior</parent>
      <child>reagpr</child>
    </Item>
    <Item>
      <parent>vehshare</parent>
      <child>vehicles_remustwo_interior</child>
    </Item>
    <Item>
      <parent>vehicles_remustwo_interior</parent>
      <child>remustwo</child>
    </Item>
    <Item>
      <parent>vehshare</parent>
      <child>vehicles_race_generic</child>
    </Item>
    <Item>
      <parent>vehicles_race_generic</parent>
      <child>vehicles_race_interior</child>
    </Item>
    <Item>
      <parent>vehicles_race_interior</parent>
      <child>vehicles_requiemzr_interior</child>
    </Item>
    <Item>
      <parent>vehicles_requiemzr_interior</parent>
      <child>requiemzr1</child>
    </Item>
    <Item>
      <parent>vehicles_requiemzr_interior</parent>
      <child>requiemzr3</child>
    </Item>
    <Item>
      <parent>vehicles_jugular_race_interior</parent>
      <child>cypherwb</child>
    </Item>
    <Item>
      <parent>vehicles_race_generic</parent>
      <child>vehicles_elegy_race</child>
    </Item>
    <Item>
      <parent>vehicles_supergt_interior</parent>
      <child>vehicles_racecar</child>
    </Item>
    <Item>
      <parent>vehicles_racecar</parent>
      <child>vehicles_race_interior</child>
    </Item>
    <Item>
      <parent>vehicles_race_interior</parent>
      <child>vehicles_race_generic</child>
    </Item>
    <Item>
      <parent>vehicles_race_generic</parent>
      <child>roxanne</child>
    </Item>
    <Item>
      <parent>vehicles_race_generic</parent>
      <child>s230</child>
    </Item>
    <Item>
      <parent>vehicles_specter_w_interior</parent>
      <child>schlagenstr</child>
    </Item>
    <Item>
      <parent>vehicles_itali_w_interior</parent>
      <child>sheavas</child>
    </Item>
    <Item>
      <parent>vehicles_feroci_interior</parent>
      <child>stratumc</child>
    </Item>
    <Item>
      <parent>vehshare</parent>
      <child>vehicles_race_interior</child>
    </Item>
    <Item>
      <parent>vehicles_race_interior</parent>
      <child>vehicles_sultanrs_interior</child>
    </Item>
    <Item>
      <parent>vehicles_sultanrs_interior</parent>
      <child>sultanrsv8</child>
    </Item>
    <Item>
      <parent>vehicles_race_generic</parent>
      <child>tampax</child>
    </Item>
    <Item>
      <parent>vehicles_race_generic</parent>
      <child>tampax2</child>
    </Item>
    <Item>
      <parent>vehshare</parent>
      <child>vehicles_sm277_interior</child>
    </Item>
    <Item>
      <parent>vehicles_sup1_interior</parent>
      <child>omnisegt</child>
    </Item>
    <Item>
      <parent>vehshare</parent>
      <child>vehicles_jugular_interior</child>
    </Item>
    <Item>
      <parent>vehicles_race_generic</parent>
      <child>vehicles_schaf_interior</child>
    </Item>
    <Item>
      <parent>vehicles_race_generic</parent>
      <child>vehicles_itali_w_interior</child>
    </Item>
    <Item>
      <parent>vehicles_monroe_interior</parent>
      <child>greenwood</child>
    </Item>
    <Item>
      <parent>vehicles_itali_w_interior</parent>
      <child>corsita</child>
    </Item>
    <Item>
      <parent>vehicles_racecar</parent>
      <child>lm87</child>
    </Item>
    <Item>
      <parent>vehicles_itali_w_interior</parent>
      <child>torero2</child>
    </Item>
    <Item>
      <parent>vehicles_itali_w_interior</parent>
      <child>tenf</child>
    </Item>
    <Item>
      <parent>vehicles_itali_w_interior</parent>
      <child>tenf2</child>
    </Item>
    <Item>
      <parent>vehicles_dom_w_interior</parent>
      <child>weevil2</child>
    </Item>
    <Item>
      <parent>vehicles_race_generic</parent>
      <child>draugur</child>
    </Item>
    <Item>
      <parent>vehicles_sm277_interior</parent>
      <child>sm722</child>
    </Item>
    <Item>
      <parent>vehicles_race_generic</parent>
      <child>postlude</child>
    </Item>
    <Item>
      <parent>vehicles_flyer_interior</parent>
      <child>conada</child>
    </Item>
    <Item>
      <parent>vehicles_dom_interior</parent>
      <child>deimos</child>
    </Item>
    <Item>
      <parent>vehicles_banshee_interior</parent>
      <child>torerod</child>
    </Item>
    <Item>
      <parent>vehicles_dom_interior</parent>
      <child>turismoc</child>
    </Item>
    <Item>
      <parent>vehicles_dom_interior</parent>
      <child>turismo2lm</child>
    </Item>
    <Item>
      <parent>vehicles_muscle_o_w_interior</parent>
      <child>vulture</child>
    </Item>
    <Item>
      <parent>vehicles_sultanrs_interior</parent>
      <child>ziongtc</child>
    </Item>
    <Item>
      <parent>vehicles_race_generic</parent>
      <child>vehicles_banshee_w_interior</child>
    </Item>
    <Item>
      <parent>vehicles_banshee_w_interior</parent>
      <child>zrgpr</child>
    </Item>
    <Item>
      <parent>vehicles_muscle_n_w_interior</parent>
      <child>domgtcoupe</child>
    </Item>
    <Item>
      <parent>vehshare</parent>
      <child>vehicles_sup2_interior</child>
    </Item>
    <Item>
      <parent>vehicles_worn_race</parent>
      <child>vehicles_race_interior</child>
    </Item>
    <Item>
      <parent>vehicles_race_interior</parent>
      <child>vehicles_race_generic</child>
    </Item>
    <Item>
      <parent>vehicles_race_generic</parent>
      <child>vehicles_comet_interior</child>
    </Item>
    <Item>
      <parent>vehicles_race_generic</parent>
      <child>vehicles_banshee_interior</child>
    </Item>
    <Item>
      <parent>vehicles_sup2_interior</parent>
      <child>draftgpr</child>
    </Item>
    <Item>
      <parent>vehicles_elegy_race</parent>
      <child>elegyr</child>
    </Item>
    <Item>
      <parent>vehicles_race_generic</parent>
      <child>vehicles_elegy_race</child>
    </Item>
    <Item>
      <parent>vehicles_elegy_race</parent>
      <child>elegyheritage</child>
    </Item>
    <Item>
      <parent>elegyheritage</parent>
      <child>elegyrace</child>
    </Item>
    <Item>
      <parent>vehicles_race_generic</parent>
      <child>vehicles_elegy_race</child>
    </Item>
    <Item>
      <parent>vehicles_elegy_race</parent>
      <child>elegyute</child>
    </Item>
  </txdRelationships>
</CVehicleModelInfo__InitDataList>