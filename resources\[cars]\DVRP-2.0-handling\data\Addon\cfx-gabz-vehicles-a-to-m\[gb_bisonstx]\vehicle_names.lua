Citizen.CreateThread(function()
AddTextEntry("BISONSTX", "Bison STX")
AddTextEntry("BISONSTX_BBAR1", "Chrome Bullbar")
AddTextEntry("BISONSTX_BBAR2", "Pushbar")
AddTextEntry("BISONSTX_BBAR2A", "Pushbar w/ Guards")
AddTextEntry("BISONSTX_BBAR3", "Alt. Pushbar")
AddTextEntry("BISONSTX_BBAR3A", "Alt. Pushbar w/ Guards")
AddTextEntry("BISONSTX_BED1", "Chrome Rollbar")
AddTextEntry("BISONSTX_BED1A", "Chrome Rollbar w/ Lights")
AddTextEntry("BISONSTX_BED2", "Black Rollbar")
AddTextEntry("BISONSTX_BED2A", "Black Rollbar w/ Lights")
AddTextEntry("BISONSTX_BED3", "Soft Bed Cover")
AddTextEntry("BISONSTX_BED4", "Sport Bar")
AddTextEntry("BISONSTX_BED4C1", "Race Setup")
AddTextEntry("BISONSTX_BED4C2", "Primary Race Setup")
AddTextEntry("BISONSTX_BED4C3", "Secondary Race")
AddTextEntry("BISONSTX_BED4A", "Low Level Spoiler")
AddTextEntry("BISONSTX_BED4AC1", "Low Level Race Setup")
AddTextEntry("BISONSTX_BED4AC2", "Primary Low Level Race Setup")
AddTextEntry("BISONSTX_BED4AC3", "Secondary Low Level Race Setup")
AddTextEntry("BISONSTX_BED4B", "Mid Level Spoiler")
AddTextEntry("BISONSTX_BED4BC1", "Mid Level Race Setup")
AddTextEntry("BISONSTX_BED4BC2", "Primary Mid Level Race Setup")
AddTextEntry("BISONSTX_BED6", "Mid Level Spoiler w/o Sportbar")
AddTextEntry("BISONSTX_BED4BC3", "Secondary Mid Level Race Setup")
AddTextEntry("BISONSTX_BED4C", "Super Spoiler")
AddTextEntry("BISONSTX_BED4CC1", "Super Race Setup")
AddTextEntry("BISONSTX_BED4CC2", "Primary Super Race Setup")
AddTextEntry("BISONSTX_BED4CC3", "Secondary Super Race Setup")
AddTextEntry("BISONSTX_BED5", "Hard Bed Cover")
AddTextEntry("BISONSTX_BED5A", "Cover w/ Low Level Spoiler")
AddTextEntry("BISONSTX_BED5B", "Cover w/ Super Spoiler")
AddTextEntry("BISONSTX_BED5C", "Cover w/ STX Spoiler")
AddTextEntry("BISONSTX_BED5D", "Cover w/ Lipped STX Spoiler")
AddTextEntry("BISONSTX_BED5E", "Cover w/ Drift Wing")
AddTextEntry("BISONSTX_BED5F", "Cover w/ Track Wing")
AddTextEntry("BISONSTX_BED5G", "Cover w/ Time Attack Wing")
AddTextEntry("BISONSTX_BED5H", "Cover w/ Drag Spoiler")
AddTextEntry("BISONSTX_BUMF0S1", "Plastic Lip")
AddTextEntry("BISONSTX_BUMF0S2", "Vented Lip")
AddTextEntry("BISONSTX_BUMF0S3", "Extended Lip")
AddTextEntry("BISONSTX_BUMF0A", "Plastic Bumper")
AddTextEntry("BISONSTX_BUMF0AS1", "Plastic w/ Lip")
AddTextEntry("BISONSTX_BUMF0AS2", "Plastic w/ Vented Lip")
AddTextEntry("BISONSTX_BUMF0AS3", "Plastic w/ Extended Lip")
AddTextEntry("BISONSTX_BUMF0B", "Premium Bumper")
AddTextEntry("BISONSTX_BUMF0BS1", "Premium w/ Lip")
AddTextEntry("BISONSTX_BUMF0BS2", "Premium w/ Vented Lip")
AddTextEntry("BISONSTX_BUMF0BS3", "Premium w/ Extended Lip")
AddTextEntry("BISONSTX_BUMF1", "Painted Bumper")
AddTextEntry("BISONSTX_BUMF1S1", "Painted w/ Lip")
AddTextEntry("BISONSTX_BUMF1S2", "Painted w/ Vented Lip")
AddTextEntry("BISONSTX_BUMF1S3", "Painted w/ Extended Lip")
AddTextEntry("BISONSTX_BUMF2", "Facelift Bumper")
AddTextEntry("BISONSTX_BUMF2A", "Plastic Facelift Bumper")
AddTextEntry("BISONSTX_BUMF2B", "Premium Facelift Bumper")
AddTextEntry("BISONSTX_BUMF3", "S-Spec Bumper")
AddTextEntry("BISONSTX_BUMF3A", "S-Spec w/ Splitter")
AddTextEntry("BISONSTX_BUMF3B", "S-Spec w/ Extended Splitter")
AddTextEntry("BISONSTX_BUMF4", "Modernized Bumper")
AddTextEntry("BISONSTX_BUMF4A", "Modernized w/ Splitter")
AddTextEntry("BISONSTX_BUMF4B", "Modernized w/ Extended Splitter")
AddTextEntry("BISONSTX_BUMF5", "Sport Bumper")
AddTextEntry("BISONSTX_BUMF6", "STX Bumper")
AddTextEntry("BISONSTX_BUMF7", "Race Bumper")
AddTextEntry("BISONSTX_BUMR0A", "Plastic Bumper")
AddTextEntry("BISONSTX_BUMR0B", "Painted Bumper")
AddTextEntry("BISONSTX_BUMR0BS1", "Extended Bumper")
AddTextEntry("BISONSTX_BUMR1", "Roll Pan")
AddTextEntry("BISONSTX_BUMR2", "STX Bumper")
AddTextEntry("BISONSTX_BUMR2A", "STX Bumper w/ Wheelie Bar")
AddTextEntry("BISONSTX_BUMR2B", "Extended STX Bumper")
AddTextEntry("BISONSTX_BUMR2C", "Ext. STX Bumper w/ Wheelie Bar")
AddTextEntry("BISONSTX_SKIRT1", "Side Step")
AddTextEntry("BISONSTX_SKIRT1A", "Chrome Side Step")
AddTextEntry("BISONSTX_SKIRT2", "Side Skirt")
AddTextEntry("BISONSTX_SKIRT2A", "Side Skirt w/ Step")
AddTextEntry("BISONSTX_SKIRT2B", "Side Skirt w/ Chrome Step")
AddTextEntry("BISONSTX_SKIRT3", "Extended Skirt")
AddTextEntry("BISONSTX_CLAD1", "STX Cladding")
AddTextEntry("BISONSTX_CLAD2", "Extended STX Cladding")
AddTextEntry("BISONSTX_CLAD3", "STX Cladding w/ Step")
AddTextEntry("BISONSTX_EXH1", "Recessed Quad Exit Exhaust")
AddTextEntry("BISONSTX_EXH1A", "Quad Exit Exhaust")
AddTextEntry("BISONSTX_EXH2", "Recessed Wide Exhaust")
AddTextEntry("BISONSTX_EXH2A", "Wide Exhaust")
AddTextEntry("BISONSTX_EXH3", "Square Exhaust")
AddTextEntry("BISONSTX_EXH4", "Big Bore Exhaust")
AddTextEntry("BISONSTX_EXH4A", "Titanium Big Bore Exhaust")
AddTextEntry("BISONSTX_EXH4B", "Black Big Bore Exhaust")
AddTextEntry("BISONSTX_GRILL0A", "Stock Grille w/ Slats")
AddTextEntry("BISONSTX_GRILL0B", "Chrome Bar Grill")
AddTextEntry("BISONSTX_GRILL0C", "Chrome Bar Grill w/ Slats")
AddTextEntry("BISONSTX_GRILL0D", "Painted Bar Grill")
AddTextEntry("BISONSTX_GRILL0E", "Painted Bar Grill w/ Slats")
AddTextEntry("BISONSTX_GRILL1", "Signature Grill")
AddTextEntry("BISONSTX_GRILL1A", "Chrome Signature Grill")
AddTextEntry("BISONSTX_GRILL2", "Vertical Bar Grill")
AddTextEntry("BISONSTX_GRILL2A", "Cross Grill")
AddTextEntry("BISONSTX_GRILL2B", "Cross Grill w/ Slats")
AddTextEntry("BISONSTX_GRILL2C", "Plastic Vertical Bar Grill")
AddTextEntry("BISONSTX_GRILL2D", "Plastic Cross Grill")
AddTextEntry("BISONSTX_GRILL2E", "Plastic Cross Grill w/ Slats")
AddTextEntry("BISONSTX_GRILL2F", "Combo Cross Grill w/ Slats")
AddTextEntry("BISONSTX_GRILL3", "Triple Bar Grill")
AddTextEntry("BISONSTX_GRILL3A", "Triple Bar Grill w/ Big Badge")
AddTextEntry("BISONSTX_GRILL4", "Billet Grill")
AddTextEntry("BISONSTX_GRILL4A", "Billet Grill w/ Big Badge")
AddTextEntry("BISONSTX_GRILL5", "Vertical Slat Grill")
AddTextEntry("BISONSTX_GRILL5A", "Vertical Slat Grill w/ Big Badge")
AddTextEntry("BISONSTX_GRILL6", "Custom Grill")
AddTextEntry("BISONSTX_GRILL7", "S-Spec Grill")
AddTextEntry("BISONSTX_GRILL7A", "S-Spec Grill w/ Bar")
AddTextEntry("BISONSTX_GRILL7B", "Chrome S-Spec Grill")
AddTextEntry("BISONSTX_GRILL7C", "Chrome S-Spec Grill w/ Bar")
AddTextEntry("BISONSTX_HOOD1", "Power Bulge Hood")
AddTextEntry("BISONSTX_HOOD1A", "Scooped Hood")
AddTextEntry("BISONSTX_HOOD1B", "Ram Air Hood")
AddTextEntry("BISONSTX_HOOD2", "Muscle Hood")
AddTextEntry("BISONSTX_HOOD2A", "Vented Muscle Hood")
AddTextEntry("BISONSTX_HOOD2B", "Carbon Muscle Hood")
AddTextEntry("BISONSTX_HOOD3", "Sport Hood")
AddTextEntry("BISONSTX_HOOD3A", "Vented Sport Hood")
AddTextEntry("BISONSTX_HOOD3B", "Carbon Sport Hood")
AddTextEntry("BISONSTX_HOOD4", "S-Spec Hood")
AddTextEntry("BISONSTX_HOOD4A", "Vented S-Spec Hood")
AddTextEntry("BISONSTX_HOOD5", "Street Hood")
AddTextEntry("BISONSTX_HOOD5A", "Scooped Street Hood")
AddTextEntry("BISONSTX_HOOD5B", "Carbon Street Hood")
AddTextEntry("BISONSTX_HOOD6", "Cowl Induction Hood")
AddTextEntry("BISONSTX_HOOD6A", "Vented Cowl Induction Hood")
AddTextEntry("BISONSTX_HOOD6B", "Black Vented Cowl Induction Hood")
AddTextEntry("BISONSTX_HOOD6C", "Carbon Vented Cowl Induction Hood")
AddTextEntry("BISONSTX_HOOD7", "Drag Scoop Hood")
AddTextEntry("BISONSTX_GFRAME1", "Painted Grille Surround")
AddTextEntry("BISONSTX_GFRAME2", "Painted + Plastic Grille Surround")
AddTextEntry("BISONSTX_GFRAME3", "Chrome Grille Surround")
AddTextEntry("BISONSTX_GFRAME4", "Chrome + Plastic Grille Surround")
AddTextEntry("BISONSTX_MIR1", "Secondary Mirror")
AddTextEntry("BISONSTX_MIR2", "Chrome Mirror")
AddTextEntry("BISONSTX_MIR3", "Carbon Mirror")
AddTextEntry("BISONSTX_ROOF0A", "Marker Lights")
AddTextEntry("BISONSTX_ROOF0B", "Roof Scoop")
AddTextEntry("BISONSTX_ROOF0C", "Race Scoop")
AddTextEntry("BISONSTX_ROOF1", "Secondary Roof")
AddTextEntry("BISONSTX_ROOF1A", "Secondary w/ Markers")
AddTextEntry("BISONSTX_ROOF1B", "Secondary Roof Scoop")
AddTextEntry("BISONSTX_ROOF1C", "Secondary Race Scoop")
AddTextEntry("BISONSTX_ROOF2", "Carbon Roof")
AddTextEntry("BISONSTX_ROOF2A", "Carbon Roof Scoop")
AddTextEntry("BISONSTX_ROOF2B", "Carbon Race Scoop")
AddTextEntry("BISONSTX_SEAT1", "S-Spec Seats")
AddTextEntry("BISONSTX_ENG1", "STX Performance Package")
AddTextEntry("BISONSTX_RTRIM1", "Painted Taillight Trim")
AddTextEntry("BISONSTX_RTRIM2", "Chrome Taillight Trim")
AddTextEntry("BISONSTX_AERIAL1", "Offset Antenna")
AddTextEntry("BISONSTX_AERIAL2", "Sharkfin Antenna")
AddTextEntry("BISONSTX_AERIAL3", "Secondary Sharkfin Antenna")
AddTextEntry("BISONSTX_AERIAL4", "Carbon Sharkfin Antenna")
AddTextEntry("BISONSTX_STRIP2", "Primary Sunstrip")
AddTextEntry("BISONSTX_STRIP3", "Secondary Sunstrip")
AddTextEntry("BISONSTX_FCAP", "Chrome Fuel Cap")
AddTextEntry("TOP_STXBED", "Bed Accessory")
AddTextEntry("TOP_STXGRI", "Grill Surround")
AddTextEntry("TOP_STXPP", "Performance Package")
AddTextEntry("TOP_STXRT", "Taillight Trim")
AddTextEntry("TOP_STXCAP", "Fuel Cap")
AddTextEntry("TOP_STXPIN", "Hood Pins")

AddTextEntry("BISONSTX_LIV1", "Dual Stripes Black")
AddTextEntry("BISONSTX_LIV2", "Dual Stripes Blue")
AddTextEntry("BISONSTX_LIV3", "Dual Stripes Grey")
AddTextEntry("BISONSTX_LIV4", "Dual Stripes Green")
AddTextEntry("BISONSTX_LIV5", "Dual Stripes Orange")
AddTextEntry("BISONSTX_LIV6", "Dual Stripes Purple")
AddTextEntry("BISONSTX_LIV7", "Dual Stripes Red")
AddTextEntry("BISONSTX_LIV8", "Dual Stripes White")
AddTextEntry("BISONSTX_LIV9", "Dual Stripes Yellow")
AddTextEntry("BISONSTX_LIV10", "Bravado Stripes Black")
AddTextEntry("BISONSTX_LIV11", "Bravado Stripes Blue")
AddTextEntry("BISONSTX_LIV12", "Bravado Stripes Gray")
AddTextEntry("BISONSTX_LIV13", "Bravado Stripes Green")
AddTextEntry("BISONSTX_LIV14", "Bravado Stripes Orange")
AddTextEntry("BISONSTX_LIV15", "Bravado Stripes Purple")
AddTextEntry("BISONSTX_LIV16", "Bravado Stripes Red")
AddTextEntry("BISONSTX_LIV17", "Bravado Stripes White")
AddTextEntry("BISONSTX_LIV18", "Bravado Stripes Yellow")
AddTextEntry("BISONSTX_LIV19", "Jonny Shapiro Gas Specialists")
AddTextEntry("BISONSTX_LIV20", "The Mighty Bush Landscaping")
AddTextEntry("BISONSTX_LIV21", "Schlott Construction")
AddTextEntry("BISONSTX_LIV22", "STD Contractors")
AddTextEntry("BISONSTX_LIV23", "You Tool")
end)