<?xml version="1.0" encoding="UTF-8"?>

<CVehicleModelInfoVariation>
  <variationData>
    <Item>
      <modelName>gbbriosof</modelName>
      <colors>
        <Item>
          <indices content="char_array">
            111
            111
            111
            156
            111
            134
          </indices>
          <liveries>
          </liveries>
        </Item>
        <Item>
          <indices content="char_array">
            1
            1
            1
            156
            8
            134
          </indices>
          <liveries>
          </liveries>
        </Item>
        <Item>
          <indices content="char_array">
            1
            27
            1
            156
            8
            134
          </indices>
          <liveries>
          </liveries>
        </Item>
        <Item>
          <indices content="char_array">
            27
            27
            27
            156
            8
            134
          </indices>
          <liveries>
          </liveries>
        </Item>
        <Item>
          <indices content="char_array">
            27
            112
            1
            156
            106
            134
          </indices>
          <liveries>
          </liveries>
        </Item>
        <Item>
          <indices content="char_array">
            67
            112
            111
            156
            67
            134
          </indices>
          <liveries>
          </liveries>
        </Item>
        <Item>
          <indices content="char_array">
            3
            3
            3
            156
            8
            134
          </indices>
          <liveries>
          </liveries>
        </Item>
        <Item>
          <indices content="char_array">
            157
            157
            157
            156
            157
            134
          </indices>
          <liveries>
          </liveries>
        </Item>
        <Item>
          <indices content="char_array">
            64
            64
            92
            156
            8
            134
          </indices>
          <liveries>
          </liveries>
        </Item>
        <Item>
          <indices content="char_array">
            136
            136
            136
            156
            111
            134
          </indices>
          <liveries>
          </liveries>
        </Item>
        <Item>
          <indices content="char_array">
            74
            74
            136
            156
            8
            134
          </indices>
          <liveries>
          </liveries>
        </Item>
        <Item>
          <indices content="char_array">
            112
            107
            4
            156
            107
            134
          </indices>
          <liveries>
          </liveries>
        </Item>
        <Item>
          <indices content="char_array">
            112
            4
            4
            156
            107
            134
          </indices>
          <liveries>
          </liveries>
        </Item>
        <Item>
          <indices content="char_array">
            112
            54
            4
            156
            107
            134
          </indices>
          <liveries>
          </liveries>
        </Item>
        <Item>
          <indices content="char_array">
            34
            3
            4
            156
            107
            134
          </indices>
          <liveries>
          </liveries>
        </Item>
        <Item>
          <indices content="char_array">
            5
            138
            4
            156
            6
            134
          </indices>
          <liveries>
          </liveries>
        </Item>
        <Item>
          <indices content="char_array">
            4
            1
            4
            156
            107
            134
          </indices>
          <liveries>
          </liveries>
        </Item>
      </colors>
      <kits>
        <Item>1698_gbbriosof_modkit</Item>
      </kits>
      <windowsWithExposedEdges/>
      <plateProbabilities>
        <Probabilities>
          <Item>
            <Name>Standard White</Name>
            <Value value="100"/>
          </Item>
        </Probabilities>
      </plateProbabilities>
      <lightSettings value="1"/>
      <sirenSettings value="0"/>
    </Item>
  </variationData>
</CVehicleModelInfoVariation>