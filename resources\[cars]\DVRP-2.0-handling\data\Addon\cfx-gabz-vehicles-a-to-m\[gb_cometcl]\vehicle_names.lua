function AddTextEntry(key, value)
Citizen.InvokeNative(GetHash<PERSON>ey("ADD_TEXT_ENTRY"), key, value)
end

Citizen.CreateThread(function()
AddTextEntry('COMETCL', 'Comet Classic')
AddTextEntry('CC_W1', 'Spoiler Decklid')
AddTextEntry('TOP_DECKLID', 'Decklid')
AddTextEntry('TOP_RTRIM', 'Rear Trim')
AddTextEntry('TOP_BADG', 'Badging')
AddTextEntry('TOP_HLH', 'Light Housing')
AddTextEntry('CC_F1', 'Vented Front Bumper')
AddTextEntry('CC_F2', 'Sport Front Bumper')
AddTextEntry('CC_F3', 'Vented Sport Front Bumper')
AddTextEntry('CC_F4', 'Race Front Bumper')
AddTextEntry('CC_F5', 'Vented Race Front Bumper')
AddTextEntry('CC_SD', 'Remove Trim')
AddTextEntry('CC_S1', 'Sport Trim')
AddTextEntry('CC_S2', 'Chrome Trim')
AddTextEntry('CC_S3', 'Painted Trim')
AddTextEntry('CC_S3A', 'Painted Trim w/o Overriders')
AddTextEntry('CC_S4', 'Partial Plastic Trim')
AddTextEntry('CC_S4A', 'Partial Plastic Trim w/o Overriders')
AddTextEntry('CC_S5', 'Partial Chrome Trim')
AddTextEntry('CC_S5A', 'Partial Chrome Trim w/o Overriders')
AddTextEntry('CC_VT', 'Side Vents')
AddTextEntry('CC_GR1', 'Triple Slat Grille')
AddTextEntry('CC_GR2', 'Plastic Grille')
AddTextEntry('CC_GR3', 'Plastic Triple Slat Grille')
AddTextEntry('CC_GR4', 'Painted Grille')
AddTextEntry('CC_GR5', 'Painted Triple Slat Grille')
AddTextEntry('CC_H1', 'Sport Hood')
AddTextEntry('CC_H2', 'Performance Hood')
AddTextEntry('CC_HD', 'Remove Light Cover')
AddTextEntry('CC_SL1', 'X Taped Lights')
AddTextEntry('CC_SL2', '+ Taped Lights')
AddTextEntry('CC_SN', 'Sunroof')
AddTextEntry('CC_SR', 'Painted Rear Trim')
AddTextEntry('CC_DF', 'Front Debadge')
AddTextEntry('CC_DR', 'Rear Debadge')
AddTextEntry('CC_DFR', 'Front & Rear Debadge')
AddTextEntry('CC_HT2', 'Painted Light Housing')

AddTextEntry('COMETCL_LIV1', 'Comet Graphics Black')
AddTextEntry('COMETCL_LIV2', 'Comet Graphics Darken')
AddTextEntry('COMETCL_LIV3', 'Comet Graphics White')
AddTextEntry('COMETCL_LIV4', 'Comet Graphics Lighten')
AddTextEntry('COMETCL_LIV5', 'Comet Graphics Silver')
AddTextEntry('COMETCL_LIV6', 'Comet Graphics Red')
AddTextEntry('COMETCL_LIV7', 'Comet Graphics Blue')
AddTextEntry('COMETCL_LIV8', 'Comet Graphics Green')
AddTextEntry('COMETCL_LIV9', 'Comet Graphics Yellow')
AddTextEntry('COMETCL_LIV10', 'Comet Graphics Gold')
AddTextEntry('COMETCL_LIV11', 'Pfister Stripes Black')
AddTextEntry('COMETCL_LIV12', 'Pfister Stripes Darken')
AddTextEntry('COMETCL_LIV13', 'Pfister Stripes White')
AddTextEntry('COMETCL_LIV14', 'Pfister Stripes Lighten')
AddTextEntry('COMETCL_LIV15', 'Pfister Stripes Silver')
AddTextEntry('COMETCL_LIV16', 'Pfister Stripes Red')
AddTextEntry('COMETCL_LIV17', 'Pfister Stripes Blue')
AddTextEntry('COMETCL_LIV18', 'Pfister Stripes Green')
AddTextEntry('COMETCL_LIV19', 'Pfister Stripes Yellow')
AddTextEntry('COMETCL_LIV20', 'Pfister Stripes Gold')
AddTextEntry('COMETCL_LIV21', 'Globe Oil')
AddTextEntry('COMETCL_LIV22', 'Modern Desperado')
end)