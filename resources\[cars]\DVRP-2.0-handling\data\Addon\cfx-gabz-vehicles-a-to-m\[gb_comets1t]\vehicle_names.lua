function AddTextEntry(key, value)
Citizen.InvokeNative(GetHash<PERSON>ey("ADD_TEXT_ENTRY"), key, value)
end

Citizen.CreateThread(function()
AddTextEntry('COMETS1T', 'Comet S1 Turbo')
AddTextEntry('COMS1T_SPL0', 'Remove Spoiler')
AddTextEntry('COMS1T_SPL1', 'Spoiler Decklid')
AddTextEntry('COMS1T_SPL2', 'SR Spoiler')
AddTextEntry('COMS1T_SPL3', 'RBS Performance Spoiler')
AddTextEntry('COMS1T_SPL4', 'Custom Spoiler')
AddTextEntry('COMS1T_SPL4A', 'Custom Spoiler MK.1')
AddTextEntry('COMS1T_SPL4B', 'Custom Spoiler MK.2')
AddTextEntry('COMS1T_SPL5', 'Ruff-Weld Spoiler')
AddTextEntry('COMS1T_SPL5A', 'Ruff-Weld Spoiler MK.1')
AddTextEntry('COMS1T_SPL5B', 'Ruff-Weld Spoiler MK.2')
AddTextEntry('COMS1T_SPL5C', 'Ruff-Weld Spoiler MK.3')
AddTextEntry('COMS1T_SKIRT1', 'SR Skirts')
AddTextEntry('COMS1T_SKIRT2', 'Facelift Skirts')
AddTextEntry('COMS1T_SKIRT2A', 'Painted Facelift Skirts')
AddTextEntry('COMS1T_SKIRT3', 'RBS Performance Skirts')
AddTextEntry('COMS1T_SKIRT4', 'Bay Shore Skirts')
AddTextEntry('COMS1T_SKIRT5', 'Ruff-Weld Skirts')
AddTextEntry('COMS1T_BUMR0C', 'Stock w/ Custom Exhausts')
AddTextEntry('COMS1T_BUMR0E', 'EU Bumper')
AddTextEntry('COMS1T_BUMR0EC', 'EU w/ Custom Exhausts')
AddTextEntry('COMS1T_BUMR1', 'Painted Bumper')
AddTextEntry('COMS1T_BUMR1E', 'EU Painted Bumper')
AddTextEntry('COMS1T_BUMR1C', 'Painted Bumper w/ Custom Exhausts')
AddTextEntry('COMS1T_BUMR1EC', 'EU Painted Bumper w/ Custom Exhausts')
AddTextEntry('COMS1T_BUMR1A', 'Safari Bumper')
AddTextEntry('COMS1T_BUMR1AE', 'EU Safari Bumper')
AddTextEntry('COMS1T_BUMR1AC', 'Safari Bumper w/ Custom Exhausts')
AddTextEntry('COMS1T_BUMR1AEC', 'EU Safari Bumper w/ Custom Exhausts')
AddTextEntry('COMS1T_BUMR2', 'Facelift Bumper')
AddTextEntry('COMS1T_BUMR2E', 'EU Facelift Bumper')
AddTextEntry('COMS1T_BUMR2A', 'Painted Facelift Bumper')
AddTextEntry('COMS1T_BUMR2AE', 'EU Painted Facelift Bumper')
AddTextEntry('COMS1T_BUMR3', 'Ruff-Weld Bumper')
AddTextEntry('COMS1T_BUMR3E', 'EU Ruff-Weld Bumper')
AddTextEntry('COMS1T_BUMR3C', 'Ruff-Weld Bumper w/ Custom Exhausts')
AddTextEntry('COMS1T_BUMR3EC', 'EU Ruff-Weld Bumper w/ Custom Exhausts')
AddTextEntry('COMS1T_BUMR4', 'X-Flow Bay Shore Bumper')
AddTextEntry('COMS1T_BUMR5', 'Alien Styling Bumper')
AddTextEntry('COMS1T_BUMR5E', 'EU Alien Styling Bumper')
AddTextEntry('COMS1T_BUMR6', 'Exposed Twin Turbo Setup')
AddTextEntry('COMS1T_FENDF0A', 'Plastic Front Trim')
AddTextEntry('COMS1T_FENDF1', 'Ruff-Weld Fender Flares')
AddTextEntry('COMS1T_FENDF1A', 'Ruff-Weld w/ Plastic Trim')
AddTextEntry('COMS1T_FENDF2', 'Slant Nose')
AddTextEntry('COMS1T_FENDF2A', 'Slant Nose w/ Fixed Lights')
AddTextEntry('COMS1T_FENDF2B', 'Slant Nose w/ Painted Fixed Lights')
AddTextEntry('COMS1T_FENDF3', 'Vented Slant Nose')
AddTextEntry('COMS1T_FENDF3A', 'Vented Slant Nose w/ Fixed Lights')
AddTextEntry('COMS1T_FENDF3B', 'Vented Slant Nose w/ Painted Fixed Lights')
AddTextEntry('COMS1T_FENDR0A', 'Remove Rock Guards')
AddTextEntry('COMS1T_FENDR1', 'SR Side Vents')
AddTextEntry('COMS1T_FENDR1A', 'Slatted SR Side Vents')
AddTextEntry('COMS1T_FENDR2', 'Ruff-Weld Fender Flares')
AddTextEntry('COMS1T_FENDR2A', 'Ruff-Weld MK.2 Fender Flares')
AddTextEntry('TOP_BADG', 'Badging')
AddTextEntry('TOP_ROOFACC', 'Roof Accessories')
AddTextEntry('COMS1T_VENT', 'Side Vents')
AddTextEntry('COMS1T_HOOD1', 'Sport Hood')
AddTextEntry('COMS1T_HOOD2', 'Performance Hood')
AddTextEntry('COMS1T_HOOD3', 'BayShore Hood')
AddTextEntry('COMS1T_SUNROOF', 'Sunroof')
AddTextEntry('COMS1T_DEBADGE1', 'Front Debadge')
AddTextEntry('COMS1T_DEBADGE2', 'Rear Debadge')
AddTextEntry('COMS1T_DEBADGE3', 'Front & Rear Debadge')
AddTextEntry('COMS1T_CAGE1', 'Half Cage')
AddTextEntry('COMS1T_CAGE2', 'Street Cage')
AddTextEntry('COMS1T_CAGE3', 'Full Cage')
AddTextEntry('COMS1T_MIR1', 'Dual Mirrors')
AddTextEntry('COMS1T_MIR2', 'Aero Mirrors')
AddTextEntry('COMS1T_FLAPS', 'Mudflaps')
AddTextEntry('COMS1T_FOGL1', 'Dual Foglights')
AddTextEntry('COMS1T_FOGL2', 'Rally Foglights')
AddTextEntry('COMS1T_STRIP1', 'Sunstrip')
AddTextEntry('COMS1T_STRIP2', 'Extended Sunstrip')
AddTextEntry('COMS1T_DUCT', 'Side Duct')
AddTextEntry('COMS1T_ROOFACC1', 'Rally Antenna')
AddTextEntry('COMS1T_ROOFACC2', 'Roof Rack w/ Spare')
AddTextEntry('COMS1T_ROOFACC3', 'Safari Setup')
AddTextEntry('COMS1T_BUMF0E', 'Stock Bumper w/ EU Plate')
AddTextEntry('COMS1T_BUMF0R', 'Stock Bumper w/o Plate')
AddTextEntry('COMS1T_BUMF0F', 'Stock Bumper w/ Foglights')
AddTextEntry('COMS1T_BUMF0EF', 'Stock Bumper w/ EU Plate & Fogs')
AddTextEntry('COMS1T_BUMF0RF', 'Stock Bumper w/o Plate & Fogs')
AddTextEntry('COMS1T_BUMF0A', 'Painted Bumper')
AddTextEntry('COMS1T_BUMF0AE', 'Painted Bumper w/ EU Plate')
AddTextEntry('COMS1T_BUMF0AR', 'Painted Bumper w/o Plate')
AddTextEntry('COMS1T_BUMF0AF', 'Painted Bumper w/ Foglights')
AddTextEntry('COMS1T_BUMF0AEF', 'Painted Bumper w/ EU Plate & Fogs')
AddTextEntry('COMS1T_BUMF0ARF', 'Painted Bumper w/o Plate & Fogs')
AddTextEntry('COMS1T_BUMF1', 'SR Bumper')
AddTextEntry('COMS1T_BUMF1E', 'SR Bumper w/ EU Plate')
AddTextEntry('COMS1T_BUMF1R', 'SR Bumper w/o Plate')
AddTextEntry('COMS1T_BUMF2', 'Facelift Bumper')
AddTextEntry('COMS1T_BUMF2E', 'Facelift Bumper w/ EU Plate')
AddTextEntry('COMS1T_BUMF2R', 'Facelift Bumper w/o Plate')
AddTextEntry('COMS1T_BUMF2A', 'Painted Facelift Bumper')
AddTextEntry('COMS1T_BUMF2AE', 'Painted Facelift Bumper w/ EU Plate')
AddTextEntry('COMS1T_BUMF2AR', 'Painted Facelift Bumper w/o Plate')
AddTextEntry('COMS1T_BUMF2B', 'Vented Facelift Bumper')
AddTextEntry('COMS1T_BUMF2BE', 'Vented Facelift Bumper w/ EU Plate')
AddTextEntry('COMS1T_BUMF2BR', 'Vented Facelift Bumper w/o Plate')
AddTextEntry('COMS1T_BUMF2C', 'Painted Vented Facelift Bumper')
AddTextEntry('COMS1T_BUMF2CE', 'Painted Vented Facelift Bumper w/ EU Plate')
AddTextEntry('COMS1T_BUMF2CR', 'Painted Vented Facelift Bumper w/o Plate')
AddTextEntry('COMS1T_BUMF3', 'Extended Facelift Bumper')
AddTextEntry('COMS1T_BUMF3E', 'Extended Facelift Bumper w/ EU Plate')
AddTextEntry('COMS1T_BUMF3R', 'Extended Facelift Bumper w/o Plate')
AddTextEntry('COMS1T_BUMF3A', 'Extended Painted Facelift Bumper')
AddTextEntry('COMS1T_BUMF3AE', 'Extended Painted Facelift Bumper w/ EU Plate')
AddTextEntry('COMS1T_BUMF3AR', 'Extended Painted Facelift Bumper w/o Plate')
AddTextEntry('COMS1T_BUMF3B', 'Extended Vented Facelift Bumper')
AddTextEntry('COMS1T_BUMF3BE', 'Extended Vented Facelift Bumper w/ EU Plate')
AddTextEntry('COMS1T_BUMF3BR', 'Extended Vented Facelift Bumper w/o Plate')
AddTextEntry('COMS1T_BUMF3C', 'Extended Painted Vented Facelift Bumper')
AddTextEntry('COMS1T_BUMF3CE', 'Extended Painted Vented Facelift Bumper w/ EU Plate')
AddTextEntry('COMS1T_BUMF3CR', 'Extended Painted Vented Facelift Bumper w/o Plate')
AddTextEntry('COMS1T_BUMF4', 'Circular Vented Bumper')
AddTextEntry('COMS1T_BUMF4E', 'Circular Vented Bumper w/ EU Plate')
AddTextEntry('COMS1T_BUMF4R', 'Circular Vented Bumper w/o Plate')
AddTextEntry('COMS1T_BUMF5', 'RBS Performance MK.1 Bumper')
AddTextEntry('COMS1T_BUMF5E', 'RBS Performance MK.1 Bumper w/ EU Plate')
AddTextEntry('COMS1T_BUMF5R', 'RBS Performance MK.1 Bumper w/o Plate')
AddTextEntry('COMS1T_BUMF6', 'RBS Performance MK.2 Bumper')
AddTextEntry('COMS1T_BUMF6E', 'RBS Performance MK.2 Bumper w/ EU Plate')
AddTextEntry('COMS1T_BUMF6R', 'RBS Performance MK.2 Bumper w/o Plate')
AddTextEntry('COMS1T_BUMF7', 'Race Bumper')
AddTextEntry('COMS1T_BUMF7E', 'Race Bumper w/ EU Plate')
AddTextEntry('COMS1T_BUMF7R', 'Race Bumper w/o Plate')
AddTextEntry('COMS1T_BUMF8', 'Ruff-Weld Bumper')
AddTextEntry('COMS1T_BUMF8E', 'Ruff-Weld Bumper w/ EU Plate')
AddTextEntry('COMS1T_BUMF8R', 'Ruff-Weld Bumper w/o Plate')
AddTextEntry('COMS1T_BUMF8A', 'Ruff-Weld MK.2 Bumper')
AddTextEntry('COMS1T_BUMF8AE', 'Ruff-Weld MK.2 Bumper w/ EU Plate')
AddTextEntry('COMS1T_BUMF8AR', 'Ruff-Weld MK.2 Bumper w/o Plate')
AddTextEntry('COMS1T_BUMF9', 'Painted Safari Bumper')
AddTextEntry('COMS1T_BUMF9A', 'Safari Bumper')
AddTextEntry('COMS1T_BUMF10', 'Bayshore X-Flow Bumper')
AddTextEntry('COMS1T_BUMF11', 'Alien Styling Bumper')
AddTextEntry('COMS1T_BUMF12', 'RBS EVO MK.1 Bumper')
AddTextEntry('COMS1T_BUMF12E', 'RBS EVO MK.1 Bumper w/ EU Plate')
AddTextEntry('COMS1T_BUMF12R', 'RBS EVO MK.1 Bumper w/o Plate')
AddTextEntry('COMS1T_BUMF13', 'RBS EVO MK.2 Bumper')
AddTextEntry('COMS1T_DIFF1', 'Rear Diffuser')
AddTextEntry('COMS1T_DIFF2', 'Ruff-Weld Rear Diffuser')

AddTextEntry('COMS1T_LIV1', 'Comet Graphics White')
AddTextEntry('COMS1T_LIV2', 'Comet Graphics Lighten')
AddTextEntry('COMS1T_LIV3', 'Comet Graphics Black')
AddTextEntry('COMS1T_LIV4', 'Comet Graphics Darken')
AddTextEntry('COMS1T_LIV5', 'Comet Graphics Red')
AddTextEntry('COMS1T_LIV6', 'Comet Graphics Orange')
AddTextEntry('COMS1T_LIV7', 'Comet Graphics Blue')
AddTextEntry('COMS1T_LIV8', 'Comet Graphics Silver')
AddTextEntry('COMS1T_LIV9', 'Comet Graphics Beige')
AddTextEntry('COMS1T_LIV10', 'Pfister Stripes White')
AddTextEntry('COMS1T_LIV11', 'Pfister Stripes Black')
AddTextEntry('COMS1T_LIV12', 'Pfister Stripes Silver')
AddTextEntry('COMS1T_LIV13', 'Pfister Stripes Red')
AddTextEntry('COMS1T_LIV14', 'Pfister Stripes Orange')
AddTextEntry('COMS1T_LIV15', 'Pfister Stripes Yellow')
AddTextEntry('COMS1T_LIV16', 'Pfister Stripes Blue')
AddTextEntry('COMS1T_LIV17', 'Pfister Stripes Green')
AddTextEntry('COMS1T_LIV18', 'Pfister Stripes Beige')
AddTextEntry('COMS1T_LIV19', 'RUFF.Weld White')
AddTextEntry('COMS1T_LIV20', 'RUFF.Weld Black')
AddTextEntry('COMS1T_LIV21', 'RUFF.Weld Red')
AddTextEntry('COMS1T_LIV22', 'Bay Shore')
AddTextEntry('COMS1T_LIV23', 'Bay Shore Alt.')
AddTextEntry('COMS1T_LIV24', 'R.W Torpedo Tubes')
AddTextEntry('COMS1T_LIV25', 'R.W 69-Cazafortunas')
AddTextEntry('COMS1T_LIV27', 'Endo #92')
AddTextEntry('COMS1T_LIV28', 'Rock N Roll Legends')
AddTextEntry('COMS1T_LIV29', 'Orange Stripes')
AddTextEntry('COMS1T_LIV30', 'Coverup Stripes')
AddTextEntry('COMS1T_LIV31', 'Coverup Stripes Monochrome')
AddTextEntry('COMS1T_LIV32', 'Refract')
AddTextEntry('COMS1T_LIV33', 'Turbo Graphics Light')
AddTextEntry('COMS1T_LIV34', 'Turbo Graphics Dark')
AddTextEntry('COMS1T_LIV35', '69th Anniversary')
AddTextEntry('COMS1T_LIV36', '69th Anniversary (Alt)')
end)