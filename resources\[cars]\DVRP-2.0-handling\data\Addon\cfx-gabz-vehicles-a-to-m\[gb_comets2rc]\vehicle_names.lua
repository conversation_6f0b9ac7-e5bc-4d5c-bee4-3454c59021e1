Citizen.CreateThread(function()
    AddTextEntry("GBCOMETS2RC", "Comet S2RC")
    AddTextEntry("0x0108D5E9", "SR Beige")
    AddTextEntry("0x074EE271", "SR Green")
    AddTextEntry("0x0825EB64", "Retro-Rennen #15")
    AddTextEntry("0x1ADAB5CD", "Pfister Race Seats")
    AddTextEntry("0x1DF72D42", "Front Plate")
    AddTextEntry("0x2A58C069", "Highlighted Splitter")
    AddTextEntry("0x2C9953E6", "Highlighted Diffuser")
    AddTextEntry("0x5BB395E0", "Secondary End Plates")
    AddTextEntry("0x5C372D3A", "Carbon Roof")
    AddTextEntry("0x5EA521F7", "Chrome Exhausts")
    AddTextEntry("0x7E7E5CBB", "Track Beige")
    AddTextEntry("0x7F74D4F6", "Black Cage")
    AddTextEntry("0x8D0AF9D4", "Track Gold")
    AddTextEntry("0x9AC790A9", "Pfister Motorsport Synergy")
    AddTextEntry("0x9D1999F1", "Track Green")
    AddTextEntry("0x9F6019DE", "Pfister Stripe Red")
    AddTextEntry("0x12C7F967", "Track Black")
    AddTextEntry("0x41D37E5A", "Painted Diffuser")
    AddTextEntry("0x53E70C7B", "Carbon Exhausts")
    AddTextEntry("0x61BBA32A", "Pfister Stripe Grey")
    AddTextEntry("0x98FAD73C", "Carbon Mirrors")
    AddTextEntry("0x438E59F0", "Bumper Delete")
    AddTextEntry("0x6322B798", "Small Front Plate")
    AddTextEntry("0x8354DA7B", "SR Black")
    AddTextEntry("0x54138F98", "Wing Delete")
    AddTextEntry("0x56138BE6", "Pfister Stripe White")
    AddTextEntry("0x278833BE", "Titanium Exhausts")
    AddTextEntry("0x770260DE", "Carbon Fender Vents")
    AddTextEntry("0x6828300F", "Pfister Stripe Black")
    AddTextEntry("0x21950787", "Bumper Delete with Plate")
    AddTextEntry("0x84676747", "Black End Plates")
    AddTextEntry("0xA07194B4", "SR Silver")
    AddTextEntry("0xABFFFD46", "Secondary Mirrors")
    AddTextEntry("0xB8C6C79D", "Secondary Cage")
    AddTextEntry("0xB34BC655", "Track Red")
    AddTextEntry("0xB50064B6", "Carbon Diffuser")
    AddTextEntry("0xBDDE4F8D", "SR Red")
    AddTextEntry("0xC1B35E84", "Pfister Stripe Blue")
    AddTextEntry("0xC6B16372", "Primary Cage")
    AddTextEntry("0xC84F7058", "Track White")
    AddTextEntry("0xC17562A8", "Track Blue")
    AddTextEntry("0xCE2B7027", "SR White")
    AddTextEntry("0xCE6177DC", "Pfister Stripe Beige")
    AddTextEntry("0xD1AF0EA2", "Full Carbon Bonnet")
    AddTextEntry("0xD5BA868E", "Touring Racer #3")
    AddTextEntry("0xD7B08F1E", "Track Silver")
    AddTextEntry("0xD116FD4B", "Pfister Stripe Yellow")
    AddTextEntry("0xDA1307FA", "SR Gold")
    AddTextEntry("0xDA662010", "Race Carbon Bonnet")
    AddTextEntry("0xEBBB2B4A", "SR Blue")
    AddTextEntry("0xEC210F6A", "Carbon Canards")
    AddTextEntry("0xEEC28EF7", "Highlighted Skirts")
    AddTextEntry("0xF8B9E5E7", "Comet SR")
    AddTextEntry("0xF9F04EF9", "Retro-Rennen #16")
end)