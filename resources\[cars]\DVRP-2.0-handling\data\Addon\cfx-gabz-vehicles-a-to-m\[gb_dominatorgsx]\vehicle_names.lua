Citizen.CreateThread(function()
    AddTextEntry("GBDOMGSX", "Dominator GSX")
    AddTextEntry("DOMGSX_SPL2", "Sport Spoiler")
    AddTextEntry("DOMGSX_SPL3", "Street Spoiler")
    AddTextEntry("DOMGSX_SPL4", "Carbon Wing")
    AddTextEntry("DOMGSX_SPL5", "Top Mount Wing")
    AddTextEntry("DOMGSX_SPL6", "Track Wing")
    AddTextEntry("DOMGSX_SPL7", "Drift Wing")
    AddTextEntry("DOMGSX_SPL8", "Circuit Wing")
    AddTextEntry("DOMGSX_SPL9", "Drag Spoiler")
    AddTextEntry("DOMGSX_DIFF1", "Street Diffuser")
    AddTextEntry("DOMGSX_DIFF2", "Sport Diffuser")
    AddTextEntry("DOMGSX_DIFF3", "MK.2 Sport Diffuser")
    AddTextEntry("DOMGSX_DIFF4", "Race Diffuser")
    AddTextEntry("DOMGSX_DIFF5", "MK.2 Race Diffuser")
    AddTextEntry("DOMGSX_DIFF6", "Track Diffuser")
    AddTextEntry("DOMGSX_DIFF7", "Drift Diffuser")
    AddTextEntry("DOMGSX_DIFF8", "Comb Diffuser")
    AddTextEntry("DOMGSX_SPLT1", "Street Spliter")
    AddTextEntry("DOMGSX_SPLT2", "Sport Spliter")
    AddTextEntry("DOMGSX_SPLT3", "Race Spliter")
    AddTextEntry("DOMGSX_SPLT4", "Track Spliter")
    AddTextEntry("DOMGSX_SPLT5", "Sharp Spliter")
    AddTextEntry("DOMGSX_SPLT6", "Super Spliter")
    AddTextEntry("DOMGSX_BUMF0A", "Carbon Stock Front Bumper")
    AddTextEntry("DOMGSX_BUMF1", "Slatted Front Bumper")
    AddTextEntry("DOMGSX_BUMF1A", "Carbon Slatted Front Bumper")
    AddTextEntry("DOMGSX_BUMF2", "Sport Front Bumper")
    AddTextEntry("DOMGSX_BUMF3", "Street Front Bumper")
    AddTextEntry("DOMGSX_SKIRT0A", "Carbon Skirt")
    AddTextEntry("DOMGSX_SKIRT1", "Extended Skirt")
    AddTextEntry("DOMGSX_SKIRT1A", "Extended Carbon Skirt")
    AddTextEntry("DOMGSX_SKIRT2", "Wide Super Skirt")
    AddTextEntry("DOMGSX_BUMR0E1", "Stock w/ Round Exhausts")
    AddTextEntry("DOMGSX_BUMR0E1", "Stock w/ Square Exhausts")
    AddTextEntry("DOMGSX_BUMR0A", "Carbon Stock Rear Bumper")
    AddTextEntry("DOMGSX_BUMR0AE1", "Carbon Stock w/ Round Exhausts")
    AddTextEntry("DOMGSX_BUMR0AE2", "Carbon Stock w/ Square Exhausts")
    AddTextEntry("DOMGSX_BUMR1", "Sport Rear Bumper")
    AddTextEntry("DOMGSX_BUMR1E1", "Sport w/ Round Exhausts")
    AddTextEntry("DOMGSX_BUMR1E2", "Sport w/ Square Exhausts")
    AddTextEntry("DOMGSX_BUMR2", "Remove Rear Bumper")
    AddTextEntry("DOMGSX_BUMR2E1", "Remove w/ Round Exhausts")
    AddTextEntry("DOMGSX_BUMR2E2", "Remove w/ Square Exhausts")
    AddTextEntry("DOMGSX_ROOF0A", "Shark Fin")
    AddTextEntry("DOMGSX_ROOF1", "Black Roof")
    AddTextEntry("DOMGSX_ROOF1A", "Black Roof w/ Shark Fin")
    AddTextEntry("DOMGSX_ROOF2", "Carbon Roof")
    AddTextEntry("DOMGSX_ROOF2A", "Carbon Roof w/ Shark Fin")
    AddTextEntry("DOMGSX_RPANEL1", "Painted Rear Panel")
    AddTextEntry("DOMGSX_RPANEL2", "Carbon Rear Panel")
    AddTextEntry("DOMGSX_WIDE1", "Drift Fender Flares")
    AddTextEntry("DOMGSX_WIDE2", "Fender Flares")
    AddTextEntry("DOMGSX_WIDE2A", "Alt. Fender Flares")
    AddTextEntry("DOMGSX_WIDE3", "Widebody Fender Flares")
    AddTextEntry("TOP_DOMR", "Rear Bumper")
    AddTextEntry("TOP_DOMFL", "Fender Flares")
    AddTextEntry("GBDOMGSX_LIVERY_1", "Karma Bite Blue")
    AddTextEntry("GBDOMGSX_LIVERY_2", "Karma Bite Gold")
    AddTextEntry("GBDOMGSX_LIVERY_3", "Karma Bite Green")
    AddTextEntry("GBDOMGSX_LIVERY_4", "Karma Bite Red")
    AddTextEntry("GBDOMGSX_LIVERY_5", "Karma Bite White")
    AddTextEntry("GBDOMGSX_LIVERY_6", "Classic Twin Stripes Black")
    AddTextEntry("GBDOMGSX_LIVERY_7", "Classic Twin Stripes Blue")
    AddTextEntry("GBDOMGSX_LIVERY_8", "Classic Twin Stripes Gold")
    AddTextEntry("GBDOMGSX_LIVERY_9", "Classic Twin Stripes Red")
    AddTextEntry("GBDOMGSX_LIVERY_10", "Classic Twin Stripes Silver")
    AddTextEntry("GBDOMGSX_LIVERY_11", "Classic Twin Stripes White")
    AddTextEntry("GBDOMGSX_LIVERY_12", "Inverse Twin Stripes Black")
    AddTextEntry("GBDOMGSX_LIVERY_13", "Inverse Twin Stripes Silver")
    AddTextEntry("GBDOMGSX_LIVERY_14", "Inverse Twin Stripes White")
    AddTextEntry("GBDOMGSX_LIVERY_15", "Thoroughbred")
    AddTextEntry("GBDOMGSX_LIVERY_16", "Number 3 Racer")
    AddTextEntry("GBDOMGSX_LIVERY_17", "The Edge")
end)