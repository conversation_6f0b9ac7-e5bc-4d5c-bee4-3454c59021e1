Citizen.CreateThread(function()
	AddTextEntry("GBEMESB1", "Emerus SB1")
	
	AddTextEntry("GBEMERUSSB1_EXHAUST_1", "Racing Exhaust")
	
	AddTextEntry("GBEMERUSSB1_SPOILER_1", "Racing Spoiler")
	AddTextEntry("GBEMERUSSB1_SPOILER_2", "Competition Spoiler")
	AddTextEntry("GBEMERUSSB1_SPOILER_3", "Split Spoiler")

	
	AddTextEntry("EMERUSSB1_LIV1", "Aurora Borealis Gradient")
	AddTextEntry("EMERUSSB1_LIV2", "Sunset Gradient")
	AddTextEntry("EMERUSSB1_LIV3", "Vice City Gradient")
	AddTextEntry("EMERUSSB1_LIV4", "Silver Polygonal")
	AddTextEntry("EMERUSSB1_LIV5", "Black Polygonal")
	AddTextEntry("EMERUSSB1_LIV6", "Blue Polygonal")
	
end)