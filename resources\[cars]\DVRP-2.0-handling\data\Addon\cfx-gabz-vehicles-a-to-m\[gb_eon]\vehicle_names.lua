Citizen.CreateThread(function()
	AddTextEntry("GBEON", "Eon")
	AddTextEntry("GBEON_WING1", "Boot Lip")
	AddTextEntry("GBEON_WING1A", "Carbon Boot Lip")
	AddTextEntry("GBEON_WING2", "Street Spoiler")
	AddTextEntry("GBEON_WING2A", "Carbon Street Spoiler")
	AddTextEntry("GBEON_WING3", "Carbon Wing")
	AddTextEntry("GBEON_WING4", "Drift Wing")
	AddTextEntry("GBEON_WING5", "Competition Wing")
	AddTextEntry("GBEON_BUMR1", "Secondary Custom Diffuser")
	AddTextEntry("GBEON_BUMR2", "Carbon Custom Diffuser")
	AddTextEntry("GBEON_BUMF1", "Secondary Custom Splitter")
	AddTextEntry("GBEON_BUMF2", "Carbon CustomSplitter")
	AddTextEntry("GBEON_SKIRT1", "Secondary Custom Skirt")
	AddTextEntry("GBEON_SKIRT2", "Carbon Custom Skirt")
	AddTextEntry("GBEON_TRIM", "Black Trim")
	AddTextEntry("GBEON_EYELID1", "Angry Eyelids")
	AddTextEntry("GBEON_EYELID2", "Sleepy Eyelids")
	AddTextEntry("GBEON_ROOF1", "Roof Bars")
	AddTextEntry("GBEON_ROOF2", "Roof Bars w/ Box")
	AddTextEntry("GBEON_LIV1", "Halftone Hexagons Black")
	AddTextEntry("GBEON_LIV2", "Halftone Hexagons Gray")
	AddTextEntry("GBEON_LIV3", "Halftone Hexagons White")
	AddTextEntry("GBEON_LIV4", "Shopping List Black")
	AddTextEntry("GBEON_LIV5", "Shopping List White")
	AddTextEntry("GBEON_LIV6", "Black Cyberlines")
	AddTextEntry("GBEON_LIV7", "White Cyberlines")
	AddTextEntry("GBEON_LIV8", "Gray Cyberlines")
	AddTextEntry("GBEON_LIV9", "Green Cyberlines")
	AddTextEntry("GBEON_LIV10", "Teal Cyberlines")
	AddTextEntry("GBEON_LIV11", "Red Cyberlines")
	AddTextEntry("GBEON_LIV12", "Mobile Service")
	AddTextEntry("GBEON_LIV13", "Two-Face Black")
	AddTextEntry("GBEON_LIV14", "Two-Face Silver")
end)