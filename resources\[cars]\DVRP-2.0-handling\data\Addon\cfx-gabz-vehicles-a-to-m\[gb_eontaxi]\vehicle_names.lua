Citizen.CreateThread(function()
	AddTextEntry("GBTAXIE<PERSON>", "Taxi Eon")
	AddTextEntry("GBEON_WING1", "Boot Lip")
	AddTextEntry("GBEON_WING1A", "Carbon Boot Lip")
	AddTextEntry("GBEON_WING2", "Street Spoiler")
	AddTextEntry("GBEON_WING2A", "Carbon Street Spoiler")
	AddTextEntry("GBEON_WING3", "Carbon Wing")
	AddTextEntry("GBEON_WING4", "Drift Wing")
	AddTextEntry("GBEON_WING5", "Competition Wing")
	AddTextEntry("GBEON_BUMR1", "Secondary Custom Diffuser")
	AddTextEntry("GBEON_BUMR2", "Carbon Custom Diffuser")
	AddTextEntry("GBEON_BUMF1", "Secondary Custom Splitter")
	AddTextEntry("GBEON_BUMF2", "Carbon CustomSplitter")
	AddTextEntry("GBEON_SKIRT1", "Secondary Custom Skirt")
	AddTextEntry("GBEON_SKIRT2", "Carbon Custom Skirt")
	AddTextEntry("GBEON_TRIM", "Black Trim")
	AddTextEntry("GBEON_EYELID1", "Angry Eyelids")
	AddTextEntry("GBEON_EYELID2", "Sleepy Eyelids")
	AddTextEntry("GBEON_ROOF1", "Roof Bars")
	AddTextEntry("GBEON_ROOF2", "Roof Bars w/ Box")
	AddTextEntry("EONT_LIV1", "Downtown Cab Co")
end)