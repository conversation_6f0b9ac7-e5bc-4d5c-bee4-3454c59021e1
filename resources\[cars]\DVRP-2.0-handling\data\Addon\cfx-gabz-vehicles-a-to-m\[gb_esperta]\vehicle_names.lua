Citizen.CreateThread(function()
	AddTextEntry("GBESPERTA", "Rumpo Esperta")
	AddTextEntry("ESP_RACK1A", "Chrome Rack")
	AddTextEntry("ESP_RACK1B", "Chrome Rack w/ Cargo 1")
	AddTextEntry("ESP_RACK1C", "Chrome Rack w/ Cargo 2")
	AddTextEntry("ESP_RACK2A", "Ladder Rack")
	AddTextEntry("ESP_RACK2B", "Ladder Rack w Cargo")
	AddTextEntry("ESP_RACK3A", "Three Bar Rack")
	AddTextEntry("ESP_RACK3B", "Three Bar Rack w/ Accessories")
	AddTextEntry("ESP_RACK3C", "Three Bar Rack w/ Cargo")
	AddTextEntry("ESP_RACK4A", "Trail Rack")
	AddTextEntry("ESP_RACK4B", "Trail Rack w/ Cargo 1")
	AddTextEntry("ESP_RACK4C", "Trail Rack w/ Cargo 2")
	AddTextEntry("ESP_SKIRT1A", "Side Steps")
	AddTextEntry("ESP_SKIRT2A", "Chrome Side Steps")
	AddTextEntry("ESP_SKIRT3A", "Heavy Duty Side Step")
	AddTextEntry("ESP_SKIRT3B", "Chrome Heavy Duty Side Step")
	AddTextEntry("ESP_SKIRT4A", "Fiberglass Running Boards")
	AddTextEntry("ESP_SKIRT5A", "Trail Rock Sliders")
	AddTextEntry("ESP_SKIRT5B", "Chrome Trail Rock Sliders")
	AddTextEntry("ESP_SKIRT6A", "Sport Skirts")
	AddTextEntry("ESP_SKIRT6B", "Black Sport Skirts")
	AddTextEntry("ESP_BONBADGE1", "Black Badge")
	AddTextEntry("ESP_BONDEF1", "Hood Deflector")
	AddTextEntry("ESP_BONDEF2", "High Level Hood Deflector")
	AddTextEntry("ESP_SP1", "Ladder")
	AddTextEntry("ESP_SP2", "Spare Wheel")
	AddTextEntry("ESP_SP3", "Ladder & Spare Wheel")
	AddTextEntry("ESP_SNORKEL", "Trail Snorkel")
	AddTextEntry("ESP_GRILL1A", "OEM Grill")
	AddTextEntry("ESP_GRILL1B", "Chrome OEM Grill")
	AddTextEntry("ESP_GRILL2A", "Alternate Grill")
	AddTextEntry("ESP_GRILL2B", "Chrome Alternate Grill")
	AddTextEntry("ESP_GRILL3A", "Premium Grill")
	AddTextEntry("ESP_GRILL3B", "Chrome Premium Grill")
	AddTextEntry("ESP_GRILL4A", "Classic Grill")
	AddTextEntry("ESP_GRILL4B", "Chrome Classic Grill")
	AddTextEntry("ESP_BUMFTRIM", "Chrome Grill Surround")
	AddTextEntry("ESP_BUMF1", "Euro Plate")
	AddTextEntry("ESP_BUMF2", "Painted Bumper")
	AddTextEntry("ESP_BUMF3", "Painted Bumper w/ Euro Plate")
	AddTextEntry("ESP_BAR1A", "Chrome Push Bar")
	AddTextEntry("ESP_BAR1B", "Chrome Brush Guard")
	AddTextEntry("ESP_BAR1C", "Fog Lights Push Bar")
	AddTextEntry("ESP_BAR1D", "Fog Lights Brush Guard")
	AddTextEntry("ESP_BAR2A", "Trail Push Bar")
	AddTextEntry("ESP_BAR2B", "Trail Brush Guard")
	AddTextEntry("ESP_BAR2C", "Fog Lights Trail Push Bar")
	AddTextEntry("ESP_BAR2D", "Fog Lights Trail Brush Guard")
	AddTextEntry("ESP_BAR3A", "Enforcer Push Bar")
	AddTextEntry("ESP_BAR3B", "Enforcer Brush Guard")
	AddTextEntry("ESP_BAR3C", "Fog Lights Enforcer Push Bar")
	AddTextEntry("ESP_BAR3D", "Fog Lights Enforcer Brush Guard")
	AddTextEntry("ESP_BAR4A", "Grill Guard")
	AddTextEntry("ESP_BAR4B", "Full Grill Guard")
	AddTextEntry("ESP_BAR4C", "Fog Lights Grill Guard")
	AddTextEntry("ESP_BAR4D", "Fog Lights Full Grill Guard")
	AddTextEntry("ESP_BAR5", "Trail Winch Bumper")
	AddTextEntry("ESP_BAR6A", "Outback Bull Bar")
	AddTextEntry("ESP_BAR6B", "Fog Lights Outback Bull Bar")
	AddTextEntry("ESP_BAR7A", "Sport Splitter")
	AddTextEntry("ESP_BAR7B", "Black Sport Splitter")
	AddTextEntry("ESP_BUMR1A", "Painted Rear Bumper")
	AddTextEntry("ESP_BUMRSPAT1A", "Sport Rear Spats")
	AddTextEntry("ESP_BUMRSPAT1B", "Black Sport Rear Spats")
	AddTextEntry("ESP_RSTEP1", "Rear Step")
	AddTextEntry("ESP_RSTEP2A", "Heavy Duty Rear Step")
	AddTextEntry("ESP_RSTEP2B", "Chrome Heavy Duty Rear Step")
	AddTextEntry("ESP_RSTEP3", "Industrial Rear Step")
	AddTextEntry("ESP_PLATER", "Rear Euro Plate")
	AddTextEntry("ESP_GUARD", "Riot Armor")
	AddTextEntry("ESP_TRIM1A", "Painted Flares")
	AddTextEntry("ESP_TRIM2A", "Trail Flares")
	AddTextEntry("ESP_TRIM2B", "Painted Trail Flares")
		

	AddTextEntry("ESPERTA_LIV1", "Truck You Removals")
	AddTextEntry("ESPERTA_LIV2", "Touchdown Car Rentals")
	AddTextEntry("ESPERTA_LIV3", "Floor Skin Hardwood Coverings")
	AddTextEntry("ESPERTA_LIV4", "LST Road Maintenance")
	AddTextEntry("ESPERTA_LIV5", "Himalaya")
	AddTextEntry("ESPERTA_LIV6", "Pharte Gas")
	AddTextEntry("ESPERTA_LIV7", "Weazel News (Classic)")
	AddTextEntry("ESPERTA_LIV8", "Weazel News (Modern)")
	AddTextEntry("ESPERTA_LIV9", "The Mighty Bush")
	AddTextEntry("ESPERTA_LIV10", "Studio Bloom")
	AddTextEntry("ESPERTA_LIV11", "Tinkle Mobile Technology")
	AddTextEntry("ESPERTA_LIV12", "Los Santos Department of Water & Power")
	AddTextEntry("ESPERTA_LIV13", "DigitalDen")
	AddTextEntry("ESPERTA_LIV14", "You Tool")
	AddTextEntry("ESPERTA_LIV15", "United States Post")
end)
