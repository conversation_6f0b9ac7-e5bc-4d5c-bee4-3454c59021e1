Citizen.CreateThread(function()
AddTextEntry("GRESLEYSTX", "Gresley STX")

AddTextEntry("GRESTX_SPL1", "Sport Spoiler")
AddTextEntry("GRESTX_BUMF1", "Sport Splitter")
AddTextEntry("GRESTX_BUMF1A", "Painted Sport Splitter")
AddTextEntry("GRESTX_BUMF2", "Extended Splitter")
AddTextEntry("GRESTX_BUMF2A", "Painted Extended Splitter")
AddTextEntry("GRESTX_BUMR1", "Plastic Rear Bumper")
AddTextEntry("GRESTX_SKIRT1", "Plastic Skirts")
AddTextEntry("GRESTX_GRILL1", "STX Grille")
AddTextEntry("GRESTX_GRILL2", "Sport Grille")
AddTextEntry("GRESTX_GRILL3", "Sport Grille w/ Painted Ducts")
AddTextEntry("GRESTX_GRILL4", "Painted Grille")
AddTextEntry("GRESTX_GRILL5", "Painted STX Grille")
AddTextEntry("GRESTX_GRILL6", "Painted Sport Grille")
AddTextEntry("GRESTX_GRILL7", "Fully Painted Sport Grille")
AddTextEntry("GRESTX_HOOD1", "Sport Hood")
AddTextEntry("GRESTX_ARCH1", "Plastic Arches")
AddTextEntry("GRESTX_MIR1", "Black Mirrors")
AddTextEntry("GRESTX_ROOFACC1", "Shark Fin")
AddTextEntry("GRESTX_ROOFACC2", "Vortex Generators")

AddTextEntry("GRESTX_LIV1", "White Race Stripes")
AddTextEntry("GRESTX_LIV2", "Black Race Stripes")
AddTextEntry("GRESTX_LIV3", "Gray Race Stripes")
AddTextEntry("GRESTX_LIV4", "Blue Race Stripes")
AddTextEntry("GRESTX_LIV5", "Red Race Stripes")
AddTextEntry("GRESTX_LIV6", "Blue STX")
AddTextEntry("GRESTX_LIV7", "Red STX")
AddTextEntry("GRESTX_LIV8", "Lime STX")
AddTextEntry("GRESTX_LIV9", "White STX")
AddTextEntry("GRESTX_LIV10", "Bravado Racing (Black)")
AddTextEntry("GRESTX_LIV11", "Bravado Racing (Gray)")
AddTextEntry("GRESTX_LIV12", "Bravado Racing (Red)")
AddTextEntry("GRESTX_LIV13", "Bravado Racing (Blue)")
AddTextEntry("GRESTX_LIV14", "Bravado Racing (Yellow)")
AddTextEntry("GRESTX_LIV15", "Bravado Racing (Lime)")
AddTextEntry("GRESTX_LIV16", "Bravado Racing (Purple)")
AddTextEntry("GRESTX_LIV17", "Bravado Racing (White)")
AddTextEntry("GRESTX_LIV18", "Bravado Billboard (Black)")
AddTextEntry("GRESTX_LIV19", "Bravado Billboard (Gray)")
AddTextEntry("GRESTX_LIV20", "Bravado Billboard (White)")
AddTextEntry("GRESTX_LIV21", "Bravado Billboard (Blue)")
AddTextEntry("GRESTX_LIV22", "Bravado Billboard (Red)")
AddTextEntry("GRESTX_LIV23", "Nightmare (Black)")
AddTextEntry("GRESTX_LIV24", "Nightmare (Gray)")
AddTextEntry("GRESTX_LIV25", "Nightmare (White)")
AddTextEntry("GRESTX_LIV26", "Nightmare (Dark)")
AddTextEntry("GRESTX_LIV27", "Nightmare (Light)")
AddTextEntry("GRESTX_LIV28", "Nightmare (Red)")
AddTextEntry("GRESTX_LIV29", "Hellfire Billboard (Black)")
AddTextEntry("GRESTX_LIV30", "Hellfire Billboard (White)")
AddTextEntry("GRESTX_LIV31", "Hellfire Decal (Black)")
AddTextEntry("GRESTX_LIV32", "Hellfire Decal (White)")
AddTextEntry("GRESTX_LIV33", "STX Billboard (Black)")
AddTextEntry("GRESTX_LIV34", "STX Billboard (White)")
AddTextEntry("GRESTX_LIV35", "STX Decal (Black)")
AddTextEntry("GRESTX_LIV36", "STX Decal (White)")
AddTextEntry("GRESTX_LIV37", "Mall Crawler")
AddTextEntry("GRESTX_LIV38", "Pavement Princess")
AddTextEntry("GRESTX_LIV39", "Weekend Warrior")
AddTextEntry("GRESTX_LIV40", "Running of the Bull")
end)