Citizen.CreateThread(function()
        AddTextEntry("GBHADES", "Hades")
        AddTextEntry("HADES_BUMR1B", "Stock Bumper with Painted Text")
        AddTextEntry("HADES_BUMR2A", "Vented Bumper with Chrome Text")
        AddTextEntry("HADES_BUMR2B", "Vented Bumper with Painted Text")
        AddTextEntry("HADES_TRUNK1B", "Stock Trunk with Dundreary Text")
        AddTextEntry("HADES_GRILL1B", "Painted Stock Grille")
        AddTextEntry("HADES_GRILL2A", "Debadged Grille")
        AddTextEntry("HADES_GRILL2B", "Painted Debadged Grille")
        AddTextEntry("HADES_GRILL3A", "Thick Bar Grille")
        AddTextEntry("HADES_GRILL3B", "Debadged Thick Bar Grille")

        AddTextEntry("HADES_LIV1", "Drag Antics White")
        AddTextEntry("HADES_LIV2", "Drag Antics Silver")
        AddTextEntry("HADES_LIV3", "Drag Antics Black")
        AddTextEntry("HADES_LIV4", "Touring")
        AddTextEntry("HADES_LIV5", "Black Tribal Flames")
        AddTextEntry("HADES_LIV6", "White Tribal Flames")
        AddTextEntry("HADES_LIV7", "Black Twin Stripes")
        AddTextEntry("HADES_LIV8", "White Twin Stripes")
        AddTextEntry("HADES_LIV9", "Hot Rod Flames (Classic)")
        AddTextEntry("HADES_LIV10", "Hot Rod Flames (Retro)")

        AddTextEntry("STANLE_SPL1", "Small Lip")
        AddTextEntry("STANLE_SPL1A", "Painted Small Lip")
        AddTextEntry("STANLE_SPL2", "Medium Lip")
        AddTextEntry("STANLE_SPL2A", "Painted Medium Lip")
        AddTextEntry("STANLE_SPL3", "Flush Spoiler")
        AddTextEntry("STANLE_SPL4", "Low Level Spoiler")
        AddTextEntry("STANLE_SPL5", "Ducktail")
        AddTextEntry("STANLE_SPL5A", "Painted Ducktail")
        AddTextEntry("STANLE_SPL6", "Sport Wing")
        AddTextEntry("STANLE_SPL7", "Big Lip")
        AddTextEntry("STANLE_SPL8", "Street Wing")
        AddTextEntry("STANLE_SPL9", "Drift Wing")
        AddTextEntry("STANLE_SPL9A", "Big Drift Wing")
        AddTextEntry("STANLE_SPL10", "Carbon Wing")
        AddTextEntry("STANLE_SPL10A", "Big Carbon Wing")
        AddTextEntry("STANLE_SPL11", "Performance Wing")
        AddTextEntry("STANLE_SPL12", "Painted Street Wing")
        AddTextEntry("STANLE_BUMF0A", "Plastic Trim Front Bumper")
        AddTextEntry("STANLE_BUMF0B", "Painted Trim Front Bumper")
        AddTextEntry("STANLE_BUMF0C", "Smooth Plastic Trim Front Bumper")
        AddTextEntry("STANLE_BUMF0D", "Smooth Painted Trim Front Bumper")
        AddTextEntry("STANLE_BUMF1", "Sport Front Bumper")
        AddTextEntry("STANLE_BUMF1A", "Extended Sport Front Bumper")
        AddTextEntry("STANLE_BUMF2", "ASP Front Bumper")
        AddTextEntry("STANLE_BUMF2A", "Stripped ASP Front Bumper")
        AddTextEntry("STANLE_BUMF3", "Hellenbach Front Bumper")
        AddTextEntry("STANLE_BUMF4", "Ricer Front Bumper")
        AddTextEntry("STANLE_BUMF5", "Front Bash Bar")
        AddTextEntry("STANLE_SKIRT1", "Painted Skirts")
        AddTextEntry("STANLE_SKIRT2", "Custom Skirts")
        AddTextEntry("STANLE_SKIRT3", "Ricer Skirts")
        AddTextEntry("STANLE_BUMR0A", "Plastic Trim Rear Bumper")
        AddTextEntry("STANLE_BUMR0B", "Painted Trim Rear Bumper")
        AddTextEntry("STANLE_BUMR0C", "Smooth Plastic Trim Rear Bumper")
        AddTextEntry("STANLE_BUMR0D", "Smooth Painted Trim Rear Bumper")
        AddTextEntry("STANLE_BUMR1", "Sport Rear Bumper")
        AddTextEntry("STANLE_BUMR2", "Ricer Rear Bumper")
        AddTextEntry("STANLE_GRILL0A", "Black Grille")
        AddTextEntry("STANLE_GRILL0B", "Painted Grille")
        AddTextEntry("STANLE_GRILL1", "Split Grille")
        AddTextEntry("STANLE_GRILL1A", "Black Split Grille")
        AddTextEntry("STANLE_GRILL1B", "Painted Split Grille")
        AddTextEntry("STANLE_GRILL2", "Open Grille")
        AddTextEntry("STANLE_GRILL2A", "Black Open Grille")
        AddTextEntry("STANLE_GRILL2B", "Painted Open Grille")
        AddTextEntry("STANLE_HOOD1", "Vented Hood")
        AddTextEntry("STANLE_HOOD2", "Split Vented Hood")
        AddTextEntry("STANLE_HOOD3", "Muscle Hood")
        AddTextEntry("STANLE_HOOD4", "Low Level Cowl Hood")
        AddTextEntry("STANLE_HOOD5", "High Level Cowl Hood")
        AddTextEntry("STANLE_HOOD6", "Blower Hood")
        AddTextEntry("STANLE_HOOD7", "Blower Low Level Cowl Hood")
        AddTextEntry("STANLE_HOOD8", "Blower High Level Cowl Hood")
        AddTextEntry("STANLE_FLARE1", "Plastic Fender Flares")
        AddTextEntry("STANLE_FLARE2", "Chrome Fender Flares")
        AddTextEntry("STANLE_WDEFLF", "Wind Deflectors")
        AddTextEntry("STANLE_RSPL1", "Roof Spoiler")
        AddTextEntry("STANLE_RSPL2", "Painted Roof Spoiler")
        AddTextEntry("STANLE_LOUVERS", "Louvers")
        AddTextEntry("STANLE_HLIGHT1", "Painted Headlight Housing")
        AddTextEntry("STANLE_TAIL1", "Small Rear Light Covers")
        AddTextEntry("STANLE_TAIL2", "Sleepy Rear Light Covers")
        AddTextEntry("STANLE_TAIL3", "Sport Rear Light Covers")
        AddTextEntry("STANLE_STRIP", "Sunstrip")
        AddTextEntry("STANLE_RTRIM0", "Chrome Rear Trim")
        AddTextEntry("STANLE_RTRIM1", "Black Rear Trim")
        AddTextEntry("STANLE_RTRIM2", "Painted Rear Trim")
        AddTextEntry("STANLE_TRUNK1", "Painted Trunk Panel")
        AddTextEntry("STANLE_TRUNK2", "Gloss Black Trunk Panel")
        AddTextEntry("STANLE_DOOR", "Plastic Door Trim & Mirrors")
        AddTextEntry("STANLE_DTRIM1", "Plastic Side Trim")
        AddTextEntry("STANLE_DTRIM2", "Painted Side Trim")
        AddTextEntry("STANLE_DTRIM3", "Smooth Plastic Side Trim")
        AddTextEntry("STANLE_DTRIM4", "Smooth Painted Side Trim")
        AddTextEntry("STANLE_DTRIMR", "Remove Side Trim")
        AddTextEntry("TOP_WDTRIM", "Window Trim")
        AddTextEntry("TOP_RTRIM", "Rear Trim")

end)
