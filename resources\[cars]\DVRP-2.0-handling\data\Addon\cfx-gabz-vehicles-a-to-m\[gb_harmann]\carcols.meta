<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<CVehicleModelInfoVarGlobal>
  <Kits>
	<Item>      
      <kitName>2475_gbharmann_modkit</kitName>
      <id value="2475" />	 	  
      <kitType>MKT_SPECIAL</kitType>
      <visibleMods>
		<!--LIVERIES-->
		<Item>
          <modelName>gbharmann_livery_1</modelName>
          <modShopLabel>HARMANN_LIVERY_1</modShopLabel>
          <linkedModels>
		    <Item></Item>
		  </linkedModels>
          <turnOffBones>
			<Item></Item>
          </turnOffBones>
          <type>VMT_LIVERY_MOD</type>
          <bone>chassis</bone>
          <collisionBone />
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="0" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>g<PERSON>rman<PERSON>_livery_7</modelName>
          <modShopLabel>HARMANN_LIVERY_7</modShopLabel>
          <linkedModels>
		    <Item></Item>
		  </linkedModels>
          <turnOffBones>
			<Item></Item>
          </turnOffBones>
          <type>VMT_LIVERY_MOD</type>
          <bone>chassis</bone>
          <collisionBone />
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="0" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>gbharmann_livery_3</modelName>
          <modShopLabel>HARMANN_LIVERY_3</modShopLabel>
          <linkedModels>
		    <Item></Item>
		  </linkedModels>
          <turnOffBones>
			<Item></Item>
          </turnOffBones>
          <type>VMT_LIVERY_MOD</type>
          <bone>chassis</bone>
          <collisionBone />
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="0" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>gbharmann_livery_4</modelName>
          <modShopLabel>HARMANN_LIVERY_4</modShopLabel>
          <linkedModels>
		    <Item></Item>
		  </linkedModels>
          <turnOffBones>
			<Item></Item>
          </turnOffBones>
          <type>VMT_LIVERY_MOD</type>
          <bone>chassis</bone>
          <collisionBone />
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="0" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>gbharmann_livery_5</modelName>
          <modShopLabel>HARMANN_LIVERY_5</modShopLabel>
          <linkedModels>
		    <Item></Item>
		  </linkedModels>
          <turnOffBones>
			<Item></Item>
          </turnOffBones>
          <type>VMT_LIVERY_MOD</type>
          <bone>chassis</bone>
          <collisionBone />
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="0" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>gbharmann_livery_6</modelName>
          <modShopLabel>HARMANN_LIVERY_6</modShopLabel>
          <linkedModels>
		    <Item></Item>
		  </linkedModels>
          <turnOffBones>
			<Item></Item>
          </turnOffBones>
          <type>VMT_LIVERY_MOD</type>
          <bone>chassis</bone>
          <collisionBone />
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="0" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>gbharmann_livery_2</modelName>
          <modShopLabel>HARMANN_LIVERY_2</modShopLabel>
          <linkedModels>
		    <Item></Item>
		  </linkedModels>
          <turnOffBones>
			<Item></Item>
          </turnOffBones>
          <type>VMT_LIVERY_MOD</type>
          <bone>chassis</bone>
          <collisionBone />
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="0" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		
		<Item>
          <modelName>harmann_bon_1</modelName>
          <modShopLabel>HARMANN_BON1</modShopLabel>
          <linkedModels />
          <turnOffBones>
            <Item>bonnet</Item>
          </turnOffBones>
          <type>VMT_BONNET</type>
          <bone>bonnet</bone>
          <collisionBone />
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="0" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>harmann_bon_2</modelName>
          <modShopLabel>HARMANN_BON2</modShopLabel>
          <linkedModels />
          <turnOffBones>
            <Item>bonnet</Item>
          </turnOffBones>
          <type>VMT_BONNET</type>
          <bone>bonnet</bone>
          <collisionBone />
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="0" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>harmann_bon_3</modelName>
          <modShopLabel>HARMANN_BON3</modShopLabel>
          <linkedModels />
          <turnOffBones>
            <Item>bonnet</Item>
          </turnOffBones>
          <type>VMT_BONNET</type>
          <bone>bonnet</bone>
          <collisionBone />
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="0" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>harmann_bon_4</modelName>
          <modShopLabel>HARMANN_BON4</modShopLabel>
          <linkedModels />
          <turnOffBones>
            <Item>bonnet</Item>
          </turnOffBones>
          <type>VMT_BONNET</type>
          <bone>bonnet</bone>
          <collisionBone />
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="0" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>harmann_bon_5</modelName>
          <modShopLabel>HARMANN_BON5</modShopLabel>
          <linkedModels />
          <turnOffBones>
            <Item>bonnet</Item>
          </turnOffBones>
          <type>VMT_BONNET</type>
          <bone>bonnet</bone>
          <collisionBone />
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="0" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>harmann_bon_6</modelName>
          <modShopLabel>HARMANN_BON6</modShopLabel>
          <linkedModels />
          <turnOffBones>
            <Item>bonnet</Item>
          </turnOffBones>
          <type>VMT_BONNET</type>
          <bone>bonnet</bone>
          <collisionBone />
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="0" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>harmann_bon_7</modelName>
          <modShopLabel>HARMANN_BON7</modShopLabel>
          <linkedModels />
          <turnOffBones>
            <Item>bonnet</Item>
          </turnOffBones>
          <type>VMT_BONNET</type>
          <bone>bonnet</bone>
          <collisionBone />
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="0" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>harmann_bon_8</modelName>
          <modShopLabel>HARMANN_BON8</modShopLabel>
          <linkedModels />
          <turnOffBones>
            <Item>bonnet</Item>
          </turnOffBones>
          <type>VMT_BONNET</type>
          <bone>bonnet</bone>
          <collisionBone />
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="0" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>harmann_bon_9</modelName>
          <modShopLabel>HARMANN_BON9</modShopLabel>
          <linkedModels />
          <turnOffBones>
            <Item>bonnet</Item>
          </turnOffBones>
          <type>VMT_BONNET</type>
          <bone>bonnet</bone>
          <collisionBone />
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="0" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>harmann_bon_10</modelName>
          <modShopLabel>HARMANN_BON10</modShopLabel>
          <linkedModels />
          <turnOffBones>
            <Item>bonnet</Item>
          </turnOffBones>
          <type>VMT_BONNET</type>
          <bone>bonnet</bone>
          <collisionBone />
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="0" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>harmann_bon_11</modelName>
          <modShopLabel>HARMANN_BON11</modShopLabel>
          <linkedModels />
          <turnOffBones>
            <Item>bonnet</Item>
          </turnOffBones>
          <type>VMT_BONNET</type>
          <bone>bonnet</bone>
          <collisionBone />
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="0" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>harmann_bon_12</modelName>
          <modShopLabel>HARMANN_BON12</modShopLabel>
          <linkedModels />
          <turnOffBones>
            <Item>bonnet</Item>
          </turnOffBones>
          <type>VMT_BONNET</type>
          <bone>bonnet</bone>
          <collisionBone />
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="0" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>harmann_bon_13</modelName>
          <modShopLabel>HARMANN_BON13</modShopLabel>
          <linkedModels />
          <turnOffBones>
            <Item>bonnet</Item>
          </turnOffBones>
          <type>VMT_BONNET</type>
          <bone>bonnet</bone>
          <collisionBone />
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="0" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>harmann_bon_14</modelName>
          <modShopLabel>HARMANN_BON14</modShopLabel>
          <linkedModels />
          <turnOffBones>
            <Item>bonnet</Item>
          </turnOffBones>
          <type>VMT_BONNET</type>
          <bone>bonnet</bone>
          <collisionBone />
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="0" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>harmann_bon_15</modelName>
          <modShopLabel>HARMANN_BON15</modShopLabel>
          <linkedModels />
          <turnOffBones>
            <Item>bonnet</Item>
          </turnOffBones>
          <type>VMT_BONNET</type>
          <bone>bonnet</bone>
          <collisionBone />
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="0" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>

	<Item>
          <modelName>harmann_platef_1</modelName>
          <modShopLabel>HARMANN_PLTF1</modShopLabel>
          <linkedModels />
          <turnOffBones>
          </turnOffBones>
          <type>VMT_PLTHOLDER</type>
          <bone>bumper_f</bone>
          <collisionBone />
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="0" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		
		<Item>
          <modelName>harmann_bumf_1</modelName>
          <modShopLabel>HARMANN_BUMF1</modShopLabel>
          <linkedModels />
          <turnOffBones>
            <Item>bumper_f</Item>
          </turnOffBones>
          <type>VMT_BUMPER_F</type>
          <bone>bumper_f</bone>
          <collisionBone />
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="0" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>harmann_bumf_2</modelName>
          <modShopLabel>HARMANN_BUMF2</modShopLabel>
          <linkedModels />
          <turnOffBones>
            <Item>bumper_f</Item>
          </turnOffBones>
          <type>VMT_BUMPER_F</type>
          <bone>bumper_f</bone>
          <collisionBone />
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="0" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>harmann_bumf_3</modelName>
          <modShopLabel>HARMANN_BUMF3</modShopLabel>
          <linkedModels />
          <turnOffBones>
            <Item>bumper_f</Item>
          </turnOffBones>
          <type>VMT_BUMPER_F</type>
          <bone>bumper_f</bone>
          <collisionBone />
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="0" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>harmann_bumf_4</modelName>
          <modShopLabel>HARMANN_BUMF4</modShopLabel>
          <linkedModels />
          <turnOffBones>
            <Item>bumper_f</Item>
          </turnOffBones>
          <type>VMT_BUMPER_F</type>
          <bone>bumper_f</bone>
          <collisionBone />
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="0" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>harmann_bumf_5</modelName>
          <modShopLabel>HARMANN_BUMF5</modShopLabel>
          <linkedModels />
          <turnOffBones>
            <Item>bumper_f</Item>
          </turnOffBones>
          <type>VMT_BUMPER_F</type>
          <bone>bumper_f</bone>
          <collisionBone />
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="0" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>harmann_bumf_6</modelName>
          <modShopLabel>HARMANN_BUMF6</modShopLabel>
          <linkedModels />
          <turnOffBones>
            <Item>bumper_f</Item>
          </turnOffBones>
          <type>VMT_BUMPER_F</type>
          <bone>bumper_f</bone>
          <collisionBone />
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="0" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>harmann_bumf_7</modelName>
          <modShopLabel>HARMANN_BUMF7</modShopLabel>
          <linkedModels />
          <turnOffBones>
            <Item>bumper_f</Item>
          </turnOffBones>
          <type>VMT_BUMPER_F</type>
          <bone>bumper_f</bone>
          <collisionBone />
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="0" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>harmann_bumf_8</modelName>
          <modShopLabel>HARMANN_BUMF8</modShopLabel>
          <linkedModels />
          <turnOffBones>
            <Item>bumper_f</Item>
          </turnOffBones>
          <type>VMT_BUMPER_F</type>
          <bone>bumper_f</bone>
          <collisionBone />
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="0" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>harmann_bumf_9</modelName>
          <modShopLabel>HARMANN_BUMF9</modShopLabel>
          <linkedModels />
          <turnOffBones>
            <Item>bumper_f</Item>
          </turnOffBones>
          <type>VMT_BUMPER_F</type>
          <bone>bumper_f</bone>
          <collisionBone />
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="0" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>harmann_bumf_10</modelName>
          <modShopLabel>HARMANN_BUMF10</modShopLabel>
          <linkedModels />
          <turnOffBones>
            <Item>bumper_f</Item>
          </turnOffBones>
          <type>VMT_BUMPER_F</type>
          <bone>bumper_f</bone>
          <collisionBone />
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="0" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>harmann_bumf_11</modelName>
          <modShopLabel>HARMANN_BUMF11</modShopLabel>
          <linkedModels />
          <turnOffBones>
            <Item>bumper_f</Item>
          </turnOffBones>
          <type>VMT_BUMPER_F</type>
          <bone>bumper_f</bone>
          <collisionBone />
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="0" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>harmann_bumf_12</modelName>
          <modShopLabel>HARMANN_BUMF12</modShopLabel>
          <linkedModels />
          <turnOffBones>
            <Item>bumper_f</Item>
          </turnOffBones>
          <type>VMT_BUMPER_F</type>
          <bone>bumper_f</bone>
          <collisionBone />
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="0" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>harmann_bumf_13</modelName>
          <modShopLabel>HARMANN_BUMF13</modShopLabel>
          <linkedModels />
          <turnOffBones>
            <Item>bumper_f</Item>
          </turnOffBones>
          <type>VMT_BUMPER_F</type>
          <bone>bumper_f</bone>
          <collisionBone />
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="0" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>harmann_bumf_14</modelName>
          <modShopLabel>HARMANN_BUMF14</modShopLabel>
          <linkedModels />
          <turnOffBones>
            <Item>bumper_f</Item>
          </turnOffBones>
          <type>VMT_BUMPER_F</type>
          <bone>bumper_f</bone>
          <collisionBone />
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="0" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>harmann_bumf_15</modelName>
          <modShopLabel>HARMANN_BUMF15</modShopLabel>
          <linkedModels />
          <turnOffBones>
            <Item>bumper_f</Item>
          </turnOffBones>
          <type>VMT_BUMPER_F</type>
          <bone>bumper_f</bone>
          <collisionBone />
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="0" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>harmann_bumf_16</modelName>
          <modShopLabel>HARMANN_BUMF16</modShopLabel>
          <linkedModels />
          <turnOffBones>
            <Item>bumper_f</Item>
          </turnOffBones>
          <type>VMT_BUMPER_F</type>
          <bone>bumper_f</bone>
          <collisionBone />
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="0" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>harmann_bumf_17</modelName>
          <modShopLabel>HARMANN_BUMF17</modShopLabel>
          <linkedModels />
          <turnOffBones>
            <Item>bumper_f</Item>
          </turnOffBones>
          <type>VMT_BUMPER_F</type>
          <bone>bumper_f</bone>
          <collisionBone />
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="0" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		
		<Item>
          <modelName>harmann_bumr_1</modelName>
          <modShopLabel>HARMANN_BUMR1</modShopLabel>
          <linkedModels />
          <turnOffBones>
            <Item>bumper_r</Item>
          </turnOffBones>
          <type>VMT_EXHAUST</type>
          <bone>bumper_r</bone>
          <collisionBone />
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="0" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>harmann_bumr_2</modelName>
          <modShopLabel>HARMANN_BUMR2</modShopLabel>
          <linkedModels />
          <turnOffBones>
            <Item>bumper_r</Item>
          </turnOffBones>
          <type>VMT_EXHAUST</type>
          <bone>bumper_r</bone>
          <collisionBone />
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="0" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>harmann_bumr_3</modelName>
          <modShopLabel>HARMANN_BUMR3</modShopLabel>
          <linkedModels />
          <turnOffBones>
            <Item>bumper_r</Item>
          </turnOffBones>
          <type>VMT_EXHAUST</type>
          <bone>bumper_r</bone>
          <collisionBone />
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="0" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>harmann_bumr_4</modelName>
          <modShopLabel>HARMANN_BUMR4</modShopLabel>
          <linkedModels />
          <turnOffBones>
            <Item>bumper_r</Item>
          </turnOffBones>
          <type>VMT_EXHAUST</type>
          <bone>bumper_r</bone>
          <collisionBone />
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="0" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>harmann_bumr_5</modelName>
          <modShopLabel>HARMANN_BUMR5</modShopLabel>
          <linkedModels />
          <turnOffBones>
            <Item>bumper_r</Item>
          </turnOffBones>
          <type>VMT_EXHAUST</type>
          <bone>bumper_r</bone>
          <collisionBone />
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="0" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>harmann_bumr_6</modelName>
          <modShopLabel>HARMANN_BUMR6</modShopLabel>
          <linkedModels />
          <turnOffBones>
            <Item>bumper_r</Item>
          </turnOffBones>
          <type>VMT_EXHAUST</type>
          <bone>bumper_r</bone>
          <collisionBone />
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="0" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>harmann_bumr_7</modelName>
          <modShopLabel>HARMANN_BUMR7</modShopLabel>
          <linkedModels />
          <turnOffBones>
            <Item>bumper_r</Item>
          </turnOffBones>
          <type>VMT_EXHAUST</type>
          <bone>bumper_r</bone>
          <collisionBone />
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="0" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>harmann_bumr_8</modelName>
          <modShopLabel>HARMANN_BUMR8</modShopLabel>
          <linkedModels />
          <turnOffBones>
            <Item>bumper_r</Item>
          </turnOffBones>
          <type>VMT_EXHAUST</type>
          <bone>bumper_r</bone>
          <collisionBone />
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="0" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>harmann_bumr_9</modelName>
          <modShopLabel>HARMANN_BUMR9</modShopLabel>
          <linkedModels />
          <turnOffBones>
            <Item>bumper_r</Item>
          </turnOffBones>
          <type>VMT_EXHAUST</type>
          <bone>bumper_r</bone>
          <collisionBone />
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="0" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>harmann_bumr_10</modelName>
          <modShopLabel>HARMANN_BUMR10</modShopLabel>
          <linkedModels />
          <turnOffBones>
            <Item>bumper_r</Item>
          </turnOffBones>
          <type>VMT_EXHAUST</type>
          <bone>bumper_r</bone>
          <collisionBone />
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="0" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>harmann_bumr_11</modelName>
          <modShopLabel>HARMANN_BUMR11</modShopLabel>
          <linkedModels />
          <turnOffBones>
            <Item>bumper_r</Item>
          </turnOffBones>
          <type>VMT_EXHAUST</type>
          <bone>bumper_r</bone>
          <collisionBone />
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="0" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>harmann_bumr_12</modelName>
          <modShopLabel>HARMANN_BUMR12</modShopLabel>
          <linkedModels />
          <turnOffBones>
            <Item>bumper_r</Item>
          </turnOffBones>
          <type>VMT_EXHAUST</type>
          <bone>bumper_r</bone>
          <collisionBone />
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="0" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>harmann_bumr_13</modelName>
          <modShopLabel>HARMANN_BUMR13</modShopLabel>
          <linkedModels />
          <turnOffBones>
            <Item>bumper_r</Item>
          </turnOffBones>
          <type>VMT_EXHAUST</type>
          <bone>bumper_r</bone>
          <collisionBone />
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="0" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>harmann_bumr_14</modelName>
          <modShopLabel>HARMANN_BUMR14</modShopLabel>
          <linkedModels />
          <turnOffBones>
            <Item>bumper_r</Item>
          </turnOffBones>
          <type>VMT_EXHAUST</type>
          <bone>bumper_r</bone>
          <collisionBone />
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="0" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>harmann_bumr_15</modelName>
          <modShopLabel>HARMANN_BUMR15</modShopLabel>
          <linkedModels />
          <turnOffBones>
            <Item>bumper_r</Item>
          </turnOffBones>
          <type>VMT_EXHAUST</type>
          <bone>bumper_r</bone>
          <collisionBone />
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="0" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>harmann_bumr_16</modelName>
          <modShopLabel>HARMANN_BUMR16</modShopLabel>
          <linkedModels />
          <turnOffBones>
            <Item>bumper_r</Item>
          </turnOffBones>
          <type>VMT_EXHAUST</type>
          <bone>bumper_r</bone>
          <collisionBone />
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="0" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		
		<Item>
          <modelName>harmann_grille_1</modelName>
          <modShopLabel>HARMANN_GRILL1</modShopLabel>
          <linkedModels />
          <turnOffBones>
            <Item>misc_c</Item>
          </turnOffBones>
          <type>VMT_GRILL</type>
          <bone>bumper_f</bone>
          <collisionBone />
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="0" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>harmann_grille_2</modelName>
          <modShopLabel>HARMANN_GRILL2</modShopLabel>
          <linkedModels />
          <turnOffBones>
            <Item>misc_c</Item>
          </turnOffBones>
          <type>VMT_GRILL</type>
          <bone>bumper_f</bone>
          <collisionBone />
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="0" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>harmann_grille_3</modelName>
          <modShopLabel>HARMANN_GRILL3</modShopLabel>
          <linkedModels />
          <turnOffBones>
            <Item>misc_c</Item>
          </turnOffBones>
          <type>VMT_GRILL</type>
          <bone>bumper_f</bone>
          <collisionBone />
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="0" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>harmann_grille_4</modelName>
          <modShopLabel>HARMANN_GRILL4</modShopLabel>
          <linkedModels />
          <turnOffBones>
            <Item>misc_c</Item>
          </turnOffBones>
          <type>VMT_GRILL</type>
          <bone>bumper_f</bone>
          <collisionBone />
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="0" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>harmann_grille_5</modelName>
          <modShopLabel>HARMANN_GRILL5</modShopLabel>
          <linkedModels />
          <turnOffBones>
            <Item>misc_c</Item>
          </turnOffBones>
          <type>VMT_GRILL</type>
          <bone>bumper_f</bone>
          <collisionBone />
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="0" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>harmann_grille_6</modelName>
          <modShopLabel>HARMANN_GRILL6</modShopLabel>
          <linkedModels />
          <turnOffBones>
            <Item>misc_c</Item>
          </turnOffBones>
          <type>VMT_GRILL</type>
          <bone>bumper_f</bone>
          <collisionBone />
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="0" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>harmann_grille_7</modelName>
          <modShopLabel>HARMANN_GRILL7</modShopLabel>
          <linkedModels />
          <turnOffBones>
            <Item>misc_c</Item>
          </turnOffBones>
          <type>VMT_GRILL</type>
          <bone>bumper_f</bone>
          <collisionBone />
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="0" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>harmann_grille_8</modelName>
          <modShopLabel>HARMANN_GRILL8</modShopLabel>
          <linkedModels />
          <turnOffBones>
            <Item>misc_c</Item>
          </turnOffBones>
          <type>VMT_GRILL</type>
          <bone>bumper_f</bone>
          <collisionBone />
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="0" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>harmann_grille_9</modelName>
          <modShopLabel>HARMANN_GRILL9</modShopLabel>
          <linkedModels />
          <turnOffBones>
            <Item>misc_c</Item>
          </turnOffBones>
          <type>VMT_GRILL</type>
          <bone>bumper_f</bone>
          <collisionBone />
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="0" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>harmann_grille_10</modelName>
          <modShopLabel>HARMANN_GRILL10</modShopLabel>
          <linkedModels />
          <turnOffBones>
            <Item>misc_c</Item>
          </turnOffBones>
          <type>VMT_GRILL</type>
          <bone>bumper_f</bone>
          <collisionBone />
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="0" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>harmann_grille_11</modelName>
          <modShopLabel>HARMANN_GRILL11</modShopLabel>
          <linkedModels />
          <turnOffBones>
            <Item>misc_c</Item>
          </turnOffBones>
          <type>VMT_GRILL</type>
          <bone>bumper_f</bone>
          <collisionBone />
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="0" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>harmann_grille_12</modelName>
          <modShopLabel>HARMANN_GRILL12</modShopLabel>
          <linkedModels />
          <turnOffBones>
            <Item>misc_c</Item>
          </turnOffBones>
          <type>VMT_GRILL</type>
          <bone>bumper_f</bone>
          <collisionBone />
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="0" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>harmann_grille_13</modelName>
          <modShopLabel>HARMANN_GRILL13</modShopLabel>
          <linkedModels />
          <turnOffBones>
            <Item>misc_c</Item>
          </turnOffBones>
          <type>VMT_GRILL</type>
          <bone>bumper_f</bone>
          <collisionBone />
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="0" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>harmann_grille_14</modelName>
          <modShopLabel>HARMANN_GRILL14</modShopLabel>
          <linkedModels />
          <turnOffBones>
            <Item>misc_c</Item>
          </turnOffBones>
          <type>VMT_GRILL</type>
          <bone>bumper_f</bone>
          <collisionBone />
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="0" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>harmann_grille_15</modelName>
          <modShopLabel>HARMANN_GRILL15</modShopLabel>
          <linkedModels />
          <turnOffBones>
            <Item>misc_c</Item>
          </turnOffBones>
          <type>VMT_GRILL</type>
          <bone>bumper_f</bone>
          <collisionBone />
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="0" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>harmann_grille_16</modelName>
          <modShopLabel>HARMANN_GRILL16</modShopLabel>
          <linkedModels />
          <turnOffBones>
            <Item>misc_c</Item>
          </turnOffBones>
          <type>VMT_GRILL</type>
          <bone>bumper_f</bone>
          <collisionBone />
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="0" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		
		<Item>
          <modelName>harmann_orn_1</modelName>
          <modShopLabel>HARMANN_ORN1</modShopLabel>
          <linkedModels />
          <turnOffBones>
          </turnOffBones>
          <type>VMT_CHASSIS2</type>
          <bone>bumper_f</bone>
          <collisionBone />
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="0" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>harmann_orn_2</modelName>
          <modShopLabel>HARMANN_ORN2</modShopLabel>
          <linkedModels />
          <turnOffBones>
          </turnOffBones>
          <type>VMT_CHASSIS2</type>
          <bone>bumper_f</bone>
          <collisionBone />
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="0" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>harmann_orn_3</modelName>
          <modShopLabel>HARMANN_ORN3</modShopLabel>
          <linkedModels />
          <turnOffBones>
          </turnOffBones>
          <type>VMT_CHASSIS2</type>
          <bone>bumper_f</bone>
          <collisionBone />
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="0" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		
		<Item>
          <modelName>harmann_hlcover_1</modelName>
          <modShopLabel>HARMANN_HLC1</modShopLabel>
          <linkedModels />
          <turnOffBones>
		  	<item></item>
          </turnOffBones>
          <type>VMT_CHASSIS3</type>
          <bone>chassis</bone>
          <collisionBone />
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="0" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>harmann_hlcover_2</modelName>
          <modShopLabel>HARMANN_HLC2</modShopLabel>
          <linkedModels />
          <turnOffBones>
		  	<item></item>
          </turnOffBones>
          <type>VMT_CHASSIS3</type>
          <bone>chassis</bone>
          <collisionBone />
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="0" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>harmann_hlcover_3</modelName>
          <modShopLabel>HARMANN_HLC3</modShopLabel>
          <linkedModels />
          <turnOffBones>
		  	<item>headlight_l</item>
			<item>headlight_r</item>
			<item>indicator_lf</item>
			<item>indicator_rf</item>
          </turnOffBones>
          <type>VMT_CHASSIS3</type>
          <bone>chassis</bone>
          <collisionBone />
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="0" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		
		<Item>
          <modelName>harmann_roof_1</modelName>
          <modShopLabel>HARMANN_ROOF1</modShopLabel>
          <linkedModels />
          <turnOffBones>
			<item>misc_r</item>
          </turnOffBones>
          <type>VMT_ROOF</type>
          <bone>chassis</bone>
          <collisionBone />
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="0" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>harmann_roof_2</modelName>
          <modShopLabel>HARMANN_ROOF2</modShopLabel>
          <linkedModels />
          <turnOffBones>
			<item>misc_r</item>
			<item>misc_a</item>
			<item>misc_b</item>
          </turnOffBones>
          <type>VMT_ROOF</type>
          <bone>chassis</bone>
          <collisionBone />
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="0" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>harmann_roof_3</modelName>
          <modShopLabel>HARMANN_ROOF3</modShopLabel>
          <linkedModels />
          <turnOffBones>
			<item>misc_r</item>
			<item>misc_a</item>
			<item>misc_b</item>
          </turnOffBones>
          <type>VMT_ROOF</type>
          <bone>chassis</bone>
          <collisionBone />
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="0" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>harmann_roof_4</modelName>
          <modShopLabel>HARMANN_ROOF4</modShopLabel>
          <linkedModels />
          <turnOffBones>
			<item>misc_r</item>
			<item>misc_a</item>
			<item>misc_b</item>
          </turnOffBones>
          <type>VMT_ROOF</type>
          <bone>chassis</bone>
          <collisionBone />
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="0" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>harmann_roof_5</modelName>
          <modShopLabel>HARMANN_ROOF5</modShopLabel>
          <linkedModels />
          <turnOffBones>
			<item>misc_r</item>
			<item>misc_a</item>
			<item>misc_b</item>
          </turnOffBones>
          <type>VMT_ROOF</type>
          <bone>chassis</bone>
          <collisionBone />
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="0" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>harmann_roof_6</modelName>
          <modShopLabel>HARMANN_ROOF6</modShopLabel>
          <linkedModels />
          <turnOffBones>
			<item>misc_r</item>
			<item>misc_a</item>
			<item>misc_b</item>
          </turnOffBones>
          <type>VMT_ROOF</type>
          <bone>chassis</bone>
          <collisionBone />
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="0" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>harmann_roof_7</modelName>
          <modShopLabel>HARMANN_ROOF7</modShopLabel>
          <linkedModels />
          <turnOffBones>
			<item>misc_r</item>
			<item>misc_a</item>
			<item>misc_b</item>
          </turnOffBones>
          <type>VMT_ROOF</type>
          <bone>chassis</bone>
          <collisionBone />
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="0" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>harmann_roof_8</modelName>
          <modShopLabel>HARMANN_ROOF8</modShopLabel>
          <linkedModels />
          <turnOffBones>
			<item>misc_r</item>
			<item>misc_a</item>
			<item>misc_b</item>
          </turnOffBones>
          <type>VMT_ROOF</type>
          <bone>chassis</bone>
          <collisionBone />
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="0" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>harmann_roof_9</modelName>
          <modShopLabel>HARMANN_ROOF9</modShopLabel>
          <linkedModels />
          <turnOffBones>
			<item>misc_r</item>
			<item>misc_a</item>
			<item>misc_b</item>
          </turnOffBones>
          <type>VMT_ROOF</type>
          <bone>chassis</bone>
          <collisionBone />
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="0" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		
		<Item>
          <modelName>harmann_mirl_1</modelName>
          <modShopLabel>HARMANN_MIR1</modShopLabel>
          <linkedModels>
		  	<item>harmann_mirr_1</item>
		  </linkedModels>
          <turnOffBones>
			<item>misc_i</item>
			<item>misc_j</item>
          </turnOffBones>
          <type>VMT_WING_R</type>
          <bone>door_dside_f</bone>
          <collisionBone />
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="0" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>harmann_mirl_2</modelName>
          <modShopLabel>HARMANN_MIR2</modShopLabel>
          <linkedModels>
		  	<item>harmann_mirr_2</item>
		  </linkedModels>
          <turnOffBones>
			<item>misc_i</item>
			<item>misc_j</item>
          </turnOffBones>
          <type>VMT_WING_R</type>
          <bone>door_dside_f</bone>
          <collisionBone />
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="0" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>harmann_mirl_3</modelName>
          <modShopLabel>HARMANN_MIR3</modShopLabel>
          <linkedModels>
		  	<item>harmann_mirr_3</item>
		  </linkedModels>
          <turnOffBones>
			<item>misc_i</item>
			<item>misc_j</item>
          </turnOffBones>
          <type>VMT_WING_R</type>
          <bone>door_dside_f</bone>
          <collisionBone />
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="0" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>harmann_mirl_4</modelName>
          <modShopLabel>HARMANN_MIR4</modShopLabel>
          <linkedModels>
		  	<item>harmann_mirr_4</item>
		  </linkedModels>
          <turnOffBones>
			<item>misc_i</item>
			<item>misc_j</item>
          </turnOffBones>
          <type>VMT_WING_R</type>
          <bone>door_dside_f</bone>
          <collisionBone />
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="0" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>harmann_mirl_5</modelName>
          <modShopLabel>HARMANN_MIR5</modShopLabel>
          <linkedModels>
		  	<item>harmann_mirr_5</item>
		  </linkedModels>
          <turnOffBones>
			<item>misc_i</item>
			<item>misc_j</item>
          </turnOffBones>
          <type>VMT_WING_R</type>
          <bone>door_dside_f</bone>
          <collisionBone />
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="0" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		
		<Item>
          <modelName>harmann_rspoiler_1</modelName>
          <modShopLabel>HARMANN_RSPL1</modShopLabel>
          <linkedModels />
          <turnOffBones>
          </turnOffBones>
          <type>VMT_WING_L</type>
          <bone>chassis</bone>
          <collisionBone />
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="0" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>harmann_rspoiler_2</modelName>
          <modShopLabel>HARMANN_RSPL2</modShopLabel>
          <linkedModels />
          <turnOffBones>
          </turnOffBones>
          <type>VMT_WING_L</type>
          <bone>chassis</bone>
          <collisionBone />
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="0" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>harmann_rspoiler_3</modelName>
          <modShopLabel>HARMANN_RSPL3</modShopLabel>
          <linkedModels />
          <turnOffBones>
          </turnOffBones>
          <type>VMT_WING_L</type>
          <bone>chassis</bone>
          <collisionBone />
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="0" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>harmann_rspoiler_4</modelName>
          <modShopLabel>HARMANN_RSPL4</modShopLabel>
          <linkedModels />
          <turnOffBones>
          </turnOffBones>
          <type>VMT_WING_L</type>
          <bone>chassis</bone>
          <collisionBone />
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="0" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		
		<Item>
          <modelName>harmann_rtrim_1</modelName>
          <modShopLabel>HARMANN_RTRIM1</modShopLabel>
          <linkedModels/>
          <turnOffBones>
			<item>misc_q</item>
          </turnOffBones>
          <type>VMT_CHASSIS4</type>
          <bone>chassis</bone>
          <collisionBone />
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="0" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>harmann_rtrim_2</modelName>
          <modShopLabel>HARMANN_RTRIM2</modShopLabel>
          <linkedModels/>
          <turnOffBones>
			<item>misc_q</item>
          </turnOffBones>
          <type>VMT_CHASSIS4</type>
          <bone>chassis</bone>
          <collisionBone />
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="0" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		
		<Item>
          <modelName>harmann_skirts_1</modelName>
          <modShopLabel>HARMANN_SKIRT1</modShopLabel>
          <linkedModels/>
          <turnOffBones>
          </turnOffBones>
          <type>VMT_SKIRT</type>
          <bone>chassis</bone>
          <collisionBone />
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="0" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>harmann_skirts_2</modelName>
          <modShopLabel>HARMANN_SKIRT2</modShopLabel>
          <linkedModels/>
          <turnOffBones>
          </turnOffBones>
          <type>VMT_SKIRT</type>
          <bone>chassis</bone>
          <collisionBone />
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="0" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>harmann_skirts_3</modelName>
          <modShopLabel>HARMANN_SKIRT3</modShopLabel>
          <linkedModels/>
          <turnOffBones>
			<item>misc_h</item>
          </turnOffBones>
          <type>VMT_SKIRT</type>
          <bone>chassis</bone>
          <collisionBone />
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="0" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>harmann_skirts_4</modelName>
          <modShopLabel>HARMANN_SKIRT4</modShopLabel>
          <linkedModels/>
          <turnOffBones>
			<item>misc_h</item>
          </turnOffBones>
          <type>VMT_SKIRT</type>
          <bone>chassis</bone>
          <collisionBone />
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="0" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>harmann_skirts_5</modelName>
          <modShopLabel>HARMANN_SKIRT5</modShopLabel>
          <linkedModels/>
          <turnOffBones>
			<item>misc_h</item>
          </turnOffBones>
          <type>VMT_SKIRT</type>
          <bone>chassis</bone>
          <collisionBone />
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="0" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>harmann_skirts_6</modelName>
          <modShopLabel>HARMANN_SKIRT6</modShopLabel>
          <linkedModels/>
          <turnOffBones>
			<item>misc_h</item>
          </turnOffBones>
          <type>VMT_SKIRT</type>
          <bone>chassis</bone>
          <collisionBone />
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="0" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		
		<Item>
          <modelName>harmann_spoiler_1</modelName>
          <modShopLabel>HARMANN_SPL1</modShopLabel>
          <linkedModels/>
          <turnOffBones>
          </turnOffBones>
          <type>VMT_SPOILER</type>
          <bone>boot</bone>
          <collisionBone />
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="0" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>harmann_spoiler_2</modelName>
          <modShopLabel>HARMANN_SPL2</modShopLabel>
          <linkedModels/>
          <turnOffBones>
          </turnOffBones>
          <type>VMT_SPOILER</type>
          <bone>boot</bone>
          <collisionBone />
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="0" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>harmann_spoiler_3</modelName>
          <modShopLabel>HARMANN_SPL3</modShopLabel>
          <linkedModels/>
          <turnOffBones>
          </turnOffBones>
          <type>VMT_SPOILER</type>
          <bone>boot</bone>
          <collisionBone />
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="0" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>harmann_spoiler_4</modelName>
          <modShopLabel>HARMANN_SPL4</modShopLabel>
          <linkedModels/>
          <turnOffBones>
          </turnOffBones>
          <type>VMT_SPOILER</type>
          <bone>boot</bone>
          <collisionBone />
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="0" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>harmann_spoiler_5</modelName>
          <modShopLabel>HARMANN_SPL5</modShopLabel>
          <linkedModels/>
          <turnOffBones>
          </turnOffBones>
          <type>VMT_SPOILER</type>
          <bone>boot</bone>
          <collisionBone />
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="0" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>harmann_spoiler_6</modelName>
          <modShopLabel>HARMANN_SPL6</modShopLabel>
          <linkedModels/>
          <turnOffBones>
          </turnOffBones>
          <type>VMT_SPOILER</type>
          <bone>boot</bone>
          <collisionBone />
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="0" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>harmann_spoiler_7</modelName>
          <modShopLabel>HARMANN_SPL7</modShopLabel>
          <linkedModels/>
          <turnOffBones>
          </turnOffBones>
          <type>VMT_SPOILER</type>
          <bone>boot</bone>
          <collisionBone />
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="0" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>harmann_spoiler_8</modelName>
          <modShopLabel>HARMANN_SPL8</modShopLabel>
          <linkedModels/>
          <turnOffBones>
          </turnOffBones>
          <type>VMT_SPOILER</type>
          <bone>boot</bone>
          <collisionBone />
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="0" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>harmann_spoiler_9</modelName>
          <modShopLabel>HARMANN_SPL9</modShopLabel>
          <linkedModels/>
          <turnOffBones>
          </turnOffBones>
          <type>VMT_SPOILER</type>
          <bone>boot</bone>
          <collisionBone />
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="0" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>harmann_spoiler_10</modelName>
          <modShopLabel>HARMANN_SPL10</modShopLabel>
          <linkedModels/>
          <turnOffBones>
          </turnOffBones>
          <type>VMT_SPOILER</type>
          <bone>boot</bone>
          <collisionBone />
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="0" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>harmann_spoiler_11</modelName>
          <modShopLabel>HARMANN_SPL11</modShopLabel>
          <linkedModels/>
          <turnOffBones>
          </turnOffBones>
          <type>VMT_SPOILER</type>
          <bone>boot</bone>
          <collisionBone />
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="0" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>harmann_spoiler_12</modelName>
          <modShopLabel>HARMANN_SPL12</modShopLabel>
          <linkedModels/>
          <turnOffBones>
          </turnOffBones>
          <type>VMT_SPOILER</type>
          <bone>boot</bone>
          <collisionBone />
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="0" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		
		<Item>
          <modelName>harmann_tlcover_1b</modelName>
          <modShopLabel>HARMANN_TLC1</modShopLabel>
          <linkedModels>
		  		<item>harmann_tlcover_1s</item>
		  </linkedModels>
          <turnOffBones>
          </turnOffBones>
          <type>VMT_INTERIOR2</type>
          <bone>chassis</bone>
          <collisionBone />
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="0" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		
		<Item>
          <modelName>harmann_tltrim_1b</modelName>
          <modShopLabel>HARMANN_TLTRIM1</modShopLabel>
          <linkedModels>
		  		<item>harmann_tltrim_1s</item>
		  </linkedModels>
          <turnOffBones>
		  		<item>misc_d</item>
				<item>misc_e</item>
          </turnOffBones>
          <type>VMT_INTERIOR3</type>
          <bone>chassis</bone>
          <collisionBone />
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="0" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>harmann_tltrim_2b</modelName>
          <modShopLabel>HARMANN_TLTRIM2</modShopLabel>
          <linkedModels>
		  		<item>harmann_tltrim_2s</item>
		  </linkedModels>
          <turnOffBones>
		  		<item>misc_d</item>
				<item>misc_e</item>
          </turnOffBones>
          <type>VMT_INTERIOR3</type>
          <bone>chassis</bone>
          <collisionBone />
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="0" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		
		
		<Item>
          <modelName>harmann_wtrimb_1</modelName>
          <modShopLabel>HARMANN_WTRIM1</modShopLabel>
          <linkedModels>
		  		<item>harmann_wtrimlf_1</item>
				<item>harmann_wtrimrf_1</item>
				<item>harmann_wtrimlr_1</item>
				<item>harmann_wtrimrr_1</item>
		  </linkedModels>
          <turnOffBones>
		  		<item>misc_l</item>
				<item>misc_m</item>
				<item>misc_n</item>
				<item>misc_o</item>
				<item>misc_p</item>
          </turnOffBones>
          <type>VMT_CHASSIS</type>
          <bone>chassis</bone>
          <collisionBone />
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="0" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>harmann_wtrimb_2</modelName>
          <modShopLabel>HARMANN_WTRIM2</modShopLabel>
          <linkedModels>
		  		<item>harmann_wtrimlf_2</item>
				<item>harmann_wtrimrf_2</item>
				<item>harmann_wtrimlr_2</item>
				<item>harmann_wtrimrr_2</item>
		  </linkedModels>
          <turnOffBones>
		  		<item>misc_l</item>
				<item>misc_m</item>
				<item>misc_n</item>
				<item>misc_o</item>
				<item>misc_p</item>
          </turnOffBones>
          <type>VMT_CHASSIS</type>
          <bone>chassis</bone>
          <collisionBone />
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="0" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>

	<Item>
          <modelName>harmann_rbadge_1</modelName>
          <modShopLabel>HARMANN_RBADGE1</modShopLabel>
          <linkedModels>
		  </linkedModels>
          <turnOffBones>
		  	<item>misc_x</item>
		  </turnOffBones>
          <type>VMT_TRUNK</type>
          <bone>boot</bone>
          <collisionBone />
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="0" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>harmann_rbadge_2</modelName>
          <modShopLabel>HARMANN_RBADGE2</modShopLabel>
          <linkedModels>
		  </linkedModels>
          <turnOffBones>
		  	<item>misc_x</item>
		  </turnOffBones>
          <type>VMT_TRUNK</type>
          <bone>boot</bone>
          <collisionBone />
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="0" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>harmann_rbadge_3</modelName>
          <modShopLabel>HARMANN_RBADGE3</modShopLabel>
          <linkedModels>
		  </linkedModels>
          <turnOffBones>
		  	<item>misc_x</item>
		  </turnOffBones>
          <type>VMT_TRUNK</type>
          <bone>boot</bone>
          <collisionBone />
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="0" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
	
		<Item>
          <modelName>harmann_sidetrim_0a</modelName>
          <modShopLabel>HARMANN_STRIM0A</modShopLabel>
          <linkedModels>
		  </linkedModels>
          <turnOffBones/>
          <type>VMT_INTERIOR4</type>
          <bone>chassis</bone>
          <collisionBone />
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="0" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
	<Item>
          <modelName>harmann_sbadge_1</modelName>
          <modShopLabel>HARMANN_SBADGE1</modShopLabel>
          <linkedModels>
		  </linkedModels>
          <turnOffBones/>
          <type>VMT_INTERIOR4</type>
          <bone>chassis</bone>
          <collisionBone />
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="0" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>harmann_sbadge_2a</modelName>
          <modShopLabel>HARMANN_SBADGE2A</modShopLabel>
          <linkedModels>
		  </linkedModels>
          <turnOffBones/>
          <type>VMT_INTERIOR4</type>
          <bone>chassis</bone>
          <collisionBone />
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="0" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>harmann_sbadge_2b</modelName>
          <modShopLabel>HARMANN_SBADGE2B</modShopLabel>
          <linkedModels>
		  </linkedModels>
          <turnOffBones/>
          <type>VMT_INTERIOR4</type>
          <bone>chassis</bone>
          <collisionBone />
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="0" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>harmann_sbadge_2c</modelName>
          <modShopLabel>HARMANN_SBADGE2C</modShopLabel>
          <linkedModels>
		  </linkedModels>
          <turnOffBones/>
          <type>VMT_INTERIOR4</type>
          <bone>chassis</bone>
          <collisionBone />
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="0" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>harmann_sbadge_3a</modelName>
          <modShopLabel>HARMANN_SBADGE3A</modShopLabel>
          <linkedModels>
		  </linkedModels>
          <turnOffBones/>
          <type>VMT_INTERIOR4</type>
          <bone>chassis</bone>
          <collisionBone />
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="0" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>harmann_sbadge_3b</modelName>
          <modShopLabel>HARMANN_SBADGE3B</modShopLabel>
          <linkedModels>
		  </linkedModels>
          <turnOffBones/>
          <type>VMT_INTERIOR4</type>
          <bone>chassis</bone>
          <collisionBone />
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="0" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>harmann_sbadge_3c</modelName>
          <modShopLabel>HARMANN_SBADGE3C</modShopLabel>
          <linkedModels>
		  </linkedModels>
          <turnOffBones/>
          <type>VMT_INTERIOR4</type>
          <bone>chassis</bone>
          <collisionBone />
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="0" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>harmann_sidetrim_1a</modelName>
          <modShopLabel>HARMANN_STRIM1A</modShopLabel>
          <linkedModels>
		  </linkedModels>
          <turnOffBones/>
          <type>VMT_INTERIOR4</type>
          <bone>chassis</bone>
          <collisionBone />
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="0" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>harmann_sidetrim_1b</modelName>
          <modShopLabel>HARMANN_STRIM1B</modShopLabel>
          <linkedModels>
		  </linkedModels>
          <turnOffBones/>
          <type>VMT_INTERIOR4</type>
          <bone>chassis</bone>
          <collisionBone />
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="0" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>harmann_sidetrim_2a</modelName>
          <modShopLabel>HARMANN_STRIM2A</modShopLabel>
          <linkedModels>
		  </linkedModels>
          <turnOffBones/>
          <type>VMT_INTERIOR4</type>
          <bone>chassis</bone>
          <collisionBone />
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="0" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>harmann_sidetrim_2b</modelName>
          <modShopLabel>HARMANN_STRIM2B</modShopLabel>
          <linkedModels>
		  </linkedModels>
          <turnOffBones/>
          <type>VMT_INTERIOR4</type>
          <bone>chassis</bone>
          <collisionBone />
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="0" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>harmann_sidetrim_3a</modelName>
          <modShopLabel>HARMANN_STRIM3A</modShopLabel>
          <linkedModels>
		  		<item>harmann_sidetrim_3al</item>
		  		<item>harmann_sidetrim_3ar</item>
		  </linkedModels>
          <turnOffBones/>
          <type>VMT_INTERIOR4</type>
          <bone>chassis</bone>
          <collisionBone />
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="0" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>harmann_sidetrim_3b</modelName>
          <modShopLabel>HARMANN_STRIM3B</modShopLabel>
          <linkedModels>
		  		<item>harmann_sidetrim_3al</item>
		  		<item>harmann_sidetrim_3ar</item>
		  </linkedModels>
          <turnOffBones/>
          <type>VMT_INTERIOR4</type>
          <bone>chassis</bone>
          <collisionBone />
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="0" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		
		<Item>
          <modelName>harmann_sidetrimc_1a</modelName>
          <modShopLabel>HARMANN_STRIMC1A</modShopLabel>
          <linkedModels>
		  </linkedModels>
          <turnOffBones/>
          <type>VMT_INTERIOR4</type>
          <bone>chassis</bone>
          <collisionBone />
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="0" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>harmann_sidetrimc_1b</modelName>
          <modShopLabel>HARMANN_STRIMC1B</modShopLabel>
          <linkedModels>
		  </linkedModels>
          <turnOffBones/>
          <type>VMT_INTERIOR4</type>
          <bone>chassis</bone>
          <collisionBone />
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="0" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>harmann_sidetrimc_2a</modelName>
          <modShopLabel>HARMANN_STRIMC2A</modShopLabel>
          <linkedModels>
		  </linkedModels>
          <turnOffBones/>
          <type>VMT_INTERIOR4</type>
          <bone>chassis</bone>
          <collisionBone />
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="0" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>harmann_sidetrimc_2b</modelName>
          <modShopLabel>HARMANN_STRIMC2B</modShopLabel>
          <linkedModels>
		  </linkedModels>
          <turnOffBones/>
          <type>VMT_INTERIOR4</type>
          <bone>chassis</bone>
          <collisionBone />
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="0" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>harmann_sidetrimc_3a</modelName>
          <modShopLabel>HARMANN_STRIMC3A</modShopLabel>
          <linkedModels>
		  		<item>harmann_sidetrimc_3al</item>
		  		<item>harmann_sidetrimc_3ar</item>
		  </linkedModels>
          <turnOffBones/>
          <type>VMT_INTERIOR4</type>
          <bone>chassis</bone>
          <collisionBone />
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="0" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>harmann_sidetrimc_3b</modelName>
          <modShopLabel>HARMANN_STRIMC3B</modShopLabel>
          <linkedModels>
		  		<item>harmann_sidetrimc_3al</item>
		  		<item>harmann_sidetrimc_3ar</item>
		  </linkedModels>
          <turnOffBones/>
          <type>VMT_INTERIOR4</type>
          <bone>chassis</bone>
          <collisionBone />
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="0" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>

	<Item>
          <modelName>harmann_ledb_1</modelName>
          <modShopLabel>HARMANN_LED1</modShopLabel>
          <linkedModels>
		  		<item>harmann_ledlf_1</item>
				<item>harmann_ledrf_1</item>
				<item>harmann_ledlr_1</item>
				<item>harmann_ledrr_1</item>
		  </linkedModels>
          <turnOffBones>
		  		<item>misc_s</item>
				<item>misc_t</item>
				<item>misc_u</item>
				<item>misc_v</item>
				<item>misc_w</item>
          </turnOffBones>
          <type>VMT_ICE</type>
          <bone>chassis</bone>
          <collisionBone />
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="0" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>harmann_ledb_2</modelName>
          <modShopLabel>HARMANN_LED2</modShopLabel>
          <linkedModels>
		  		<item>harmann_ledlf_2</item>
				<item>harmann_ledrf_2</item>
				<item>harmann_ledlr_2</item>
				<item>harmann_ledrr_2</item>
		  </linkedModels>
          <turnOffBones>
		  		<item>misc_s</item>
				<item>misc_t</item>
				<item>misc_u</item>
				<item>misc_v</item>
				<item>misc_w</item>
          </turnOffBones>
          <type>VMT_ICE</type>
          <bone>chassis</bone>
          <collisionBone />
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="0" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>harmann_ledb_3</modelName>
          <modShopLabel>HARMANN_LED3</modShopLabel>
          <linkedModels>
		  		<item>harmann_ledlf_3</item>
				<item>harmann_ledrf_3</item>
				<item>harmann_ledlr_3</item>
				<item>harmann_ledrr_3</item>
		  </linkedModels>
          <turnOffBones>
		  		<item>misc_s</item>
				<item>misc_t</item>
				<item>misc_u</item>
				<item>misc_v</item>
				<item>misc_w</item>
          </turnOffBones>
          <type>VMT_ICE</type>
          <bone>chassis</bone>
          <collisionBone />
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="0" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>harmann_ledb_4</modelName>
          <modShopLabel>HARMANN_LED4</modShopLabel>
          <linkedModels>
		  		<item>harmann_ledlf_4</item>
				<item>harmann_ledrf_4</item>
				<item>harmann_ledlr_4</item>
				<item>harmann_ledrr_4</item>
		  </linkedModels>
          <turnOffBones>
		  		<item>misc_s</item>
				<item>misc_t</item>
				<item>misc_u</item>
				<item>misc_v</item>
				<item>misc_w</item>
          </turnOffBones>
          <type>VMT_ICE</type>
          <bone>chassis</bone>
          <collisionBone />
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="0" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>harmann_ledb_5</modelName>
          <modShopLabel>HARMANN_LED5</modShopLabel>
          <linkedModels>
		  		<item>harmann_ledlf_5</item>
				<item>harmann_ledrf_5</item>
				<item>harmann_ledlr_5</item>
				<item>harmann_ledrr_5</item>
		  </linkedModels>
          <turnOffBones>
		  		<item>misc_s</item>
				<item>misc_t</item>
				<item>misc_u</item>
				<item>misc_v</item>
				<item>misc_w</item>
          </turnOffBones>
          <type>VMT_ICE</type>
          <bone>chassis</bone>
          <collisionBone />
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="0" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>harmann_ledb_6</modelName>
          <modShopLabel>HARMANN_LED6</modShopLabel>
          <linkedModels>
		  		<item>harmann_ledlf_6</item>
				<item>harmann_ledrf_6</item>
				<item>harmann_ledlr_6</item>
				<item>harmann_ledrr_6</item>
		  </linkedModels>
          <turnOffBones>
		  		<item>misc_s</item>
				<item>misc_t</item>
				<item>misc_u</item>
				<item>misc_v</item>
				<item>misc_w</item>
          </turnOffBones>
          <type>VMT_ICE</type>
          <bone>chassis</bone>
          <collisionBone />
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="0" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>harmann_ledb_7</modelName>
          <modShopLabel>HARMANN_LED7</modShopLabel>
          <linkedModels>
		  		<item>harmann_ledlf_7</item>
				<item>harmann_ledrf_7</item>
				<item>harmann_ledlr_7</item>
				<item>harmann_ledrr_7</item>
		  </linkedModels>
          <turnOffBones>
		  		<item>misc_s</item>
				<item>misc_t</item>
				<item>misc_u</item>
				<item>misc_v</item>
				<item>misc_w</item>
          </turnOffBones>
          <type>VMT_ICE</type>
          <bone>chassis</bone>
          <collisionBone />
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="0" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>harmann_ledb_8</modelName>
          <modShopLabel>HARMANN_LED8</modShopLabel>
          <linkedModels>
		  		<item>harmann_ledlf_8</item>
				<item>harmann_ledrf_8</item>
				<item>harmann_ledlr_8</item>
				<item>harmann_ledrr_8</item>
		  </linkedModels>
          <turnOffBones>
		  		<item>misc_s</item>
				<item>misc_t</item>
				<item>misc_u</item>
				<item>misc_v</item>
				<item>misc_w</item>
          </turnOffBones>
          <type>VMT_ICE</type>
          <bone>chassis</bone>
          <collisionBone />
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="0" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>harmann_ledb_9</modelName>
          <modShopLabel>HARMANN_LED9</modShopLabel>
          <linkedModels>
		  		<item>harmann_ledlf_9</item>
				<item>harmann_ledrf_9</item>
				<item>harmann_ledlr_9</item>
				<item>harmann_ledrr_9</item>
		  </linkedModels>
          <turnOffBones>
		  		<item>misc_s</item>
				<item>misc_t</item>
				<item>misc_u</item>
				<item>misc_v</item>
				<item>misc_w</item>
          </turnOffBones>
          <type>VMT_ICE</type>
          <bone>chassis</bone>
          <collisionBone />
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="0" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>harmann_ledb_10</modelName>
          <modShopLabel>HARMANN_LED10</modShopLabel>
          <linkedModels>
		  		<item>harmann_ledlf_10</item>
				<item>harmann_ledrf_10</item>
				<item>harmann_ledlr_10</item>
				<item>harmann_ledrr_10</item>
		  </linkedModels>
          <turnOffBones>
		  		<item>misc_s</item>
				<item>misc_t</item>
				<item>misc_u</item>
				<item>misc_v</item>
				<item>misc_w</item>
          </turnOffBones>
          <type>VMT_ICE</type>
          <bone>chassis</bone>
          <collisionBone />
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="0" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>harmann_ledb_11</modelName>
          <modShopLabel>HARMANN_LED11</modShopLabel>
          <linkedModels>
		  		<item>harmann_ledlf_11</item>
				<item>harmann_ledrf_11</item>
				<item>harmann_ledlr_11</item>
				<item>harmann_ledrr_11</item>
		  </linkedModels>
          <turnOffBones>
		  		<item>misc_s</item>
				<item>misc_t</item>
				<item>misc_u</item>
				<item>misc_v</item>
				<item>misc_w</item>
          </turnOffBones>
          <type>VMT_ICE</type>
          <bone>chassis</bone>
          <collisionBone />
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="0" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>harmann_ledb_12</modelName>
          <modShopLabel>HARMANN_LED12</modShopLabel>
          <linkedModels>
		  		<item>harmann_ledlf_12</item>
				<item>harmann_ledrf_12</item>
				<item>harmann_ledlr_12</item>
				<item>harmann_ledrr_12</item>
		  </linkedModels>
          <turnOffBones>
		  		<item>misc_s</item>
				<item>misc_t</item>
				<item>misc_u</item>
				<item>misc_v</item>
				<item>misc_w</item>
          </turnOffBones>
          <type>VMT_ICE</type>
          <bone>chassis</bone>
          <collisionBone />
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="0" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>harmann_ledb_13</modelName>
          <modShopLabel>HARMANN_LED13</modShopLabel>
          <linkedModels>
		  		<item>harmann_ledlf_13</item>
				<item>harmann_ledrf_13</item>
				<item>harmann_ledlr_13</item>
				<item>harmann_ledrr_13</item>
		  </linkedModels>
          <turnOffBones>
		  		<item>misc_s</item>
				<item>misc_t</item>
				<item>misc_u</item>
				<item>misc_v</item>
				<item>misc_w</item>
          </turnOffBones>
          <type>VMT_ICE</type>
          <bone>chassis</bone>
          <collisionBone />
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="0" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>harmann_ledb_14</modelName>
          <modShopLabel>HARMANN_LED14</modShopLabel>
          <linkedModels>
		  		<item>harmann_ledlf_14</item>
				<item>harmann_ledrf_14</item>
				<item>harmann_ledlr_14</item>
				<item>harmann_ledrr_14</item>
		  </linkedModels>
          <turnOffBones>
		  		<item>misc_s</item>
				<item>misc_t</item>
				<item>misc_u</item>
				<item>misc_v</item>
				<item>misc_w</item>
          </turnOffBones>
          <type>VMT_ICE</type>
          <bone>chassis</bone>
          <collisionBone />
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="0" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
      </visibleMods>
      <linkMods>
        <Item>
          <modelName>harmann_sidetrim_3al</modelName>
          <bone>door_dside_f</bone>
          <turnOffExtra value="false" />
        </Item>
		<Item>
          <modelName>harmann_sidetrim_3ar</modelName>
          <bone>door_pside_f</bone>
          <turnOffExtra value="false" />
        </Item>
        <Item>
          <modelName>harmann_sidetrimc_3al</modelName>
          <bone>door_dside_f</bone>
          <turnOffExtra value="false" />
        </Item>
		<Item>
          <modelName>harmann_sidetrimc_3ar</modelName>
          <bone>door_pside_f</bone>
          <turnOffExtra value="false" />
        </Item>
		
		<Item>
          <modelName>harmann_wtrimlf_1</modelName>
          <bone>door_dside_f</bone>
          <turnOffExtra value="false" />
        </Item>
		<Item>
          <modelName>harmann_wtrimrf_1</modelName>
          <bone>door_pside_f</bone>
          <turnOffExtra value="false" />
        </Item>
		<Item>
          <modelName>harmann_wtrimlr_1</modelName>
          <bone>door_dside_r</bone>
          <turnOffExtra value="false" />
        </Item>
		<Item>
          <modelName>harmann_wtrimrr_1</modelName>
          <bone>door_pside_r</bone>
          <turnOffExtra value="false" />
        </Item>
		<Item>
          <modelName>harmann_wtrimlf_2</modelName>
          <bone>door_dside_f</bone>
          <turnOffExtra value="false" />
        </Item>
		<Item>
          <modelName>harmann_wtrimrf_2</modelName>
          <bone>door_pside_f</bone>
          <turnOffExtra value="false" />
        </Item>
		<Item>
          <modelName>harmann_wtrimlr_2</modelName>
          <bone>door_dside_r</bone>
          <turnOffExtra value="false" />
        </Item>
		<Item>
          <modelName>harmann_wtrimrr_2</modelName>
          <bone>door_pside_r</bone>
          <turnOffExtra value="false" />
        </Item>
		
		<Item>
          <modelName>harmann_tlcover_1s</modelName>
          <bone>boot</bone>
          <turnOffExtra value="false" />
        </Item>
		<Item>
          <modelName>harmann_tltrim_1s</modelName>
          <bone>boot</bone>
          <turnOffExtra value="false" />
        </Item>
		<Item>
          <modelName>harmann_tltrim_2s</modelName>
          <bone>boot</bone>
          <turnOffExtra value="false" />
        </Item>
		
		<Item>
          <modelName>harmann_mirr_1</modelName>
          <bone>door_pside_f</bone>
          <turnOffExtra value="false" />
        </Item>
		<Item>
          <modelName>harmann_mirr_2</modelName>
          <bone>door_pside_f</bone>
          <turnOffExtra value="false" />
        </Item>
		<Item>
          <modelName>harmann_mirr_3</modelName>
          <bone>door_pside_f</bone>
          <turnOffExtra value="false" />
        </Item>
		<Item>
          <modelName>harmann_mirr_4</modelName>
          <bone>door_pside_f</bone>
          <turnOffExtra value="false" />
        </Item>
		<Item>
          <modelName>harmann_mirr_5</modelName>
          <bone>door_pside_f</bone>
          <turnOffExtra value="false" />
        </Item>
		
	<Item>
          <modelName>harmann_ledlf_1</modelName>
          <bone>door_dside_f</bone>
          <turnOffExtra value="false" />
        </Item>
		<Item>
          <modelName>harmann_ledrf_1</modelName>
          <bone>door_pside_f</bone>
          <turnOffExtra value="false" />
        </Item>
		<Item>
          <modelName>harmann_ledlr_1</modelName>
          <bone>door_dside_r</bone>
          <turnOffExtra value="false" />
        </Item>
		<Item>
          <modelName>harmann_ledrr_1</modelName>
          <bone>door_pside_r</bone>
          <turnOffExtra value="false" />
        </Item>
		<Item>
          <modelName>harmann_ledlf_2</modelName>
          <bone>door_dside_f</bone>
          <turnOffExtra value="false" />
        </Item>
		<Item>
          <modelName>harmann_ledrf_2</modelName>
          <bone>door_pside_f</bone>
          <turnOffExtra value="false" />
        </Item>
		<Item>
          <modelName>harmann_ledlr_2</modelName>
          <bone>door_dside_r</bone>
          <turnOffExtra value="false" />
        </Item>
		<Item>
          <modelName>harmann_ledrr_2</modelName>
          <bone>door_pside_r</bone>
          <turnOffExtra value="false" />
        </Item>
		<Item>
          <modelName>harmann_ledlf_3</modelName>
          <bone>door_dside_f</bone>
          <turnOffExtra value="false" />
        </Item>
		<Item>
          <modelName>harmann_ledrf_3</modelName>
          <bone>door_pside_f</bone>
          <turnOffExtra value="false" />
        </Item>
		<Item>
          <modelName>harmann_ledlr_3</modelName>
          <bone>door_dside_r</bone>
          <turnOffExtra value="false" />
        </Item>
		<Item>
          <modelName>harmann_ledrr_3</modelName>
          <bone>door_pside_r</bone>
          <turnOffExtra value="false" />
        </Item>
		<Item>
          <modelName>harmann_ledlf_4</modelName>
          <bone>door_dside_f</bone>
          <turnOffExtra value="false" />
        </Item>
		<Item>
          <modelName>harmann_ledrf_4</modelName>
          <bone>door_pside_f</bone>
          <turnOffExtra value="false" />
        </Item>
		<Item>
          <modelName>harmann_ledlr_4</modelName>
          <bone>door_dside_r</bone>
          <turnOffExtra value="false" />
        </Item>
		<Item>
          <modelName>harmann_ledrr_4</modelName>
          <bone>door_pside_r</bone>
          <turnOffExtra value="false" />
        </Item>
		<Item>
          <modelName>harmann_ledlf_5</modelName>
          <bone>door_dside_f</bone>
          <turnOffExtra value="false" />
        </Item>
		<Item>
          <modelName>harmann_ledrf_5</modelName>
          <bone>door_pside_f</bone>
          <turnOffExtra value="false" />
        </Item>
		<Item>
          <modelName>harmann_ledlr_5</modelName>
          <bone>door_dside_r</bone>
          <turnOffExtra value="false" />
        </Item>
		<Item>
          <modelName>harmann_ledrr_5</modelName>
          <bone>door_pside_r</bone>
          <turnOffExtra value="false" />
        </Item>
		<Item>
          <modelName>harmann_ledlf_6</modelName>
          <bone>door_dside_f</bone>
          <turnOffExtra value="false" />
        </Item>
		<Item>
          <modelName>harmann_ledrf_6</modelName>
          <bone>door_pside_f</bone>
          <turnOffExtra value="false" />
        </Item>
		<Item>
          <modelName>harmann_ledlr_6</modelName>
          <bone>door_dside_r</bone>
          <turnOffExtra value="false" />
        </Item>
		<Item>
          <modelName>harmann_ledrr_6</modelName>
          <bone>door_pside_r</bone>
          <turnOffExtra value="false" />
        </Item>
		<Item>
          <modelName>harmann_ledlf_7</modelName>
          <bone>door_dside_f</bone>
          <turnOffExtra value="false" />
        </Item>
		<Item>
          <modelName>harmann_ledrf_7</modelName>
          <bone>door_pside_f</bone>
          <turnOffExtra value="false" />
        </Item>
		<Item>
          <modelName>harmann_ledlr_7</modelName>
          <bone>door_dside_r</bone>
          <turnOffExtra value="false" />
        </Item>
		<Item>
          <modelName>harmann_ledrr_7</modelName>
          <bone>door_pside_r</bone>
          <turnOffExtra value="false" />
        </Item>
		<Item>
          <modelName>harmann_ledlf_8</modelName>
          <bone>door_dside_f</bone>
          <turnOffExtra value="false" />
        </Item>
		<Item>
          <modelName>harmann_ledrf_8</modelName>
          <bone>door_pside_f</bone>
          <turnOffExtra value="false" />
        </Item>
		<Item>
          <modelName>harmann_ledlr_8</modelName>
          <bone>door_dside_r</bone>
          <turnOffExtra value="false" />
        </Item>
		<Item>
          <modelName>harmann_ledrr_8</modelName>
          <bone>door_pside_r</bone>
          <turnOffExtra value="false" />
        </Item>
		<Item>
          <modelName>harmann_ledlf_9</modelName>
          <bone>door_dside_f</bone>
          <turnOffExtra value="false" />
        </Item>
		<Item>
          <modelName>harmann_ledrf_9</modelName>
          <bone>door_pside_f</bone>
          <turnOffExtra value="false" />
        </Item>
		<Item>
          <modelName>harmann_ledlr_9</modelName>
          <bone>door_dside_r</bone>
          <turnOffExtra value="false" />
        </Item>
		<Item>
          <modelName>harmann_ledrr_9</modelName>
          <bone>door_pside_r</bone>
          <turnOffExtra value="false" />
        </Item>
		<Item>
          <modelName>harmann_ledlf_10</modelName>
          <bone>door_dside_f</bone>
          <turnOffExtra value="false" />
        </Item>
		<Item>
          <modelName>harmann_ledrf_10</modelName>
          <bone>door_pside_f</bone>
          <turnOffExtra value="false" />
        </Item>
		<Item>
          <modelName>harmann_ledlr_10</modelName>
          <bone>door_dside_r</bone>
          <turnOffExtra value="false" />
        </Item>
		<Item>
          <modelName>harmann_ledrr_10</modelName>
          <bone>door_pside_r</bone>
          <turnOffExtra value="false" />
        </Item>
		<Item>
          <modelName>harmann_ledlf_11</modelName>
          <bone>door_dside_f</bone>
          <turnOffExtra value="false" />
        </Item>
		<Item>
          <modelName>harmann_ledrf_11</modelName>
          <bone>door_pside_f</bone>
          <turnOffExtra value="false" />
        </Item>
		<Item>
          <modelName>harmann_ledlr_11</modelName>
          <bone>door_dside_r</bone>
          <turnOffExtra value="false" />
        </Item>
		<Item>
          <modelName>harmann_ledrr_11</modelName>
          <bone>door_pside_r</bone>
          <turnOffExtra value="false" />
        </Item>
		<Item>
          <modelName>harmann_ledlf_12</modelName>
          <bone>door_dside_f</bone>
          <turnOffExtra value="false" />
        </Item>
		<Item>
          <modelName>harmann_ledrf_12</modelName>
          <bone>door_pside_f</bone>
          <turnOffExtra value="false" />
        </Item>
		<Item>
          <modelName>harmann_ledlr_12</modelName>
          <bone>door_dside_r</bone>
          <turnOffExtra value="false" />
        </Item>
		<Item>
          <modelName>harmann_ledrr_12</modelName>
          <bone>door_pside_r</bone>
          <turnOffExtra value="false" />
        </Item>
		<Item>
          <modelName>harmann_ledlf_13</modelName>
          <bone>door_dside_f</bone>
          <turnOffExtra value="false" />
        </Item>
		<Item>
          <modelName>harmann_ledrf_13</modelName>
          <bone>door_pside_f</bone>
          <turnOffExtra value="false" />
        </Item>
		<Item>
          <modelName>harmann_ledlr_13</modelName>
          <bone>door_dside_r</bone>
          <turnOffExtra value="false" />
        </Item>
		<Item>
          <modelName>harmann_ledrr_13</modelName>
          <bone>door_pside_r</bone>
          <turnOffExtra value="false" />
        </Item>
		<Item>
          <modelName>harmann_ledlf_14</modelName>
          <bone>door_dside_f</bone>
          <turnOffExtra value="false" />
        </Item>
		<Item>
          <modelName>harmann_ledrf_14</modelName>
          <bone>door_pside_f</bone>
          <turnOffExtra value="false" />
        </Item>
		<Item>
          <modelName>harmann_ledlr_14</modelName>
          <bone>door_dside_r</bone>
          <turnOffExtra value="false" />
        </Item>
		<Item>
          <modelName>harmann_ledrr_14</modelName>
          <bone>door_pside_r</bone>
          <turnOffExtra value="false" />
        </Item>	
      </linkMods>
      <statMods>
        <Item>
          <identifier />
          <modifier value="25" />
          <audioApply value="1.000000" />
          <weight value="20" />
          <type>VMT_ENGINE</type>
        </Item>
        <Item>
          <identifier />
          <modifier value="50" />
          <audioApply value="1.000000" />
          <weight value="20" />
          <type>VMT_ENGINE</type>
        </Item>
        <Item>
          <identifier />
          <modifier value="75" />
          <audioApply value="1.000000" />
          <weight value="20" />
          <type>VMT_ENGINE</type>
        </Item>
        <Item>
          <identifier />
          <modifier value="100" />
          <audioApply value="1.000000" />
          <weight value="20" />
          <type>VMT_ENGINE</type>
        </Item>
        <Item>
          <identifier />
          <modifier value="25" />
          <audioApply value="1.000000" />
          <weight value="5" />
          <type>VMT_BRAKES</type>
        </Item>
        <Item>
          <identifier />
          <modifier value="50" />
          <audioApply value="1.000000" />
          <weight value="5" />
          <type>VMT_BRAKES</type>
        </Item>
        <Item>
          <identifier />
          <modifier value="100" />
          <audioApply value="1.000000" />
          <weight value="5" />
          <type>VMT_BRAKES</type>
        </Item>
        <Item>
          <identifier />
          <modifier value="25" />
          <audioApply value="1.000000" />
          <weight value="5" />
          <type>VMT_GEARBOX</type>
        </Item>
        <Item>
          <identifier />
          <modifier value="50" />
          <audioApply value="1.000000" />
          <weight value="5" />
          <type>VMT_GEARBOX</type>
        </Item>
        <Item>
          <identifier />
          <modifier value="100" />
          <audioApply value="1.000000" />
          <weight value="5" />
          <type>VMT_GEARBOX</type>
        </Item>
        <Item>
          <identifier />
          <modifier value="20" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_ARMOUR</type>
        </Item>
        <Item>
          <identifier />
          <modifier value="40" />
          <audioApply value="1.000000" />
          <weight value="10" />
          <type>VMT_ARMOUR</type>
        </Item>
        <Item>
          <identifier />
          <modifier value="60" />
          <audioApply value="1.000000" />
          <weight value="20" />
          <type>VMT_ARMOUR</type>
        </Item>
        <Item>
          <identifier />
          <modifier value="80" />
          <audioApply value="1.000000" />
          <weight value="30" />
          <type>VMT_ARMOUR</type>
        </Item>
        <Item>
          <identifier />
          <modifier value="100" />
          <audioApply value="1.000000" />
          <weight value="40" />
          <type>VMT_ARMOUR</type>
        </Item>
        <Item>
          <identifier />
          <modifier value="5" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_SUSPENSION</type>
        </Item>
        <Item>
          <identifier />
          <modifier value="10" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_SUSPENSION</type>
        </Item>
        <Item>
          <identifier />
          <modifier value="25" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_SUSPENSION</type>
        </Item>
        <Item>
          <identifier />
          <modifier value="35" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_SUSPENSION</type>
        </Item>
        <Item>
          <identifier>HORN_TRUCK</identifier>
          <modifier value="1766676233" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>HORN_COP</identifier>
          <modifier value="2904189469" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>HORN_CLOWN</identifier>
          <modifier value="2543206147" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>HORN_MUSICAL_1</identifier>
          <modifier value="1732399718" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>HORN_MUSICAL_2</identifier>
          <modifier value="2046162893" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>HORN_MUSICAL_3</identifier>
          <modifier value="2194999691" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>HORN_MUSICAL_4</identifier>
          <modifier value="2508304100" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>HORN_MUSICAL_5</identifier>
          <modifier value="3707223535" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
		<Item>
          <identifier>HORN_SAD_TROMBONE</identifier>
          <modifier value="632950117" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>MUSICAL_HORN_BUSINESS_1</identifier>
          <modifier value="3628534289" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>MUSICAL_HORN_BUSINESS_2</identifier>
          <modifier value="3892554122" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>MUSICAL_HORN_BUSINESS_3</identifier>
          <modifier value="4112892878" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>MUSICAL_HORN_BUSINESS_4</identifier>
          <modifier value="116877169" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>MUSICAL_HORN_BUSINESS_5</identifier>
          <modifier value="2684983719" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>MUSICAL_HORN_BUSINESS_6</identifier>
          <modifier value="2982690084" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
		</Item>
		<Item>
          <identifier>MUSICAL_HORN_BUSINESS_7</identifier>
          <modifier value="3203290992" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
		</Item>
		<Item>
          <identifier>DLC_BUSI2_C_MAJOR_NOTES_C0</identifier>
          <modifier value="771284519" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
		</Item>
		<Item>
          <identifier>DLC_BUSI2_C_MAJOR_NOTES_D0</identifier>
          <modifier value="2586621229" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
		</Item>
		<Item>
          <identifier>DLC_BUSI2_C_MAJOR_NOTES_E0</identifier>
          <modifier value="283386134" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
		</Item>
		<Item>
          <identifier>DLC_BUSI2_C_MAJOR_NOTES_F0</identifier>
          <modifier value="3884502400" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
		</Item>
		<Item>
          <identifier>DLC_BUSI2_C_MAJOR_NOTES_G0</identifier>
          <modifier value="265723083" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
		</Item>
		<Item>
          <identifier>DLC_BUSI2_C_MAJOR_NOTES_A0</identifier>
          <modifier value="1746883687" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
		</Item>
		<Item>
          <identifier>DLC_BUSI2_C_MAJOR_NOTES_B0</identifier>
          <modifier value="1919870950" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
		</Item>
		<Item>
          <identifier>DLC_BUSI2_C_MAJOR_NOTES_C1</identifier>
          <modifier value="1085277077" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
		</Item>
        <Item>
          <identifier>HIPSTER_HORN_1</identifier>
          <modifier value="444549672" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>HIPSTER_HORN_2</identifier>
          <modifier value="1603064898" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>HIPSTER_HORN_3</identifier>
          <modifier value="240366033" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>HIPSTER_HORN_4</identifier>
          <modifier value="960137118" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>INDEP_HORN_1</identifier>
          <modifier value="3572144790" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>INDEP_HORN_2</identifier>
          <modifier value="3801396714" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>INDEP_HORN_3</identifier>
          <modifier value="2843657151" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>INDEP_HORN_4</identifier>
          <modifier value="3341811489" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>LUXE_HORN_1</identifier>
          <modifier value="3199657341" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
    <Item>
          <identifier>LUXE_HORN_2</identifier>
          <modifier value="2900378064" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
    <Item>
          <identifier>LUXE_HORN_3</identifier>
          <modifier value="3956195248" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
		<!-- PREVIEW HORN - FOR MOD SHOP USE ONLY -->
		<Item>
          <identifier>LUXORY_HORN_1</identifier>
          <modifier value="676333254" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
		<!-- PREVIEW HORN - FOR MOD SHOP USE ONLY -->
		<Item>
          <identifier>LUXURY_HORN_2</identifier>
          <modifier value="2099578296" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
		<!-- PREVIEW HORN - FOR MOD SHOP USE ONLY -->
		<Item>
          <identifier>LUXURY_HORN_3</identifier>
          <modifier value="1373384483" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
		<Item>
          <identifier>ORGAN_HORN_LOOP_01</identifier>
          <modifier value="2916775806" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
		<!-- PREVIEW HORN - FOR MOD SHOP USE ONLY -->
		<Item>
          <identifier>ORGAN_HORN_LOOP_01_PREVIEW</identifier>
          <modifier value="3714706952" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
		<Item>
          <identifier>ORGAN_HORN_LOOP_02</identifier>
          <modifier value="2611860261" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
		<!-- PREVIEW HORN - FOR MOD SHOP USE ONLY -->
		<Item>
          <identifier>ORGAN_HORN_LOOP_02_PREVIEW</identifier>
          <modifier value="3206770359" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
		<Item>
          <identifier>LOWRIDER_HORN_1</identifier>
          <modifier value="310529291" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
		<!-- PREVIEW HORN - FOR MOD SHOP USE ONLY -->
		<Item>
          <identifier>LOWRIDER_HORN_1_PREVIEW</identifier>
          <modifier value="2965568987" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
		<Item>
          <identifier>LOWRIDER_HORN_2</identifier>
          <modifier value="55291550" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
		<!-- PREVIEW HORN - FOR MOD SHOP USE ONLY -->
		<Item>
          <identifier>LOWRIDER_HORN_2_PREVIEW</identifier>
          <modifier value="965054819" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
		<Item>
          <identifier>XM15_HORN_01</identifier>
          <modifier value="55862314" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
		<!-- PREVIEW HORN - FOR MOD SHOP USE ONLY -->
		<Item>
          <identifier>XM15_HORN_01_PREVIEW</identifier>
          <modifier value="2156743178" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>	
		<Item>
          <identifier>XM15_HORN_02</identifier>
          <modifier value="400002352" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
		<!-- PREVIEW HORN - FOR MOD SHOP USE ONLY -->
		<Item>
          <identifier>XM15_HORN_02_PREVIEW</identifier>
          <modifier value="897484282" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
		<Item>
          <identifier>XM15_HORN_03</identifier>
          <modifier value="560832604" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
		<!-- PREVIEW HORN - FOR MOD SHOP USE ONLY -->
		<Item>
          <identifier>XM15_HORN_03_PREVIEW</identifier>
          <modifier value="314232747" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
      </statMods>
      <slotNames>
		<Item>
          <slot>VMT_CHASSIS2</slot>
          <name>TOP_GB_ORN</name>
        </Item>
		<Item>
          <slot>VMT_CHASSIS4</slot>
          <name>TOP_GB_ROOFTRIM</name>
        </Item>
		<Item>
          <slot>VMT_INTERIOR2</slot>
          <name>TOP_GB_TLC</name>
        </Item>
		<Item>
          <slot>VMT_INTERIOR3</slot>
          <name>TOP_GB_TLTRIMS</name>
        </Item>
		<Item>
          <slot>VMT_INTERIOR4</slot>
          <name>TOP_GB_SIDETRIMS</name>
        </Item>
      </slotNames>
      <liveryNames />
    </Item>
  </Kits>
  
</CVehicleModelInfoVarGlobal>
