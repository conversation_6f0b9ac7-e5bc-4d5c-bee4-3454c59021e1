Citizen.CreateThread(function()
    AddTextEntry("GBHARMANN", "<PERSON>rmann")

    AddTextEntry("HARMANN_LIVERY_1", "Black Gradient")
	AddTextEntry("HARMANN_LIVERY_2", "Diamonds Are Forever")
	AddTextEntry("HARMANN_LIVERY_3", "Golden Gradient")
	AddTextEntry("HARMANN_LIVERY_4", "Silver Gradient")
	AddTextEntry("HARMANN_LIVERY_5", "Sport Fade Black")
	AddTextEntry("HARMANN_LIVERY_6", "Sport Fade White")
	AddTextEntry("HARMANN_LIVERY_7", "White Gradient")
	
	AddTextEntry("HARMANN_BON1", "Smooth Bonnet")
    AddTextEntry("HARMANN_BON2", "Middle Ridged Bonnet")
    AddTextEntry("HARMANN_BON3", "Carbon Middle Ridged Bonnet")
    AddTextEntry("HARMANN_BON4", "Narrow Ridged Bonnet")
    AddTextEntry("HARMANN_BON5", "Carbon Narrow Ridged Bonnet")
    AddTextEntry("HARMANN_BON6", "Wide Ridged Bonnet")
    AddTextEntry("HARMANN_BON7", "Carbon Wide Ridged Bonnet")
    AddTextEntry("HARMANN_BON8", "Simple Intake Bonnet")
    AddTextEntry("HARMANN_BON9", "Carbon Simple Intake Bonnet")
    AddTextEntry("HARMANN_BON10", "Raised Intake Bonnet")
    AddTextEntry("HARMANN_BON11", "Carbon Raised Intake Bonnet")
    AddTextEntry("HARMANN_BON12", "Sport Intake Bonnet")
    AddTextEntry("HARMANN_BON13", "Carbon Sport Intake Bonnet")
    AddTextEntry("HARMANN_BON14", "Tuner Bonnet")
    AddTextEntry("HARMANN_BON15", "Carbon Tuner Bonnet")
    
    AddTextEntry("HARMANN_BUMF1", "Aero Parts")
    AddTextEntry("HARMANN_BUMF2", "Aero Parts 2")
    AddTextEntry("HARMANN_BUMF3", "Chrome Sport Bumper")
    AddTextEntry("HARMANN_BUMF4", "Black Sport Bumper")
    AddTextEntry("HARMANN_BUMF5", "Carbon Sport Bumper")
    AddTextEntry("HARMANN_BUMF6", "Carbon Sport w/ Splitter")
    AddTextEntry("HARMANN_BUMF7", "Sport II Bumper")
    AddTextEntry("HARMANN_BUMF8", "Sport II w/ Splitter")
    AddTextEntry("HARMANN_BUMF9", "Luxury Bumper")
    AddTextEntry("HARMANN_BUMF10", "Luxury Bumper w/ Chrome Mesh")
    AddTextEntry("HARMANN_BUMF11", "Carbon Luxury Bumper")
    AddTextEntry("HARMANN_BUMF12", "Tuner Bumper")
    AddTextEntry("HARMANN_BUMF13", "Half Painted Tuner Bumper")
    AddTextEntry("HARMANN_BUMF14", "Painted Tuner Bumper")
    AddTextEntry("HARMANN_BUMF15", "Tuner Bumper II")
    AddTextEntry("HARMANN_BUMF16", "Half Painted Tuner Bumper II")
    AddTextEntry("HARMANN_BUMF17", "Painted Tuner Bumper II")
    
    AddTextEntry("HARMANN_BUMR1", "Remove Reflectors")
    AddTextEntry("HARMANN_BUMR2", "Carbon Stock Bumper")
    AddTextEntry("HARMANN_BUMR3", "Carbon Stock w/ Sport Exhaust")
    AddTextEntry("HARMANN_BUMR4", "Sport Bumper II")
    AddTextEntry("HARMANN_BUMR5", "Sport II w/ Aero")
    AddTextEntry("HARMANN_BUMR6", "Carbon Sport Bumper II")
    AddTextEntry("HARMANN_BUMR7", "Carbon Sport II w/ Aero")
    AddTextEntry("HARMANN_BUMR8", "Luxury Bumper")
    AddTextEntry("HARMANN_BUMR9", "Tuner Bumper")
    AddTextEntry("HARMANN_BUMR10", "Painted Tuner Bumper")
    AddTextEntry("HARMANN_BUMR11", "Vented Tuner Bumper")
    AddTextEntry("HARMANN_BUMR12", "Painted Vented Tuner Bumper")
    AddTextEntry("HARMANN_BUMR13", "Tuner Bumper II")
    AddTextEntry("HARMANN_BUMR14", "Painted Tuner Bumper II")
    AddTextEntry("HARMANN_BUMR15", "Vented Tuner Bumper II")
    AddTextEntry("HARMANN_BUMR16", "Painted Vented Tuner Bumper II")

    AddTextEntry("HARMANN_GRILL1", "Black Classic")
    AddTextEntry("HARMANN_GRILL2", "Painted Classic")
    AddTextEntry("HARMANN_GRILL3", "Chrome Basic")
    AddTextEntry("HARMANN_GRILL4", "Black Basic")
    AddTextEntry("HARMANN_GRILL5", "Painted Basic")
    AddTextEntry("HARMANN_GRILL6", "Chrome Modern")
    AddTextEntry("HARMANN_GRILL7", "Black Modern")
    AddTextEntry("HARMANN_GRILL8", "Painted Modern")
    AddTextEntry("HARMANN_GRILL9", "Chrome Luxury")
    AddTextEntry("HARMANN_GRILL10", "Black Luxury")
    AddTextEntry("HARMANN_GRILL11", "Painted Luxury")
    AddTextEntry("HARMANN_GRILL12", "Carbon Tuner")
    AddTextEntry("HARMANN_GRILL13", "Painted Tuner")
    AddTextEntry("HARMANN_GRILL14", "Full Carbon Tuner II")
    AddTextEntry("HARMANN_GRILL15", "Accented Tuner II")
    AddTextEntry("HARMANN_GRILL16", "Painted Tuner II")

    AddTextEntry("HARMANN_ORN1", "Chrome Hood Ornament")
    AddTextEntry("HARMANN_ORN2", "Black Hood Ornament")
    AddTextEntry("HARMANN_ORN3", "Painted Hood Ornament")

    AddTextEntry("HARMANN_HLC1", "Top Headlight Covers")
    AddTextEntry("HARMANN_HLC2", "Half Headlight Covers")
    AddTextEntry("HARMANN_HLC3", "Full Headlight Covers")

    AddTextEntry("HARMANN_ROOF1", "Black Roof")
    AddTextEntry("HARMANN_ROOF2", "Full Black Roof")
    AddTextEntry("HARMANN_ROOF3", "Full Black Roof w/ Starlight")
    AddTextEntry("HARMANN_ROOF4", "Painted Roof")
    AddTextEntry("HARMANN_ROOF5", "Painted Roof w/ Starlight")
    AddTextEntry("HARMANN_ROOF6", "Smooth Black Roof")
    AddTextEntry("HARMANN_ROOF7", "Smooth Black Roof w/ Starlight")
    AddTextEntry("HARMANN_ROOF8", "Smooth Painted Roof")
    AddTextEntry("HARMANN_ROOF9", "Smooth Painted Roof w/ Starlight")

    AddTextEntry("HARMANN_MIR1", "Smooth Black Mirrors")
    AddTextEntry("HARMANN_MIR2", "Smooth Carbon Mirrors")
    AddTextEntry("HARMANN_MIR3", "Ridged Mirrors")
    AddTextEntry("HARMANN_MIR4", "Black Ridged Mirrors")
    AddTextEntry("HARMANN_MIR5", "Carbon Ridged Mirrors")

    AddTextEntry("HARMANN_RSPL1", "Painted Roof Spoiler")
    AddTextEntry("HARMANN_RSPL2", "Carbon Roof Spoiler")
    AddTextEntry("HARMANN_RSPL3", "Sport Roof Spoiler")
    AddTextEntry("HARMANN_RSPL4", "Carbon Sport Roof Spoiler")

    AddTextEntry("HARMANN_RTRIM1", "Black Roof Trim")
    AddTextEntry("HARMANN_RTRIM2", "Painted Roof Trim")

    AddTextEntry("HARMANN_SKIRT1", "Tuner Skirts")
    AddTextEntry("HARMANN_SKIRT2", "Tuner Skirts II")
    AddTextEntry("HARMANN_SKIRT3", "Aftermarket Sideskirts I")
    AddTextEntry("HARMANN_SKIRT4", "Aftermarket Sideskirts II")
    AddTextEntry("HARMANN_SKIRT5", "Painted Aftermarket Sideskirts I")
    AddTextEntry("HARMANN_SKIRT6", "Painted Aftermarket Sideskirts II")

    AddTextEntry("HARMANN_SPL1", "OEM Ducktail")
    AddTextEntry("HARMANN_SPL2", "Carbon OEM Ducktail")
    AddTextEntry("HARMANN_SPL3", "Tuner Ducktail")
    AddTextEntry("HARMANN_SPL4", "Carbon Tuner Ducktail")
    AddTextEntry("HARMANN_SPL5", "Italian Ducktail")
    AddTextEntry("HARMANN_SPL6", "Carbon Italian Ducktail")
    AddTextEntry("HARMANN_SPL7", "Big Ducktail")
    AddTextEntry("HARMANN_SPL8", "Carbon Big Ducktail")
    AddTextEntry("HARMANN_SPL9", "Small Drag Wing")
    AddTextEntry("HARMANN_SPL10", "Carbon Small Drag Wing")
    AddTextEntry("HARMANN_SPL11", "Drag Wing")
    AddTextEntry("HARMANN_SPL12", "Carbon Drag Wing")

    AddTextEntry("HARMANN_TLC1", "Taillight Covers")

    AddTextEntry("HARMANN_TLTRIM1", "Black Taillight Trim")
    AddTextEntry("HARMANN_TLTRIM2", "Painted Taillight Trim")

    AddTextEntry("HARMANN_WTRIM1", "Black Window Trim")
    AddTextEntry("HARMANN_WTRIM2", "Painted Window Trim")

    AddTextEntry("HARMANN_STRIM0A", "STR Side Badges")
    AddTextEntry("HARMANN_SBADGE1", "V12 Side Badges")
    AddTextEntry("HARMANN_SBADGE2A", "Chrome C-Pillar Badges")
    AddTextEntry("HARMANN_SBADGE2B", "Black C-Pillar Badges")
    AddTextEntry("HARMANN_SBADGE2C", "Painted C-Pillar Badges")
    AddTextEntry("HARMANN_SBADGE3A", "V12 & Chrome C-Pillar Badges")
    AddTextEntry("HARMANN_SBADGE3B", "V12 & Black C-Pillar Badges")
    AddTextEntry("HARMANN_SBADGE3C", "V12 & Painted C-Pillar Badges")
    AddTextEntry("HARMANN_STRIM1A", "Gentle Side Addon 1")
    AddTextEntry("HARMANN_STRIM1B", "Sport Side Addon 1")
    AddTextEntry("HARMANN_STRIM2A", "Gentle Side Addon 2")
    AddTextEntry("HARMANN_STRIM2B", "Sport Side Addon 2")
    AddTextEntry("HARMANN_STRIM3A", "Gentle Side Addon 3")
    AddTextEntry("HARMANN_STRIM3B", "Sport Side Addon 3")
    AddTextEntry("HARMANN_STRIMC1A", "Carbon Gentle Side Addon 1")
    AddTextEntry("HARMANN_STRIMC1B", "Carbon Sport Side Addon 1")
    AddTextEntry("HARMANN_STRIMC2A", "Carbon Gentle Side Addon 2")
    AddTextEntry("HARMANN_STRIMC2B", "Carbon Sport Side Addon 2")
    AddTextEntry("HARMANN_STRIMC3A", "Carbon Gentle Side Addon 3")
    AddTextEntry("HARMANN_STRIMC3B", "Carbon Sport Side Addon 3")

    AddTextEntry("HARMANN_LED1", "Blue Ambient Lighting")
    AddTextEntry("HARMANN_LED2", "White Ambient Lighting")
    AddTextEntry("HARMANN_LED3", "Green Ambient Lighting")
    AddTextEntry("HARMANN_LED4", "Green Ambient Lighting 2")
    AddTextEntry("HARMANN_LED5", "Lime Ambient Lighting")
    AddTextEntry("HARMANN_LED6", "Yellow Ambient Lighting")
    AddTextEntry("HARMANN_LED7", "Orange Ambient Lighting")
    AddTextEntry("HARMANN_LED8", "Red Ambient Lighting")
    AddTextEntry("HARMANN_LED9", "Pink Ambient Lighting")
    AddTextEntry("HARMANN_LED10", "Purple Ambient Lighting")
    AddTextEntry("HARMANN_LED11", "Purple Gradient Lighting")
    AddTextEntry("HARMANN_LED12", "Green Gradient Lighting")
    AddTextEntry("HARMANN_LED13", "Orange Gradient Lighting")
    AddTextEntry("HARMANN_LED14", "Teal Gradient Lighting")
        
    AddTextEntry("TOP_GB_ORN", "Hood Ornaments")
    AddTextEntry("TOP_GB_ROOFTRIM", "Roof Trims")
    AddTextEntry("TOP_GB_TLC", "Taillight Covers")
    AddTextEntry("TOP_GB_TLTRIMS", "Taillight Trims")
    AddTextEntry("TOP_GB_SIDETRIMS", "Side Trims")

    AddTextEntry("HARMANN_RBADGE1", "Enus-Benefactor Rear Badge")
    AddTextEntry("HARMANN_RBADGE2", "STR Rear Badge")
    AddTextEntry("HARMANN_RBADGE3", "Debadged")
        
    AddTextEntry("HARMANN_PLTF1", "Front Plate")
    
    AddTextEntry("HARMANN_LIV1", "-")
end)
