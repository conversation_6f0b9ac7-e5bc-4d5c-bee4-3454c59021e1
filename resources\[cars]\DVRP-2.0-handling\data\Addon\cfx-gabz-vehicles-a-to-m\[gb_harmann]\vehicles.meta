<?xml version="1.0" encoding="UTF-8"?>
<CVehicleModelInfo__InitDataList>
  <residentTxd>vehshare</residentTxd>
  <residentAnims />
  <InitDatas>
  <Item>
      <modelName>g<PERSON>rmann</modelName>
      <txdName>g<PERSON>rmann</txdName>
      <handlingId>GBHARMANN</handlingId>
      <gameName>GBHARMANN</gameName>
      <vehicleMakeName>BENEFAC</vehicleMakeName>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected />
      <ptfxAssetName>null</ptfxAssetName>
      <audioNameHash />
      <layout>LAYOUT_STANDARD</layout>
      <coverBoundOffsets>DEITY_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout />
      <cameraName>FOLLOW_CHEETAH_CAMERA</cameraName>
      <aimCameraName>DEFAULT_THIRD_PERSON_VEHICLE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>VEHICLE_BONNET_CAMERA_STANDARD_LONG_REBLA</bonnetCameraName>
      <povCameraName>DEFAULT_POV_CAMERA_LOOKAROUND</povCameraName>
      <FirstPersonDriveByIKOffset x="0.060000" y="-0.07000" z="-0.040000" />
      <FirstPersonDriveByUnarmedIKOffset x="0.000000" y="-0.025000" z="0.000000" />
	  <FirstPersonProjectileDriveByIKOffset x="0.040000" y="-0.150000" z="-0.080000" />
	  <FirstPersonProjectileDriveByPassengerIKOffset x="-0.090000" y="-0.090000" z="-0.070000" />
	  <FirstPersonProjectileDriveByRearLeftIKOffset x="0.000000" y="-0.040000" z="-0.070000" />
	  <FirstPersonProjectileDriveByRearRightIKOffset x="0.000000" y="-0.040000" z="-0.070000" />
	  <FirstPersonDriveByLeftPassengerIKOffset x="0.040000" y="0.000000" z="-0.040000" />
	  <FirstPersonDriveByRightPassengerIKOffset x="-0.020000" y="-0.090000" z="-0.050000" />
	  <FirstPersonDriveByRightRearPassengerIKOffset x="-0.040000" y="0.020000" z="-0.020000" />
	  <FirstPersonDriveByLeftPassengerUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000" />
	  <FirstPersonDriveByRightPassengerUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000" />
	  <FirstPersonMobilePhoneOffset x="0.155000" y="0.273000" z="0.573000" />
      <FirstPersonPassengerMobilePhoneOffset x="0.205000" y="0.325000" z="0.445000" />
	  <FirstPersonMobilePhoneSeatIKOffset>
		<Item>
			<Offset x="0.136000" y="0.116000" z="0.485000" />
			<SeatIndex value="2" />
		</Item>
		<Item>
			<Offset x="0.136000" y="0.116000" z="0.485000" />
			<SeatIndex value="3" />
		</Item>
	  </FirstPersonMobilePhoneSeatIKOffset> 
      <PovCameraOffset x="-0.020000" y="-0.110000" z="0.620000" />
      <PovCameraVerticalAdjustmentForRollCage value="0.000000" />
      <PovPassengerCameraOffset x="0.000000" y="0.075000" z="0.000000" />
      <PovRearPassengerCameraOffset x="0.000000" y="-0.100000" z="0.000000" />
      <vfxInfoName>VFXVEHICLEINFO_CAR_GENERIC</vfxInfoName>
      <shouldUseCinematicViewMode value="true" />
      <shouldCameraTransitionOnClimbUpDown value="false" />
      <shouldCameraIgnoreExiting value="false" />
      <AllowPretendOccupants value="true" />
      <AllowJoyriding value="true" />
      <AllowSundayDriving value="true" />
      <AllowBodyColorMapping value="true" />
      <wheelScale value="0.320000" />
      <wheelScaleRear value="0.320000" />
      <dirtLevelMin value="0.000000" />
      <dirtLevelMax value="0.450000" />
      <envEffScaleMin value="0.000000" />
      <envEffScaleMax value="0.000000" />
      <envEffScaleMin2 value="0.000000" />
      <envEffScaleMax2 value="0.000000" />
      <damageMapScale value="0.600000" />
      <damageOffsetScale value="1.000000" />
      <diffuseTint value="0x00FFFFFF" />
      <steerWheelMult value="1.000000" />
      <HDTextureDist value="5.000000" />
      <lodDistances content="float_array">
        15.000000
        35.000000
        75.000000
        150.000000
        500.000000
        500.000000
      </lodDistances>
      <minSeatHeight value="0.925" />
      <identicalModelSpawnDistance value="20" />
      <maxNumOfSameColor value="10" />
      <defaultBodyHealth value="1000.000000" />
      <pretendOccupantsScale value="1.000000" />
      <visibleSpawnDistScale value="1.000000" />
      <trackerPathWidth value="2.000000" />
      <weaponForceMult value="1.000000" />
      <frequency value="5" />
      <swankness>SWANKNESS_5</swankness>
      <maxNum value="3" />
      <flags>FLAG_PARKING_SENSORS FLAG_RICH_CAR FLAG_NO_BROKEN_DOWN_SCENARIO FLAG_INCREASE_CAMBER_WITH_SUSPENSION_MOD</flags>
      <type>VEHICLE_TYPE_CAR</type>
      <plateType>VPT_FRONT_AND_BACK_PLATES</plateType>
	  <dashboardType>VDT_RACE</dashboardType>
      <vehicleClass>VC_SEDAN</vehicleClass>
      <wheelType>VWT_HIEND</wheelType>
      <trailers />
      <additionalTrailers />
      <drivers />
      <extraIncludes />
      <doorsWithCollisionWhenClosed />
      <driveableDoors />
      <bumpersNeedToCollideWithMap value="true" />
      <needsRopeTexture value="false" />
      <requiredExtras />
      <rewards />
      <cinematicPartCamera>
        <Item>WHEEL_FRONT_RIGHT_CAMERA</Item>
        <Item>WHEEL_FRONT_LEFT_CAMERA</Item>
        <Item>WHEEL_REAR_RIGHT_CAMERA</Item>
        <Item>WHEEL_REAR_LEFT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet />
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000" />
      <buoyancySphereSizeScale value="1.000000" />
      <pOverrideRagdollThreshold type="NULL" />
	  <firstPersonDrivebyData>
        <Item>STD_DEITY_FRONT_LEFT</Item>
        <Item>STD_DEITY_FRONT_RIGHT</Item>
        <Item>STD_DEITY_REAR_LEFT</Item>
        <Item>STD_DEITY_REAR_RIGHT</Item>
      </firstPersonDrivebyData>
    </Item>
 </InitDatas>
  <txdRelationships>
	<Item>
      <parent>vehshare</parent>
      <child>vehicles_gbharmann_interior</child>
    </Item>
    <Item>
      <parent>vehicles_gbharmann_interior</parent>
      <child>gbharmann</child>
    </Item>
  </txdRelationships>
</CVehicleModelInfo__InitDataList>
