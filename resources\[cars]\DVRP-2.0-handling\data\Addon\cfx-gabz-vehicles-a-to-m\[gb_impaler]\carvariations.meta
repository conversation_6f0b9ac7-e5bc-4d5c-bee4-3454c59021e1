<?xml version="1.0" encoding="UTF-8"?>

<CVehicleModelInfoVariation>
  <variationData>
    <Item>
      <modelName>gbimpaler</modelName>
      <colors>
        <Item>
          <indices content="char_array">
            4
            4
            4
            156
            8
            8
          </indices>
          <liveries />
        </Item>
        <Item>
          <indices content="char_array">
            111
            111
            111
            156
            8
            8
          </indices>
          <liveries />
        </Item>
        <Item>
          <indices content="char_array">
            0
            0
            0
            156
            8
            8
          </indices>
          <liveries />
        </Item>
        <Item>
          <indices content="char_array">
            34
            34
            34
            156
            8
            8
          </indices>
          <liveries />
        </Item>
        <Item>
          <indices content="char_array">
            65
            65
            65
            156
            8
            8
          </indices>
          <liveries />
        </Item>
        <Item>
          <indices content="char_array">
            110
            110
            136
            156
            8
            8
          </indices>
          <liveries />
        </Item>
        <Item>
          <indices content="char_array">
            62
            62
            62
            156
            8
            8
          </indices>
          <liveries />
        </Item>
        <Item>
          <indices content="char_array">
            96
            96
            34
            156
            8
            8
          </indices>
          <liveries />
        </Item>
      </colors>
      <kits>
        <Item>7332_gbimpaler_modkit</Item>
      </kits>
      <windowsWithExposedEdges/>
      <plateProbabilities>
        <Probabilities>
          <Item>
            <Name>Standard White</Name>
            <Value value="100"/>
          </Item>
        </Probabilities>
      </plateProbabilities>
      <lightSettings value="2"/>
      <sirenSettings value="0"/>
    </Item>
  </variationData>
</CVehicleModelInfoVariation>