Citizen.CreateThread(function()
AddTextEntry("GBIMPALER", "Impaler LE")

AddTextEntry("GBIMPALER_SPL1", "Ducktail Spoiler")
AddTextEntry("GBIMPALER_SPL2", "Stock Car Spoiler")
AddTextEntry("GBIMPALER_SPL3", "Mid Level Spoiler")
AddTextEntry("GBIMPALER_SPL4", "Carbon Wing")
AddTextEntry("GBIMPALER_SPL4A", "Painted Carbon Wing")
AddTextEntry("GBIMPALER_SPL5", "Drift Wing")
AddTextEntry("GBIMPALER_SPL6", "Race Wing")
AddTextEntry("GBIMPALER_SPL7", "Big Carbon Wing")
AddTextEntry("GBIMPALER_BUMF1", "Sport Front Bumper")
AddTextEntry("GBIMPALER_BUMR1", "Sport Rear Bumper")
AddTextEntry("GBIMPALER_SKIRT1", "Sport Skirts")
AddTextEntry("GBIMPALER_GRILL0A", "Chrome Grill")
AddTextEntry("GBIMPALER_GRILL0B", "Black Grill")
AddTextEntry("GBIMPALER_GRILL0C", "Painted Grill")
AddTextEntry("GBIMPALER_GRILL1", "Split Grill")
AddTextEntry("GBIMPALER_GRILL1A", "Chrome Split Grill")
AddTextEntry("GBIMPALER_GRILL1B", "Black Split Grill")
AddTextEntry("GBIMPALER_GRILL1C", "Painted Split Grill")
AddTextEntry("GBIMPALER_GRILL2", "Open Grill")
AddTextEntry("GBIMPALER_GRILL2A", "Black Open Grill")
AddTextEntry("GBIMPALER_GRILL2B", "Painted Open Grill")
AddTextEntry("GBIMPALER_HOOD1", "Sport Hood")
AddTextEntry("GBIMPALER_FVENT0A", "Chrome Bumper Inserts")
AddTextEntry("GBIMPALER_FVENT0B", "Black Bumper Inserts")
AddTextEntry("GBIMPALER_FVENT0C", "Painted Bumper Inserts")
AddTextEntry("GBIMPALER_FVENT1", "Sport Bumper Inserts")
AddTextEntry("GBIMPALER_FVENT1A", "Sport Chrome Bumper Inserts")
AddTextEntry("GBIMPALER_FVENT1B", "Sport Black Bumper Inserts")
AddTextEntry("GBIMPALER_FVENT1C", "Sport Painted Bumper Inserts")
AddTextEntry("GBIMPALER_FVENT2", "Open Bumper Inserts")
AddTextEntry("GBIMPALER_FVENT2A", "Open Chrome Bumper Inserts")
AddTextEntry("GBIMPALER_FVENT2B", "Open Black Bumper Inserts")
AddTextEntry("GBIMPALER_FVENT2C", "Open Painted Bumper Inserts")
AddTextEntry("GBIMPALER_RTRIM1", "Painted Rear Trim")
AddTextEntry("GBIMPALER_RTRIMREM", "Remove Rear Trim")

AddTextEntry("TOP_FVENT", "Front Bumper Inserts")

AddTextEntryByHash(GetHashKey('GBIMPALER_LIV1'), "White Athletic")
AddTextEntryByHash(GetHashKey('GBIMPALER_LIV2'), "Gray Athletic")
AddTextEntryByHash(GetHashKey('GBIMPALER_LIV3'), "Black Athletic")
AddTextEntryByHash(GetHashKey('GBIMPALER_LIV4'), "Red Athletic")
AddTextEntryByHash(GetHashKey('GBIMPALER_LIV5'), "White Pacesetter")
AddTextEntryByHash(GetHashKey('GBIMPALER_LIV6'), "Gray Pacesetter")
AddTextEntryByHash(GetHashKey('GBIMPALER_LIV7'), "Black Pacesetter")
AddTextEntryByHash(GetHashKey('GBIMPALER_LIV8'), "Hot Flames")
AddTextEntryByHash(GetHashKey('GBIMPALER_LIV9'), "Cool Flames")
AddTextEntryByHash(GetHashKey('GBIMPALER_LIV10'), "Yellow Fragment")
AddTextEntryByHash(GetHashKey('GBIMPALER_LIV11'), "Drone #22")
AddTextEntryByHash(GetHashKey('GBIMPALER_LIV12'), "King of Hearts")
AddTextEntryByHash(GetHashKey('GBIMPALER_LIV13'), "Blacktop Two-Tone")
AddTextEntryByHash(GetHashKey('GBIMPALER_LIV14'), "Silver Surf")
AddTextEntryByHash(GetHashKey('GBIMPALER_LIV15'), "Mors Mutual Insurance")
AddTextEntryByHash(GetHashKey('GBIMPALER_LIV16'), "Gruppe Sechs")
AddTextEntryByHash(GetHashKey('GBIMPALER_LIV17'), "Merryweather Security")
end)