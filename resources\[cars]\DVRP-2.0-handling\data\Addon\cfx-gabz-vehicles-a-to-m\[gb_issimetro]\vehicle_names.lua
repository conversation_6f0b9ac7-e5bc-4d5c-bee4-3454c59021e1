Citizen.CreateThread(function()
AddTextEntry("GBISSIM", "Issi Metro")

AddTextEntry("ISSIMETRO_SPOILER_1", "Aftermarket Spoiler")
AddTextEntry("ISSIMETRO_SPOILER_2", "<PERSON><PERSON>y Spoiler")
AddTextEntry("ISSIMETRO_SPOILER_3", "Signature Spoiler")
AddTextEntry("ISSIMETRO_SPOILER_4", "Signature Split Spoiler")
AddTextEntry("ISSIMETRO_SPOILER_5", "Competition Spoiler")

AddTextEntry("ISSIMETRO_SKIRT_1", "Primary Skirts")
AddTextEntry("ISSIMETRO_SKIRT_2", "Aftermarket Skirts")
AddTextEntry("ISSIMETRO_SKIRT_3", "Signature Skirts")

AddTextEntry("ISSIMETRO_HOOD_1", "Sports Hood")
AddTextEntry("ISSIMETRO_HOOD_2", "Primary Sports Hood")
AddTextEntry("ISSIMETRO_HOOD_3", "Secondary Sports Hood")
AddTextEntry("ISSIMETRO_HOOD_4", "Rally Hood")

AddTextEntry("ISSIMETRO_FENDER_1", "Painted Fenders")
AddTextEntry("ISSIMETRO_FENDER_2", "Signature Fenders")

AddTextEntry("ISSIMETRO_FBUMPER_1", "Primary Front Bumper")
AddTextEntry("ISSIMETRO_FBUMPER_2", "Signature Front Bumper")
AddTextEntry("ISSIMETRO_FBUMPER_3", "Painted Signature Front Bumper")
AddTextEntry("ISSIMETRO_FBUMPER_4", "Competition Front Bumper")

AddTextEntry("ISSIMETRO_GRILLE_1", "Weeny Grille")
AddTextEntry("ISSIMETRO_GRILLE_2", "Painted Grille")
AddTextEntry("ISSIMETRO_GRILLE_3", "Signature Grille")

AddTextEntry("ISSIMETRO_LIV1", "Finish Line (White)")
AddTextEntry("ISSIMETRO_LIV2", "Finish Line (Black)")
AddTextEntry("ISSIMETRO_LIV3", "Checkerboard")
AddTextEntry("ISSIMETRO_LIV4", "Union Jack")
AddTextEntry("ISSIMETRO_LIV5", "Union Jack Monochrome")
AddTextEntry("ISSIMETRO_LIV6", "Stickerbomb")
AddTextEntry("ISSIMETRO_LIV7", "Tamarro Racing")

end)
