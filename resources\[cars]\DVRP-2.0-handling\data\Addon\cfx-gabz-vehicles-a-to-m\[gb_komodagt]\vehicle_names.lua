Citizen.CreateThread(function()
AddTextEntry("GBKOMODAGT", "Komoda GT")
AddTextEntry("KOMODAGT_LIV1", "<PERSON><PERSON>'s Lager #33")
AddTextEntry("KOMODAGT_LIV2", "Lampadati-Powermetal #92")
AddTextEntry("KOMODAGT_LIV3", "Stronzo Team #16")
AddTextEntry("KOMODAGT_LIV4", "Akedo Rally Team #47")
AddTextEntry("KOMODAGT_LIV5", "Komoda Decals Black")
AddTextEntry("KOMODAGT_LIV6", "Komoda Decals Grey")
AddTextEntry("KOMODAGT_LIV7", "Komoda Decals White")
AddTextEntry("KOMODAGT_LIV8", "Miasma Rallye")
AddTextEntry("KOMODAGT_LIV9", "Speciale Italiano")
AddTextEntry("KOMODAGT_LIV10", "Tricolor")
AddTextEntry("KOMODAGT_LIV11", "Pinstripes Black")
AddTextEntry("KOMODAGT_LIV12", "Pinstripes Grey")
AddTextEntry("KOMODAGT_LIV13", "Pinstripes White")
AddTextEntry("KOMODAGT_LIV14", "Pinstripes Red")
AddTextEntry("KOMODAGT_LIV15", "Pinstripes Yellow")
AddTextEntry("KOMODAGT_LIV16", "Pinstripes Green")
AddTextEntry("KOMODAGT_LIV17", "Pinstripes Blue")
AddTextEntry("KOMODAGT_LIV18", "Pinstripes Purple")
AddTextEntry("KOMODA_EUROP_R", "Euro Rear Plate")
AddTextEntry("KOMODA_EUROP_R_NULL", "Remove Rear Plate")
AddTextEntry("KOMODA_SEAT1", "Sport Seats")
AddTextEntry("KOMODA_SEAT2", "Racing Seats #1")
AddTextEntry("KOMODA_SEAT3", "Racing Seats #2")
AddTextEntry("KOMODA_SEAT4", "Racing Seats #3")
AddTextEntry("KOMODA_SEAT5", "Racing Seats #4")
AddTextEntry("KOMODA_SEAT6", "Racing Seats #5")
AddTextEntry("KOMODA_SEAT7", "Racing Seats #6")
AddTextEntry("KOMODA_SEAT8", "Carbon Rcaing Seats")
AddTextEntry("KOMODA_ROLLCAGE1", "Rollcage")
AddTextEntry("KOMODA_ROOF1", "Roof Racks")
AddTextEntry("KOMODA_FOGS1", "Foglights Under Bumper")
AddTextEntry("KOMODA_FOGS2", "Foglights Over Bumper")
AddTextEntry("KOMODA_FOGS3", "Complete Foglight Set")
AddTextEntry("KOMODA_FOGS4", "Rally Foglights")
AddTextEntry("KOMODA_FOGS5", "Covered Rally Foglights")
AddTextEntry("KOMODA_SUNSTRIP", "Sunstrip")
AddTextEntry("KOMODA_HOOD1", "Lean Bonnet")
AddTextEntry("KOMODA_HOODSCOOP1", "Bonnet Scoop")
AddTextEntry("KOMODA_SPOIL1", "Plastic Spoiler")
AddTextEntry("KOMODA_SPOIL2", "Painted Sport Spoiler")
AddTextEntry("KOMODA_BUMPF0", "Plate Delete")
AddTextEntry("KOMODA_BUMPF0ALT", "Stock Bumper w/ Euro Plate")
AddTextEntry("KOMODA_BUMPF0ALT2", "Plastic Euro Bumper")
AddTextEntry("KOMODA_BUMPF0ALT3", "Plastic Euro Bumper w/ Euro Plate")
AddTextEntry("KOMODA_BUMPF0ALT4", "Plastic Euro Bumper w/ American Plate")
AddTextEntry("KOMODA_BUMPF1", "Painted Euro Bumper")
AddTextEntry("KOMODA_BUMPF2", "Painted USDM Bumper")
AddTextEntry("KOMODA_BUMPF3", "Painted Euro Bumper w/ Euro Plate")
AddTextEntry("KOMODA_BUMPF4", "Painted USDM Bumper w/ Euro Plate")
AddTextEntry("KOMODA_BUMPF5", "Painted USDM Bumper w/ American Plate")
AddTextEntry("KOMODA_BUMPF6", "Painted Euro Bumper w/ American Plate")
AddTextEntry("KOMODA_BUMPR0", "Plastic Euro Bumper")
AddTextEntry("KOMODA_BUMPR1", "Painted Euro Bumper")
AddTextEntry("KOMODA_BUMPR2", "Painted USDM Bumper")
AddTextEntry("KOMODA_BODY1", "Painted Valances")
AddTextEntry("KOMODA_BODY2", "Racing Bodykit")
AddTextEntry("KOMODA_SKIRT1", "Painted Skirt")
AddTextEntry("KOMODA_EX1", "Racing Exhaust")
AddTextEntry("KOMODA_TRIM_NULL", "Remove Plastic Trim")
AddTextEntry("KOMODA_TRIM_MAIN", "Painted Window Trim")
AddTextEntry("KOMODA_TAPE1", "Cross Covers")
AddTextEntry("KOMODA_TAPE2", "Rotated Cross Covers")
AddTextEntry("KOMODA_TAPE3", "Full Headlight Covers")
AddTextEntry("KOMODA_MUD1", "Mudflaps")
AddTextEntry("KOMODA_ANTENNA1", "Roof Antenna")
end)