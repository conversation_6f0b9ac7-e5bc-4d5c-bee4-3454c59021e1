Citizen.CreateThread(function()
AddTextEntry("GBMILANO", "Milano GTS")

AddTextEntry("MILANO_SPL1", "GT Wing")
AddTextEntry("MILANO_SPL2", "GT Wing MK.2")
AddTextEntry("MILANO_SPL3", "Competition Spoiler")
AddTextEntry("MILANO_SPL4", "Competition Package")
AddTextEntry("MILANO_BUMF0A", "Splitter")
AddTextEntry("MILANO_BUMF0B", "Carbon Splitter")
AddTextEntry("MILANO_BUMF1", "Sport Bumper")
AddTextEntry("MILANO_BUMF1A", "Sport w/ Splitter")
AddTextEntry("MILANO_BUMF1B", "Sport w/ C. Splitter")
AddTextEntry("MILANO_BUMF2", "GT Bumper")
AddTextEntry("MILANO_BUMF2A", "GT w/ Splitter")
AddTextEntry("MILANO_BUMF2B", "GT w/ C. Splitter")
AddTextEntry("MILANO_BUMF3", "Competition Bumper")
AddTextEntry("MILANO_BUMF3A", "Accent Competition Bumper")
AddTextEntry("MILANO_BUMF4", "Vented Competition Bumper")
AddTextEntry("MILANO_BUMF4A", "Vented Accent Competition Bumper")
AddTextEntry("MILANO_SKIRT0A", "Carbon Skirts")
AddTextEntry("MILANO_SKIRT0B", "Accent Skirts")
AddTextEntry("MILANO_SKIRT0C", "Carbon Accent Skirts")
AddTextEntry("MILANO_SKIRT1", "Competition Skirts")
AddTextEntry("MILANO_SKIRT1A", "Carbon Competition Skirts")
AddTextEntry("MILANO_SKIRT1B", "Accent Competition Skirts")
AddTextEntry("MILANO_SKIRT1C", "Carbon Accent Competition Skirts")
AddTextEntry("MILANO_BUMR0A", "Carbon Bumper")
AddTextEntry("MILANO_BUMR1", "Sport Bumper")
AddTextEntry("MILANO_BUMR1A", "Carbon Sport Bumper")
AddTextEntry("MILANO_BUMR2", "Competition Bumper")
AddTextEntry("MILANO_BUMR2A", "Carbon Competition Bumper")
AddTextEntry("MILANO_BUMR2B", "Accent Competition Bumper")
AddTextEntry("MILANO_BUMR2C", "Carbon Accent Competition Bumper")
AddTextEntry("MILANO_HOOD1", "Vented Hood")
AddTextEntry("MILANO_HOOD2", "Sport Hood")
AddTextEntry("MILANO_HOOD3", "GT Hood")
AddTextEntry("MILANO_HOOD4", "Competition Hood")
AddTextEntry("MILANO_FIN1", "Side Fins")
AddTextEntry("MILANO_FIN2", "Carbon Side Fins")
AddTextEntry("MILANO_MIR", "Carbon Mirrors")

AddTextEntry("MILANO_LIV1", "Classic White")
AddTextEntry("MILANO_LIV2", "Classic Yellow")
AddTextEntry("MILANO_LIV3", "Classic Red")
AddTextEntry("MILANO_LIV4", "Classic Blue")
AddTextEntry("MILANO_LIV5", "Classic Black")
AddTextEntry("MILANO_LIV6", "White Stripes")
AddTextEntry("MILANO_LIV7", "Yellow Stripes")
AddTextEntry("MILANO_LIV8", "Red Stripes")
AddTextEntry("MILANO_LIV9", "Blue Stripes")
AddTextEntry("MILANO_LIV10", "Gray Stripes")
AddTextEntry("MILANO_LIV11", "Champione White")
AddTextEntry("MILANO_LIV12", "Champione Yellow")
AddTextEntry("MILANO_LIV13", "Champione Red")
AddTextEntry("MILANO_LIV14", "Course 8")
AddTextEntry("MILANO_LIV15", "Course 15")
AddTextEntry("MILANO_LIV16", "Sketched Lines")
AddTextEntry("MILANO_LIV17", "Italian Stripes")
AddTextEntry("MILANO_LIV18", "French Stripes")
AddTextEntry("MILANO_LIV19", "Sogno Americano Blue")
AddTextEntry("MILANO_LIV20", "Sogno Americano White")
end)