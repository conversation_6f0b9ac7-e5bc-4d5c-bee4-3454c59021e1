Citizen.CreateThread(function()
	AddTextEntry("GBMOGULRS", "Mogul RS")
	AddTextEntry("GBMOGULRS_WING0", "Remove Spoiler")
	AddTextEntry("GBMOGULRS_WING0A", "Carbon Stock Spoiler")
	AddTextEntry("GBMOGULRS_WING1", "Rally Spoiler")
	AddTextEntry("GBMOGULRS_WING1A", "Carbon Rally Spoiler")
	AddTextEntry("GBMOGULRS_WING2", "Low Level Spoiler")
	AddTextEntry("GBMOGULRS_WING3", "Low Level Wing")
	AddTextEntry("GBMOGULRS_WING4", "X-Mount Wing")
	AddTextEntry("GBMOGULRS_WING5", "Intermediate Wing")
	AddTextEntry("GBMOGULRS_WING6", "Drift Wing")
	AddTextEntry("GBMOGULRS_WING7", "Mid Level Wing")
	AddTextEntry("GBMOG<PERSON><PERSON>S_WING8", "Track Wing")
	AddTextEntry("GBMOGULRS_WING9", "High Level Wing")
	AddTextEntry("GBMOGULRS_WING10", "Circuit Wing")
	AddTextEntry("GBMOGULRS_WING11", "Extreme Street Spoiler")
	AddTextEntry("GBMOGULRS_WING12", "Time Attack Wing")
	AddTextEntry("GBMOGULRS_WING13", "Chassis Mount Wing")
	AddTextEntry("GBMOGULRS_BUMF0A", "Exposed Intercooler")
	AddTextEntry("GBMOGULRS_BUMF0B", "Carbon Lip")
	AddTextEntry("GBMOGULRS_BUMF0C", "Carbon Lip w/ Cooler")
	AddTextEntry("GBMOGULRS_BUMF1", "Smooth Bumper")
	AddTextEntry("GBMOGULRS_BUMF1A", "Smooth Bumper w/ Cooler")
	AddTextEntry("GBMOGULRS_BUMF1B", "Carbon Smooth Bumper")
	AddTextEntry("GBMOGULRS_BUMF1C", "Carbon Smooth Bumper w/ Cooler")
	AddTextEntry("GBMOGULRS_BUMF2", "Tuner Bumper")
	AddTextEntry("GBMOGULRS_BUMF2A", "Tuner Bumper w/ Cooler")
	AddTextEntry("GBMOGULRS_BUMF2B", "Carbon Tuner Bumper")
	AddTextEntry("GBMOGULRS_BUMF2C", "Carbon Tuner Bumper w/ Cooler")
	AddTextEntry("GBMOGULRS_BUMF3", "Track Bumper")
	AddTextEntry("GBMOGULRS_BUMF3A", "Track Bumper w/ Cooler")
	AddTextEntry("GBMOGULRS_BUMF3B", "Carbon Track Bumper")
	AddTextEntry("GBMOGULRS_BUMF3C", "Carbon Track Bumper w/ Cooler")
	AddTextEntry("GBMOGULRS_BUMF4", "Street Bumper")
	AddTextEntry("GBMOGULRS_BUMF4A", "Street Bumper w/ Cooler")
	AddTextEntry("GBMOGULRS_BUMF5", "Rally Bumper")
	AddTextEntry("GBMOGULRS_BUMF5A", "Rally Bumper w/ Cooler")
	AddTextEntry("GBMOGULRS_BUMR1", "Street Bumper")
	AddTextEntry("GBMOGULRS_BUMRN", "Remove Bumper")
	AddTextEntry("GBMOGULRS_DIFF1", "Diffuser")
	AddTextEntry("GBMOGULRS_DIFF1N", "Diffuser w/ Remove")
	AddTextEntry("GBMOGULRS_DIFF2", "Diffuser MK2")
	AddTextEntry("GBMOGULRS_DIFF2N", "Diffuser MK2 w/ Remove")
	AddTextEntry("GBMOGULRS_SKIRT1", "Street Skirt")
	AddTextEntry("GBMOGULRS_SKIRT2", "Rally Skirt")
	AddTextEntry("GBMOGULRS_EXH5", "Race Exhaust")
	AddTextEntry("GBMOGULRS_EXH1A", "Alt. Chrome Tip Exhaust")
	AddTextEntry("GBMOGULRS_EXH2A", "Alt. Titanium Exhaust")
	AddTextEntry("GBMOGULRS_EXH3A", "Alt. Titanium Tuner Exhaust")
	AddTextEntry("GBMOGULRS_EXH4A", "Alt. Big Bore Exhaust")
	AddTextEntry("GBMOGULRS_EXH5A", "Alt. Race Exhaust")
	AddTextEntry("GBMOGULRS_HOOD2", "Power Bulge Hood")
	AddTextEntry("GBMOGULRS_HOOD2A", "Carbon Power Bulge Hood")
	AddTextEntry("GBMOGULRS_HOOD3", "Vented Hood")
	AddTextEntry("GBMOGULRS_HOOD3A", "Carbon Vented Hood")
	AddTextEntry("GBMOGULRS_EYE2", "Custom Eyebrows")
	AddTextEntry("GBMOGULRS_EYE1", "Headlight Vents")
	AddTextEntry("GBMOGULRS_EYE3", "Primary Headlight Covers")
	AddTextEntry("GBMOGULRS_EYE4", "Carbon Headlight Covers")
	AddTextEntry("GBMOGULRS_ROOF1", "Roof Spoiler")
	AddTextEntry("GBMOGULRS_ROOF2", "Roof Scoop")
	AddTextEntry("GBMOGULRS_ROOF3", "Spoiler & Scoop Combo")
	AddTextEntry("GBMOGULRS_ROOF4", "Carbon Roof")
	AddTextEntry("GBMOGULRS_ROOF5", "Carbon Roof w/ Spoiler")
	AddTextEntry("GBMOGULRS_ROOF6", "Carbon Roof w/ Scoop")
	AddTextEntry("GBMOGULRS_ROOF7", "Carbon Roof Combo")
end)