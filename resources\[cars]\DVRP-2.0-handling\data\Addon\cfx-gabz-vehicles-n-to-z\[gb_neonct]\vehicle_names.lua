Citizen.CreateThread(function()
    AddTextEntry("GBNEONCT", "Neon Cross Turismo")
    AddTextEntry("NEONCT_DIFF1", "Secondary Colored Diffuser")
    AddTextEntry("NEONCT_DIFF2", "Carbon Diffuser")
    AddTextEntry("NEONCT_LIV1", "Pfister Racing")
    AddTextEntry("NEONCT_LIV2", "Pfister Racing Alt")
    AddTextEntry("NEONCT_LIV3", "Double Black Stripes")
    AddTextEntry("NEONCT_LIV4", "Double White Stripes")
    AddTextEntry("NEONCT_LIV5", "Black Stripe")
    AddTextEntry("NEONCT_LIV6", "White Stripe")
    AddTextEntry("NEONCT_MIR1", "Primary Colored Mirrors")
    AddTextEntry("NEONCT_MIR2", "Carbon Mirrors")
    AddTextEntry("NEONCT_ROOF1", "Secondary Colored Roof")
    AddTextEntry("NEONCT_ROOF2", "Carbon Roof")
    AddTextEntry("NEONCT_ROOFA1", "Roof Rails")
    AddTextEntry("NEONCT_ROOFA2", "Ski Box")
    AddTextEntry("NEONCT_ROOFA3", "Snowboards")
    AddTextEntry("NEONCT_ROOFA4", "Roof Stuff")
    AddTextEntry("NEONCT_SKIRT1", "Primary Colored Skirts")
    AddTextEntry("NEONCT_SKIRT2", "Secondary Colored Skirts")
    AddTextEntry("NEONCT_SPLIT1", "Primary Colored Splitter")
    AddTextEntry("NEONCT_SPLIT2", "Secondary Colored Splitter")
    AddTextEntry("NEONCT_WING1", "Carbon Spoiler")
end)