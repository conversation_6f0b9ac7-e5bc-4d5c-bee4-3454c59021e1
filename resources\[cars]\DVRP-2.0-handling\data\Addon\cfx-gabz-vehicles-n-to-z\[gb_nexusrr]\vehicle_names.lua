Citizen.CreateThread(function()
AddTextEntry("NEXUSRR", "Nexus RR")

AddTextEntry("NEXUSRR_FBUMPER_01", "Delete Plate")
AddTextEntry("NEXUSRR_FBUMPER_02", "Stock Bumper with Canard")
AddTextEntry("NEXUSRR_FBUMPER_1", "Glossy Front Bumper")
AddTextEntry("NEXUSRR_FBUMPER_2", "Glossy Front Bumper with Canard")
AddTextEntry("NEXUSRR_FBUMPER_3", "Glossy Front Bumper with Plate Delete")
AddTextEntry("NEXUSRR_FBUMPER_4", "Glossy Front Bumper with Plate Delete and Canard")

AddTextEntry("NEXUSRR_FBUMPER_5", "Chrome Front Bumper")
AddTextEntry("NEXUSRR_FBUMPER_6", "Chrome Front Bumper with Canard")
AddTextEntry("NEXUSRR_FBUMPER_7", "Chrome Front Bumper with Plate Delete")
AddTextEntry("NEXUSRR_FBUMPER_8", "Chrome Front Bumper with Plate Delete and Canard")

AddTextEntry("NEXUSRR_FBUMPER_9", "Carbon Front Bumper")
AddTextEntry("NEXUSRR_FBUMPER_10", "Carbon Front Bumper with Canard")
AddTextEntry("NEXUSRR_FBUMPER_11", "Carbon Front Bumper with Plate Delete")
AddTextEntry("NEXUSRR_FBUMPER_12", "Carbon Front Bumper with Plate Delete and Canard")

AddTextEntry("NEXUSRR_FBUMPER_13", "Type-S Bumper")
AddTextEntry("NEXUSRR_FBUMPER_14", "Type-S Bumper with Canard")
AddTextEntry("NEXUSRR_FBUMPER_15", "Type-S Bumper with Plate")
AddTextEntry("NEXUSRR_FBUMPER_16", "Type-S Bumper with Plate and Canard")

AddTextEntry("NEXUSRR_FBUMPER_17", "Subtly Painted Type-S Bumper")
AddTextEntry("NEXUSRR_FBUMPER_18", "Subtly Painted Type-S Bumper with Canard")
AddTextEntry("NEXUSRR_FBUMPER_19", "Subtly Painted Type-S Bumper with Plate")
AddTextEntry("NEXUSRR_FBUMPER_20", "Subtly Painted Type-S Bumper with Plate and Canard")

AddTextEntry("NEXUSRR_FBUMPER_21", "Painted Type-S Bumper")
AddTextEntry("NEXUSRR_FBUMPER_22", "Painted Type-S Bumper with Canard")
AddTextEntry("NEXUSRR_FBUMPER_23", "Painted Type-S Bumper with Plate")
AddTextEntry("NEXUSRR_FBUMPER_24", "Painted Type-S Bumper with Plate and Canard")

AddTextEntry("NEXUSRR_FBUMPERWIDE_01", "Stock Front Bumper with Widebody")
AddTextEntry("NEXUSRR_FBUMPERWIDE_02", "Stock Front Bumper with Plate Delete and Widebody")
AddTextEntry("NEXUSRR_FBUMPERWIDE_1", "Widebody Glossy Front Bumper")
AddTextEntry("NEXUSRR_FBUMPERWIDE_3", "Widebody Glossy Front Bumper with Plate Delete")
AddTextEntry("NEXUSRR_FBUMPERWIDE_5", "Widebody Chrome Front Bumper")
AddTextEntry("NEXUSRR_FBUMPERWIDE_7", "Widebody Chrome Front Bumper with Plate Delete")
AddTextEntry("NEXUSRR_FBUMPERWIDE_9", "Widebody Carbon Front Bumper")
AddTextEntry("NEXUSRR_FBUMPERWIDE_11", "Widebody Carbon Front Bumper with Plate Delete")
AddTextEntry("NEXUSRR_FBUMPERWIDE_13", "Widebody Type-S Bumper")
AddTextEntry("NEXUSRR_FBUMPERWIDE_15", "Widebody Type-S Bumper with Plate")
AddTextEntry("NEXUSRR_FBUMPERWIDE_17", "Widebody Subtly Painted Type-S Bumper")
AddTextEntry("NEXUSRR_FBUMPERWIDE_19", "Widebody Subtly Painted Type-S Bumper with Plate")
AddTextEntry("NEXUSRR_FBUMPERWIDE_21", "Widebody Painted Type-S Bumper")
AddTextEntry("NEXUSRR_FBUMPERWIDE_23", "Widebody Painted Type-S Bumper with Plate")

AddTextEntry("NEXUSRR_RBUMPER_1", "Stock Rear Bumper with Accent")
AddTextEntry("NEXUSRR_RBUMPER_2", "Grand Tourer Rear Bumper")
AddTextEntry("NEXUSRR_RBUMPER_3", "Grand Tourer Aero Rear Bumper")
AddTextEntry("NEXUSRR_RBUMPER_4", "Grand Tourer Aero Rear Bumper with Accent")
AddTextEntry("NEXUSRR_RBUMPER_5", "Carbon Grand Tourer Rear Bumper")
AddTextEntry("NEXUSRR_RBUMPER_6", "Carbon Grand Tourer Rear Bumper with Accent")
AddTextEntry("NEXUSRR_RBUMPER_7", "Racing Bumper")
AddTextEntry("NEXUSRR_RBUMPER_8", "Racing Bumper with Black Gloss")
AddTextEntry("NEXUSRR_RBUMPER_9", "Racing Bumper with Accent")
AddTextEntry("NEXUSRR_RBUMPER_10", "Racing Bumper with Black Gloss and Accent")

AddTextEntry("NEXUSRR_RBUMPERWIDE_0", "Stock Bumper with Widebody")
AddTextEntry("NEXUSRR_RBUMPERWIDE_1", "Stock Rear Bumper with Accent and Widebody")
AddTextEntry("NEXUSRR_RBUMPERWIDE_2", "Widebody Grand Tourer Rear Bumper")
AddTextEntry("NEXUSRR_RBUMPERWIDE_3", "Widebody Grand Tourer Aero Rear Bumper")
AddTextEntry("NEXUSRR_RBUMPERWIDE_4", "Widebody Grand Tourer Aero Rear Bumper with Accent")
AddTextEntry("NEXUSRR_RBUMPERWIDE_5", "Widebody Carbon Grand Tourer Rear Bumper")
AddTextEntry("NEXUSRR_RBUMPERWIDE_6", "Widebody Carbon Grand Tourer Rear Bumper with Accent")
AddTextEntry("NEXUSRR_RBUMPERWIDE_7", "Widebody Racing Bumper")
AddTextEntry("NEXUSRR_RBUMPERWIDE_8", "Widebody Racing Bumper with Black Gloss")
AddTextEntry("NEXUSRR_RBUMPERWIDE_9", "Widebody Racing Bumper with Accent")
AddTextEntry("NEXUSRR_RBUMPERWIDE_10", "Widebody Racing Bumper with Black Gloss and Accent")

AddTextEntry("NEXUSRR_DHANDLES_1", "Painted Door Handles")
AddTextEntry("NEXUSRR_DHANDLES_2", "Black Gloss Door Handles")

AddTextEntry("NEXUSRR_MIRRORS_1", "Black Gloss Mirrors")
AddTextEntry("NEXUSRR_MIRRORS_2", "Carbon Mirrors")


AddTextEntry("NEXUSRR_FUELCELL_1", "Fuel Cell")

AddTextEntry("NEXUSRR_SKIRT_1", "Glossy Sideskirt")
AddTextEntry("NEXUSRR_SKIRT_2", "Carbon Sideskirt")
AddTextEntry("NEXUSRR_SKIRT_3", "Sideskirt with Accent")
AddTextEntry("NEXUSRR_SKIRT_4", "Carbon Sideskirt with Accent")
AddTextEntry("NEXUSRR_SKIRT_5", "Racing Sideskirt")
AddTextEntry("NEXUSRR_SKIRT_6", "Wide Sideskirt with Front Fin")
AddTextEntry("NEXUSRR_SKIRT_7", "Wide Sideskirt with Rear Fin")

AddTextEntry("NEXUSRR_SPLITTER_1", "Custom Splitter")
AddTextEntry("NEXUSRR_SPLITTER_2", "Custom Splitter with Accent")
AddTextEntry("NEXUSRR_SPLITTER_3", "Touring Splitter")
AddTextEntry("NEXUSRR_SPLITTER_4", "Racing Splitter")
AddTextEntry("NEXUSRR_SPLITTER_5", "Racing Splitter with Accent")

AddTextEntry("NEXUSRR_SPOILER_1", "Plastic Ducktail")
AddTextEntry("NEXUSRR_SPOILER_2", "Carbon Ducktail")
AddTextEntry("NEXUSRR_SPOILER_3", "Lowered Extreme Spoiler")
AddTextEntry("NEXUSRR_SPOILER_4", "Competition Spoiler")
AddTextEntry("NEXUSRR_SPOILER_5", "Custom Spoiler")
AddTextEntry("NEXUSRR_SPOILER_6", "Extreme Spoiler")
AddTextEntry("NEXUSRR_SPOILER_7", "Aero Spoiler")
AddTextEntry("NEXUSRR_SPOILER_8", "Carbon Racing Spoiler")
AddTextEntry("NEXUSRR_SPOILER_9", "Painted Racing Spoiler")

AddTextEntry("NEXUSRR_ROOF_1", "Painted Roof")
AddTextEntry("NEXUSRR_ROOF_2", "Carbon Roof")

AddTextEntry("NEXUSRR_HOOD_1", "Carbon Stock Hood")
AddTextEntry("NEXUSRR_HOOD_2", "Racing Hood")
AddTextEntry("NEXUSRR_HOOD_3", "Painted Racing Hood")
AddTextEntry("NEXUSRR_HOOD_4", "Carbon Racing Hood")
AddTextEntry("NEXUSRR_HOOD_5", "Competition Hood")
AddTextEntry("NEXUSRR_HOOD_6", "Carbon Competition Hood")
AddTextEntry("NEXUSRR_HOOD_7", "Stage 1 Hood")
AddTextEntry("NEXUSRR_HOOD_8", "Carbon Stage 1 Hood")
AddTextEntry("NEXUSRR_HOOD_9", "Stage 2 Hood")
AddTextEntry("NEXUSRR_HOOD_10", "Carbon Stage 2 Hood")
AddTextEntry("NEXUSRR_HOOD_11", "Stage 3 Hood")
AddTextEntry("NEXUSRR_HOOD_12", "Carbon Stage 3 Hood")

AddTextEntry("NEXUSRR_LIV1", "Motorsport Edition")
AddTextEntry("NEXUSRR_LIV2", "Atomic Racing")
AddTextEntry("NEXUSRR_LIV3", "X-Flow")
AddTextEntry("NEXUSRR_LIV4", "Stealth Racer")
AddTextEntry("NEXUSRR_LIV5", "Gutter & Blood")
AddTextEntry("NEXUSRR_LIV6", "Demonoil")
AddTextEntry("NEXUSRR_LIV7", "Auto Exotic (Blue)")
AddTextEntry("NEXUSRR_LIV8", "Auto Exotic (Red)")
AddTextEntry("NEXUSRR_LIV9", "Junk Energy")
AddTextEntry("NEXUSRR_LIV10", "Junk Energy (Alt)")
AddTextEntry("NEXUSRR_LIV11", "Fukaru")

end)
