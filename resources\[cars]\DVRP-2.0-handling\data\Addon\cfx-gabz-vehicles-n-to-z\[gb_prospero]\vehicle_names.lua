Citizen.CreateThread(function()
	AddTextEntry("GBPROSPERO", "Prospero")
	AddTextEntry("PROSP_ROOF0", "Remove Roof")
	AddTextEntry("PROSP_EROOF1", "Elongated Roof")
	AddTextEntry("PROSP_EROOF0", "Opened Elongated Roof")
	AddTextEntry("PROSP_ROOF1", "Accented Roof")
	AddTextEntry("PROSP_ROOF00", "Opened Accented Roof")
	AddTextEntry("PROSP_EROOF2", "Elongated Accented Roof")
	AddTextEntry("PROSP_EROOF22", "Opened Elongated Accented Roof")
	AddTextEntry("PROSP_MIR1", "Secondary Mirrors")
	AddTextEntry("PROSP_MIR2", "Carbon Mirrors")
	AddTextEntry("PROSP_DUCT1", "Painted Ducts")
	AddTextEntry("PROSP_DUCT2", "Black Ducts")
	AddTextEntry("PROSP_DUCT3", "Carbon Ducts")
	AddTextEntry("PROSP_FEND1", "Fake Fender Vents")
	AddTextEntry("PROSP_FEND2", "Fender Vents")
	AddTextEntry("PROSP_WING1", "Raised Stock Wing")
	AddTextEntry("PROSP_WING2", "Super Wing")
	AddTextEntry("PROSP_WING3", "Tuner Wing")
	AddTextEntry("PROSP_WING4", "Aggressive Wing")
	AddTextEntry("PROSP_WING7", "Strada Wing")
	AddTextEntry("PROSP_WING8", "Corsa Wing")
	AddTextEntry("PROSP_WING9", "GT Wing")
	AddTextEntry("PROSP_BON0A", "Carbon Bonnet")
	AddTextEntry("PROSP_BON1", "Accented Bonnet")
	AddTextEntry("PROSP_BON2", "Accented Carbon Bonnet")
	AddTextEntry("PROSP_BON3", "Ignus Bonnet")
	AddTextEntry("PROSP_BON4", "Race Bonnet")
	AddTextEntry("PROSP_BON5", "Carbon Race Bonnet")
	AddTextEntry("PROSP_BON6", "Painted Race Bonnet")
	AddTextEntry("PROSP_BON7", "Aftermarket Bonnet")
	AddTextEntry("PROSP_BON8", "Carbon Aftermarket Bonnet")
	AddTextEntry("PROSP_ECV1", "Engine Vent Covers")
	AddTextEntry("PROSP_EC1", "Carbon Engine Vents")
	AddTextEntry("PROSP_EC2", "Painted Engine Vents")
	AddTextEntry("PROSP_EC3", "Race Engine Cover")
	AddTextEntry("PROSP_EC4", "Carbon Race Cover")
	AddTextEntry("PROSP_EC5", "Painted Race Cover")
	AddTextEntry("PROSP_DIFF1", "Tuner Diffuser")
	AddTextEntry("PROSP_DIFF2", "Super Diffuser")
	AddTextEntry("PROSP_DIFF1B", "Black Panel with Tuner Diffuser")
	AddTextEntry("PROSP_DIFF2B", "Black Panel with Super Diffuser")
	AddTextEntry("PROSP_BUMR0", "Black Rear Panel")
	AddTextEntry("PROSP_BUMR1", "Sport Bumper")
	AddTextEntry("PROSP_BUMR2", "Carbon Sport Bumper")
	AddTextEntry("PROSP_BUMR3", "Painted Panel Sport Bumper")
	AddTextEntry("PROSP_BUMR4", "Strada Bumper")
	AddTextEntry("PROSP_BUMR5", "Painted Strada Bumper")
	AddTextEntry("PROSP_BUMR6", "50th Anni. Bumper")
	AddTextEntry("PROSP_BUMR7", "Corsa Bumper")
	AddTextEntry("PROSP_BUMR7B", "Accented Corsa Bumper")
	AddTextEntry("PROSP_BUMR8", "Corsa Bumper with Slanted Exhausts")
	AddTextEntry("PROSP_SSKIRT1A", "Super Skirts")
	AddTextEntry("PROSP_SSKIRT1B", "Super Skirts II")
	AddTextEntry("PROSP_SKIRT1", "Sport Side Panels")
	AddTextEntry("PROSP_SKIRT1B", "Sport Side Panels with Skirts")
	AddTextEntry("PROSP_SKIRT2", "50th Anni. Side Panels")
	AddTextEntry("PROSP_SKIRT3", "Strada Side Panels")
	AddTextEntry("PROSP_SKIRT4", "Corsa Side Panels")
	AddTextEntry("PROSP_SKIRT4B", "Accented Corsa Side Panels")
	AddTextEntry("PROSP_SKIRT5", "Ultimate Side Panels")
	AddTextEntry("PROSP_SKIRT6", "Carbon Ultimate Side Panels")
	AddTextEntry("PROSP_SKIRT7", "Carbon Corsa Side Panels")
	AddTextEntry("PROSP_SSKIRT1AWB", "Super Skirts w Flares")
	AddTextEntry("PROSP_SSKIRT1BWB", "Super Skirts II w Flares")
	AddTextEntry("PROSP_SKIRT1WB", "Sport Side Panels w Flares")
	AddTextEntry("PROSP_SKIRT1BWB", "Sport Side Panels w Skirts and Flares")
	AddTextEntry("PROSP_SKIRT2WB", "50th Anni. Side Panels w Flares")
	AddTextEntry("PROSP_SKIRT3WB", "Strada Side Panels w Flares")
	AddTextEntry("PROSP_SPLIT1A", "Super Splitter")
	AddTextEntry("PROSP_SPLIT1B", "Super Splitter II")
	AddTextEntry("PROSP_BUMF1", "Sport Bumper")
	AddTextEntry("PROSP_BUMF1B", "Sport Bumper with Splitter")
	AddTextEntry("PROSP_BUMF2", "Strada Bumper")
	AddTextEntry("PROSP_BUMF3", "50th Anni. Bumper")
	AddTextEntry("PROSP_BUMF4", "Painted Strada Bumper")
	AddTextEntry("PROSP_BUMF5", "Evo Bumper")
	AddTextEntry("PROSP_BUMF5A", "Evo Bumper with Black Splitter")
	AddTextEntry("PROSP_BUMF5B", "Evo Bumper with Carbon Splitter")
	AddTextEntry("PROSP_BUMF6", "Evo R Bumper ")
	AddTextEntry("PROSP_BUMF6A", "Evo R Bumper with Black Splitter")
	AddTextEntry("PROSP_BUMF6B", "Evo R Bumper with Carbon Splitter")
	AddTextEntry("PROSP_BUMF7", "Corsa Bumper")
	AddTextEntry("PROSP_BUMF7A", "Corsa Bumper with Black Splitter")
	AddTextEntry("PROSP_BUMF7B", "Corsa Bumper with Carbon Splitter")
	AddTextEntry("PROSP_BUMF8", "Corsa Bumper II")
	AddTextEntry("PROSP_BUMF9", "Vented Corsa Bumper II")
	AddTextEntry("PROSP_BUMF9B", "Accented Vented Corsa Bumper II")
	AddTextEntry("PROSP_WBF1", "Front Fender Flares")	

	AddTextEntry("PROSPERO_LIVERY_1", "Zoccolona Viziata Black")
	AddTextEntry("PROSPERO_LIVERY_2", "Zoccolona Viziata Blue")
	AddTextEntry("PROSPERO_LIVERY_3", "Zoccolona Viziata Red")
	AddTextEntry("PROSPERO_LIVERY_4", "Zoccolona Viziata White")
	AddTextEntry("PROSPERO_LIVERY_5", "Zoccolona Viziata Yellow")
	AddTextEntry("PROSPERO_LIVERY_6", "Gradiente Black")
	AddTextEntry("PROSPERO_LIVERY_7", "Gradiente Blue")
	AddTextEntry("PROSPERO_LIVERY_8", "Gradiente Red")
	AddTextEntry("PROSPERO_LIVERY_9", "Gradiente Silver")
	AddTextEntry("PROSPERO_LIVERY_10", "Gradiente White")
	AddTextEntry("PROSPERO_LIVERY_11", "II Feroce")
	AddTextEntry("PROSPERO_LIVERY_12", "II Prospero")
	AddTextEntry("PROSPERO_LIVERY_13", "Tricolore")
	AddTextEntry("PROSPERO_LIVERY_14", "XO Novantasei")
	AddTextEntry("PROSPERO_LIVERY_15", "Punto Nero")
	AddTextEntry("PROSPERO_LIVERY_16", "Punto Bianco")
	AddTextEntry("PROSPERO_LIVERY_17", "White Street Decals")
	AddTextEntry("PROSPERO_LIVERY_18", "Black Street Decals")
	AddTextEntry("PROSPERO_LIVERY_19", "Twin Stripes Black")
	AddTextEntry("PROSPERO_LIVERY_20", "Twin Stripes Silver")
	AddTextEntry("PROSPERO_LIVERY_21", "Twin Stripes White")
	AddTextEntry("PROSPERO_LIVERY_22", "Twin Stripes Red")
	AddTextEntry("PROSPERO_LIVERY_23", "Twin Stripes Yellow")
	AddTextEntry("PROSPERO_LIVERY_24", "Twin Stripes Gold")
	AddTextEntry("PROSPERO_LIVERY_25", "Avanguardia")
	AddTextEntry("PROSPERO_LIVERY_26", "Meinmacht")
	AddTextEntry("PROSPERO_LIVERY_27", "X-Flow")
	AddTextEntry("PROSPERO_LIVERY_28", "Kisama X-Flow")
end)
