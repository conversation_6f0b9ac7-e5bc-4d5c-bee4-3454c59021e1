Citizen.CreateThread(function()
AddTextEntry("GBRAID", "Raidillon")
AddTextEntry("RAID_LIVERY_1", "Factory Decals - Black")
AddTextEntry("RAID_LIVERY_2", "Factory Decals - White")
AddTextEntry("RAID_LIVERY_3", "Globe Oil Racing #6")
AddTextEntry("RAID_LIVERY_4", "Toundra Rally Team #2")
AddTextEntry("RAID_LIVERY_5", "Kronos Team #19")
AddTextEntry("RAID_SPOILER_1", "Sport Spoiler")
AddTextEntry("RAID_SPOILER_2", "Sport Spoiler with Decal")
AddTextEntry("RAID_SPOILER_3", "Low Spoiler")
AddTextEntry("RAID_SPOILER_4", "Low Spoiler with Decal")
AddTextEntry("RAID_SPOILER_5", "Rally Spoiler")
AddTextEntry("RAID_TRIM_1", "Plastic Pillar")
AddTextEntry("RAID_TRIM_2", "Chrome Trim")
AddTextEntry("RAID_MIRROR_1", "Left Lightweight Mirror")
AddTextEntry("RAID_MIRROR_2", "Lightweight Mirrors")
AddTextEntry("RAID_FOGS_1", "Rally Lights Type I")
AddTextEntry("RAID_FOGS_2", "Covered Rally Lights Type I")
AddTextEntry("RAID_FOGS_3", "Rally Lights Type II")
AddTextEntry("RAID_FOGS_4", "Covered Rally Lights Type II")
AddTextEntry("RAID_ANTENNA_1", "Roof Antenna")
AddTextEntry("RAID_ROLL_1", "Rollcage")
AddTextEntry("RAID_MUDFLAPS_1", "Mudflaps")
AddTextEntry("RAID_SUNSTRIP_1", "Sunstrip")
AddTextEntry("RAID_PANEL_1", "Black Front Panel")
AddTextEntry("RAID_PLASTIC_BUMPERS_1", "Plastic Bumpers")
AddTextEntry("RAID_CLASSIC_BODY_1", "Classic Body")
AddTextEntry("RAID_CLASSIC_BODY_2", "Classic Body with Painted Bumper Guards")
AddTextEntry("RAID_CLASSIC_BODY_3", "Classic Body with Plastic Bumper Guards")
AddTextEntry("RAID_CLASSIC_BODY_3_ALT", "Classic Body with Chrome Bumper Guards")
AddTextEntry("RAID_CLASSIC_BODY_4", "Classic Body with Front Lip")
AddTextEntry("RAID_CLASSIC_BODY_5", "Classic Body with Front Lip and Painted Bumper Guards")
AddTextEntry("RAID_CLASSIC_BODY_6", "Classic Body with Front Lip and Plastic Bumper Guards")
AddTextEntry("RAID_CLASSIC_BODY_6_ALT", "Classic Body with Front Lip and Chrome Bumper Guards")
AddTextEntry("RAID_RACE_BODY_1", "Racing Body")
AddTextEntry("RAID_RACE_BODY_2", "Racing Body with Painted Bumper Guards")
AddTextEntry("RAID_RACE_BODY_3", "Racing Body with Plastic Bumper Guards")
AddTextEntry("RAID_RACE_BODY_3_ALT", "Racing Body with Chrome Bumper Guards")
end)
