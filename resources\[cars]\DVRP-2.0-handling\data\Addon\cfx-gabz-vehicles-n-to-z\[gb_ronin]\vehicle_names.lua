Citizen.CreateThread(function()
        AddTextEntry("GBRONI<PERSON>", "Ronin")
        AddTextEntry("RONIN_LIV1", "Dense Racing")
        AddTextEntry("RONIN_LIV2", "RBS Drift Team")
        AddTextEntry("RONIN_LIV3", "Gutter & Blood")
        AddTextEntry("RONIN_LIV4", "Lozspeed")
        AddTextEntry("RONIN_LIV5", "Fukaru")
        AddTextEntry("RONIN_LIV6", "Yours Tuned")
        AddTextEntry("RONIN_LIV7", "Kisama Beer")
        AddTextEntry("RONIN_LIV8", "Scratch Effect")

        AddTextEntry("RONIN_SKIRT1", "Carbon Skirts")
        AddTextEntry("RONIN_SKIRT2", "Sport Skirts")
        AddTextEntry("RONIN_SKIRT3", "Painted Skirts")
        AddTextEntry("RONIN_SKIRT4", "Extended Skirts")
        AddTextEntry("RONIN_SKIRT5", "Flow Skirts")
        AddTextEntry("RONIN_BON0B", "Secondary Bonnet")
        AddTextEntry("RONIN_BON0C", "Carbon Bonnet")
        AddTextEntry("RONIN_BON1A", "Sport Bonnet")
        AddTextEntry("RONIN_BON1B", "Secondary Sport Bonnet")
        AddTextEntry("RONIN_BON1C", "Carbon Sport Bonnet")
        AddTextEntry("RONIN_BON2A", "Smooth Bonnet")
        AddTextEntry("RONIN_BON2B", "Secondary Smooth Bonnet")
        AddTextEntry("RONIN_BON2C", "Carbon Smooth Bonnet")
        AddTextEntry("RONIN_BON3A", "V1 Race Bonnet")
        AddTextEntry("RONIN_BON3B", "V1 Carbon Race Bonnet")
        AddTextEntry("RONIN_BON4A", "V2 Race Bonnet")
        AddTextEntry("RONIN_BON4B", "V2 Carbon Race Bonnet")
        AddTextEntry("RONIN_EXH1", "Black Exhausts")
        AddTextEntry("RONIN_EXH2", "Circular Exhausts")
        AddTextEntry("RONIN_ROOF0B", "Secondary Roof")
        AddTextEntry("RONIN_ROOF0C", "Carbon Roof")
        AddTextEntry("RONIN_ROOF1A", "Secondary Roof and Pilars")
        AddTextEntry("RONIN_ROOF1B", "Carbon Roof w/ Sec. Pillars")
        AddTextEntry("RONIN_RSPOILER1", "Painted Roof Spoiler")
        AddTextEntry("RONIN_RSPOILER2", "Secondary Roof Spoiler")
        AddTextEntry("RONIN_RSPOILER3", "Carbon Roof Spoiler")
        AddTextEntry("RONIN_DIFF1", "Sport Diffuser")
        AddTextEntry("RONIN_DIFF2", "Tuner Diffuser")
        AddTextEntry("RONIN_DIFF3", "Aggressive Diffuser")
        AddTextEntry("RONIN_DIFF4", "Tuner V2 Diffuser")
        AddTextEntry("RONIN_BUMR2", "Race Bumper")
        AddTextEntry("RONIN_MESHR", "Detailed Rear Mesh")
        AddTextEntry("RONIN_MIR1", "Secondary Mirrors")
        AddTextEntry("RONIN_SPLIT1", "Sport Splitter")
        AddTextEntry("RONIN_SPLIT2", "Full Sport Splitter")
        AddTextEntry("RONIN_SPLIT3", "Tuner Splitter")
        AddTextEntry("RONIN_SPLIT4", "The Ring Splitter")
        AddTextEntry("RONIN_SPLIT5", "Race Splitter")
        AddTextEntry("RONIN_BUMF1", "Sport Bumper")
        AddTextEntry("RONIN_BUMF2", "V2 Bumper")
        AddTextEntry("RONIN_BUMF3", "V2 Sport Bumper")
        AddTextEntry("RONIN_PLTF1", "Front Plate")
        AddTextEntry("RONIN_PLTF2", "Small Front Plate")
        AddTextEntry("RONIN_WING0B", "Carbon Wing")
        AddTextEntry("RONIN_WING1A", "Raised Wing")
        AddTextEntry("RONIN_WING1B", "Raised Carbon Wing")
        AddTextEntry("RONIN_SPL1A", "Lip Spoiler")
        AddTextEntry("RONIN_SPL1B", "Lip Spoiler w/ Carbon base")
        AddTextEntry("RONIN_SPL2A", "Ducktail")
        AddTextEntry("RONIN_SPL2B", "Ducktail w/ Carbon base")
        AddTextEntry("RONIN_SPL3A", "Tuner Ducktail")
        AddTextEntry("RONIN_SPL3B", "Tuner Ducktail w/ Carbon base")
        AddTextEntry("RONIN_SPL4A", "Tuner Spoiler")
        AddTextEntry("RONIN_SPL4B", "Tuner Spoiler w/ Carbon base")
        AddTextEntry("RONIN_SPL5", "The Ring Spoiler")
        AddTextEntry("RONIN_SPL6", "Race Spoiler")
end)
