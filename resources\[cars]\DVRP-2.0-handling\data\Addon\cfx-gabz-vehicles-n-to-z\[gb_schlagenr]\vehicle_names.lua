Citizen.CreateThread(function()
    AddTextEntry("GBSCHLAGEN<PERSON>", "Schlagen Roadster")
    AddTextEntry("SCHLAR_BON1", "Vented Bonnet")
    AddTextEntry("SCHLAR_CAGE0", "Remove Frame")
    AddTextEntry("SC<PERSON>AR_CAGE1", "Rollcage")
    AddTextEntry("SCHLAR_GRILL1", "Basic Grille")
    AddTextEntry("SCHLAR_GRILL2", "Basic Double Grille")
    AddTextEntry("SCHLAR_GRILL3", "GT Grille")
    AddTextEntry("SCHLAR_GRILL4", "GT Grille V2")
    AddTextEntry("<PERSON><PERSON>AR_NOROOF", "Lower Roof")
    AddTextEntry("SCHLAR_SKIRT1", "Carbon Skirts")
    AddTextEntry("SCHLAR_SKIRT2", "Carbon Skirts V2")
    AddTextEntry("SCHLAR_SPLIT1", "Carbon Splitter")
    AddTextEntry("SCHLAR_SVENTS1", "Side Vent Cover")
    AddTextEntry("<PERSON><PERSON>AR_WING1", "GT Wing")
    AddTextEntry("SCHLAR_WING2", "Tuner Wing")
end)
