Citizen.CreateThread(function()
AddTextEntry("SCH<PERSON>UBER", "Schrauber")

AddTextEntry("SCHRAUBER_SKIRT_1", "Painted Skirt")
AddTextEntry("SCHRAUBER_GRILLE_1", "Painted Grille")
AddTextEntry("SCHRAUBER_GRILLE_2", "Golden Grille")
AddTextEntry("SCHRAUBER_WIPER_1", "Straight Wiper")
AddTextEntry("SCHRAUBER_WIPER_2", "Headlight Wipers")
AddTextEntry("SCHRAUBER_MIRROR_1", "Painted Mirrors")
AddTextEntry("SCHRAUBER_MIRROR_2", "Racing Mirrors")

AddTextEntry("SCHRAUBER_SPOILER_1", "Touring Spoiler")
AddTextEntry("SCHRAUBER_SPOILER_2", "Super Touring Spoiler")
AddTextEntry("SCHRAUBER_SPOILER_3", "Racing Spoiler")

AddTextEntry("SCHRAUBER_FOGS_0", "Foglights")
AddTextEntry("SCHRAUBER_NO_PLATE_1", "Delete Plate")
AddTextEntry("SCHRAUBER_EURO_PLATE_2", "Add Euro Plate")
AddTextEntry("SCHRAUBER_NO_PLATE_FOGS_3", "Delete Plates w/ Foglights")
AddTextEntry("SCHRAUBER_EURO_PLATE_FOGS_4", "Add Euro Plates w/ Foglights")

AddTextEntry("SCHRAUBER_PAINTED_FB_5", "Painted Bumper")
AddTextEntry("SCHRAUBER_PAINTED_FB_NO_PLATE_6", "Painted Bumper w/ Plate Delete")
AddTextEntry("SCHRAUBER_PAINTED_FB_EURO_PLATE_7", "Painted Bumper w/ Euro Plate")
AddTextEntry("SCHRAUBER_PAINTED_FB_FOGS_8", "Painted Bumper w/ Foglights")
AddTextEntry("SCHRAUBER_PAINTED_FB_NO_PLATE_FOGS_9", "Painted Bumper w/ Plate Delete and Foglights")
AddTextEntry("SCHRAUBER_PAINTED_FB_EURO_PLATE_FOGS_10", "Painted Bumper w/ Euro Plate and Foglights")

AddTextEntry("SCHRAUBER_PAINTED_FB_11", "Touring Bumper")
AddTextEntry("SCHRAUBER_PAINTED_FB_NO_PLATE_12", "Touring Bumper w/ Plate Delete")
AddTextEntry("SCHRAUBER_PAINTED_FB_EURO_PLATE_13", "Touring Bumper w/ Euro Plate")
AddTextEntry("SCHRAUBER_PAINTED_FB_FOGS_14", "Touring Bumper w/ Foglights")
AddTextEntry("SCHRAUBER_PAINTED_FB_NO_PLATE_FOGS_15", "Touring Bumper w/ Plate Delete and Foglights")
AddTextEntry("SCHRAUBER_PAINTED_FB_EURO_PLATE_FOGS_16", "Touring Bumper w/ Euro Plate and Foglights")

AddTextEntry("SCHRAUBER_PAINTED_FB_17", "Touring Bumper (Splitter)")
AddTextEntry("SCHRAUBER_PAINTED_FB_NO_PLATE_18", "Touring Bumper (Splitter) w/ Plate Delete")
AddTextEntry("SCHRAUBER_PAINTED_FB_EURO_PLATE_19", "Touring Bumper (Splitter) w/ Euro Plate")
AddTextEntry("SCHRAUBER_PAINTED_FB_FOGS_20", "Touring Bumper (Splitter) w/ Foglights")
AddTextEntry("SCHRAUBER_PAINTED_FB_NO_PLATE_FOGS_21", "Touring Bumper (Splitter) w/ Plate Delete and Foglights")
AddTextEntry("SCHRAUBER_PAINTED_FB_EURO_PLATE_FOGS_22", "Touring Bumper (Splitter) w/ Euro Plate and Foglights")

AddTextEntry("SCHRAUBER_PAINTED_FB_23", "Painted Touring Bumper")
AddTextEntry("SCHRAUBER_PAINTED_FB_NO_PLATE_24", "Painted Touring Bumper w/ Plate Delete")
AddTextEntry("SCHRAUBER_PAINTED_FB_EURO_PLATE_25", "Painted Touring Bumper w/ Euro Plate")
AddTextEntry("SCHRAUBER_PAINTED_FB_FOGS_26", "Painted Touring Bumper w/ Foglights")
AddTextEntry("SCHRAUBER_PAINTED_FB_NO_PLATE_FOGS_27", "Painted Touring Bumper w/ Plate Delete and Foglights")
AddTextEntry("SCHRAUBER_PAINTED_FB_EURO_PLATE_FOGS_28", "Painted Touring Bumper w/ Euro Plate and Foglights")

AddTextEntry("SCHRAUBER_PAINTED_FB_29", "Painted Touring Bumper (Splitter)")
AddTextEntry("SCHRAUBER_PAINTED_FB_NO_PLATE_30", "Painted Touring Bumper (Splitter) w/ Plate Delete")
AddTextEntry("SCHRAUBER_PAINTED_FB_EURO_PLATE_31", "Painted Touring Bumper (Splitter) w/ Euro Plate")
AddTextEntry("SCHRAUBER_PAINTED_FB_FOGS_32", "Painted Touring Bumper (Splitter) w/ Foglights")
AddTextEntry("SCHRAUBER_PAINTED_FB_NO_PLATE_FOGS_33", "Painted Touring Bumper (Splitter) w/ Plate Delete and Foglights")
AddTextEntry("SCHRAUBER_PAINTED_FB_EURO_PLATE_FOGS_34", "Painted Touring Bumper (Splitter) w/ Euro Plate and Foglights")

AddTextEntry("SCHRAUBER_PAINTED_FB_35", "Super Touring Bumper")
AddTextEntry("SCHRAUBER_PAINTED_FB_NO_PLATE_36", "Super Touring Bumper w/ Plate Delete")
AddTextEntry("SCHRAUBER_PAINTED_FB_EURO_PLATE_37", "Super Touring Bumper w/ Euro Plate")

AddTextEntry("SCHRAUBER_PAINTED_FB_38", "Super Touring Bumper (Splitter)")
AddTextEntry("SCHRAUBER_PAINTED_FB_NO_PLATE_39", "Super Touring Bumper (Splitter) w/ Plate Delete")
AddTextEntry("SCHRAUBER_PAINTED_FB_EURO_PLATE_40", "Super Touring Bumper (Splitter) w/ Euro Plate")

AddTextEntry("SCHRAUBER_RB_2", "Painted Bumper")
AddTextEntry("SCHRAUBER_RB_3", "Touring Bumper")
AddTextEntry("SCHRAUBER_RB_4", "Painted Touring Bumper")
AddTextEntry("SCHRAUBER_RB_5", "Super Touring Bumper")
AddTextEntry("SCHRAUBER_DELETE_1", "Delete Bumper")
AddTextEntry("SCHRAUBER_RB_6", "Delete w/ Custom Diffuser")

AddTextEntry("SCHRAUBER_SUNROOF_1", "Opened Sunroof")
AddTextEntry("SCHRAUBER_SUNROOF_2", "Fully Opened Sunroof")
AddTextEntry("SCHRAUBER_NO_SUNROOF_3", "Delete Sunroof")
AddTextEntry("SCHRAUBER_RACK_4", "Roof Rack")
AddTextEntry("SCHRAUBER_NO_SUNROOF_RACK_5", "Opened Sunroof w/ Roof Rack")
AddTextEntry("SCHRAUBER_SUNROOF_RACK_6", "Fully Opened Sunroof w/ Roof Rack")
AddTextEntry("SCHRAUBER_SUNROOF_RACK_7", "Delete Sunroof w/ Roof Rack")

AddTextEntry("SCHRAUBER_KIT_1", "Touring Bodykit")
AddTextEntry("SCHRAUBER_KIT_2", "Super Touring Bodykit")

AddTextEntry("SCHRAUBER_COVER_1", "Windshield Sunstrip")
AddTextEntry("SCHRAUBER_COVER_2", "Rear Sunstrip")
AddTextEntry("SCHRAUBER_COVER_3", "Front and Rear Sunstrip")
AddTextEntry("SCHRAUBER_COVER_4", "Complete Set")
AddTextEntry("SCHRAUBER_COVER_5", "Headlight Covers")

AddTextEntry("SCHRAUBER_ROLLCAGE_1", "Remove Rear Headrest")
AddTextEntry("SCHRAUBER_ROLLCAGE_2", "Add Rollcage")
AddTextEntry("SCHRAUBER_ROLLCAGE_3", "Delete Rear Headrest w/ Rollcage")

AddTextEntry("SCHRAUBER_GUTTER_1", "Painted Gutter")

AddTextEntry("SCHRAUBER_EXHAUST_1", "Sport Exhaust")
AddTextEntry("SCHRAUBER_EXHAUST_2", "Titanium Exhaust")
AddTextEntry("SCHRAUBER_EXHAUST_3", "Racing Exhaust")
AddTextEntry("SCHRAUBER_EXHAUST_4", "Dual Big Bore Exhaust")
AddTextEntry("SCHRAUBER_EXHAUST_4A", "Titanium Dual Big Bore Exhaust")

AddTextEntry("SCHRAUBER_ANTENNA_1", "Extended Antenna")
AddTextEntry("SCHRAUBER_ANTENNA_2", "Radio Antenna")
AddTextEntry("SCHRAUBER_ANTENNA_3", "Trunk Antenna")
AddTextEntry("SCHRAUBER_ANTENNA_4", "Racing Antenna")

AddTextEntry("SCHRAUBER_REAR_PLATE_1", "Euro Rear Plate")
AddTextEntry("SCHRAUBER_DELETE_REAR_PLATE_1", "Delete Rear Plate")

AddTextEntry("SCHRAUBER_SEAT_1", "Sport Seats")
AddTextEntry("SCHRAUBER_SEAT_2", "Sport Seats w/ Green Belts")
AddTextEntry("SCHRAUBER_SEAT_3", "Sport Seats w/ Red Belts")
AddTextEntry("SCHRAUBER_SEAT_4", "Racing Seats w/ Green Belts")
AddTextEntry("SCHRAUBER_SEAT_5", "Racing Seats w/ Red Belts")
AddTextEntry("SCHRAUBER_SEAT_6", "Competition Seats w/ Green Belts")
AddTextEntry("SCHRAUBER_SEAT_7", "Competition w/ Red Belts")
AddTextEntry("SCHRAUBER_SEAT_8", "Carbon Fiber Seats")

AddTextEntry("SCHRAUBER_LIV1", "Continental Racer #00")
AddTextEntry("SCHRAUBER_LIV2", "Continental Racer #10")
AddTextEntry("SCHRAUBER_LIV3", "Continental Racer #15")
AddTextEntry("SCHRAUBER_LIV4", "Continental Racer #23")
AddTextEntry("SCHRAUBER_LIV5", "Continental Racer #46")
AddTextEntry("SCHRAUBER_LIV6", "Infinite")
AddTextEntry("SCHRAUBER_LIV7", "Infinite Inverse")
AddTextEntry("SCHRAUBER_LIV8", "Team Dusche Gold #55")
AddTextEntry("SCHRAUBER_LIV9", "Pendulus Bull Emic Racing #18")
AddTextEntry("SCHRAUBER_LIV10", "CoK Team #26")
end)
