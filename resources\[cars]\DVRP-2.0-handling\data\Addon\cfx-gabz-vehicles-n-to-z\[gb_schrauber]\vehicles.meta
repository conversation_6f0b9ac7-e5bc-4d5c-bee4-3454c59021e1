<?xml version="1.0" encoding="UTF-8"?>
<CVehicleModelInfo__InitDataList>
  <residentTxd>vehshare</residentTxd>
  <residentAnims />
  <InitDatas>
    <Item>
      <modelName>gbschrauber</modelName>
      <txdName>gbschrauber</txdName>
      <handlingId>SCHRAUBER</handlingId>
      <gameName>SCHRAUBER</gameName>
      <vehicleMakeName>BENEFACTOR</vehicleMakeName>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected />
      <ptfxAssetName>null</ptfxAssetName>
      <audioNameHash />
      <layout>LAYOUT_STANDARD</layout>
      <coverBoundOffsets>SULTAN2_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout />
      <cameraName>FOLLOW_CHEETAH_CAMERA</cameraName>
      <aimCameraName>DEFAULT_THIRD_PERSON_VEHICLE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>VEHICLE_BONNET_CAMERA_LOW</bonnetCameraName>
      <povCameraName>REDUCED_NEAR_CLIP_POV_CAMERA</povCameraName>
      <FirstPersonDriveByIKOffset x="0.030000" y="-0.12000" z="-0.040000" />
      <FirstPersonDriveByUnarmedIKOffset x="-0.040000" y="-0.110000" z="-0.040000" />
	  <FirstPersonProjectileDriveByIKOffset x="0.030000" y="-0.155000" z="-0.060000" />
	  <FirstPersonProjectileDriveByPassengerIKOffset x="-0.030000" y="-0.155000" z="-0.060000" />
	  <FirstPersonProjectileDriveByRearLeftIKOffset x="-0.040000" y="-0.055000" z="-0.065000" />
	  <FirstPersonProjectileDriveByRearRightIKOffset x="0.020000" y="-0.055000" z="-0.070000" />
	  <FirstPersonDriveByLeftPassengerIKOffset x="0.050000" y="-0.040000" z="-0.030000" />
	  <FirstPersonDriveByRightPassengerIKOffset x="-0.030000" y="-0.12000" z="-0.040000" />
	  <FirstPersonDriveByRightRearPassengerIKOffset x="-0.020000" y="-0.040000" z="-0.030000" />
	  <FirstPersonDriveByLeftPassengerUnarmedIKOffset x="0.020000" y="-0.070000" z="-0.030000" />
	  <FirstPersonDriveByRightPassengerUnarmedIKOffset x="-0.040000" y="-0.110000" z="-0.040000" />
	  <FirstPersonMobilePhoneOffset x="0.150000" y="0.200000" z="0.540000" />
      <FirstPersonPassengerMobilePhoneOffset x="0.231000" y="0.170000" z="0.420000" />
	  <FirstPersonMobilePhoneSeatIKOffset>
		<Item>
			<Offset x="0.200000" y="0.130000" z="0.428000" />
			<SeatIndex value="2" />
		</Item>
		<Item>
			<Offset x="0.200000" y="0.130000" z="0.432000" />
			<SeatIndex value="3" />
		</Item>
	  </FirstPersonMobilePhoneSeatIKOffset> 
      <PovCameraOffset x="0.000000" y="-0.190000" z="0.660000" />
      <PovCameraVerticalAdjustmentForRollCage value="0.000000" />
      <PovPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000" />
      <vfxInfoName>VFXVEHICLEINFO_CAR_GENERIC</vfxInfoName>
      <shouldUseCinematicViewMode value="true" />
      <shouldCameraTransitionOnClimbUpDown value="false" />
      <shouldCameraIgnoreExiting value="false" />
      <AllowPretendOccupants value="true" />
      <AllowJoyriding value="true" />
      <AllowSundayDriving value="true" />
      <AllowBodyColorMapping value="true" />
      <wheelScale value="0.211500" />
      <wheelScaleRear value="0.211500" />
      <dirtLevelMin value="0.000000" />
      <dirtLevelMax value="0.450000" />
      <envEffScaleMin value="0.000000" />
      <envEffScaleMax value="1.000000" />
      <envEffScaleMin2 value="0.000000" />
      <envEffScaleMax2 value="1.000000" />
      <damageMapScale value="0.40000" />
      <damageOffsetScale value="0.40000" />
      <diffuseTint value="0x00FFFFFF" />
      <steerWheelMult value="1.00000" />
      <HDTextureDist value="5.000000" />
      <lodDistances content="float_array">
        15.000000
        30.000000
        60.000000	
        120.000000
        500.000000
        500.000000
      </lodDistances>
      <minSeatHeight value="0.844" />
      <identicalModelSpawnDistance value="20" />
      <maxNumOfSameColor value="1" />
      <defaultBodyHealth value="1000.000000" />
      <pretendOccupantsScale value="1.000000" />
      <visibleSpawnDistScale value="1.000000" />
      <trackerPathWidth value="2.000000" />
      <weaponForceMult value="1.000000" />
      <frequency value="40" />
      <swankness>SWANKNESS_4</swankness>
      <maxNum value="10" />
      <flags>FLAG_EXTRAS_STRONG FLAG_AVERAGE_CAR</flags>
      <type>VEHICLE_TYPE_CAR</type>
      <plateType>VPT_FRONT_AND_BACK_PLATES</plateType>
      <dashboardType>VDT_SULTAN</dashboardType>
      <vehicleClass>VC_SPORT</vehicleClass>
      <wheelType>VWT_TUNER</wheelType>
      <trailers />
      <additionalTrailers />
      <drivers />
      <extraIncludes />
      <doorsWithCollisionWhenClosed />
      <driveableDoors />
      <bumpersNeedToCollideWithMap value="false" />
      <needsRopeTexture value="false" />
      <requiredExtras />
      <rewards />
      <cinematicPartCamera>
        <Item>WHEEL_FRONT_RIGHT_CAMERA</Item>
        <Item>WHEEL_FRONT_LEFT_CAMERA</Item>
        <Item>WHEEL_REAR_RIGHT_CAMERA</Item>
        <Item>WHEEL_REAR_LEFT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet />
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000" />
      <buoyancySphereSizeScale value="1.000000" />
      <pOverrideRagdollThreshold type="NULL" />
	  <firstPersonDrivebyData>
        <Item>STD_SULTAN2_FRONT_LEFT</Item>
        <Item>STD_SULTAN2_FRONT_RIGHT</Item>
        <Item>STD_SULTAN2_REAR_LEFT</Item>
        <Item>STD_SULTAN2_REAR_RIGHT</Item>
      </firstPersonDrivebyData>
	  <numSeatsOverride value="2" />
    </Item>
  </InitDatas>
  <txdRelationships>
    <Item>
      <parent>vehicles_sultan_interior</parent>
      <child>gbschrauber</child>
    </Item> 
  </txdRelationships>
</CVehicleModelInfo__InitDataList>
  