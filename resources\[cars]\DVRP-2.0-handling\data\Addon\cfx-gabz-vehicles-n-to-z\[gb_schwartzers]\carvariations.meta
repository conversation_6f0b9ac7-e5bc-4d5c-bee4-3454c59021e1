<?xml version="1.0" encoding="UTF-8"?>

<CVehicleModelInfoVariation>
  <variationData>
    <Item>
      <modelName>gbschwartzers</modelName>
      <colors>
        <Item>
          <indices content="char_array">
            0
            88
            10
            156
            99
            111
          </indices>
          <liveries />
        </Item>
        <Item>
          <indices content="char_array">
            111
            88
            10
            156
            99
            111
          </indices>
          <liveries />
        </Item>
        <Item>
          <indices content="char_array">
            2
            88
            3
            156
            99
            111
          </indices>
          <liveries />
        </Item>
        <Item>
          <indices content="char_array">
            6
            88
            4
            156
            99
            111
          </indices>
          <liveries />
        </Item>
        <Item>
          <indices content="char_array">
            75
            88
            64
            156
            99
            111
          </indices>
          <liveries />
        </Item>
        <Item>
          <indices content="char_array">
            122
            88
            134
            156
            99
            111
          </indices>
          <liveries />
        </Item>
        <Item>
          <indices content="char_array">
            32
            88
            27
            156
            99
            111
          </indices>
          <liveries />
        </Item>
        <Item>
          <indices content="char_array">
            8
            88
            8
            156
            99
            111
          </indices>
          <liveries />
        </Item>
      </colors>
      <kits>
        <Item>666_gbschwartzers_modkit</Item>
      </kits>
      <windowsWithExposedEdges/>
      <plateProbabilities>
        <Probabilities>
          <Item>
            <Name>Standard White</Name>
            <Value value="100"/>
          </Item>
        </Probabilities>
      </plateProbabilities>
      <lightSettings value="46"/>
      <sirenSettings value="0"/>
    </Item>
  </variationData>
</CVehicleModelInfoVariation>