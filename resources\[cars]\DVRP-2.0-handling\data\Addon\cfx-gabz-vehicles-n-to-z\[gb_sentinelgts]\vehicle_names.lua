Citizen.CreateThread(function()
AddTextEntry("GBSENTINEL", "Sentinel GTS")
AddTextEntry("TOP_GFRAME", "Grill Frame")
AddTextEntry("SENTGTS_BUMF1", "GTS Front Bumper")
AddTextEntry("SENTGTS_BUMF1A", "Carbon GTS Front Bumper")
AddTextEntry("SENTGTS_BUMF1B", "Painted GTS Front Bumper")
AddTextEntry("SENTGTS_BUMF2", "Sport Front Bumper")
AddTextEntry("SENTGTS_BUMF2A", "Carbon Sport Front Bumper")
AddTextEntry("SENTGTS_BUMF2B", "Painted Sport Front Bumper")
AddTextEntry("SENTGTS_BUMF3", "Custom Front Bumper")
AddTextEntry("SENTGTS_BUMR0A", "Carbon Stock Rear Bumper")
AddTextEntry("SENTGTS_BUMR1", "XS Rear Bumper")
AddTextEntry("SENTGTS_BUMR1A", "Carbon XS Rear Bumper")
AddTextEntry("SENTGTS_BUMR1B", "Painted XS Rear Bumper")
AddTextEntry("SENTGTS_BUMR2", "GTS Rear Bumper")
AddTextEntry("SENTGTS_BUMR2A", "Carbon GTS Rear Bumper")
AddTextEntry("SENTGTS_BUMR2B", "Painted GTS Rear Bumper")
AddTextEntry("SENTGTS_BUMR3", "Carbon Sport Rear Bumper")
AddTextEntry("SENTGTS_BUMR3A", "Sport Rear Bumper")
AddTextEntry("SENTGTS_GRILL0A", "Chrome Grill")
AddTextEntry("SENTGTS_GRILL0B", "Carbon Grill")
AddTextEntry("SENTGTS_GRILL0C", "Painted Grill")
AddTextEntry("SENTGTS_GRILL1", "Horizontal Grill")
AddTextEntry("SENTGTS_GRILL1A", "Carbon Horizontal Grill")
AddTextEntry("SENTGTS_GRILL2", "Open Grill")
AddTextEntry("SENTGTS_VENT1", "Side Vents")
AddTextEntry("SENTGTS_VENT1A", "Carbon Side Vents")
AddTextEntry("SENTGTS_HOOD1", "XS Hood")
AddTextEntry("SENTGTS_HOOD2", "GTS Hood")
AddTextEntry("SENTGTS_HOOD3", "Sport Hood")
AddTextEntry("SENTGTS_HOOD4", "Vented Hood")
AddTextEntry("SENTGTS_GFRAME1", "Chrome Grill Frame")
AddTextEntry("SENTGTS_GFRAME2", "Carbon Grill Frame")
AddTextEntry("SENTGTS_GFRAME3", "Painted Grill Frame")
AddTextEntry("SENTGTS_ROOF0A", "Carbon Roof")
AddTextEntry("SENTGTS_ROOF1", "Indent Roof")
AddTextEntry("SENTGTS_ROOF1A", "Carbon Indent Roof")
AddTextEntry("SENTGTS_ROOF2", "Ridged Roof")
AddTextEntry("SENTGTS_ROOF2A", "Carbon Ridged Roof")
AddTextEntry("SENTGTS_SPL1", "Small Bootlip")
AddTextEntry("SENTGTS_SPL1A", "Small Carbon Bootlip")
AddTextEntry("SENTGTS_SPL2", "Intermediate Bootlip")
AddTextEntry("SENTGTS_SPL2A", "Intermediate Carbon Bootlip")
AddTextEntry("SENTGTS_SPL3", "Carbon Wing Type 1")
AddTextEntry("SENTGTS_SPL4", "Carbon Wing Type 2")
AddTextEntry("SENTGTS_SPL5", "Carbon Wing Type 3")
AddTextEntry("SENTGTS_SPL6", "Carbon Wing Type 4")
AddTextEntry("SENTGTS_SPL7", "Carbon Wing Type 5")
AddTextEntry("SENTGTS_SKIRT1", "Black Skirt")
AddTextEntry("SENTGTS_SKIRT2", "Carbon Skirt")

AddTextEntry("SENTGTS_LIV1", "Gold Kammsport")
AddTextEntry("SENTGTS_LIV2", "White Kammsport")
AddTextEntry("SENTGTS_LIV3", "Black Kammsport")
AddTextEntry("SENTGTS_LIV4", "Speed Demon")
AddTextEntry("SENTGTS_LIV5", "Performance Graphics")
AddTextEntry("SENTGTS_LIV6", "STD Stripes")
AddTextEntry("SENTGTS_LIV7", "STD Stripes Alt.")
AddTextEntry("SENTGTS_LIV8", "Ocean Showroom")
AddTextEntry("SENTGTS_LIV9", "Sunset Showroom")
AddTextEntry("SENTGTS_LIV10", "Competition Showroom")
AddTextEntry("SENTGTS_LIV11", "GTS Evolved Blue")
AddTextEntry("SENTGTS_LIV12", "GTS Evolved Red")
AddTextEntry("SENTGTS_LIV13", "GTS Evolved Green")
AddTextEntry("SENTGTS_LIV14", "GTS Evolved Deutsche")
end)