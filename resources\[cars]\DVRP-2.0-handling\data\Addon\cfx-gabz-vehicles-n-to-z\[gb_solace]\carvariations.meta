<?xml version="1.0" encoding="UTF-8"?>

<CVehicleModelInfoVariation>
  <variationData>
   <Item>
      <modelName>gbsolace</modelName>
      <colors>
        <Item>
          <indices content="char_array">
            91
            70
            91
            120
            47
            70
          </indices>
          <liveries>
            <Item value="false" />
          </liveries>
        </Item>
        <Item>
          <indices content="char_array">
            49
            4
            49
            98
            1
            134
          </indices>
          <liveries>
            <Item value="false" />
          </liveries>
        </Item>
        <Item>
          <indices content="char_array">
            71
            89
            71
            98
            1
            134
          </indices>
          <liveries>
            <Item value="false" />
          </liveries>
        </Item>
        <Item>
          <indices content="char_array">
            27
            1
            30
            120
            1
            134
          </indices>
          <liveries>
            <Item value="false" />
          </liveries>
        </Item>
        <Item>
          <indices content="char_array">
            6
            147
            6
            120
            1
            134
          </indices>
          <liveries>
            <Item value="false" />
          </liveries>
        </Item>
        <Item>
          <indices content="char_array">
            66
            38
            60
            3
            1
            134
          </indices>
          <liveries>
            <Item value="false" />
          </liveries>
        </Item>
      </colors>
      <kits>
        <Item>814_gbsolace_modkit</Item>
      </kits>
      <windowsWithExposedEdges />
      <plateProbabilities>
        <Probabilities>
          <Item>
            <Name>Standard White</Name>
            <Value value="100" />
          </Item>
        </Probabilities>
      </plateProbabilities>
      <lightSettings value="119" />
      <sirenSettings value="0" />
    </Item>     
  </variationData>
</CVehicleModelInfoVariation>