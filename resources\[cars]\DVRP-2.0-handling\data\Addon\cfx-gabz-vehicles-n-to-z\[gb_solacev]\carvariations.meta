<?xml version="1.0" encoding="UTF-8"?>

<CVehicleModelInfoVariation>
  <variationData>
   <Item>
      <modelName>gbsolacev</modelName>
      <colors>
        <Item>
          <indices content="char_array">
            91
            70
            91
            120
            47
            22
          </indices>
          <liveries>
            <Item value="false" />
          </liveries>
        </Item>
        <Item>
          <indices content="char_array">
            49
            4
            49
            120
            3
            22
          </indices>
          <liveries>
            <Item value="false" />
          </liveries>
        </Item>
        <Item>
          <indices content="char_array">
            71
            89
            71
            120
            3
            22
          </indices>
          <liveries>
            <Item value="false" />
          </liveries>
        </Item>
        <Item>
          <indices content="char_array">
            27
            1
            30
            120
            3
            22
          </indices>
          <liveries>
            <Item value="false" />
          </liveries>
        </Item>
        <Item>
          <indices content="char_array">
            6
            147
            6
            120
            3
            22
          </indices>
          <liveries>
            <Item value="false" />
          </liveries>
        </Item>
        <Item>
          <indices content="char_array">
            66
            38
            60
            120
            3
            22
          </indices>
          <liveries>
            <Item value="false" />
          </liveries>
        </Item>
      </colors>
      <kits>
        <Item>812_gbsolacev_modkit</Item>
      </kits>
      <windowsWithExposedEdges />
      <plateProbabilities>
        <Probabilities>
          <Item>
            <Name>Standard White</Name>
            <Value value="100" />
          </Item>
        </Probabilities>
      </plateProbabilities>
      <lightSettings value="119" />
      <sirenSettings value="0" />
    </Item>     
  </variationData>
</CVehicleModelInfoVariation>