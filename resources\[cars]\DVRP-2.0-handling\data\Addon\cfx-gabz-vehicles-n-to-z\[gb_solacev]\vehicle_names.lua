Citizen.CreateThread(function()
AddTextEntry("S<PERSON><PERSON><PERSON><PERSON>", "Solace Vitesse")

AddTextEntry("SOLACE_SKIRT_1", "Carbon Skirt")

AddTextEntry("SOLACE_SPOILER_0", "Delete Spoiler")
AddTextEntry("SOL<PERSON>E_SPOILER_1", "Racing Lip Spoiler")
AddTextEntry("SOLACE_SPOILER_2", "Carbon Spoiler")
AddTextEntry("SOLACE_SPOILER_3", "Sport Spoiler")
AddTextEntry("SOLACE_SPOILER_4", "Trunk Rack")
AddTextEntry("SOLACEV_PILLAR_1", "Piano Black Trim")
AddTextEntry("SOLACE_ROOF_1", "Painted Windshield Frame")
AddTextEntry("SOLACE_ROOF_2", "Roof Rack")
AddTextEntry("SOL<PERSON>E_ROOF_3", "Roof Rack with Tent")
AddTextEntry("SOLACE_HOOD_1", "Subtle Vented Hood")
AddTextEntry("SOLACE_HOOD_2", "Aero Duct Hood")
AddTextEntry("SOLACE_HOOD_3", "Vented Aero Duct Hood")
AddTextEntry("SOLACE_HOOD_4", "Racing Hood")
AddTextEntry("SOLACE_HOOD_5", "Tuner Hood")
AddTextEntry("SOLACE_BF_A_0", "Deleted Grille Facelift Bumper")
AddTextEntry("SOLACE_BF_A_1", "Simple Black Grille Facelift Bumper")
AddTextEntry("SOLACE_BF_A_2", "Simple Chrome Grille Facelift Bumper")
AddTextEntry("SOLACE_BF_A_3", "Luxurious Black Grille Facelift Bumper")
AddTextEntry("SOLACE_BF_A_4", "Luxruious Chrome Grille Facelift Bumper")
AddTextEntry("SOLACE_BF_A_5", "Racing Black Grille Facelift Bumper")
AddTextEntry("SOLACE_BF_A_6", "Racing Premium Black Grille Facelift Bumper")
AddTextEntry("SOLACE_BF_A_7", "Racing Chrome Grille Facelift Bumper")
AddTextEntry("SOLACE_BF_B_0", "Deleted Grille Custom Bumper")
AddTextEntry("SOLACE_BF_B_1", "Luxurious Black Grille Custom Bumper")
AddTextEntry("SOLACE_BF_B_2", "Luxurious Accent Grille Custom Bumper")
AddTextEntry("SOLACE_BF_B_3", "Luxurious Chrome Grille Custom Bumper")
AddTextEntry("SOLACE_BF_B_4", "Horizontal Black Grille Custom Bumper")
AddTextEntry("SOLACE_BF_B_5", "Horizontal Chrome Grille Custom Bumper")
AddTextEntry("SOLACE_BF_B_6", "Racing Black Grille Custom Bumper")
AddTextEntry("SOLACE_BF_B_7", "Racing Chrome Grille Custom Bumper")
AddTextEntry("SOLACE_BF_C_0", "Deleted Grille Retro Bumper")
AddTextEntry("SOLACE_BF_C_1", "Luxurious Black Grille Retro Bumper")
AddTextEntry("SOLACE_BF_C_2", "Luxurious Accent Grille Retro Bumper")
AddTextEntry("SOLACE_BF_C_3", "Racing Black Grille Retro Bumper")
AddTextEntry("SOLACE_BF_C_4", "Horizontal Chrome Grille Retro Bumper")
AddTextEntry("SOLACE_BF_C_5", "Horizontal Black Grille Retro Bumper")
AddTextEntry("SOLACE_BF_C_6", "Luxurious Accent Grille Retro Bumper")
AddTextEntry("SOLACE_BF_C_7", "Luxurious Black Grille Retro Bumper")
AddTextEntry("SOLACE_BR_STOCK_0", "Diffuser Delete")
AddTextEntry("SOLACE_BR_STOCK_1", "Chrome Round Exhausts")
AddTextEntry("SOLACE_BR_STOCK_2", "Carbon Round Exhausts")
AddTextEntry("SOLACE_BR_STOCK_3", "Chrome Round Exhausts and Diffuser")
AddTextEntry("SOLACE_BR_STOCK_4", "Carbon Round Exhausts and Diffuser")
AddTextEntry("SOLACE_BR_A_1", "Custom Rear Bumper")
AddTextEntry("SOLACE_BR_A_2", "Custom Facelift Rear Bumper")
AddTextEntry("SOLACE_BR_B_1", "Racing Rear Bumper")
AddTextEntry("SOLACE_BR_B_2", "Racing Facelift Rear Bumper")
AddTextEntry("SOLACE_BF_D_1", "Chopped Front Bumper")

AddTextEntry("SOLACE_LIV1", "Air Herler")
AddTextEntry("SOLACE_LIV2", "Her Majesty")
AddTextEntry("SOLACE_LIV3", "Performance Stripe Green")
AddTextEntry("SOLACE_LIV4", "Performance Stripe Gray")
AddTextEntry("SOLACE_LIV5", "Performance Stripe Black")
AddTextEntry("SOLACE_LIV6", "Performance Stripe White")
AddTextEntry("SOLACEV_LIV7", "Dewbauchee Terroil Racing #33")
end)