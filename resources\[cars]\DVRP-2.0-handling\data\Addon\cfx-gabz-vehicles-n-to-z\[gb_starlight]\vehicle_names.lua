Citizen.CreateThread(function()
	AddTextEntry("STRLGHT", "Starlight")
	AddTextEntry("STARLIGHT_BAR1", "Bullbar")
	AddTextEntry("STARLIGHT_BAR2", "Bullbar MK2")
	AddTextEntry("STARLIGHT_MOLD", "Plastic Door Molding")
	AddTextEntry("STARLIGHT_ROOFP", "Primary Color Roof")
	AddTextEntry("STARLIGHT_ROOF1", "Round Roof Lights")
	AddTextEntry("STARLIGHT_ROOF1A", "Light Bar")
	AddTextEntry("STARLIGHT_ROOF2", "Roof Rack")
	AddTextEntry("STARLIGHT_ROOF2A", "Roof Rack w/ Roofbox")
	AddTextEntry("STARLIGHT_ROOF3", "Sport Roof Rack")
	AddTextEntry("STARLIGHT_ROOF3A", "Sport Roof Rack w/ Round Lights")
	AddTextEntry("STARLIGHT_ROOF3B", "Sport Roof Rack w/ Light Bar")
	AddTextEntry("STARLIGHT_ROOF3C", "Sport Roof Rack w/ Cargo")

	AddTextEntry("STARLIGHT_LIV1", "Got Mud? (Black)")
	AddTextEntry("STARLIGHT_LIV2", "Got Mud? (Gray)")
	AddTextEntry("STARLIGHT_LIV3", "Got Mud? (White)")
	AddTextEntry("STARLIGHT_LIV4", "Tracker (White)")
	AddTextEntry("STARLIGHT_LIV5", "Tracker (Light)")
	AddTextEntry("STARLIGHT_LIV6", "Tracker (Black)")
	AddTextEntry("STARLIGHT_LIV7", "Tracker (Dark)")
	AddTextEntry("STARLIGHT_LIV8", "Tracker (Yellow)")
	AddTextEntry("STARLIGHT_LIV9", "Tracker (Green)")
	AddTextEntry("STARLIGHT_LIV10", "Tracker (Red)")
	AddTextEntry("STARLIGHT_LIV11", "Tracker (Orange)")
	AddTextEntry("STARLIGHT_LIV12", "Tracker (Blue)")
	AddTextEntry("STARLIGHT_LIV13", "Stripes (Swedish)")
	AddTextEntry("STARLIGHT_LIV14", "Stripes (Finnish)")
	AddTextEntry("STARLIGHT_LIV15", "Stripes (Danish)")
	AddTextEntry("STARLIGHT_LIV16", "Stripes (Norwegian)")
	AddTextEntry("STARLIGHT_LIV17", "Stripes (Icelandic)")
	AddTextEntry("STARLIGHT_LIV18", "Promotional (Black-White)")
	AddTextEntry("STARLIGHT_LIV19", "Promotional (Black-Blue)")
	AddTextEntry("STARLIGHT_LIV20", "Promotional (White-Yellow)")
	AddTextEntry("STARLIGHT_LIV21", "Retro Mosaic")
	AddTextEntry("STARLIGHT_LIV22", "Modern Mosaic")
end)