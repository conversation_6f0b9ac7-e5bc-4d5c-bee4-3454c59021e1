<?xml version="1.0" encoding="UTF-8"?>
<CVehicleModelInfo__InitDataList>
  <residentTxd>vehshare</residentTxd>
  <residentAnims />
  <InitDatas>
 	<Item>
      <modelName>gbstarlight</modelName>
      <txdName>gbstarlight</txdName>
      <handlingId>STRLGHT</handlingId>
      <gameName>STRLGHT</gameName>
      <vehicleMakeName>VULCAR</vehicleMakeName>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected />
      <ptfxAssetName>null</ptfxAssetName>
      <audioNameHash />
      <layout>LAYOUT_RANGER_TOROS</layout>
      <coverBoundOffsets>NOVAK_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout />
      <cameraName>DEFAULT_FOLLOW_VEHICLE_CAMERA</cameraName>
      <aimCameraName>DEFAULT_THIRD_PERSON_VEHICLE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>VEHICLE_BONNET_CAMERA_STANDARD_LONG_EXTRA_LOW</bonnetCameraName>
      <povCameraName>DEFAULT_POV_CAMERA</povCameraName>
      <FirstPersonDriveByIKOffset x="0.060000" y="-0.100000" z="-0.045000" />
      <FirstPersonDriveByUnarmedIKOffset x="0.000000" y="-0.060000" z="-0.010000" />
	  <FirstPersonProjectileDriveByIKOffset x="0.055000" y="-0.120000" z="-0.070000" />
	  <FirstPersonProjectileDriveByPassengerIKOffset x="0.000000" y="-0.150000" z="-0.030000" />
	  <FirstPersonProjectileDriveByRearLeftIKOffset x="-0.060000" y="-0.040000" z="-0.060000" />
	  <FirstPersonProjectileDriveByRearRightIKOffset x="0.060000" y="-0.080000" z="-0.060000" />
	  <FirstPersonDriveByLeftPassengerIKOffset x="0.040000" y="0.000000" z="-0.010000" />
	  <FirstPersonDriveByRightPassengerIKOffset  x="-0.040000" y="-0.070000" z="-0.055000" />
	  <FirstPersonDriveByRightRearPassengerIKOffset x="-0.040000" y="0.000000" z="-0.010000" />
	  <FirstPersonDriveByRightPassengerUnarmedIKOffset x="0.000000" y="-0.070000" z="-0.010000" />
      <FirstPersonDriveByLeftPassengerUnarmedIKOffset x="0.000000" y="-0.060000" z="-0.010000" />
	  <FirstPersonMobilePhoneOffset x="0.140000" y="0.138000" z="0.540000" />
      <FirstPersonPassengerMobilePhoneOffset x="0.220000" y="0.113000" z="0.415000" />
      <FirstPersonMobilePhoneSeatIKOffset>
		<Item>
			<Offset x="0.220000" y="0.230000" z="0.440000" />
			<SeatIndex value="2" />
		</Item>
		<Item>
			<Offset x="0.210000" y="0.220000" z="0.440000" />
			<SeatIndex value="3" />
		</Item>
      </FirstPersonMobilePhoneSeatIKOffset>
      <PovCameraOffset x="0.000000" y="-0.205000" z="0.655000" />
      <PovCameraVerticalAdjustmentForRollCage value="0.000000" />
      <PovPassengerCameraOffset x="0.010000" y="0.000000" z="0.030000" />
	  <PovRearPassengerCameraOffset x="0.015000" y="0.100000" z="0.030000" />
      <vfxInfoName>VFXVEHICLEINFO_CAR_OFFROAD</vfxInfoName>
      <shouldUseCinematicViewMode value="true" />
      <shouldCameraTransitionOnClimbUpDown value="false" />
      <shouldCameraIgnoreExiting value="false" />
      <AllowPretendOccupants value="true" />
      <AllowJoyriding value="true" />
      <AllowSundayDriving value="true" />
      <AllowBodyColorMapping value="true" />
      <wheelScale value="0.221750" />
      <wheelScaleRear value="0.221750" />
      <dirtLevelMin value="0.000000" />
      <dirtLevelMax value="0.850000" />
      <envEffScaleMin value="0.000000" />
      <envEffScaleMax value="1.000000" />
      <envEffScaleMin2 value="0.000000" />
      <envEffScaleMax2 value="1.000000" />
      <damageMapScale value="0.400000" />
      <damageOffsetScale value="0.400000" />
      <diffuseTint value="0x00FFFFFF" />
      <steerWheelMult value="1.000000" />
      <HDTextureDist value="5.000000" />
      <lodDistances content="float_array">
        35.000000
        200.000000
        300.000000
        500.000000
        500.000000
        500.000000
      </lodDistances>
      <minSeatHeight value="0.400" />
      <identicalModelSpawnDistance value="20" />
      <maxNumOfSameColor value="10" />
      <defaultBodyHealth value="1000.000000" />
      <pretendOccupantsScale value="1.000000" />
      <visibleSpawnDistScale value="1.000000" />
      <trackerPathWidth value="2.000000" />
      <weaponForceMult value="1.000000" />
      <frequency value="30" />
      <swankness>SWANKNESS_1</swankness>
      <maxNum value="5" />
      <flags>FLAG_AVERAGE_CAR FLAG_POOR_CAR FLAG_IS_OFFROAD_VEHICLE FLAG_HEADLIGHTS_USE_ACTUAL_BONE_POS FLAG_IS_BULKY FLAG_HAS_INTERIOR_EXTRAS FLAG_CARGOBOB_HOOK_UP_CHASSIS FLAG_NO_HEAVY_BRAKE_ANIMATION</flags>
      <type>VEHICLE_TYPE_CAR</type>
      <plateType>VPT_BACK_PLATES</plateType>
	  <dashboardType>VDT_BANSHEE</dashboardType>
      <vehicleClass>VC_SUV</vehicleClass>
      <wheelType>VWT_SUV</wheelType>
      <trailers />
      <additionalTrailers />
      <drivers />
      <extraIncludes />
      <doorsWithCollisionWhenClosed />
      <driveableDoors />
      <bumpersNeedToCollideWithMap value="false" />
      <needsRopeTexture value="false" />
      <requiredExtras>EXTRA_1</requiredExtras>
      <rewards />
      <cinematicPartCamera>
        <Item>WHEEL_WIDE_REAR_RIGHT_CAMERA</Item>
        <Item>WHEEL_WIDE_REAR_LEFT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet>Truck</NmBraceOverrideSet>
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000" />
      <buoyancySphereSizeScale value="1.000000" />
      <pOverrideRagdollThreshold type="NULL" />
      <firstPersonDrivebyData>
        <Item>VAN_NOVAK_FRONT_LEFT</Item>
        <Item>VAN_NOVAK_FRONT_RIGHT</Item>
        <Item>VAN_NOVAK_REAR_LEFT</Item>
        <Item>VAN_NOVAK_REAR_RIGHT</Item>
      </firstPersonDrivebyData>
    </Item>     
  </InitDatas>
  <txdRelationships>
    <Item>
      <parent>vehicles_fmj_interior</parent>
      <child>vehicles_starlight_interior</child>
    </Item>
    <Item>
      <parent>vehicles_starlight_interior</parent>
      <child>gbstarlight</child>
    </Item>
  </txdRelationships>
</CVehicleModelInfo__InitDataList>
  