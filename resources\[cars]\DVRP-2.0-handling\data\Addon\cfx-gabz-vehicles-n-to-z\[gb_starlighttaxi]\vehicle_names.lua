function AddTextEntry(key, value)
	Citizen.InvokeNative(GetHash<PERSON>ey("ADD_TEXT_ENTRY"), key, value)
end

Citizen.CreateThread(function()
	AddTextEntry("TAXISTRLGHT", "Taxi Starlight")
	AddTextEntry("STARLIGHT_BAR1", "Bullbar")
	AddTextEntry("STARLIGHT_BAR2", "Bullbar MK2")
	AddTextEntry("STARLIGHT_MOLD", "Plastic Door Molding")
	AddTextEntry("STARLIGHT_ROOFP", "Primary Color Roof")
	AddTextEntry("STARLIGHT_ROOF1", "Round Roof Lights")
	AddTextEntry("STARLIGHT_ROOF1A", "Light Bar")
	AddTextEntry("STARLIGHT_ROOF2", "Roof Rack")
	AddTextEntry("STARLIGHT_ROOF2A", "Roof Rack w/ Roofbox")
	AddTextEntry("STARLIGHT_ROOF3", "Sport Roof Rack")
	AddTextEntry("STARLIGHT_ROOF3A", "Sport Roof Rack w/ Round Lights")
	AddTextEntry("STARLIGHT_ROOF3B", "Sport Roof Rack w/ Light Bar")
	AddTextEntry("STARLIGHT_ROOF3C", "Sport Roof Rack w/ Cargo")
    AddTextEntry("TAXISTARLIGHT_LIV1", "Downtown Cab Co")
end)