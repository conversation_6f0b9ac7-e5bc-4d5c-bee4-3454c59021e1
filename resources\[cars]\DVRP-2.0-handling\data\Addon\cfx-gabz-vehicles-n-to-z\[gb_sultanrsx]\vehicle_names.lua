Citizen.CreateThread(function()
AddTextEntry("GBSULTRSX", "Sultan RSx")
AddTextEntry("SULTRSX_WING0", "Remove Spoiler")
AddTextEntry("SULTRSX_WING1", "Classic Spoiler")
AddTextEntry("SULTRSX_WING2", "Mid Level Spoiler")
AddTextEntry("SULTRSX_WING3", "Low Level Wing")
AddTextEntry("SULTRSX_WING4", "X-Mount Wing")
AddTextEntry("SULTRSX_WING5", "Intermediate Wing")
AddTextEntry("SULTRSX_WING6", "Drift Wing")
AddTextEntry("SULTRSX_WING7", "Mid Level Wing")
AddTextEntry("SULTRSX_WING8", "Track Wing")
AddTextEntry("SULTRSX_WING9", "High Level Wing")
AddTextEntry("SULTRSX_WING10", "Circuit Wing")
AddTextEntry("SULTRSX_WING11", "Extreme Street Spoiler")
AddTextEntry("SULTRSX_WING12", "Time Attack Wing")
AddTextEntry("SULTRSX_SPLIT1", "Track Splitter")
AddTextEntry("SULTRSX_SPLIT2", "Custom Splitter")
AddTextEntry("SULTRSX_SPLIT3", "Accent Splitter")
AddTextEntry("SULTRSX_SPLIT3A", "Carbon Accent Splitter")
AddTextEntry("SULTRSX_SPLIT4", "Super Splitter")
AddTextEntry("SULTRSX_SPLIT4A", "Carbon Super Splitter")
AddTextEntry("SULTRSX_SPLIT4B", "Painted Super Splitter")
AddTextEntry("SULTRSX_DIFF1", "Custom Diffuser")
AddTextEntry("SULTRSX_DIFF2", "Track Diffuser")
AddTextEntry("SULTRSX_SKIRT1", "Custom Skirt")
AddTextEntry("SULTRSX_SKIRT1A", "Carbon Custom Skirt")
AddTextEntry("SULTRSX_SKIRT2", "Carbon Street Skirt")
AddTextEntry("SULTRSX_SKIRT3", "Race Skirt")
AddTextEntry("SULTRSX_SKIRT3A", "Carbon Race Skirt")
AddTextEntry("SULTRSX_SKIRT4", "Track Skirt")
AddTextEntry("SULTRSX_SKIRT4A", "Carbon Track Skirt")
AddTextEntry("SULTRSX_SKIRT5", "Super Skirt")
AddTextEntry("SULTRSX_SKIRT5A", "Carbon Super Skirt")
AddTextEntry("SULTRSX_EYELID1", "Small Eyelid")
AddTextEntry("SULTRSX_EYELID2", "Sleepy Eyelid")
AddTextEntry("SULTRSX_CANARD", "Front Canards")
AddTextEntry("SULTRSX_RSCOOP1", "Track Roof Scoop")
AddTextEntry("SULTRSX_RSCOOP2", "Custom Roof Scoop")
AddTextEntry("SULTRSX_RSCOOP3", "Street Roof Scoop")
AddTextEntry("SULTRSX_RSCOOP4", "Race Roof Scoop")
AddTextEntry("SULTRSX_RSCOOP5", "Track Roof Scoop")
AddTextEntry("SULTRSX_RSCOOP6", "Competition Roof Scoop")
AddTextEntry("SULTRSX_AERIAL1", "Factory Shark Fin")
AddTextEntry("SULTRSX_AERIAL2", "Carbon Shark Fin")
AddTextEntry("SULTRSX_AERIAL3", "Snub Shark Fin")
AddTextEntry("SULTRSX_AERIAL4", "Race Aerials")
AddTextEntry("SULTRSX_AERIAL5", "Track Aerials")

AddTextEntry("GBSULTANRSX_LIV1", "Base Stripe Black")
AddTextEntry("GBSULTANRSX_LIV2", "Base Stripe White")
AddTextEntry("GBSULTANRSX_LIV3", "Base Stripe Red")
AddTextEntry("GBSULTANRSX_LIV4", "Base Stripe Yellow")
AddTextEntry("GBSULTANRSX_LIV5", "Base Stripe Blue")
AddTextEntry("GBSULTANRSX_LIV6", "Twin Stripes Black")
AddTextEntry("GBSULTANRSX_LIV7", "Twin Stripes White")
AddTextEntry("GBSULTANRSX_LIV8", "Twin Stripes Red")
AddTextEntry("GBSULTANRSX_LIV9", "Twin Stripes Yellow")
AddTextEntry("GBSULTANRSX_LIV10", "Twin Stripes Blue")
AddTextEntry("GBSULTANRSX_LIV11", "Racer #20")
AddTextEntry("GBSULTANRSX_LIV12", "Cherry Breeze")
AddTextEntry("GBSULTANRSX_LIV13", "KRAP Rally Team #33")
AddTextEntry("GBSULTANRSX_LIV14", "Street Style Green")
AddTextEntry("GBSULTANRSX_LIV15", "Street Style Pink")
AddTextEntry("GBSULTANRSX_LIV16", "Street Style Orange")
AddTextEntry("GBSULTANRSX_LIV17", "Street Style Blue")
AddTextEntry("GBSULTANRSX_LIV18", "Princess Robot Bubblegum")
AddTextEntry("GBSULTANRSX_LIV19", "Fujiwara Racing")
AddTextEntry("GBSULTANRSX_LIV20", "Patterned Speed")
end)