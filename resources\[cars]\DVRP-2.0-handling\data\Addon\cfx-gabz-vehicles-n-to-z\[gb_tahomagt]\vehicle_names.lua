Citizen.CreateThread(function()
	AddTextEntry("TAHOMAGT", "Tahoma GT")
	AddTextEntry("TAHGT_SPL1A", "Primary Sports Spoiler")
	AddTextEntry("TAHGT_SPL1B", "Carbon Sports Spoiler")
	AddTextEntry("TAHGT_EXH3", "Thin Box Exhausts")
	AddTextEntry("TAHGT_EXH5", "Dual Side Pipes")
	AddTextEntry("TAHGT_EXH6", "Single Side Pipes")
	AddTextEntry("TAHGT_EXH9", "Quad Side Pipes")
	AddTextEntry("TAHGT_HOOD1", "Performance Hood")
	AddTextEntry("TAHGT_HOOD2", "Cowl Induction Hood")
	AddTextEntry("TAHGT_HOOD6", "Box Intake Bugcatcher")
	AddTextEntry("TAHGT_HOOD7", "Triple Intake Box Bugcatcher")
	AddTextEntry("TAHGT_SPLT1A", "Painted Chin Spoiler")
	AddTextEntry("TAHGT_SPLT2A", "Painted Extended Chin Spoiler")
	AddTextEntry("TAHGT_SPLT3", "Race Chin Spoiler")
	AddTextEntry("TAHGT_SPLT3A", "Painted Race Chin Spoiler")
	AddTextEntry("TAHGT_ROOF0A", "Painted Roof")
	AddTextEntry("TAHGT_ROOF1", "Zebra Skin Roof")
	AddTextEntry("TAHGT_ROOF2", "Tiger Skin Roof")
	AddTextEntry("TAHGT_ROOF3", "Leopard Skin Roof")
	AddTextEntry("TAHGT_ROOF4", "Croc Skin Roof")
	AddTextEntry("TAHGT_TRIM1", "Black Side Trim")
	AddTextEntry("TAHGT_TRIM2", "Painted Side Trim")
	AddTextEntry("TAHGT_TRIMREM", "Remove Side Trim")
end)