<?xml version="1.0" encoding="UTF-8"?>

<CHandlingDataMgr>
  <HandlingData>
	<Item type="CHandlingData">
      <handlingName>GBTERRORIZER</handlingName>
      <fMass value="8750.000000" />
      <fInitialDragCoeff value="10.000000" />
      <fDownforceModifier value="0.38" />
      <fPercentSubmerged value="85.000000" />
      <vecCentreOfMassOffset x="0.000000" y="0.000000" z="0.00000" />
      <vecInertiaMultiplier x="1.40000000" y="1.8000000" z="2.20000" />
      <fDriveBiasFront value="0.00000" />
      <nInitialDriveGears value="4" />
      <fInitialDriveForce value="0.21000" />
      <fDriveInertia value="0.5000" />
      <fClutchChangeRateScaleUpShift value="2.0000000" />
      <fClutchChangeRateScaleDownShift value="2.00000" />
      <fInitialDriveMaxFlatVel value="140.000000" />
      <fBrakeForce value="0.300000" />
      <fBrakeBiasFront value="0.650000" />
      <fHandBrakeForce value="0.800000" />
      <fSteeringLock value="28.000000" />
      <fTractionCurveMax value="1.800" />
      <fTractionCurveMin value="1.500" />
      <fTractionCurveLateral value="24.000000" />
      <fTractionSpringDeltaMax value="0.175000" />
      <fLowSpeedTractionLossMult value="0.33300" />
      <fCamberStiffnesss value="0.000000" />
      <fTractionBiasFront value="0.49500" />
      <fTractionLossMult value="0.200000" />
      <fSuspensionForce value="2.2000000" />
      <fSuspensionCompDamp value="1.000000" />
      <fSuspensionReboundDamp value="1.0000" />
      <fSuspensionUpperLimit value="0.17000" />
      <fSuspensionLowerLimit value="-0.17000" />
      <fSuspensionRaise value="0.000000" />
      <fSuspensionBiasFront value="0.500000" />
      <fAntiRollBarForce value="0.200000" />
      <fAntiRollBarBiasFront value="0.500000" />
      <fRollCentreHeightFront value="0.40000" />
      <fRollCentreHeightRear value="0.40000" />
      <fCollisionDamageMult value="0.100000" />
      <fWeaponDamageMult value="0.100000" />
      <fDeformationDamageMult value="0.150000" />
      <fEngineDamageMult value="0.200000" />
      <fPetrolTankVolume value="80.000000" />
      <fOilVolume value="8.000000" />
      <fSeatOffsetDistX value="0.000000" />
      <fSeatOffsetDistY value="0.000000" />
      <fSeatOffsetDistZ value="0.000000" />
      <nMonetaryValue value="50000" />
      <strModelFlags>2220010</strModelFlags>
      <strHandlingFlags>2</strHandlingFlags>
      <strDamageFlags>0</strDamageFlags>
      <AIHandling>AVERAGE</AIHandling>
      <SubHandlingData>
		    <Item type="CVehicleWeaponHandlingData">
          <uWeaponHash>
            <Item>VEHICLE_WEAPON_TURRET_INSURGENT</Item>
            <Item />
            <Item />
          </uWeaponHash>
          <WeaponSeats content="int_array">
            8 
            0 
            0 
          </WeaponSeats>
          <fTurretSpeed content="float_array">
            10.000000	
            0.000000	
          </fTurretSpeed>
          <fTurretPitchMin content="float_array">
            -1.000000
            0.000000	
          </fTurretPitchMin>
          <fTurretPitchMax content="float_array">
            0.707000	
            0.000000	
          </fTurretPitchMax>
          <fTurretCamPitchMin content="float_array">
            -0.500000	
            0.000000	
          </fTurretCamPitchMin>
          <fTurretCamPitchMax content="float_array">
            0.000000	
            0.000000	
          </fTurretCamPitchMax>
          <fBulletVelocityForGravity content="float_array">
            0.000000	
            0.000000	
          </fBulletVelocityForGravity>
          <fTurretPitchForwardMin content="float_array">
            -0.080000	
            0.000000	
          </fTurretPitchForwardMin>
          <fUvAnimationMult value="0.000000" />
          <fMiscGadgetVar value="0.000000" />
          <fWheelImpactOffset value="0.000000" />
        </Item>
        <Item type="CCarHandlingData">
          <fBackEndPopUpCarImpulseMult value="0.100000"/>
          <fBackEndPopUpBuildingImpulseMult value="0.030000"/>
          <fBackEndPopUpMaxDeltaSpeed value="0.600000"/>
          <strAdvancedFlags>8000000</strAdvancedFlags>
        </Item>
        <Item type="NULL" />
      </SubHandlingData>
    </Item>
  </HandlingData>
</CHandlingDataMgr>