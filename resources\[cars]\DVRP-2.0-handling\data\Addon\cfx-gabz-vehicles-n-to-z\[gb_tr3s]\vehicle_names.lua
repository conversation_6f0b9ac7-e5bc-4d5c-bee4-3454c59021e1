Citizen.CreateThread(function()
AddTextEntry("TR3S", "TR3-S")
AddTextEntry("TR3S_MIRROR_1", "Painted Mirrors")
AddTextEntry("TR3S_MIRROR_2", "Carbon Mirrors")
AddTextEntry("TR3S_MIRROR_3", "Painted Mirrors w/ Carbon Base")
AddTextEntry("TR3S_MIRROR_4", "Full Carbon Mirrors")
AddTextEntry("TR3S_AIRVENT_1", "Fender Vent")
AddTextEntry("TR3S_EXHAUST_1", "Sport Exhaust")
AddTextEntry("TR3S_EXHAUST_2", "Racing Exhaust")
AddTextEntry("TR3S_EXHAUST_3", "Competition Exhaust")
AddTextEntry("TR3S_ROOF_1", "Carbon Roof")
AddTextEntry("TR3S_ROOF_2", "Painted Roof Scoop")
AddTextEntry("TR3S_ROOF_3", "Carbon Roof Scoop")
AddTextEntry("TR3S_FIN_1", "Fins w/Accent")
AddTextEntry("TR3S_FIN_2", "Carbon Fins w/ Accent")
AddTextEntry("TR3S_BUMPF_1", "Splitter #1 w/ Accent")
AddTextEntry("TR3S_BUMPF_2", "Carbon Splitter #1 w/ Accent")
AddTextEntry("TR3S_BUMPF_3", "Splitter #2 w/ Accent")
AddTextEntry("TR3S_BUMPF_4", "Carbon Splitter #2 w/ Accent")
AddTextEntry("TR3S_BUMPF_5", "Facelift Bumper")
AddTextEntry("TR3S_BUMPF_6", "Facelift Bumper w/ Splitter")
AddTextEntry("TR3S_BUMPF_7", "Facelift Bumper w/ Racing Splitter")
AddTextEntry("TR3S_RDIFF_1", "Diffuser w/ Accent")
AddTextEntry("TR3S_RDIFF_2", "Carbon Diffuser w/ Accent")
AddTextEntry("TR3S_BUMPR_1", "Rear w/ Accent")
AddTextEntry("TR3S_BUMPR_2", "Carbon Rear w/ Accent")
AddTextEntry("TR3S_SKIRT_1", "Skirt w/ Accent")
AddTextEntry("TR3S_SKIRT_2", "Carbon Skirt w/ Accent")
AddTextEntry("TR3S_SPOILER_1", "Fixated Spoiler")
AddTextEntry("TR3S_SPOILER_2", "Lifted Spoiler")
AddTextEntry("TR3S_SPOILER_3", "Sport Spoiler")
AddTextEntry("TR3S_SPOILER_4", "Sport Spoiler w/ Struts")
AddTextEntry("TR3S_SPOILER_5", "Deactivated Spoiler")
AddTextEntry("TR3S_SPOILER_6", "Racing Spoiler")
AddTextEntry("TR3S_SPOILER_7", "Carbon Racing Spoiler")
AddTextEntry("TR3S_SPOILER_8", "Competition Spoiler")
AddTextEntry("TR3S_HOOD_1", "Carbon Hood")
AddTextEntry("TR3S_HOOD_2", "Racing Hood")
AddTextEntry("TR3S_HOOD_3", "Carbon Racing Hood")

AddTextEntry("TR3S_LIV1", "Black Stripes")
AddTextEntry("TR3S_LIV2", "Blue Stripes")
AddTextEntry("TR3S_LIV3", "Green Stripes")
AddTextEntry("TR3S_LIV4", "Orange Stripes")
AddTextEntry("TR3S_LIV5", "Red Stripes")
AddTextEntry("TR3S_LIV6", "White Stripes")
AddTextEntry("TR3S_LIV7", "Racing Heritage")
end)