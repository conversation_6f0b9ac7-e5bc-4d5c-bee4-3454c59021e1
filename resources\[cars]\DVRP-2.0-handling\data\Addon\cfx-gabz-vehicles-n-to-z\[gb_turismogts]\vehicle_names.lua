Citizen.CreateThread(function()
AddTextEntry("GBTURGTS", "Turismo 300GT Spider")
AddTextEntry("TURGT_SKIRT0A", "Plasitc Skirt")
AddTextEntry("TURGT_BUMF0A", "Stock Bumper w/ Fogs")
AddTextEntry("TURGT_BUMF0B", "Painted Stock Bumper")
AddTextEntry("TURGT_BUMF0C", "Painted Stock Bumper w/ Fogs")
AddTextEntry("TURGT_BUMF1", "GT Bumper")
AddTextEntry("TURGT_BUMF1A", "GT Bumper w/ Fogs")
AddTextEntry("TURGT_BUMF1B", "Painted GT Bumper")
AddTextEntry("TURGT_BUMF1C", "Painted GT Bumper w/ Fogs")
AddTextEntry("TURGT_BUMF2", "Race Bumper")
AddTextEntry("TURGT_BUMR0A", "Painted Stock Bumper")
AddTextEntry("TURGT_BUMR1", "Race Bumper")
AddTextEntry("TURGT_BUMR1A", "Painted Race Bumper")
AddTextEntry("TURGT_FLAPS1", "Mudflaps")
AddTextEntry("TURGT_EXH1", "Race Exhaust")
AddTextEntry("TURGT_EXH1A", "Race Exhaust w/ Diffuser")
AddTextEntry("TURGT_EXH2", "Street Exhaust")
AddTextEntry("TURGT_EXH3", "Side Exit Exhaust")
AddTextEntry("TURGT_EXH4", "Fart Cannons")
AddTextEntry("TURGT_EXH5", "Rally Exhaust")
AddTextEntry("TURGT_FRUNK0A", "Frunk Pins")
AddTextEntry("TURGT_FRUNK1", "Vented Frunk")
AddTextEntry("TURGT_FRUNK1A", "Vented Frunk w/ Pins")
AddTextEntry("TURGT_FRUNK2", "Plastic Vented Frunk")
AddTextEntry("TURGT_FRUNK2A", "Plastic Vented Frunk w/ Pins")
AddTextEntry("TURGT_FVENT1", "Wing Vents")	
AddTextEntry("TURGT_FVENT2", "Plastic Wing Vents")	
AddTextEntry("TURGT_RVENT1", "Side Vents")	
AddTextEntry("TURGT_HOOD0A", "Primary Engine Cover Slats")	
AddTextEntry("TURGT_HOOD0B", "Plastic Engine Cover Slats")
AddTextEntry("TURGT_AERIAL1", "Race Antenna")	
AddTextEntry("TURGT_ACS1", "Body & Hood Pins")	
AddTextEntry("TURGT_ACS2", "Tow Hooks")	
AddTextEntry("TURGT_ACS3", "Race Setup")	
AddTextEntry("TURGT_MIR1", "Facelift Mirrors")	
AddTextEntry("TURGT_RSPL1", "Roof Spoiler")	
AddTextEntry("TURGT_RSPL1A", "Primary Roof Spoiler")	
AddTextEntry("TURGT_RSPL1B", "Secondary Roof Spoiler")	
AddTextEntry("TURGT_BLADE0A", "Plastic Side Blades")	
AddTextEntry("TURGT_BLADE1", "Dual Side Blades")	
AddTextEntry("TURGT_BLADE1A", "Plastic Dual Side Blades")	
AddTextEntry("TURGT_BLADE2", "Single Side Blades")	
AddTextEntry("TURGT_BLADE2A", "Plastic Single Side Blades")	
AddTextEntry("TURGT_BLADE3", "Remove Side Blades")	
AddTextEntry("TOP_FVENTS", "Wing Vents")	
AddTextEntry("TOP_RVENTS", "Side Vents")	
AddTextEntry("TURGT_CAGE0A", "Race Seats")	
AddTextEntry("TURGT_CAGE0B", "Carbon Seats")
AddTextEntry("TURGT_CAGE1", "Primary Rollcage")	
AddTextEntry("TURGT_CAGE1A", "Race Seats w/ Primary Cage")	
AddTextEntry("TURGT_CAGE1B", "Carbon Seats w/ Primary Cage")
AddTextEntry("TURGT_CAGE2", "Secondary Rollcage")	
AddTextEntry("TURGT_CAGE2A", "Race Seats w/ Secondary Cage")	
AddTextEntry("TURGT_CAGE2B", "Carbon Seats w/ Secondary Cage")
AddTextEntry("TURGT_FOGS2", "Covered Rally Fogs")
AddTextEntry("TURGT_LIV1", "Premier-Grotti Rally #10")
AddTextEntry("TURGT_LIV2", "Team Homies Sharp #135")
AddTextEntry("TURGT_LIV3", "Palla-Grotti Rallye #072")
AddTextEntry("TURGT_LIV4", "American Classic Stripes")
AddTextEntry("TURGT_LIV5", "Tricolore Classic Stripes")
end)