Citizen.CreateThread(function()
AddTextEntry("GBVIVANT", "Vivant")
AddTextEntry("BORDEAUX", "Bordeaux")
AddTextEntry("VIVANT_SPOILER1", "Painted Stock Spoiler")
AddTextEntry("VIVANT_SPOILER2", "Extended Spoiler")
AddTextEntry("VIVANT_SPOILER3", "Large Spoiler")
AddTextEntry("VIVANT_SPOILER4", "GTS Spoiler")
AddTextEntry("VIVANT_SPOILER5", "Upswept Spoiler")
AddTextEntry("VIVANT_SPOILER6", "Whaletail Spoiler")
AddTextEntry("VIVANT_SPOILER7", "Rally Spoiler")
AddTextEntry("VIVANT_SPOILER8", "Carbon Rally Spoiler")
AddTextEntry("VIVANT_BUMF0A", "Painted Front Bumper")
AddTextEntry("VIVANT_BUMF1", "GTS Front Bumper")
AddTextEntry("VIVANT_BUMF1A", "GTS Front Bumper w/ Fogs")
AddTextEntry("VIVANT_BUMF1B", "GTS Front Bumper w/ Covered Fogs")
AddTextEntry("VIVANT_BUMF2", "Custom Front Bumper")
AddTextEntry("VIVANT_BUMF2A", "Custom Front Bumper w/ Fogs")
AddTextEntry("VIVANT_BUMF2B", "Custom Front Bumper w/ Covered Fogs")
AddTextEntry("VIVANT_BUMF2C", "Race Front Bumper")
AddTextEntry("VIVANT_SKIRT1", "Painted Skirt")
AddTextEntry("VIVANT_BUMR0A", "Painted Rear Bumper")
AddTextEntry("VIVANT_BUMR1", "GRB Rear Bumper")
AddTextEntry("VIVANT_BUMR2", "GTS Rear Bumper")
AddTextEntry("VIVANT_GRILL1", "Alternate Badge")
AddTextEntry("VIVANT_GRILL2", "Open Grille")
AddTextEntry("VIVANT_GRILL3", "Open Grille w/ Alt. Badge")
AddTextEntry("VIVANT_GRILL4", "Open Grille w/o Badge")
AddTextEntry("VIVANT_HOOD1", "Twin Vented Hood")
AddTextEntry("VIVANT_HOOD2", "Power Bulge Hood")
AddTextEntry("VIVANT_HOOD3", "Twin Vented Bulge Hood")
AddTextEntry("VIVANT_HOOD4", "Vented Hood")
AddTextEntry("VIVANT_HOOD5", "Race Hood")
AddTextEntry("VIVANT_HOOD6", "GRB Hood")
AddTextEntry("VIVANT_HOOD7", "Vented GRB Hood")
AddTextEntry("VIVANT_TBUMF0A", "Accent Bumper Trim")
AddTextEntry("VIVANT_TBUMF0B", "Painted Bumper Trim")
AddTextEntry("VIVANT_TBUMF0C", "Painted Accent Bumper Trim")
AddTextEntry("VIVANT_TBUMF1", "Vented Bumper Trim")
AddTextEntry("VIVANT_TBUMF1A", "Painted Sport Bumper Trim")
AddTextEntry("VIVANT_TBUMF1B", "Accent Sport Bumper Trim")
AddTextEntry("VIVANT_TBUMF1C", "Painted Accent Sport Bumper Trim")
AddTextEntry("VIVANT_TBUMF2", "Race Bumper Trim")
AddTextEntry("VIVANT_TBUMF2A", "Painted Race Bumper Trim")
AddTextEntry("VIVANT_TBUMF2B", "Accent Race Bumper Trim")
AddTextEntry("VIVANT_TBUMF2C", "Painted Accent Race Bumper Trim")
AddTextEntry("VIVANT_TBUMF3", "Smooth Bumper Trim")
AddTextEntry("VIVANT_TBUMF3A", "Painted Smooth Bumper Trim")
AddTextEntry("VIVANT_WIDE0", "Remove Side Trim")
AddTextEntry("VIVANT_WIDE1", "Sport Widebody Kit")
AddTextEntry("VIVANT_WIDE1A", "Painted Sport Widebody Kit")
AddTextEntry("VIVANT_WIDE2", "GTS Widebody Kit")
AddTextEntry("VIVANT_WIDE2A", "Accent GTS Widebody Kit")
AddTextEntry("VIVANT_WIDE3", "Painted Widebody Kit")
AddTextEntry("VIVANT_WIDE3A", "Painted Accent Widebody Kit")
AddTextEntry("VIVANT_WIDE4", "Rally Widebody Kit")
AddTextEntry("VIVANT_WIDE4A", "Painted Rally Widebody Kit")
AddTextEntry("VIVANT_WIDE4B", "Rally Widebody Kit w/ Trim")
AddTextEntry("VIVANT_WIDE4C", "Painted Rally Widebody Kit w/ Trim")
AddTextEntry("VIVANT_ROOF1", "Sunroof")
AddTextEntry("VIVANT_ROOF2", "Covered Sunroof")
AddTextEntry("VIVANT_ROOF3", "XXL Sunroof")
AddTextEntry("VIVANT_HOODB", "Hood Bra")
AddTextEntry("VIVANT_RPANEL1", "Painted Trunk Panel")
AddTextEntry("VIVANT_RPANEL2", "Vented Trunk Panel")
AddTextEntry("VIVANT_RPANEL3", "Smooth Trunk Panel")
AddTextEntry("VIVANT_RPANEL4", "Carbon Smooth Trunk Panel")
AddTextEntry("VIVANT_LCOVER1", "Covered Indicators")
AddTextEntry("VIVANT_LCOVER2", "Covered Headlights & Indicators")
AddTextEntry("VIVANT_BADGE1", "Painted Pillar Badge")
AddTextEntry("VIVANT_BADGE2", "GTS Pillar Badge")
AddTextEntry("VIVANT_BADGE3", "Painted GTS Pillar Badge")
AddTextEntry("VIVANT_BADGE4", "Debadge Smooth Pillar")
AddTextEntry("VIVANT_BADGE5", "Side Vents")
AddTextEntry("VIVANT_TBUMR0A", "Accent Bumper Trim")
AddTextEntry("VIVANT_TBUMR1", "Painted Bumper Trim")
AddTextEntry("VIVANT_TBUMR1A", "Painted Accent Bumper Trim")
AddTextEntry("VIVANT_TBUMR2", "Smooth Bumper Trim")
AddTextEntry("VIVANT_TBUMR2A", "Painted Smooth Bumper Trim")
AddTextEntry("VIVANT_FOGS1", "Dual Fog Lights")
AddTextEntry("VIVANT_FOGS1A", "Painted Dual Fog Lights")
AddTextEntry("VIVANT_FOGS1B", "Covered Dual Fog Lights")
AddTextEntry("VIVANT_FOGS1C", "Painted Covered Dual Fog Lights")
AddTextEntry("VIVANT_FOGS2", "Rally Fogs Lights")
AddTextEntry("VIVANT_FOGS2A", "Painted Rally Fog Lights")
AddTextEntry("VIVANT_FOGS2B", "Covered Rally Fog Lights")
AddTextEntry("VIVANT_FOGS2C", "Painted Covered Rally Fog Lights")
AddTextEntry("VIVANT_FOGS3", "Safari Fogs Lights")
AddTextEntry("VIVANT_FOGS3A", "Painted Safari Fog Lights")
AddTextEntry("VIVANT_FOGS3B", "Covered Safari Fog Lights")
AddTextEntry("VIVANT_FOGS3C", "Painted Covered Safari Fog Lights")
AddTextEntry("VIVANT_MIR", "Painted Mirrors")
AddTextEntry("TOP_VVTOPF", "Front Bumper Trim")
AddTextEntry("TOP_VVTOPR", "Rear Bumper Trim")
AddTextEntry("TOP_VVWIDE", "Widebody Kit")
AddTextEntry("TOP_VVBADGE", "Pillar Badge")
AddTextEntry("TOP_VVHOOD", "Hood Accessories")

AddTextEntry("VIVANT_LIV1", "Kabel #8")
AddTextEntry("VIVANT_LIV2", "80s Stripes Black")
AddTextEntry("VIVANT_LIV3", "80s Stripes White")
AddTextEntry("VIVANT_LIV4", "Atomic #26")
AddTextEntry("VIVANT_LIV5", "Équipe")
AddTextEntry("VIVANT_LIV6", "Équipe Sport")
AddTextEntry("VIVANT_LIV7", "Bordeaux Rallye")
AddTextEntry("VIVANT_LIV8", "Team Atomic-CoK #104")
AddTextEntry("VIVANT_LIV9", "Premier Racing")
AddTextEntry("VIVANT_LIV10", "Pizza This")
end)