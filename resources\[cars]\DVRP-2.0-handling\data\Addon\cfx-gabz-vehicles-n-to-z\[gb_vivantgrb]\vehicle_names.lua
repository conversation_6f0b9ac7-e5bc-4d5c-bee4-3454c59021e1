Citizen.CreateThread(function()
AddTextEntry("VIVGRB", "Vivant GrB")
AddTextEntry("BORDEAUX", "Bordeaux")

AddTextEntry("VIVANTGRB_WIPERS_1", "Remove Rear Wipers")

AddTextEntry("VIVANTGRB_CANARDS_1", "Front Canards")
AddTextEntry("VIVANTGRB_CANARDS_2", "Hillclimb Splitter and Canards")
AddTextEntry("VIVANTGRB_CANARDS_3", "Front Guard")
AddTextEntry("VIVANTGRB_CANARDS_4", "Plastic Front Canards")
AddTextEntry("VIVANTGRB_CANARDS_5", "Plastic Hillclimb Splitter and Canards")
AddTextEntry("VIVANTGRB_CANARDS_6", "Plastic Front Guard")

AddTextEntry("VIVANTGRB_FOGS_1", "Quad Foglights")
AddTextEntry("VIVANTGRB_FOGS_2", "<PERSON><PERSON>er and <PERSON> Foglights")
AddTextEntry("VIVANTGRB_FOGS_3", "Dual Center Foglights")
AddTextEntry("VIVANTGRB_FOGS_4", "Covered Quad Foglights")
AddTextEntry("VIVANTGRB_FOGS_5", "Covered Dual Foglights")
AddTextEntry("VIVANTGRB_FOGS_6", "Raid Foglights")

AddTextEntry("VIVANTGRB_FBUMPER_1", "Delete Front Plate")
AddTextEntry("VIVANTGRB_FBUMPER_2", "Cut Front Bumper")
AddTextEntry("VIVANTGRB_FBUMPER_3", "Cut Front Bumper without Plate")
AddTextEntry("VIVANTGRB_FBUMPER_4", "Racing Front Bumper")
AddTextEntry("VIVANTGRB_FBUMPER_5", "Racing Front Bumper without Plate")
AddTextEntry("VIVANTGRB_FBUMPER_7", "Rally Front Bumper with Small Plate")
AddTextEntry("VIVANTGRB_FBUMPER_8", "Rally Front Bumper")
AddTextEntry("VIVANTGRB_FBUMPER_9", "Rally Front Bumper without Plate")

AddTextEntry("VIVANTGRB_RBUMPER_1", "Cut Rear Bumper")

AddTextEntry("VIVANTGRB_ROOF_1", "Delete Antenna")
AddTextEntry("VIVANTGRB_ROOF_2", "Spare Wheel with Rally Antenna")
AddTextEntry("VIVANTGRB_ROOF_3", "Spare Wheel and Roof Intake")
AddTextEntry("VIVANTGRB_ROOF_4", "Rally Antenna")
AddTextEntry("VIVANTGRB_ROOF_5", "Rally Antenna and Roof Intake")
AddTextEntry("VIVANTGRB_ROOF_6", "Vent Up and Stock Antenna")
AddTextEntry("VIVANTGRB_ROOF_7", "Vent Up")
AddTextEntry("VIVANTGRB_ROOF_8", "Vent Up and Rally Antenna")
AddTextEntry("VIVANTGRB_ROOF_9", "Vent Up and Roof Intake with Stock Antenna")
AddTextEntry("VIVANTGRB_ROOF_10", "Vent Up and Roof Intake")
AddTextEntry("VIVANTGRB_ROOF_11", "Vent Up and Roof Intake with Rally Antenna")
AddTextEntry("VIVANTGRB_ROOF_12", "Vent Down and Stock Antenna")
AddTextEntry("VIVANTGRB_ROOF_13", "Vent Down")
AddTextEntry("VIVANTGRB_ROOF_14", "Vent Down and Rally Antenna")
AddTextEntry("VIVANTGRB_ROOF_15", "Vent Down and Roof Intake with Stock Antenna")
AddTextEntry("VIVANTGRB_ROOF_16", "Vent Down and Roof Intake")
AddTextEntry("VIVANTGRB_ROOF_17", "Vent Down and Roof Intake with Rally Antenna")

AddTextEntry("VIVANTGRB_SUNSTRIP_1", "Sunstrip")

AddTextEntry("VIVANTGRB_COVERS_1", "Headlight Cover")
AddTextEntry("VIVANTGRB_COVERS_2", "Circular Cover")

AddTextEntry("VIVANTGRB_SPOILER_1", "Plastic Spoiler")
AddTextEntry("VIVANTGRB_SPOILER_2", "Rally Spoiler")
AddTextEntry("VIVANTGRB_SPOILER_3", "Rally Spoiler with Rallycross Wing")
AddTextEntry("VIVANTGRB_SPOILER_4", "Rally Spoiler with Hillclimb Spoiler")
AddTextEntry("VIVANTGRB_SPOILER_5", "Extreme Spec")
AddTextEntry("VIVANTGRB_SPOILER_6", "Plastic Rally Spoiler")
AddTextEntry("VIVANTGRB_SPOILER_7", "Plastic Rally Spoiler with Rallycross Wing")
AddTextEntry("VIVANTGRB_SPOILER_8", "Plastic Rally Spoiler with Hillclimb Spoiler")
AddTextEntry("VIVANTGRB_SPOILER_9", "Plastic Extreme Spec")

AddTextEntry("VIVANTGRB_HATCH_1", "Exposed Engine")
AddTextEntry("VIVANTGRB_HATCH_2", "Rear Hatch with Vent")
AddTextEntry("VIVANTGRB_HATCH_3", "Vent and Exposed Engine")

AddTextEntry("VIVANTGRB_SKIRT_1", "Rally Skirts")
AddTextEntry("VIVANTGRB_SKIRT_2", "Mudflaps")
AddTextEntry("VIVANTGRB_SKIRT_3", "Rally Skirts and Mudflaps")

AddTextEntry("VIVANTGRB_GRILLE_1", "Alternative Badge")
AddTextEntry("VIVANTGRB_GRILLE_2", "Delete Grille with Alternative Badge")
AddTextEntry("VIVANTGRB_GRILLE_3", "Badge Only")
AddTextEntry("VIVANTGRB_GRILLE_4", "Full Delete")

AddTextEntry("VIVANTGRB_ROLLCAGE_1", "Rollcage")

AddTextEntry("VIVANTGRB_EXHAUST_1", "Racing Exhaust")
AddTextEntry("VIVANTGRB_EXHAUST_2", "Racing Exhaust and Diffuser")

AddTextEntry("VIVANTGRB_LIV1", "Bordeaux Rallye #44")
AddTextEntry("VIVANTGRB_LIV2", "Redwood Rally #3")
AddTextEntry("VIVANTGRB_LIV3", "CoK Atomic Team #142")

AddTextEntry("VIVANTGRB_HOOD_1", "Hood Deflector")

AddTextEntry("VIVANTGRB_SEATS_1", "Painted Sports Seats")
AddTextEntry("VIVANTGRB_SEATS_2", "Ballistic Fiber Tuner Seats")
AddTextEntry("VIVANTGRB_SEATS_3", "Painted Track Seats")
AddTextEntry("VIVANTGRB_SEATS_4", "Carbon Sports Seats")
AddTextEntry("VIVANTGRB_SEATS_5", "Painted Bucket Seats")
AddTextEntry("VIVANTGRB_SEATS_6", "Carbon Track Seats")
AddTextEntry("VIVANTGRB_SEATS_7", "Ballistic Fiber Sports Seats")
AddTextEntry("VIVANTGRB_SEATS_8", "Carbon Bucket Seats")
AddTextEntry("VIVANTGRB_SEATS_9", "Ballistic Fiber Track Seats")
AddTextEntry("VIVANTGRB_SEATS_10", "Painted Tuner Seats")
AddTextEntry("VIVANTGRB_SEATS_11", "Ballistic Fiber Bucket Seats")
AddTextEntry("VIVANTGRB_SEATS_12", "Carbon Seats")
AddTextEntry("VIVANTGRB_SEATS_13", "Carbon Tuner Seats")

end)