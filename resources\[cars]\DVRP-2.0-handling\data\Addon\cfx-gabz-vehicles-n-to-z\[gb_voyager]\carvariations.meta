<?xml version="1.0" encoding="UTF-8"?>

<CVehicleModelInfoVariation>
  <variationData>
    <Item>
      <modelName>gbvoyager</modelName>
      <colors>
        <Item>
          <indices content="char_array">
            122
            59
            0
            156
            0
            0
          </indices>
          <liveries>
            <Item value="false" />
          </liveries>
        </Item>
        <Item>
          <indices content="char_array">
            122
            48
            0
            156
            0
            0
          </indices>
          <liveries>
            <Item value="false" />
          </liveries>
        </Item>
        <Item>
          <indices content="char_array">
            122
            121
            0
            156
            0
            0
          </indices>
          <liveries>
            <Item value="false" />
          </liveries>
        </Item>
        <Item>
          <indices content="char_array">
            57
            57
            0
            156
            0
            0
          </indices>
          <liveries>
            <Item value="false" />
          </liveries>
        </Item>
        <Item>
          <indices content="char_array">
            38
            38
            0
            156
            0
            0
          </indices>
          <liveries>
            <Item value="false" />
          </liveries>
        </Item>
        <Item>
          <indices content="char_array">
            99
            154
            0
            156
            0
            0
          </indices>
          <liveries>
            <Item value="false" />
          </liveries>
        </Item>
        <Item>
          <indices content="char_array">
            122
            40
            0
            156
            0
            0
          </indices>
          <liveries>
            <Item value="false" />
          </liveries>
        </Item>
      </colors>
      <kits>
        <Item>8111_gbvoyager_modkit</Item>
      </kits>
      <windowsWithExposedEdges/>
      <plateProbabilities>
        <Probabilities>
          <Item>
            <Name>Standard White</Name>
            <Value value="70"/>
          </Item>
          <Item>
            <Name>White Plate 2</Name>
            <Value value="30"/>
          </Item>
        </Probabilities>
      </plateProbabilities>
      <lightSettings value="14"/>
      <sirenSettings value="0"/>
    </Item>
    <Item>
      <modelName>gbvoyager2</modelName>
      <colors>
        <Item>
          <indices content="char_array">
            111
            111
            0
            156
            0
            0
          </indices>
          <liveries>
            <Item value="false" />
          </liveries>
        </Item>
      </colors>
      <kits>
        <Item>8111_gbvoyager_modkit</Item>
      </kits>
      <windowsWithExposedEdges/>
      <plateProbabilities>
        <Probabilities>
          <Item>
            <Name>Standard White</Name>
            <Value value="70"/>
          </Item>
          <Item>
            <Name>White Plate 2</Name>
            <Value value="30"/>
          </Item>
        </Probabilities>
      </plateProbabilities>
      <lightSettings value="14"/>
      <sirenSettings value="0"/>
    </Item>
    <Item>
      <modelName>gbvoyagerb</modelName>
      <colors>
        <Item>
          <indices content="char_array">
            111
            111
            0
            156
            0
            0
          </indices>
          <liveries>
            <Item value="false" />
          </liveries>
        </Item>
        <Item>
          <indices content="char_array">
            31
            111
            0
            156
            0
            0
          </indices>
          <liveries>
            <Item value="false" />
          </liveries>
        </Item>
        <Item>
          <indices content="char_array">
            31
            111
            0
            156
            0
            0
          </indices>
          <liveries>
            <Item value="false" />
          </liveries>
        </Item>
        <Item>
          <indices content="char_array">
            88
            88
            0
            156
            0
            0
          </indices>
          <liveries>
            <Item value="false" />
          </liveries>
        </Item>
        <Item>
          <indices content="char_array">
            69
            111
            0
            156
            0
            0
          </indices>
          <liveries>
            <Item value="false" />
          </liveries>
        </Item>
        <Item>
          <indices content="char_array">
            31
            111
            0
            156
            0
            0
          </indices>
          <liveries>
            <Item value="false" />
          </liveries>
        </Item>
        <Item>
          <indices content="char_array">
            36
            111
            0
            156
            0
            0
          </indices>
          <liveries>
            <Item value="false" />
          </liveries>
        </Item>
      </colors>
      <kits>
        <Item>8112_gbvoyagerb_modkit</Item>
      </kits>
      <windowsWithExposedEdges/>
      <plateProbabilities>
        <Probabilities>
          <Item>
            <Name>Standard White</Name>
            <Value value="70"/>
          </Item>
          <Item>
            <Name>White Plate 2</Name>
            <Value value="30"/>
          </Item>
        </Probabilities>
      </plateProbabilities>
      <lightSettings value="14"/>
      <sirenSettings value="0"/>
    </Item>
    <Item>
      <modelName>gbvoyagerb2</modelName>
      <colors>
        <Item>
          <indices content="char_array">
            111
            111
            0
            156
            0
            0
          </indices>
          <liveries>
            <Item value="false" />
          </liveries>
        </Item>
        <Item>
          <indices content="char_array">
            31
            111
            0
            156
            0
            0
          </indices>
          <liveries>
            <Item value="false" />
          </liveries>
        </Item>
        <Item>
          <indices content="char_array">
            31
            111
            0
            156
            0
            0
          </indices>
          <liveries>
            <Item value="false" />
          </liveries>
        </Item>
        <Item>
          <indices content="char_array">
            88
            88
            0
            156
            0
            0
          </indices>
          <liveries>
            <Item value="false" />
          </liveries>
        </Item>
        <Item>
          <indices content="char_array">
            69
            111
            0
            156
            0
            0
          </indices>
          <liveries>
            <Item value="false" />
          </liveries>
        </Item>
        <Item>
          <indices content="char_array">
            31
            111
            0
            156
            0
            0
          </indices>
          <liveries>
            <Item value="false" />
          </liveries>
        </Item>
        <Item>
          <indices content="char_array">
            36
            111
            0
            156
            0
            0
          </indices>
          <liveries>
            <Item value="false" />
          </liveries>
        </Item>
      </colors>
      <kits>
        <Item>8113_gbvoyagerb2_modkit</Item>
      </kits>
      <windowsWithExposedEdges/>
      <plateProbabilities>
        <Probabilities>
          <Item>
            <Name>Standard White</Name>
            <Value value="70"/>
          </Item>
          <Item>
            <Name>White Plate 2</Name>
            <Value value="30"/>
          </Item>
        </Probabilities>
      </plateProbabilities>
      <lightSettings value="14"/>
      <sirenSettings value="0"/>
    </Item>
    <Item>
      <modelName>gbvoyagerg</modelName>
      <colors>
        <Item>
          <indices content="char_array">
            122
            59
            0
            156
            0
            0
          </indices>
          <liveries>
            <Item value="false" />
          </liveries>
        </Item>
        <Item>
          <indices content="char_array">
            122
            48
            0
            156
            0
            0
          </indices>
          <liveries>
            <Item value="false" />
          </liveries>
        </Item>
        <Item>
          <indices content="char_array">
            122
            121
            0
            156
            0
            0
          </indices>
          <liveries>
            <Item value="false" />
          </liveries>
        </Item>
        <Item>
          <indices content="char_array">
            57
            57
            0
            156
            0
            0
          </indices>
          <liveries>
            <Item value="false" />
          </liveries>
        </Item>
        <Item>
          <indices content="char_array">
            38
            38
            0
            156
            0
            0
          </indices>
          <liveries>
            <Item value="false" />
          </liveries>
        </Item>
        <Item>
          <indices content="char_array">
            99
            154
            0
            156
            0
            0
          </indices>
          <liveries>
            <Item value="false" />
          </liveries>
        </Item>
        <Item>
          <indices content="char_array">
            122
            40
            0
            156
            0
            0
          </indices>
          <liveries>
            <Item value="false" />
          </liveries>
        </Item>
      </colors>
      <kits>
        <Item>8114_gbvoyagerg_modkit</Item>
      </kits>
      <windowsWithExposedEdges/>
      <plateProbabilities>
        <Probabilities>
          <Item>
            <Name>Standard White</Name>
            <Value value="70"/>
          </Item>
          <Item>
            <Name>White Plate 2</Name>
            <Value value="30"/>
          </Item>
        </Probabilities>
      </plateProbabilities>
      <lightSettings value="14"/>
      <sirenSettings value="0"/>
    </Item>
    <Item>
      <modelName>gbvoyagerh</modelName>
      <colors>
        <Item>
          <indices content="char_array">
            122
            59
            0
            156
            0
            0
          </indices>
          <liveries>
            <Item value="false" />
          </liveries>
        </Item>
        <Item>
          <indices content="char_array">
            122
            48
            0
            156
            0
            0
          </indices>
          <liveries>
            <Item value="false" />
          </liveries>
        </Item>
        <Item>
          <indices content="char_array">
            122
            121
            0
            156
            0
            0
          </indices>
          <liveries>
            <Item value="false" />
          </liveries>
        </Item>
        <Item>
          <indices content="char_array">
            57
            57
            0
            156
            0
            0
          </indices>
          <liveries>
            <Item value="false" />
          </liveries>
        </Item>
        <Item>
          <indices content="char_array">
            38
            38
            0
            156
            0
            0
          </indices>
          <liveries>
            <Item value="false" />
          </liveries>
        </Item>
        <Item>
          <indices content="char_array">
            99
            154
            0
            156
            0
            0
          </indices>
          <liveries>
            <Item value="false" />
          </liveries>
        </Item>
        <Item>
          <indices content="char_array">
            122
            40
            0
            156
            0
            0
          </indices>
          <liveries>
            <Item value="false" />
          </liveries>
        </Item>
      </colors>
      <kits>
        <Item>8117_gbvoyagerh_modkit</Item>
      </kits>
      <windowsWithExposedEdges/>
      <plateProbabilities>
        <Probabilities>
          <Item>
            <Name>Standard White</Name>
            <Value value="70"/>
          </Item>
          <Item>
            <Name>White Plate 2</Name>
            <Value value="30"/>
          </Item>
        </Probabilities>
      </plateProbabilities>
      <lightSettings value="14"/>
      <sirenSettings value="0"/>
    </Item>
  </variationData>
</CVehicleModelInfoVariation>