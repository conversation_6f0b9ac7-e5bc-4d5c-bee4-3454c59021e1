Citizen.CreateThread(function()
AddTextEntry("<PERSON><PERSON><PERSON><PERSON><PERSON>", "Voyager 6x4-T")
AddTextEntry("GBVOYAGER2", "Voyager 4x2-T")
AddTextEntry("<PERSON><PERSON>Y<PERSON>ER<PERSON>", "Voyager 6x4-B")
AddTextEntry("GBVOYAGERB2", "Voyager 4x2-B")
AddTextEntry("GBVOYAGERH", "Voyager 6x2-H")
AddTextEntry("GBVOYAGERG", "Voyager Trash Truck")
AddTextEntry("VOYAGER_ROOF1", "Aero Cab Extension")
AddTextEntry("VOYAGER_PROT", "Cab Protector")
AddTextEntry("VOYAGER_BUMF0A", "Chrome Front Bumper")
AddTextEntry("VOYAGER_BUMF0B", "Painted Front Bumper")
AddTextEntry("VOYAGER_BUMF1", "Heavy Front Bumper")
AddTextEntry("VOYAGER_BUMF1A", "Chrome Heavy Front Bumper")
AddTextEntry("VOYAGER_SKIRT0A", "Painted Mud Guard")
AddTextEntry("VOYAGER_SKIRT1", "Accessory Config 1")
AddTextEntry("VOYAGER_SKIRT1A", "Accessory Config 1 (Painted)")
AddTextEntry("VOYAGER_SKIRT2", "Accessory Config 2")
AddTextEntry("VOYAGER_SKIRT2A", "Accessory Config 2 (Painted)")
AddTextEntry("VOYAGER_SKIRT3", "Accessory Config 3")
AddTextEntry("VOYAGER_SKIRT3A", "Accessory Config 3 (Painted)")
AddTextEntry("VOYAGER_EXH1", "Straight Exhaust")
AddTextEntry("VOYAGER_GRILL0", "Plastic Slats")
AddTextEntry("VOYAGER_GRILL0A", "Chrome Slats")
AddTextEntry("VOYAGER_GRILL1", "Honeycomb Slats")
AddTextEntry("VOYAGER_GRILL1A", "Chrome Honeycomb Slats")
AddTextEntry("VOYAGER_GRILL2", "Premium Slats")
AddTextEntry("VOYAGER_GRILL2A", "Plastic-Chrome Premium Slats")
AddTextEntry("VOYAGER_GRILL2B", "Chrome-Plastic Premium Slats")
AddTextEntry("VOYAGER_GRILL2C", "Chrome Premium Slats")
AddTextEntry("VOYAGER_GFRAME0A", "Chrome Grille Frame")
AddTextEntry("VOYAGER_HLIGHT1", "Chrome Headlight Housing")
AddTextEntry("VOYAGER_HMIR1", "Plastic Hood Mirrors")
AddTextEntry("VOYAGER_HMIR2", "Chrome-Plastic Hood Mirrors")
AddTextEntry("VOYAGER_HMIR3", "Plastic-Chrome Hood Mirrors")
AddTextEntry("VOYAGER_VISOR1", "Plastic Visor")
AddTextEntry("VOYAGER_VISOR1A", "Chrome Visor")
AddTextEntry("VOYAGER_VISOR1B", "Painted Visor")
AddTextEntry("VOYAGER_VISOR1C", "Transparent Visor")
AddTextEntry("VOYAGER_MIR1", "Plastic-Chrome Mirrors")
AddTextEntry("VOYAGER_MIR2", "Painted-Chrome Mirrors")
AddTextEntry("VOYAGER_MIR3", "Painted-Plastic Mirrors")
AddTextEntry("VOYAGER_MIR4", "Chrome Mirrors")

AddTextEntry("VOYAGER_LIV1", "Retro Two-tone (Light)")
AddTextEntry("VOYAGER_LIV2", "Retro Two-tone (Dark)")
AddTextEntry("VOYAGER_LIV3", "White Swoosh")
AddTextEntry("VOYAGER_LIV4", "Purple Swoosh")
AddTextEntry("VOYAGER_LIV5", "Transparent Swoosh")
AddTextEntry("VOYAGER_LIV6", "Two-tone Top (White)")
AddTextEntry("VOYAGER_LIV7", "Two-tone Top (Black)")
AddTextEntry("VOYAGER_LIV8", "Two-tone Top (Cream)")
AddTextEntry("VOYAGER_LIV9", "Two-tone Bottom (White)")
AddTextEntry("VOYAGER_LIV10", "Two-tone Bottom (Black)")
AddTextEntry("VOYAGER_LIV11", "Two-tone Bottom (Cream)")
AddTextEntry("VOYAGER_LIV12", "White Stripes")
AddTextEntry("VOYAGER_LIV13", "Black Stripes")
AddTextEntry("VOYAGER_LIV14", "Red Stripes")
AddTextEntry("VOYAGER_LIV15", "Green Stripes")
AddTextEntry("VOYAGER_LIV16", "Blue Stripes")
AddTextEntry("VOYAGER_LIV17", "JiffiRent")
AddTextEntry("VOYAGER_LIV18", "You Tool")
AddTextEntry("VOYAGER_LIV19", "PostOP")
AddTextEntry("VOYAGER_LIV20", "GoPostal")
AddTextEntry("VOYAGER_LIV21", "Escalera")

AddTextEntry("VOYAGERB_LIV1", "Raven Slaughterhouse")
AddTextEntry("VOYAGERB_LIV2", "JiffiRent")
AddTextEntry("VOYAGERB_LIV3", "GoPostal (Classic)")
AddTextEntry("VOYAGERB_LIV4", "GoPostal (Modern)")
AddTextEntry("VOYAGERB_LIV5", "Grain of Truth")
AddTextEntry("VOYAGERB_LIV6", "O'Deas Pharmacy")
AddTextEntry("VOYAGERB_LIV7", "National Transfer & Storage")
AddTextEntry("VOYAGERB_LIV8", "Big-G-Goods")
AddTextEntry("VOYAGERB_LIV9", "Robs Liquor")

AddTextEntry("VOYAGERB2_LIV1", "Pearls Seafood")
AddTextEntry("VOYAGERB2_LIV2", "Touchdown Rentals")
AddTextEntry("VOYAGERB2_LIV3", "Post OP (Classic)")
AddTextEntry("VOYAGERB2_LIV4", "Post OP (Modern)")
AddTextEntry("VOYAGERB2_LIV5", "247 Supermarket")
AddTextEntry("VOYAGERB2_LIV6", "DigitalDen")
AddTextEntry("VOYAGERB2_LIV7", "Himalaya")
AddTextEntry("VOYAGERB2_LIV8", "Big-G-Goods")
AddTextEntry("VOYAGERB2_LIV9", "Robs Liquor")

AddTextEntry("VOYAGERG_LIV1", "Rusty Compactor")
AddTextEntry("VOYAGERG_LIV2", "Butt Lovers")
AddTextEntry("VOYAGERG_LIV3", "Butt Lovers Rusty")
AddTextEntry("VOYAGERG_LIV4", "HOBO")
AddTextEntry("VOYAGERG_LIV5", "HOBO Rusty")
AddTextEntry("VOYAGERG_LIV6", "Waste Transfer Services")
AddTextEntry("VOYAGERG_LIV7", "Waste Transfer Services Rusty")


AddTextEntry("VOYAGERH_LIV22", "Casey's Highway Clearance")
AddTextEntry("VOYAGERH_LIV23", "Hayes Auto")
AddTextEntry("VOYAGERH_LIV24", "Benny's Original Motorworks")
end)