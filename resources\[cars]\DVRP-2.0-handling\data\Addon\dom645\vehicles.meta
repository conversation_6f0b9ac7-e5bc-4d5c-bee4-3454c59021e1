<?xml version="1.0" encoding="UTF-8"?>
<CVehicleModelInfo__InitDataList>
  <residentTxd>vehshare</residentTxd>
  <residentAnims />
  <InitDatas>
	<Item>
      <modelName>dom645</modelName>
      <txdName>dom645</txdName>
      <handlingId>DOM645</handlingId>
      <gameName>DOM645</gameName>
      <vehicleMakeName>VAPID</vehicleMakeName>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected />
      <ptfxAssetName>null</ptfxAssetName>
      <audioNameHash>dominator7</audioNameHash>
      <layout>LAYOUT_LOW</layout>
      <coverBoundOffsets>ELLIE_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout />
      <cameraName>FOLLOW_CHEETAH_CAMERA</cameraName>
      <aimCameraName>MID_BOX_VEHICLE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>VEHICLE_BONNET_CAMERA_STANDARD_LONG</bonnetCameraName>
      <povCameraName>DEFAULT_POV_CAMERA</povCameraName>
      <FirstPersonDriveByIKOffset x="0.000000" y="-0.040000" z="-0.040000" />
      <FirstPersonDriveByUnarmedIKOffset x="-0.010000" y="-0.070000" z="-0.010000" />
	  <FirstPersonProjectileDriveByIKOffset x="0.000000" y="-0.100000" z="-0.050000" />
	  <FirstPersonProjectileDriveByPassengerIKOffset x="0.020000" y="-0.100000" z="-0.050000" />
	  <FirstPersonDriveByRightPassengerIKOffset x="-0.020000" y="-0.020000" z="-0.040000" />
	  <FirstPersonDriveByRightPassengerUnarmedIKOffset x="-0.025000" y="-0.050000" z="0.000000" />
	  <FirstPersonMobilePhoneOffset x="0.120000" y="0.090000" z="0.575000" />
      <FirstPersonPassengerMobilePhoneOffset x="0.225000" y="0.050000" z="0.460000" />
      <PovCameraOffset x="0.000000" y="-0.310000" z="0.655000" />
      <PovCameraVerticalAdjustmentForRollCage value="0.000000" />
      <PovPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000" />
      <PovRearPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000" />
      <vfxInfoName>VFXVEHICLEINFO_CAR_GENERIC</vfxInfoName>
      <shouldUseCinematicViewMode value="true" />
      <shouldCameraTransitionOnClimbUpDown value="false" />
      <shouldCameraIgnoreExiting value="false" />
      <AllowPretendOccupants value="true" />
      <AllowJoyriding value="true" />
      <AllowSundayDriving value="true" />
      <AllowBodyColorMapping value="true" />
      <wheelScale value="0.200000" />
      <wheelScaleRear value="0.200000" />
      <dirtLevelMin value="0.000000" />
      <dirtLevelMax value="0.450000" />
      <envEffScaleMin value="0.000000" />
      <envEffScaleMax value="1.000000" />
      <envEffScaleMin2 value="0.000000" />
      <envEffScaleMax2 value="1.000000" />
      <damageMapScale value="0.250000" />
      <damageOffsetScale value="0.250000" />
      <diffuseTint value="0x00FFFFFF" />
      <steerWheelMult value="1.00000" />
      <HDTextureDist value="5.000000" />
      <lodDistances content="float_array">
        10.000000
        25.000000
        60.000000
        120.000000
        500.000000
        500.000000
      </lodDistances>
      <minSeatHeight value="0.844" />
      <identicalModelSpawnDistance value="20" />
      <maxNumOfSameColor value="1" />
      <defaultBodyHealth value="1000.000000" />
      <pretendOccupantsScale value="1.000000" />
      <visibleSpawnDistScale value="1.000000" />
      <trackerPathWidth value="2.000000" />
      <weaponForceMult value="1.000000" />
      <frequency value="30" />
      <swankness>SWANKNESS_4</swankness>
      <maxNum value="10" />
      <flags>FLAG_EXTRAS_REQUIRE FLAG_EXTRAS_CONVERTIBLE FLAG_EXTRAS_STRONG FLAG_AVERAGE_CAR</flags>
      <type>VEHICLE_TYPE_CAR</type>
      <plateType>VPT_BACK_PLATES</plateType>
	  <dashboardType>VDT_DUKES</dashboardType>
      <vehicleClass>VC_SPORT_CLASSIC</vehicleClass>
      <wheelType>VWT_MUSCLE</wheelType>
      <trailers />
      <additionalTrailers />
      <drivers />
      <extraIncludes />
      <doorsWithCollisionWhenClosed />
      <driveableDoors />
      <bumpersNeedToCollideWithMap value="false" />
      <needsRopeTexture value="false" />
      <requiredExtras />
      <rewards />
      <cinematicPartCamera>
        <Item>WHEEL_FRONT_RIGHT_CAMERA</Item>
        <Item>WHEEL_FRONT_LEFT_CAMERA</Item>
        <Item>WHEEL_REAR_RIGHT_CAMERA</Item>
        <Item>WHEEL_REAR_LEFT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet />
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000" />
      <buoyancySphereSizeScale value="1.000000" />
      <pOverrideRagdollThreshold type="NULL" />
	  <firstPersonDrivebyData>
        <Item>LOW_ELLIE_FRONT_LEFT</Item>
        <Item>LOW_ELLIE_FRONT_RIGHT</Item>
      </firstPersonDrivebyData>
    </Item>
	<Item>
      <modelName>dom645t</modelName>
      <txdName>dom645</txdName>
      <handlingId>DOM645T</handlingId>
      <gameName>DOM645T</gameName>
      <vehicleMakeName>VAPID</vehicleMakeName>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected />
      <ptfxAssetName>null</ptfxAssetName>
      <audioNameHash>dominator7</audioNameHash>
      <layout>LAYOUT_LOW</layout>
      <coverBoundOffsets>ELLIE_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout />
      <cameraName>FOLLOW_CHEETAH_CAMERA</cameraName>
      <aimCameraName>MID_BOX_VEHICLE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>VEHICLE_BONNET_CAMERA_STANDARD_LONG</bonnetCameraName>
      <povCameraName>DEFAULT_POV_CAMERA</povCameraName>
      <FirstPersonDriveByIKOffset x="0.000000" y="-0.040000" z="-0.040000" />
      <FirstPersonDriveByUnarmedIKOffset x="-0.010000" y="-0.070000" z="-0.010000" />
	  <FirstPersonProjectileDriveByIKOffset x="0.000000" y="-0.100000" z="-0.050000" />
	  <FirstPersonProjectileDriveByPassengerIKOffset x="0.020000" y="-0.100000" z="-0.050000" />
	  <FirstPersonDriveByRightPassengerIKOffset x="-0.020000" y="-0.020000" z="-0.040000" />
	  <FirstPersonDriveByRightPassengerUnarmedIKOffset x="-0.025000" y="-0.050000" z="0.000000" />
	  <FirstPersonMobilePhoneOffset x="0.120000" y="0.090000" z="0.575000" />
      <FirstPersonPassengerMobilePhoneOffset x="0.225000" y="0.050000" z="0.460000" />
      <PovCameraOffset x="0.000000" y="-0.310000" z="0.655000" />
      <PovCameraVerticalAdjustmentForRollCage value="0.000000" />
      <PovPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000" />
      <PovRearPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000" />
      <vfxInfoName>VFXVEHICLEINFO_CAR_GENERIC</vfxInfoName>
      <shouldUseCinematicViewMode value="true" />
      <shouldCameraTransitionOnClimbUpDown value="false" />
      <shouldCameraIgnoreExiting value="false" />
      <AllowPretendOccupants value="true" />
      <AllowJoyriding value="true" />
      <AllowSundayDriving value="true" />
      <AllowBodyColorMapping value="true" />
      <wheelScale value="0.200000" />
      <wheelScaleRear value="0.200000" />
      <dirtLevelMin value="0.000000" />
      <dirtLevelMax value="0.450000" />
      <envEffScaleMin value="0.000000" />
      <envEffScaleMax value="1.000000" />
      <envEffScaleMin2 value="0.000000" />
      <envEffScaleMax2 value="1.000000" />
      <damageMapScale value="0.250000" />
      <damageOffsetScale value="0.250000" />
      <diffuseTint value="0x00FFFFFF" />
      <steerWheelMult value="1.00000" />
      <HDTextureDist value="5.000000" />
      <lodDistances content="float_array">
        10.000000
        25.000000
        60.000000
        120.000000
        500.000000
        500.000000
      </lodDistances>
      <minSeatHeight value="0.844" />
      <identicalModelSpawnDistance value="20" />
      <maxNumOfSameColor value="1" />
      <defaultBodyHealth value="1000.000000" />
      <pretendOccupantsScale value="1.000000" />
      <visibleSpawnDistScale value="1.000000" />
      <trackerPathWidth value="2.000000" />
      <weaponForceMult value="1.000000" />
      <frequency value="30" />
      <swankness>SWANKNESS_4</swankness>
      <maxNum value="10" />
      <flags>FLAG_EXTRAS_STRONG FLAG_AVERAGE_CAR</flags>
      <type>VEHICLE_TYPE_CAR</type>
      <plateType>VPT_BACK_PLATES</plateType>
	  <dashboardType>VDT_DUKES</dashboardType>
      <vehicleClass>VC_MUSCLE</vehicleClass>
      <wheelType>VWT_MUSCLE</wheelType>
      <trailers />
      <additionalTrailers />
      <drivers />
      <extraIncludes />
      <doorsWithCollisionWhenClosed />
      <driveableDoors />
      <bumpersNeedToCollideWithMap value="false" />
      <needsRopeTexture value="false" />
      <requiredExtras />
      <rewards />
      <cinematicPartCamera>
        <Item>WHEEL_FRONT_RIGHT_CAMERA</Item>
        <Item>WHEEL_FRONT_LEFT_CAMERA</Item>
        <Item>WHEEL_REAR_RIGHT_CAMERA</Item>
        <Item>WHEEL_REAR_LEFT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet />
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000" />
      <buoyancySphereSizeScale value="1.000000" />
      <pOverrideRagdollThreshold type="NULL" />
	  <firstPersonDrivebyData>
        <Item>LOW_ELLIE_FRONT_LEFT</Item>
        <Item>LOW_ELLIE_FRONT_RIGHT</Item>
      </firstPersonDrivebyData>
    </Item>
  </InitDatas>
  <txdRelationships>
	<Item>
      <parent>vehicles_dom_interior</parent>
      <child>dom645</child>
	</Item>
	<Item>
      <parent>vehicles_dom_interior</parent>
      <child>dom645t</child>
	</Item>
  </txdRelationships>
</CVehicleModelInfo__InitDataList>