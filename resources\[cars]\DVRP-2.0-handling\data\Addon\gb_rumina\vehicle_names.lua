Citizen.CreateThread(function()
AddTextEntry("GBRUMINA", "Rumina")

AddTextEntry("RUMINA_SPL0", "Remove Wing")
AddTextEntry("RUMINA_SPL1", "Turbozone Wing")
AddTextEntry("RUMINA_SPL2", "Tuner Wing")
AddTextEntry("RUMINA_SPL3", "Vigor III Wing")
AddTextEntry("RUMINA_SPL4", "GT Wing")
AddTextEntry("RUMINA_SPL5", "Polygon Type A Wing (P)")
AddTextEntry("RUMINA_BUMF1", "LINK Bumper")
AddTextEntry("RUMINA_BUMF2", "RBS Perf. Bumper")
AddTextEntry("RUMINA_BUMF3", "Most Wanted Bumper")
AddTextEntry("RUMINA_BUMF4", "X-Flow Smooth Bumper")
AddTextEntry("RUMINA_BUMF5", "Cattana LSDC1 Bumper")
AddTextEntry("RUMINA_BUMR1", "LINK Bumper")
AddTextEntry("RUMINA_BUMR2", "RBS Perf. Bumper")
AddTextEntry("RUMINA_BUMR3", "Most Wanted Bumper")
AddTextEntry("RUMINA_BUMR4", "X-Flow Smooth Bumper")
AddTextEntry("RUMINA_BUMR5", "Cattana LSDC1 Bumper")
AddTextEntry("RUMINA_SKIRT1", "LINK Skirts")
AddTextEntry("RUMINA_SKIRT2", "RBS Perf. Skirts")
AddTextEntry("RUMINA_SKIRT3", "Most Wanted Skirts")
AddTextEntry("RUMINA_SKIRT4", "X-Flow Smooth Skirts")
AddTextEntry("RUMINA_SKIRT5", "Cattana LSDC1 Skirts")
AddTextEntry("RUMINA_HPIN1", "Chrome Catches")
AddTextEntry("RUMINA_HPIN2", "Angled Pins")
AddTextEntry("RUMINA_HPIN3", "Black Latches")
AddTextEntry("RUMINA_HPIN4", "Chrome Quick-Catches")
AddTextEntry("RUMINA_STRIP", "Sunstrip")
AddTextEntry("RUMINA_TRUNK1", "Custom Lipped Trunk")
AddTextEntry("RUMINA_HCOVER1", "Eyelids")
AddTextEntry("RUMINA_HCOVER2", "Half Vented Covers")
AddTextEntry("RUMINA_HCOVER3", "Vented Covers")
AddTextEntry("RUMINA_HCOVER4", "Full Covers")
AddTextEntry("RUMINA_HLIGHT1", "Chrome Housings")
AddTextEntry("RUMINA_HLIGHT2", "Painted Housings")
AddTextEntry("RUMINA_TAIL0A", "Chrome Light Trim")
AddTextEntry("RUMINA_TAIL0B", "Painted Light Trim")
AddTextEntry("RUMINA_TAIL1", "Aftermarket Taillights")
AddTextEntry("RUMINA_TAIL1A", "Aftermarket Taillights (P)")
AddTextEntry("RUMINA_TAIL1B", "Aftermarket Taillights (CF)")
AddTextEntry("RUMINA_MIR0A", "Stock Mirrors (CF)")
AddTextEntry("RUMINA_MIR1", "Aero Mirrors")
AddTextEntry("RUMINA_MIR1A", "Aero Mirrors (CF)")
AddTextEntry("RUMINA_HOOD1", "Auto Exotic Hood")
AddTextEntry("RUMINA_HOOD1A", "Auto Exotic Hood (CF)")
AddTextEntry("RUMINA_HOOD2", "Endo Raised Hood")
AddTextEntry("RUMINA_HOOD2A", "Endo Raised Hood (CF)")
AddTextEntry("RUMINA_HOOD3", "X-Flow Hood")
AddTextEntry("RUMINA_HOOD3A", "X-Flow Hood (CF)")
AddTextEntry("RUMINA_HOOD4", "Annisport Hood")
AddTextEntry("RUMINA_HOOD4A", "Annisport Hood (CF)")
AddTextEntry("RUMINA_HOOD5", "Annisport II Hood")
AddTextEntry("RUMINA_HOOD5A", "Annisport II Hood (CF)")
AddTextEntry("RUMINA_HOOD6", "Most Wanted Hood")
AddTextEntry("RUMINA_HOOD6A", "Most Wanted Hood (CF)")
AddTextEntry("RUMINA_WGR1", "Bolt-on Rear Fenders")
AddTextEntry("RUMINA_WGR1A", "Bolt-on Rear Fenders (CF)")
AddTextEntry("RUMINA_WGR2", "Turbozone Rear Fenders")
AddTextEntry("RUMINA_WGR3", "Most Wanted Rear Fenders")
AddTextEntry("RUMINA_WGF1", "Most Wanted Front Fenders")
AddTextEntry("RUMINA_WGF1A", "Most Wanted Front Fenders (CF)")
AddTextEntry("RUMINA_WGF2", "Turbozone Front Fenders")

AddTextEntry("RUMINA_LIV1", "Salvator Mundi")
AddTextEntry("RUMINA_LIV2", "RBS Performance")
AddTextEntry("RUMINA_LIV3", "Cattana LSDC1")

AddTextEntry("TOP_RMN_WGR", "Rear Fenders")
AddTextEntry("TOP_RMN_WGF", "Front Fenders")
AddTextEntry("TOP_RMN_BUMR", "Rear Bumper & Exhaust")
AddTextEntry("TOP_RMN_HL", "Headlight Accessory")
AddTextEntry("TOP_RMN_TAIL", "Taillights")
end)