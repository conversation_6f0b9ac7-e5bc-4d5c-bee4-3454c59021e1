<?xml version="1.0" encoding="UTF-8"?>

<CVehicleMetadataMgr>
	<ClipSetMaps>
	</ClipSetMaps>
	<VehicleDriveByInfos>
	</VehicleDriveByInfos>
	<VehicleDriveByAnimInfos>
	</VehicleDriveByAnimInfos>
	<VehicleCoverBoundOffsetInfos>
	</VehicleCoverBoundOffsetInfos>
	<VehicleSeatInfos>
	</VehicleSeatInfos> 
	<VehicleSeatAnimInfos>
	</VehicleSeatAnimInfos>
	<VehicleEntryPointInfos>
		<Item type="CVehicleEntryPointInfo">
			<Name>ENTRY_POINT_PLUMPO_LEFT_1</Name>
			<DoorBoneName />
			<SecondDoorBoneName />
			<DoorHandleBoneName />
			<WindowId>INVALID</WindowId>
			<VehicleSide>SIDE_LEFT</VehicleSide>
			<AccessableSeats>
				<Item ref="SEAT_EXTRA_LEFT_1" />
			</AccessableSeats>
			<VehicleExtraPointsInfo ref="NULL" />
			<Flags>MPWarpInOut SPEntryAllowedAlso</Flags>
			<BlockJackReactionSides />
		</Item>
		<Item type="CVehicleEntryPointInfo">
			<Name>ENTRY_POINT_PLUMPO_RIGHT_1</Name>
			<DoorBoneName />
			<SecondDoorBoneName />
			<DoorHandleBoneName />
			<WindowId>INVALID</WindowId>
			<VehicleSide>SIDE_RIGHT</VehicleSide>
			<AccessableSeats>
				<Item ref="SEAT_EXTRA_RIGHT_1" />
			</AccessableSeats>
			<VehicleExtraPointsInfo ref="NULL" />
			<Flags>MPWarpInOut SPEntryAllowedAlso</Flags>
			<BlockJackReactionSides />
		</Item>
		<Item type="CVehicleEntryPointInfo">
			<Name>ENTRY_POINT_PLUMPO_LEFT_2</Name>
			<DoorBoneName>cargodoor</DoorBoneName>
			<SecondDoorBoneName />
			<DoorHandleBoneName />
			<WindowId>INVALID</WindowId>
			<VehicleSide>SIDE_LEFT</VehicleSide>
			<AccessableSeats>
				<Item ref="SEAT_EXTRA_LEFT_2" />
			</AccessableSeats>
			<VehicleExtraPointsInfo ref="EXTRA_VEHICLE_POINTS_STD_LEFT" />
			<Flags>BlockJackReactionUntilJackerIsReady</Flags>
			<BlockJackReactionSides />
		</Item>
		<Item type="CVehicleEntryPointInfo">
			<Name>ENTRY_POINT_PLUMPO_RIGHT_2</Name>
			<DoorBoneName>boot</DoorBoneName>
			<SecondDoorBoneName />
			<DoorHandleBoneName />
			<WindowId>INVALID</WindowId>
			<VehicleSide>SIDE_RIGHT</VehicleSide>
			<AccessableSeats>
				<Item ref="SEAT_EXTRA_RIGHT_2" />
			</AccessableSeats>
			<VehicleExtraPointsInfo ref="EXTRA_VEHICLE_POINTS_STD_RIGHT" />
			<Flags>BlockJackReactionUntilJackerIsReady</Flags>
			<BlockJackReactionSides />
		</Item>
	</VehicleEntryPointInfos>
	<VehicleEntryPointAnimInfos>
		<Item type="CVehicleEntryPointAnimInfo">
			<Name>ENTRY_POINT_ANIM_PLUMPO_VAN_REAR_LEFT</Name>
			<CommonClipSetMap ref="COMMON_CLIPSET_MAP_TRUCK_LEFT" />
			<EntryClipSetMap ref="ENTRY_CLIPSET_MAP_RIOT_VAN_REAR_LEFT" />
			<ExitClipSetMap ref="EXIT_CLIPSET_MAP_RIOT_VAN_REAR_LEFT" />
			<AlternateTryLockedDoorClipId />
			<AlternateForcedEntryClipId />
			<AlternateJackFromOutSideClipId />
			<AlternateBeJackedFromOutSideClipId />
			<AlternateEntryPointClipSetId />
			<EnterVehicleMoveNetwork>ENTER_VEHICLE_STD</EnterVehicleMoveNetwork>
			<EntryTranslation x="4.000000" y="0.145000" z="-0.462000" />
			<OpenDoorTranslation x="0.000000" y="0.000000" />
			<OpenDoorHeadingChange value="0.000000" />
			<EntryHeadingChange value="1.570800" />
			<ExtraZForWaterEntry value="0.000000" />
			<EntryPointFlags>UseVehicleRelativeEntryPosition JackIncludesGetIn NavigateToWarpEntryPoint UseOpenDoorBlendAnims UseGetUpAfterJack JackVariationsIncludeGetIn DeadJackIncludesGetIn</EntryPointFlags>
			<EntryAnimVariations ref="ENTRY_ANIM_VARIATIONS_VAN_REAR_LEFT" />
		</Item>
		<Item type="CVehicleEntryPointAnimInfo">
			<Name>ENTRY_POINT_ANIM_PLUMPO_VAN_REAR_RIGHT</Name>
			<CommonClipSetMap ref="COMMON_CLIPSET_MAP_TRUCK_LEFT" />
			<EntryClipSetMap ref="ENTRY_CLIPSET_MAP_RIOT_VAN_REAR_RIGHT" />
			<ExitClipSetMap ref="EXIT_CLIPSET_MAP_RIOT_VAN_REAR_RIGHT" />
			<AlternateTryLockedDoorClipId />
			<AlternateForcedEntryClipId />
			<AlternateJackFromOutSideClipId />
			<AlternateBeJackedFromOutSideClipId />
			<AlternateEntryPointClipSetId />
			<EnterVehicleMoveNetwork>ENTER_VEHICLE_STD</EnterVehicleMoveNetwork>
			<EntryTranslation x="-4.000000" y="0.113600" z="-0.490000" />
			<OpenDoorTranslation x="0.000000" y="0.000000" />
			<OpenDoorHeadingChange value="0.000000" />
			<EntryHeadingChange value="-1.570800" />
			<ExtraZForWaterEntry value="0.000000" />
			<EntryPointFlags>UseVehicleRelativeEntryPosition JackIncludesGetIn NavigateToWarpEntryPoint UseOpenDoorBlendAnims UseGetUpAfterJack JackVariationsIncludeGetIn DeadJackIncludesGetIn</EntryPointFlags>
			<EntryAnimVariations ref="ENTRY_ANIM_VARIATIONS_VAN_REAR_RIGHT" />
		</Item>
	</VehicleEntryPointAnimInfos>
	<VehicleLayoutInfos>
		<Item type="CVehicleLayoutInfo">
			<Name>LAYOUT_VAN_PLUMPO</Name>
			<Seats>
				<Item>
					<SeatInfo ref="SEAT_VAN_FRONT_LEFT" />
					<SeatAnimInfo ref="SEAT_ANIM_VAN_FRONT_LEFT" />
				</Item>
				<Item>
					<SeatInfo ref="SEAT_VAN_FRONT_RIGHT" />
					<SeatAnimInfo ref="SEAT_ANIM_VAN_FRONT_RIGHT" />
				</Item>
				<Item>
					<SeatInfo ref="SEAT_VAN_REAR_LEFT" />
					<SeatAnimInfo ref="SEAT_ANIM_VAN_REAR_LEFT" />
				</Item>
				<Item>
					<SeatInfo ref="SEAT_VAN_REAR_RIGHT" />
					<SeatAnimInfo ref="SEAT_ANIM_VAN_REAR_RIGHT" />
				</Item>
				<Item>
					<SeatInfo ref="SEAT_EXTRA_LEFT_1" />
					<SeatAnimInfo ref="SEAT_ANIM_VAN_EXTRA_LEFT" />
				</Item>
				<Item>
					<SeatInfo ref="SEAT_EXTRA_RIGHT_1" />
					<SeatAnimInfo ref="SEAT_ANIM_VAN_EXTRA_RIGHT" />
				</Item>
				<Item>
					<SeatInfo ref="SEAT_EXTRA_LEFT_2" />
					<SeatAnimInfo ref="SEAT_ANIM_STD_LOWRIDER2_SIDEDOOR_REAR_LEFT" />
				</Item>
				<Item>
					<SeatInfo ref="SEAT_EXTRA_RIGHT_2" />
					<SeatAnimInfo ref="SEAT_ANIM_STD_LOWRIDER2_SIDEDOOR_REAR_RIGHT" />
				</Item>
			</Seats>
			<EntryPoints>
				<Item>
					<EntryPointInfo ref="ENTRY_POINT_VAN_FRONT_LEFT" />
					<EntryPointAnimInfo ref="ENTRY_POINT_ANIM_VAN_FRONT_LEFT" />
				</Item>
				<Item>
					<EntryPointInfo ref="ENTRY_POINT_VAN_FRONT_RIGHT" />
					<EntryPointAnimInfo ref="ENTRY_POINT_ANIM_VAN_FRONT_RIGHT" />
				</Item>
				<Item>
					<EntryPointInfo ref="ENTRY_POINT_VAN_REAR_LEFT" />
					<EntryPointAnimInfo ref="ENTRY_POINT_ANIM_VAN_REAR_LEFT" />
				</Item>
				<Item>
					<EntryPointInfo ref="ENTRY_POINT_VAN_REAR_RIGHT" />
					<EntryPointAnimInfo ref="ENTRY_POINT_ANIM_VAN_REAR_RIGHT" />
				</Item>
				<Item>
					<EntryPointInfo ref="ENTRY_POINT_PLUMPO_LEFT_1" />
					<EntryPointAnimInfo ref="ENTRY_POINT_ANIM_PLUMPO_VAN_REAR_LEFT" />
				</Item>
				<Item>
					<EntryPointInfo ref="ENTRY_POINT_PLUMPO_RIGHT_1" />
					<EntryPointAnimInfo ref="ENTRY_POINT_ANIM_PLUMPO_VAN_REAR_RIGHT" />
				</Item>
				<Item>
					<EntryPointInfo ref="ENTRY_POINT_PLUMPO_LEFT_2" />
					<EntryPointAnimInfo ref="ENTRY_POINT_ANIM_STD_LOWRIDER2_SIDEDOOR_REAR_LEFT" />
				</Item>
				<Item>
					<EntryPointInfo ref="ENTRY_POINT_PLUMPO_RIGHT_2" />
					<EntryPointAnimInfo ref="ENTRY_POINT_ANIM_STD_LOWRIDER2_SIDEDOOR_REAR_RIGHT" />
				</Item>
			</EntryPoints>
			<LayoutFlags>StreamAnims UseLeanSteerAnims</LayoutFlags>
			<BicycleInfo ref="NULL" />
			<HandsUpClipSetId>busted_vehicle_std</HandsUpClipSetId>
			<SteeringWheelOffset x="0.000000" y="0.350000" z="0.320000" />
			<MaxXAcceleration value="4.00000" />
			<BodyLeanXApproachSpeed value="5.00000" />
			<BodyLeanXSmallDelta value="0.30000" />
			<FirstPersonAdditiveIdleClipSets>
				<Item>clipset@veh@van@ds@idle_a</Item>
				<Item>clipset@veh@van@ds@idle_b</Item>
				<Item>clipset@veh@van@ds@idle_c</Item>
				<Item>clipset@veh@van@ds@idle_d</Item>
				<Item>clipset@veh@van@ds@idle_e</Item>
			</FirstPersonAdditiveIdleClipSets>
			<FirstPersonRoadRageClipSets>
				<Item>clipset@veh@van@ds@hit_wheel@idle_a</Item>
				<Item>clipset@veh@van@ds@hit_wheel@idle_b</Item>
				<Item>clipset@veh@van@ds@hit_wheel@idle_c</Item>
			</FirstPersonRoadRageClipSets>
		</Item>
	</VehicleLayoutInfos>
</CVehicleMetadataMgr>