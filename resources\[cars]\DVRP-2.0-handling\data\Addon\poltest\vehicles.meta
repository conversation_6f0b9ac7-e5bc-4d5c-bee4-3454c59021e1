<?xml version="1.0" encoding="UTF-8"?>
<CVehicleModelInfo__InitDataList>
  <residentTxd>vehshare</residentTxd>
  <residentAnims />
  <InitDatas> 
    <Item>
      <modelName>poltenf</modelName>
      <txdName>poltenf</txdName>
      <handlingId>TENF</handlingId>
      <gameName>TENF</gameName>
      <vehicleMakeName>OBEY</vehicleMakeName>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected />
      <ptfxAssetName>null</ptfxAssetName>
      <audioNameHash>tenf2</audioNameHash>
      <layout>LAYOUT_LOW_RESTRICTED</layout>
      <coverBoundOffsets>TENF_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout />
      <cameraName>FOLLOW_CHEETAH_CAMERA</cameraName>
      <aimCameraName>DEFAULT_THIRD_PERSON_VEHICLE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>VEHICLE_BONNET_CAMERA_STANDARD_LONG_EXTRA_LOW</bonnetCameraName>
      <povCameraName>REDUCED_NEAR_CLIP_POV_CAMERA</povCameraName>
      <FirstPersonDriveByIKOffset x="0.020000" y="-0.100000" z="-0.010000" />
      <FirstPersonDriveByUnarmedIKOffset x="0.000000" y="-0.130000" z="-0.020000" />
	  <FirstPersonProjectileDriveByIKOffset x="0.060000" y="-0.180000" z="-0.040000" />
	  <FirstPersonProjectileDriveByPassengerIKOffset x="-0.060000" y="-0.180000" z="-0.040000" />
	  <FirstPersonDriveByRightPassengerIKOffset x="-0.020000" y="-0.100000" z="-0.010000" />
	  <FirstPersonDriveByRightPassengerUnarmedIKOffset x="-0.020000" y="-0.140000" z="-0.020000" />
	  <FirstPersonMobilePhoneOffset x="0.133000" y="0.115000" z="0.495000" />
      <FirstPersonPassengerMobilePhoneOffset x="0.200000" y="0.060000" z="0.385000" />
      <FirstPersonMobilePhoneSeatIKOffset />
      <PovCameraOffset x="0.000000" y="-0.305000" z="0.613000" />
      <PovCameraVerticalAdjustmentForRollCage value="0.000000" />
      <PovPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000" />
      <vfxInfoName>VFXVEHICLEINFO_CAR_GENERIC</vfxInfoName>
      <shouldUseCinematicViewMode value="true" />
      <shouldCameraTransitionOnClimbUpDown value="false" />
      <shouldCameraIgnoreExiting value="false" />
      <AllowPretendOccupants value="true" />
      <AllowJoyriding value="true" />
      <AllowSundayDriving value="true" />
      <AllowBodyColorMapping value="true" />
      <wheelScale value="0.282000" />
      <wheelScaleRear value="0.282000" />
      <dirtLevelMin value="0.000000" />
      <dirtLevelMax value="0.450000" />
      <envEffScaleMin value="0.000000" />
      <envEffScaleMax value="1.000000" />
      <envEffScaleMin2 value="0.000000" />
      <envEffScaleMax2 value="1.000000" />
      <damageMapScale value="0.350000" />
      <damageOffsetScale value="0.350000" />
      <diffuseTint value="0x00FFFFFF" />
      <steerWheelMult value="1.000000" />
      <HDTextureDist value="5.000000" />
      <lodDistances content="float_array">
        15.000000
        30.000000
        60.000000	
        120.000000
        500.000000
        500.000000
      </lodDistances>
      <minSeatHeight value="0.844" />
      <identicalModelSpawnDistance value="20" />
      <maxNumOfSameColor value="1" />
      <defaultBodyHealth value="1000.000000" />
      <pretendOccupantsScale value="1.000000" />
      <visibleSpawnDistScale value="1.000000" />
      <trackerPathWidth value="2.000000" />
      <weaponForceMult value="1.000000" />
      <frequency value="30" />
      <swankness>SWANKNESS_4</swankness>
      <maxNum value="10" />
      <flags>FLAG_AVERAGE_CAR FLAG_HAS_INTERIOR_EXTRAS FLAG_CAN_HAVE_NEONS FLAG_EXTRAS_ALL FLAG_BOOT_IN_FRONT FLAG_EXTRAS_STRONG FLAG_RECESSED_HEADLIGHT_CORONAS
        FLAG_USE_INTERIOR_RED_LIGHT FLAG_LAW_ENFORCEMENT FLAG_EMERGENCY_SERVICE</flags>
      <type>VEHICLE_TYPE_CAR</type>
      <plateType>VPT_BACK_PLATES</plateType>
	  <dashboardType>VDT_RACE</dashboardType>
      <vehicleClass>VC_SPORT</vehicleClass>
      <wheelType>VWT_HIEND</wheelType>
      <trailers />
      <additionalTrailers />
      <drivers />
      <extraIncludes />
      <doorsWithCollisionWhenClosed />
      <driveableDoors />
      <bumpersNeedToCollideWithMap value="false" />
      <needsRopeTexture value="false" />
      <requiredExtras />
      <rewards />
      <cinematicPartCamera>
        <Item>WHEEL_FRONT_RIGHT_CAMERA</Item>
        <Item>WHEEL_FRONT_LEFT_CAMERA</Item>
        <Item>WHEEL_REAR_RIGHT_CAMERA</Item>
        <Item>WHEEL_REAR_LEFT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet />
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000" />
      <buoyancySphereSizeScale value="1.000000" />
      <pOverrideRagdollThreshold type="NULL" />
	  <firstPersonDrivebyData>
		<Item>LOW_TENF_FRONT_LEFT</Item>
		<Item>LOW_TENF_FRONT_RIGHT</Item>
      </firstPersonDrivebyData>
    </Item>
	<Item>
      <modelName>polballer7</modelName>
      <txdName>polballer7</txdName>
      <handlingId>BALLER7</handlingId>
      <gameName>BALLER7</gameName>
      <vehicleMakeName>GALLIVAN</vehicleMakeName>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected />
      <ptfxAssetName>null</ptfxAssetName>
      <audioNameHash>baller7</audioNameHash>
      <layout>LAYOUT_STD_HIGHWINDOW</layout>
      <coverBoundOffsets>BALLER7_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout />
      <cameraName>DEFAULT_FOLLOW_VEHICLE_CAMERA</cameraName>
      <aimCameraName>DEFAULT_THIRD_PERSON_VEHICLE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>VEHICLE_BONNET_CAMERA_STANDARD_LONG</bonnetCameraName>
      <povCameraName>DEFAULT_POV_CAMERA</povCameraName>
      <FirstPersonDriveByIKOffset x="0.000000" y="-0.040000" z="-0.040000" />
      <FirstPersonDriveByUnarmedIKOffset x="0.000000" y="0.000000" z="-0.020000" />
      <FirstPersonProjectileDriveByIKOffset x="0.030000" y="0.000000" z="0.000000" />
      <FirstPersonProjectileDriveByPassengerIKOffset x="-0.030000" y="0.000000" z="0.000000" />
      <FirstPersonProjectileDriveByRearLeftIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonProjectileDriveByRearRightIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonDriveByLeftPassengerIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonDriveByRightPassengerIKOffset x="0.000000" y="-0.040000" z="-0.040000" />
      <FirstPersonDriveByRightRearPassengerIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonDriveByLeftPassengerUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonDriveByRightPassengerUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonMobilePhoneOffset x="0.135000" y="0.240000" z="0.520000" />
      <FirstPersonPassengerMobilePhoneOffset x="0.136000" y="0.223000" z="0.425000" />
      <FirstPersonMobilePhoneSeatIKOffset>
        <Item>
            <Offset x="0.136000" y="0.136000" z="0.415000" />
            <SeatIndex value="2" />
        </Item>
        <Item>
            <Offset x="0.136000" y="0.136000" z="0.415000" />
            <SeatIndex value="3" />
        </Item>
      </FirstPersonMobilePhoneSeatIKOffset> 
      <PovCameraOffset x="0.000000" y="-0.165000" z="0.645000" />
      <PovCameraVerticalAdjustmentForRollCage value="0.000000" />
      <PovPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000" />
      <PovRearPassengerCameraOffset x="0.000000" y="-0.025000" z="0.000000" />
      <vfxInfoName>VFXVEHICLEINFO_CAR_GENERIC</vfxInfoName>
      <shouldUseCinematicViewMode value="true" />
      <shouldCameraTransitionOnClimbUpDown value="false" />
      <shouldCameraIgnoreExiting value="false" />
      <AllowPretendOccupants value="true" />
      <AllowJoyriding value="true" />
      <AllowSundayDriving value="true" />
      <AllowBodyColorMapping value="true" />
      <wheelScale value="0.344200" />
      <wheelScaleRear value="0.344200" />
      <dirtLevelMin value="0.000000" />
      <dirtLevelMax value="0.550000" />
      <envEffScaleMin value="0.000000" />
      <envEffScaleMax value="0.000000" />
      <envEffScaleMin2 value="0.000000" />
      <envEffScaleMax2 value="0.000000" />
      <damageMapScale value="0.300000" />
      <damageOffsetScale value="1.000000" />
      <diffuseTint value="0x00FFFFFF" />
      <steerWheelMult value="1.000000" />
      <HDTextureDist value="5.000000" />
      <lodDistances content="float_array">
        15.000000   
        30.000000
        70.000000   
        140.000000  
        350.000000  
        350.000000
      </lodDistances>
      <minSeatHeight value="0.952" />
      <identicalModelSpawnDistance value="20" />
      <maxNumOfSameColor value="10" />
      <defaultBodyHealth value="1000.000000" />
      <pretendOccupantsScale value="1.000000" />
      <visibleSpawnDistScale value="1.000000" />
      <trackerPathWidth value="2.000000" />
      <weaponForceMult value="1.000000" />
      <frequency value="100" />
      <swankness>SWANKNESS_3</swankness>
      <maxNum value="999" />
      <flags>FLAG_PARKING_SENSORS FLAG_RICH_CAR FLAG_IS_OFFROAD_VEHICLE FLAG_IS_BULKY FLAG_HAS_INTERIOR_EXTRAS FLAG_EXTRAS_ALL FLAG_EXTRAS_STRONG FLAG_RECESSED_HEADLIGHT_CORONAS
        FLAG_USE_INTERIOR_RED_LIGHT FLAG_LAW_ENFORCEMENT FLAG_EMERGENCY_SERVICE</flags>
      <type>VEHICLE_TYPE_CAR</type>
      <plateType>VPT_FRONT_AND_BACK_PLATES</plateType>
      <dashboardType>VDT_CAVALCADE</dashboardType>
      <vehicleClass>VC_SUV</vehicleClass>
      <wheelType>VWT_SUV</wheelType>
      <trailers />
      <additionalTrailers />
      <drivers />
      <extraIncludes />
      <doorsWithCollisionWhenClosed />
      <driveableDoors />
      <bumpersNeedToCollideWithMap value="false" />
      <needsRopeTexture value="false" />
      <requiredExtras />
      <rewards />
      <cinematicPartCamera>
        <Item>WHEEL_FRONT_RIGHT_CAMERA</Item>
        <Item>WHEEL_FRONT_LEFT_CAMERA</Item>
        <Item>WHEEL_REAR_RIGHT_CAMERA</Item>
        <Item>WHEEL_REAR_LEFT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet>Truck</NmBraceOverrideSet>
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000" />
      <buoyancySphereSizeScale value="1.000000" />
      <pOverrideRagdollThreshold type="NULL" />
      <firstPersonDrivebyData>
        <Item>STD_BALLER7_FRONT_LEFT</Item>
        <Item>STD_BALLER7_FRONT_RIGHT</Item>
        <Item>STD_BALLER7_REAR_LEFT</Item>
        <Item>STD_BALLER7_REAR_RIGHT</Item>
      </firstPersonDrivebyData>
    </Item>
	  <Item>
      <modelName>polcavalcade3</modelName>
      <txdName>polcavalcade3</txdName>
      <handlingId>POLCAVALCADE3</handlingId>
      <gameName>CAVALCADE3</gameName>
      <vehicleMakeName>ALBANY</vehicleMakeName>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected />
      <ptfxAssetName>null</ptfxAssetName>
      <audioNameHash>cavalcade3</audioNameHash>
      <layout>LAYOUT_RANGER</layout>
      <coverBoundOffsets>CAVALCADE3_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout />
      <cameraName>DEFAULT_FOLLOW_VEHICLE_CAMERA</cameraName>
      <aimCameraName>BOX_VEHICLE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>VEHICLE_BONNET_CAMERA_STANDARD_LONG_REBLA</bonnetCameraName>
      <povCameraName>REDUCED_NEAR_CLIP_POV_CAMERA</povCameraName>
      <FirstPersonDriveByIKOffset x="0.020000" y="-0.090000" z="-0.070000" />
      <FirstPersonDriveByUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonProjectileDriveByIKOffset x="0.000000" y="-0.088000" z="-0.027000" />
      <FirstPersonProjectileDriveByPassengerIKOffset x="-0.113000" y="-0.005000" z="-0.078000" />
      <FirstPersonDriveByLeftPassengerIKOffset x="0.060000" y="0.010000" z="-0.020000" />
      <FirstPersonDriveByRightPassengerIKOffset x="-0.065000" y="-0.108000" z="-0.060000" />
      <FirstPersonDriveByLeftPassengerUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonDriveByRightPassengerUnarmedIKOffset x="-0.080000" y="-0.010000" z="-0.030000" />
      <FirstPersonDriveByRightRearPassengerIKOffset x="-0.060000" y="-0.020000" z="-0.040000" />
      <FirstPersonProjectileDriveByRearLeftIKOffset x="-0.040000" y="-0.068000" z="-0.047000" />
      <FirstPersonProjectileDriveByRearRightIKOffset x="-0.040000" y="-0.068000" z="-0.057000" />
      <FirstPersonMobilePhoneOffset x="0.150000" y="0.208000" z="0.570000" />
      <FirstPersonPassengerMobilePhoneOffset x="0.206000" y="0.223000" z="0.425000" />
      <FirstPersonDriveByRightRearPassengerIKOffset x="0.000000" y="-0.088000" z="-0.047000" />
      <PovCameraOffset x="0.000000" y="-0.175000" z="0.675000" />
      <PovCameraVerticalAdjustmentForRollCage value="-0.010000" />
      <PovPassengerCameraOffset x="0.010000" y="0.045000" z="0.005000" />
      <PovRearPassengerCameraOffset x="0.000000" y="0.150000" z="0.025000" />
      <vfxInfoName>VFXVEHICLEINFO_CAR_GENERIC</vfxInfoName>
      <shouldUseCinematicViewMode value="true" />
      <shouldCameraTransitionOnClimbUpDown value="false" />
      <shouldCameraIgnoreExiting value="false" />
      <AllowPretendOccupants value="true" />
      <AllowJoyriding value="true" />
      <AllowSundayDriving value="true" />
      <AllowBodyColorMapping value="true" />
      <wheelScale value="0.317000" />
      <wheelScaleRear value="0.317000" />
      <dirtLevelMin value="0.000000" />
      <dirtLevelMax value="0.850000" />
      <envEffScaleMin value="0.000000" />
      <envEffScaleMax value="0.000000" />
      <envEffScaleMin2 value="0.000000" />
      <envEffScaleMax2 value="0.000000" />
      <damageMapScale value="0.600000" />
      <damageOffsetScale value="1.000000" />
      <diffuseTint value="0xAA0A0A0A" />
      <steerWheelMult value="1.000000" />
      <HDTextureDist value="5.000000" />
      <lodDistances content="float_array">
        15.000000   
        35.000000   
        80.000000   
        160.000000  
        500.000000  
        500.000000
      </lodDistances>
      <minSeatHeight value="0.966" />
      <identicalModelSpawnDistance value="20" />
      <maxNumOfSameColor value="10" />
      <defaultBodyHealth value="1000.000000" />
      <pretendOccupantsScale value="1.000000" />
      <visibleSpawnDistScale value="1.000000" />
      <trackerPathWidth value="2.000000" />
      <weaponForceMult value="1.000000" />
      <frequency value="100" />
      <swankness>SWANKNESS_1</swankness>
      <maxNum value="999" />
      <flags>FLAG_AVERAGE_CAR FLAG_IS_OFFROAD_VEHICLE FLAG_IS_BULKY FLAG_INCREASE_CAMBER_WITH_SUSPENSION_MOD FLAG_EXTRAS_ALL FLAG_EXTRAS_STRONG FLAG_RECESSED_HEADLIGHT_CORONAS
        FLAG_USE_INTERIOR_RED_LIGHT FLAG_LAW_ENFORCEMENT FLAG_EMERGENCY_SERVICE</flags>
      <type>VEHICLE_TYPE_CAR</type>
      <plateType>VPT_FRONT_AND_BACK_PLATES</plateType>
      <dashboardType>VDT_RACE</dashboardType>
      <vehicleClass>VC_SUV</vehicleClass>
      <wheelType>VWT_SUV</wheelType>
      <trailers />
      <additionalTrailers />
      <drivers />
      <extraIncludes />
      <doorsWithCollisionWhenClosed />
      <driveableDoors />
      <bumpersNeedToCollideWithMap value="false" />
      <needsRopeTexture value="false" />
      <requiredExtras />
      <rewards />
      <cinematicPartCamera>
        <Item>WHEEL_FRONT_RIGHT_CAMERA</Item>
        <Item>WHEEL_FRONT_LEFT_CAMERA</Item>
        <Item>WHEEL_REAR_RIGHT_CAMERA</Item>
        <Item>WHEEL_REAR_LEFT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet />
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000" />
      <buoyancySphereSizeScale value="1.000000" />
      <pOverrideRagdollThreshold type="NULL" />
      <firstPersonDrivebyData>
        <Item>CAVALCADE3_FRONT_LEFT</Item>
        <Item>CAVALCADE3_FRONT_RIGHT</Item>
        <Item>CAVALCADE3_REAR_LEFT</Item>
        <Item>CAVALCADE3_REAR_RIGHT</Item>
      </firstPersonDrivebyData>
    </Item>   
	<Item>
      <modelName>polcomet5</modelName>
      <txdName>polcomet5</txdName>
      <handlingId>COMET5</handlingId>
      <gameName>COMET5</gameName>
      <vehicleMakeName>PFISTER</vehicleMakeName>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected />
      <ptfxAssetName>null</ptfxAssetName>
      <audioNameHash>comet5</audioNameHash>
      <layout>LAYOUT_LOW</layout>
      <coverBoundOffsets>COMET5_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout />
      <cameraName>FOLLOW_CHEETAH_CAMERA</cameraName>
      <aimCameraName>DEFAULT_THIRD_PERSON_VEHICLE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>VEHICLE_BONNET_CAMERA_LOW_LOW</bonnetCameraName>
      <povCameraName>DEFAULT_POV_CAMERA</povCameraName>
      <FirstPersonDriveByIKOffset x="0.030000" y="-0.010000" z="-0.035000" />
      <FirstPersonDriveByUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonProjectileDriveByIKOffset x="0.020000" y="-0.080000" z="0.000000" />
      <FirstPersonProjectileDriveByPassengerIKOffset x="-0.120000" y="-0.080000" z="0.000000" />
      <FirstPersonDriveByRightPassengerIKOffset x="-0.030000" y="0.020000" z="-0.035000" />
      <FirstPersonDriveByRightPassengerUnarmedIKOffset x="0.000000" y="-0.120000" z="-0.025000" />
      <FirstPersonMobilePhoneOffset x="0.155000" y="0.148000" z="0.523000" />
      <FirstPersonPassengerMobilePhoneOffset x="0.181000" y="0.083000" z="0.441000" />
      <PovCameraOffset x="0.000000" y="-0.305000" z="0.650000" />
      <PovCameraVerticalAdjustmentForRollCage value="0.000000" />
      <PovPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000" />
      <vfxInfoName>VFXVEHICLEINFO_CAR_GENERIC</vfxInfoName>
      <shouldUseCinematicViewMode value="true" />
      <shouldCameraTransitionOnClimbUpDown value="false" />
      <shouldCameraIgnoreExiting value="false" />
      <AllowPretendOccupants value="true" />
      <AllowJoyriding value="true" />
      <AllowSundayDriving value="true" />
      <AllowBodyColorMapping value="true" />
      <wheelScale value="0.266000" />
      <wheelScaleRear value="0.266000" />
      <dirtLevelMin value="0.000000" />
      <dirtLevelMax value="0.300000" />
      <envEffScaleMin value="0.000000" />
      <envEffScaleMax value="1.000000" />
      <envEffScaleMin2 value="0.000000" />
      <envEffScaleMax2 value="1.000000" />
      <damageMapScale value="0.600000" />
      <damageOffsetScale value="0.600000" />
      <diffuseTint value="0x00FFFFFF" />
      <steerWheelMult value="1.000000" />
      <HDTextureDist value="5.000000" />
      <lodDistances content="float_array">
        10.000000   
        25.000000   
        60.000000   
        120.000000  
        500.000000  
        500.000000
      </lodDistances>
      <minSeatHeight value="0.881" />
      <identicalModelSpawnDistance value="20" />
      <maxNumOfSameColor value="1" />
      <defaultBodyHealth value="1000.000000" />
      <pretendOccupantsScale value="1.000000" />
      <visibleSpawnDistScale value="1.000000" />
      <trackerPathWidth value="2.000000" />
      <weaponForceMult value="1.000000" />
      <frequency value="30" />
      <swankness>SWANKNESS_5</swankness>
      <maxNum value="5" />
      <flags>FLAG_BOOT_IN_FRONT FLAG_SPORTS FLAG_SPAWN_ON_TRAILER FLAG_PARKING_SENSORS FLAG_RICH_CAR FLAG_NO_BROKEN_DOWN_SCENARIO FLAG_CAN_HAVE_NEONS FLAG_EXTRAS_ALL FLAG_EXTRAS_STRONG FLAG_RECESSED_HEADLIGHT_CORONAS
        FLAG_USE_INTERIOR_RED_LIGHT FLAG_LAW_ENFORCEMENT FLAG_EMERGENCY_SERVICE</flags>
      <type>VEHICLE_TYPE_CAR</type>
      <plateType>VPT_BACK_PLATES</plateType>
      <dashboardType>VDT_BANSHEE</dashboardType>
      <vehicleClass>VC_SPORT</vehicleClass>
      <wheelType>VWT_SPORT</wheelType>
      <trailers />
      <additionalTrailers />
      <drivers />
      <extraIncludes />
      <doorsWithCollisionWhenClosed />
      <driveableDoors />
      <bumpersNeedToCollideWithMap value="false" />
      <needsRopeTexture value="false" />
      <requiredExtras />
      <rewards />
      <cinematicPartCamera>
        <Item>WHEEL_FRONT_RIGHT_CAMERA</Item>
        <Item>WHEEL_FRONT_LEFT_CAMERA</Item>
        <Item>WHEEL_REAR_RIGHT_CAMERA</Item>
        <Item>WHEEL_REAR_LEFT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet />
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000" />
      <buoyancySphereSizeScale value="1.000000" />
      <pOverrideRagdollThreshold type="NULL" />
      <firstPersonDrivebyData>
        <Item>LOW_COMET5_FRONT_LEFT</Item>
        <Item>LOW_COMET5_FRONT_RIGHT</Item>
      </firstPersonDrivebyData>
    </Item>
	<Item>
      <modelName>polgranger2</modelName>
      <txdName>polgranger2</txdName>
      <handlingId>GRANGER2</handlingId>
      <gameName>GRANGER2</gameName>
      <vehicleMakeName>DECLASSE</vehicleMakeName>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected />
      <ptfxAssetName>weap_ba_vehicle_weapons</ptfxAssetName>
      <audioNameHash>granger2</audioNameHash>
      <layout>LAYOUT_STD_GRANGER2</layout>
      <coverBoundOffsets>GRANGER2_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout />
      <cameraName>DEFAULT_FOLLOW_VEHICLE_CAMERA</cameraName>
      <aimCameraName>BOX_VEHICLE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>VEHICLE_BONNET_CAMERA_STANDARD_LONG_REBLA</bonnetCameraName>
      <povCameraName>REDUCED_NEAR_CLIP_POV_CAMERA_LOOKAROUND</povCameraName>
      <FirstPersonDriveByIKOffset x="0.020000" y="-0.090000" z="-0.070000" />
      <FirstPersonDriveByUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonProjectileDriveByIKOffset x="0.000000" y="-0.088000" z="-0.027000" />
      <FirstPersonProjectileDriveByPassengerIKOffset x="-0.113000" y="-0.005000" z="-0.078000" />
      <FirstPersonDriveByLeftPassengerIKOffset x="0.060000" y="0.010000" z="-0.020000" />
      <FirstPersonDriveByRightPassengerIKOffset x="-0.065000" y="-0.108000" z="-0.060000" />
      <FirstPersonDriveByLeftPassengerUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonDriveByRightPassengerUnarmedIKOffset x="-0.080000" y="-0.010000" z="-0.030000" />
      <FirstPersonDriveByRightRearPassengerIKOffset x="-0.060000" y="-0.020000" z="-0.040000" />
      <FirstPersonProjectileDriveByRearLeftIKOffset x="-0.040000" y="-0.068000" z="-0.047000" />
      <FirstPersonProjectileDriveByRearRightIKOffset x="-0.040000" y="-0.068000" z="-0.057000" />
      <FirstPersonMobilePhoneOffset x="0.150000" y="0.208000" z="0.570000" />
      <FirstPersonPassengerMobilePhoneOffset x="0.206000" y="0.223000" z="0.425000" />
      <FirstPersonDriveByRightRearPassengerIKOffset x="0.000000" y="-0.088000" z="-0.047000" />
      <PovCameraOffset x="0.025000" y="-0.125000" z="0.575000" />
      <PovCameraVerticalAdjustmentForRollCage value="-0.010000" />
      <PovPassengerCameraOffset x="0.010000" y="0.045000" z="0.085000" />
      <PovRearPassengerCameraOffset x="0.000000" y="0.150000" z="0.110000" />
      <vfxInfoName>VFXVEHICLEINFO_CAR_GENERIC</vfxInfoName>
      <shouldUseCinematicViewMode value="true" />
      <shouldCameraTransitionOnClimbUpDown value="false" />
      <shouldCameraIgnoreExiting value="false" />
      <AllowPretendOccupants value="true" />
      <AllowJoyriding value="true" />
      <AllowSundayDriving value="true" />
      <AllowBodyColorMapping value="true" />
      <wheelScale value="0.292300" />
      <wheelScaleRear value="0.292300" />
      <dirtLevelMin value="0.000000" />
      <dirtLevelMax value="0.850000" />
      <envEffScaleMin value="0.000000" />
      <envEffScaleMax value="0.000000" />
      <envEffScaleMin2 value="0.000000" />
      <envEffScaleMax2 value="0.000000" />
      <damageMapScale value="0.600000" />
      <damageOffsetScale value="1.000000" />
      <diffuseTint value="0xAA0A0A0A" />
      <steerWheelMult value="1.000000" />
      <HDTextureDist value="5.000000" />
      <lodDistances content="float_array">
        15.000000   
        35.000000   
        80.000000   
        160.000000  
        500.000000  
        500.000000
      </lodDistances>
      <minSeatHeight value="0.966" />
      <identicalModelSpawnDistance value="20" />
      <maxNumOfSameColor value="10" />
      <defaultBodyHealth value="1000.000000" />
      <pretendOccupantsScale value="1.000000" />
      <visibleSpawnDistScale value="1.000000" />
      <trackerPathWidth value="2.000000" />
      <weaponForceMult value="1.000000" />
      <frequency value="100" />
      <swankness>SWANKNESS_1</swankness>
      <maxNum value="999" />
      <flags>FLAG_AVERAGE_CAR FLAG_POOR_CAR FLAG_ALLOWS_RAPPEL FLAG_IS_OFFROAD_VEHICLE FLAG_IS_BULKY FLAG_RECESSED_HEADLIGHT_CORONAS FLAG_HAS_INTERIOR_EXTRAS FLAG_EXTRAS_ALL FLAG_EXTRAS_STRONG FLAG_RECESSED_HEADLIGHT_CORONAS
        FLAG_USE_INTERIOR_RED_LIGHT FLAG_LAW_ENFORCEMENT FLAG_EMERGENCY_SERVICE</flags>
      <type>VEHICLE_TYPE_CAR</type>
      <plateType>VPT_FRONT_AND_BACK_PLATES</plateType>
      <dashboardType>VDT_RACE</dashboardType>
      <vehicleClass>VC_SUV</vehicleClass>
      <wheelType>VWT_SUV</wheelType>
      <trailers />
      <additionalTrailers />
      <drivers />
      <extraIncludes />
      <doorsWithCollisionWhenClosed />
      <driveableDoors />
      <bumpersNeedToCollideWithMap value="false" />
      <needsRopeTexture value="false" />
      <requiredExtras />
      <rewards />
      <cinematicPartCamera>
        <Item>WHEEL_FRONT_RIGHT_CAMERA</Item>
        <Item>WHEEL_FRONT_LEFT_CAMERA</Item>
        <Item>WHEEL_REAR_RIGHT_CAMERA</Item>
        <Item>WHEEL_REAR_LEFT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet />
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000" />
      <buoyancySphereSizeScale value="1.000000" />
      <pOverrideRagdollThreshold type="NULL" />
      <firstPersonDrivebyData>
        <Item>GRANGER2_FRONT_LEFT</Item>
        <Item>GRANGER2_FRONT_RIGHT</Item>
        <Item>GRANGER2_REAR_LEFT</Item>
        <Item>GRANGER2_REAR_RIGHT</Item>
      </firstPersonDrivebyData>
    </Item>
	<Item>
      <modelName>polimpaler7</modelName>
      <txdName>polimpaler7</txdName>
      <handlingId>POLIMPALER5</handlingId>
      <gameName>POLIMPALER5</gameName>
      <vehicleMakeName>DECLASSE</vehicleMakeName>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected />
      <ptfxAssetName>null</ptfxAssetName>
      <audioNameHash>impaler5</audioNameHash>
      <layout>LAYOUT_STD_IMPALER5</layout>
      <coverBoundOffsets>IMPALER5_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout />
      <cameraName>FOLLOW_CHEETAH_CAMERA</cameraName>
      <aimCameraName>MID_BOX_VEHICLE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>VEHICLE_BONNET_CAMERA_LOW_CALICO</bonnetCameraName>
      <povCameraName>REDUCED_NEAR_CLIP_POV_CAMERA</povCameraName>
      <FirstPersonDriveByIKOffset x="0.000000" y="-0.050000" z="-0.100000" />
      <FirstPersonDriveByUnarmedIKOffset x="0.000000" y="-0.025000" z="0.000000" />
      <FirstPersonProjectileDriveByIKOffset x="0.000000" y="-0.080000" z="-0.020000" />
      <FirstPersonProjectileDriveByPassengerIKOffset x="0.000000" y="-0.080000" z="-0.020000" />
      <FirstPersonDriveByLeftPassengerIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonDriveByRightPassengerIKOffset x="0.000000" y="-0.050000" z="-0.060000" />
      <FirstPersonDriveByLeftPassengerUnarmedIKOffset x="0.000000" y="-0.000000" z="0.000000" />
      <FirstPersonDriveByRightPassengerUnarmedIKOffset x="0.000000" y="-0.000000" z="0.000000" />
      <FirstPersonMobilePhoneOffset x="0.170000" y="0.270000" z="0.510000" />
      <FirstPersonPassengerMobilePhoneOffset x="0.135000" y="0.085000" z="0.465000" />
      <FirstPersonMobilePhoneSeatIKOffset /> 
      <PovCameraOffset x="0.000000" y="-0.245000" z="0.660000" />
      <PovCameraVerticalAdjustmentForRollCage value="0.000000" />
      <PovPassengerCameraOffset x="0.000000" y="0.000000" z="0.020000" />
      <PovRearPassengerCameraOffset x="0.000000" y="0.000000" z="0.010000" />
      <vfxInfoName>VFXVEHICLEINFO_CAR_GENERIC</vfxInfoName>
      <shouldUseCinematicViewMode value="true" />
      <shouldCameraTransitionOnClimbUpDown value="false" />
      <shouldCameraIgnoreExiting value="false" />
      <AllowPretendOccupants value="true" />
      <AllowJoyriding value="true" />
      <AllowSundayDriving value="true" />
      <AllowBodyColorMapping value="true" />
      <wheelScale value="0.236000" />
      <wheelScaleRear value="0.236000" />
      <dirtLevelMin value="0.300000" />
      <dirtLevelMax value="0.850000" />
      <envEffScaleMin value="0.000000" />
      <envEffScaleMax value="1.000000" />
      <envEffScaleMin2 value="0.000000" />
      <envEffScaleMax2 value="1.000000" />
      <damageMapScale value="0.600000" />
      <damageOffsetScale value="1.000000" />
      <diffuseTint value="0x00FFFFFF" />
      <steerWheelMult value="1.000000" />
      <HDTextureDist value="5.000000" />
      <lodDistances content="float_array">
        10.000000
        25.000000
        50.000000
        100.000000
        500.000000
        500.000000
      </lodDistances>
      <minSeatHeight value="0.855" />
      <identicalModelSpawnDistance value="20" />
      <maxNumOfSameColor value="10" />
      <defaultBodyHealth value="1000.000000" />
      <pretendOccupantsScale value="1.000000" />
      <visibleSpawnDistScale value="1.000000" />
      <trackerPathWidth value="2.000000" />
      <weaponForceMult value="1.000000" />
      <frequency value="50" />
      <swankness>SWANKNESS_1</swankness>
      <maxNum value="50" />
      <flags>FLAG_POOR_CAR FLAG_HAS_INTERIOR_EXTRAS FLAG_EXTRAS_STRONG FLAG_RECESSED_HEADLIGHT_CORONAS
        FLAG_USE_INTERIOR_RED_LIGHT FLAG_LAW_ENFORCEMENT FLAG_EMERGENCY_SERVICE</flags>
      <type>VEHICLE_TYPE_CAR</type>
      <plateType>VPT_FRONT_AND_BACK_PLATES</plateType>
      <dashboardType>VDT_GENTAXI</dashboardType>
      <vehicleClass>VC_EMERGENCY</vehicleClass>
      <wheelType>VWT_MUSCLE</wheelType>
      <trailers />
      <additionalTrailers />
      <drivers/>
      <extraIncludes />
      <doorsWithCollisionWhenClosed />
      <driveableDoors />
      <bumpersNeedToCollideWithMap value="false" />
      <needsRopeTexture value="false" />
      <requiredExtras />
      <rewards />
      <cinematicPartCamera>
        <Item>WHEEL_FRONT_RIGHT_CAMERA</Item>
        <Item>WHEEL_FRONT_LEFT_CAMERA</Item>
        <Item>WHEEL_REAR_RIGHT_CAMERA</Item>
        <Item>WHEEL_REAR_LEFT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet />
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000" />
      <buoyancySphereSizeScale value="1.000000" />
      <pOverrideRagdollThreshold type="NULL" />
      <firstPersonDrivebyData>
        <Item>STD_POLIMPALER5_FRONT_LEFT</Item>
        <Item>STD_POLIMPALER5_FRONT_RIGHT</Item>
        <Item>STD_POLIMPALER5_REAR_LEFT</Item>
        <Item>STD_POLIMPALER5_REAR_RIGHT</Item>
      </firstPersonDrivebyData>
    </Item> 
	<Item>
      <modelName>polkuruma</modelName>
      <txdName>polkuruma</txdName>
      <handlingId>KURUMA</handlingId>
      <gameName>KURUMA</gameName>
      <vehicleMakeName>KARIN</vehicleMakeName>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected />
      <ptfxAssetName>null</ptfxAssetName>
      <audioNameHash>kuruma</audioNameHash>
      <layout>LAYOUT_STD_EXITFIXUP</layout>
      <coverBoundOffsets>SULTAN_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout />
      <cameraName>DEFAULT_FOLLOW_VEHICLE_CAMERA</cameraName>
      <aimCameraName>MID_BOX_VEHICLE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>VEHICLE_BONNET_CAMERA_NEAR</bonnetCameraName>
      <povCameraName>DEFAULT_POV_CAMERA</povCameraName>
      <FirstPersonDriveByIKOffset x="0.000000" y="-0.050000" z="-0.030000" />
      <FirstPersonDriveByUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonProjectileDriveByIKOffset x="0.033000" y="0.023000" z="-0.050000" />
      <FirstPersonProjectileDriveByPassengerIKOffset x="0.058000" y="0.048000" z="-0.055000" />
      <FirstPersonProjectileDriveByRearLeftIKOffset x="0.000000" y="0.020000" z="0.000000" />
      <FirstPersonProjectileDriveByRearRightIKOffset x="0.000000" y="0.020000" z="0.000000" />
      <FirstPersonDriveByLeftPassengerIKOffset x="0.000000" y="-0.010000" z="-0.010000" />
      <FirstPersonDriveByRightPassengerIKOffset x="0.000000" y="-0.050000" z="-0.030000" />
      <FirstPersonDriveByRightRearPassengerIKOffset x="0.000000" y="-0.010000" z="-0.010000" />
      <FirstPersonDriveByLeftPassengerUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonDriveByRightPassengerUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonMobilePhoneOffset x="0.138000" y="0.203000" z="0.551000" />
      <FirstPersonPassengerMobilePhoneOffset x="0.138000" y="0.123000" z="0.478000" />
      <PovCameraOffset x="0.000000" y="-0.190000" z="0.645000" />
      <PovCameraVerticalAdjustmentForRollCage value="0.000000" />
      <PovPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000" />
      <PovRearPassengerCameraOffset x="0.000000" y="-0.050000" z="0.035000" />
      <vfxInfoName>VFXVEHICLEINFO_CAR_GENERIC</vfxInfoName>
      <shouldUseCinematicViewMode value="true" />
      <shouldCameraTransitionOnClimbUpDown value="false" />
      <shouldCameraIgnoreExiting value="false" />
      <AllowPretendOccupants value="true" />
      <AllowJoyriding value="true" />
      <AllowSundayDriving value="true" />
      <AllowBodyColorMapping value="true" />
      <wheelScale value="0.274500" />
      <wheelScaleRear value="0.274500" />
      <dirtLevelMin value="0.000000" />
      <dirtLevelMax value="0.300000" />
      <envEffScaleMin value="0.000000" />
      <envEffScaleMax value="1.000000" />
      <envEffScaleMin2 value="0.000000" />
      <envEffScaleMax2 value="1.000000" />
      <damageMapScale value="0.600000" />
      <damageOffsetScale value="1.000000" />
      <diffuseTint value="0x00FFFFFF" />
      <steerWheelMult value="0.700000" />
      <HDTextureDist value="5.000000" />
      <lodDistances content="float_array">
        10.000000
        25.000000
        50.000000
        100.000000
        500.000000
        500.000000
      </lodDistances>
      <identicalModelSpawnDistance value="20" />
      <maxNumOfSameColor value="10" />
      <defaultBodyHealth value="1000.000000" />
      <pretendOccupantsScale value="1.000000" />
      <visibleSpawnDistScale value="1.000000" />
      <trackerPathWidth value="2.000000" />
      <weaponForceMult value="1.000000" />
      <frequency value="50" />
      <swankness>SWANKNESS_2</swankness>
      <maxNum value="50" />
      <flags>FLAG_AVERAGE_CAR FLAG_POOR_CAR FLAG_RECESSED_HEADLIGHT_CORONAS
        FLAG_USE_INTERIOR_RED_LIGHT FLAG_LAW_ENFORCEMENT FLAG_EMERGENCY_SERVICE</flags>
      <type>VEHICLE_TYPE_CAR</type>
      <plateType>VPT_BACK_PLATES</plateType>
      <dashboardType>VDT_SULTAN</dashboardType>
      <vehicleClass>VC_SPORT</vehicleClass>
      <wheelType>VWT_SPORT</wheelType>
      <trailers />
      <additionalTrailers />
      <drivers />
      <extraIncludes />
      <doorsWithCollisionWhenClosed />
      <driveableDoors />
      <bumpersNeedToCollideWithMap value="false" />
      <needsRopeTexture value="false" />
      <requiredExtras />
      <rewards />
      <cinematicPartCamera>
        <Item>WHEEL_FRONT_RIGHT_CAMERA</Item>
        <Item>WHEEL_FRONT_LEFT_CAMERA</Item>
        <Item>WHEEL_REAR_RIGHT_CAMERA</Item>
        <Item>WHEEL_REAR_LEFT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet />
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000" />
      <buoyancySphereSizeScale value="1.000000" />
      <pOverrideRagdollThreshold type="NULL" />
      <firstPersonDrivebyData>
        <Item>STD_KURUMA_FRONT_LEFT</Item>
        <Item>STD_KURUMA_FRONT_RIGHT</Item>
        <Item>STD_ASEA_REAR_LEFT</Item>
        <Item>STD_ASEA_REAR_RIGHT</Item>
      </firstPersonDrivebyData>
    </Item>
	<Item>
      <modelName>polniobe</modelName>
      <txdName>polniobe</txdName>
      <handlingId>NIOBE</handlingId>
      <gameName>NIOBE</gameName>
      <vehicleMakeName>UBERMACHT</vehicleMakeName>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected />
      <ptfxAssetName>null</ptfxAssetName>
      <audioNameHash>imorgon</audioNameHash>
      <layout>LAYOUT_LOW_INFERNUS</layout>
      <coverBoundOffsets>NIOBE_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout />
      <cameraName>FOLLOW_CHEETAH_CAMERA</cameraName>
      <aimCameraName>BOX_VEHICLE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>VEHICLE_BONNET_CAMERA_STANDARD_LONG_EXTRA_LOW</bonnetCameraName>
      <povCameraName>REDUCED_NEAR_CLIP_POV_CAMERA</povCameraName>
      <FirstPersonDriveByIKOffset x="0.050000" y="-0.100000" z="-0.050000" />
      <FirstPersonDriveByUnarmedIKOffset x="0.000000" y="-0.020000" z="-0.010000" />
      <FirstPersonProjectileDriveByIKOffset x="0.080000" y="-0.110000" z="-0.070000" />
      <FirstPersonProjectileDriveByPassengerIKOffset x="-0.050000" y="-0.140000" z="-0.050000" />
      <FirstPersonDriveByRightPassengerIKOffset x="-0.050000" y="-0.090000" z="-0.050000" />
      <FirstPersonDriveByRightPassengerUnarmedIKOffset x="0.000000" y="-0.040000" z="-0.020000" />
      <FirstPersonMobilePhoneOffset x="0.125000" y="0.160000" z="0.550000" />
      <FirstPersonPassengerMobilePhoneOffset x="0.223000" y="0.160000" z="0.415000" />
      <FirstPersonMobilePhoneSeatIKOffset /> 
      <PovCameraOffset x="0.000000" y="-0.215000" z="0.6650000" />
      <PovCameraVerticalAdjustmentForRollCage value="-0.025000" />
      <PovPassengerCameraOffset x="0.000000" y="0.060000" z="0.00000" />
      <PovRearPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000" />
      <vfxInfoName>VFXVEHICLEINFO_CAR_GENERIC</vfxInfoName>
      <shouldUseCinematicViewMode value="true" />
      <shouldCameraTransitionOnClimbUpDown value="false" />
      <shouldCameraIgnoreExiting value="false" />
      <AllowPretendOccupants value="true" />
      <AllowJoyriding value="false" />
      <AllowSundayDriving value="false" />
      <AllowBodyColorMapping value="true" />
      <wheelScale value="0.267000" /> 
      <wheelScaleRear value="0.267000" />
      <dirtLevelMin value="0.000000" />
      <dirtLevelMax value="1.000000" />
      <envEffScaleMin value="0.300000" />
      <envEffScaleMax value="0.400000" />
      <envEffScaleMin2 value="0.000000" />
      <envEffScaleMax2 value="1.000000" />
      <damageMapScale value="0.400000" />
      <damageOffsetScale value="0.400000" />
      <diffuseTint value="0x00FFFFFF" />
      <steerWheelMult value="0.750000" />
      <HDTextureDist value="5.000000" />
      <lodDistances content="float_array">
        15.000000
        25.000000
        70.000000
        140.000000
        500.000000
        500.000000
      </lodDistances>
      <minSeatHeight value="0.988" />
      <identicalModelSpawnDistance value="20" />
      <maxNumOfSameColor value="10" />
      <defaultBodyHealth value="1000.000000" />
      <pretendOccupantsScale value="1.000000" />
      <visibleSpawnDistScale value="1.000000" />
      <trackerPathWidth value="2.000000" />
      <weaponForceMult value="1.000000" />
      <frequency value="5" />
      <swankness>SWANKNESS_5</swankness>
      <maxNum value="1" />
      <flags>FLAG_RICH_CAR FLAG_NO_BROKEN_DOWN_SCENARIO FLAG_CAN_HAVE_NEONS FLAG_RECESSED_HEADLIGHT_CORONAS
        FLAG_USE_INTERIOR_RED_LIGHT FLAG_LAW_ENFORCEMENT FLAG_EMERGENCY_SERVICE</flags>
      <type>VEHICLE_TYPE_CAR</type>
      <plateType>VPT_BACK_PLATES</plateType>
      <dashboardType>VDT_RACE</dashboardType>
      <vehicleClass>VC_SPORT</vehicleClass>
      <wheelType>VWT_HIEND</wheelType>
      <trailers />
      <additionalTrailers />
      <drivers />
      <extraIncludes />
      <doorsWithCollisionWhenClosed>
        <Item>VEH_EXT_BOOT</Item>
      </doorsWithCollisionWhenClosed>
      <driveableDoors />
      <bumpersNeedToCollideWithMap value="false" />
      <needsRopeTexture value="true" />
      <requiredExtras />
      <rewards />
      <cinematicPartCamera>
        <Item>WHEEL_WIDE_REAR_RIGHT_CAMERA</Item>
        <Item>WHEEL_WIDE_REAR_LEFT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet>Truck</NmBraceOverrideSet>
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000" />
      <buoyancySphereSizeScale value="1.000000" />
      <pOverrideRagdollThreshold type="NULL" />
      <firstPersonDrivebyData>
        <Item>STD_NIOBE_FRONT_LEFT</Item>
        <Item>STD_NIOBE_FRONT_RIGHT</Item>
      </firstPersonDrivebyData>
      <numSeatsOverride value="2" />
    </Item>
	<Item>
      <modelName>polstan</modelName>
      <txdName>polstan</txdName>
      <handlingId>polstan</handlingId>
      <gameName>STANIER</gameName>
      <vehicleMakeName>VAPID</vehicleMakeName>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected />
      <ptfxAssetName>null</ptfxAssetName>
      <audioNameHash>STANIER</audioNameHash>
      <layout>LAYOUT_STANDARD</layout>
      <coverBoundOffsets>TAXI_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout />
      <cameraName>DEFAULT_FOLLOW_VEHICLE_CAMERA</cameraName>
      <aimCameraName>DEFAULT_THIRD_PERSON_VEHICLE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>VEHICLE_BONNET_CAMERA_MID</bonnetCameraName>
      <povCameraName>DEFAULT_POV_CAMERA</povCameraName>
      <FirstPersonDriveByIKOffset x="0.000000" y="-0.060000" z="-0.040000" />
      <FirstPersonDriveByUnarmedIKOffset x="0.000000" y="-0.025000" z="0.000000" />
      <FirstPersonProjectileDriveByIKOffset x="0.030000" y="-0.080000" z="-0.020000" />
      <FirstPersonProjectileDriveByPassengerIKOffset x="0.030000" y="-0.080000" z="-0.020000" />
      <FirstPersonProjectileDriveByRearLeftIKOffset x="0.000000" y="0.020000" z="0.010000" />
      <FirstPersonProjectileDriveByRearRightIKOffset x="0.000000" y="0.020000" z="0.010000" />
      <FirstPersonDriveByLeftPassengerIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonDriveByRightPassengerIKOffset x="0.000000" y="-0.060000" z="-0.040000" />
      <FirstPersonDriveByRightRearPassengerIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonDriveByLeftPassengerUnarmedIKOffset x="0.000000" y="-0.000000" z="0.000000" />
      <FirstPersonDriveByRightPassengerUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonMobilePhoneOffset x="0.150000" y="0.278000" z="0.510000" />
      <FirstPersonPassengerMobilePhoneOffset x="0.135000" y="0.175000" z="0.445000" />
      <FirstPersonMobilePhoneSeatIKOffset>
        <Item>
            <Offset x="0.136000" y="0.126000" z="0.455000" />
            <SeatIndex value="2" />
        </Item>
        <Item>
            <Offset x="0.136000" y="0.126000" z="0.455000" />
            <SeatIndex value="3" />
        </Item>
      </FirstPersonMobilePhoneSeatIKOffset> 
      <PovCameraOffset x="0.000000" y="-0.145000" z="0.650000" />
      <PovCameraVerticalAdjustmentForRollCage value="0.000000" />
      <PovPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000" />
      <PovRearPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000" />
      <vfxInfoName>VFXVEHICLEINFO_CAR_GENERIC</vfxInfoName>
      <shouldUseCinematicViewMode value="true" />
      <shouldCameraTransitionOnClimbUpDown value="false" />
      <shouldCameraIgnoreExiting value="false" />
      <AllowPretendOccupants value="true" />
      <AllowJoyriding value="true" />
      <AllowSundayDriving value="true" />
      <AllowBodyColorMapping value="true" />
      <wheelScale value="0.234500" />
      <wheelScaleRear value="0.234500" />
      <dirtLevelMin value="0.000000" />
      <dirtLevelMax value="0.850000" />
      <envEffScaleMin value="0.000000" />
      <envEffScaleMax value="1.000000" />
      <envEffScaleMin2 value="0.000000" />
      <envEffScaleMax2 value="1.000000" />
      <damageMapScale value="0.600000" />
      <damageOffsetScale value="1.000000" />
      <diffuseTint value="0x00FFFFFF" />
      <steerWheelMult value="1.000000" />
      <HDTextureDist value="5.000000" />
      <lodDistances content="float_array">
        15.000000
        30.000000
        50.000000
        100.000000
        500.000000
        500.000000
      </lodDistances>
      <minSeatHeight value="0.839" />
      <identicalModelSpawnDistance value="20" />
      <maxNumOfSameColor value="10" />
      <defaultBodyHealth value="1000.000000" />
      <pretendOccupantsScale value="1.000000" />
      <visibleSpawnDistScale value="1.000000" />
      <trackerPathWidth value="2.000000" />
      <weaponForceMult value="1.000000" />
      <frequency value="50" />
      <swankness>SWANKNESS_1</swankness>
      <maxNum value="50" />
      <flags>FLAG_AVERAGE_CAR FLAG_POOR_CAR FLAG_HAS_INTERIOR_EXTRAS</flags>
      <type>VEHICLE_TYPE_CAR</type>
      <plateType>VPT_FRONT_AND_BACK_PLATES</plateType>
      <dashboardType>VDT_GENTAXI</dashboardType>
      <vehicleClass>VC_SEDAN</vehicleClass>
      <wheelType>VWT_SPORT</wheelType>
      <trailers />
      <additionalTrailers />
      <drivers />
      <extraIncludes />
      <doorsWithCollisionWhenClosed />
      <driveableDoors />
      <bumpersNeedToCollideWithMap value="true" />
      <needsRopeTexture value="false" />
      <requiredExtras />
      <rewards />
      <cinematicPartCamera>
        <Item>WHEEL_FRONT_RIGHT_CAMERA</Item>
        <Item>WHEEL_FRONT_LEFT_CAMERA</Item>
        <Item>WHEEL_REAR_RIGHT_CAMERA</Item>
        <Item>WHEEL_REAR_LEFT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet />
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000" />
      <buoyancySphereSizeScale value="1.000000" />
      <pOverrideRagdollThreshold type="NULL" />
      <firstPersonDrivebyData>
        <Item>STD_TAXI_FRONT_LEFT</Item>
        <Item>STD_TAXI_FRONT_RIGHT</Item>
        <Item>STD_TAXI_REAR_LEFT</Item>
        <Item>STD_TAXI_REAR_RIGHT</Item>
      </firstPersonDrivebyData>
    </Item>
  </InitDatas>
  <txdRelationships>
	<Item>
      <parent>vehicles_itali_w_interior</parent>
      <child>poltenf</child>
    </Item>
	<Item>
      <parent>vehicles_cav_w2_interior</parent>
      <child>polballer7</child>
    </Item>
	<Item>
      <parent>vehicles_gendials</parent>
      <child>cavalcade3</child>
    </Item> 
	<Item>
      <parent>vehicles_banshee_interior</parent>
      <child>polcomet5</child>
    </Item>
	<Item>
      <parent>vehicles_gendials</parent>
      <child>polgranger2</child>
    </Item>
	<Item>
      <parent>vehicles_poltax_interior</parent>
      <child>polimpaler7</child>
    </Item>
	<Item>
      <parent>vehicles_sultan_interior</parent>
      <child>polkuruma</child>
    </Item>
    <Item>
      <parent>vehicles_sm277_interior</parent>
      <child>polniobe</child>
    </Item>
	<Item>
      <parent>vehicles_poltax_interior</parent>
      <child>polstan</child>
    </Item>
  </txdRelationships>
</CVehicleModelInfo__InitDataList>
