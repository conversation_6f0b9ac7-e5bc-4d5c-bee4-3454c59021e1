<?xml version="1.0" encoding="UTF-8"?>

<CVehicleMetadataMgr>
  <AnimRateSets />
  <ClipSetMaps />
  <VehicleCoverBoundOffsetInfos>
    <Item type="CVehicleCoverBoundOffsetInfo">
      <Name>DOMINATOR10_COVER_OFFSET_INFO</Name>
      <ExtraSideOffset value="0.005000" />
      <ExtraForwardOffset value="-0.140000" />
      <ExtraBackwardOffset value="-0.120000" />
      <ExtraZOffset value="0.000000" />
      <CoverBoundInfos />
    </Item>
    <Item type="CVehicleCoverBoundOffsetInfo">
      <Name>COQUETTE5_COVER_OFFSET_INFO</Name>
      <ExtraSideOffset value="-0.080000" />
      <ExtraForwardOffset value="0.085000" />
      <ExtraBackwardOffset value="-0.250000" />
      <ExtraZOffset value="0.050000" />
      <CoverBoundInfos />
    </Item>
    <Item type="CVehicleCoverBoundOffsetInfo">
      <Name>ENVISAGE_COVER_OFFSET_INFO</Name>
      <ExtraSideOffset value="0.005000" />
      <ExtraForwardOffset value="0.000000" />
      <ExtraBackwardOffset value="-0.100000" />
      <ExtraZOffset value="-0.050000" />
      <CoverBoundInfos />
    </Item>
    <Item type="CVehicleCoverBoundOffsetInfo">
      <Name>EUROSX32_COVER_OFFSET_INFO</Name>
      <ExtraSideOffset value="0.050000" />
      <ExtraForwardOffset value="-0.100000" />
      <ExtraBackwardOffset value="-0.120000" />
      <ExtraZOffset value="-0.400000" />
      <CoverBoundInfos />
    </Item>
    <Item type="CVehicleCoverBoundOffsetInfo">
      <Name>NIOBE_COVER_OFFSET_INFO</Name>
      <ExtraSideOffset value="0.060000" />
      <ExtraForwardOffset value="-0.140000" />
      <ExtraBackwardOffset value="-0.110000" />
      <ExtraZOffset value="-0.250000" />
      <CoverBoundInfos />
    </Item>
    <Item type="CVehicleCoverBoundOffsetInfo">
      <Name>PIPISTRELLO_COVER_OFFSET_INFO</Name>
      <ExtraSideOffset value="-0.030000" />
      <ExtraForwardOffset value="-0.170000" />
      <ExtraBackwardOffset value="-0.080000" />
      <ExtraZOffset value="-0.100000" />
      <CoverBoundInfos />
    </Item>
    <Item type="CVehicleCoverBoundOffsetInfo">
      <Name>POLGREENWOOD_COVER_OFFSET_INFO</Name>
      <ExtraSideOffset value="-0.030000" />
      <ExtraForwardOffset value="0.070000" />
      <ExtraBackwardOffset value="-0.300000" />
      <ExtraZOffset value="-0.050000" />
      <CoverBoundInfos />
    </Item>
    <Item type="CVehicleCoverBoundOffsetInfo">
      <Name>POLICET3_COVER_OFFSET_INFO</Name>
      <ExtraSideOffset value="0.100000" />
      <ExtraForwardOffset value="-0.200000" />
      <ExtraBackwardOffset value="-0.250000" />
      <ExtraZOffset value="1.000000" />
      <CoverBoundInfos>
        <Item>
          <Name>Front</Name>
          <Position x="0.000000" y="2.000000" z="-0.200000" />
          <Length value="1.300000" />
          <Width value="2.250000" />
          <Height value="0.850000" />
          <ActiveBoundExclusions />
        </Item>
        <Item>
          <Name>REAR</Name>
          <Position x="0.000000" y="-0.780000" z="0.080000" />
          <Length value="4.250000" />
          <Width value="2.250000" />
          <Height value="2.050000" />
          <ActiveBoundExclusions />
        </Item>
      </CoverBoundInfos>
    </Item>
    <Item type="CVehicleCoverBoundOffsetInfo">
      <Name>VORSCH_COVER_OFFSET_INFO</Name>
      <ExtraSideOffset value="0.005000" />
      <ExtraForwardOffset value="0.000000" />
      <ExtraBackwardOffset value="-0.300000" />
      <ExtraZOffset value="-0.050000" />
      <CoverBoundInfos />
    </Item>
    <Item type="CVehicleCoverBoundOffsetInfo">
      <Name>YOSEMITE1500_COVER_OFFSET_INFO</Name>
      <ExtraSideOffset value="0.130000" />
      <ExtraForwardOffset value="0.000000" />
      <ExtraBackwardOffset value="-0.220000" />
      <ExtraZOffset value="-0.100000" />
      <CoverBoundInfos />
    </Item>
  </VehicleCoverBoundOffsetInfos>
  <BicycleInfos />
  <POVTuningInfos />
  <EntryAnimVariations />
  <VehicleExtraPointsInfos />
  <DrivebyWeaponGroups />
  <VehicleDriveByAnimInfos>
    <Item type="CVehicleDriveByAnimInfo">
      <Name>STD_POLIMPALER6_ANIM_INFO_ONE_HANDED_PS</Name>
      <WeaponGroup ref="DRIVEBY_DEFAULT_ONE_HANDED" />
      <DriveByClipSet>clipset@veh@driveby@greenwood@ps</DriveByClipSet>
      <FirstPersonDriveByClipSet>driveby@first_person@passenger_left_handed@1h</FirstPersonDriveByClipSet>
      <AltFirstPersonDriveByClipSets>
        <Item>
          <Weapons>
            <Item>WEAPON_MICROSMG</Item>
          </Weapons>
          <ClipSet>driveby@first_person@passenger_left_handed@smg</ClipSet>
        </Item>
      </AltFirstPersonDriveByClipSets>
      <RestrictedDriveByClipSet />
      <VehicleMeleeClipSet />
      <FirstPersonVehicleMeleeClipSet />
      <Network>STD_CAR_DRIVEBY</Network>
      <UseOverrideAngles value="false" />
      <OverrideAnglesInThirdPersonOnly value="false" />
      <OverrideMinAimAngle value="0.000000" />
      <OverrideMaxAimAngle value="0.000000" />
      <OverrideMinRestrictedAimAngle value="0.000000" />
      <OverrideMaxRestrictedAimAngle value="0.000000" />
    </Item>
    <Item type="CVehicleDriveByAnimInfo">
      <Name>LOW_DB_COQUETTE5_ANIM_INFO_THROW_DS</Name>
      <WeaponGroup ref="DRIVEBY_THROW" />
      <DriveByClipSet>drive_by@low_ds_grenades</DriveByClipSet>
      <FirstPersonDriveByClipSet>driveby@first_person@driver@throw</FirstPersonDriveByClipSet>
      <AltFirstPersonDriveByClipSets />
      <RestrictedDriveByClipSet />
      <VehicleMeleeClipSet />
      <FirstPersonVehicleMeleeClipSet />
      <Network>MOUNTED_THROW</Network>
      <UseOverrideAngles value="true" />
      <OverrideAnglesInThirdPersonOnly value="false" />
      <OverrideMinAimAngle value="65.000000" />
      <OverrideMaxAimAngle value="-130.000000" />
      <OverrideMinRestrictedAimAngle value="0.000000" />
      <OverrideMaxRestrictedAimAngle value="0.000000" />
    </Item>
  </VehicleDriveByAnimInfos>
  <VehicleDriveByInfos>
    <Item type="CVehicleDriveByInfo">
      <Name>DRIVEBY_STD_POLIMPALER6_FRONT_RIGHT</Name>
      <MinAimSweepHeadingAngleDegs value="-225.000000" />
      <MaxAimSweepHeadingAngleDegs value="225.000000" />
      <FirstPersonMinAimSweepHeadingAngleDegs value="-180.000000" />
      <FirstPersonMaxAimSweepHeadingAngleDegs value="180.000000" />
      <FirstPersonUnarmedMinAimSweepHeadingAngleDegs value="-180.000000" />
      <FirstPersonUnarmedMaxAimSweepHeadingAngleDegs value="180.000000" />
      <MinRestrictedAimSweepHeadingAngleDegs value="-225.000000" />
      <MaxRestrictedAimSweepHeadingAngleDegs value="225.000000" />
      <MinSmashWindowAngleDegs value="17.500000" />
      <MaxSmashWindowAngleDegs value="180.000000" />
      <MinSmashWindowAngleFirstPersonDegs value="35.000000" />
      <MaxSmashWindowAngleFirstPersonDegs value="180.000000" />
      <MaxSpeedParam value="0.500000" />
      <MaxLongitudinalLeanBlendWeightDelta value="0.250000" />
      <MaxLateralLeanBlendWeightDelta value="0.250000" />
      <ApproachSpeedToWithinMaxBlendDelta value="1.000000" />
      <SpineAdditiveBlendInDelay value="0.050000" />
      <SpineAdditiveBlendInDurationStill value="0.150000" />
      <SpineAdditiveBlendInDuration value="0.750000" />
      <SpineAdditiveBlendOutDelay value="0.000000" />
      <SpineAdditiveBlendOutDuration value="0.500000" />
      <MinUnarmedDrivebyYawIfWindowRolledUp value="-3.140000" />
      <MaxUnarmedDrivebyYawIfWindowRolledUp value="0.925000" />
      <DriveByAnimInfos>
        <Item ref="STD_DB_ANIM_INFO_UNARMED_PS" />
        <Item ref="STD_POLIMPALER6_ANIM_INFO_ONE_HANDED_PS" />
        <Item ref="STD_DB_ANIM_INFO_THROW_PS" />
      </DriveByAnimInfos>
      <DriveByCamera />
      <PovDriveByCamera />
      <DriveByFlags>UseMountedProjectileTask LeftHandedFirstPersonAnims
        LeftHandedUnarmedFirstPersonAnims</DriveByFlags>
    </Item>
    <Item type="CVehicleDriveByInfo">
      <Name>DRIVEBY_LOW_COQUETTE5_FRONT_LEFT</Name>
      <MinAimSweepHeadingAngleDegs value="-190.000000" />
      <MaxAimSweepHeadingAngleDegs value="190.000000" />
      <FirstPersonMinAimSweepHeadingAngleDegs value="-180.000000" />
      <FirstPersonMaxAimSweepHeadingAngleDegs value="180.000000" />
      <FirstPersonUnarmedMinAimSweepHeadingAngleDegs value="-180.000000" />
      <FirstPersonUnarmedMaxAimSweepHeadingAngleDegs value="180.000000" />
      <MinRestrictedAimSweepHeadingAngleDegs value="-190.000000" />
      <MaxRestrictedAimSweepHeadingAngleDegs value="190.000000" />
      <MinSmashWindowAngleDegs value="-180.000000" />
      <MaxSmashWindowAngleDegs value="-17.500000" />
      <MinSmashWindowAngleFirstPersonDegs value="-180.000000" />
      <MaxSmashWindowAngleFirstPersonDegs value="-30.000000" />
      <MaxSpeedParam value="0.500000" />
      <MaxLongitudinalLeanBlendWeightDelta value="0.250000" />
      <MaxLateralLeanBlendWeightDelta value="0.250000" />
      <ApproachSpeedToWithinMaxBlendDelta value="1.000000" />
      <SpineAdditiveBlendInDelay value="0.050000" />
      <SpineAdditiveBlendInDurationStill value="0.150000" />
      <SpineAdditiveBlendInDuration value="0.750000" />
      <SpineAdditiveBlendOutDelay value="0.000000" />
      <SpineAdditiveBlendOutDuration value="0.500000" />
      <MinUnarmedDrivebyYawIfWindowRolledUp value="-0.800000" />
      <MaxUnarmedDrivebyYawIfWindowRolledUp value="3.140000" />
      <DriveByAnimInfos>
        <Item ref="LOW_DB_ANIM_INFO_UNARMED_DS" />
        <Item ref="ZTYPE_DB_ANIM_INFO_ONE_HANDED_DS" />
        <Item ref="LOW_DB_COQUETTE5_ANIM_INFO_THROW_DS" />
      </DriveByAnimInfos>
      <DriveByCamera />
      <PovDriveByCamera />
      <DriveByFlags>UseMountedProjectileTask LeftHandedProjctiles LeftHandedUnarmedFirstPersonAnims</DriveByFlags>
    </Item>
  </VehicleDriveByInfos>
  <VehicleSeatInfos>
    <Item type="CVehicleSeatInfo">
      <Name>SEAT_POLICET3_EXTRA_LEFT_1</Name>
      <SeatBoneName>seat_dside_r1</SeatBoneName>
      <ShuffleLink />
      <RearSeatLink />
      <DefaultCarTask>TASK_DRIVE_WANDER</DefaultCarTask>
      <SeatFlags>KeepOnHeadProp PrioritizeRearSeatEntry</SeatFlags>
      <HairScale value="0.000000" />
      <ShuffleLink2 />
    </Item>
    <Item type="CVehicleSeatInfo">
      <Name>SEAT_POLICET3_EXTRA_RIGHT_1</Name>
      <SeatBoneName>seat_pside_r1</SeatBoneName>
      <ShuffleLink />
      <RearSeatLink />
      <DefaultCarTask>TASK_DRIVE_WANDER</DefaultCarTask>
      <SeatFlags>KeepOnHeadProp PrioritizeRearSeatEntry</SeatFlags>
      <HairScale value="0.000000" />
      <ShuffleLink2 />
    </Item>
  </VehicleSeatInfos>
  <VehicleSeatAnimInfos>
    <Item type="CVehicleSeatAnimInfo">
      <Name>SEAT_ANIM_STD_POLIMPALER6_FRONT_RIGHT</Name>
      <DriveByInfo ref="DRIVEBY_STD_POLIMPALER6_FRONT_RIGHT" />
      <InsideClipSetMap ref="INSIDE_CLIPSET_MAP_STD_FRONT_RIGHT" />
      <PanicClipSet>clipset@veh@std@ps@idle_panic</PanicClipSet>
      <AgitatedClipSet />
      <DuckedClipSet>clipset@veh@std@ps@idle_duck</DuckedClipSet>
      <FemaleClipSet />
      <LowriderLeanClipSet />
      <AltLowriderLeanClipSet />
      <LowLODIdleAnim>STD_CAR_FRONT_PS_IDLE</LowLODIdleAnim>
      <SeatAmbientContext />
      <InVehicleMoveNetwork>VEHICLE_DEFAULT</InVehicleMoveNetwork>
      <SeatAnimFlags>UseStandardInVehicleAnims HasPanicAnims UseCloseDoorBlendAnims
        PreventShuffleJack</SeatAnimFlags>
      <SteeringSmoothing value="0.100000" />
      <ExitToAimInfoName>MESA</ExitToAimInfoName>
      <MaleGestureClipSetId>ANIM_GROUP_GESTURE_M_CAR_STD_PS</MaleGestureClipSetId>
      <FemaleGestureClipSetId>ANIM_GROUP_GESTURE_F_CAR_STD_PS</FemaleGestureClipSetId>
      <FPSMinSteeringRateOverride value="-1.000000" />
      <FPSMaxSteeringRateOverride value="-1.000000" />
      <SeatCollisionBoundsOffset x="0.000000" y="0.000000" z="0.000000" />
    </Item>
    <Item type="CVehicleSeatAnimInfo">
      <Name>SEAT_ANIM_LOW_COQUETTE5_FRONT_LEFT</Name>
      <DriveByInfo ref="DRIVEBY_LOW_COQUETTE5_FRONT_LEFT" />
      <InsideClipSetMap ref="INSIDE_CLIPSET_MAP_LOW_FRONT_LEFT" />
      <PanicClipSet>clipset@veh@low@ds@idle_panic</PanicClipSet>
      <AgitatedClipSet>clipset@veh@low@ds@idle_agitated</AgitatedClipSet>
      <DuckedClipSet>clipset@veh@low@ds@idle_duck</DuckedClipSet>
      <FemaleClipSet />
      <LowriderLeanClipSet />
      <AltLowriderLeanClipSet />
      <LowLODIdleAnim>LOW_CAR_FRONT_DS_IDLE</LowLODIdleAnim>
      <SeatAmbientContext>IN_CAR_LOW</SeatAmbientContext>
      <InVehicleMoveNetwork>VEHICLE_DEFAULT</InVehicleMoveNetwork>
      <SeatAnimFlags>UseStandardInVehicleAnims HasPanicAnims UseCloseDoorBlendAnims</SeatAnimFlags>
      <SteeringSmoothing value="0.100000" />
      <ExitToAimInfoName>LOW</ExitToAimInfoName>
      <MaleGestureClipSetId>ANIM_GROUP_GESTURE_M_CAR_LOW_DS</MaleGestureClipSetId>
      <FemaleGestureClipSetId>ANIM_GROUP_GESTURE_F_CAR_LOW_DS</FemaleGestureClipSetId>
      <FPSMinSteeringRateOverride value="-1.000000" />
      <FPSMaxSteeringRateOverride value="-1.000000" />
      <SeatCollisionBoundsOffset x="0.000000" y="0.000000" z="0.000000" />
    </Item>
  </VehicleSeatAnimInfos>
  <VehicleEntryPointInfos>
    <Item type="CVehicleEntryPointInfo">
      <Name>ENTRY_POINT_POLICET3_FRONT_LEFT</Name>
      <DoorBoneName>door_dside_f</DoorBoneName>
      <SecondDoorBoneName />
      <DoorHandleBoneName>handle_dside_f</DoorHandleBoneName>
      <WindowId>FRONT_LEFT</WindowId>
      <VehicleSide>SIDE_LEFT</VehicleSide>
      <AccessableSeats>
        <Item ref="SEAT_VAN_FRONT_LEFT" />
      </AccessableSeats>
      <VehicleExtraPointsInfo ref="EXTRA_VEHICLE_POINTS_STD_LEFT" />
      <Flags />
      <BlockJackReactionSides />
      <BreakoutTestPoint>POINT_TYPE_MAX</BreakoutTestPoint>
    </Item>
    <Item type="CVehicleEntryPointInfo">
      <Name>ENTRY_POINT_POLICET3_FRONT_RIGHT</Name>
      <DoorBoneName>door_pside_f</DoorBoneName>
      <SecondDoorBoneName />
      <DoorHandleBoneName>handle_pside_f</DoorHandleBoneName>
      <WindowId>FRONT_RIGHT</WindowId>
      <VehicleSide>SIDE_RIGHT</VehicleSide>
      <AccessableSeats>
        <Item ref="SEAT_VAN_FRONT_RIGHT" />
      </AccessableSeats>
      <VehicleExtraPointsInfo ref="EXTRA_VEHICLE_POINTS_STD_RIGHT" />
      <Flags>BlockJackReactionUntilJackerIsReady</Flags>
      <BlockJackReactionSides />
      <BreakoutTestPoint>POINT_TYPE_MAX</BreakoutTestPoint>
    </Item>
    <Item type="CVehicleEntryPointInfo">
      <Name>ENTRY_POINT_POLICET3_SIDE_LEFT_1</Name>
      <DoorBoneName />
      <SecondDoorBoneName />
      <DoorHandleBoneName />
      <WindowId>INVALID</WindowId>
      <VehicleSide>SIDE_LEFT</VehicleSide>
      <AccessableSeats>
        <Item ref="SEAT_POLICET3_EXTRA_LEFT_1" />
      </AccessableSeats>
      <VehicleExtraPointsInfo ref="NULL" />
      <Flags />
      <BlockJackReactionSides />
      <BreakoutTestPoint>POINT_TYPE_MAX</BreakoutTestPoint>
    </Item>
    <Item type="CVehicleEntryPointInfo">
      <Name>ENTRY_POINT_POLICET3_SIDE_RIGHT_1</Name>
      <DoorBoneName />
      <SecondDoorBoneName />
      <DoorHandleBoneName />
      <WindowId>INVALID</WindowId>
      <VehicleSide>SIDE_RIGHT</VehicleSide>
      <AccessableSeats>
        <Item ref="SEAT_POLICET3_EXTRA_RIGHT_1" />
      </AccessableSeats>
      <VehicleExtraPointsInfo ref="NULL" />
      <Flags />
      <BlockJackReactionSides />
      <BreakoutTestPoint>POINT_TYPE_MAX</BreakoutTestPoint>
    </Item>
  </VehicleEntryPointInfos>
  <VehicleEntryPointAnimInfos>
    <Item type="CVehicleEntryPointAnimInfo">
      <Name>ENTRY_POINT_ANIM_STD_CASTIGATOR_FRONT_LEFT</Name>
      <CommonClipSetMap ref="COMMON_CLIPSET_MAP_AUTOMOBILE_LEFT" />
      <EntryClipSetMap ref="ENTRY_CLIPSET_MAP_STD_FRONT_LEFT" />
      <ExitClipSetMap ref="EXIT_CLIPSET_MAP_STD_FRONT_LEFT" />
      <AlternateTryLockedDoorClipId>STD_Locked_DS</AlternateTryLockedDoorClipId>
      <AlternateForcedEntryClipId>STD_Force_Entry_DS</AlternateForcedEntryClipId>
      <AlternateJackFromOutSideClipId>std_perp_ds</AlternateJackFromOutSideClipId>
      <AlternateBeJackedFromOutSideClipId>std_victim_ds</AlternateBeJackedFromOutSideClipId>
      <AlternateEntryPointClipSetId>clipset@veh@std@ds@female@enter_exit</AlternateEntryPointClipSetId>
      <EnterVehicleMoveNetwork>ENTER_VEHICLE_STD</EnterVehicleMoveNetwork>
      <EntryTranslation x="-0.842000" y="-0.650000" z="0.500000" />
      <OpenDoorTranslation x="-0.050000" y="0.150000" />
      <OpenDoorHeadingChange value="1.570000" />
      <EntryHeadingChange value="-1.570000" />
      <ExtraZForMPPlaneWarp value="-2.000000" />
      <EntryPointFlags>JackIncludesGetIn UsesNoDoorTransitionForExit UseOpenDoorBlendAnims
        UseGetUpAfterJack JackVariationsIncludeGetIn HasCombatEntry</EntryPointFlags>
      <EntryAnimVariations ref="ENTRY_ANIM_VARIATIONS_STANDARD_FRONT_LEFT" />
      <NMJumpFromVehicleTuningSet>STANDARD</NMJumpFromVehicleTuningSet>
    </Item>
    <Item type="CVehicleEntryPointAnimInfo">
      <Name>ENTRY_POINT_ANIM_STD_CASTIGATOR_FRONT_RIGHT</Name>
      <CommonClipSetMap ref="COMMON_CLIPSET_MAP_AUTOMOBILE_RIGHT" />
      <EntryClipSetMap ref="ENTRY_CLIPSET_MAP_STD_FRONT_RIGHT" />
      <ExitClipSetMap ref="EXIT_CLIPSET_MAP_STD_FRONT_RIGHT" />
      <AlternateTryLockedDoorClipId>STD_Locked_PS</AlternateTryLockedDoorClipId>
      <AlternateForcedEntryClipId>STD_Force_Entry_PS</AlternateForcedEntryClipId>
      <AlternateJackFromOutSideClipId>std_perp_ps</AlternateJackFromOutSideClipId>
      <AlternateBeJackedFromOutSideClipId>std_victim_ps</AlternateBeJackedFromOutSideClipId>
      <AlternateEntryPointClipSetId>clipset@veh@std@ps@female@enter_exit</AlternateEntryPointClipSetId>
      <EnterVehicleMoveNetwork>ENTER_VEHICLE_STD</EnterVehicleMoveNetwork>
      <EntryTranslation x="0.900000" y="-0.650000" z="0.500000" />
      <OpenDoorTranslation x="0.050000" y="0.150000" />
      <OpenDoorHeadingChange value="-1.570000" />
      <EntryHeadingChange value="1.570000" />
      <ExtraZForMPPlaneWarp value="-2.000000" />
      <EntryPointFlags>JackIncludesGetIn UsesNoDoorTransitionForExit UseOpenDoorBlendAnims
        FixUpMoverToEntryPointOnExit UseGetUpAfterJack JackVariationsIncludeGetIn HasCombatEntry</EntryPointFlags>
      <EntryAnimVariations ref="ENTRY_ANIM_VARIATIONS_STANDARD_FRONT_RIGHT" />
      <NMJumpFromVehicleTuningSet />
    </Item>
    <Item type="CVehicleEntryPointAnimInfo">
      <Name>ENTRY_POINT_ANIM_STD_CASTIGATOR_REAR_LEFT</Name>
      <CommonClipSetMap ref="COMMON_CLIPSET_MAP_AUTOMOBILE_LEFT" />
      <EntryClipSetMap ref="ENTRY_CLIPSET_MAP_STD_REAR_LEFT" />
      <ExitClipSetMap ref="EXIT_CLIPSET_MAP_STD_REAR_LEFT" />
      <AlternateTryLockedDoorClipId />
      <AlternateForcedEntryClipId />
      <AlternateJackFromOutSideClipId />
      <AlternateBeJackedFromOutSideClipId />
      <AlternateEntryPointClipSetId />
      <EnterVehicleMoveNetwork>ENTER_VEHICLE_STD</EnterVehicleMoveNetwork>
      <EntryTranslation x="-0.870000" y="-0.690000" z="0.500000" />
      <OpenDoorTranslation x="-0.200000" y="0.000000" />
      <OpenDoorHeadingChange value="1.570000" />
      <EntryHeadingChange value="-1.570000" />
      <ExtraZForMPPlaneWarp value="-2.000000" />
      <EntryPointFlags>JackIncludesGetIn UsesNoDoorTransitionForExit UseOpenDoorBlendAnims
        FixUpMoverToEntryPointOnExit UseGetUpAfterJack</EntryPointFlags>
      <EntryAnimVariations ref="ENTRY_ANIM_VARIATIONS_STANDARD_REAR_LEFT" />
      <NMJumpFromVehicleTuningSet />
    </Item>
    <Item type="CVehicleEntryPointAnimInfo">
      <Name>ENTRY_POINT_ANIM_STD_CASTIGATOR_REAR_RIGHT</Name>
      <CommonClipSetMap ref="COMMON_CLIPSET_MAP_AUTOMOBILE_RIGHT" />
      <EntryClipSetMap ref="ENTRY_CLIPSET_MAP_STD_REAR_RIGHT" />
      <ExitClipSetMap ref="EXIT_CLIPSET_MAP_STD_REAR_RIGHT" />
      <AlternateTryLockedDoorClipId />
      <AlternateForcedEntryClipId />
      <AlternateJackFromOutSideClipId />
      <AlternateBeJackedFromOutSideClipId />
      <AlternateEntryPointClipSetId />
      <EnterVehicleMoveNetwork>ENTER_VEHICLE_STD</EnterVehicleMoveNetwork>
      <EntryTranslation x="0.810000" y="-0.690000" z="0.500000" />
      <OpenDoorTranslation x="0.200000" y="0.000000" />
      <OpenDoorHeadingChange value="-1.570000" />
      <EntryHeadingChange value="1.570000" />
      <ExtraZForMPPlaneWarp value="-2.000000" />
      <EntryPointFlags>JackIncludesGetIn UsesNoDoorTransitionForExit UseOpenDoorBlendAnims
        FixUpMoverToEntryPointOnExit UseGetUpAfterJack</EntryPointFlags>
      <EntryAnimVariations ref="ENTRY_ANIM_VARIATIONS_STANDARD_REAR_RIGHT" />
      <NMJumpFromVehicleTuningSet />
    </Item>
    <Item type="CVehicleEntryPointAnimInfo">
      <Name>ENTRY_POINT_ANIM_DOMINATOR10_FRONT_LEFT</Name>
      <CommonClipSetMap ref="COMMON_CLIPSET_MAP_AUTOMOBILE_LEFT" />
      <EntryClipSetMap ref="ENTRY_CLIPSET_MAP_STD_FRONT_LEFT" />
      <ExitClipSetMap ref="EXIT_CLIPSET_MAP_STD_FRONT_LEFT" />
      <AlternateTryLockedDoorClipId>STD_Locked_DS</AlternateTryLockedDoorClipId>
      <AlternateForcedEntryClipId>STD_Force_Entry_DS</AlternateForcedEntryClipId>
      <AlternateJackFromOutSideClipId>std_perp_ds</AlternateJackFromOutSideClipId>
      <AlternateBeJackedFromOutSideClipId>std_victim_ds</AlternateBeJackedFromOutSideClipId>
      <AlternateEntryPointClipSetId>clipset@veh@std@ds@female@enter_exit</AlternateEntryPointClipSetId>
      <EnterVehicleMoveNetwork>ENTER_VEHICLE_STD</EnterVehicleMoveNetwork>
      <EntryTranslation x="-0.760000" y="-0.650000" z="0.700000" />
      <OpenDoorTranslation x="-0.050000" y="0.150000" />
      <OpenDoorHeadingChange value="1.570000" />
      <EntryHeadingChange value="-1.570000" />
      <ExtraZForMPPlaneWarp value="-2.000000" />
      <EntryPointFlags>JackIncludesGetIn UsesNoDoorTransitionForExit UseOpenDoorBlendAnims
        FixUpMoverToEntryPointOnExit UseGetUpAfterJack JackVariationsIncludeGetIn HasCombatEntry</EntryPointFlags>
      <EntryAnimVariations ref="ENTRY_ANIM_VARIATIONS_STANDARD_FRONT_LEFT" />
      <NMJumpFromVehicleTuningSet>STANDARD</NMJumpFromVehicleTuningSet>
    </Item>
    <Item type="CVehicleEntryPointAnimInfo">
      <Name>ENTRY_POINT_ANIM_DOMINATOR10_FRONT_RIGHT</Name>
      <CommonClipSetMap ref="COMMON_CLIPSET_MAP_AUTOMOBILE_RIGHT" />
      <EntryClipSetMap ref="ENTRY_CLIPSET_MAP_STD_FRONT_RIGHT" />
      <ExitClipSetMap ref="EXIT_CLIPSET_MAP_STD_FRONT_RIGHT" />
      <AlternateTryLockedDoorClipId>STD_Locked_PS</AlternateTryLockedDoorClipId>
      <AlternateForcedEntryClipId>STD_Force_Entry_PS</AlternateForcedEntryClipId>
      <AlternateJackFromOutSideClipId>std_perp_ps</AlternateJackFromOutSideClipId>
      <AlternateBeJackedFromOutSideClipId>std_victim_ps</AlternateBeJackedFromOutSideClipId>
      <AlternateEntryPointClipSetId>clipset@veh@std@ps@female@enter_exit</AlternateEntryPointClipSetId>
      <EnterVehicleMoveNetwork>ENTER_VEHICLE_STD</EnterVehicleMoveNetwork>
      <EntryTranslation x="0.822600" y="-0.650000" z="0.700000" />
      <OpenDoorTranslation x="0.050000" y="0.150000" />
      <OpenDoorHeadingChange value="-1.570000" />
      <EntryHeadingChange value="1.570000" />
      <ExtraZForMPPlaneWarp value="-2.000000" />
      <EntryPointFlags>JackIncludesGetIn UsesNoDoorTransitionForExit UseOpenDoorBlendAnims
        FixUpMoverToEntryPointOnExit UseGetUpAfterJack JackVariationsIncludeGetIn HasCombatEntry</EntryPointFlags>
      <EntryAnimVariations ref="ENTRY_ANIM_VARIATIONS_STANDARD_FRONT_RIGHT" />
      <NMJumpFromVehicleTuningSet />
    </Item>
    <Item type="CVehicleEntryPointAnimInfo">
      <Name>ENTRY_POINT_ANIM_STD_ENVISAGE_FRONT_LEFT</Name>
      <CommonClipSetMap ref="COMMON_CLIPSET_MAP_AUTOMOBILE_LEFT" />
      <EntryClipSetMap ref="ENTRY_CLIPSET_MAP_STD_FRONT_LEFT" />
      <ExitClipSetMap ref="EXIT_CLIPSET_MAP_STD_FRONT_LEFT" />
      <AlternateTryLockedDoorClipId>STD_Locked_DS</AlternateTryLockedDoorClipId>
      <AlternateForcedEntryClipId>STD_Force_Entry_DS</AlternateForcedEntryClipId>
      <AlternateJackFromOutSideClipId>std_perp_ds</AlternateJackFromOutSideClipId>
      <AlternateBeJackedFromOutSideClipId>std_victim_ds</AlternateBeJackedFromOutSideClipId>
      <AlternateEntryPointClipSetId>clipset@veh@std@ds@female@enter_exit</AlternateEntryPointClipSetId>
      <EnterVehicleMoveNetwork>ENTER_VEHICLE_STD</EnterVehicleMoveNetwork>
      <EntryTranslation x="-0.840000" y="-0.650000" z="0.500000" />
      <OpenDoorTranslation x="-0.050000" y="0.150000" />
      <OpenDoorHeadingChange value="1.570000" />
      <EntryHeadingChange value="-1.570000" />
      <ExtraZForMPPlaneWarp value="-2.000000" />
      <EntryPointFlags>JackIncludesGetIn UsesNoDoorTransitionForExit UseOpenDoorBlendAnims
        FixUpMoverToEntryPointOnExit UseGetUpAfterJack JackVariationsIncludeGetIn HasCombatEntry</EntryPointFlags>
      <EntryAnimVariations ref="ENTRY_ANIM_VARIATIONS_STANDARD_FRONT_LEFT" />
      <NMJumpFromVehicleTuningSet>STANDARD</NMJumpFromVehicleTuningSet>
    </Item>
    <Item type="CVehicleEntryPointAnimInfo">
      <Name>ENTRY_POINT_ANIM_STD_ENVISAGE_FRONT_RIGHT</Name>
      <CommonClipSetMap ref="COMMON_CLIPSET_MAP_AUTOMOBILE_RIGHT" />
      <EntryClipSetMap ref="ENTRY_CLIPSET_MAP_STD_FRONT_RIGHT" />
      <ExitClipSetMap ref="EXIT_CLIPSET_MAP_STD_FRONT_RIGHT" />
      <AlternateTryLockedDoorClipId>STD_Locked_PS</AlternateTryLockedDoorClipId>
      <AlternateForcedEntryClipId>STD_Force_Entry_PS</AlternateForcedEntryClipId>
      <AlternateJackFromOutSideClipId>std_perp_ps</AlternateJackFromOutSideClipId>
      <AlternateBeJackedFromOutSideClipId>std_victim_ps</AlternateBeJackedFromOutSideClipId>
      <AlternateEntryPointClipSetId>clipset@veh@std@ps@female@enter_exit</AlternateEntryPointClipSetId>
      <EnterVehicleMoveNetwork>ENTER_VEHICLE_STD</EnterVehicleMoveNetwork>
      <EntryTranslation x="0.920000" y="-0.650000" z="0.500000" />
      <OpenDoorTranslation x="0.050000" y="0.150000" />
      <OpenDoorHeadingChange value="-1.570000" />
      <EntryHeadingChange value="1.570000" />
      <ExtraZForMPPlaneWarp value="-2.000000" />
      <EntryPointFlags>JackIncludesGetIn UsesNoDoorTransitionForExit UseOpenDoorBlendAnims
        FixUpMoverToEntryPointOnExit UseGetUpAfterJack JackVariationsIncludeGetIn HasCombatEntry</EntryPointFlags>
      <EntryAnimVariations ref="ENTRY_ANIM_VARIATIONS_STANDARD_FRONT_RIGHT" />
      <NMJumpFromVehicleTuningSet />
    </Item>
    <Item type="CVehicleEntryPointAnimInfo">
      <Name>ENTRY_POINT_ANIM_LOW_EUROSX32_FRONT_LEFT</Name>
      <CommonClipSetMap ref="COMMON_CLIPSET_MAP_AUTOMOBILE_LEFT" />
      <EntryClipSetMap ref="ENTRY_CLIPSET_MAP_LOW_FRONT_LEFT" />
      <ExitClipSetMap ref="EXIT_CLIPSET_MAP_LOW_FRONT_LEFT" />
      <AlternateTryLockedDoorClipId>LOW_Locked_DS</AlternateTryLockedDoorClipId>
      <AlternateForcedEntryClipId>LOW_Force_Entry_DS</AlternateForcedEntryClipId>
      <AlternateJackFromOutSideClipId>low_perp_ds_a</AlternateJackFromOutSideClipId>
      <AlternateBeJackedFromOutSideClipId>low_victim_ds_a</AlternateBeJackedFromOutSideClipId>
      <AlternateEntryPointClipSetId>clipset@veh@low@ds@female@enter_exit</AlternateEntryPointClipSetId>
      <EnterVehicleMoveNetwork>ENTER_VEHICLE_STD</EnterVehicleMoveNetwork>
      <EntryTranslation x="-1.000000" y="-0.850000" z="0.610300" />
      <OpenDoorTranslation x="0.000000" y="0.000000" />
      <OpenDoorHeadingChange value="1.570000" />
      <EntryHeadingChange value="-1.570000" />
      <ExtraZForMPPlaneWarp value="-2.000000" />
      <EntryPointFlags>JackIncludesGetIn UsesNoDoorTransitionForExit UseOpenDoorBlendAnims
        UseGetUpAfterJack DeadJackIncludesGetIn HasCombatEntry</EntryPointFlags>
      <EntryAnimVariations ref="ENTRY_ANIM_VARIATIONS_LOW_FRONT_LEFT" />
      <NMJumpFromVehicleTuningSet>LOWCAR</NMJumpFromVehicleTuningSet>
    </Item>
    <Item type="CVehicleEntryPointAnimInfo">
      <Name>ENTRY_POINT_ANIM_LOW_EUROSX32_FRONT_RIGHT</Name>
      <CommonClipSetMap ref="COMMON_CLIPSET_MAP_AUTOMOBILE_RIGHT" />
      <EntryClipSetMap ref="ENTRY_CLIPSET_MAP_LOW_FRONT_RIGHT" />
      <ExitClipSetMap ref="EXIT_CLIPSET_MAP_LOW_FRONT_RIGHT" />
      <AlternateTryLockedDoorClipId>LOW_Locked_PS</AlternateTryLockedDoorClipId>
      <AlternateForcedEntryClipId>LOW_Force_Entry_PS</AlternateForcedEntryClipId>
      <AlternateJackFromOutSideClipId>low_perp_ps_a</AlternateJackFromOutSideClipId>
      <AlternateBeJackedFromOutSideClipId>low_victim_ps_a</AlternateBeJackedFromOutSideClipId>
      <AlternateEntryPointClipSetId>clipset@veh@low@ps@female@enter_exit</AlternateEntryPointClipSetId>
      <EnterVehicleMoveNetwork>ENTER_VEHICLE_STD</EnterVehicleMoveNetwork>
      <EntryTranslation x="0.950000" y="-0.850000" z="0.610000" />
      <OpenDoorTranslation x="0.000000" y="0.000000" />
      <OpenDoorHeadingChange value="-1.570000" />
      <EntryHeadingChange value="1.570000" />
      <ExtraZForMPPlaneWarp value="-2.000000" />
      <EntryPointFlags>JackIncludesGetIn UsesNoDoorTransitionForExit UseOpenDoorBlendAnims
        UseGetUpAfterJack DeadJackIncludesGetIn HasCombatEntry</EntryPointFlags>
      <EntryAnimVariations ref="ENTRY_ANIM_VARIATIONS_LOW_FRONT_RIGHT" />
      <NMJumpFromVehicleTuningSet />
    </Item>
    <Item type="CVehicleEntryPointAnimInfo">
      <Name>ENTRY_POINT_ANIM_LOW_NIOBE_FRONT_LEFT</Name>
      <CommonClipSetMap ref="COMMON_CLIPSET_MAP_AUTOMOBILE_LEFT" />
      <EntryClipSetMap ref="ENTRY_CLIPSET_MAP_LOW_FRONT_LEFT" />
      <ExitClipSetMap ref="EXIT_CLIPSET_MAP_LOW_FRONT_LEFT" />
      <AlternateTryLockedDoorClipId>LOW_Locked_DS</AlternateTryLockedDoorClipId>
      <AlternateForcedEntryClipId>LOW_Force_Entry_DS</AlternateForcedEntryClipId>
      <AlternateJackFromOutSideClipId>low_perp_ds_a</AlternateJackFromOutSideClipId>
      <AlternateBeJackedFromOutSideClipId>low_victim_ds_a</AlternateBeJackedFromOutSideClipId>
      <AlternateEntryPointClipSetId>clipset@veh@low@ds@female@enter_exit</AlternateEntryPointClipSetId>
      <EnterVehicleMoveNetwork>ENTER_VEHICLE_STD</EnterVehicleMoveNetwork>
      <EntryTranslation x="-0.980000" y="-0.900000" z="0.610300" />
      <OpenDoorTranslation x="0.000000" y="0.000000" />
      <OpenDoorHeadingChange value="1.570000" />
      <EntryHeadingChange value="-1.570000" />
      <ExtraZForMPPlaneWarp value="-2.000000" />
      <EntryPointFlags>JackIncludesGetIn UsesNoDoorTransitionForExit UseOpenDoorBlendAnims
        UseGetUpAfterJack DeadJackIncludesGetIn HasCombatEntry</EntryPointFlags>
      <EntryAnimVariations ref="ENTRY_ANIM_VARIATIONS_LOW_FRONT_LEFT" />
      <NMJumpFromVehicleTuningSet>LOWCAR</NMJumpFromVehicleTuningSet>
    </Item>
    <Item type="CVehicleEntryPointAnimInfo">
      <Name>ENTRY_POINT_ANIM_LOW_NIOBE_FRONT_RIGHT</Name>
      <CommonClipSetMap ref="COMMON_CLIPSET_MAP_AUTOMOBILE_RIGHT" />
      <EntryClipSetMap ref="ENTRY_CLIPSET_MAP_LOW_FRONT_RIGHT" />
      <ExitClipSetMap ref="EXIT_CLIPSET_MAP_LOW_FRONT_RIGHT" />
      <AlternateTryLockedDoorClipId>LOW_Locked_PS</AlternateTryLockedDoorClipId>
      <AlternateForcedEntryClipId>LOW_Force_Entry_PS</AlternateForcedEntryClipId>
      <AlternateJackFromOutSideClipId>low_perp_ps_a</AlternateJackFromOutSideClipId>
      <AlternateBeJackedFromOutSideClipId>low_victim_ps_a</AlternateBeJackedFromOutSideClipId>
      <AlternateEntryPointClipSetId>clipset@veh@low@ps@female@enter_exit</AlternateEntryPointClipSetId>
      <EnterVehicleMoveNetwork>ENTER_VEHICLE_STD</EnterVehicleMoveNetwork>
      <EntryTranslation x="0.875000" y="-0.900000" z="0.610000" />
      <OpenDoorTranslation x="0.000000" y="0.000000" />
      <OpenDoorHeadingChange value="-1.570000" />
      <EntryHeadingChange value="1.570000" />
      <ExtraZForMPPlaneWarp value="-2.000000" />
      <EntryPointFlags>JackIncludesGetIn UsesNoDoorTransitionForExit UseOpenDoorBlendAnims
        UseGetUpAfterJack DeadJackIncludesGetIn HasCombatEntry</EntryPointFlags>
      <EntryAnimVariations ref="ENTRY_ANIM_VARIATIONS_LOW_FRONT_RIGHT" />
      <NMJumpFromVehicleTuningSet />
    </Item>
    <Item type="CVehicleEntryPointAnimInfo">
      <Name>ENTRY_POINT_ANIM_STD_POLIMPALER6_FRONT_LEFT</Name>
      <CommonClipSetMap ref="COMMON_CLIPSET_MAP_AUTOMOBILE_LEFT" />
      <EntryClipSetMap ref="ENTRY_CLIPSET_MAP_STD_FRONT_LEFT" />
      <ExitClipSetMap ref="EXIT_CLIPSET_MAP_STD_FRONT_LEFT" />
      <AlternateTryLockedDoorClipId>STD_Locked_DS</AlternateTryLockedDoorClipId>
      <AlternateForcedEntryClipId>STD_Force_Entry_DS</AlternateForcedEntryClipId>
      <AlternateJackFromOutSideClipId>std_perp_ds</AlternateJackFromOutSideClipId>
      <AlternateBeJackedFromOutSideClipId>std_victim_ds</AlternateBeJackedFromOutSideClipId>
      <AlternateEntryPointClipSetId>clipset@veh@std@ds@female@enter_exit</AlternateEntryPointClipSetId>
      <EnterVehicleMoveNetwork>ENTER_VEHICLE_STD</EnterVehicleMoveNetwork>
      <EntryTranslation x="-0.700000" y="-0.350000" z="0.500000" />
      <OpenDoorTranslation x="-0.050000" y="0.150000" />
      <OpenDoorHeadingChange value="1.570000" />
      <EntryHeadingChange value="-1.570000" />
      <ExtraZForMPPlaneWarp value="-2.000000" />
      <EntryPointFlags>JackIncludesGetIn UsesNoDoorTransitionForExit UseOpenDoorBlendAnims
        UseGetUpAfterJack JackVariationsIncludeGetIn HasCombatEntry</EntryPointFlags>
      <EntryAnimVariations ref="ENTRY_ANIM_VARIATIONS_STANDARD_FRONT_LEFT" />
      <NMJumpFromVehicleTuningSet>STANDARD</NMJumpFromVehicleTuningSet>
    </Item>
    <Item type="CVehicleEntryPointAnimInfo">
      <Name>ENTRY_POINT_ANIM_STD_POLIMPALER6_FRONT_RIGHT</Name>
      <CommonClipSetMap ref="COMMON_CLIPSET_MAP_AUTOMOBILE_RIGHT" />
      <EntryClipSetMap ref="ENTRY_CLIPSET_MAP_STD_FRONT_RIGHT" />
      <ExitClipSetMap ref="EXIT_CLIPSET_MAP_STD_FRONT_RIGHT" />
      <AlternateTryLockedDoorClipId>STD_Locked_PS</AlternateTryLockedDoorClipId>
      <AlternateForcedEntryClipId>STD_Force_Entry_PS</AlternateForcedEntryClipId>
      <AlternateJackFromOutSideClipId>std_perp_ps</AlternateJackFromOutSideClipId>
      <AlternateBeJackedFromOutSideClipId>std_victim_ps</AlternateBeJackedFromOutSideClipId>
      <AlternateEntryPointClipSetId>clipset@veh@std@ps@female@enter_exit</AlternateEntryPointClipSetId>
      <EnterVehicleMoveNetwork>ENTER_VEHICLE_STD</EnterVehicleMoveNetwork>
      <EntryTranslation x="0.700000" y="-0.350000" z="0.500000" />
      <OpenDoorTranslation x="0.050000" y="0.150000" />
      <OpenDoorHeadingChange value="-1.570000" />
      <EntryHeadingChange value="1.570000" />
      <ExtraZForMPPlaneWarp value="-2.000000" />
      <EntryPointFlags>JackIncludesGetIn UsesNoDoorTransitionForExit UseOpenDoorBlendAnims
        UseGetUpAfterJack JackVariationsIncludeGetIn HasCombatEntry</EntryPointFlags>
      <EntryAnimVariations ref="ENTRY_ANIM_VARIATIONS_STANDARD_FRONT_RIGHT" />
      <NMJumpFromVehicleTuningSet />
    </Item>
    <Item type="CVehicleEntryPointAnimInfo">
      <Name>ENTRY_POINT_ANIM_STD_POLIMPALER6_REAR_LEFT</Name>
      <CommonClipSetMap ref="COMMON_CLIPSET_MAP_AUTOMOBILE_LEFT" />
      <EntryClipSetMap ref="ENTRY_CLIPSET_MAP_STD_REAR_LEFT" />
      <ExitClipSetMap ref="EXIT_CLIPSET_MAP_STD_REAR_LEFT" />
      <AlternateTryLockedDoorClipId />
      <AlternateForcedEntryClipId />
      <AlternateJackFromOutSideClipId />
      <AlternateBeJackedFromOutSideClipId />
      <AlternateEntryPointClipSetId />
      <EnterVehicleMoveNetwork>ENTER_VEHICLE_STD</EnterVehicleMoveNetwork>
      <EntryTranslation x="-0.700000" y="-0.420000" z="0.500000" />
      <OpenDoorTranslation x="-0.200000" y="0.000000" />
      <OpenDoorHeadingChange value="1.570000" />
      <EntryHeadingChange value="-1.570000" />
      <ExtraZForMPPlaneWarp value="-2.000000" />
      <EntryPointFlags>JackIncludesGetIn UsesNoDoorTransitionForExit UseOpenDoorBlendAnims
        UseGetUpAfterJack</EntryPointFlags>
      <EntryAnimVariations ref="ENTRY_ANIM_VARIATIONS_STANDARD_REAR_LEFT" />
      <NMJumpFromVehicleTuningSet />
    </Item>
    <Item type="CVehicleEntryPointAnimInfo">
      <Name>ENTRY_POINT_ANIM_STD_POLIMPALER6_REAR_RIGHT</Name>
      <CommonClipSetMap ref="COMMON_CLIPSET_MAP_AUTOMOBILE_RIGHT" />
      <EntryClipSetMap ref="ENTRY_CLIPSET_MAP_STD_REAR_RIGHT" />
      <ExitClipSetMap ref="EXIT_CLIPSET_MAP_STD_REAR_RIGHT" />
      <AlternateTryLockedDoorClipId />
      <AlternateForcedEntryClipId />
      <AlternateJackFromOutSideClipId />
      <AlternateBeJackedFromOutSideClipId />
      <AlternateEntryPointClipSetId />
      <EnterVehicleMoveNetwork>ENTER_VEHICLE_STD</EnterVehicleMoveNetwork>
      <EntryTranslation x="0.700000" y="-0.420000" z="0.500000" />
      <OpenDoorTranslation x="0.200000" y="0.000000" />
      <OpenDoorHeadingChange value="-1.570000" />
      <EntryHeadingChange value="1.570000" />
      <ExtraZForMPPlaneWarp value="-2.000000" />
      <EntryPointFlags>JackIncludesGetIn UsesNoDoorTransitionForExit UseOpenDoorBlendAnims
        UseGetUpAfterJack</EntryPointFlags>
      <EntryAnimVariations ref="ENTRY_ANIM_VARIATIONS_STANDARD_REAR_RIGHT" />
      <NMJumpFromVehicleTuningSet />
    </Item>
  </VehicleEntryPointAnimInfos>
  <VehicleExplosionInfos />
  <VehicleLayoutInfos>
    <Item type="CVehicleLayoutInfo">
      <Name>LAYOUT_STD_CASTIGATOR</Name>
      <Seats>
        <Item>
          <SeatInfo ref="SEAT_STANDARD_FRONT_LEFT" />
          <SeatAnimInfo ref="SEAT_ANIM_STANDARD_FRONT_LEFT" />
        </Item>
        <Item>
          <SeatInfo ref="SEAT_STANDARD_FRONT_RIGHT" />
          <SeatAnimInfo ref="SEAT_ANIM_STANDARD_FRONT_RIGHT" />
        </Item>
        <Item>
          <SeatInfo ref="SEAT_STANDARD_REAR_LEFT" />
          <SeatAnimInfo ref="SEAT_ANIM_STANDARD_REAR_LEFT" />
        </Item>
        <Item>
          <SeatInfo ref="SEAT_STANDARD_REAR_RIGHT" />
          <SeatAnimInfo ref="SEAT_ANIM_STANDARD_REAR_RIGHT" />
        </Item>
      </Seats>
      <EntryPoints>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_STANDARD_FRONT_LEFT" />
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_STD_CASTIGATOR_FRONT_LEFT" />
        </Item>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_STANDARD_FRONT_RIGHT" />
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_STD_CASTIGATOR_FRONT_RIGHT" />
        </Item>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_STANDARD_REAR_LEFT" />
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_STD_CASTIGATOR_REAR_LEFT" />
        </Item>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_STANDARD_REAR_RIGHT" />
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_STD_CASTIGATOR_REAR_RIGHT" />
        </Item>
      </EntryPoints>
      <LayoutFlags>StreamAnims UseDoorOscillation UseLeanSteerAnims UseSteeringWheelIk</LayoutFlags>
      <BicycleInfo ref="NULL" />
      <AnimRateSet ref="STD_LOW_ANIM_RATE_SET" />
      <HandsUpClipSetId>busted_vehicle_std</HandsUpClipSetId>
      <SteeringWheelOffset x="0.000000" y="0.350000" z="0.320000" />
      <MaxXAcceleration value="4.000000" />
      <BodyLeanXApproachSpeed value="5.000000" />
      <BodyLeanXSmallDelta value="0.300000" />
      <LookBackApproachSpeedScale value="1.000000" />
      <FirstPersonAdditiveIdleClipSets>
        <Item>clipset@veh@std@ds@idle_a</Item>
        <Item>clipset@veh@std@ds@idle_b</Item>
        <Item>clipset@veh@std@ds@idle_c</Item>
        <Item>clipset@veh@std@ds@idle_d</Item>
        <Item>clipset@veh@std@ds@idle_e</Item>
      </FirstPersonAdditiveIdleClipSets>
      <FirstPersonRoadRageClipSets>
        <Item>clipset@veh@std@ds@hit_wheel@idle_a</Item>
        <Item>clipset@veh@std@ds@hit_wheel@idle_b</Item>
        <Item>clipset@veh@std@ds@hit_wheel@idle_c</Item>
      </FirstPersonRoadRageClipSets>
      <CellphoneClipsets>
        <CellphoneClipsetDS>ANIM_GROUP_CELLPHONE_IN_CAR_DS</CellphoneClipsetDS>
        <CellphoneClipsetPS>ANIM_GROUP_CELLPHONE_IN_CAR_PS</CellphoneClipsetPS>
        <CellphoneClipsetDS_FPS>ANIM_GROUP_CELLPHONE_IN_CAR_DS_FPS</CellphoneClipsetDS_FPS>
        <CellphoneClipsetPS_FPS>ANIM_GROUP_CELLPHONE_IN_CAR_DS_FPS</CellphoneClipsetPS_FPS>
      </CellphoneClipsets>
    </Item>
    <Item type="CVehicleLayoutInfo">
      <Name>LAYOUT_LOW_LAYOUT_LOW_COQUETTE5</Name>
      <Seats>
        <Item>
          <SeatInfo ref="SEAT_LOW_FRONT_LEFT" />
          <SeatAnimInfo ref="SEAT_ANIM_LOW_COQUETTE5_FRONT_LEFT" />
        </Item>
        <Item>
          <SeatInfo ref="SEAT_LOW_FRONT_RIGHT" />
          <SeatAnimInfo ref="SEAT_ANIM_LOW_FRONT_RIGHT" />
        </Item>
      </Seats>
      <EntryPoints>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_LOW_FRONT_LEFT" />
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_LOW_FRONT_LEFT" />
        </Item>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_LOW_FRONT_RIGHT" />
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_LOW_FRONT_RIGHT" />
        </Item>
      </EntryPoints>
      <LayoutFlags>StreamAnims UseDoorOscillation UseLeanSteerAnims UseSteeringWheelIk</LayoutFlags>
      <BicycleInfo ref="NULL" />
      <AnimRateSet ref="STD_LOW_ANIM_RATE_SET" />
      <HandsUpClipSetId>busted_vehicle_low</HandsUpClipSetId>
      <SteeringWheelOffset x="0.000000" y="0.350000" z="0.320000" />
      <MaxXAcceleration value="15.000000" />
      <BodyLeanXApproachSpeed value="10.000000" />
      <BodyLeanXSmallDelta value="0.400000" />
      <LookBackApproachSpeedScale value="0.500000" />
      <FirstPersonAdditiveIdleClipSets>
        <Item>clipset@veh@low@ds@idle_a</Item>
        <Item>clipset@veh@low@ds@idle_b</Item>
        <Item>clipset@veh@low@ds@idle_c</Item>
        <Item>clipset@veh@low@ds@idle_d</Item>
        <Item>clipset@veh@low@ds@idle_e</Item>
      </FirstPersonAdditiveIdleClipSets>
      <FirstPersonRoadRageClipSets>
        <Item>clipset@veh@low@ds@hit_wheel@idle_a</Item>
        <Item>clipset@veh@low@ds@hit_wheel@idle_b</Item>
        <Item>clipset@veh@low@ds@hit_wheel@idle_c</Item>
      </FirstPersonRoadRageClipSets>
      <CellphoneClipsets>
        <CellphoneClipsetDS>ANIM_GROUP_CELLPHONE_IN_CAR_DS</CellphoneClipsetDS>
        <CellphoneClipsetPS>ANIM_GROUP_CELLPHONE_IN_CAR_PS</CellphoneClipsetPS>
        <CellphoneClipsetDS_FPS>ANIM_GROUP_CELLPHONE_IN_CAR_DS_FPS</CellphoneClipsetDS_FPS>
        <CellphoneClipsetPS_FPS>ANIM_GROUP_CELLPHONE_IN_CAR_DS_FPS</CellphoneClipsetPS_FPS>
      </CellphoneClipsets>
    </Item>
    <Item type="CVehicleLayoutInfo">
      <Name>LAYOUT_STD_DOMINATOR10</Name>
      <Seats>
        <Item>
          <SeatInfo ref="SEAT_STANDARD_FRONT_LEFT" />
          <SeatAnimInfo ref="SEAT_ANIM_STANDARD_FRONT_LEFT" />
        </Item>
        <Item>
          <SeatInfo ref="SEAT_STANDARD_FRONT_RIGHT" />
          <SeatAnimInfo ref="SEAT_ANIM_STANDARD_FRONT_RIGHT" />
        </Item>
      </Seats>
      <EntryPoints>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_STANDARD_FRONT_LEFT" />
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_DOMINATOR10_FRONT_LEFT" />
        </Item>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_STANDARD_FRONT_RIGHT" />
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_DOMINATOR10_FRONT_RIGHT" />
        </Item>
      </EntryPoints>
      <LayoutFlags>StreamAnims UseDoorOscillation UseLeanSteerAnims UseSteeringWheelIk</LayoutFlags>
      <BicycleInfo ref="NULL" />
      <AnimRateSet ref="STD_LOW_ANIM_RATE_SET" />
      <HandsUpClipSetId>busted_vehicle_std</HandsUpClipSetId>
      <SteeringWheelOffset x="0.000000" y="0.350000" z="0.320000" />
      <MaxXAcceleration value="4.000000" />
      <BodyLeanXApproachSpeed value="5.000000" />
      <BodyLeanXSmallDelta value="0.300000" />
      <LookBackApproachSpeedScale value="1.000000" />
      <FirstPersonAdditiveIdleClipSets>
        <Item>clipset@veh@std@ds@idle_a</Item>
        <Item>clipset@veh@std@ds@idle_b</Item>
        <Item>clipset@veh@std@ds@idle_c</Item>
        <Item>clipset@veh@std@ds@idle_d</Item>
        <Item>clipset@veh@std@ds@idle_e</Item>
      </FirstPersonAdditiveIdleClipSets>
      <FirstPersonRoadRageClipSets>
        <Item>clipset@veh@std@ds@hit_wheel@idle_a</Item>
        <Item>clipset@veh@std@ds@hit_wheel@idle_b</Item>
        <Item>clipset@veh@std@ds@hit_wheel@idle_c</Item>
      </FirstPersonRoadRageClipSets>
      <CellphoneClipsets>
        <CellphoneClipsetDS>ANIM_GROUP_CELLPHONE_IN_CAR_DS</CellphoneClipsetDS>
        <CellphoneClipsetPS>ANIM_GROUP_CELLPHONE_IN_CAR_PS</CellphoneClipsetPS>
        <CellphoneClipsetDS_FPS>ANIM_GROUP_CELLPHONE_IN_CAR_DS_FPS</CellphoneClipsetDS_FPS>
        <CellphoneClipsetPS_FPS>ANIM_GROUP_CELLPHONE_IN_CAR_DS_FPS</CellphoneClipsetPS_FPS>
      </CellphoneClipsets>
    </Item>
    <Item type="CVehicleLayoutInfo">
      <Name>LAYOUT_STD_ENVISAGE</Name>
      <Seats>
        <Item>
          <SeatInfo ref="SEAT_STANDARD_FRONT_LEFT" />
          <SeatAnimInfo ref="SEAT_ANIM_STANDARD_FRONT_LEFT" />
        </Item>
        <Item>
          <SeatInfo ref="SEAT_STANDARD_FRONT_RIGHT" />
          <SeatAnimInfo ref="SEAT_ANIM_STANDARD_FRONT_RIGHT" />
        </Item>
      </Seats>
      <EntryPoints>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_STANDARD_FRONT_LEFT" />
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_STD_ENVISAGE_FRONT_LEFT" />
        </Item>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_STANDARD_FRONT_RIGHT" />
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_STD_ENVISAGE_FRONT_RIGHT" />
        </Item>
      </EntryPoints>
      <LayoutFlags>StreamAnims UseDoorOscillation UseLeanSteerAnims UseSteeringWheelIk</LayoutFlags>
      <BicycleInfo ref="NULL" />
      <AnimRateSet ref="STD_LOW_ANIM_RATE_SET" />
      <HandsUpClipSetId>busted_vehicle_std</HandsUpClipSetId>
      <SteeringWheelOffset x="0.000000" y="0.350000" z="0.320000" />
      <MaxXAcceleration value="4.000000" />
      <BodyLeanXApproachSpeed value="5.000000" />
      <BodyLeanXSmallDelta value="0.300000" />
      <LookBackApproachSpeedScale value="1.000000" />
      <FirstPersonAdditiveIdleClipSets>
        <Item>clipset@veh@std@ds@idle_a</Item>
        <Item>clipset@veh@std@ds@idle_b</Item>
        <Item>clipset@veh@std@ds@idle_c</Item>
        <Item>clipset@veh@std@ds@idle_d</Item>
        <Item>clipset@veh@std@ds@idle_e</Item>
      </FirstPersonAdditiveIdleClipSets>
      <FirstPersonRoadRageClipSets>
        <Item>clipset@veh@std@ds@hit_wheel@idle_a</Item>
        <Item>clipset@veh@std@ds@hit_wheel@idle_b</Item>
        <Item>clipset@veh@std@ds@hit_wheel@idle_c</Item>
      </FirstPersonRoadRageClipSets>
      <CellphoneClipsets>
        <CellphoneClipsetDS>ANIM_GROUP_CELLPHONE_IN_CAR_DS</CellphoneClipsetDS>
        <CellphoneClipsetPS>ANIM_GROUP_CELLPHONE_IN_CAR_PS</CellphoneClipsetPS>
        <CellphoneClipsetDS_FPS>ANIM_GROUP_CELLPHONE_IN_CAR_DS_FPS</CellphoneClipsetDS_FPS>
        <CellphoneClipsetPS_FPS>ANIM_GROUP_CELLPHONE_IN_CAR_DS_FPS</CellphoneClipsetPS_FPS>
      </CellphoneClipsets>
    </Item>
    <Item type="CVehicleLayoutInfo">
      <Name>LAYOUT_LOW_EUROSX32</Name>
      <Seats>
        <Item>
          <SeatInfo ref="SEAT_LOW_FRONT_LEFT" />
          <SeatAnimInfo ref="SEAT_ANIM_LOW_FRONT_LEFT" />
        </Item>
        <Item>
          <SeatInfo ref="SEAT_LOW_FRONT_RIGHT" />
          <SeatAnimInfo ref="SEAT_ANIM_LOW_FRONT_RIGHT" />
        </Item>
      </Seats>
      <EntryPoints>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_LOW_FRONT_LEFT" />
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_LOW_EUROSX32_FRONT_LEFT" />
        </Item>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_LOW_FRONT_RIGHT" />
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_LOW_EUROSX32_FRONT_RIGHT" />
        </Item>
      </EntryPoints>
      <LayoutFlags>StreamAnims UseDoorOscillation UseLeanSteerAnims UseSteeringWheelIk</LayoutFlags>
      <BicycleInfo ref="NULL" />
      <AnimRateSet ref="STD_LOW_ANIM_RATE_SET" />
      <HandsUpClipSetId>busted_vehicle_low</HandsUpClipSetId>
      <SteeringWheelOffset x="0.000000" y="0.350000" z="0.320000" />
      <MaxXAcceleration value="15.000000" />
      <BodyLeanXApproachSpeed value="10.000000" />
      <BodyLeanXSmallDelta value="0.400000" />
      <LookBackApproachSpeedScale value="0.500000" />
      <FirstPersonAdditiveIdleClipSets>
        <Item>clipset@veh@low@ds@idle_a</Item>
        <Item>clipset@veh@low@ds@idle_b</Item>
        <Item>clipset@veh@low@ds@idle_c</Item>
        <Item>clipset@veh@low@ds@idle_d</Item>
        <Item>clipset@veh@low@ds@idle_e</Item>
      </FirstPersonAdditiveIdleClipSets>
      <FirstPersonRoadRageClipSets>
        <Item>clipset@veh@low@ds@hit_wheel@idle_a</Item>
        <Item>clipset@veh@low@ds@hit_wheel@idle_b</Item>
        <Item>clipset@veh@low@ds@hit_wheel@idle_c</Item>
      </FirstPersonRoadRageClipSets>
      <CellphoneClipsets>
        <CellphoneClipsetDS>ANIM_GROUP_CELLPHONE_IN_CAR_DS</CellphoneClipsetDS>
        <CellphoneClipsetPS>ANIM_GROUP_CELLPHONE_IN_CAR_PS</CellphoneClipsetPS>
        <CellphoneClipsetDS_FPS>ANIM_GROUP_CELLPHONE_IN_CAR_DS_FPS</CellphoneClipsetDS_FPS>
        <CellphoneClipsetPS_FPS>ANIM_GROUP_CELLPHONE_IN_CAR_DS_FPS</CellphoneClipsetPS_FPS>
      </CellphoneClipsets>
    </Item>
    <Item type="CVehicleLayoutInfo">
      <Name>LAYOUT_LOW_NIOBE</Name>
      <Seats>
        <Item>
          <SeatInfo ref="SEAT_LOW_FRONT_LEFT" />
          <SeatAnimInfo ref="SEAT_ANIM_LOW_FRONT_LEFT" />
        </Item>
        <Item>
          <SeatInfo ref="SEAT_LOW_FRONT_RIGHT" />
          <SeatAnimInfo ref="SEAT_ANIM_LOW_FRONT_RIGHT" />
        </Item>
      </Seats>
      <EntryPoints>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_LOW_FRONT_LEFT" />
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_LOW_NIOBE_FRONT_LEFT" />
        </Item>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_LOW_FRONT_RIGHT" />
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_LOW_NIOBE_FRONT_RIGHT" />
        </Item>
      </EntryPoints>
      <LayoutFlags>StreamAnims UseDoorOscillation UseLeanSteerAnims UseSteeringWheelIk</LayoutFlags>
      <BicycleInfo ref="NULL" />
      <AnimRateSet ref="STD_LOW_ANIM_RATE_SET" />
      <HandsUpClipSetId>busted_vehicle_low</HandsUpClipSetId>
      <SteeringWheelOffset x="0.000000" y="0.350000" z="0.320000" />
      <MaxXAcceleration value="15.000000" />
      <BodyLeanXApproachSpeed value="10.000000" />
      <BodyLeanXSmallDelta value="0.400000" />
      <LookBackApproachSpeedScale value="0.500000" />
      <FirstPersonAdditiveIdleClipSets>
        <Item>clipset@veh@low@ds@idle_a</Item>
        <Item>clipset@veh@low@ds@idle_b</Item>
        <Item>clipset@veh@low@ds@idle_c</Item>
        <Item>clipset@veh@low@ds@idle_d</Item>
        <Item>clipset@veh@low@ds@idle_e</Item>
      </FirstPersonAdditiveIdleClipSets>
      <FirstPersonRoadRageClipSets>
        <Item>clipset@veh@low@ds@hit_wheel@idle_a</Item>
        <Item>clipset@veh@low@ds@hit_wheel@idle_b</Item>
        <Item>clipset@veh@low@ds@hit_wheel@idle_c</Item>
      </FirstPersonRoadRageClipSets>
      <CellphoneClipsets>
        <CellphoneClipsetDS>ANIM_GROUP_CELLPHONE_IN_CAR_DS</CellphoneClipsetDS>
        <CellphoneClipsetPS>ANIM_GROUP_CELLPHONE_IN_CAR_PS</CellphoneClipsetPS>
        <CellphoneClipsetDS_FPS>ANIM_GROUP_CELLPHONE_IN_CAR_DS_FPS</CellphoneClipsetDS_FPS>
        <CellphoneClipsetPS_FPS>ANIM_GROUP_CELLPHONE_IN_CAR_DS_FPS</CellphoneClipsetPS_FPS>
      </CellphoneClipsets>
    </Item>
    <Item type="CVehicleLayoutInfo">
      <Name>LAYOUT_LOW_PIPISTRELLO</Name>
      <Seats>
        <Item>
          <SeatInfo ref="SEAT_LOW_FRONT_LEFT" />
          <SeatAnimInfo ref="SEAT_ANIM_LOW_RESTRICTED_FRONT_LEFT" />
        </Item>
        <Item>
          <SeatInfo ref="SEAT_LOW_FRONT_RIGHT" />
          <SeatAnimInfo ref="SEAT_ANIM_LOW_RESTRICTED_FRONT_RIGHT" />
        </Item>
      </Seats>
      <EntryPoints>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_LOW_FRONT_LEFT" />
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_LOW_FRONT_LEFT" />
        </Item>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_LOW_FRONT_RIGHT" />
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_LOW_FRONT_RIGHT" />
        </Item>
      </EntryPoints>
      <LayoutFlags>StreamAnims UseDoorOscillation NoArmIkOnInsideCloseDoor NoArmIkOnOutsideCloseDoor
        UseLeanSteerAnims UseSteeringWheelIk AutomaticCloseDoor</LayoutFlags>
      <BicycleInfo ref="NULL" />
      <AnimRateSet ref="STD_LOW_ANIM_RATE_SET" />
      <HandsUpClipSetId>busted_vehicle_low</HandsUpClipSetId>
      <SteeringWheelOffset x="0.000000" y="0.350000" z="0.320000" />
      <MaxXAcceleration value="15.000000" />
      <BodyLeanXApproachSpeed value="10.000000" />
      <BodyLeanXSmallDelta value="0.400000" />
      <LookBackApproachSpeedScale value="1.000000" />
      <FirstPersonAdditiveIdleClipSets>
        <Item>clipset@veh@low@ds@idle_a</Item>
        <Item>clipset@veh@low@ds@idle_b</Item>
        <Item>clipset@veh@low@ds@idle_c</Item>
        <Item>clipset@veh@low@ds@idle_d</Item>
        <Item>clipset@veh@low@ds@idle_e</Item>
      </FirstPersonAdditiveIdleClipSets>
      <FirstPersonRoadRageClipSets>
        <Item>clipset@veh@low@ds@hit_wheel@idle_a</Item>
        <Item>clipset@veh@low@ds@hit_wheel@idle_b</Item>
        <Item>clipset@veh@low@ds@hit_wheel@idle_c</Item>
      </FirstPersonRoadRageClipSets>
      <CellphoneClipsets>
        <CellphoneClipsetDS>ANIM_GROUP_CELLPHONE_IN_CAR_DS</CellphoneClipsetDS>
        <CellphoneClipsetPS>ANIM_GROUP_CELLPHONE_IN_CAR_PS</CellphoneClipsetPS>
        <CellphoneClipsetDS_FPS>ANIM_GROUP_CELLPHONE_IN_CAR_DS_FPS</CellphoneClipsetDS_FPS>
        <CellphoneClipsetPS_FPS>ANIM_GROUP_CELLPHONE_IN_CAR_DS_FPS</CellphoneClipsetPS_FPS>
      </CellphoneClipsets>
    </Item>
    <Item type="CVehicleLayoutInfo">
      <Name>LAYOUT_STD_POLIMPALER6</Name>
      <Seats>
        <Item>
          <SeatInfo ref="SEAT_STANDARD_FRONT_LEFT" />
          <SeatAnimInfo ref="SEAT_ANIM_STANDARD_FRONT_LEFT" />
        </Item>
        <Item>
          <SeatInfo ref="SEAT_STANDARD_FRONT_RIGHT" />
          <SeatAnimInfo ref="SEAT_ANIM_STD_POLIMPALER6_FRONT_RIGHT" />
        </Item>
        <Item>
          <SeatInfo ref="SEAT_STANDARD_REAR_LEFT" />
          <SeatAnimInfo ref="SEAT_ANIM_STANDARD_REAR_LEFT" />
        </Item>
        <Item>
          <SeatInfo ref="SEAT_STANDARD_REAR_RIGHT" />
          <SeatAnimInfo ref="SEAT_ANIM_STANDARD_REAR_RIGHT" />
        </Item>
      </Seats>
      <EntryPoints>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_STANDARD_FRONT_LEFT" />
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_STD_POLIMPALER6_FRONT_LEFT" />
        </Item>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_STANDARD_FRONT_RIGHT" />
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_STD_POLIMPALER6_FRONT_RIGHT" />
        </Item>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_STANDARD_REAR_LEFT" />
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_STD_POLIMPALER6_REAR_LEFT" />
        </Item>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_STANDARD_REAR_RIGHT" />
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_STD_POLIMPALER6_REAR_RIGHT" />
        </Item>
      </EntryPoints>
      <LayoutFlags>StreamAnims UseDoorOscillation UseLeanSteerAnims UseSteeringWheelIk</LayoutFlags>
      <BicycleInfo ref="NULL" />
      <AnimRateSet ref="STD_LOW_ANIM_RATE_SET" />
      <HandsUpClipSetId>busted_vehicle_std</HandsUpClipSetId>
      <SteeringWheelOffset x="0.000000" y="0.350000" z="0.320000" />
      <MaxXAcceleration value="4.000000" />
      <BodyLeanXApproachSpeed value="5.000000" />
      <BodyLeanXSmallDelta value="0.300000" />
      <LookBackApproachSpeedScale value="1.000000" />
      <FirstPersonAdditiveIdleClipSets>
        <Item>clipset@veh@std@ds@idle_a</Item>
        <Item>clipset@veh@std@ds@idle_b</Item>
        <Item>clipset@veh@std@ds@idle_c</Item>
        <Item>clipset@veh@std@ds@idle_d</Item>
        <Item>clipset@veh@std@ds@idle_e</Item>
      </FirstPersonAdditiveIdleClipSets>
      <FirstPersonRoadRageClipSets>
        <Item>clipset@veh@std@ds@hit_wheel@idle_a</Item>
        <Item>clipset@veh@std@ds@hit_wheel@idle_b</Item>
        <Item>clipset@veh@std@ds@hit_wheel@idle_c</Item>
      </FirstPersonRoadRageClipSets>
      <CellphoneClipsets>
        <CellphoneClipsetDS>ANIM_GROUP_CELLPHONE_IN_CAR_DS</CellphoneClipsetDS>
        <CellphoneClipsetPS>ANIM_GROUP_CELLPHONE_IN_CAR_PS</CellphoneClipsetPS>
        <CellphoneClipsetDS_FPS>ANIM_GROUP_CELLPHONE_IN_CAR_DS_FPS</CellphoneClipsetDS_FPS>
        <CellphoneClipsetPS_FPS>ANIM_GROUP_CELLPHONE_IN_CAR_DS_FPS</CellphoneClipsetPS_FPS>
      </CellphoneClipsets>
    </Item>
    <Item type="CVehicleLayoutInfo">
      <Name>LAYOUT_VAN_POLICET3</Name>
      <Seats>
        <Item>
          <SeatInfo ref="SEAT_VAN_FRONT_LEFT" />
          <SeatAnimInfo ref="SEAT_ANIM_VAN_FRONT_LEFT" />
        </Item>
        <Item>
          <SeatInfo ref="SEAT_VAN_FRONT_RIGHT" />
          <SeatAnimInfo ref="SEAT_ANIM_VAN_FRONT_RIGHT" />
        </Item>
        <Item>
          <SeatInfo ref="SEAT_VAN_REAR_LEFT" />
          <SeatAnimInfo ref="SEAT_ANIM_VAN_REAR_LEFT" />
        </Item>
        <Item>
          <SeatInfo ref="SEAT_VAN_REAR_RIGHT" />
          <SeatAnimInfo ref="SEAT_ANIM_VAN_REAR_RIGHT" />
        </Item>
        <Item>
          <SeatInfo ref="SEAT_POLICET3_EXTRA_LEFT_1" />
          <SeatAnimInfo ref="SEAT_ANIM_SWAT_RANGER_SIDE_LEFT" />
        </Item>
        <Item>
          <SeatInfo ref="SEAT_POLICET3_EXTRA_RIGHT_1" />
          <SeatAnimInfo ref="SEAT_ANIM_SWAT_RANGER_SIDE_RIGHT" />
        </Item>
      </Seats>
      <EntryPoints>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_POLICET3_FRONT_LEFT" />
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_VAN_FRONT_LEFT" />
        </Item>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_POLICET3_FRONT_RIGHT" />
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_VAN_FRONT_RIGHT" />
        </Item>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_VAN_REAR_LEFT" />
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_VAN_REAR_LEFT" />
        </Item>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_VAN_REAR_RIGHT" />
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_VAN_REAR_RIGHT" />
        </Item>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_POLICET3_SIDE_LEFT_1" />
          <EntryPointAnimInfo ref="ENTRY_POINT_SWAT_RANGER_SIDE_LEFT" />
        </Item>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_POLICET3_SIDE_RIGHT_1" />
          <EntryPointAnimInfo ref="ENTRY_POINT_SWAT_RANGER_SIDE_RIGHT" />
        </Item>
      </EntryPoints>
      <LayoutFlags>StreamAnims UseVanOpenDoorBlendParams UseDoorOscillation UseLeanSteerAnims
        UseLowerDoorBlockTest</LayoutFlags>
      <BicycleInfo ref="NULL" />
      <AnimRateSet ref="NULL" />
      <HandsUpClipSetId>busted_vehicle_van</HandsUpClipSetId>
      <SteeringWheelOffset x="0.006700" y="0.330000" z="0.270000" />
      <MaxXAcceleration value="3.000000" />
      <BodyLeanXApproachSpeed value="15.000000" />
      <BodyLeanXSmallDelta value="0.100000" />
      <LookBackApproachSpeedScale value="1.000000" />
      <FirstPersonAdditiveIdleClipSets>
        <Item>clipset@veh@van@ds@idle_a</Item>
        <Item>clipset@veh@van@ds@idle_b</Item>
        <Item>clipset@veh@van@ds@idle_c</Item>
        <Item>clipset@veh@van@ds@idle_d</Item>
        <Item>clipset@veh@van@ds@idle_e</Item>
      </FirstPersonAdditiveIdleClipSets>
      <FirstPersonRoadRageClipSets>
        <Item>clipset@veh@van@ds@hit_wheel@idle_a</Item>
        <Item>clipset@veh@van@ds@hit_wheel@idle_b</Item>
        <Item>clipset@veh@van@ds@hit_wheel@idle_c</Item>
      </FirstPersonRoadRageClipSets>
      <CellphoneClipsets>
        <CellphoneClipsetDS>ANIM_GROUP_CELLPHONE_IN_CAR_DS</CellphoneClipsetDS>
        <CellphoneClipsetPS>ANIM_GROUP_CELLPHONE_IN_CAR_PS</CellphoneClipsetPS>
        <CellphoneClipsetDS_FPS>ANIM_GROUP_CELLPHONE_IN_CAR_DS_FPS</CellphoneClipsetDS_FPS>
        <CellphoneClipsetPS_FPS>ANIM_GROUP_CELLPHONE_IN_CAR_DS_FPS</CellphoneClipsetPS_FPS>
      </CellphoneClipsets>
    </Item>
  </VehicleLayoutInfos>
  <VehicleScenarioLayoutInfos />
  <SeatOverrideAnimInfos />
  <InVehicleOverrideInfos />
  <FirstPersonDriveByLookAroundData>
    <Item type="CFirstPersonDriveByLookAroundData">
      <Name>STD_CASTIGATOR_FRONT_LEFT</Name>
      <AllowLookback value="true" />
      <HeadingLimits x="-189.000000" y="170.000000" />
      <DataLeft>
        <Offsets>
          <Item>
            <Offset value="-0.380000" />
            <AngleToBlendInOffset x="60.000000" y="140.000000" />
          </Item>
          <Item>
            <Offset value="0.150000" />
            <AngleToBlendInOffset x="60.000000" y="120.000000" />
          </Item>
          <Item>
            <Offset value="-0.015000" />
            <AngleToBlendInOffset x="0.000000" y="20.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-1.000000" y="5.000000" />
        <AngleToBlendInExtraPitch x="30.000000" y="110.000000" />
      </DataLeft>
      <DataRight>
        <Offsets>
          <Item>
            <Offset value="-0.000000" />
            <AngleToBlendInOffset x="0.000000" y="0.000000" />
          </Item>
          <Item>
            <Offset value="-0.000000" />
            <AngleToBlendInOffset x="0.000000" y="0.000000" />
          </Item>
          <Item>
            <Offset value="-0.000000" />
            <AngleToBlendInOffset x="0.000000" y="0.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-0.500000" y="-11.000000" />
        <AngleToBlendInExtraPitch x="0.000000" y="90.000000" />
      </DataRight>
      <WheelClipInfo>
        <HeadingLimitsLeft x="0.380000" y="0.430000" />
        <HeadingLimitsRight x="0.480000" y="0.530000" />
        <PitchLimits x="0.470000" y="0.530000" />
        <PitchOffset x="0.000000" y="0.050000" />
        <WheelAngleLimits x="-0.700000" y="0.000000" />
        <WheelAngleOffset x="0.010000" y="0.040000" />
        <MaxWheelOffsetY value="0.170000" />
        <WheelClipLerpInRate value="0.150000" />
        <WheelClipLerpOutRate value="0.180000" />
      </WheelClipInfo>
    </Item>
    <Item type="CFirstPersonDriveByLookAroundData">
      <Name>STD_CASTIGATOR_FRONT_RIGHT</Name>
      <AllowLookback value="true" />
      <HeadingLimits x="-188.000000" y="170.000000" />
      <DataLeft>
        <Offsets>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="0.000000" y="90.000000" />
          </Item>
          <Item>
            <Offset value="-0.000000" />
            <AngleToBlendInOffset x="0.000000" y="190.000000" />
          </Item>
          <Item>
            <Offset value="0.040000" />
            <AngleToBlendInOffset x="0.000000" y="90.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-2.000000" y="-12.500000" />
        <AngleToBlendInExtraPitch x="0.000000" y="90.000000" />
      </DataLeft>
      <DataRight>
        <Offsets>
          <Item>
            <Offset value="0.380000" />
            <AngleToBlendInOffset x="60.000000" y="140.000000" />
          </Item>
          <Item>
            <Offset value="0.080000" />
            <AngleToBlendInOffset x="60.000000" y="120.000000" />
          </Item>
          <Item>
            <Offset value="-0.045000" />
            <AngleToBlendInOffset x="0.000000" y="40.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-1.000000" y="5.000000" />
        <AngleToBlendInExtraPitch x="30.000000" y="110.000000" />
      </DataRight>
      <WheelClipInfo>
        <HeadingLimitsLeft x="0.380000" y="0.430000" />
        <HeadingLimitsRight x="0.480000" y="0.530000" />
        <PitchLimits x="0.470000" y="0.530000" />
        <PitchOffset x="0.000000" y="0.050000" />
        <WheelAngleLimits x="-0.700000" y="0.000000" />
        <WheelAngleOffset x="0.010000" y="0.040000" />
        <MaxWheelOffsetY value="0.170000" />
        <WheelClipLerpInRate value="0.150000" />
        <WheelClipLerpOutRate value="0.180000" />
      </WheelClipInfo>
    </Item>
    <Item type="CFirstPersonDriveByLookAroundData">
      <Name>STD_CASTIGATOR_REAR_LEFT</Name>
      <AllowLookback value="true" />
      <HeadingLimits x="-190.000000" y="150.000000" />
      <DataLeft>
        <Offsets>
          <Item>
            <Offset value="-0.200000" />
            <AngleToBlendInOffset x="20.000000" y="120.000000" />
          </Item>
          <Item>
            <Offset value="0.320000" />
            <AngleToBlendInOffset x="20.000000" y="100.000000" />
          </Item>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="0.000000" y="0.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-10.000000" y="7.000000" />
        <AngleToBlendInExtraPitch x="20.000000" y="100.000000" />
      </DataLeft>
      <DataRight>
        <Offsets>
          <Item>
            <Offset value="-0.140000" />
            <AngleToBlendInOffset x="0.000000" y="190.000000" />
          </Item>
          <Item>
            <Offset value="-0.040000" />
            <AngleToBlendInOffset x="0.000000" y="190.000000" />
          </Item>
          <Item>
            <Offset value="0.030000" />
            <AngleToBlendInOffset x="0.000000" y="150.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-3.000000" y="-10.000000" />
        <AngleToBlendInExtraPitch x="0.000000" y="40.000000" />
      </DataRight>
      <WheelClipInfo>
        <HeadingLimitsLeft x="0.380000" y="0.430000" />
        <HeadingLimitsRight x="0.480000" y="0.530000" />
        <PitchLimits x="0.470000" y="0.530000" />
        <PitchOffset x="0.000000" y="0.050000" />
        <WheelAngleLimits x="-0.700000" y="0.000000" />
        <WheelAngleOffset x="0.010000" y="0.040000" />
        <MaxWheelOffsetY value="0.170000" />
        <WheelClipLerpInRate value="0.150000" />
        <WheelClipLerpOutRate value="0.180000" />
      </WheelClipInfo>
    </Item>
    <Item type="CFirstPersonDriveByLookAroundData">
      <Name>STD_CASTIGATOR_REAR_RIGHT</Name>
      <AllowLookback value="true" />
      <HeadingLimits x="-190.000000" y="150.000000" />
      <DataLeft>
        <Offsets>
          <Item>
            <Offset value="0.140000" />
            <AngleToBlendInOffset x="0.000000" y="190.000000" />
          </Item>
          <Item>
            <Offset value="-0.040000" />
            <AngleToBlendInOffset x="0.000000" y="190.000000" />
          </Item>
          <Item>
            <Offset value="0.030000" />
            <AngleToBlendInOffset x="0.000000" y="150.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-3.000000" y="-10.000000" />
        <AngleToBlendInExtraPitch x="0.000000" y="40.000000" />
      </DataLeft>
      <DataRight>
        <Offsets>
          <Item>
            <Offset value="0.180000" />
            <AngleToBlendInOffset x="20.000000" y="120.000000" />
          </Item>
          <Item>
            <Offset value="0.330000" />
            <AngleToBlendInOffset x="20.000000" y="100.000000" />
          </Item>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="0.000000" y="0.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-8.000000" y="7.000000" />
        <AngleToBlendInExtraPitch x="20.000000" y="100.000000" />
      </DataRight>
      <WheelClipInfo>
        <HeadingLimitsLeft x="0.380000" y="0.430000" />
        <HeadingLimitsRight x="0.480000" y="0.530000" />
        <PitchLimits x="0.470000" y="0.530000" />
        <PitchOffset x="0.000000" y="0.050000" />
        <WheelAngleLimits x="-0.700000" y="0.000000" />
        <WheelAngleOffset x="0.010000" y="0.040000" />
        <MaxWheelOffsetY value="0.170000" />
        <WheelClipLerpInRate value="0.150000" />
        <WheelClipLerpOutRate value="0.180000" />
      </WheelClipInfo>
    </Item>
    <Item type="CFirstPersonDriveByLookAroundData">
      <Name>STD_COQUETTE5_FRONT_LEFT</Name>
      <AllowLookback value="true" />
      <HeadingLimits x="-177.000000" y="155.000000" />
      <DataLeft>
        <Offsets>
          <Item>
            <Offset value="-0.280000" />
            <AngleToBlendInOffset x="0.000000" y="160.000000" />
          </Item>
          <Item>
            <Offset value="0.200000" />
            <AngleToBlendInOffset x="0.000000" y="140.000000" />
          </Item>
          <Item>
            <Offset value="-0.070000" />
            <AngleToBlendInOffset x="0.000000" y="120.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-8.000000" y="15.000000" />
        <AngleToBlendInExtraPitch x="45.000000" y="110.000000" />
      </DataLeft>
      <DataRight>
        <Offsets>
          <Item>
            <Offset value="-0.100000" />
            <AngleToBlendInOffset x="45.000000" y="90.000000" />
          </Item>
          <Item>
            <Offset value="0.125000" />
            <AngleToBlendInOffset x="0.000000" y="180.000000" />
          </Item>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="0.000000" y="90.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-5.000000" y="-8.000000" />
        <AngleToBlendInExtraPitch x="0.000000" y="90.000000" />
      </DataRight>
      <WheelClipInfo>
        <HeadingLimitsLeft x="0.380000" y="0.430000" />
        <HeadingLimitsRight x="0.480000" y="0.530000" />
        <PitchLimits x="0.470000" y="0.530000" />
        <PitchOffset x="0.000000" y="0.020000" />
        <WheelAngleLimits x="-0.700000" y="0.000000" />
        <WheelAngleOffset x="0.010000" y="0.020000" />
        <MaxWheelOffsetY value="0.170000" />
        <WheelClipLerpInRate value="0.150000" />
        <WheelClipLerpOutRate value="0.180000" />
      </WheelClipInfo>
    </Item>
    <Item type="CFirstPersonDriveByLookAroundData">
      <Name>STD_COQUETTE5_FRONT_RIGHT</Name>
      <AllowLookback value="true" />
      <HeadingLimits x="-180.000000" y="150.000000" />
      <DataLeft>
        <Offsets>
          <Item>
            <Offset value="0.025000" />
            <AngleToBlendInOffset x="45.000000" y="90.000000" />
          </Item>
          <Item>
            <Offset value="0.080000" />
            <AngleToBlendInOffset x="0.000000" y="180.000000" />
          </Item>
          <Item>
            <Offset value="-0.000000" />
            <AngleToBlendInOffset x="90.000000" y="180.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-6.000000" y="-8.000000" />
        <AngleToBlendInExtraPitch x="0.000000" y="90.000000" />
      </DataLeft>
      <DataRight>
        <Offsets>
          <Item>
            <Offset value="0.200000" />
            <AngleToBlendInOffset x="40.000000" y="120.000000" />
          </Item>
          <Item>
            <Offset value="0.250000" />
            <AngleToBlendInOffset x="60.000000" y="135.000000" />
          </Item>
          <Item>
            <Offset value="-0.050000" />
            <AngleToBlendInOffset x="45.000000" y="120.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-8.000000" y="5.000000" />
        <AngleToBlendInExtraPitch x="45.000000" y="110.000000" />
      </DataRight>
      <WheelClipInfo>
        <HeadingLimitsLeft x="0.380000" y="0.430000" />
        <HeadingLimitsRight x="0.480000" y="0.530000" />
        <PitchLimits x="0.470000" y="0.530000" />
        <PitchOffset x="0.000000" y="0.050000" />
        <WheelAngleLimits x="-0.700000" y="0.000000" />
        <WheelAngleOffset x="0.010000" y="0.040000" />
        <MaxWheelOffsetY value="0.170000" />
        <WheelClipLerpInRate value="0.150000" />
        <WheelClipLerpOutRate value="0.180000" />
      </WheelClipInfo>
    </Item>
    <Item type="CFirstPersonDriveByLookAroundData">
      <Name>STD_DOMINATOR10_FRONT_LEFT</Name>
      <AllowLookback value="true" />
      <HeadingLimits x="-185.000000" y="160.000000" />
      <DataLeft>
        <Offsets>
          <Item>
            <Offset value="-0.250000" />
            <AngleToBlendInOffset x="45.000000" y="120.000000" />
          </Item>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="0.000000" y="0.000000" />
          </Item>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="0.000000" y="0.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-10.000000" y="5.000000" />
        <AngleToBlendInExtraPitch x="45.000000" y="160.000000" />
      </DataLeft>
      <DataRight>
        <Offsets>
          <Item>
            <Offset value="-0.090000" />
            <AngleToBlendInOffset x="45.000000" y="90.000000" />
          </Item>
          <Item>
            <Offset value="-0.100000" />
            <AngleToBlendInOffset x="80.000000" y="165.000000" />
          </Item>
          <Item>
            <Offset value="0.050000" />
            <AngleToBlendInOffset x="45.000000" y="160.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-6.000000" y="-7.000000" />
        <AngleToBlendInExtraPitch x="65.000000" y="160.000000" />
      </DataRight>
      <WheelClipInfo>
        <HeadingLimitsLeft x="0.380000" y="0.430000" />
        <HeadingLimitsRight x="0.480000" y="0.530000" />
        <PitchLimits x="0.470000" y="0.530000" />
        <PitchOffset x="0.000000" y="0.050000" />
        <WheelAngleLimits x="-0.700000" y="0.000000" />
        <WheelAngleOffset x="0.010000" y="0.040000" />
        <MaxWheelOffsetY value="0.170000" />
        <WheelClipLerpInRate value="0.150000" />
        <WheelClipLerpOutRate value="0.180000" />
      </WheelClipInfo>
    </Item>
    <Item type="CFirstPersonDriveByLookAroundData">
      <Name>STD_DOMINATOR10_FRONT_RIGHT</Name>
      <AllowLookback value="true" />
      <HeadingLimits x="-185.000000" y="160.000000" />
      <DataLeft>
        <Offsets>
          <Item>
            <Offset value="0.060000" />
            <AngleToBlendInOffset x="45.000000" y="90.000000" />
          </Item>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="80.000000" y="165.000000" />
          </Item>
          <Item>
            <Offset value="0.050000" />
            <AngleToBlendInOffset x="120.000000" y="160.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-6.000000" y="-8.000000" />
        <AngleToBlendInExtraPitch x="90.000000" y="190.000000" />
      </DataLeft>
      <DataRight>
        <Offsets>
          <Item>
            <Offset value="0.250000" />
            <AngleToBlendInOffset x="45.000000" y="90.000000" />
          </Item>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="45.000000" y="90.000000" />
          </Item>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="45.000000" y="120.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-10.000000" y="5.000000" />
        <AngleToBlendInExtraPitch x="45.000000" y="110.000000" />
      </DataRight>
      <WheelClipInfo>
        <HeadingLimitsLeft x="0.380000" y="0.430000" />
        <HeadingLimitsRight x="0.480000" y="0.530000" />
        <PitchLimits x="0.470000" y="0.530000" />
        <PitchOffset x="0.000000" y="0.050000" />
        <WheelAngleLimits x="-0.700000" y="0.000000" />
        <WheelAngleOffset x="0.010000" y="0.040000" />
        <MaxWheelOffsetY value="0.170000" />
        <WheelClipLerpInRate value="0.150000" />
        <WheelClipLerpOutRate value="0.180000" />
      </WheelClipInfo>
    </Item>
    <Item type="CFirstPersonDriveByLookAroundData">
      <Name>STD_ENVISAGE_FRONT_LEFT</Name>
      <AllowLookback value="true" />
      <HeadingLimits x="-147.000000" y="165.000000" />
      <DataLeft>
        <Offsets>
          <Item>
            <Offset value="-0.350000" />
            <AngleToBlendInOffset x="40.000000" y="160.000000" />
          </Item>
          <Item>
            <Offset value="0.150000" />
            <AngleToBlendInOffset x="60.000000" y="160.000000" />
          </Item>
          <Item>
            <Offset value="-0.045000" />
            <AngleToBlendInOffset x="25.000000" y="120.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-15.000000" y="10.000000" />
        <AngleToBlendInExtraPitch x="50.000000" y="110.000000" />
      </DataLeft>
      <DataRight>
        <Offsets>
          <Item>
            <Offset value="-0.050000" />
            <AngleToBlendInOffset x="45.000000" y="90.000000" />
          </Item>
          <Item>
            <Offset value="-0.060000" />
            <AngleToBlendInOffset x="0.000000" y="190.000000" />
          </Item>
          <Item>
            <Offset value="0.025000" />
            <AngleToBlendInOffset x="50.000000" y="90.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-3.000000" y="-11.000000" />
        <AngleToBlendInExtraPitch x="25.000000" y="70.000000" />
      </DataRight>
      <WheelClipInfo>
        <HeadingLimitsLeft x="0.200000" y="0.430000" />
        <HeadingLimitsRight x="0.480000" y="0.500000" />
        <PitchLimits x="0.470000" y="0.530000" />
        <PitchOffset x="0.000000" y="0.100000" />
        <WheelAngleLimits x="-0.700000" y="0.000000" />
        <WheelAngleOffset x="0.010000" y="0.040000" />
        <MaxWheelOffsetY value="0.170000" />
        <WheelClipLerpInRate value="0.150000" />
        <WheelClipLerpOutRate value="0.180000" />
      </WheelClipInfo>
    </Item>
    <Item type="CFirstPersonDriveByLookAroundData">
      <Name>STD_ENVISAGE_FRONT_RIGHT</Name>
      <AllowLookback value="true" />
      <HeadingLimits x="-145.000000" y="160.000000" />
      <DataLeft>
        <Offsets>
          <Item>
            <Offset value="0.070000" />
            <AngleToBlendInOffset x="45.000000" y="90.000000" />
          </Item>
          <Item>
            <Offset value="-0.150000" />
            <AngleToBlendInOffset x="0.000000" y="190.000000" />
          </Item>
          <Item>
            <Offset value="-0.020000" />
            <AngleToBlendInOffset x="90.000000" y="140.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-2.000000" y="-8.000000" />
        <AngleToBlendInExtraPitch x="30.000000" y="70.000000" />
      </DataLeft>
      <DataRight>
        <Offsets>
          <Item>
            <Offset value="0.300000" />
            <AngleToBlendInOffset x="40.000000" y="160.000000" />
          </Item>
          <Item>
            <Offset value="0.120000" />
            <AngleToBlendInOffset x="60.000000" y="160.000000" />
          </Item>
          <Item>
            <Offset value="-0.040000" />
            <AngleToBlendInOffset x="30.000000" y="140.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-10.000000" y="10.000000" />
        <AngleToBlendInExtraPitch x="50.000000" y="110.000000" />
      </DataRight>
      <WheelClipInfo>
        <HeadingLimitsLeft x="0.380000" y="0.430000" />
        <HeadingLimitsRight x="0.480000" y="0.530000" />
        <PitchLimits x="0.470000" y="0.530000" />
        <PitchOffset x="0.000000" y="0.050000" />
        <WheelAngleLimits x="-0.700000" y="0.000000" />
        <WheelAngleOffset x="0.010000" y="0.040000" />
        <MaxWheelOffsetY value="0.170000" />
        <WheelClipLerpInRate value="0.150000" />
        <WheelClipLerpOutRate value="0.180000" />
      </WheelClipInfo>
    </Item>
    <Item type="CFirstPersonDriveByLookAroundData">
      <Name>LOW_EUROSX32_FRONT_LEFT</Name>
      <AllowLookback value="true" />
      <HeadingLimits x="-185.000000" y="165.000000" />
      <DataLeft>
        <Offsets>
          <Item>
            <Offset value="-0.380000" />
            <AngleToBlendInOffset x="0.000000" y="165.000000" />
          </Item>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="0.000000" y="0.000000" />
          </Item>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="0.000000" y="0.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-10.000000" y="5.000000" />
        <AngleToBlendInExtraPitch x="50.000000" y="120.000000" />
      </DataLeft>
      <DataRight>
        <Offsets>
          <Item>
            <Offset value="-0.020000" />
            <AngleToBlendInOffset x="50.000000" y="80.000000" />
          </Item>
          <Item>
            <Offset value="0.050000" />
            <AngleToBlendInOffset x="0.000000" y="100.000000" />
          </Item>
          <Item>
            <Offset value="0.040000" />
            <AngleToBlendInOffset x="0.000000" y="130.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-2.500000" y="-7.500000" />
        <AngleToBlendInExtraPitch x="0.000000" y="140.000000" />
      </DataRight>
      <WheelClipInfo>
        <HeadingLimitsLeft x="0.340000" y="0.400000" />
        <HeadingLimitsRight x="0.450000" y="0.500000" />
        <PitchLimits x="0.470000" y="0.530000" />
        <PitchOffset x="0.000000" y="0.065000" />
        <WheelAngleLimits x="-0.700000" y="0.000000" />
        <WheelAngleOffset x="0.000000" y="0.020000" />
        <MaxWheelOffsetY value="0.170000" />
        <WheelClipLerpInRate value="0.150000" />
        <WheelClipLerpOutRate value="0.180000" />
      </WheelClipInfo>
    </Item>
    <Item type="CFirstPersonDriveByLookAroundData">
      <Name>LOW_EUROSX32_FRONT_RIGHT</Name>
      <AllowLookback value="true" />
      <HeadingLimits x="-185.000000" y="165.000000" />
      <DataLeft>
        <Offsets>
          <Item>
            <Offset value="0.040000" />
            <AngleToBlendInOffset x="50.000000" y="80.000000" />
          </Item>
          <Item>
            <Offset value="-0.020000" />
            <AngleToBlendInOffset x="0.000000" y="100.000000" />
          </Item>
          <Item>
            <Offset value="0.040000" />
            <AngleToBlendInOffset x="0.000000" y="130.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-2.500000" y="-6.500000" />
        <AngleToBlendInExtraPitch x="0.000000" y="140.000000" />
      </DataLeft>
      <DataRight>
        <Offsets>
          <Item>
            <Offset value="0.380000" />
            <AngleToBlendInOffset x="0.000000" y="165.000000" />
          </Item>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="0.000000" y="0.000000" />
          </Item>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="0.000000" y="0.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-10.000000" y="5.000000" />
        <AngleToBlendInExtraPitch x="50.000000" y="120.000000" />
      </DataRight>
      <WheelClipInfo>
        <HeadingLimitsLeft x="0.340000" y="0.400000" />
        <HeadingLimitsRight x="0.450000" y="0.500000" />
        <PitchLimits x="0.470000" y="0.530000" />
        <PitchOffset x="0.000000" y="0.065000" />
        <WheelAngleLimits x="-0.700000" y="0.000000" />
        <WheelAngleOffset x="0.000000" y="0.020000" />
        <MaxWheelOffsetY value="0.170000" />
        <WheelClipLerpInRate value="0.150000" />
        <WheelClipLerpOutRate value="0.180000" />
      </WheelClipInfo>
    </Item>
    <Item type="CFirstPersonDriveByLookAroundData">
      <Name>STD_NIOBE_FRONT_LEFT</Name>
      <AllowLookback value="true" />
      <HeadingLimits x="-189.000000" y="165.000000" />
      <DataLeft>
        <Offsets>
          <Item>
            <Offset value="-0.300000" />
            <AngleToBlendInOffset x="85.000000" y="160.000000" />
          </Item>
          <Item>
            <Offset value="0.040000" />
            <AngleToBlendInOffset x="60.000000" y="100.000000" />
          </Item>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="0.000000" y="0.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-5.000000" y="2.500000" />
        <AngleToBlendInExtraPitch x="40.000000" y="150.000000" />
      </DataLeft>
      <DataRight>
        <Offsets>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="0.000000" y="189.000000" />
          </Item>
          <Item>
            <Offset value="-0.050000" />
            <AngleToBlendInOffset x="0.000000" y="189.000000" />
          </Item>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="0.000000" y="0.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="0.000000" y="-10.000000" />
        <AngleToBlendInExtraPitch x="0.000000" y="180.000000" />
      </DataRight>
      <WheelClipInfo>
        <HeadingLimitsLeft x="0.000000" y="0.300000" />
        <HeadingLimitsRight x="0.480000" y="0.530000" />
        <PitchLimits x="0.470000" y="0.530000" />
        <PitchOffset x="0.000000" y="0.065000" />
        <WheelAngleLimits x="-0.700000" y="0.000000" />
        <WheelAngleOffset x="0.000000" y="0.020000" />
        <MaxWheelOffsetY value="0.170000" />
        <WheelClipLerpInRate value="0.150000" />
        <WheelClipLerpOutRate value="0.180000" />
      </WheelClipInfo>
    </Item>
    <Item type="CFirstPersonDriveByLookAroundData">
      <Name>STD_NIOBE_FRONT_RIGHT</Name>
      <AllowLookback value="true" />
      <HeadingLimits x="-189.000000" y="165.000000" />
      <DataLeft>
        <Offsets>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="0.000000" y="0.000000" />
          </Item>
          <Item>
            <Offset value="-0.140000" />
            <AngleToBlendInOffset x="0.000000" y="189.000000" />
          </Item>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="0.000000" y="0.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="0.000000" y="-9.800000" />
        <AngleToBlendInExtraPitch x="0.000000" y="160.000000" />
      </DataLeft>
      <DataRight>
        <Offsets>
          <Item>
            <Offset value="0.300000" />
            <AngleToBlendInOffset x="40.000000" y="150.000000" />
          </Item>
          <Item>
            <Offset value="0.040000" />
            <AngleToBlendInOffset x="0.000000" y="100.000000" />
          </Item>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="0.000000" y="0.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-5.000000" y="8.000000" />
        <AngleToBlendInExtraPitch x="40.000000" y="150.000000" />
      </DataRight>
      <WheelClipInfo>
        <HeadingLimitsLeft x="0.000000" y="0.300000" />
        <HeadingLimitsRight x="0.480000" y="0.530000" />
        <PitchLimits x="0.470000" y="0.530000" />
        <PitchOffset x="0.000000" y="0.065000" />
        <WheelAngleLimits x="-0.700000" y="0.000000" />
        <WheelAngleOffset x="0.000000" y="0.020000" />
        <MaxWheelOffsetY value="0.170000" />
        <WheelClipLerpInRate value="0.150000" />
        <WheelClipLerpOutRate value="0.180000" />
      </WheelClipInfo>
    </Item>
    <Item type="CFirstPersonDriveByLookAroundData">
      <Name>STD_PARAGON3_FRONT_LEFT</Name>
      <AllowLookback value="true" />
      <HeadingLimits x="-190.000000" y="168.000000" />
      <DataLeft>
        <Offsets>
          <Item>
            <Offset value="-0.300000" />
            <AngleToBlendInOffset x="40.000000" y="168.000000" />
          </Item>
          <Item>
            <Offset value="0.080000" />
            <AngleToBlendInOffset x="40.000000" y="168.000000" />
          </Item>
          <Item>
            <Offset value="0.010000" />
            <AngleToBlendInOffset x="40.000000" y="80.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-5.000000" y="5.000000" />
        <AngleToBlendInExtraPitch x="40.000000" y="100.000000" />
      </DataLeft>
      <DataRight>
        <Offsets>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="0.000000" y="0.000000" />
          </Item>
          <Item>
            <Offset value="-0.000000" />
            <AngleToBlendInOffset x="0.000000" y="0.000000" />
          </Item>
          <Item>
            <Offset value="0.040000" />
            <AngleToBlendInOffset x="40.000000" y="90.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-2.000000" y="-14.500000" />
        <AngleToBlendInExtraPitch x="0.000000" y="120.000000" />
      </DataRight>
      <WheelClipInfo>
        <HeadingLimitsLeft x="0.280000" y="0.430000" />
        <HeadingLimitsRight x="0.480000" y="0.530000" />
        <PitchLimits x="0.470000" y="0.530000" />
        <PitchOffset x="0.000000" y="0.010000" />
        <WheelAngleLimits x="-0.700000" y="0.000000" />
        <WheelAngleOffset x="0.010000" y="0.030000" />
        <MaxWheelOffsetY value="0.170000" />
        <WheelClipLerpInRate value="0.150000" />
        <WheelClipLerpOutRate value="0.180000" />
      </WheelClipInfo>
    </Item>
    <Item type="CFirstPersonDriveByLookAroundData">
      <Name>STD_PARAGON3_FRONT_RIGHT</Name>
      <AllowLookback value="true" />
      <HeadingLimits x="-190.000000" y="168.000000" />
      <DataLeft>
        <Offsets>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="0.000000" y="0.000000" />
          </Item>
          <Item>
            <Offset value="-0.000000" />
            <AngleToBlendInOffset x="0.000000" y="0.000000" />
          </Item>
          <Item>
            <Offset value="0.040000" />
            <AngleToBlendInOffset x="40.000000" y="90.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-2.000000" y="-14.500000" />
        <AngleToBlendInExtraPitch x="0.000000" y="120.000000" />
      </DataLeft>
      <DataRight>
        <Offsets>
          <Item>
            <Offset value="0.330000" />
            <AngleToBlendInOffset x="40.000000" y="168.000000" />
          </Item>
          <Item>
            <Offset value="0.080000" />
            <AngleToBlendInOffset x="40.000000" y="168.000000" />
          </Item>
          <Item>
            <Offset value="-0.010000" />
            <AngleToBlendInOffset x="40.000000" y="80.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-5.000000" y="5.000000" />
        <AngleToBlendInExtraPitch x="40.000000" y="100.000000" />
      </DataRight>
      <WheelClipInfo>
        <HeadingLimitsLeft x="0.280000" y="0.430000" />
        <HeadingLimitsRight x="0.480000" y="0.530000" />
        <PitchLimits x="0.470000" y="0.530000" />
        <PitchOffset x="0.000000" y="0.010000" />
        <WheelAngleLimits x="-0.700000" y="0.000000" />
        <WheelAngleOffset x="0.010000" y="0.030000" />
        <MaxWheelOffsetY value="0.170000" />
        <WheelClipLerpInRate value="0.150000" />
        <WheelClipLerpOutRate value="0.180000" />
      </WheelClipInfo>
    </Item>
    <Item type="CFirstPersonDriveByLookAroundData">
      <Name>LOW_PIPISTRELLO_FRONT_LEFT</Name>
      <AllowLookback value="false" />
      <HeadingLimits x="-90.000000" y="155.000000" />
      <DataLeft>
        <Offsets>
          <Item>
            <Offset value="-0.200000" />
            <AngleToBlendInOffset x="0.000000" y="100.000000" />
          </Item>
          <Item>
            <Offset value="0.150000" />
            <AngleToBlendInOffset x="0.000000" y="120.000000" />
          </Item>
          <Item>
            <Offset value="-0.015000" />
            <AngleToBlendInOffset x="0.000000" y="155.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="1.000000" y="5.000000" />
        <AngleToBlendInExtraPitch x="0.000000" y="90.000000" />
      </DataLeft>
      <DataRight>
        <Offsets>
          <Item>
            <Offset value="-0.020000" />
            <AngleToBlendInOffset x="0.000000" y="90.000000" />
          </Item>
          <Item>
            <Offset value="0.050000" />
            <AngleToBlendInOffset x="0.000000" y="90.000000" />
          </Item>
          <Item>
            <Offset value="0.040000" />
            <AngleToBlendInOffset x="0.000000" y="90.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-6.500000" y="-14.000000" />
        <AngleToBlendInExtraPitch x="0.000000" y="90.000000" />
      </DataRight>
      <WheelClipInfo>
        <HeadingLimitsLeft x="0.340000" y="0.400000" />
        <HeadingLimitsRight x="0.450000" y="0.500000" />
        <PitchLimits x="0.470000" y="0.530000" />
        <PitchOffset x="0.000000" y="0.065000" />
        <WheelAngleLimits x="-0.700000" y="0.000000" />
        <WheelAngleOffset x="0.000000" y="0.020000" />
        <MaxWheelOffsetY value="0.170000" />
        <WheelClipLerpInRate value="0.150000" />
        <WheelClipLerpOutRate value="0.180000" />
      </WheelClipInfo>
    </Item>
    <Item type="CFirstPersonDriveByLookAroundData">
      <Name>LOW_PIPISTRELLO_FRONT_RIGHT</Name>
      <AllowLookback value="false" />
      <HeadingLimits x="-90.000000" y="155.000000" />
      <DataLeft>
        <Offsets>
          <Item>
            <Offset value="-0.020000" />
            <AngleToBlendInOffset x="50.000000" y="90.000000" />
          </Item>
          <Item>
            <Offset value="0.050000" />
            <AngleToBlendInOffset x="0.000000" y="90.000000" />
          </Item>
          <Item>
            <Offset value="0.040000" />
            <AngleToBlendInOffset x="0.000000" y="90.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-7.000000" y="-18.000000" />
        <AngleToBlendInExtraPitch x="0.000000" y="120.000000" />
      </DataLeft>
      <DataRight>
        <Offsets>
          <Item>
            <Offset value="0.200000" />
            <AngleToBlendInOffset x="0.000000" y="100.000000" />
          </Item>
          <Item>
            <Offset value="0.150000" />
            <AngleToBlendInOffset x="0.000000" y="120.000000" />
          </Item>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="0.000000" y="0.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-5.000000" y="0.000000" />
        <AngleToBlendInExtraPitch x="0.000000" y="155.000000" />
      </DataRight>
      <WheelClipInfo>
        <HeadingLimitsLeft x="0.340000" y="0.400000" />
        <HeadingLimitsRight x="0.450000" y="0.500000" />
        <PitchLimits x="0.470000" y="0.530000" />
        <PitchOffset x="0.000000" y="0.065000" />
        <WheelAngleLimits x="-0.700000" y="0.000000" />
        <WheelAngleOffset x="0.000000" y="0.020000" />
        <MaxWheelOffsetY value="0.170000" />
        <WheelClipLerpInRate value="0.150000" />
        <WheelClipLerpOutRate value="0.180000" />
      </WheelClipInfo>
    </Item>
    <Item type="CFirstPersonDriveByLookAroundData">
      <Name>STD_POLIMPALER5_FRONT_LEFT</Name>
      <AllowLookback value="true" />
      <HeadingLimits x="-105.000000" y="160.000000" />
      <DataLeft>
        <Offsets>
          <Item>
            <Offset value="-0.380000" />
            <AngleToBlendInOffset x="20.000000" y="100.000000" />
          </Item>
          <Item>
            <Offset value="0.100000" />
            <AngleToBlendInOffset x="40.000000" y="100.000000" />
          </Item>
          <Item>
            <Offset value="0.050000" />
            <AngleToBlendInOffset x="0.000000" y="50.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-15.000000" y="10.000000" />
        <AngleToBlendInExtraPitch x="45.000000" y="110.000000" />
      </DataLeft>
      <DataRight>
        <Offsets>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="0.000000" y="0.000000" />
          </Item>
          <Item>
            <Offset value="0.250000" />
            <AngleToBlendInOffset x="0.000000" y="105.000000" />
          </Item>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="0.000000" y="0.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-2.000000" y="-10.000000" />
        <AngleToBlendInExtraPitch x="0.000000" y="90.000000" />
      </DataRight>
      <WheelClipInfo>
        <HeadingLimitsLeft x="0.380000" y="0.430000" />
        <HeadingLimitsRight x="0.480000" y="0.530000" />
        <PitchLimits x="0.470000" y="0.530000" />
        <PitchOffset x="0.000000" y="0.050000" />
        <WheelAngleLimits x="-0.700000" y="0.000000" />
        <WheelAngleOffset x="0.010000" y="0.040000" />
        <MaxWheelOffsetY value="0.170000" />
        <WheelClipLerpInRate value="0.150000" />
        <WheelClipLerpOutRate value="0.180000" />
      </WheelClipInfo>
    </Item>
    <Item type="CFirstPersonDriveByLookAroundData">
      <Name>STD_POLIMPALER5_FRONT_RIGHT</Name>
      <AllowLookback value="true" />
      <HeadingLimits x="-105.000000" y="160.000000" />
      <DataLeft>
        <Offsets>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="0.000000" y="0.000000" />
          </Item>
          <Item>
            <Offset value="0.250000" />
            <AngleToBlendInOffset x="0.000000" y="105.000000" />
          </Item>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="0.000000" y="190.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-2.000000" y="-10.000000" />
        <AngleToBlendInExtraPitch x="0.000000" y="90.000000" />
      </DataLeft>
      <DataRight>
        <Offsets>
          <Item>
            <Offset value="0.400000" />
            <AngleToBlendInOffset x="20.000000" y="100.000000" />
          </Item>
          <Item>
            <Offset value="0.200000" />
            <AngleToBlendInOffset x="40.000000" y="100.000000" />
          </Item>
          <Item>
            <Offset value="-0.025000" />
            <AngleToBlendInOffset x="0.000000" y="90.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-15.000000" y="20.000000" />
        <AngleToBlendInExtraPitch x="45.000000" y="110.000000" />
      </DataRight>
      <WheelClipInfo>
        <HeadingLimitsLeft x="0.380000" y="0.430000" />
        <HeadingLimitsRight x="0.480000" y="0.530000" />
        <PitchLimits x="0.470000" y="0.530000" />
        <PitchOffset x="0.000000" y="0.050000" />
        <WheelAngleLimits x="-0.700000" y="0.000000" />
        <WheelAngleOffset x="0.010000" y="0.040000" />
        <MaxWheelOffsetY value="0.170000" />
        <WheelClipLerpInRate value="0.150000" />
        <WheelClipLerpOutRate value="0.180000" />
      </WheelClipInfo>
    </Item>
    <Item type="CFirstPersonDriveByLookAroundData">
      <Name>STD_POLIMPALER5_REAR_LEFT</Name>
      <AllowLookback value="true" />
      <HeadingLimits x="-190.000000" y="160.000000" />
      <DataLeft>
        <Offsets>
          <Item>
            <Offset value="-0.400000" />
            <AngleToBlendInOffset x="25.000000" y="150.000000" />
          </Item>
          <Item>
            <Offset value="0.350000" />
            <AngleToBlendInOffset x="20.000000" y="160.000000" />
          </Item>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="0.000000" y="120.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-10.000000" y="10.000000" />
        <AngleToBlendInExtraPitch x="20.000000" y="100.000000" />
      </DataLeft>
      <DataRight>
        <Offsets>
          <Item>
            <Offset value="-0.100000" />
            <AngleToBlendInOffset x="0.000000" y="90.000000" />
          </Item>
          <Item>
            <Offset value="0.050000" />
            <AngleToBlendInOffset x="0.000000" y="90.000000" />
          </Item>
          <Item>
            <Offset value="0.075000" />
            <AngleToBlendInOffset x="0.000000" y="100.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="0.000000" y="0.000000" />
        <AngleToBlendInExtraPitch x="0.000000" y="0.000000" />
      </DataRight>
      <WheelClipInfo>
        <HeadingLimitsLeft x="0.380000" y="0.430000" />
        <HeadingLimitsRight x="0.480000" y="0.530000" />
        <PitchLimits x="0.470000" y="0.530000" />
        <PitchOffset x="0.000000" y="0.050000" />
        <WheelAngleLimits x="-0.700000" y="0.000000" />
        <WheelAngleOffset x="0.010000" y="0.040000" />
        <MaxWheelOffsetY value="0.170000" />
        <WheelClipLerpInRate value="0.150000" />
        <WheelClipLerpOutRate value="0.180000" />
      </WheelClipInfo>
    </Item>
    <Item type="CFirstPersonDriveByLookAroundData">
      <Name>STD_POLIMPALER5_REAR_RIGHT</Name>
      <AllowLookback value="true" />
      <HeadingLimits x="-190.000000" y="160.000000" />
      <DataLeft>
        <Offsets>
          <Item>
            <Offset value="0.100000" />
            <AngleToBlendInOffset x="0.000000" y="90.000000" />
          </Item>
          <Item>
            <Offset value="0.050000" />
            <AngleToBlendInOffset x="0.000000" y="90.000000" />
          </Item>
          <Item>
            <Offset value="0.075000" />
            <AngleToBlendInOffset x="0.000000" y="100.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="0.000000" y="0.000000" />
        <AngleToBlendInExtraPitch x="0.000000" y="0.000000" />
      </DataLeft>
      <DataRight>
        <Offsets>
          <Item>
            <Offset value="0.400000" />
            <AngleToBlendInOffset x="25.000000" y="150.000000" />
          </Item>
          <Item>
            <Offset value="0.350000" />
            <AngleToBlendInOffset x="20.000000" y="160.000000" />
          </Item>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="0.000000" y="120.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-10.000000" y="10.000000" />
        <AngleToBlendInExtraPitch x="20.000000" y="100.000000" />
      </DataRight>
      <WheelClipInfo>
        <HeadingLimitsLeft x="0.380000" y="0.430000" />
        <HeadingLimitsRight x="0.480000" y="0.530000" />
        <PitchLimits x="0.470000" y="0.530000" />
        <PitchOffset x="0.000000" y="0.050000" />
        <WheelAngleLimits x="-0.700000" y="0.000000" />
        <WheelAngleOffset x="0.010000" y="0.040000" />
        <MaxWheelOffsetY value="0.170000" />
        <WheelClipLerpInRate value="0.150000" />
        <WheelClipLerpOutRate value="0.180000" />
      </WheelClipInfo>
    </Item>
    <Item type="CFirstPersonDriveByLookAroundData">
      <Name>STD_POLIMPALER6_FRONT_LEFT</Name>
      <AllowLookback value="true" />
      <HeadingLimits x="-190.000000" y="160.000000" />
      <DataLeft>
        <Offsets>
          <Item>
            <Offset value="-0.350000" />
            <AngleToBlendInOffset x="40.000000" y="160.000000" />
          </Item>
          <Item>
            <Offset value="0.150000" />
            <AngleToBlendInOffset x="60.000000" y="160.000000" />
          </Item>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="60.000000" y="120.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-15.000000" y="8.000000" />
        <AngleToBlendInExtraPitch x="50.000000" y="110.000000" />
      </DataLeft>
      <DataRight>
        <Offsets>
          <Item>
            <Offset value="-0.030000" />
            <AngleToBlendInOffset x="45.000000" y="130.000000" />
          </Item>
          <Item>
            <Offset value="-0.110000" />
            <AngleToBlendInOffset x="0.000000" y="130.000000" />
          </Item>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="0.000000" y="90.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-1.500000" y="-7.000000" />
        <AngleToBlendInExtraPitch x="0.000000" y="190.000000" />
      </DataRight>
      <WheelClipInfo>
        <HeadingLimitsLeft x="0.380000" y="0.430000" />
        <HeadingLimitsRight x="0.480000" y="0.530000" />
        <PitchLimits x="0.470000" y="0.530000" />
        <PitchOffset x="0.000000" y="0.050000" />
        <WheelAngleLimits x="-0.700000" y="0.000000" />
        <WheelAngleOffset x="0.010000" y="0.040000" />
        <MaxWheelOffsetY value="0.170000" />
        <WheelClipLerpInRate value="0.150000" />
        <WheelClipLerpOutRate value="0.180000" />
      </WheelClipInfo>
    </Item>
    <Item type="CFirstPersonDriveByLookAroundData">
      <Name>STD_POLIMPALER6_FRONT_RIGHT</Name>
      <AllowLookback value="true" />
      <HeadingLimits x="-190.000000" y="160.000000" />
      <DataLeft>
        <Offsets>
          <Item>
            <Offset value="0.050000" />
            <AngleToBlendInOffset x="45.000000" y="90.000000" />
          </Item>
          <Item>
            <Offset value="-0.150000" />
            <AngleToBlendInOffset x="0.000000" y="100.000000" />
          </Item>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="0.000000" y="90.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-1.500000" y="-7.000000" />
        <AngleToBlendInExtraPitch x="0.000000" y="190.000000" />
      </DataLeft>
      <DataRight>
        <Offsets>
          <Item>
            <Offset value="0.400000" />
            <AngleToBlendInOffset x="40.000000" y="160.000000" />
          </Item>
          <Item>
            <Offset value="0.150000" />
            <AngleToBlendInOffset x="60.000000" y="160.000000" />
          </Item>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="60.000000" y="120.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-15.000000" y="8.000000" />
        <AngleToBlendInExtraPitch x="50.000000" y="110.000000" />
      </DataRight>
      <WheelClipInfo>
        <HeadingLimitsLeft x="0.380000" y="0.430000" />
        <HeadingLimitsRight x="0.480000" y="0.530000" />
        <PitchLimits x="0.470000" y="0.530000" />
        <PitchOffset x="0.000000" y="0.050000" />
        <WheelAngleLimits x="-0.700000" y="0.000000" />
        <WheelAngleOffset x="0.010000" y="0.040000" />
        <MaxWheelOffsetY value="0.170000" />
        <WheelClipLerpInRate value="0.150000" />
        <WheelClipLerpOutRate value="0.180000" />
      </WheelClipInfo>
    </Item>
    <Item type="CFirstPersonDriveByLookAroundData">
      <Name>STD_POLIMPALER6_REAR_LEFT</Name>
      <AllowLookback value="true" />
      <HeadingLimits x="-190.000000" y="160.000000" />
      <DataLeft>
        <Offsets>
          <Item>
            <Offset value="-0.400000" />
            <AngleToBlendInOffset x="0.000000" y="160.000000" />
          </Item>
          <Item>
            <Offset value="0.300000" />
            <AngleToBlendInOffset x="0.000000" y="160.000000" />
          </Item>
          <Item>
            <Offset value="-0.025000" />
            <AngleToBlendInOffset x="0.000000" y="90.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-10.000000" y="6.000000" />
        <AngleToBlendInExtraPitch x="20.000000" y="100.000000" />
      </DataLeft>
      <DataRight>
        <Offsets>
          <Item>
            <Offset value="-0.125000" />
            <AngleToBlendInOffset x="0.000000" y="90.000000" />
          </Item>
          <Item>
            <Offset value="-0.010000" />
            <AngleToBlendInOffset x="0.000000" y="90.000000" />
          </Item>
          <Item>
            <Offset value="0.035000" />
            <AngleToBlendInOffset x="0.000000" y="140.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-2.000000" y="10.000000" />
        <AngleToBlendInExtraPitch x="0.000000" y="90.000000" />
      </DataRight>
      <WheelClipInfo>
        <HeadingLimitsLeft x="0.380000" y="0.430000" />
        <HeadingLimitsRight x="0.480000" y="0.530000" />
        <PitchLimits x="0.470000" y="0.530000" />
        <PitchOffset x="0.000000" y="0.050000" />
        <WheelAngleLimits x="-0.700000" y="0.000000" />
        <WheelAngleOffset x="0.010000" y="0.040000" />
        <MaxWheelOffsetY value="0.170000" />
        <WheelClipLerpInRate value="0.150000" />
        <WheelClipLerpOutRate value="0.180000" />
      </WheelClipInfo>
    </Item>
    <Item type="CFirstPersonDriveByLookAroundData">
      <Name>STD_POLIMPALER6_REAR_RIGHT</Name>
      <AllowLookback value="true" />
      <HeadingLimits x="-190.000000" y="160.000000" />
      <DataLeft>
        <Offsets>
          <Item>
            <Offset value="0.150000" />
            <AngleToBlendInOffset x="0.000000" y="90.000000" />
          </Item>
          <Item>
            <Offset value="-0.010000" />
            <AngleToBlendInOffset x="0.000000" y="90.000000" />
          </Item>
          <Item>
            <Offset value="0.035000" />
            <AngleToBlendInOffset x="0.000000" y="140.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-2.000000" y="10.000000" />
        <AngleToBlendInExtraPitch x="0.000000" y="90.000000" />
      </DataLeft>
      <DataRight>
        <Offsets>
          <Item>
            <Offset value="0.400000" />
            <AngleToBlendInOffset x="0.000000" y="160.000000" />
          </Item>
          <Item>
            <Offset value="0.300000" />
            <AngleToBlendInOffset x="0.000000" y="160.000000" />
          </Item>
          <Item>
            <Offset value="-0.025000" />
            <AngleToBlendInOffset x="0.000000" y="90.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-10.000000" y="6.000000" />
        <AngleToBlendInExtraPitch x="20.000000" y="100.000000" />
      </DataRight>
      <WheelClipInfo>
        <HeadingLimitsLeft x="0.380000" y="0.430000" />
        <HeadingLimitsRight x="0.480000" y="0.530000" />
        <PitchLimits x="0.470000" y="0.530000" />
        <PitchOffset x="0.000000" y="0.050000" />
        <WheelAngleLimits x="-0.700000" y="0.000000" />
        <WheelAngleOffset x="0.010000" y="0.040000" />
        <MaxWheelOffsetY value="0.170000" />
        <WheelClipLerpInRate value="0.150000" />
        <WheelClipLerpOutRate value="0.180000" />
      </WheelClipInfo>
    </Item>
    <Item type="CFirstPersonDriveByLookAroundData">
      <Name>VAN_POLICET3_FRONT_LEFT</Name>
      <AllowLookback value="false" />
      <HeadingLimits x="-100.000000" y="115.000000" />
      <DataLeft>
        <Offsets>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="0.000000" y="0.000000" />
          </Item>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="0.000000" y="0.000000" />
          </Item>
          <Item>
            <Offset value="0.050000" />
            <AngleToBlendInOffset x="20.000000" y="50.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-4.000000" y="40.000000" />
        <AngleToBlendInExtraPitch x="20.000000" y="50.000000" />
      </DataLeft>
      <DataRight>
        <Offsets>
          <Item>
            <Offset value="-0.100000" />
            <AngleToBlendInOffset x="0.000000" y="90.000000" />
          </Item>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="0.000000" y="0.000000" />
          </Item>
          <Item>
            <Offset value="0.050000" />
            <AngleToBlendInOffset x="50.000000" y="80.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-10.000000" y="10.500000" />
        <AngleToBlendInExtraPitch x="0.000000" y="80.000000" />
      </DataRight>
      <WheelClipInfo>
        <HeadingLimitsLeft x="0.380000" y="0.430000" />
        <HeadingLimitsRight x="0.480000" y="0.530000" />
        <PitchLimits x="0.470000" y="0.530000" />
        <PitchOffset x="0.000000" y="0.050000" />
        <WheelAngleLimits x="-0.700000" y="0.000000" />
        <WheelAngleOffset x="0.010000" y="0.040000" />
        <MaxWheelOffsetY value="0.170000" />
        <WheelClipLerpInRate value="0.150000" />
        <WheelClipLerpOutRate value="0.180000" />
      </WheelClipInfo>
    </Item>
    <Item type="CFirstPersonDriveByLookAroundData">
      <Name>VAN_POLICET3_FRONT_RIGHT</Name>
      <AllowLookback value="false" />
      <HeadingLimits x="-133.000000" y="115.000000" />
      <DataLeft>
        <Offsets>
          <Item>
            <Offset value="0.100000" />
            <AngleToBlendInOffset x="0.000000" y="90.000000" />
          </Item>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="0.000000" y="0.000000" />
          </Item>
          <Item>
            <Offset value="0.050000" />
            <AngleToBlendInOffset x="50.000000" y="80.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-1.000000" y="-14.500000" />
        <AngleToBlendInExtraPitch x="0.000000" y="80.000000" />
      </DataLeft>
      <DataRight>
        <Offsets>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="0.000000" y="0.000000" />
          </Item>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="0.000000" y="0.000000" />
          </Item>
          <Item>
            <Offset value="0.050000" />
            <AngleToBlendInOffset x="20.000000" y="50.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="0.000000" y="-10.000000" />
        <AngleToBlendInExtraPitch x="20.000000" y="50.000000" />
      </DataRight>
      <WheelClipInfo>
        <HeadingLimitsLeft x="0.380000" y="0.430000" />
        <HeadingLimitsRight x="0.480000" y="0.530000" />
        <PitchLimits x="0.470000" y="0.530000" />
        <PitchOffset x="0.000000" y="0.050000" />
        <WheelAngleLimits x="-0.700000" y="0.000000" />
        <WheelAngleOffset x="0.010000" y="0.040000" />
        <MaxWheelOffsetY value="0.170000" />
        <WheelClipLerpInRate value="0.150000" />
        <WheelClipLerpOutRate value="0.180000" />
      </WheelClipInfo>
    </Item>
    <Item type="CFirstPersonDriveByLookAroundData">
      <Name>VAN_POLICET3_REAR_LEFT</Name>
      <AllowLookback value="true" />
      <HeadingLimits x="-104.000000" y="-30.000000" />
      <DataLeft>
        <Offsets>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="0.000000" y="0.000000" />
          </Item>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="0.000000" y="0.000000" />
          </Item>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="0.000000" y="0.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-17.000000" y="10.000000" />
        <AngleToBlendInExtraPitch x="0.000000" y="0.000000" />
      </DataLeft>
      <DataRight>
        <Offsets>
          <Item>
            <Offset value="-0.150000" />
            <AngleToBlendInOffset x="0.000000" y="50.000000" />
          </Item>
          <Item>
            <Offset value="0.050000" />
            <AngleToBlendInOffset x="0.000000" y="50.000000" />
          </Item>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="0.000000" y="0.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-17.000000" y="10.000000" />
        <AngleToBlendInExtraPitch x="0.000000" y="0.000000" />
      </DataRight>
      <WheelClipInfo>
        <HeadingLimitsLeft x="0.380000" y="0.430000" />
        <HeadingLimitsRight x="0.480000" y="0.530000" />
        <PitchLimits x="0.470000" y="0.530000" />
        <PitchOffset x="0.000000" y="0.050000" />
        <WheelAngleLimits x="-0.700000" y="0.000000" />
        <WheelAngleOffset x="0.010000" y="0.040000" />
        <MaxWheelOffsetY value="0.170000" />
        <WheelClipLerpInRate value="0.150000" />
        <WheelClipLerpOutRate value="0.180000" />
      </WheelClipInfo>
    </Item>
    <Item type="CFirstPersonDriveByLookAroundData">
      <Name>VAN_POLICET3_REAR_RIGHT</Name>
      <AllowLookback value="true" />
      <HeadingLimits x="-100.000000" y="-33.000000" />
      <DataLeft>
        <Offsets>
          <Item>
            <Offset value="0.030000" />
            <AngleToBlendInOffset x="0.000000" y="50.000000" />
          </Item>
          <Item>
            <Offset value="0.010000" />
            <AngleToBlendInOffset x="0.000000" y="50.000000" />
          </Item>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="0.000000" y="0.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-15.000000" y="7.000000" />
        <AngleToBlendInExtraPitch x="0.000000" y="0.000000" />
      </DataLeft>
      <DataRight>
        <Offsets>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="0.000000" y="0.000000" />
          </Item>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="0.000000" y="0.000000" />
          </Item>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="0.000000" y="0.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-15.000000" y="10.000000" />
        <AngleToBlendInExtraPitch x="0.000000" y="0.000000" />
      </DataRight>
      <WheelClipInfo>
        <HeadingLimitsLeft x="0.380000" y="0.430000" />
        <HeadingLimitsRight x="0.480000" y="0.530000" />
        <PitchLimits x="0.470000" y="0.530000" />
        <PitchOffset x="0.000000" y="0.050000" />
        <WheelAngleLimits x="-0.700000" y="0.000000" />
        <WheelAngleOffset x="0.010000" y="0.040000" />
        <MaxWheelOffsetY value="0.170000" />
        <WheelClipLerpInRate value="0.150000" />
        <WheelClipLerpOutRate value="0.180000" />
      </WheelClipInfo>
    </Item>
    <Item type="CFirstPersonDriveByLookAroundData">
      <Name>STD_POLDORADO_FRONT_LEFT</Name>
      <AllowLookback value="true" />
      <HeadingLimits x="-95.000000" y="173.000000" />
      <DataLeft>
        <Offsets>
          <Item>
            <Offset value="-0.415000" />
            <AngleToBlendInOffset x="40.000000" y="120.000000" />
          </Item>
          <Item>
            <Offset value="0.150000" />
            <AngleToBlendInOffset x="50.000000" y="120.000000" />
          </Item>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="0.000000" y="0.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-17.000000" y="10.000000" />
        <AngleToBlendInExtraPitch x="5.000000" y="110.000000" />
      </DataLeft>
      <DataRight>
        <Offsets>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="0.000000" y="0.000000" />
          </Item>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="0.000000" y="0.000000" />
          </Item>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="0.000000" y="0.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="0.300000" y="-8.500000" />
        <AngleToBlendInExtraPitch x="0.000000" y="70.000000" />
      </DataRight>
      <WheelClipInfo>
        <HeadingLimitsLeft x="0.380000" y="0.430000" />
        <HeadingLimitsRight x="0.480000" y="0.530000" />
        <PitchLimits x="0.470000" y="0.530000" />
        <PitchOffset x="0.000000" y="0.010000" />
        <WheelAngleLimits x="-0.700000" y="0.000000" />
        <WheelAngleOffset x="0.010000" y="0.020000" />
        <MaxWheelOffsetY value="0.170000" />
        <WheelClipLerpInRate value="0.150000" />
        <WheelClipLerpOutRate value="0.180000" />
      </WheelClipInfo>
    </Item>
    <Item type="CFirstPersonDriveByLookAroundData">
      <Name>STD_POLDORADO_FRONT_RIGHT</Name>
      <AllowLookback value="true" />
      <HeadingLimits x="-95.000000" y="165.000000" />
      <DataLeft>
        <Offsets>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="0.000000" y="79.000000" />
          </Item>
          <Item>
            <Offset value="0.090000" />
            <AngleToBlendInOffset x="0.000000" y="79.000000" />
          </Item>
          <Item>
            <Offset value="-0.000000" />
            <AngleToBlendInOffset x="0.000000" y="0.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="0.300000" y="-8.500000" />
        <AngleToBlendInExtraPitch x="0.000000" y="70.000000" />
      </DataLeft>
      <DataRight>
        <Offsets>
          <Item>
            <Offset value="0.350000" />
            <AngleToBlendInOffset x="60.000000" y="120.000000" />
          </Item>
          <Item>
            <Offset value="0.150000" />
            <AngleToBlendInOffset x="60.000000" y="120.000000" />
          </Item>
          <Item>
            <Offset value="0.030000" />
            <AngleToBlendInOffset x="5.000000" y="60.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-20.000000" y="5.000000" />
        <AngleToBlendInExtraPitch x="5.000000" y="90.000000" />
      </DataRight>
      <WheelClipInfo>
        <HeadingLimitsLeft x="0.380000" y="0.430000" />
        <HeadingLimitsRight x="0.480000" y="0.530000" />
        <PitchLimits x="0.470000" y="0.530000" />
        <PitchOffset x="0.000000" y="0.050000" />
        <WheelAngleLimits x="-0.700000" y="0.000000" />
        <WheelAngleOffset x="0.010000" y="0.040000" />
        <MaxWheelOffsetY value="0.170000" />
        <WheelClipLerpInRate value="0.150000" />
        <WheelClipLerpOutRate value="0.180000" />
      </WheelClipInfo>
    </Item>
    <Item type="CFirstPersonDriveByLookAroundData">
      <Name>STD_POLDORADO_REAR_LEFT</Name>
      <AllowLookback value="true" />
      <HeadingLimits x="-190.000000" y="140.000000" />
      <DataLeft>
        <Offsets>
          <Item>
            <Offset value="-0.250000" />
            <AngleToBlendInOffset x="25.000000" y="140.000000" />
          </Item>
          <Item>
            <Offset value="0.400000" />
            <AngleToBlendInOffset x="0.000000" y="120.000000" />
          </Item>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="0.000000" y="140.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-10.000000" y="10.000000" />
        <AngleToBlendInExtraPitch x="20.000000" y="100.000000" />
      </DataLeft>
      <DataRight>
        <Offsets>
          <Item>
            <Offset value="-0.140000" />
            <AngleToBlendInOffset x="0.000000" y="190.000000" />
          </Item>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="0.000000" y="190.000000" />
          </Item>
          <Item>
            <Offset value="0.050000" />
            <AngleToBlendInOffset x="0.000000" y="190.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-7.500000" y="-9.000000" />
        <AngleToBlendInExtraPitch x="0.000000" y="190.000000" />
      </DataRight>
      <WheelClipInfo>
        <HeadingLimitsLeft x="0.380000" y="0.430000" />
        <HeadingLimitsRight x="0.480000" y="0.530000" />
        <PitchLimits x="0.470000" y="0.530000" />
        <PitchOffset x="0.000000" y="0.050000" />
        <WheelAngleLimits x="-0.700000" y="0.000000" />
        <WheelAngleOffset x="0.010000" y="0.040000" />
        <MaxWheelOffsetY value="0.170000" />
        <WheelClipLerpInRate value="0.150000" />
        <WheelClipLerpOutRate value="0.180000" />
      </WheelClipInfo>
    </Item>
    <Item type="CFirstPersonDriveByLookAroundData">
      <Name>STD_POLDORADO_REAR_RIGHT</Name>
      <AllowLookback value="true" />
      <HeadingLimits x="-190.000000" y="140.000000" />
      <DataLeft>
        <Offsets>
          <Item>
            <Offset value="0.140000" />
            <AngleToBlendInOffset x="0.000000" y="190.000000" />
          </Item>
          <Item>
            <Offset value="-0.050000" />
            <AngleToBlendInOffset x="0.000000" y="190.000000" />
          </Item>
          <Item>
            <Offset value="0.050000" />
            <AngleToBlendInOffset x="0.000000" y="190.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-8.000000" y="-8.500000" />
        <AngleToBlendInExtraPitch x="0.000000" y="190.000000" />
      </DataLeft>
      <DataRight>
        <Offsets>
          <Item>
            <Offset value="0.250000" />
            <AngleToBlendInOffset x="25.000000" y="140.000000" />
          </Item>
          <Item>
            <Offset value="0.400000" />
            <AngleToBlendInOffset x="0.000000" y="120.000000" />
          </Item>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="0.000000" y="140.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-10.000000" y="10.000000" />
        <AngleToBlendInExtraPitch x="20.000000" y="100.000000" />
      </DataRight>
      <WheelClipInfo>
        <HeadingLimitsLeft x="0.380000" y="0.430000" />
        <HeadingLimitsRight x="0.480000" y="0.530000" />
        <PitchLimits x="0.470000" y="0.530000" />
        <PitchOffset x="0.000000" y="0.050000" />
        <WheelAngleLimits x="-0.700000" y="0.000000" />
        <WheelAngleOffset x="0.010000" y="0.040000" />
        <MaxWheelOffsetY value="0.170000" />
        <WheelClipLerpInRate value="0.150000" />
        <WheelClipLerpOutRate value="0.180000" />
      </WheelClipInfo>
    </Item>
    <Item type="CFirstPersonDriveByLookAroundData">
      <Name>STD_VORSCH_FRONT_LEFT</Name>
      <AllowLookback value="true" />
      <HeadingLimits x="-185.000000" y="165.000000" />
      <DataLeft>
        <Offsets>
          <Item>
            <Offset value="-0.300000" />
            <AngleToBlendInOffset x="40.000000" y="160.000000" />
          </Item>
          <Item>
            <Offset value="0.150000" />
            <AngleToBlendInOffset x="60.000000" y="160.000000" />
          </Item>
          <Item>
            <Offset value="-0.045000" />
            <AngleToBlendInOffset x="25.000000" y="120.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-15.000000" y="10.000000" />
        <AngleToBlendInExtraPitch x="50.000000" y="110.000000" />
      </DataLeft>
      <DataRight>
        <Offsets>
          <Item>
            <Offset value="-0.050000" />
            <AngleToBlendInOffset x="45.000000" y="90.000000" />
          </Item>
          <Item>
            <Offset value="-0.060000" />
            <AngleToBlendInOffset x="0.000000" y="190.000000" />
          </Item>
          <Item>
            <Offset value="0.025000" />
            <AngleToBlendInOffset x="50.000000" y="90.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-3.000000" y="-11.000000" />
        <AngleToBlendInExtraPitch x="25.000000" y="70.000000" />
      </DataRight>
      <WheelClipInfo>
        <HeadingLimitsLeft x="0.200000" y="0.430000" />
        <HeadingLimitsRight x="0.480000" y="0.500000" />
        <PitchLimits x="0.470000" y="0.530000" />
        <PitchOffset x="0.000000" y="0.100000" />
        <WheelAngleLimits x="-0.700000" y="0.000000" />
        <WheelAngleOffset x="0.010000" y="0.040000" />
        <MaxWheelOffsetY value="0.170000" />
        <WheelClipLerpInRate value="0.150000" />
        <WheelClipLerpOutRate value="0.180000" />
      </WheelClipInfo>
    </Item>
    <Item type="CFirstPersonDriveByLookAroundData">
      <Name>STD_VORSCH_FRONT_RIGHT</Name>
      <AllowLookback value="true" />
      <HeadingLimits x="-185.000000" y="160.000000" />
      <DataLeft>
        <Offsets>
          <Item>
            <Offset value="0.070000" />
            <AngleToBlendInOffset x="45.000000" y="90.000000" />
          </Item>
          <Item>
            <Offset value="-0.150000" />
            <AngleToBlendInOffset x="0.000000" y="190.000000" />
          </Item>
          <Item>
            <Offset value="-0.020000" />
            <AngleToBlendInOffset x="90.000000" y="140.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-2.000000" y="-8.000000" />
        <AngleToBlendInExtraPitch x="30.000000" y="70.000000" />
      </DataLeft>
      <DataRight>
        <Offsets>
          <Item>
            <Offset value="0.300000" />
            <AngleToBlendInOffset x="40.000000" y="160.000000" />
          </Item>
          <Item>
            <Offset value="0.120000" />
            <AngleToBlendInOffset x="60.000000" y="160.000000" />
          </Item>
          <Item>
            <Offset value="-0.040000" />
            <AngleToBlendInOffset x="30.000000" y="140.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-10.000000" y="10.000000" />
        <AngleToBlendInExtraPitch x="50.000000" y="110.000000" />
      </DataRight>
      <WheelClipInfo>
        <HeadingLimitsLeft x="0.380000" y="0.430000" />
        <HeadingLimitsRight x="0.480000" y="0.530000" />
        <PitchLimits x="0.470000" y="0.530000" />
        <PitchOffset x="0.000000" y="0.050000" />
        <WheelAngleLimits x="-0.700000" y="0.000000" />
        <WheelAngleOffset x="0.010000" y="0.040000" />
        <MaxWheelOffsetY value="0.170000" />
        <WheelClipLerpInRate value="0.150000" />
        <WheelClipLerpOutRate value="0.180000" />
      </WheelClipInfo>
    </Item>
    <Item type="CFirstPersonDriveByLookAroundData">
      <Name>STD_VORSCH_REAR_LEFT</Name>
      <AllowLookback value="true" />
      <HeadingLimits x="-190.000000" y="128.000000" />
      <DataLeft>
        <Offsets>
          <Item>
            <Offset value="-0.120000" />
            <AngleToBlendInOffset x="20.000000" y="120.000000" />
          </Item>
          <Item>
            <Offset value="0.200000" />
            <AngleToBlendInOffset x="20.000000" y="100.000000" />
          </Item>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="0.000000" y="0.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-10.000000" y="7.000000" />
        <AngleToBlendInExtraPitch x="20.000000" y="100.000000" />
      </DataLeft>
      <DataRight>
        <Offsets>
          <Item>
            <Offset value="-0.140000" />
            <AngleToBlendInOffset x="0.000000" y="190.000000" />
          </Item>
          <Item>
            <Offset value="-0.040000" />
            <AngleToBlendInOffset x="0.000000" y="190.000000" />
          </Item>
          <Item>
            <Offset value="0.030000" />
            <AngleToBlendInOffset x="0.000000" y="150.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-3.000000" y="-1.000000" />
        <AngleToBlendInExtraPitch x="0.000000" y="40.000000" />
      </DataRight>
      <WheelClipInfo>
        <HeadingLimitsLeft x="0.380000" y="0.430000" />
        <HeadingLimitsRight x="0.480000" y="0.530000" />
        <PitchLimits x="0.470000" y="0.530000" />
        <PitchOffset x="0.000000" y="0.050000" />
        <WheelAngleLimits x="-0.700000" y="0.000000" />
        <WheelAngleOffset x="0.010000" y="0.040000" />
        <MaxWheelOffsetY value="0.170000" />
        <WheelClipLerpInRate value="0.150000" />
        <WheelClipLerpOutRate value="0.180000" />
      </WheelClipInfo>
    </Item>
    <Item type="CFirstPersonDriveByLookAroundData">
      <Name>STD_VORSCH_REAR_RIGHT</Name>
      <AllowLookback value="true" />
      <HeadingLimits x="-190.000000" y="128.000000" />
      <DataLeft>
        <Offsets>
          <Item>
            <Offset value="0.140000" />
            <AngleToBlendInOffset x="0.000000" y="190.000000" />
          </Item>
          <Item>
            <Offset value="-0.040000" />
            <AngleToBlendInOffset x="0.000000" y="190.000000" />
          </Item>
          <Item>
            <Offset value="0.030000" />
            <AngleToBlendInOffset x="0.000000" y="150.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-3.000000" y="-1.000000" />
        <AngleToBlendInExtraPitch x="0.000000" y="40.000000" />
      </DataLeft>
      <DataRight>
        <Offsets>
          <Item>
            <Offset value="0.120000" />
            <AngleToBlendInOffset x="20.000000" y="120.000000" />
          </Item>
          <Item>
            <Offset value="0.200000" />
            <AngleToBlendInOffset x="20.000000" y="100.000000" />
          </Item>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="0.000000" y="0.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-10.000000" y="7.000000" />
        <AngleToBlendInExtraPitch x="20.000000" y="100.000000" />
      </DataRight>
      <WheelClipInfo>
        <HeadingLimitsLeft x="0.380000" y="0.430000" />
        <HeadingLimitsRight x="0.480000" y="0.530000" />
        <PitchLimits x="0.470000" y="0.530000" />
        <PitchOffset x="0.000000" y="0.050000" />
        <WheelAngleLimits x="-0.700000" y="0.000000" />
        <WheelAngleOffset x="0.010000" y="0.040000" />
        <MaxWheelOffsetY value="0.170000" />
        <WheelClipLerpInRate value="0.150000" />
        <WheelClipLerpOutRate value="0.180000" />
      </WheelClipInfo>
    </Item>
    <Item type="CFirstPersonDriveByLookAroundData">
      <Name>STD_YOSEMITE1500_FRONT_LEFT</Name>
      <AllowLookback value="true" />
      <HeadingLimits x="-178.000000" y="165.000000" />
      <DataLeft>
        <Offsets>
          <Item>
            <Offset value="-0.350000" />
            <AngleToBlendInOffset x="0.000000" y="165.000000" />
          </Item>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="0.000000" y="0.000000" />
          </Item>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="0.000000" y="0.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-10.000000" y="10.000000" />
        <AngleToBlendInExtraPitch x="50.000000" y="100.000000" />
      </DataLeft>
      <DataRight>
        <Offsets>
          <Item>
            <Offset value="-0.120000" />
            <AngleToBlendInOffset x="50.000000" y="120.000000" />
          </Item>
          <Item>
            <Offset value="0.200000" />
            <AngleToBlendInOffset x="50.000000" y="120.000000" />
          </Item>
          <Item>
            <Offset value="0.070000" />
            <AngleToBlendInOffset x="30.000000" y="120.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-2.000000" y="-8.000000" />
        <AngleToBlendInExtraPitch x="0.000000" y="90.000000" />
      </DataRight>
      <WheelClipInfo>
        <HeadingLimitsLeft x="0.380000" y="0.430000" />
        <HeadingLimitsRight x="0.480000" y="0.530000" />
        <PitchLimits x="0.470000" y="0.500000" />
        <PitchOffset x="0.000000" y="0.010000" />
        <WheelAngleLimits x="-0.700000" y="0.000000" />
        <WheelAngleOffset x="0.010000" y="0.020000" />
        <MaxWheelOffsetY value="0.170000" />
        <WheelClipLerpInRate value="0.150000" />
        <WheelClipLerpOutRate value="0.180000" />
      </WheelClipInfo>
    </Item>
    <Item type="CFirstPersonDriveByLookAroundData">
      <Name>STD_YOSEMITE1500_FRONT_RIGHT</Name>
      <AllowLookback value="true" />
      <HeadingLimits x="-178.000000" y="165.000000" />
      <DataLeft>
        <Offsets>
          <Item>
            <Offset value="0.120000" />
            <AngleToBlendInOffset x="50.000000" y="120.000000" />
          </Item>
          <Item>
            <Offset value="0.200000" />
            <AngleToBlendInOffset x="50.000000" y="120.000000" />
          </Item>
          <Item>
            <Offset value="0.070000" />
            <AngleToBlendInOffset x="30.000000" y="120.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-2.000000" y="-8.000000" />
        <AngleToBlendInExtraPitch x="0.000000" y="90.000000" />
      </DataLeft>
      <DataRight>
        <Offsets>
          <Item>
            <Offset value="0.350000" />
            <AngleToBlendInOffset x="0.000000" y="165.000000" />
          </Item>
          <Item>
            <Offset value="-0.020000" />
            <AngleToBlendInOffset x="0.000000" y="45.000000" />
          </Item>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="0.000000" y="0.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-10.000000" y="10.000000" />
        <AngleToBlendInExtraPitch x="10.000000" y="90.000000" />
      </DataRight>
      <WheelClipInfo>
        <HeadingLimitsLeft x="0.380000" y="0.430000" />
        <HeadingLimitsRight x="0.480000" y="0.530000" />
        <PitchLimits x="0.470000" y="0.500000" />
        <PitchOffset x="0.000000" y="0.010000" />
        <WheelAngleLimits x="-0.700000" y="0.000000" />
        <WheelAngleOffset x="0.010000" y="0.020000" />
        <MaxWheelOffsetY value="0.170000" />
        <WheelClipLerpInRate value="0.150000" />
        <WheelClipLerpOutRate value="0.180000" />
      </WheelClipInfo>
    </Item>
  </FirstPersonDriveByLookAroundData>
</CVehicleMetadataMgr>