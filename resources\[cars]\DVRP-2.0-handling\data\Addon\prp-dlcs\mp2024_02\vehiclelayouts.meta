<?xml version="1.0" encoding="UTF-8"?>

<CVehicleMetadataMgr>
  <AnimRateSets />
  <ClipSetMaps>
    <Item type="CClipSetMap">
      <Name>ENTRY_CLIPSET_MAP_HELI_CARGOBOB5_FRONT_LEFT</Name>
      <Maps>
        <Item>
          <ClipSetId>clipset@veh@heli@cargobob5@ds@enter_exit</ClipSetId>
          <VarClipSetId>EntryVarClipSet</VarClipSetId>
          <ConditionFlags />
          <InformationFlags />
        </Item>
      </Maps>
    </Item>
    <Item type="CClipSetMap">
      <Name>EXIT_CLIPSET_MAP_HELI_CARGOBOB5_FRONT_LEFT</Name>
      <Maps>
        <Item>
          <ClipSetId>clipset@veh@heli@cargobob5@ds@enter_exit</ClipSetId>
          <VarClipSetId>ExitVarClipSet</VarClipSetId>
          <ConditionFlags />
          <InformationFlags />
        </Item>
      </Maps>
    </Item>
    <Item type="CClipSetMap">
      <Name>ENTRY_CLIPSET_MAP_HELI_CARGOBOB5_FRONT_RIGHT</Name>
      <Maps>
        <Item>
          <ClipSetId>clipset@veh@heli@cargobob5@ps@enter_exit</ClipSetId>
          <VarClipSetId>EntryVarClipSet</VarClipSetId>
          <ConditionFlags />
          <InformationFlags />
        </Item>
      </Maps>
    </Item>
    <Item type="CClipSetMap">
      <Name>EXIT_CLIPSET_MAP_HELI_CARGOBOB5_FRONT_RIGHT</Name>
      <Maps>
        <Item>
          <ClipSetId>clipset@veh@heli@cargobob5@ps@enter_exit</ClipSetId>
          <VarClipSetId>ExitVarClipSet</VarClipSetId>
          <ConditionFlags />
          <InformationFlags />
        </Item>
      </Maps>
    </Item>
    <Item type="CClipSetMap">
      <Name>INSIDE_CLIPSET_MAP_HELI_CARGOBOB5_FRONT_RIGHT</Name>
      <Maps>
        <Item>
          <ClipSetId>clipset@veh@heli@cargobob5@ps@base</ClipSetId>
          <VarClipSetId>InsideVarClipSet</VarClipSetId>
          <ConditionFlags />
          <InformationFlags />
        </Item>
      </Maps>
    </Item>
    <Item type="CClipSetMap">
      <Name>ENTRY_CLIPSET_MAP_PLANE_DUSTER2_FRONT_LEFT</Name>
      <Maps>
        <Item>
          <ClipSetId>clipset@veh@plane@duster2@front@ds@enter_exit</ClipSetId>
          <VarClipSetId>EntryVarClipSet</VarClipSetId>
          <ConditionFlags />
          <InformationFlags />
        </Item>
      </Maps>
    </Item>
    <Item type="CClipSetMap">
      <Name>EXIT_CLIPSET_MAP_PLANE_DUSTER2_FRONT_LEFT</Name>
      <Maps>
        <Item>
          <ClipSetId>clipset@veh@plane@duster2@front@ds@enter_exit</ClipSetId>
          <VarClipSetId>ExitVarClipSet</VarClipSetId>
          <ConditionFlags />
          <InformationFlags />
        </Item>
      </Maps>
    </Item>
    <Item type="CClipSetMap">
      <Name>ENTRY_CLIPSET_MAP_PLANE_DUSTER2_FRONT_RIGHT</Name>
      <Maps>
        <Item>
          <ClipSetId>clipset@veh@plane@duster2@front@ps@enter_exit</ClipSetId>
          <VarClipSetId>EntryVarClipSet</VarClipSetId>
          <ConditionFlags />
          <InformationFlags />
        </Item>
      </Maps>
    </Item>
    <Item type="CClipSetMap">
      <Name>EXIT_CLIPSET_MAP_PLANE_DUSTER2_FRONT_RIGHT</Name>
      <Maps>
        <Item>
          <ClipSetId>clipset@veh@plane@duster2@front@ps@enter_exit</ClipSetId>
          <VarClipSetId>ExitVarClipSet</VarClipSetId>
          <ConditionFlags />
          <InformationFlags />
        </Item>
      </Maps>
    </Item>
    <Item type="CClipSetMap">
      <Name>INSIDE_CLIPSET_MAP_PLANE_DUSTER2_FRONT</Name>
      <Maps>
        <Item>
          <ClipSetId>clipset@veh@plane@duster2@front@ds@base</ClipSetId>
          <VarClipSetId>InsideVarClipSet</VarClipSetId>
          <ConditionFlags />
          <InformationFlags />
        </Item>
      </Maps>
    </Item>
    <Item type="CClipSetMap">
      <Name>ENTRY_CLIPSET_MAP_POLTERMINUS_FRONT_LEFT</Name>
      <Maps>
        <Item>
          <ClipSetId>anim@veh@van@btype2@ds@enter_exit</ClipSetId>
          <VarClipSetId>EntryVarClipSet</VarClipSetId>
          <ConditionFlags />
          <InformationFlags />
        </Item>
      </Maps>
    </Item>
    <Item type="CClipSetMap">
      <Name>EXIT_CLIPSET_MAP_POLTERMINUS_FRONT_LEFT</Name>
      <Maps>
        <Item>
          <ClipSetId>anim@veh@van@btype2@ds@enter_exit</ClipSetId>
          <VarClipSetId>ExitVarClipSet</VarClipSetId>
          <ConditionFlags />
          <InformationFlags />
        </Item>
      </Maps>
    </Item>
    <Item type="CClipSetMap">
      <Name>ENTRY_CLIPSET_MAP_POLTERMINUS_FRONT_RIGHT</Name>
      <Maps>
        <Item>
          <ClipSetId>anim@veh@van@btype2@ps@enter_exit</ClipSetId>
          <VarClipSetId>EntryVarClipSet</VarClipSetId>
          <ConditionFlags />
          <InformationFlags />
        </Item>
      </Maps>
    </Item>
    <Item type="CClipSetMap">
      <Name>EXIT_CLIPSET_MAP_POLTERMINUS_FRONT_RIGHT</Name>
      <Maps>
        <Item>
          <ClipSetId>anim@veh@van@btype2@ps@enter_exit</ClipSetId>
          <VarClipSetId>ExitVarClipSet</VarClipSetId>
          <ConditionFlags />
          <InformationFlags />
        </Item>
      </Maps>
    </Item>
    <Item type="CClipSetMap">
      <Name>COMMON_CLIPSET_MAP_PLANE_TITAN2</Name>
      <Maps>
        <Item>
          <ClipSetId>clipset@veh@plane@titan2@common@enter_exit</ClipSetId>
          <VarClipSetId>CommonVarClipSet</VarClipSetId>
          <ConditionFlags />
          <InformationFlags />
        </Item>
      </Maps>
    </Item>
    <Item type="CClipSetMap">
      <Name>ENTRY_CLIPSET_MAP_PLANE_TITAN2_FRONT_LEFT</Name>
      <Maps>
        <Item>
          <ClipSetId>clipset@veh@plane@titan2@front@ds@enter_exit</ClipSetId>
          <VarClipSetId>EntryVarClipSet</VarClipSetId>
          <ConditionFlags />
          <InformationFlags />
        </Item>
      </Maps>
    </Item>
    <Item type="CClipSetMap">
      <Name>EXIT_CLIPSET_MAP_PLANE_TITAN2_FRONT_LEFT</Name>
      <Maps>
        <Item>
          <ClipSetId>clipset@veh@plane@titan2@front@ds@enter_exit</ClipSetId>
          <VarClipSetId>ExitVarClipSet</VarClipSetId>
          <ConditionFlags />
          <InformationFlags />
        </Item>
      </Maps>
    </Item>
    <Item type="CClipSetMap">
      <Name>ENTRY_CLIPSET_MAP_PLANE_TITAN2_FRONT_RIGHT</Name>
      <Maps>
        <Item>
          <ClipSetId>clipset@veh@plane@titan2@front@ps@enter_exit</ClipSetId>
          <VarClipSetId>EntryVarClipSet</VarClipSetId>
          <ConditionFlags />
          <InformationFlags />
        </Item>
      </Maps>
    </Item>
    <Item type="CClipSetMap">
      <Name>EXIT_CLIPSET_MAP_PLANE_TITAN2_FRONT_RIGHT</Name>
      <Maps>
        <Item>
          <ClipSetId>clipset@veh@plane@titan2@front@ps@enter_exit</ClipSetId>
          <VarClipSetId>ExitVarClipSet</VarClipSetId>
          <ConditionFlags />
          <InformationFlags />
        </Item>
      </Maps>
    </Item>
    <Item type="CClipSetMap">
      <Name>INSIDE_CLIPSET_MAP_PLANE_TITAN2_REAR_LEFT</Name>
      <Maps>
        <Item>
          <ClipSetId>clipset@veh@plane@titan2@common@enter_exit_rear</ClipSetId>
          <VarClipSetId>InsideVarClipSet</VarClipSetId>
          <ConditionFlags />
          <InformationFlags />
        </Item>
      </Maps>
    </Item>
    <Item type="CClipSetMap">
      <Name>ENTRY_CLIPSET_MAP_PLANE_TITAN2_REAR_LEFT</Name>
      <Maps>
        <Item>
          <ClipSetId>clipset@veh@plane@titan2@rear@rds@enter_exit</ClipSetId>
          <VarClipSetId>EntryVarClipSet</VarClipSetId>
          <ConditionFlags />
          <InformationFlags />
        </Item>
      </Maps>
    </Item>
    <Item type="CClipSetMap">
      <Name>EXIT_CLIPSET_MAP_PLANE_TITAN2_REAR_LEFT</Name>
      <Maps>
        <Item>
          <ClipSetId>clipset@veh@plane@titan2@rear@rds@enter_exit</ClipSetId>
          <VarClipSetId>ExitVarClipSet</VarClipSetId>
          <ConditionFlags />
          <InformationFlags />
        </Item>
      </Maps>
    </Item>
    <Item type="CClipSetMap">
      <Name>INSIDE_CLIPSET_MAP_PLANE_TITAN2_REAR_RIGHT</Name>
      <Maps>
        <Item>
          <ClipSetId>clipset@veh@plane@titan2@common@enter_exit_rear</ClipSetId>
          <VarClipSetId>InsideVarClipSet</VarClipSetId>
          <ConditionFlags />
          <InformationFlags />
        </Item>
      </Maps>
    </Item>
    <Item type="CClipSetMap">
      <Name>ENTRY_CLIPSET_MAP_PLANE_TITAN2_REAR_RIGHT</Name>
      <Maps>
        <Item>
          <ClipSetId>clipset@veh@plane@titan2@rear@rps@enter_exit</ClipSetId>
          <VarClipSetId>EntryVarClipSet</VarClipSetId>
          <ConditionFlags />
          <InformationFlags />
        </Item>
      </Maps>
    </Item>
    <Item type="CClipSetMap">
      <Name>EXIT_CLIPSET_MAP_PLANE_TITAN2_REAR_RIGHT</Name>
      <Maps>
        <Item>
          <ClipSetId>clipset@veh@plane@titan2@rear@rps@enter_exit</ClipSetId>
          <VarClipSetId>ExitVarClipSet</VarClipSetId>
          <ConditionFlags />
          <InformationFlags />
        </Item>
      </Maps>
    </Item>
    <Item type="CClipSetMap">
      <Name>ENTRY_CLIPSET_MAP_PLANE_TITAN2_REAR_RIGHT_2</Name>
      <Maps>
        <Item>
          <ClipSetId>clipset@veh@plane@titan2@rear@rps1@enter_exit</ClipSetId>
          <VarClipSetId>EntryVarClipSet</VarClipSetId>
          <ConditionFlags />
          <InformationFlags />
        </Item>
      </Maps>
    </Item>
    <Item type="CClipSetMap">
      <Name>EXIT_CLIPSET_MAP_PLANE_TITAN2_REAR_RIGHT_2</Name>
      <Maps>
        <Item>
          <ClipSetId>clipset@veh@plane@titan2@rear@rps1@enter_exit</ClipSetId>
          <VarClipSetId>ExitVarClipSet</VarClipSetId>
          <ConditionFlags />
          <InformationFlags />
        </Item>
      </Maps>
    </Item>
  </ClipSetMaps>
  <VehicleCoverBoundOffsetInfos>
    <Item type="CVehicleCoverBoundOffsetInfo">
      <Name>CHAVOSV6_COVER_OFFSET_INFO</Name>
      <ExtraSideOffset value="0.100000" />
      <ExtraForwardOffset value="0.000000" />
      <ExtraBackwardOffset value="-0.250000" />
      <ExtraZOffset value="-0.300000" />
      <CoverBoundInfos />
    </Item>
    <Item type="CVehicleCoverBoundOffsetInfo">
      <Name>COQUETTE6_COVER_OFFSET_INFO</Name>
      <ExtraSideOffset value="-0.100000" />
      <ExtraForwardOffset value="0.000000" />
      <ExtraBackwardOffset value="0.450000" />
      <ExtraZOffset value="0.450000" />
      <CoverBoundInfos>
        <Item>
          <Name>Front</Name>
          <Position x="0.000000" y="1.575000" z="0.245000" />
          <Length value="1.400000" />
          <Width value="2.100000" />
          <Height value="1.000000" />
          <ActiveBoundExclusions />
        </Item>
        <Item>
          <Name>MIDDLE</Name>
          <Position x="0.000000" y="-0.012000" z="0.245000" />
          <Length value="1.775000" />
          <Width value="1.950000" />
          <Height value="1.000000" />
          <ActiveBoundExclusions />
        </Item>
        <Item>
          <Name>REARWHEEL</Name>
          <Position x="0.000000" y="-1.312500" z="0.245000" />
          <Length value="0.825000" />
          <Width value="2.175000" />
          <Height value="1.000000" />
          <ActiveBoundExclusions />
        </Item>
        <Item>
          <Name>REAR</Name>
          <Position x="0.000000" y="-1.847500" z="0.245000" />
          <Length value="0.455000" />
          <Width value="1.950000" />
          <Height value="1.000000" />
          <ActiveBoundExclusions />
        </Item>
      </CoverBoundInfos>
    </Item>
    <Item type="CVehicleCoverBoundOffsetInfo">
      <Name>BANSHEE3_COVER_OFFSET_INFO</Name>
      <ExtraSideOffset value="-0.100000" />
      <ExtraForwardOffset value="0.000000" />
      <ExtraBackwardOffset value="-0.250000" />
      <ExtraZOffset value="0.450000" />
      <CoverBoundInfos>
        <Item>
          <Name>Front</Name>
          <Position x="0.000000" y="1.575000" z="0.245000" />
          <Length value="1.200000" />
          <Width value="2.100000" />
          <Height value="1.000000" />
          <ActiveBoundExclusions />
        </Item>
        <Item>
          <Name>MIDDLE</Name>
          <Position x="0.000000" y="-0.012000" z="0.245000" />
          <Length value="1.975000" />
          <Width value="1.950000" />
          <Height value="1.000000" />
          <ActiveBoundExclusions />
        </Item>
        <Item>
          <Name>REARWHEEL</Name>
          <Position x="0.000000" y="-1.312500" z="0.245000" />
          <Length value="0.985000" />
          <Width value="2.175000" />
          <Height value="1.000000" />
          <ActiveBoundExclusions />
        </Item>
        <Item>
          <Name>REAR</Name>
          <Position x="0.000000" y="-2.077500" z="0.245000" />
          <Length value="0.415000" />
          <Width value="1.950000" />
          <Height value="1.000000" />
          <ActiveBoundExclusions />
        </Item>
      </CoverBoundInfos>
    </Item>
    <Item type="CVehicleCoverBoundOffsetInfo">
      <Name>URANUS_COVER_OFFSET_INFO</Name>
      <ExtraSideOffset value="0.040000" />
      <ExtraForwardOffset value="0.030000" />
      <ExtraBackwardOffset value="-0.250000" />
      <ExtraZOffset value="-0.100000" />
      <CoverBoundInfos />
    </Item>
    <Item type="CVehicleCoverBoundOffsetInfo">
      <Name>CARGOBOB5_COVER_OFFSET_INFO</Name>
      <ExtraSideOffset value="0.100000" />
      <ExtraForwardOffset value="-0.200000" />
      <ExtraBackwardOffset value="-0.250000" />
      <ExtraZOffset value="1.000000" />
      <CoverBoundInfos>
        <Item>
          <Name>NOSE</Name>
          <Position x="0.000000" y="4.870000" z="0.000000" />
          <Length value="0.740000" />
          <Width value="2.070000" />
          <Height value="1.460000" />
          <ActiveBoundExclusions />
        </Item>
        <Item>
          <Name>COCKPIT</Name>
          <Position x="0.000000" y="4.120000" z="0.000000" />
          <Length value="0.800000" />
          <Width value="2.650000" />
          <Height value="1.460000" />
          <ActiveBoundExclusions />
        </Item>
        <Item>
          <Name>Door</Name>
          <Position x="0.000000" y="2.600000" z="0.130000" />
          <Length value="2.290000" />
          <Width value="2.800000" />
          <Height value="1.860000" />
          <ActiveBoundExclusions />
        </Item>
        <Item>
          <Name>MIDDLE1</Name>
          <Position x="0.000000" y="1.140000" z="-0.090000" />
          <Length value="0.670000" />
          <Width value="4.320000" />
          <Height value="1.520000" />
          <ActiveBoundExclusions />
        </Item>
        <Item>
          <Name>MIDDLE2</Name>
          <Position x="0.000000" y="0.430000" z="-0.000000" />
          <Length value="0.790000" />
          <Width value="4.550000" />
          <Height value="1.800000" />
          <ActiveBoundExclusions />
        </Item>
        <Item>
          <Name>MIDDLE3</Name>
          <Position x="0.000000" y="-0.330000" z="0.000000" />
          <Length value="0.830000" />
          <Width value="4.740000" />
          <Height value="1.970000" />
          <ActiveBoundExclusions />
        </Item>
        <Item>
          <Name>MIDDLE4</Name>
          <Position x="0.000000" y="-2.680000" z="0.000000" />
          <Length value="3.830000" />
          <Width value="4.830000" />
          <Height value="1.970000" />
          <ActiveBoundExclusions />
        </Item>
        <Item>
          <Name>MIDDLE5</Name>
          <Position x="0.000000" y="-5.300000" z="-0.180000" />
          <Length value="1.460000" />
          <Width value="4.830000" />
          <Height value="1.450000" />
          <ActiveBoundExclusions />
        </Item>
      </CoverBoundInfos>
    </Item>
    <Item type="CVehicleCoverBoundOffsetInfo">
      <Name>DUSTER2_COVER_OFFSET_INFO</Name>
      <ExtraSideOffset value="2.800000" />
      <ExtraForwardOffset value="0.600000" />
      <ExtraBackwardOffset value="1.500000" />
      <ExtraZOffset value="0.100000" />
      <CoverBoundInfos>
        <Item>
          <Name>FUSE1</Name>
          <Position x="0.000000" y="-1.680000" z="-0.370000" />
          <Length value="1.090000" />
          <Width value="1.345000" />
          <Height value="2.100000" />
          <ActiveBoundExclusions />
        </Item>
        <Item>
          <Name>FUSE2</Name>
          <Position x="0.000000" y="-2.930000" z="-0.470000" />
          <Length value="1.440000" />
          <Width value="1.160000" />
          <Height value="1.900000" />
          <ActiveBoundExclusions />
        </Item>
        <Item>
          <Name>FUSE3</Name>
          <Position x="0.000000" y="-4.190000" z="-0.610000" />
          <Length value="1.100000" />
          <Width value="0.870000" />
          <Height value="1.580000" />
          <ActiveBoundExclusions />
        </Item>
        <Item>
          <Name>TAIL</Name>
          <Position x="0.000000" y="-5.530000" z="-0.720000" />
          <Length value="1.380000" />
          <Width value="0.600000" />
          <Height value="1.400000" />
          <ActiveBoundExclusions />
        </Item>
      </CoverBoundInfos>
    </Item>
    <Item type="CVehicleCoverBoundOffsetInfo">
      <Name>FIREBOLT_COVER_OFFSET_INFO</Name>
      <ExtraSideOffset value="0.100000" />
      <ExtraForwardOffset value="0.000000" />
      <ExtraBackwardOffset value="-0.260000" />
      <ExtraZOffset value="-0.100000" />
      <CoverBoundInfos />
    </Item>
  </VehicleCoverBoundOffsetInfos>
  <BicycleInfos />
  <POVTuningInfos />
  <EntryAnimVariations>
    <Item type="CEntryAnimVariations">
      <Name>ENTRY_ANIM_VARIATIONS_PLANE_DUSTER2_FRONT_LEFT</Name>
      <Flags />
      <Conditions />
      <EntryAnims />
      <JackingAnims />
      <ExitAnims>
        <Item type="CConditionalClipSet">
          <ClipSet>clipset@veh@plane@duster2@front@ds@enter_exit_skydive</ClipSet>
          <AssociatedSpeech />
          <Conditions />
          <ActionFlags>IsSkyDive</ActionFlags>
        </Item>
      </ExitAnims>
    </Item>
    <Item type="CEntryAnimVariations">
      <Name>ENTRY_ANIM_VARIATIONS_PLANE_DUSTER2_FRONT_RIGHT</Name>
      <Flags />
      <Conditions />
      <EntryAnims />
      <JackingAnims />
      <ExitAnims>
        <Item type="CConditionalClipSet">
          <ClipSet>clipset@veh@duster2@front@ps@enter_exit_skydive</ClipSet>
          <AssociatedSpeech />
          <Conditions />
          <ActionFlags>IsSkyDive</ActionFlags>
        </Item>
      </ExitAnims>
    </Item>
    <Item type="CEntryAnimVariations">
      <Name>ENTRY_ANIM_VARIATIONS_PLANE_TITAN2_FRONT_LEFT</Name>
      <Flags />
      <Conditions />
      <EntryAnims />
      <JackingAnims />
      <ExitAnims>
        <Item type="CConditionalClipSet">
          <ClipSet>clipset@veh@plane@titan2@front@ds@skydive</ClipSet>
          <AssociatedSpeech />
          <Conditions />
          <ActionFlags>IsSkyDive</ActionFlags>
        </Item>
      </ExitAnims>
    </Item>
    <Item type="CEntryAnimVariations">
      <Name>ENTRY_ANIM_VARIATIONS_PLANE_TITAN2_FRONT_RIGHT</Name>
      <Flags />
      <Conditions />
      <EntryAnims />
      <JackingAnims />
      <ExitAnims>
        <Item type="CConditionalClipSet">
          <ClipSet>clipset@veh@plane@titan2@front@ps@skydive</ClipSet>
          <AssociatedSpeech />
          <Conditions />
          <ActionFlags>IsSkyDive</ActionFlags>
        </Item>
      </ExitAnims>
    </Item>
    <Item type="CEntryAnimVariations">
      <Name>ENTRY_ANIM_VARIATIONS_PLANE_TITAN2_REAR_LEFT</Name>
      <Flags />
      <Conditions />
      <EntryAnims />
      <JackingAnims />
      <ExitAnims>
        <Item type="CConditionalClipSet">
          <ClipSet>clipset@veh@plane@titan2@rear@rds@skydive</ClipSet>
          <AssociatedSpeech />
          <Conditions />
          <ActionFlags>IsSkyDive</ActionFlags>
        </Item>
      </ExitAnims>
    </Item>
    <Item type="CEntryAnimVariations">
      <Name>ENTRY_ANIM_VARIATIONS_PLANE_TITAN2_REAR_RIGHT</Name>
      <Flags />
      <Conditions />
      <EntryAnims />
      <JackingAnims />
      <ExitAnims>
        <Item type="CConditionalClipSet">
          <ClipSet>clipset@veh@plane@titan2@rear@rps@skydive</ClipSet>
          <AssociatedSpeech />
          <Conditions />
          <ActionFlags>IsSkyDive</ActionFlags>
        </Item>
      </ExitAnims>
    </Item>
    <Item type="CEntryAnimVariations">
      <Name>ENTRY_ANIM_VARIATIONS_PLANE_TITAN2_REAR_RIGHT_2</Name>
      <Flags />
      <Conditions />
      <EntryAnims />
      <JackingAnims />
      <ExitAnims>
        <Item type="CConditionalClipSet">
          <ClipSet>clipset@veh@plane@titan2@rear@rps1@skydive</ClipSet>
          <AssociatedSpeech />
          <Conditions />
          <ActionFlags>IsSkyDive</ActionFlags>
        </Item>
      </ExitAnims>
    </Item>
  </EntryAnimVariations>
  <VehicleExtraPointsInfos>
    <Item type="CVehicleExtraPointsInfo">
      <Name>EXTRA_VEHICLE_POINTS_PLANE_DUSTER2_FRONT_LEFT</Name>
      <ExtraVehiclePoints>
        <Item>
          <LocationType>SEAT_RELATIVE</LocationType>
          <PointType>GET_IN</PointType>
          <Position x="-1.300000" y="0.331000" z="-0.030000" />
          <Heading value="-0.600000" />
          <Pitch value="0.000000" />
          <BoneName />
        </Item>
      </ExtraVehiclePoints>
    </Item>
    <Item type="CVehicleExtraPointsInfo">
      <Name>EXTRA_VEHICLE_POINTS_PLANE_DUSTER2_FRONT_RIGHT</Name>
      <ExtraVehiclePoints>
        <Item>
          <LocationType>SEAT_RELATIVE</LocationType>
          <PointType>GET_IN</PointType>
          <Position x="1.300000" y="0.331000" z="-0.030000" />
          <Heading value="0.600000" />
          <Pitch value="0.000000" />
          <BoneName />
        </Item>
      </ExtraVehiclePoints>
    </Item>
    <Item type="CVehicleExtraPointsInfo">
      <Name>EXTRA_VEHICLE_POINTS_PLANE_TITAN2_FRONT</Name>
      <ExtraVehiclePoints>
        <Item>
          <LocationType>DRIVER_SEAT_RELATIVE</LocationType>
          <PointType>GET_IN</PointType>
          <Position x="0.090000" y="-1.435000" z="0.455000" />
          <Heading value="-0.350000" />
          <Pitch value="0.000000" />
          <BoneName />
        </Item>
        <Item>
          <LocationType>DRIVER_SEAT_RELATIVE</LocationType>
          <PointType>GET_OUT</PointType>
          <Position x="0.155000" y="-1.235000" z="0.307000" />
          <Heading value="1.570000" />
          <Pitch value="0.000000" />
          <BoneName />
        </Item>
      </ExtraVehiclePoints>
    </Item>
    <Item type="CVehicleExtraPointsInfo">
      <Name>EXTRA_VEHICLE_POINTS_PLANE_TITAN2_REAR</Name>
      <ExtraVehiclePoints>
        <Item>
          <LocationType>DRIVER_SEAT_RELATIVE</LocationType>
          <PointType>GET_IN</PointType>
          <Position x="-0.563000" y="-4.865000" z="-0.373000" />
          <Heading value="-1.570000" />
          <Pitch value="0.000000" />
          <BoneName />
        </Item>
        <Item>
          <LocationType>DRIVER_SEAT_RELATIVE</LocationType>
          <PointType>GET_OUT</PointType>
          <Position x="-0.746000" y="-5.057000" z="-0.456000" />
          <Heading value="1.570800" />
          <Pitch value="0.000000" />
          <BoneName />
        </Item>
      </ExtraVehiclePoints>
    </Item>
  </VehicleExtraPointsInfos>
  <DrivebyWeaponGroups />
  <VehicleDriveByAnimInfos>
    <Item type="CVehicleDriveByAnimInfo">
      <Name>LOW_BANSHEE3_ANIM_INFO_ONE_HANDED_DS</Name>
      <WeaponGroup ref="DRIVEBY_DEFAULT_ONE_HANDED" />
      <DriveByClipSet>drive_by@low_ds</DriveByClipSet>
      <FirstPersonDriveByClipSet>driveby@first_person@driver@1h</FirstPersonDriveByClipSet>
      <AltFirstPersonDriveByClipSets>
        <Item>
          <Weapons>
            <Item>WEAPON_MICROSMG</Item>
            <Item>WEAPON_TECPISTOL</Item>
          </Weapons>
          <ClipSet>driveby@first_person@driver@smg</ClipSet>
        </Item>
      </AltFirstPersonDriveByClipSets>
      <RestrictedDriveByClipSet>drive_by@restricted@TIGHTLOW_DS</RestrictedDriveByClipSet>
      <VehicleMeleeClipSet />
      <FirstPersonVehicleMeleeClipSet />
      <Network>STD_CAR_DRIVEBY</Network>
      <UseOverrideAngles value="false" />
      <OverrideAnglesInThirdPersonOnly value="false" />
      <OverrideMinAimAngle value="0.000000" />
      <OverrideMaxAimAngle value="0.000000" />
      <OverrideMinRestrictedAimAngle value="0.000000" />
      <OverrideMaxRestrictedAimAngle value="0.000000" />
    </Item>
    <Item type="CVehicleDriveByAnimInfo">
      <Name>STD_POLTERMINUS_ANIM_INFO_ONE_HANDED_PS</Name>
      <WeaponGroup ref="DRIVEBY_DEFAULT_ONE_HANDED" />
      <DriveByClipSet>clipset@veh@driveby@astron2@ps</DriveByClipSet>
      <FirstPersonDriveByClipSet>driveby@first_person@passenger_left_handed@1h</FirstPersonDriveByClipSet>
      <AltFirstPersonDriveByClipSets>
        <Item>
          <Weapons>
            <Item>WEAPON_MICROSMG</Item>
          </Weapons>
          <ClipSet>driveby@first_person@passenger_left_handed@smg</ClipSet>
        </Item>
      </AltFirstPersonDriveByClipSets>
      <RestrictedDriveByClipSet />
      <VehicleMeleeClipSet />
      <FirstPersonVehicleMeleeClipSet />
      <Network>STD_CAR_DRIVEBY</Network>
      <UseOverrideAngles value="false" />
      <OverrideAnglesInThirdPersonOnly value="false" />
      <OverrideMinAimAngle value="0.000000" />
      <OverrideMaxAimAngle value="0.000000" />
      <OverrideMinRestrictedAimAngle value="0.000000" />
      <OverrideMaxRestrictedAimAngle value="0.000000" />
    </Item>
  </VehicleDriveByAnimInfos>
  <VehicleDriveByInfos>
    <Item type="CVehicleDriveByInfo">
      <Name>DRIVEBY_LOW_BANSHEE3_FRONT_LEFT</Name>
      <MinAimSweepHeadingAngleDegs value="-190.000000" />
      <MaxAimSweepHeadingAngleDegs value="190.000000" />
      <FirstPersonMinAimSweepHeadingAngleDegs value="-180.000000" />
      <FirstPersonMaxAimSweepHeadingAngleDegs value="180.000000" />
      <FirstPersonUnarmedMinAimSweepHeadingAngleDegs value="-180.000000" />
      <FirstPersonUnarmedMaxAimSweepHeadingAngleDegs value="180.000000" />
      <MinRestrictedAimSweepHeadingAngleDegs value="-190.000000" />
      <MaxRestrictedAimSweepHeadingAngleDegs value="190.000000" />
      <MinSmashWindowAngleDegs value="-180.000000" />
      <MaxSmashWindowAngleDegs value="-17.500000" />
      <MinSmashWindowAngleFirstPersonDegs value="-180.000000" />
      <MaxSmashWindowAngleFirstPersonDegs value="-30.000000" />
      <MaxSpeedParam value="0.500000" />
      <MaxLongitudinalLeanBlendWeightDelta value="0.250000" />
      <MaxLateralLeanBlendWeightDelta value="0.250000" />
      <ApproachSpeedToWithinMaxBlendDelta value="1.000000" />
      <SpineAdditiveBlendInDelay value="0.050000" />
      <SpineAdditiveBlendInDurationStill value="0.150000" />
      <SpineAdditiveBlendInDuration value="0.750000" />
      <SpineAdditiveBlendOutDelay value="0.000000" />
      <SpineAdditiveBlendOutDuration value="0.500000" />
      <MinUnarmedDrivebyYawIfWindowRolledUp value="-0.800000" />
      <MaxUnarmedDrivebyYawIfWindowRolledUp value="3.140000" />
      <DriveByAnimInfos>
        <Item ref="LOW_DB_ANIM_INFO_UNARMED_DS" />
        <Item ref="LOW_BANSHEE3_ANIM_INFO_ONE_HANDED_DS" />
        <Item ref="LOW_DB_ANIM_INFO_THROW_DS" />
      </DriveByAnimInfos>
      <DriveByCamera />
      <PovDriveByCamera />
      <DriveByFlags>UseMountedProjectileTask LeftHandedProjctiles LeftHandedUnarmedFirstPersonAnims</DriveByFlags>
    </Item>
    <Item type="CVehicleDriveByInfo">
      <Name>DRIVEBY_STD_FIREBOLT_FRONT_RIGHT</Name>
      <MinAimSweepHeadingAngleDegs value="-225.000000" />
      <MaxAimSweepHeadingAngleDegs value="225.000000" />
      <FirstPersonMinAimSweepHeadingAngleDegs value="-180.000000" />
      <FirstPersonMaxAimSweepHeadingAngleDegs value="180.000000" />
      <FirstPersonUnarmedMinAimSweepHeadingAngleDegs value="-180.000000" />
      <FirstPersonUnarmedMaxAimSweepHeadingAngleDegs value="180.000000" />
      <MinRestrictedAimSweepHeadingAngleDegs value="-225.000000" />
      <MaxRestrictedAimSweepHeadingAngleDegs value="225.000000" />
      <MinSmashWindowAngleDegs value="17.500000" />
      <MaxSmashWindowAngleDegs value="180.000000" />
      <MinSmashWindowAngleFirstPersonDegs value="35.000000" />
      <MaxSmashWindowAngleFirstPersonDegs value="180.000000" />
      <MaxSpeedParam value="0.500000" />
      <MaxLongitudinalLeanBlendWeightDelta value="0.250000" />
      <MaxLateralLeanBlendWeightDelta value="0.250000" />
      <ApproachSpeedToWithinMaxBlendDelta value="1.000000" />
      <SpineAdditiveBlendInDelay value="0.050000" />
      <SpineAdditiveBlendInDurationStill value="0.150000" />
      <SpineAdditiveBlendInDuration value="0.750000" />
      <SpineAdditiveBlendOutDelay value="0.000000" />
      <SpineAdditiveBlendOutDuration value="0.500000" />
      <MinUnarmedDrivebyYawIfWindowRolledUp value="-3.140000" />
      <MaxUnarmedDrivebyYawIfWindowRolledUp value="0.925000" />
      <DriveByAnimInfos>
        <Item ref="STD_DB_ANIM_INFO_UNARMED_PS" />
        <Item ref="STD_POLTERMINUS_ANIM_INFO_ONE_HANDED_PS" />
        <Item ref="STD_DB_ANIM_INFO_THROW_PS" />
      </DriveByAnimInfos>
      <DriveByCamera />
      <PovDriveByCamera />
      <DriveByFlags>UseMountedProjectileTask LeftHandedFirstPersonAnims
        LeftHandedUnarmedFirstPersonAnims</DriveByFlags>
    </Item>
    <Item type="CVehicleDriveByInfo">
      <Name>DRIVEBY_STD_POLTERMINUS_FRONT_RIGHT</Name>
      <MinAimSweepHeadingAngleDegs value="-225.000000" />
      <MaxAimSweepHeadingAngleDegs value="225.000000" />
      <FirstPersonMinAimSweepHeadingAngleDegs value="-180.000000" />
      <FirstPersonMaxAimSweepHeadingAngleDegs value="180.000000" />
      <FirstPersonUnarmedMinAimSweepHeadingAngleDegs value="-180.000000" />
      <FirstPersonUnarmedMaxAimSweepHeadingAngleDegs value="180.000000" />
      <MinRestrictedAimSweepHeadingAngleDegs value="-225.000000" />
      <MaxRestrictedAimSweepHeadingAngleDegs value="225.000000" />
      <MinSmashWindowAngleDegs value="17.500000" />
      <MaxSmashWindowAngleDegs value="180.000000" />
      <MinSmashWindowAngleFirstPersonDegs value="35.000000" />
      <MaxSmashWindowAngleFirstPersonDegs value="180.000000" />
      <MaxSpeedParam value="0.500000" />
      <MaxLongitudinalLeanBlendWeightDelta value="0.250000" />
      <MaxLateralLeanBlendWeightDelta value="0.250000" />
      <ApproachSpeedToWithinMaxBlendDelta value="1.000000" />
      <SpineAdditiveBlendInDelay value="0.050000" />
      <SpineAdditiveBlendInDurationStill value="0.150000" />
      <SpineAdditiveBlendInDuration value="0.750000" />
      <SpineAdditiveBlendOutDelay value="0.000000" />
      <SpineAdditiveBlendOutDuration value="0.500000" />
      <MinUnarmedDrivebyYawIfWindowRolledUp value="-3.140000" />
      <MaxUnarmedDrivebyYawIfWindowRolledUp value="0.925000" />
      <DriveByAnimInfos>
        <Item ref="STD_DB_ANIM_INFO_UNARMED_PS" />
        <Item ref="STD_POLTERMINUS_ANIM_INFO_ONE_HANDED_PS" />
        <Item ref="STD_DB_ANIM_INFO_THROW_PS" />
      </DriveByAnimInfos>
      <DriveByCamera />
      <PovDriveByCamera />
      <DriveByFlags>UseMountedProjectileTask LeftHandedFirstPersonAnims
        LeftHandedUnarmedFirstPersonAnims</DriveByFlags>
    </Item>
  </VehicleDriveByInfos>
  <VehicleSeatInfos>
    <Item type="CVehicleSeatInfo">
      <Name>SEAT_STD_DRIFTJESTER3_FRONT_LEFT</Name>
      <SeatBoneName>seat_dside_f</SeatBoneName>
      <ShuffleLink>SEAT_STD_DRIFTJESTER3_FRONT_RIGHT</ShuffleLink>
      <RearSeatLink />
      <DefaultCarTask>TASK_DRIVE_WANDER</DefaultCarTask>
      <SeatFlags>IsFrontSeat</SeatFlags>
      <HairScale value="-0.275000" />
    </Item>
    <Item type="CVehicleSeatInfo">
      <Name>SEAT_STD_DRIFTJESTER3_FRONT_RIGHT</Name>
      <SeatBoneName>seat_pside_f</SeatBoneName>
      <ShuffleLink>SEAT_STD_DRIFTJESTER3_FRONT_LEFT</ShuffleLink>
      <RearSeatLink />
      <DefaultCarTask>TASK_DRIVE_WANDER</DefaultCarTask>
      <SeatFlags>IsFrontSeat</SeatFlags>
      <HairScale value="-0.275000" />
    </Item>
    <Item type="CVehicleSeatInfo">
      <Name>SEAT_PLANE_TITAN2_REAR_LEFT</Name>
      <SeatBoneName>seat_dside_r</SeatBoneName>
      <ShuffleLink>SEAT_PLANE_TITAN2_REAR_RIGHT</ShuffleLink>
      <RearSeatLink />
      <DefaultCarTask>TASK_DRIVE_WANDER</DefaultCarTask>
      <SeatFlags>IsIdleSeat</SeatFlags>
      <HairScale value="0.000000" />
      <ShuffleLink2>SEAT_PLANE_TITAN2_REAR_RIGHT_2</ShuffleLink2>
    </Item>
    <Item type="CVehicleSeatInfo">
      <Name>SEAT_PLANE_TITAN2_REAR_RIGHT</Name>
      <SeatBoneName>seat_pside_r</SeatBoneName>
      <ShuffleLink>SEAT_PLANE_TITAN2_REAR_RIGHT_2</ShuffleLink>
      <RearSeatLink />
      <DefaultCarTask>TASK_DRIVE_WANDER</DefaultCarTask>
      <SeatFlags>IsIdleSeat</SeatFlags>
      <HairScale value="0.000000" />
      <ShuffleLink2>SEAT_PLANE_TITAN2_REAR_LEFT</ShuffleLink2>
    </Item>
    <Item type="CVehicleSeatInfo">
      <Name>SEAT_PLANE_TITAN2_REAR_RIGHT_2</Name>
      <SeatBoneName>seat_pside_r1</SeatBoneName>
      <ShuffleLink>SEAT_PLANE_TITAN2_REAR_LEFT</ShuffleLink>
      <RearSeatLink />
      <DefaultCarTask>TASK_DRIVE_WANDER</DefaultCarTask>
      <SeatFlags>IsIdleSeat</SeatFlags>
      <HairScale value="0.000000" />
      <ShuffleLink2>SEAT_PLANE_TITAN2_REAR_RIGHT</ShuffleLink2>
    </Item>
  </VehicleSeatInfos>
  <VehicleSeatAnimInfos>
    <Item type="CVehicleSeatAnimInfo">
      <Name>SEAT_ANIM_LOW_BANSHEE3_FRONT_LEFT</Name>
      <DriveByInfo ref="DRIVEBY_LOW_BANSHEE3_FRONT_LEFT" />
      <InsideClipSetMap ref="INSIDE_CLIPSET_MAP_LOW_FRONT_LEFT" />
      <PanicClipSet>clipset@veh@low@ds@idle_panic</PanicClipSet>
      <AgitatedClipSet>clipset@veh@low@ds@idle_agitated</AgitatedClipSet>
      <DuckedClipSet>clipset@veh@low@ds@idle_duck</DuckedClipSet>
      <FemaleClipSet />
      <LowriderLeanClipSet />
      <AltLowriderLeanClipSet />
      <LowLODIdleAnim>LOW_CAR_FRONT_DS_IDLE</LowLODIdleAnim>
      <SeatAmbientContext>IN_CAR_LOW</SeatAmbientContext>
      <InVehicleMoveNetwork>VEHICLE_DEFAULT</InVehicleMoveNetwork>
      <SeatAnimFlags>UseStandardInVehicleAnims HasPanicAnims UseCloseDoorBlendAnims</SeatAnimFlags>
      <SteeringSmoothing value="0.100000" />
      <ExitToAimInfoName>LOW</ExitToAimInfoName>
      <MaleGestureClipSetId>ANIM_GROUP_GESTURE_M_CAR_LOW_DS</MaleGestureClipSetId>
      <FemaleGestureClipSetId>ANIM_GROUP_GESTURE_F_CAR_LOW_DS</FemaleGestureClipSetId>
      <FPSMinSteeringRateOverride value="2.000000" />
      <FPSMaxSteeringRateOverride value="9.000000" />
      <SeatCollisionBoundsOffset x="0.000000" y="0.000000" z="0.000000" />
    </Item>
    <Item type="CVehicleSeatAnimInfo">
      <Name>SEAT_ANIM_HELI_CARGOBOB5_FRONT_RIGHT</Name>
      <DriveByInfo ref="DRIVEBY_HELI_FRONT_RIGHT" />
      <InsideClipSetMap ref="INSIDE_CLIPSET_MAP_HELI_CARGOBOB5_FRONT_RIGHT" />
      <PanicClipSet />
      <AgitatedClipSet />
      <DuckedClipSet />
      <FemaleClipSet />
      <LowriderLeanClipSet />
      <AltLowriderLeanClipSet />
      <LowLODIdleAnim>HELI_FRONT_PS_IDLE</LowLODIdleAnim>
      <SeatAmbientContext />
      <InVehicleMoveNetwork>VEHICLE_DEFAULT</InVehicleMoveNetwork>
      <SeatAnimFlags>WeaponAttachedToLeftHand UseStandardInVehicleAnims HasPanicAnims
        PreventShuffleJack</SeatAnimFlags>
      <SteeringSmoothing value="0.100000" />
      <ExitToAimInfoName />
      <MaleGestureClipSetId />
      <FemaleGestureClipSetId />
      <FPSMinSteeringRateOverride value="-1.000000" />
      <FPSMaxSteeringRateOverride value="-1.000000" />
      <SeatCollisionBoundsOffset x="0.000000" y="0.000000" z="0.000000" />
    </Item>
    <Item type="CVehicleSeatAnimInfo">
      <Name>SEAT_ANIM_PLANE_DUSTER2_FRONT</Name>
      <DriveByInfo ref="NULL" />
      <InsideClipSetMap ref="INSIDE_CLIPSET_MAP_PLANE_DUSTER2_FRONT" />
      <PanicClipSet />
      <AgitatedClipSet />
      <DuckedClipSet />
      <FemaleClipSet />
      <LowriderLeanClipSet />
      <AltLowriderLeanClipSet />
      <LowLODIdleAnim>HELI_FRONT_DS_IDLE</LowLODIdleAnim>
      <SeatAmbientContext />
      <InVehicleMoveNetwork>VEHICLE_DEFAULT</InVehicleMoveNetwork>
      <SeatAnimFlags>UseStandardInVehicleAnims PreventShuffleJack UseDirectEntryOnlyWhenEntering</SeatAnimFlags>
      <SteeringSmoothing value="0.100000" />
      <ExitToAimInfoName />
      <MaleGestureClipSetId />
      <FemaleGestureClipSetId />
      <FPSMinSteeringRateOverride value="-1.000000" />
      <FPSMaxSteeringRateOverride value="-1.000000" />
      <SeatCollisionBoundsOffset x="0.000000" y="0.000000" z="0.000000" />
    </Item>
    <Item type="CVehicleSeatAnimInfo">
      <Name>SEAT_ANIM_STD_FIREBOLT_FRONT_RIGHT</Name>
      <DriveByInfo ref="DRIVEBY_STD_FIREBOLT_FRONT_RIGHT" />
      <InsideClipSetMap ref="INSIDE_CLIPSET_MAP_STD_FRONT_RIGHT" />
      <PanicClipSet>clipset@veh@std@ps@idle_panic</PanicClipSet>
      <AgitatedClipSet />
      <DuckedClipSet>clipset@veh@std@ps@idle_duck</DuckedClipSet>
      <FemaleClipSet />
      <LowriderLeanClipSet />
      <AltLowriderLeanClipSet />
      <LowLODIdleAnim>STD_CAR_FRONT_PS_IDLE</LowLODIdleAnim>
      <SeatAmbientContext>IN_CAR_STANDARD</SeatAmbientContext>
      <InVehicleMoveNetwork>VEHICLE_DEFAULT</InVehicleMoveNetwork>
      <SeatAnimFlags>UseStandardInVehicleAnims HasPanicAnims UseCloseDoorBlendAnims
        PreventShuffleJack</SeatAnimFlags>
      <SteeringSmoothing value="0.100000" />
      <ExitToAimInfoName>STANDARD</ExitToAimInfoName>
      <MaleGestureClipSetId>ANIM_GROUP_GESTURE_M_CAR_STD_PS</MaleGestureClipSetId>
      <FemaleGestureClipSetId>ANIM_GROUP_GESTURE_F_CAR_STD_PS</FemaleGestureClipSetId>
      <FPSMinSteeringRateOverride value="-1.000000" />
      <FPSMaxSteeringRateOverride value="-1.000000" />
      <SeatCollisionBoundsOffset x="0.000000" y="0.000000" z="0.000000" />
    </Item>
    <Item type="CVehicleSeatAnimInfo">
      <Name>SEAT_ANIM_STD_POLTERMINUS_FRONT_LEFT</Name>
      <DriveByInfo ref="DRIVEBY_RANGER_4x4_FRONT_LEFT" />
      <InsideClipSetMap ref="INSIDE_CLIPSET_MAP_STD_FRONT_LEFT" />
      <PanicClipSet>clipset@veh@std@ds@idle_panic</PanicClipSet>
      <AgitatedClipSet>clipset@veh@std@ds@idle_agitated</AgitatedClipSet>
      <DuckedClipSet>clipset@veh@std@ds@idle_duck</DuckedClipSet>
      <FemaleClipSet />
      <LowriderLeanClipSet />
      <AltLowriderLeanClipSet />
      <LowLODIdleAnim>STD_CAR_FRONT_DS_IDLE</LowLODIdleAnim>
      <SeatAmbientContext>IN_CAR_STANDARD</SeatAmbientContext>
      <InVehicleMoveNetwork>VEHICLE_DEFAULT</InVehicleMoveNetwork>
      <SeatAnimFlags>UseStandardInVehicleAnims UseCloseDoorBlendAnims</SeatAnimFlags>
      <SteeringSmoothing value="0.040000" />
      <ExitToAimInfoName>STANDARD</ExitToAimInfoName>
      <MaleGestureClipSetId>ANIM_GROUP_GESTURE_M_CAR_STD_DS</MaleGestureClipSetId>
      <FemaleGestureClipSetId>ANIM_GROUP_GESTURE_F_CAR_STD_DS</FemaleGestureClipSetId>
      <FPSMinSteeringRateOverride value="-1.000000" />
      <FPSMaxSteeringRateOverride value="-1.000000" />
      <SeatCollisionBoundsOffset x="0.000000" y="0.000000" z="0.000000" />
    </Item>
    <Item type="CVehicleSeatAnimInfo">
      <Name>SEAT_ANIM_STD_POLTERMINUS_FRONT_RIGHT</Name>
      <DriveByInfo ref="DRIVEBY_STD_POLTERMINUS_FRONT_RIGHT" />
      <InsideClipSetMap ref="INSIDE_CLIPSET_MAP_STD_FRONT_RIGHT" />
      <PanicClipSet>clipset@veh@std@ps@idle_panic</PanicClipSet>
      <AgitatedClipSet />
      <DuckedClipSet>clipset@veh@std@ps@idle_duck</DuckedClipSet>
      <FemaleClipSet />
      <LowriderLeanClipSet />
      <AltLowriderLeanClipSet />
      <LowLODIdleAnim>STD_CAR_FRONT_PS_IDLE</LowLODIdleAnim>
      <SeatAmbientContext>IN_CAR_STANDARD</SeatAmbientContext>
      <InVehicleMoveNetwork>VEHICLE_DEFAULT</InVehicleMoveNetwork>
      <SeatAnimFlags>UseStandardInVehicleAnims HasPanicAnims UseCloseDoorBlendAnims
        PreventShuffleJack</SeatAnimFlags>
      <SteeringSmoothing value="0.100000" />
      <ExitToAimInfoName>STANDARD</ExitToAimInfoName>
      <MaleGestureClipSetId>ANIM_GROUP_GESTURE_M_CAR_STD_PS</MaleGestureClipSetId>
      <FemaleGestureClipSetId>ANIM_GROUP_GESTURE_F_CAR_STD_PS</FemaleGestureClipSetId>
      <FPSMinSteeringRateOverride value="-1.000000" />
      <FPSMaxSteeringRateOverride value="-1.000000" />
      <SeatCollisionBoundsOffset x="0.000000" y="0.000000" z="0.000000" />
    </Item>
    <Item type="CVehicleSeatAnimInfo">
      <Name>SEAT_ANIM_PLANE_TITAN2_REAR_LEFT</Name>
      <DriveByInfo ref="NULL" />
      <InsideClipSetMap ref="INSIDE_CLIPSET_MAP_PLANE_TITAN2_REAR_LEFT" />
      <PanicClipSet />
      <AgitatedClipSet />
      <DuckedClipSet />
      <FemaleClipSet />
      <LowriderLeanClipSet />
      <AltLowriderLeanClipSet />
      <LowLODIdleAnim>BUS_PASSENGER</LowLODIdleAnim>
      <SeatAmbientContext />
      <InVehicleMoveNetwork>VEHICLE_TURRET_SEATED</InVehicleMoveNetwork>
      <SeatAnimFlags>CannotBeJacked UseStandardInVehicleAnims PreventShuffleJack
        CanWarpToDriverSeatIfNoDriver DisableAbnormalExits UseDirectEntryOnlyWhenEntering</SeatAnimFlags>
      <SteeringSmoothing value="0.100000" />
      <ExitToAimInfoName />
      <MaleGestureClipSetId />
      <FemaleGestureClipSetId />
      <FPSMinSteeringRateOverride value="-1.000000" />
      <FPSMaxSteeringRateOverride value="-1.000000" />
      <SeatCollisionBoundsOffset x="0.000000" y="0.000000" z="0.000000" />
    </Item>
    <Item type="CVehicleSeatAnimInfo">
      <Name>SEAT_ANIM_PLANE_TITAN2_REAR_RIGHT</Name>
      <DriveByInfo ref="NULL" />
      <InsideClipSetMap ref="INSIDE_CLIPSET_MAP_PLANE_TITAN2_REAR_RIGHT" />
      <PanicClipSet />
      <AgitatedClipSet />
      <DuckedClipSet />
      <FemaleClipSet />
      <LowriderLeanClipSet />
      <AltLowriderLeanClipSet />
      <LowLODIdleAnim>BUS_PASSENGER</LowLODIdleAnim>
      <SeatAmbientContext />
      <InVehicleMoveNetwork>VEHICLE_TURRET_SEATED</InVehicleMoveNetwork>
      <SeatAnimFlags>CannotBeJacked UseStandardInVehicleAnims PreventShuffleJack
        CanWarpToDriverSeatIfNoDriver DisableAbnormalExits UseDirectEntryOnlyWhenEntering</SeatAnimFlags>
      <SteeringSmoothing value="0.100000" />
      <ExitToAimInfoName />
      <MaleGestureClipSetId />
      <FemaleGestureClipSetId />
      <FPSMinSteeringRateOverride value="-1.000000" />
      <FPSMaxSteeringRateOverride value="-1.000000" />
      <SeatCollisionBoundsOffset x="0.000000" y="0.000000" z="0.000000" />
    </Item>
  </VehicleSeatAnimInfos>
  <VehicleEntryPointInfos>
    <Item type="CVehicleEntryPointInfo">
      <Name>ENTRY_POINT_MP_HELI_CARGOBOB5_WARP_REAR_LEFT</Name>
      <DoorBoneName />
      <SecondDoorBoneName />
      <DoorHandleBoneName />
      <WindowId>INVALID</WindowId>
      <VehicleSide>SIDE_LEFT</VehicleSide>
      <AccessableSeats>
        <Item ref="SEAT_HELI_CARGOBOB_REAR_LEFT" />
        <Item ref="SEAT_EXTRA_NO_SHUFFLE_LEFT_1" />
        <Item ref="SEAT_EXTRA_NO_SHUFFLE_LEFT_2" />
        <Item ref="SEAT_EXTRA_NO_SHUFFLE_LEFT_3" />
        <Item ref="SEAT_EXTRA_NO_SHUFFLE_LEFT_4" />
        <Item ref="SEAT_EXTRA_NO_SHUFFLE_LEFT_5" />
        <Item ref="SEAT_EXTRA_NO_SHUFFLE_LEFT_6" />
      </AccessableSeats>
      <VehicleExtraPointsInfo ref="NULL" />
      <Flags>MPWarpInOut</Flags>
      <BlockJackReactionSides />
      <BreakoutTestPoint>POINT_TYPE_MAX</BreakoutTestPoint>
    </Item>
    <Item type="CVehicleEntryPointInfo">
      <Name>ENTRY_POINT_MP_HELI_CARGOBOB5_WARP_REAR_RIGHT</Name>
      <DoorBoneName />
      <SecondDoorBoneName />
      <DoorHandleBoneName />
      <WindowId>INVALID</WindowId>
      <VehicleSide>SIDE_RIGHT</VehicleSide>
      <AccessableSeats>
        <Item ref="SEAT_HELI_CARGOBOB_REAR_RIGHT" />
        <Item ref="SEAT_EXTRA_NO_SHUFFLE_RIGHT_1" />
        <Item ref="SEAT_EXTRA_NO_SHUFFLE_RIGHT_2" />
        <Item ref="SEAT_EXTRA_NO_SHUFFLE_RIGHT_3" />
        <Item ref="SEAT_EXTRA_NO_SHUFFLE_RIGHT_4" />
        <Item ref="SEAT_EXTRA_NO_SHUFFLE_RIGHT_5" />
        <Item ref="SEAT_EXTRA_NO_SHUFFLE_RIGHT_6" />
      </AccessableSeats>
      <VehicleExtraPointsInfo ref="NULL" />
      <Flags>MPWarpInOut</Flags>
      <BlockJackReactionSides />
      <BreakoutTestPoint>POINT_TYPE_MAX</BreakoutTestPoint>
    </Item>
    <Item type="CVehicleEntryPointInfo">
      <Name>ENTRY_POINT_PLANE_DUSTER2_FRONT_LEFT</Name>
      <DoorBoneName>door_dside_f</DoorBoneName>
      <SecondDoorBoneName />
      <DoorHandleBoneName />
      <WindowId>FRONT_LEFT</WindowId>
      <VehicleSide>SIDE_LEFT</VehicleSide>
      <AccessableSeats>
        <Item ref="SEAT_SINGLE_FRONT" />
      </AccessableSeats>
      <VehicleExtraPointsInfo ref="EXTRA_VEHICLE_POINTS_PLANE_DUSTER2_FRONT_LEFT" />
      <Flags />
      <BlockJackReactionSides />
      <BreakoutTestPoint>POINT_TYPE_MAX</BreakoutTestPoint>
    </Item>
    <Item type="CVehicleEntryPointInfo">
      <Name>ENTRY_POINT_PLANE_DUSTER2_FRONT_RIGHT</Name>
      <DoorBoneName>door_dside_f</DoorBoneName>
      <SecondDoorBoneName />
      <DoorHandleBoneName />
      <WindowId>FRONT_RIGHT</WindowId>
      <VehicleSide>SIDE_RIGHT</VehicleSide>
      <AccessableSeats>
        <Item ref="SEAT_SINGLE_FRONT" />
      </AccessableSeats>
      <VehicleExtraPointsInfo ref="EXTRA_VEHICLE_POINTS_PLANE_DUSTER2_FRONT_RIGHT" />
      <Flags />
      <BlockJackReactionSides />
      <BreakoutTestPoint>POINT_TYPE_MAX</BreakoutTestPoint>
    </Item>
    <Item type="CVehicleEntryPointInfo">
      <Name>ENTRY_POINT_STD_DRIFTJESTER3_FRONT_LEFT</Name>
      <DoorBoneName>door_dside_f</DoorBoneName>
      <SecondDoorBoneName />
      <DoorHandleBoneName>handle_dside_f</DoorHandleBoneName>
      <WindowId>FRONT_LEFT</WindowId>
      <VehicleSide>SIDE_LEFT</VehicleSide>
      <AccessableSeats>
        <Item ref="SEAT_STD_DRIFTJESTER3_FRONT_LEFT" />
      </AccessableSeats>
      <VehicleExtraPointsInfo ref="EXTRA_VEHICLE_POINTS_STD_LEFT" />
      <Flags>BlockJackReactionUntilJackerIsReady</Flags>
      <BlockJackReactionSides>
        <Item>SIDE_LEFT</Item>
      </BlockJackReactionSides>
    </Item>
    <Item type="CVehicleEntryPointInfo">
      <Name>ENTRY_POINT_STD_DRIFTJESTER3_FRONT_RIGHT</Name>
      <DoorBoneName>door_pside_f</DoorBoneName>
      <SecondDoorBoneName />
      <DoorHandleBoneName>handle_pside_f</DoorHandleBoneName>
      <WindowId>FRONT_RIGHT</WindowId>
      <VehicleSide>SIDE_RIGHT</VehicleSide>
      <AccessableSeats>
        <Item ref="SEAT_STD_DRIFTJESTER3_FRONT_RIGHT" />
      </AccessableSeats>
      <VehicleExtraPointsInfo ref="EXTRA_VEHICLE_POINTS_STD_RIGHT" />
      <Flags>BlockJackReactionUntilJackerIsReady</Flags>
      <BlockJackReactionSides>
        <Item>SIDE_RIGHT</Item>
      </BlockJackReactionSides>
    </Item>
    <Item type="CVehicleEntryPointInfo">
      <Name>ENTRY_POINT_PLANE_TITAN2_FRONT_LEFT</Name>
      <DoorBoneName>door_dside_f</DoorBoneName>
      <SecondDoorBoneName />
      <DoorHandleBoneName>handle_dside_f</DoorHandleBoneName>
      <WindowId>FRONT_LEFT</WindowId>
      <VehicleSide>SIDE_LEFT</VehicleSide>
      <AccessableSeats>
        <Item ref="SEAT_STANDARD_FRONT_LEFT" />
      </AccessableSeats>
      <VehicleExtraPointsInfo ref="EXTRA_VEHICLE_POINTS_PLANE_TITAN2_FRONT" />
      <Flags>IgnoreSmashWindowCheck</Flags>
      <BlockJackReactionSides />
      <BreakoutTestPoint>POINT_TYPE_MAX</BreakoutTestPoint>
    </Item>
    <Item type="CVehicleEntryPointInfo">
      <Name>ENTRY_POINT_PLANE_TITAN2_FRONT_RIGHT</Name>
      <DoorBoneName>door_dside_f</DoorBoneName>
      <SecondDoorBoneName />
      <DoorHandleBoneName>handle_dside_f</DoorHandleBoneName>
      <WindowId>FRONT_RIGHT</WindowId>
      <VehicleSide>SIDE_LEFT</VehicleSide>
      <AccessableSeats>
        <Item ref="SEAT_STANDARD_FRONT_RIGHT" />
      </AccessableSeats>
      <VehicleExtraPointsInfo ref="EXTRA_VEHICLE_POINTS_PLANE_TITAN2_FRONT" />
      <Flags>IgnoreSmashWindowCheck BlockJackReactionUntilJackerIsReady</Flags>
      <BlockJackReactionSides />
      <BreakoutTestPoint>POINT_TYPE_MAX</BreakoutTestPoint>
    </Item>
    <Item type="CVehicleEntryPointInfo">
      <Name>ENTRY_POINT_PLANE_TITAN2_REAR_LEFT</Name>
      <DoorBoneName>door_dside_r</DoorBoneName>
      <SecondDoorBoneName />
      <DoorHandleBoneName>handle_dside_r</DoorHandleBoneName>
      <WindowId>INVALID</WindowId>
      <VehicleSide>SIDE_LEFT</VehicleSide>
      <AccessableSeats>
        <Item ref="SEAT_PLANE_TITAN2_REAR_LEFT" />
      </AccessableSeats>
      <VehicleExtraPointsInfo ref="EXTRA_VEHICLE_POINTS_PLANE_TITAN2_REAR" />
      <Flags>IgnoreSmashWindowCheck BlockJackReactionUntilJackerIsReady CloseDoorAfterClimbDown</Flags>
      <BlockJackReactionSides />
      <BreakoutTestPoint>POINT_TYPE_MAX</BreakoutTestPoint>
    </Item>
    <Item type="CVehicleEntryPointInfo">
      <Name>ENTRY_POINT_PLANE_TITAN2_REAR_RIGHT</Name>
      <DoorBoneName>door_dside_r</DoorBoneName>
      <SecondDoorBoneName />
      <DoorHandleBoneName>handle_dside_r</DoorHandleBoneName>
      <WindowId>INVALID</WindowId>
      <VehicleSide>SIDE_LEFT</VehicleSide>
      <AccessableSeats>
        <Item ref="SEAT_PLANE_TITAN2_REAR_RIGHT" />
      </AccessableSeats>
      <VehicleExtraPointsInfo ref="EXTRA_VEHICLE_POINTS_PLANE_TITAN2_REAR" />
      <Flags>IgnoreSmashWindowCheck BlockJackReactionUntilJackerIsReady CloseDoorAfterClimbDown</Flags>
      <BlockJackReactionSides />
      <BreakoutTestPoint>POINT_TYPE_MAX</BreakoutTestPoint>
    </Item>
    <Item type="CVehicleEntryPointInfo">
      <Name>ENTRY_POINT_PLANE_TITAN2_REAR_RIGHT_2</Name>
      <DoorBoneName>door_dside_r</DoorBoneName>
      <SecondDoorBoneName />
      <DoorHandleBoneName>handle_dside_r</DoorHandleBoneName>
      <WindowId>INVALID</WindowId>
      <VehicleSide>SIDE_LEFT</VehicleSide>
      <AccessableSeats>
        <Item ref="SEAT_PLANE_TITAN2_REAR_RIGHT_2" />
      </AccessableSeats>
      <VehicleExtraPointsInfo ref="EXTRA_VEHICLE_POINTS_PLANE_TITAN2_REAR" />
      <Flags>IgnoreSmashWindowCheck BlockJackReactionUntilJackerIsReady CloseDoorAfterClimbDown</Flags>
      <BlockJackReactionSides />
      <BreakoutTestPoint>POINT_TYPE_MAX</BreakoutTestPoint>
    </Item>
    <Item type="CVehicleEntryPointInfo">
      <Name>ENTRY_POINT_PLANE_TITAN2_CARGO_WARP_REAR_LEFT</Name>
      <DoorBoneName />
      <SecondDoorBoneName />
      <DoorHandleBoneName />
      <WindowId>INVALID</WindowId>
      <VehicleSide>SIDE_REAR</VehicleSide>
      <AccessableSeats>
        <Item ref="SEAT_EXTRA_NO_SHUFFLE_LEFT_1" />
        <Item ref="SEAT_EXTRA_NO_SHUFFLE_LEFT_2" />
        <Item ref="SEAT_EXTRA_NO_SHUFFLE_LEFT_3" />
        <Item ref="SEAT_EXTRA_NO_SHUFFLE_LEFT_4" />
        <Item ref="SEAT_EXTRA_NO_SHUFFLE_LEFT_5" />
        <Item ref="SEAT_EXTRA_NO_SHUFFLE_LEFT_6" />
      </AccessableSeats>
      <VehicleExtraPointsInfo ref="NULL" />
      <Flags>MPWarpInOut</Flags>
      <BlockJackReactionSides />
      <BreakoutTestPoint>POINT_TYPE_MAX</BreakoutTestPoint>
    </Item>
    <Item type="CVehicleEntryPointInfo">
      <Name>ENTRY_POINT_PLANE_TITAN2_CARGO_WARP_REAR_RIGHT</Name>
      <DoorBoneName />
      <SecondDoorBoneName />
      <DoorHandleBoneName />
      <WindowId>INVALID</WindowId>
      <VehicleSide>SIDE_REAR</VehicleSide>
      <AccessableSeats>
        <Item ref="SEAT_EXTRA_NO_SHUFFLE_RIGHT_2" />
        <Item ref="SEAT_EXTRA_NO_SHUFFLE_RIGHT_3" />
        <Item ref="SEAT_EXTRA_NO_SHUFFLE_RIGHT_4" />
        <Item ref="SEAT_EXTRA_NO_SHUFFLE_RIGHT_5" />
        <Item ref="SEAT_EXTRA_NO_SHUFFLE_RIGHT_6" />
      </AccessableSeats>
      <VehicleExtraPointsInfo ref="NULL" />
      <Flags>MPWarpInOut</Flags>
      <BlockJackReactionSides />
      <BreakoutTestPoint>POINT_TYPE_MAX</BreakoutTestPoint>
    </Item>
  </VehicleEntryPointInfos>
  <VehicleEntryPointAnimInfos>
    <Item type="CVehicleEntryPointAnimInfo">
      <Name>ENTRY_POINT_ANIM_HELI_CARGOBOB5_FRONT_LEFT</Name>
      <CommonClipSetMap ref="COMMON_CLIPSET_MAP_TRUCK_LEFT" />
      <EntryClipSetMap ref="ENTRY_CLIPSET_MAP_HELI_CARGOBOB5_FRONT_LEFT" />
      <ExitClipSetMap ref="EXIT_CLIPSET_MAP_HELI_CARGOBOB5_FRONT_LEFT" />
      <AlternateTryLockedDoorClipId />
      <AlternateForcedEntryClipId />
      <AlternateJackFromOutSideClipId />
      <AlternateBeJackedFromOutSideClipId />
      <AlternateEntryPointClipSetId />
      <EnterVehicleMoveNetwork>ENTER_VEHICLE_STD</EnterVehicleMoveNetwork>
      <EntryTranslation x="-1.400000" y="-0.540000" z="-1.130000" />
      <OpenDoorTranslation x="0.000000" y="0.000000" />
      <OpenDoorHeadingChange value="0.000000" />
      <EntryHeadingChange value="-1.570000" />
      <ExtraZForMPPlaneWarp value="-2.000000" />
      <EntryPointFlags>JackIncludesGetIn UsesNoDoorTransitionForEnter UsesNoDoorTransitionForExit
        DontCloseDoorOutside UseGetUpAfterJack DeadJackIncludesGetIn</EntryPointFlags>
      <EntryAnimVariations ref="ENTRY_ANIM_VARIATIONS_HELICOPTER_FRONT_LEFT" />
      <NMJumpFromVehicleTuningSet />
    </Item>
    <Item type="CVehicleEntryPointAnimInfo">
      <Name>ENTRY_POINT_ANIM_HELI_CARGOBOB5_FRONT_RIGHT</Name>
      <CommonClipSetMap ref="COMMON_CLIPSET_MAP_TRUCK_RIGHT" />
      <EntryClipSetMap ref="ENTRY_CLIPSET_MAP_HELI_CARGOBOB5_FRONT_RIGHT" />
      <ExitClipSetMap ref="EXIT_CLIPSET_MAP_HELI_CARGOBOB5_FRONT_RIGHT" />
      <AlternateTryLockedDoorClipId />
      <AlternateForcedEntryClipId />
      <AlternateJackFromOutSideClipId />
      <AlternateBeJackedFromOutSideClipId />
      <AlternateEntryPointClipSetId />
      <EnterVehicleMoveNetwork>ENTER_VEHICLE_STD</EnterVehicleMoveNetwork>
      <EntryTranslation x="1.400000" y="-0.540000" z="-1.130000" />
      <OpenDoorTranslation x="0.000000" y="0.000000" />
      <OpenDoorHeadingChange value="0.000000" />
      <EntryHeadingChange value="1.570000" />
      <ExtraZForMPPlaneWarp value="-2.000000" />
      <EntryPointFlags>JackIncludesGetIn UsesNoDoorTransitionForEnter UsesNoDoorTransitionForExit
        DontCloseDoorOutside UseGetUpAfterJack DeadJackIncludesGetIn</EntryPointFlags>
      <EntryAnimVariations ref="ENTRY_ANIM_VARIATIONS_HELICOPTER_FRONT_RIGHT" />
      <NMJumpFromVehicleTuningSet />
    </Item>
    <Item type="CVehicleEntryPointAnimInfo">
      <Name>ENTRY_POINT_ANIM_HELI_CARGOBOB5_REAR_LEFT</Name>
      <CommonClipSetMap ref="COMMON_CLIPSET_MAP_TRUCK_LEFT" />
      <EntryClipSetMap ref="ENTRY_CLIPSET_MAP_HELI_REAR_LEFT" />
      <ExitClipSetMap ref="EXIT_CLIPSET_MAP_HELI_REAR_LEFT" />
      <AlternateTryLockedDoorClipId />
      <AlternateForcedEntryClipId />
      <AlternateJackFromOutSideClipId />
      <AlternateBeJackedFromOutSideClipId />
      <AlternateEntryPointClipSetId />
      <EnterVehicleMoveNetwork>ENTER_VEHICLE_STD</EnterVehicleMoveNetwork>
      <EntryTranslation x="-2.225000" y="2.075000" z="0.500000" />
      <OpenDoorTranslation x="0.000000" y="0.000000" />
      <OpenDoorHeadingChange value="1.570000" />
      <EntryHeadingChange value="-1.570000" />
      <ExtraZForMPPlaneWarp value="-2.000000" />
      <EntryPointFlags>UseVehicleRelativeEntryPosition JackIncludesGetIn NavigateToWarpEntryPoint
        UseGetUpAfterJack JackVariationsIncludeGetIn DeadJackIncludesGetIn</EntryPointFlags>
      <EntryAnimVariations ref="ENTRY_ANIM_VARIATIONS_HELICOPTER_REAR_LEFT" />
      <NMJumpFromVehicleTuningSet />
    </Item>
    <Item type="CVehicleEntryPointAnimInfo">
      <Name>ENTRY_POINT_ANIM_HELI_CARGOBOB5_REAR_RIGHT</Name>
      <CommonClipSetMap ref="COMMON_CLIPSET_MAP_TRUCK_RIGHT" />
      <EntryClipSetMap ref="ENTRY_CLIPSET_MAP_HELI_REAR_RIGHT" />
      <ExitClipSetMap ref="EXIT_CLIPSET_MAP_HELI_REAR_RIGHT" />
      <AlternateTryLockedDoorClipId />
      <AlternateForcedEntryClipId />
      <AlternateJackFromOutSideClipId />
      <AlternateBeJackedFromOutSideClipId />
      <AlternateEntryPointClipSetId />
      <EnterVehicleMoveNetwork>ENTER_VEHICLE_STD</EnterVehicleMoveNetwork>
      <EntryTranslation x="2.225000" y="2.075000" z="0.500000" />
      <OpenDoorTranslation x="0.000000" y="0.000000" />
      <OpenDoorHeadingChange value="-1.570000" />
      <EntryHeadingChange value="1.570000" />
      <ExtraZForMPPlaneWarp value="-2.000000" />
      <EntryPointFlags>UseVehicleRelativeEntryPosition JackIncludesGetIn NavigateToWarpEntryPoint
        UseGetUpAfterJack JackVariationsIncludeGetIn DeadJackIncludesGetIn</EntryPointFlags>
      <EntryAnimVariations ref="ENTRY_ANIM_VARIATIONS_HELICOPTER_REAR_RIGHT" />
      <NMJumpFromVehicleTuningSet />
    </Item>
    <Item type="CVehicleEntryPointAnimInfo">
      <Name>ENTRY_POINT_ANIM_PLANE_DUSTER2_FRONT_LEFT</Name>
      <CommonClipSetMap ref="COMMON_CLIPSET_MAP_PLANE_FRONT_LEFT" />
      <EntryClipSetMap ref="ENTRY_CLIPSET_MAP_PLANE_DUSTER2_FRONT_LEFT" />
      <ExitClipSetMap ref="EXIT_CLIPSET_MAP_PLANE_DUSTER2_FRONT_LEFT" />
      <AlternateTryLockedDoorClipId />
      <AlternateForcedEntryClipId />
      <AlternateJackFromOutSideClipId />
      <AlternateBeJackedFromOutSideClipId />
      <AlternateEntryPointClipSetId />
      <EnterVehicleMoveNetwork>ENTER_VEHICLE_STD</EnterVehicleMoveNetwork>
      <EntryTranslation x="-1.250000" y="-0.198000" z="-0.953000" />
      <OpenDoorTranslation x="0.000000" y="0.000000" />
      <OpenDoorHeadingChange value="0.000000" />
      <EntryHeadingChange value="0.000000" />
      <ExtraZForMPPlaneWarp value="-2.000000" />
      <EntryPointFlags>HasClimbUp JackIncludesGetIn UsesNoDoorTransitionForEnter UseNewPlaneSystem
        DontCloseDoorOutside HasOnVehicleEntry UseGetUpAfterJack JackVariationsIncludeGetIn
        DeadJackIncludesGetIn</EntryPointFlags>
      <EntryAnimVariations ref="ENTRY_ANIM_VARIATIONS_PLANE_DUSTER2_FRONT_LEFT" />
      <NMJumpFromVehicleTuningSet />
    </Item>
    <Item type="CVehicleEntryPointAnimInfo">
      <Name>ENTRY_POINT_ANIM_PLANE_DUSTER2_FRONT_RIGHT</Name>
      <CommonClipSetMap ref="COMMON_CLIPSET_MAP_PLANE_FRONT_RIGHT" />
      <EntryClipSetMap ref="ENTRY_CLIPSET_MAP_PLANE_DUSTER2_FRONT_RIGHT" />
      <ExitClipSetMap ref="EXIT_CLIPSET_MAP_PLANE_DUSTER2_FRONT_RIGHT" />
      <AlternateTryLockedDoorClipId />
      <AlternateForcedEntryClipId />
      <AlternateJackFromOutSideClipId />
      <AlternateBeJackedFromOutSideClipId />
      <AlternateEntryPointClipSetId />
      <EnterVehicleMoveNetwork>ENTER_VEHICLE_STD</EnterVehicleMoveNetwork>
      <EntryTranslation x="1.250000" y="-0.198000" z="-0.953000" />
      <OpenDoorTranslation x="0.000000" y="0.000000" />
      <OpenDoorHeadingChange value="0.000000" />
      <EntryHeadingChange value="0.000000" />
      <ExtraZForMPPlaneWarp value="-2.000000" />
      <EntryPointFlags>HasClimbUp JackIncludesGetIn UsesNoDoorTransitionForEnter UseNewPlaneSystem
        DontCloseDoorOutside HasOnVehicleEntry UseGetUpAfterJack JackVariationsIncludeGetIn
        DeadJackIncludesGetIn</EntryPointFlags>
      <EntryAnimVariations ref="ENTRY_ANIM_VARIATIONS_PLANE_DUSTER2_FRONT_RIGHT" />
      <NMJumpFromVehicleTuningSet />
    </Item>
    <Item type="CVehicleEntryPointAnimInfo">
      <Name>ENTRY_POINT_ANIM_STD_POLTERMINUS_FRONT_LEFT</Name>
      <CommonClipSetMap ref="COMMON_CLIPSET_MAP_AUTOMOBILE_LEFT" />
      <EntryClipSetMap ref="ENTRY_CLIPSET_MAP_POLTERMINUS_FRONT_LEFT" />
      <ExitClipSetMap ref="EXIT_CLIPSET_MAP_POLTERMINUS_FRONT_LEFT" />
      <AlternateTryLockedDoorClipId />
      <AlternateForcedEntryClipId />
      <AlternateJackFromOutSideClipId>van_perp_ds_a</AlternateJackFromOutSideClipId>
      <AlternateBeJackedFromOutSideClipId>van_victim_ds_a</AlternateBeJackedFromOutSideClipId>
      <AlternateEntryPointClipSetId />
      <EnterVehicleMoveNetwork>ENTER_VEHICLE_STD</EnterVehicleMoveNetwork>
      <EntryTranslation x="-1.200000" y="-0.550000" z="0.000000" />
      <OpenDoorTranslation x="0.000000" y="0.000000" />
      <OpenDoorHeadingChange value="1.570000" />
      <EntryHeadingChange value="-1.570000" />
      <ExtraZForMPPlaneWarp value="-2.000000" />
      <EntryPointFlags>JackIncludesGetIn UsesNoDoorTransitionForExit UseOpenDoorBlendAnims
        UseGetUpAfterJack JackVariationsIncludeGetIn DeadJackIncludesGetIn HasCombatEntry</EntryPointFlags>
      <EntryAnimVariations ref="ENTRY_ANIM_VARIATIONS_VAN_FRONT_LEFT" />
      <NMJumpFromVehicleTuningSet>SLOWTRUCK</NMJumpFromVehicleTuningSet>
    </Item>
    <Item type="CVehicleEntryPointAnimInfo">
      <Name>ENTRY_POINT_ANIM_STD_POLTERMINUS_FRONT_RIGHT</Name>
      <CommonClipSetMap ref="COMMON_CLIPSET_MAP_AUTOMOBILE_RIGHT" />
      <EntryClipSetMap ref="ENTRY_CLIPSET_MAP_POLTERMINUS_FRONT_RIGHT" />
      <ExitClipSetMap ref="EXIT_CLIPSET_MAP_POLTERMINUS_FRONT_RIGHT" />
      <AlternateTryLockedDoorClipId />
      <AlternateForcedEntryClipId />
      <AlternateJackFromOutSideClipId>van_perp_ps_a</AlternateJackFromOutSideClipId>
      <AlternateBeJackedFromOutSideClipId>van_victim_ps_a</AlternateBeJackedFromOutSideClipId>
      <AlternateEntryPointClipSetId />
      <EnterVehicleMoveNetwork>ENTER_VEHICLE_STD</EnterVehicleMoveNetwork>
      <EntryTranslation x="1.200000" y="-0.500000" z="0.000000" />
      <OpenDoorTranslation x="0.000000" y="0.000000" />
      <OpenDoorHeadingChange value="-1.570000" />
      <EntryHeadingChange value="1.570000" />
      <ExtraZForMPPlaneWarp value="-2.000000" />
      <EntryPointFlags>JackIncludesGetIn UsesNoDoorTransitionForExit UseOpenDoorBlendAnims
        UseGetUpAfterJack JackVariationsIncludeGetIn DeadJackIncludesGetIn HasCombatEntry</EntryPointFlags>
      <EntryAnimVariations ref="ENTRY_ANIM_VARIATIONS_VAN_FRONT_RIGHT" />
      <NMJumpFromVehicleTuningSet>SLOWTRUCK</NMJumpFromVehicleTuningSet>
    </Item>
    <Item type="CVehicleEntryPointAnimInfo">
      <Name>ENTRY_POINT_ANIM_STD_POLTERMINUS_REAR_LEFT</Name>
      <CommonClipSetMap ref="COMMON_CLIPSET_MAP_AUTOMOBILE_LEFT" />
      <EntryClipSetMap ref="ENTRY_CLIPSET_MAP_STD_REAR_LEFT" />
      <ExitClipSetMap ref="EXIT_CLIPSET_MAP_STD_REAR_LEFT" />
      <AlternateTryLockedDoorClipId />
      <AlternateForcedEntryClipId />
      <AlternateJackFromOutSideClipId />
      <AlternateBeJackedFromOutSideClipId />
      <AlternateEntryPointClipSetId />
      <EnterVehicleMoveNetwork>ENTER_VEHICLE_STD</EnterVehicleMoveNetwork>
      <EntryTranslation x="-0.870000" y="-0.620000" z="0.000000" />
      <OpenDoorTranslation x="-0.200000" y="0.000000" />
      <OpenDoorHeadingChange value="1.570000" />
      <EntryHeadingChange value="-1.570000" />
      <ExtraZForMPPlaneWarp value="-2.000000" />
      <EntryPointFlags>JackIncludesGetIn UsesNoDoorTransitionForExit UseOpenDoorBlendAnims
        FixUpMoverToEntryPointOnExit UseGetUpAfterJack</EntryPointFlags>
      <EntryAnimVariations ref="ENTRY_ANIM_VARIATIONS_STANDARD_REAR_LEFT" />
      <NMJumpFromVehicleTuningSet />
    </Item>
    <Item type="CVehicleEntryPointAnimInfo">
      <Name>ENTRY_POINT_ANIM_STD_POLTERMINUS_REAR_RIGHT</Name>
      <CommonClipSetMap ref="COMMON_CLIPSET_MAP_AUTOMOBILE_RIGHT" />
      <EntryClipSetMap ref="ENTRY_CLIPSET_MAP_STD_REAR_RIGHT" />
      <ExitClipSetMap ref="EXIT_CLIPSET_MAP_STD_REAR_RIGHT" />
      <AlternateTryLockedDoorClipId />
      <AlternateForcedEntryClipId />
      <AlternateJackFromOutSideClipId />
      <AlternateBeJackedFromOutSideClipId />
      <AlternateEntryPointClipSetId />
      <EnterVehicleMoveNetwork>ENTER_VEHICLE_STD</EnterVehicleMoveNetwork>
      <EntryTranslation x="0.870000" y="-0.620000" z="0.000000" />
      <OpenDoorTranslation x="0.200000" y="0.000000" />
      <OpenDoorHeadingChange value="-1.570000" />
      <EntryHeadingChange value="1.570000" />
      <ExtraZForMPPlaneWarp value="-2.000000" />
      <EntryPointFlags>JackIncludesGetIn UsesNoDoorTransitionForExit UseOpenDoorBlendAnims
        FixUpMoverToEntryPointOnExit UseGetUpAfterJack</EntryPointFlags>
      <EntryAnimVariations ref="ENTRY_ANIM_VARIATIONS_STANDARD_REAR_RIGHT" />
      <NMJumpFromVehicleTuningSet />
    </Item>
    <Item type="CVehicleEntryPointAnimInfo">
      <Name>ENTRY_POINT_ANIM_PLANE_TITAN2_FRONT_LEFT</Name>
      <CommonClipSetMap ref="COMMON_CLIPSET_MAP_PLANE_TITAN2" />
      <EntryClipSetMap ref="ENTRY_CLIPSET_MAP_PLANE_TITAN2_FRONT_LEFT" />
      <ExitClipSetMap ref="EXIT_CLIPSET_MAP_PLANE_TITAN2_FRONT_LEFT" />
      <AlternateTryLockedDoorClipId />
      <AlternateForcedEntryClipId />
      <AlternateJackFromOutSideClipId />
      <AlternateBeJackedFromOutSideClipId />
      <AlternateEntryPointClipSetId />
      <EnterVehicleMoveNetwork>ENTER_VEHICLE_STD</EnterVehicleMoveNetwork>
      <EntryTranslation x="-2.126000" y="-1.298000" z="-1.503000" />
      <OpenDoorTranslation x="0.000000" y="0.000000" />
      <OpenDoorHeadingChange value="0.000000" />
      <EntryHeadingChange value="-1.570000" />
      <ExtraZForMPPlaneWarp value="-2.000000" />
      <EntryPointFlags>JackIncludesGetIn UsesNoDoorTransitionForEnter UseNewPlaneSystem
        PreventJackInterrupt DontCloseDoorOutside JackVariationsIncludeGetIn DeadJackIncludesGetIn</EntryPointFlags>
      <EntryAnimVariations ref="ENTRY_ANIM_VARIATIONS_PLANE_TITAN2_FRONT_LEFT" />
      <NMJumpFromVehicleTuningSet />
    </Item>
    <Item type="CVehicleEntryPointAnimInfo">
      <Name>ENTRY_POINT_ANIM_PLANE_TITAN2_FRONT_RIGHT</Name>
      <CommonClipSetMap ref="COMMON_CLIPSET_MAP_PLANE_TITAN2" />
      <EntryClipSetMap ref="ENTRY_CLIPSET_MAP_PLANE_TITAN2_FRONT_RIGHT" />
      <ExitClipSetMap ref="EXIT_CLIPSET_MAP_PLANE_TITAN2_FRONT_RIGHT" />
      <AlternateTryLockedDoorClipId />
      <AlternateForcedEntryClipId />
      <AlternateJackFromOutSideClipId />
      <AlternateBeJackedFromOutSideClipId />
      <AlternateEntryPointClipSetId />
      <EnterVehicleMoveNetwork>ENTER_VEHICLE_STD</EnterVehicleMoveNetwork>
      <EntryTranslation x="-3.242000" y="-1.298000" z="-1.502000" />
      <OpenDoorTranslation x="0.000000" y="0.000000" />
      <OpenDoorHeadingChange value="0.000000" />
      <EntryHeadingChange value="-1.570000" />
      <ExtraZForMPPlaneWarp value="-2.000000" />
      <EntryPointFlags>JackIncludesGetIn UsesNoDoorTransitionForEnter UseNewPlaneSystem
        DontCloseDoorInside PreventJackInterrupt DontCloseDoorOutside JackVariationsIncludeGetIn
        DeadJackIncludesGetIn</EntryPointFlags>
      <EntryAnimVariations ref="ENTRY_ANIM_VARIATIONS_PLANE_TITAN2_FRONT_RIGHT" />
      <NMJumpFromVehicleTuningSet />
    </Item>
    <Item type="CVehicleEntryPointAnimInfo">
      <Name>ENTRY_POINT_ANIM_PLANE_TITAN2_REAR_LEFT</Name>
      <CommonClipSetMap ref="COMMON_CLIPSET_MAP_PLANE_TITAN2" />
      <EntryClipSetMap ref="ENTRY_CLIPSET_MAP_PLANE_TITAN2_REAR_LEFT" />
      <ExitClipSetMap ref="EXIT_CLIPSET_MAP_PLANE_TITAN2_REAR_LEFT" />
      <AlternateTryLockedDoorClipId />
      <AlternateForcedEntryClipId />
      <AlternateJackFromOutSideClipId />
      <AlternateBeJackedFromOutSideClipId />
      <AlternateEntryPointClipSetId />
      <EnterVehicleMoveNetwork>ENTER_VEHICLE_STD</EnterVehicleMoveNetwork>
      <EntryTranslation x="-2.260000" y="2.338000" z="0.017000" />
      <OpenDoorTranslation x="-1.325000" y="0.025000" />
      <OpenDoorHeadingChange value="0.000000" />
      <EntryHeadingChange value="3.140000" />
      <ExtraZForMPPlaneWarp value="-2.000000" />
      <EntryPointFlags>JackIncludesGetIn UsesNoDoorTransitionForEnter UseNewPlaneSystem
        DontCloseDoorInside PreventJackInterrupt DontCloseDoorOutside JackVariationsIncludeGetIn
        DeadJackIncludesGetIn</EntryPointFlags>
      <EntryAnimVariations ref="ENTRY_ANIM_VARIATIONS_PLANE_TITAN2_REAR_LEFT" />
      <NMJumpFromVehicleTuningSet />
    </Item>
    <Item type="CVehicleEntryPointAnimInfo">
      <Name>ENTRY_POINT_ANIM_PLANE_TITAN2_REAR_RIGHT</Name>
      <CommonClipSetMap ref="COMMON_CLIPSET_MAP_PLANE_TITAN2" />
      <EntryClipSetMap ref="ENTRY_CLIPSET_MAP_PLANE_TITAN2_REAR_RIGHT" />
      <ExitClipSetMap ref="EXIT_CLIPSET_MAP_PLANE_TITAN2_REAR_RIGHT" />
      <AlternateTryLockedDoorClipId />
      <AlternateForcedEntryClipId />
      <AlternateJackFromOutSideClipId />
      <AlternateBeJackedFromOutSideClipId />
      <AlternateEntryPointClipSetId />
      <EnterVehicleMoveNetwork>ENTER_VEHICLE_STD</EnterVehicleMoveNetwork>
      <EntryTranslation x="1.815000" y="-3.500000" z="0.000000" />
      <OpenDoorTranslation x="-1.625000" y="0.025000" />
      <OpenDoorHeadingChange value="0.000000" />
      <EntryHeadingChange value="0.017500" />
      <ExtraZForMPPlaneWarp value="-2.000000" />
      <EntryPointFlags>JackIncludesGetIn UsesNoDoorTransitionForEnter UseNewPlaneSystem
        DontCloseDoorInside PreventJackInterrupt DontCloseDoorOutside JackVariationsIncludeGetIn
        DeadJackIncludesGetIn</EntryPointFlags>
      <EntryAnimVariations ref="ENTRY_ANIM_VARIATIONS_PLANE_TITAN2_REAR_RIGHT" />
      <NMJumpFromVehicleTuningSet />
    </Item>
    <Item type="CVehicleEntryPointAnimInfo">
      <Name>ENTRY_POINT_ANIM_PLANE_TITAN2_REAR_RIGHT_2</Name>
      <CommonClipSetMap ref="COMMON_CLIPSET_MAP_PLANE_TITAN2" />
      <EntryClipSetMap ref="ENTRY_CLIPSET_MAP_PLANE_TITAN2_REAR_RIGHT_2" />
      <ExitClipSetMap ref="EXIT_CLIPSET_MAP_PLANE_TITAN2_REAR_RIGHT_2" />
      <AlternateTryLockedDoorClipId />
      <AlternateForcedEntryClipId />
      <AlternateJackFromOutSideClipId />
      <AlternateBeJackedFromOutSideClipId />
      <AlternateEntryPointClipSetId />
      <EnterVehicleMoveNetwork>ENTER_VEHICLE_STD</EnterVehicleMoveNetwork>
      <EntryTranslation x="0.280000" y="-3.600000" z="0.000000" />
      <OpenDoorTranslation x="-1.625000" y="0.025000" />
      <OpenDoorHeadingChange value="0.000000" />
      <EntryHeadingChange value="-0.022500" />
      <ExtraZForMPPlaneWarp value="-2.000000" />
      <EntryPointFlags>JackIncludesGetIn UsesNoDoorTransitionForEnter UseNewPlaneSystem
        DontCloseDoorInside PreventJackInterrupt DontCloseDoorOutside JackVariationsIncludeGetIn
        DeadJackIncludesGetIn</EntryPointFlags>
      <EntryAnimVariations ref="ENTRY_ANIM_VARIATIONS_PLANE_TITAN2_REAR_RIGHT_2" />
      <NMJumpFromVehicleTuningSet />
    </Item>
    <Item type="CVehicleEntryPointAnimInfo">
      <Name>ENTRY_POINT_ANIM_PLANE_TITAN2_CARGO_WARP_REAR_LEFT</Name>
      <CommonClipSetMap ref="COMMON_CLIPSET_MAP_PLANE_JET" />
      <EntryClipSetMap ref="ENTRY_CLIPSET_MAP_PLANE_MILJET_FRONT_LEFT" />
      <ExitClipSetMap ref="EXIT_CLIPSET_MAP_PLANE_MILJET_FRONT_LEFT" />
      <AlternateTryLockedDoorClipId />
      <AlternateForcedEntryClipId />
      <AlternateJackFromOutSideClipId />
      <AlternateBeJackedFromOutSideClipId />
      <AlternateEntryPointClipSetId />
      <EnterVehicleMoveNetwork>ENTER_VEHICLE_STD</EnterVehicleMoveNetwork>
      <EntryTranslation x="-0.100000" y="-14.400000" z="-0.650000" />
      <OpenDoorTranslation x="0.000000" y="0.000000" />
      <OpenDoorHeadingChange value="0.000000" />
      <EntryHeadingChange value="0.000000" />
      <ExtraZForMPPlaneWarp value="-2.000000" />
      <EntryPointFlags>UseVehicleRelativeEntryPosition WarpOut UseNewPlaneSystem DontCloseDoorInside
        DontCloseDoorOutside NavigateToWarpEntryPoint</EntryPointFlags>
      <EntryAnimVariations ref="NULL" />
      <NMJumpFromVehicleTuningSet />
    </Item>
    <Item type="CVehicleEntryPointAnimInfo">
      <Name>ENTRY_POINT_ANIM_PLANE_TITAN2_CARGO_WARP_REAR_RIGHT</Name>
      <CommonClipSetMap ref="COMMON_CLIPSET_MAP_PLANE_JET" />
      <EntryClipSetMap ref="ENTRY_CLIPSET_MAP_PLANE_MILJET_FRONT_RIGHT" />
      <ExitClipSetMap ref="EXIT_CLIPSET_MAP_PLANE_MILJET_FRONT_RIGHT" />
      <AlternateTryLockedDoorClipId />
      <AlternateForcedEntryClipId />
      <AlternateJackFromOutSideClipId />
      <AlternateBeJackedFromOutSideClipId />
      <AlternateEntryPointClipSetId />
      <EnterVehicleMoveNetwork>ENTER_VEHICLE_STD</EnterVehicleMoveNetwork>
      <EntryTranslation x="0.100000" y="-14.400000" z="-0.650000" />
      <OpenDoorTranslation x="0.000000" y="0.000000" />
      <OpenDoorHeadingChange value="0.000000" />
      <EntryHeadingChange value="0.000000" />
      <ExtraZForMPPlaneWarp value="-2.000000" />
      <EntryPointFlags>UseVehicleRelativeEntryPosition WarpOut UseNewPlaneSystem DontCloseDoorInside
        DontCloseDoorOutside NavigateToWarpEntryPoint</EntryPointFlags>
      <EntryAnimVariations ref="NULL" />
      <NMJumpFromVehicleTuningSet />
    </Item>
    <Item type="CVehicleEntryPointAnimInfo">
      <Name>ENTRY_POINT_ANIM_STD_FIREBOLT_FRONT_LEFT</Name>
      <CommonClipSetMap ref="COMMON_CLIPSET_MAP_AUTOMOBILE_LEFT" />
      <EntryClipSetMap ref="ENTRY_CLIPSET_MAP_STD_BOBCAT_FRONT_LEFT" />
      <ExitClipSetMap ref="EXIT_CLIPSET_MAP_STD_FRONT_LEFT" />
      <AlternateTryLockedDoorClipId>STD_Locked_DS</AlternateTryLockedDoorClipId>
      <AlternateForcedEntryClipId>STD_Force_Entry_DS</AlternateForcedEntryClipId>
      <AlternateJackFromOutSideClipId>std_perp_ds</AlternateJackFromOutSideClipId>
      <AlternateBeJackedFromOutSideClipId>std_victim_ds</AlternateBeJackedFromOutSideClipId>
      <AlternateEntryPointClipSetId>clipset@veh@std@bobcat@ds@female@enter_exit</AlternateEntryPointClipSetId>
      <EnterVehicleMoveNetwork>ENTER_VEHICLE_STD</EnterVehicleMoveNetwork>
      <EntryTranslation x="-1.000000" y="-0.450000" z="0.500000" />
      <OpenDoorTranslation x="-0.050000" y="0.150000" />
      <OpenDoorHeadingChange value="1.570000" />
      <EntryHeadingChange value="-1.570000" />
      <ExtraZForMPPlaneWarp value="-2.000000" />
      <EntryPointFlags>JackIncludesGetIn UsesNoDoorTransitionForExit UseOpenDoorBlendAnims
        FixUpMoverToEntryPointOnExit UseGetUpAfterJack JackVariationsIncludeGetIn HasCombatEntry</EntryPointFlags>
      <EntryAnimVariations ref="ENTRY_ANIM_VARIATIONS_STANDARD_FRONT_LEFT" />
      <NMJumpFromVehicleTuningSet>SLOWTRUCK</NMJumpFromVehicleTuningSet>
    </Item>
    <Item type="CVehicleEntryPointAnimInfo">
      <Name>ENTRY_POINT_ANIM_STD_FIREBOLT_FRONT_RIGHT</Name>
      <CommonClipSetMap ref="COMMON_CLIPSET_MAP_AUTOMOBILE_RIGHT" />
      <EntryClipSetMap ref="ENTRY_CLIPSET_MAP_STD_BOBCAT_FRONT_RIGHT" />
      <ExitClipSetMap ref="EXIT_CLIPSET_MAP_STD_FRONT_RIGHT" />
      <AlternateTryLockedDoorClipId>STD_Locked_PS</AlternateTryLockedDoorClipId>
      <AlternateForcedEntryClipId>STD_Force_Entry_PS</AlternateForcedEntryClipId>
      <AlternateJackFromOutSideClipId>std_perp_ps</AlternateJackFromOutSideClipId>
      <AlternateBeJackedFromOutSideClipId>std_victim_ps</AlternateBeJackedFromOutSideClipId>
      <AlternateEntryPointClipSetId>clipset@veh@std@bobcat@ps@female@enter_exit</AlternateEntryPointClipSetId>
      <EnterVehicleMoveNetwork>ENTER_VEHICLE_STD</EnterVehicleMoveNetwork>
      <EntryTranslation x="1.000000" y="-0.450000" z="0.500000" />
      <OpenDoorTranslation x="0.050000" y="0.150000" />
      <OpenDoorHeadingChange value="-1.570000" />
      <EntryHeadingChange value="1.570000" />
      <ExtraZForMPPlaneWarp value="-2.000000" />
      <EntryPointFlags>JackIncludesGetIn UsesNoDoorTransitionForExit UseOpenDoorBlendAnims
        FixUpMoverToEntryPointOnExit UseGetUpAfterJack JackVariationsIncludeGetIn HasCombatEntry</EntryPointFlags>
      <EntryAnimVariations ref="ENTRY_ANIM_VARIATIONS_STANDARD_FRONT_RIGHT" />
      <NMJumpFromVehicleTuningSet />
    </Item>
  </VehicleEntryPointAnimInfos>
  <VehicleExplosionInfos />
  <VehicleLayoutInfos>
    <Item type="CVehicleLayoutInfo">
      <Name>LAYOUT_LOW_BANSHEE3</Name>
      <Seats>
        <Item>
          <SeatInfo ref="SEAT_LOW_FRONT_LEFT" />
          <SeatAnimInfo ref="SEAT_ANIM_LOW_BANSHEE3_FRONT_LEFT" />
        </Item>
        <Item>
          <SeatInfo ref="SEAT_LOW_FRONT_RIGHT" />
          <SeatAnimInfo ref="SEAT_ANIM_LOW_FRONT_RIGHT" />
        </Item>
      </Seats>
      <EntryPoints>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_LOW_FRONT_LEFT" />
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_LOW_FRONT_LEFT" />
        </Item>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_LOW_FRONT_RIGHT" />
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_LOW_FRONT_RIGHT" />
        </Item>
      </EntryPoints>
      <LayoutFlags>StreamAnims UseDoorOscillation UseLeanSteerAnims UseSteeringWheelIk</LayoutFlags>
      <BicycleInfo ref="NULL" />
      <AnimRateSet ref="STD_LOW_ANIM_RATE_SET" />
      <HandsUpClipSetId>busted_vehicle_low</HandsUpClipSetId>
      <SteeringWheelOffset x="0.000000" y="0.350000" z="0.320000" />
      <MaxXAcceleration value="15.000000" />
      <BodyLeanXApproachSpeed value="10.000000" />
      <BodyLeanXSmallDelta value="0.400000" />
      <LookBackApproachSpeedScale value="0.500000" />
      <FirstPersonAdditiveIdleClipSets>
        <Item>clipset@veh@low@ds@idle_a</Item>
        <Item>clipset@veh@low@ds@idle_b</Item>
        <Item>clipset@veh@low@ds@idle_c</Item>
        <Item>clipset@veh@low@ds@idle_d</Item>
        <Item>clipset@veh@low@ds@idle_e</Item>
      </FirstPersonAdditiveIdleClipSets>
      <FirstPersonRoadRageClipSets>
        <Item>clipset@veh@low@ds@hit_wheel@idle_a</Item>
        <Item>clipset@veh@low@ds@hit_wheel@idle_b</Item>
        <Item>clipset@veh@low@ds@hit_wheel@idle_c</Item>
      </FirstPersonRoadRageClipSets>
      <CellphoneClipsets>
        <CellphoneClipsetDS>ANIM_GROUP_CELLPHONE_IN_CAR_DS</CellphoneClipsetDS>
        <CellphoneClipsetPS>ANIM_GROUP_CELLPHONE_IN_CAR_PS</CellphoneClipsetPS>
        <CellphoneClipsetDS_FPS>ANIM_GROUP_CELLPHONE_IN_CAR_DS_FPS</CellphoneClipsetDS_FPS>
        <CellphoneClipsetPS_FPS>ANIM_GROUP_CELLPHONE_IN_CAR_DS_FPS</CellphoneClipsetPS_FPS>
      </CellphoneClipsets>
    </Item>
    <Item type="CVehicleLayoutInfo">
      <Name>LAYOUT_HELI_CARGOBOB5</Name>
      <Seats>
        <Item>
          <SeatInfo ref="SEAT_STANDARD_FRONT_LEFT" />
          <SeatAnimInfo ref="SEAT_ANIM_HELI_FRONT_LEFT" />
        </Item>
        <Item>
          <SeatInfo ref="SEAT_STANDARD_FRONT_RIGHT" />
          <SeatAnimInfo ref="SEAT_ANIM_HELI_CARGOBOB5_FRONT_RIGHT" />
        </Item>
        <Item>
          <SeatInfo ref="SEAT_HELI_CARGOBOB_REAR_LEFT" />
          <SeatAnimInfo ref="SEAT_ANIM_PLANE_TITAN_LEFT" />
        </Item>
        <Item>
          <SeatInfo ref="SEAT_HELI_CARGOBOB_REAR_RIGHT" />
          <SeatAnimInfo ref="SEAT_ANIM_PLANE_TITAN_RIGHT" />
        </Item>
        <Item>
          <SeatInfo ref="SEAT_EXTRA_NO_SHUFFLE_LEFT_1" />
          <SeatAnimInfo ref="SEAT_ANIM_PLANE_TITAN_LEFT" />
        </Item>
        <Item>
          <SeatInfo ref="SEAT_EXTRA_NO_SHUFFLE_RIGHT_1" />
          <SeatAnimInfo ref="SEAT_ANIM_PLANE_TITAN_RIGHT" />
        </Item>
        <Item>
          <SeatInfo ref="SEAT_EXTRA_NO_SHUFFLE_LEFT_2" />
          <SeatAnimInfo ref="SEAT_ANIM_PLANE_TITAN_LEFT" />
        </Item>
        <Item>
          <SeatInfo ref="SEAT_EXTRA_NO_SHUFFLE_RIGHT_2" />
          <SeatAnimInfo ref="SEAT_ANIM_PLANE_TITAN_RIGHT" />
        </Item>
        <Item>
          <SeatInfo ref="SEAT_EXTRA_NO_SHUFFLE_LEFT_3" />
          <SeatAnimInfo ref="SEAT_ANIM_PLANE_TITAN_LEFT" />
        </Item>
        <Item>
          <SeatInfo ref="SEAT_EXTRA_NO_SHUFFLE_RIGHT_3" />
          <SeatAnimInfo ref="SEAT_ANIM_PLANE_TITAN_RIGHT" />
        </Item>
        <Item>
          <SeatInfo ref="SEAT_EXTRA_NO_SHUFFLE_LEFT_4" />
          <SeatAnimInfo ref="SEAT_ANIM_PLANE_TITAN_LEFT" />
        </Item>
        <Item>
          <SeatInfo ref="SEAT_EXTRA_NO_SHUFFLE_RIGHT_4" />
          <SeatAnimInfo ref="SEAT_ANIM_PLANE_TITAN_RIGHT" />
        </Item>
        <Item>
          <SeatInfo ref="SEAT_EXTRA_NO_SHUFFLE_LEFT_5" />
          <SeatAnimInfo ref="SEAT_ANIM_PLANE_TITAN_LEFT" />
        </Item>
        <Item>
          <SeatInfo ref="SEAT_EXTRA_NO_SHUFFLE_RIGHT_5" />
          <SeatAnimInfo ref="SEAT_ANIM_PLANE_TITAN_RIGHT" />
        </Item>
        <Item>
          <SeatInfo ref="SEAT_EXTRA_NO_SHUFFLE_LEFT_6" />
          <SeatAnimInfo ref="SEAT_ANIM_PLANE_TITAN_LEFT" />
        </Item>
        <Item>
          <SeatInfo ref="SEAT_EXTRA_NO_SHUFFLE_RIGHT_6" />
          <SeatAnimInfo ref="SEAT_ANIM_PLANE_TITAN_RIGHT" />
        </Item>
      </Seats>
      <EntryPoints>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_STANDARD_FRONT_LEFT" />
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_HELI_CARGOBOB5_FRONT_LEFT" />
        </Item>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_STANDARD_FRONT_RIGHT" />
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_HELI_CARGOBOB5_FRONT_RIGHT" />
        </Item>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_MP_HELI_CARGOBOB5_WARP_REAR_LEFT" />
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_HELI_CARGOBOB5_REAR_LEFT" />
        </Item>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_MP_HELI_CARGOBOB5_WARP_REAR_RIGHT" />
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_HELI_CARGOBOB5_REAR_RIGHT" />
        </Item>
      </EntryPoints>
      <LayoutFlags>StreamAnims UseFinerAlignTolerance Use2DBodyBlend IgnoreFrontSeatsWhenOnVehicle</LayoutFlags>
      <BicycleInfo ref="NULL" />
      <AnimRateSet ref="NULL" />
      <HandsUpClipSetId>busted_vehicle_std</HandsUpClipSetId>
      <SteeringWheelOffset x="0.000000" y="0.350000" z="0.320000" />
      <MaxXAcceleration value="25.000000" />
      <BodyLeanXApproachSpeed value="15.000000" />
      <BodyLeanXSmallDelta value="0.100000" />
      <LookBackApproachSpeedScale value="1.000000" />
      <FirstPersonAdditiveIdleClipSets />
      <FirstPersonRoadRageClipSets />
      <CellphoneClipsets>
        <CellphoneClipsetDS>ANIM_GROUP_CELLPHONE_IN_CAR_DS</CellphoneClipsetDS>
        <CellphoneClipsetPS>ANIM_GROUP_CELLPHONE_IN_CAR_PS</CellphoneClipsetPS>
        <CellphoneClipsetDS_FPS>ANIM_GROUP_CELLPHONE_IN_CAR_DS_FPS</CellphoneClipsetDS_FPS>
        <CellphoneClipsetPS_FPS>ANIM_GROUP_CELLPHONE_IN_CAR_DS_FPS</CellphoneClipsetPS_FPS>
      </CellphoneClipsets>
    </Item>
    <Item type="CVehicleLayoutInfo">
      <Name>LAYOUT_PLANE_DUSTER2</Name>
      <Seats>
        <Item>
          <SeatInfo ref="SEAT_SINGLE_FRONT" />
          <SeatAnimInfo ref="SEAT_ANIM_PLANE_DUSTER2_FRONT" />
        </Item>
      </Seats>
      <EntryPoints>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_PLANE_DUSTER2_FRONT_LEFT" />
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_PLANE_DUSTER2_FRONT_LEFT" />
        </Item>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_PLANE_DUSTER2_FRONT_RIGHT" />
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_PLANE_DUSTER2_FRONT_RIGHT" />
        </Item>
      </EntryPoints>
      <LayoutFlags>StreamAnims MustCloseDoor DisableJackingAndBusting UseFinerAlignTolerance
        Use2DBodyBlend</LayoutFlags>
      <BicycleInfo ref="NULL" />
      <AnimRateSet ref="NULL" />
      <HandsUpClipSetId>busted_vehicle_std</HandsUpClipSetId>
      <SteeringWheelOffset x="0.000000" y="0.350000" z="0.320000" />
      <MaxXAcceleration value="25.000000" />
      <BodyLeanXApproachSpeed value="15.000000" />
      <BodyLeanXSmallDelta value="0.100000" />
      <LookBackApproachSpeedScale value="1.000000" />
      <FirstPersonAdditiveIdleClipSets />
      <FirstPersonRoadRageClipSets />
      <CellphoneClipsets>
        <CellphoneClipsetDS>ANIM_GROUP_CELLPHONE_IN_CAR_DS</CellphoneClipsetDS>
        <CellphoneClipsetPS>ANIM_GROUP_CELLPHONE_IN_CAR_PS</CellphoneClipsetPS>
        <CellphoneClipsetDS_FPS>ANIM_GROUP_CELLPHONE_IN_CAR_DS_FPS</CellphoneClipsetDS_FPS>
        <CellphoneClipsetPS_FPS>ANIM_GROUP_CELLPHONE_IN_CAR_DS_FPS</CellphoneClipsetPS_FPS>
      </CellphoneClipsets>
    </Item>
    <Item type="CVehicleLayoutInfo">
      <Name>LAYOUT_STD_DRIFTJESTER3</Name>
      <Seats>
        <Item>
          <SeatInfo ref="SEAT_STD_DRIFTJESTER3_FRONT_LEFT" />
          <SeatAnimInfo ref="SEAT_ANIM_STANDARD_FRONT_LEFT" />
        </Item>
        <Item>
          <SeatInfo ref="SEAT_STD_DRIFTJESTER3_FRONT_RIGHT" />
          <SeatAnimInfo ref="SEAT_ANIM_STANDARD_FRONT_RIGHT" />
        </Item>
      </Seats>
      <EntryPoints>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_STD_DRIFTJESTER3_FRONT_LEFT" />
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_STD_EXITFIXUP_FRONT_LEFT" />
        </Item>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_STD_DRIFTJESTER3_FRONT_RIGHT" />
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_STD_EXITFIXUP_FRONT_RIGHT" />
        </Item>
      </EntryPoints>
      <LayoutFlags>StreamAnims UseDoorOscillation UseLeanSteerAnims UseSteeringWheelIk
        UseFinerAlignTolerance</LayoutFlags>
      <BicycleInfo ref="NULL" />
      <AnimRateSet ref="STD_LOW_ANIM_RATE_SET" />
      <HandsUpClipSetId>busted_vehicle_std</HandsUpClipSetId>
      <SteeringWheelOffset x="0.000000" y="0.350000" z="0.320000" />
      <MaxXAcceleration value="4.00000" />
      <BodyLeanXApproachSpeed value="5.00000" />
      <BodyLeanXSmallDelta value="0.30000" />
      <LookBackApproachSpeedScale value="1.00000" />
      <FirstPersonAdditiveIdleClipSets>
        <Item>clipset@veh@std@ds@idle_a</Item>
        <Item>clipset@veh@std@ds@idle_b</Item>
        <Item>clipset@veh@std@ds@idle_c</Item>
        <Item>clipset@veh@std@ds@idle_d</Item>
        <Item>clipset@veh@std@ds@idle_e</Item>
      </FirstPersonAdditiveIdleClipSets>
      <FirstPersonRoadRageClipSets>
        <Item>clipset@veh@std@ds@hit_wheel@idle_a</Item>
        <Item>clipset@veh@std@ds@hit_wheel@idle_b</Item>
        <Item>clipset@veh@std@ds@hit_wheel@idle_c</Item>
      </FirstPersonRoadRageClipSets>
    </Item>
    <Item type="CVehicleLayoutInfo">
      <Name>LAYOUT_STD_POLTERMINUS</Name>
      <Seats>
        <Item>
          <SeatInfo ref="SEAT_STANDARD_FRONT_LEFT" />
          <SeatAnimInfo ref="SEAT_ANIM_STD_POLTERMINUS_FRONT_LEFT" />
        </Item>
        <Item>
          <SeatInfo ref="SEAT_STANDARD_FRONT_RIGHT" />
          <SeatAnimInfo ref="SEAT_ANIM_STD_POLTERMINUS_FRONT_RIGHT" />
        </Item>
        <Item>
          <SeatInfo ref="SEAT_STANDARD_REAR_LEFT" />
          <SeatAnimInfo ref="SEAT_ANIM_STANDARD_REAR_LEFT" />
        </Item>
        <Item>
          <SeatInfo ref="SEAT_STANDARD_REAR_RIGHT" />
          <SeatAnimInfo ref="SEAT_ANIM_STANDARD_REAR_RIGHT" />
        </Item>
      </Seats>
      <EntryPoints>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_STANDARD_FRONT_LEFT" />
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_STD_POLTERMINUS_FRONT_LEFT" />
        </Item>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_STANDARD_FRONT_RIGHT" />
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_STD_POLTERMINUS_FRONT_RIGHT" />
        </Item>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_STANDARD_REAR_LEFT" />
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_STD_POLTERMINUS_REAR_LEFT" />
        </Item>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_STANDARD_REAR_RIGHT" />
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_STD_POLTERMINUS_REAR_RIGHT" />
        </Item>
      </EntryPoints>
      <LayoutFlags>StreamAnims UseDoorOscillation UseLeanSteerAnims UseSteeringWheelIk</LayoutFlags>
      <BicycleInfo ref="NULL" />
      <AnimRateSet ref="STD_LOW_ANIM_RATE_SET" />
      <HandsUpClipSetId>busted_vehicle_std</HandsUpClipSetId>
      <SteeringWheelOffset x="0.000000" y="0.350000" z="0.320000" />
      <MaxXAcceleration value="4.000000" />
      <BodyLeanXApproachSpeed value="5.000000" />
      <BodyLeanXSmallDelta value="0.300000" />
      <LookBackApproachSpeedScale value="1.000000" />
      <FirstPersonAdditiveIdleClipSets>
        <Item>clipset@veh@std@ds@idle_a</Item>
        <Item>clipset@veh@std@ds@idle_b</Item>
        <Item>clipset@veh@std@ds@idle_c</Item>
        <Item>clipset@veh@std@ds@idle_d</Item>
        <Item>clipset@veh@std@ds@idle_e</Item>
      </FirstPersonAdditiveIdleClipSets>
      <FirstPersonRoadRageClipSets>
        <Item>clipset@veh@std@ds@hit_wheel@idle_a</Item>
        <Item>clipset@veh@std@ds@hit_wheel@idle_b</Item>
        <Item>clipset@veh@std@ds@hit_wheel@idle_c</Item>
      </FirstPersonRoadRageClipSets>
      <CellphoneClipsets>
        <CellphoneClipsetDS>ANIM_GROUP_CELLPHONE_IN_CAR_DS</CellphoneClipsetDS>
        <CellphoneClipsetPS>ANIM_GROUP_CELLPHONE_IN_CAR_PS</CellphoneClipsetPS>
        <CellphoneClipsetDS_FPS>ANIM_GROUP_CELLPHONE_IN_CAR_DS_FPS</CellphoneClipsetDS_FPS>
        <CellphoneClipsetPS_FPS>ANIM_GROUP_CELLPHONE_IN_CAR_DS_FPS</CellphoneClipsetPS_FPS>
      </CellphoneClipsets>
    </Item>
    <Item type="CVehicleLayoutInfo">
      <Name>LAYOUT_PLANE_TITAN2</Name>
      <Seats>
        <Item>
          <SeatInfo ref="SEAT_STANDARD_FRONT_LEFT" />
          <SeatAnimInfo ref="SEAT_ANIM_PLANE_JET_FRONT_LEFT" />
        </Item>
        <Item>
          <SeatInfo ref="SEAT_STANDARD_FRONT_RIGHT" />
          <SeatAnimInfo ref="SEAT_ANIM_PLANE_JET_FRONT_RIGHT" />
        </Item>
        <Item>
          <SeatInfo ref="SEAT_PLANE_TITAN2_REAR_LEFT" />
          <SeatAnimInfo ref="SEAT_ANIM_PLANE_TITAN2_REAR_LEFT" />
        </Item>
        <Item>
          <SeatInfo ref="SEAT_PLANE_TITAN2_REAR_RIGHT" />
          <SeatAnimInfo ref="SEAT_ANIM_PLANE_TITAN2_REAR_RIGHT" />
        </Item>
        <Item>
          <SeatInfo ref="SEAT_PLANE_TITAN2_REAR_RIGHT_2" />
          <SeatAnimInfo ref="SEAT_ANIM_PLANE_TITAN2_REAR_RIGHT" />
        </Item>
        <Item>
          <SeatInfo ref="SEAT_EXTRA_NO_SHUFFLE_LEFT_1" />
          <SeatAnimInfo ref="SEAT_ANIM_PLANE_TITAN_LEFT" />
        </Item>
        <Item>
          <SeatInfo ref="SEAT_EXTRA_NO_SHUFFLE_RIGHT_2" />
          <SeatAnimInfo ref="SEAT_ANIM_PLANE_TITAN_RIGHT" />
        </Item>
        <Item>
          <SeatInfo ref="SEAT_EXTRA_NO_SHUFFLE_LEFT_2" />
          <SeatAnimInfo ref="SEAT_ANIM_PLANE_TITAN_LEFT" />
        </Item>
        <Item>
          <SeatInfo ref="SEAT_EXTRA_NO_SHUFFLE_RIGHT_3" />
          <SeatAnimInfo ref="SEAT_ANIM_PLANE_TITAN_RIGHT" />
        </Item>
        <Item>
          <SeatInfo ref="SEAT_EXTRA_NO_SHUFFLE_LEFT_3" />
          <SeatAnimInfo ref="SEAT_ANIM_PLANE_TITAN_LEFT" />
        </Item>
        <Item>
          <SeatInfo ref="SEAT_EXTRA_NO_SHUFFLE_RIGHT_4" />
          <SeatAnimInfo ref="SEAT_ANIM_PLANE_TITAN_RIGHT" />
        </Item>
        <Item>
          <SeatInfo ref="SEAT_EXTRA_NO_SHUFFLE_LEFT_4" />
          <SeatAnimInfo ref="SEAT_ANIM_PLANE_TITAN_LEFT" />
        </Item>
        <Item>
          <SeatInfo ref="SEAT_EXTRA_NO_SHUFFLE_RIGHT_5" />
          <SeatAnimInfo ref="SEAT_ANIM_PLANE_TITAN_RIGHT" />
        </Item>
        <Item>
          <SeatInfo ref="SEAT_EXTRA_NO_SHUFFLE_LEFT_5" />
          <SeatAnimInfo ref="SEAT_ANIM_PLANE_TITAN_LEFT" />
        </Item>
        <Item>
          <SeatInfo ref="SEAT_EXTRA_NO_SHUFFLE_RIGHT_6" />
          <SeatAnimInfo ref="SEAT_ANIM_PLANE_TITAN_RIGHT" />
        </Item>
        <Item>
          <SeatInfo ref="SEAT_EXTRA_NO_SHUFFLE_LEFT_6" />
          <SeatAnimInfo ref="SEAT_ANIM_PLANE_TITAN_LEFT" />
        </Item>
      </Seats>
      <EntryPoints>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_PLANE_TITAN2_FRONT_LEFT" />
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_PLANE_TITAN2_FRONT_LEFT" />
        </Item>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_PLANE_TITAN2_FRONT_RIGHT" />
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_PLANE_TITAN2_FRONT_RIGHT" />
        </Item>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_PLANE_TITAN2_REAR_LEFT" />
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_PLANE_TITAN2_REAR_LEFT" />
        </Item>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_PLANE_TITAN2_REAR_RIGHT" />
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_PLANE_TITAN2_REAR_RIGHT" />
        </Item>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_PLANE_TITAN2_REAR_RIGHT_2" />
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_PLANE_TITAN2_REAR_RIGHT_2" />
        </Item>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_PLANE_TITAN2_CARGO_WARP_REAR_LEFT" />
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_PLANE_TITAN2_CARGO_WARP_REAR_LEFT" />
        </Item>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_PLANE_TITAN2_CARGO_WARP_REAR_RIGHT" />
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_PLANE_TITAN2_CARGO_WARP_REAR_RIGHT" />
        </Item>
      </EntryPoints>
      <LayoutFlags>StreamAnims AllowEarlyDoorAndSeatUnreservation NoArmIkOnOutsideOpenDoor
        DisableJackingAndBusting ClimbUpAfterOpenDoor UseFinerAlignTolerance Use2DBodyBlend
        WarpInWhenStoodOnTop</LayoutFlags>
      <BicycleInfo ref="NULL" />
      <AnimRateSet ref="NULL" />
      <HandsUpClipSetId>busted_vehicle_std</HandsUpClipSetId>
      <SteeringWheelOffset x="0.000000" y="0.350000" z="0.320000" />
      <MaxXAcceleration value="25.000000" />
      <BodyLeanXApproachSpeed value="15.000000" />
      <BodyLeanXSmallDelta value="0.100000" />
      <LookBackApproachSpeedScale value="1.000000" />
      <FirstPersonAdditiveIdleClipSets />
      <FirstPersonRoadRageClipSets />
      <CellphoneClipsets>
        <CellphoneClipsetDS>ANIM_GROUP_CELLPHONE_IN_CAR_DS</CellphoneClipsetDS>
        <CellphoneClipsetPS>ANIM_GROUP_CELLPHONE_IN_CAR_PS</CellphoneClipsetPS>
        <CellphoneClipsetDS_FPS>ANIM_GROUP_CELLPHONE_IN_CAR_DS_FPS</CellphoneClipsetDS_FPS>
        <CellphoneClipsetPS_FPS>ANIM_GROUP_CELLPHONE_IN_CAR_DS_FPS</CellphoneClipsetPS_FPS>
      </CellphoneClipsets>
    </Item>
    <Item type="CVehicleLayoutInfo">
      <Name>LAYOUT_STD_FIREBOLT</Name>
      <Seats>
        <Item>
          <SeatInfo ref="SEAT_STANDARD_FRONT_LEFT" />
          <SeatAnimInfo ref="SEAT_ANIM_STANDARD_FRONT_LEFT" />
        </Item>
        <Item>
          <SeatInfo ref="SEAT_STANDARD_FRONT_RIGHT" />
          <SeatAnimInfo ref="SEAT_ANIM_STD_FIREBOLT_FRONT_RIGHT" />
        </Item>
      </Seats>
      <EntryPoints>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_STANDARD_FRONT_LEFT" />
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_STD_FIREBOLT_FRONT_LEFT" />
        </Item>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_STANDARD_FRONT_RIGHT" />
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_STD_FIREBOLT_FRONT_RIGHT" />
        </Item>
      </EntryPoints>
      <LayoutFlags>StreamAnims NoArmIkOnOutsideCloseDoor UseLeanSteerAnims UseSteeringWheelIk
        UseFinerAlignTolerance</LayoutFlags>
      <BicycleInfo ref="NULL" />
      <AnimRateSet ref="STD_LOW_ANIM_RATE_SET" />
      <HandsUpClipSetId>busted_vehicle_std</HandsUpClipSetId>
      <SteeringWheelOffset x="0.000000" y="0.350000" z="0.320000" />
      <MaxXAcceleration value="4.000000" />
      <BodyLeanXApproachSpeed value="5.000000" />
      <BodyLeanXSmallDelta value="0.300000" />
      <LookBackApproachSpeedScale value="0.800000" />
      <FirstPersonAdditiveIdleClipSets>
        <Item>clipset@veh@std@ds@idle_a</Item>
        <Item>clipset@veh@std@ds@idle_b</Item>
        <Item>clipset@veh@std@ds@idle_c</Item>
        <Item>clipset@veh@std@ds@idle_d</Item>
        <Item>clipset@veh@std@ds@idle_e</Item>
      </FirstPersonAdditiveIdleClipSets>
      <FirstPersonRoadRageClipSets>
        <Item>clipset@veh@std@ds@hit_wheel@idle_a</Item>
        <Item>clipset@veh@std@ds@hit_wheel@idle_b</Item>
        <Item>clipset@veh@std@ds@hit_wheel@idle_c</Item>
      </FirstPersonRoadRageClipSets>
      <CellphoneClipsets>
        <CellphoneClipsetDS>ANIM_GROUP_CELLPHONE_IN_CAR_DS</CellphoneClipsetDS>
        <CellphoneClipsetPS>ANIM_GROUP_CELLPHONE_IN_CAR_PS</CellphoneClipsetPS>
        <CellphoneClipsetDS_FPS>ANIM_GROUP_CELLPHONE_IN_CAR_DS_FPS</CellphoneClipsetDS_FPS>
        <CellphoneClipsetPS_FPS>ANIM_GROUP_CELLPHONE_IN_CAR_DS_FPS</CellphoneClipsetPS_FPS>
      </CellphoneClipsets>
    </Item>
  </VehicleLayoutInfos>
  <VehicleScenarioLayoutInfos />
  <SeatOverrideAnimInfos />
  <InVehicleOverrideInfos />
  <FirstPersonDriveByLookAroundData>
    <Item type="CFirstPersonDriveByLookAroundData">
      <Name>STD_DRIFTJESTER3_FRONT_LEFT</Name>
      <AllowLookback value="true" />
      <HeadingLimits x="-185.000000" y="150.000000" />
      <DataLeft>
        <Offsets>
          <Item>
            <Offset value="-0.250000" />
            <AngleToBlendInOffset x="40.000000" y="150.000000" />
          </Item>
          <Item>
            <Offset value="0.040000" />
            <AngleToBlendInOffset x="0.000000" y="100.000000" />
          </Item>
          <Item>
            <Offset value="-0.010000" />
            <AngleToBlendInOffset x="0.000000" y="100.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-10.000000" y="2.500000" />
        <AngleToBlendInExtraPitch x="40.000000" y="150.000000" />
      </DataLeft>
      <DataRight>
        <Offsets>
          <Item>
            <Offset value="0.050000" />
            <AngleToBlendInOffset x="0.000000" y="80.000000" />
          </Item>
          <Item>
            <Offset value="0.010000" />
            <AngleToBlendInOffset x="0.000000" y="185.000000" />
          </Item>
          <Item>
            <Offset value="0.040000" />
            <AngleToBlendInOffset x="50.000000" y="80.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="1.000000" y="-6.000000" />
        <AngleToBlendInExtraPitch x="0.000000" y="160.000000" />
      </DataRight>
      <WheelClipInfo>
        <HeadingLimitsLeft x="0.000000" y="0.300000" />
        <HeadingLimitsRight x="0.480000" y="0.530000" />
        <PitchLimits x="0.470000" y="0.530000" />
        <PitchOffset x="0.000000" y="0.065000" />
        <WheelAngleLimits x="-0.700000" y="0.000000" />
        <WheelAngleOffset x="0.000000" y="0.020000" />
        <MaxWheelOffsetY value="0.170000" />
        <WheelClipLerpInRate value="0.150000" />
        <WheelClipLerpOutRate value="0.180000" />
      </WheelClipInfo>
    </Item>
    <Item type="CFirstPersonDriveByLookAroundData">
      <Name>STD_DRIFTJESTER3_FRONT_RIGHT</Name>
      <AllowLookback value="true" />
      <HeadingLimits x="-182.000000" y="150.000000" />
      <DataLeft>
        <Offsets>
          <Item>
            <Offset value="-0.050000" />
            <AngleToBlendInOffset x="0.000000" y="80.000000" />
          </Item>
          <Item>
            <Offset value="0.010000" />
            <AngleToBlendInOffset x="0.000000" y="185.000000" />
          </Item>
          <Item>
            <Offset value="0.040000" />
            <AngleToBlendInOffset x="50.000000" y="80.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="1.000000" y="-6.000000" />
        <AngleToBlendInExtraPitch x="0.000000" y="160.000000" />
      </DataLeft>
      <DataRight>
        <Offsets>
          <Item>
            <Offset value="0.250000" />
            <AngleToBlendInOffset x="40.000000" y="150.000000" />
          </Item>
          <Item>
            <Offset value="0.040000" />
            <AngleToBlendInOffset x="0.000000" y="100.000000" />
          </Item>
          <Item>
            <Offset value="-0.010000" />
            <AngleToBlendInOffset x="0.000000" y="100.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-10.000000" y="2.500000" />
        <AngleToBlendInExtraPitch x="40.000000" y="150.000000" />
      </DataRight>
      <WheelClipInfo>
        <HeadingLimitsLeft x="0.000000" y="0.300000" />
        <HeadingLimitsRight x="0.480000" y="0.530000" />
        <PitchLimits x="0.470000" y="0.530000" />
        <PitchOffset x="0.000000" y="0.065000" />
        <WheelAngleLimits x="-0.700000" y="0.000000" />
        <WheelAngleOffset x="0.000000" y="0.020000" />
        <MaxWheelOffsetY value="0.170000" />
        <WheelClipLerpInRate value="0.150000" />
        <WheelClipLerpOutRate value="0.180000" />
      </WheelClipInfo>
    </Item>
    <Item type="CFirstPersonDriveByLookAroundData">
      <Name>LOW_JESTER5_FRONT_LEFT</Name>
      <AllowLookback value="true" />
      <HeadingLimits x="-180.000000" y="160.000000" />
      <DataLeft>
        <Offsets>
          <Item>
            <Offset value="-0.300000" />
            <AngleToBlendInOffset x="40.000000" y="150.000000" />
          </Item>
          <Item>
            <Offset value="0.100000" />
            <AngleToBlendInOffset x="40.000000" y="100.000000" />
          </Item>
          <Item>
            <Offset value="0.015300" />
            <AngleToBlendInOffset x="30.000000" y="90.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-0.000000" y="-2.000000" />
        <AngleToBlendInExtraPitch x="10.000000" y="40.000000" />
      </DataLeft>
      <DataRight>
        <Offsets>
          <Item>
            <Offset value="-0.010000" />
            <AngleToBlendInOffset x="0.000000" y="80.000000" />
          </Item>
          <Item>
            <Offset value="-0.065000" />
            <AngleToBlendInOffset x="0.000000" y="140.000000" />
          </Item>
          <Item>
            <Offset value="0.025000" />
            <AngleToBlendInOffset x="50.000000" y="80.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-6.500000" y="-7.600000" />
        <AngleToBlendInExtraPitch x="0.000000" y="160.000000" />
      </DataRight>
      <WheelClipInfo>
        <HeadingLimitsLeft x="0.000000" y="0.300000" />
        <HeadingLimitsRight x="0.480000" y="0.530000" />
        <PitchLimits x="0.470000" y="0.530000" />
        <PitchOffset x="0.000000" y="0.065000" />
        <WheelAngleLimits x="-0.700000" y="0.000000" />
        <WheelAngleOffset x="0.000000" y="0.030000" />
        <MaxWheelOffsetY value="0.170000" />
        <WheelClipLerpInRate value="0.150000" />
        <WheelClipLerpOutRate value="0.180000" />
      </WheelClipInfo>
    </Item>
    <Item type="CFirstPersonDriveByLookAroundData">
      <Name>LOW_JESTER5_FRONT_RIGHT</Name>
      <AllowLookback value="true" />
      <HeadingLimits x="-180.000000" y="160.000000" />
      <DataLeft>
        <Offsets>
          <Item>
            <Offset value="-0.075000" />
            <AngleToBlendInOffset x="0.000000" y="80.000000" />
          </Item>
          <Item>
            <Offset value="-0.065000" />
            <AngleToBlendInOffset x="0.000000" y="140.000000" />
          </Item>
          <Item>
            <Offset value="0.025000" />
            <AngleToBlendInOffset x="50.000000" y="80.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-1.500000" y="-6.700000" />
        <AngleToBlendInExtraPitch x="0.000000" y="160.000000" />
      </DataLeft>
      <DataRight>
        <Offsets>
          <Item>
            <Offset value="0.300000" />
            <AngleToBlendInOffset x="40.000000" y="150.000000" />
          </Item>
          <Item>
            <Offset value="0.100000" />
            <AngleToBlendInOffset x="40.000000" y="160.000000" />
          </Item>
          <Item>
            <Offset value="0.015000" />
            <AngleToBlendInOffset x="30.000000" y="50.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-2.000000" y="-2.000000" />
        <AngleToBlendInExtraPitch x="10.000000" y="40.000000" />
      </DataRight>
      <WheelClipInfo>
        <HeadingLimitsLeft x="0.000000" y="0.300000" />
        <HeadingLimitsRight x="0.480000" y="0.530000" />
        <PitchLimits x="0.470000" y="0.530000" />
        <PitchOffset x="0.000000" y="0.065000" />
        <WheelAngleLimits x="-0.700000" y="0.000000" />
        <WheelAngleOffset x="0.000000" y="0.020000" />
        <MaxWheelOffsetY value="0.170000" />
        <WheelClipLerpInRate value="0.150000" />
        <WheelClipLerpOutRate value="0.180000" />
      </WheelClipInfo>
    </Item>
    <Item type="CFirstPersonDriveByLookAroundData">
      <Name>STD_URANUS_FRONT_LEFT</Name>
      <AllowLookback value="true" />
      <HeadingLimits x="-180.000000" y="160.000000" />
      <DataLeft>
        <Offsets>
          <Item>
            <Offset value="-0.325000" />
            <AngleToBlendInOffset x="40.000000" y="100.000000" />
          </Item>
          <Item>
            <Offset value="0.065000" />
            <AngleToBlendInOffset x="40.000000" y="100.000000" />
          </Item>
          <Item>
            <Offset value="0.040000" />
            <AngleToBlendInOffset x="0.000000" y="100.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-15.000000" y="20.000000" />
        <AngleToBlendInExtraPitch x="40.000000" y="110.000000" />
      </DataLeft>
      <DataRight>
        <Offsets>
          <Item>
            <Offset value="0.050200" />
            <AngleToBlendInOffset x="60.000000" y="90.000000" />
          </Item>
          <Item>
            <Offset value="-0.000000" />
            <AngleToBlendInOffset x="0.000000" y="0.000000" />
          </Item>
          <Item>
            <Offset value="0.080000" />
            <AngleToBlendInOffset x="50.000000" y="90.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-5.000000" y="-12.500000" />
        <AngleToBlendInExtraPitch x="0.000000" y="90.000000" />
      </DataRight>
      <WheelClipInfo>
        <HeadingLimitsLeft x="0.380000" y="0.430000" />
        <HeadingLimitsRight x="0.480000" y="0.530000" />
        <PitchLimits x="0.470000" y="0.530000" />
        <PitchOffset x="0.000000" y="0.050000" />
        <WheelAngleLimits x="-0.700000" y="0.000000" />
        <WheelAngleOffset x="0.010000" y="0.040000" />
        <MaxWheelOffsetY value="0.170000" />
        <WheelClipLerpInRate value="0.150000" />
        <WheelClipLerpOutRate value="0.180000" />
      </WheelClipInfo>
    </Item>
    <Item type="CFirstPersonDriveByLookAroundData">
      <Name>STD_URANUS_FRONT_RIGHT</Name>
      <AllowLookback value="true" />
      <HeadingLimits x="-180.100006" y="160.000000" />
      <DataLeft>
        <Offsets>
          <Item>
            <Offset value="-0.060000" />
            <AngleToBlendInOffset x="60.000000" y="90.000000" />
          </Item>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="0.000000" y="0.000000" />
          </Item>
          <Item>
            <Offset value="0.090000" />
            <AngleToBlendInOffset x="40.000000" y="90.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-5.000000" y="-12.500000" />
        <AngleToBlendInExtraPitch x="0.000000" y="90.000000" />
      </DataLeft>
      <DataRight>
        <Offsets>
          <Item>
            <Offset value="0.325000" />
            <AngleToBlendInOffset x="40.000000" y="100.000000" />
          </Item>
          <Item>
            <Offset value="0.065000" />
            <AngleToBlendInOffset x="40.000000" y="100.000000" />
          </Item>
          <Item>
            <Offset value="0.040000" />
            <AngleToBlendInOffset x="0.000000" y="100.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-15.000000" y="20.000000" />
        <AngleToBlendInExtraPitch x="40.000000" y="110.000000" />
      </DataRight>
      <WheelClipInfo>
        <HeadingLimitsLeft x="0.380000" y="0.430000" />
        <HeadingLimitsRight x="0.480000" y="0.530000" />
        <PitchLimits x="0.470000" y="0.530000" />
        <PitchOffset x="0.000000" y="0.050000" />
        <WheelAngleLimits x="-0.700000" y="0.000000" />
        <WheelAngleOffset x="0.010000" y="0.040000" />
        <MaxWheelOffsetY value="0.170000" />
        <WheelClipLerpInRate value="0.150000" />
        <WheelClipLerpOutRate value="0.180000" />
      </WheelClipInfo>
    </Item>
    <Item type="CFirstPersonDriveByLookAroundData">
      <Name>LOW_POLCOQUETTE_FRONT_LEFT</Name>
      <AllowLookback value="false" />
      <HeadingLimits x="-106.599998" y="162.000000" />
      <DataLeft>
        <Offsets>
          <Item>
            <Offset value="-0.280000" />
            <AngleToBlendInOffset x="40.000000" y="162.000000" />
          </Item>
          <Item>
            <Offset value="0.120000" />
            <AngleToBlendInOffset x="40.000000" y="162.000000" />
          </Item>
          <Item>
            <Offset value="0.020000" />
            <AngleToBlendInOffset x="40.000000" y="130.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-10.000000" y="10.000000" />
        <AngleToBlendInExtraPitch x="40.000000" y="162.000000" />
      </DataLeft>
      <DataRight>
        <Offsets>
          <Item>
            <Offset value="0.050000" />
            <AngleToBlendInOffset x="5.000000" y="102.000000" />
          </Item>
          <Item>
            <Offset value="0.160000" />
            <AngleToBlendInOffset x="40.000000" y="102.000000" />
          </Item>
          <Item>
            <Offset value="0.020000" />
            <AngleToBlendInOffset x="40.000000" y="102.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-3.000000" y="-9.500000" />
        <AngleToBlendInExtraPitch x="0.000000" y="102.000000" />
      </DataRight>
      <WheelClipInfo>
        <HeadingLimitsLeft x="0.300000" y="0.430000" />
        <HeadingLimitsRight x="0.480000" y="0.530000" />
        <PitchLimits x="0.470000" y="0.530000" />
        <PitchOffset x="0.000000" y="0.050000" />
        <WheelAngleLimits x="-0.700000" y="0.000000" />
        <WheelAngleOffset x="0.010000" y="0.040000" />
        <MaxWheelOffsetY value="0.170000" />
        <WheelClipLerpInRate value="0.150000" />
        <WheelClipLerpOutRate value="0.180000" />
      </WheelClipInfo>
    </Item>
    <Item type="CFirstPersonDriveByLookAroundData">
      <Name>LOW_POLCOQUETTE_FRONT_RIGHT</Name>
      <AllowLookback value="false" />
      <HeadingLimits x="-111.900002" y="162.000000" />
      <DataLeft>
        <Offsets>
          <Item>
            <Offset value="-0.050000" />
            <AngleToBlendInOffset x="5.000000" y="102.000000" />
          </Item>
          <Item>
            <Offset value="0.260000" />
            <AngleToBlendInOffset x="40.000000" y="102.000000" />
          </Item>
          <Item>
            <Offset value="0.020000" />
            <AngleToBlendInOffset x="40.000000" y="102.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-3.000000" y="-9.500000" />
        <AngleToBlendInExtraPitch x="0.000000" y="102.000000" />
      </DataLeft>
      <DataRight>
        <Offsets>
          <Item>
            <Offset value="0.290000" />
            <AngleToBlendInOffset x="40.000000" y="162.000000" />
          </Item>
          <Item>
            <Offset value="0.120000" />
            <AngleToBlendInOffset x="40.000000" y="162.000000" />
          </Item>
          <Item>
            <Offset value="0.020000" />
            <AngleToBlendInOffset x="20.000000" y="100.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-10.000000" y="10.000000" />
        <AngleToBlendInExtraPitch x="40.000000" y="162.000000" />
      </DataRight>
      <WheelClipInfo>
        <HeadingLimitsLeft x="0.300000" y="0.430000" />
        <HeadingLimitsRight x="0.480000" y="0.530000" />
        <PitchLimits x="0.470000" y="0.530000" />
        <PitchOffset x="0.000000" y="0.050000" />
        <WheelAngleLimits x="-0.700000" y="0.000000" />
        <WheelAngleOffset x="0.010000" y="0.040000" />
        <MaxWheelOffsetY value="0.170000" />
        <WheelClipLerpInRate value="0.150000" />
        <WheelClipLerpOutRate value="0.180000" />
      </WheelClipInfo>
    </Item>
    <Item type="CFirstPersonDriveByLookAroundData">
      <Name>RANGER_POLTERMINUS_FRONT_LEFT</Name>
      <AllowLookback value="true" />
      <HeadingLimits x="-112.000000" y="160.000000" />
      <DataLeft>
        <Offsets>
          <Item>
            <Offset value="-0.375000" />
            <AngleToBlendInOffset x="40.000000" y="160.000000" />
          </Item>
          <Item>
            <Offset value="0.220000" />
            <AngleToBlendInOffset x="40.000000" y="160.000000" />
          </Item>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="0.000000" y="50.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-15.000000" y="-3.000000" />
        <AngleToBlendInExtraPitch x="0.000000" y="60.000000" />
      </DataLeft>
      <DataRight>
        <Offsets>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="0.000000" y="100.000000" />
          </Item>
          <Item>
            <Offset value="0.350000" />
            <AngleToBlendInOffset x="0.000000" y="110.000000" />
          </Item>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="30.000000" y="90.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-12.000000" y="-12.000000" />
        <AngleToBlendInExtraPitch x="30.000000" y="90.000000" />
      </DataRight>
      <WheelClipInfo>
        <HeadingLimitsLeft x="0.380000" y="0.430000" />
        <HeadingLimitsRight x="0.480000" y="0.530000" />
        <PitchLimits x="0.470000" y="0.530000" />
        <PitchOffset x="0.000000" y="0.020000" />
        <WheelAngleLimits x="-0.700000" y="0.000000" />
        <WheelAngleOffset x="0.010000" y="0.020000" />
        <MaxWheelOffsetY value="0.170000" />
        <WheelClipLerpInRate value="0.150000" />
        <WheelClipLerpOutRate value="0.180000" />
      </WheelClipInfo>
    </Item>
    <Item type="CFirstPersonDriveByLookAroundData">
      <Name>RANGER_POLTERMINUS_FRONT_RIGHT</Name>
      <AllowLookback value="true" />
      <HeadingLimits x="-112.000000" y="160.000000" />
      <DataLeft>
        <Offsets>
          <Item>
            <Offset value="-0.000000" />
            <AngleToBlendInOffset x="0.000000" y="100.000000" />
          </Item>
          <Item>
            <Offset value="0.350000" />
            <AngleToBlendInOffset x="0.000000" y="110.000000" />
          </Item>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="30.000000" y="90.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-12.000000" y="-12.000000" />
        <AngleToBlendInExtraPitch x="30.000000" y="90.000000" />
      </DataLeft>
      <DataRight>
        <Offsets>
          <Item>
            <Offset value="0.375000" />
            <AngleToBlendInOffset x="40.000000" y="160.000000" />
          </Item>
          <Item>
            <Offset value="0.220000" />
            <AngleToBlendInOffset x="40.000000" y="160.000000" />
          </Item>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="0.000000" y="50.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-15.000000" y="-3.000000" />
        <AngleToBlendInExtraPitch x="0.000000" y="60.000000" />
      </DataRight>
      <WheelClipInfo>
        <HeadingLimitsLeft x="0.380000" y="0.430000" />
        <HeadingLimitsRight x="0.480000" y="0.530000" />
        <PitchLimits x="0.470000" y="0.530000" />
        <PitchOffset x="0.000000" y="0.020000" />
        <WheelAngleLimits x="-0.700000" y="0.000000" />
        <WheelAngleOffset x="0.010000" y="0.020000" />
        <MaxWheelOffsetY value="0.170000" />
        <WheelClipLerpInRate value="0.150000" />
        <WheelClipLerpOutRate value="0.180000" />
      </WheelClipInfo>
    </Item>
    <Item type="CFirstPersonDriveByLookAroundData">
      <Name>RANGER_POLTERMINUS_REAR_LEFT</Name>
      <AllowLookback value="true" />
      <HeadingLimits x="-187.000000" y="160.000000" />
      <DataLeft>
        <Offsets>
          <Item>
            <Offset value="-0.350000" />
            <AngleToBlendInOffset x="0.000000" y="160.000000" />
          </Item>
          <Item>
            <Offset value="0.200000" />
            <AngleToBlendInOffset x="30.000000" y="160.000000" />
          </Item>
          <Item>
            <Offset value="-0.050000" />
            <AngleToBlendInOffset x="0.000000" y="160.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-12.000000" y="5.000000" />
        <AngleToBlendInExtraPitch x="30.000000" y="60.000000" />
      </DataLeft>
      <DataRight>
        <Offsets>
          <Item>
            <Offset value="-0.300000" />
            <AngleToBlendInOffset x="100.000000" y="180.000000" />
          </Item>
          <Item>
            <Offset value="-0.080000" />
            <AngleToBlendInOffset x="0.000000" y="180.000000" />
          </Item>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="45.000000" y="180.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-3.000000" y="-11.500000" />
        <AngleToBlendInExtraPitch x="0.000000" y="70.000000" />
      </DataRight>
      <WheelClipInfo>
        <HeadingLimitsLeft x="0.380000" y="0.430000" />
        <HeadingLimitsRight x="0.480000" y="0.530000" />
        <PitchLimits x="0.470000" y="0.530000" />
        <PitchOffset x="0.000000" y="0.020000" />
        <WheelAngleLimits x="-0.700000" y="0.000000" />
        <WheelAngleOffset x="0.010000" y="0.020000" />
        <MaxWheelOffsetY value="0.170000" />
        <WheelClipLerpInRate value="0.150000" />
        <WheelClipLerpOutRate value="0.180000" />
      </WheelClipInfo>
    </Item>
    <Item type="CFirstPersonDriveByLookAroundData">
      <Name>RANGER_POLTERMINUS_REAR_RIGHT</Name>
      <AllowLookback value="true" />
      <HeadingLimits x="-187.000000" y="160.000000" />
      <DataLeft>
        <Offsets>
          <Item>
            <Offset value="0.300000" />
            <AngleToBlendInOffset x="100.000000" y="180.000000" />
          </Item>
          <Item>
            <Offset value="-0.080000" />
            <AngleToBlendInOffset x="0.000000" y="180.000000" />
          </Item>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="45.000000" y="180.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-5.000000" y="-11.500000" />
        <AngleToBlendInExtraPitch x="0.000000" y="70.000000" />
      </DataLeft>
      <DataRight>
        <Offsets>
          <Item>
            <Offset value="0.350000" />
            <AngleToBlendInOffset x="0.000000" y="160.000000" />
          </Item>
          <Item>
            <Offset value="0.200000" />
            <AngleToBlendInOffset x="30.000000" y="160.000000" />
          </Item>
          <Item>
            <Offset value="-0.070000" />
            <AngleToBlendInOffset x="0.000000" y="160.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-12.000000" y="5.000000" />
        <AngleToBlendInExtraPitch x="30.000000" y="60.000000" />
      </DataRight>
      <WheelClipInfo>
        <HeadingLimitsLeft x="0.380000" y="0.430000" />
        <HeadingLimitsRight x="0.480000" y="0.530000" />
        <PitchLimits x="0.470000" y="0.530000" />
        <PitchOffset x="0.000000" y="0.020000" />
        <WheelAngleLimits x="-0.700000" y="0.000000" />
        <WheelAngleOffset x="0.010000" y="0.020000" />
        <MaxWheelOffsetY value="0.170000" />
        <WheelClipLerpInRate value="0.150000" />
        <WheelClipLerpOutRate value="0.180000" />
      </WheelClipInfo>
    </Item>
    <Item type="CFirstPersonDriveByLookAroundData">
      <Name>LOW_BANSHEE3_FRONT_LEFT</Name>
      <AllowLookback value="true" />
      <HeadingLimits x="-170.000000" y="150.000000" />
      <DataLeft>
        <Offsets>
          <Item>
            <Offset value="-0.250000" />
            <AngleToBlendInOffset x="0.000000" y="150.000000" />
          </Item>
          <Item>
            <Offset value="0.100000" />
            <AngleToBlendInOffset x="0.000000" y="150.000000" />
          </Item>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="0.000000" y="0.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="0.000000" y="5.000000" />
        <AngleToBlendInExtraPitch x="0.000000" y="150.000000" />
      </DataLeft>
      <DataRight>
        <Offsets>
          <Item>
            <Offset value="-0.020000" />
            <AngleToBlendInOffset x="0.000000" y="150.000000" />
          </Item>
          <Item>
            <Offset value="0.170000" />
            <AngleToBlendInOffset x="0.000000" y="150.000000" />
          </Item>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="0.000000" y="0.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="2.500000" y="-4.000000" />
        <AngleToBlendInExtraPitch x="0.000000" y="150.000000" />
      </DataRight>
      <WheelClipInfo>
        <HeadingLimitsLeft x="0.380000" y="0.430000" />
        <HeadingLimitsRight x="0.480000" y="0.530000" />
        <PitchLimits x="0.470000" y="0.550000" />
        <PitchOffset x="0.000000" y="0.050000" />
        <WheelAngleLimits x="-0.700000" y="0.000000" />
        <WheelAngleOffset x="0.010000" y="0.040000" />
        <MaxWheelOffsetY value="0.170000" />
        <WheelClipLerpInRate value="0.150000" />
        <WheelClipLerpOutRate value="0.180000" />
      </WheelClipInfo>
    </Item>
    <Item type="CFirstPersonDriveByLookAroundData">
      <Name>LOW_BANSHEE3_FRONT_RIGHT</Name>
      <AllowLookback value="false" />
      <HeadingLimits x="-180.000000" y="150.000000" />
      <DataLeft>
        <Offsets>
          <Item>
            <Offset value="0.200000" />
            <AngleToBlendInOffset x="0.000000" y="170.000000" />
          </Item>
          <Item>
            <Offset value="0.100000" />
            <AngleToBlendInOffset x="0.000000" y="170.000000" />
          </Item>
          <Item>
            <Offset value="0.030000" />
            <AngleToBlendInOffset x="0.000000" y="170.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="2.000000" y="-6.000000" />
        <AngleToBlendInExtraPitch x="0.000000" y="150.000000" />
      </DataLeft>
      <DataRight>
        <Offsets>
          <Item>
            <Offset value="0.250000" />
            <AngleToBlendInOffset x="50.000000" y="150.000000" />
          </Item>
          <Item>
            <Offset value="0.100000" />
            <AngleToBlendInOffset x="50.000000" y="150.000000" />
          </Item>
          <Item>
            <Offset value="-0.000000" />
            <AngleToBlendInOffset x="0.000000" y="0.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="0.000000" y="5.000000" />
        <AngleToBlendInExtraPitch x="0.000000" y="150.000000" />
      </DataRight>
      <WheelClipInfo>
        <HeadingLimitsLeft x="0.380000" y="0.430000" />
        <HeadingLimitsRight x="0.480000" y="0.530000" />
        <PitchLimits x="0.470000" y="0.550000" />
        <PitchOffset x="0.000000" y="0.050000" />
        <WheelAngleLimits x="-0.700000" y="0.000000" />
        <WheelAngleOffset x="0.010000" y="0.040000" />
        <MaxWheelOffsetY value="0.170000" />
        <WheelClipLerpInRate value="0.150000" />
        <WheelClipLerpOutRate value="0.180000" />
      </WheelClipInfo>
    </Item>
    <Item type="CFirstPersonDriveByLookAroundData">
      <Name>LOW_COQUETTE6_FRONT_LEFT</Name>
      <AllowLookback value="true" />
      <HeadingLimits x="-170.000000" y="150.000000" />
      <DataLeft>
        <Offsets>
          <Item>
            <Offset value="-0.250000" />
            <AngleToBlendInOffset x="0.000000" y="150.000000" />
          </Item>
          <Item>
            <Offset value="0.100000" />
            <AngleToBlendInOffset x="0.000000" y="150.000000" />
          </Item>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="0.000000" y="0.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-3.000000" y="5.000000" />
        <AngleToBlendInExtraPitch x="0.000000" y="150.000000" />
      </DataLeft>
      <DataRight>
        <Offsets>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="0.000000" y="0.000000" />
          </Item>
          <Item>
            <Offset value="-0.070000" />
            <AngleToBlendInOffset x="0.000000" y="150.000000" />
          </Item>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="0.000000" y="0.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="2.000000" y="14.000000" />
        <AngleToBlendInExtraPitch x="0.000000" y="150.000000" />
      </DataRight>
      <WheelClipInfo>
        <HeadingLimitsLeft x="0.380000" y="0.430000" />
        <HeadingLimitsRight x="0.480000" y="0.530000" />
        <PitchLimits x="0.470000" y="0.550000" />
        <PitchOffset x="0.000000" y="0.050000" />
        <WheelAngleLimits x="-0.700000" y="0.000000" />
        <WheelAngleOffset x="0.010000" y="0.040000" />
        <MaxWheelOffsetY value="0.170000" />
        <WheelClipLerpInRate value="0.150000" />
        <WheelClipLerpOutRate value="0.180000" />
      </WheelClipInfo>
    </Item>
    <Item type="CFirstPersonDriveByLookAroundData">
      <Name>LOW_COQUETTE6_FRONT_RIGHT</Name>
      <AllowLookback value="false" />
      <HeadingLimits x="-170.000000" y="150.000000" />
      <DataLeft>
        <Offsets>
          <Item>
            <Offset value="0.020000" />
            <AngleToBlendInOffset x="0.000000" y="150.000000" />
          </Item>
          <Item>
            <Offset value="-0.050000" />
            <AngleToBlendInOffset x="0.000000" y="150.000000" />
          </Item>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="0.000000" y="0.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="2.000000" y="11.000000" />
        <AngleToBlendInExtraPitch x="0.000000" y="150.000000" />
      </DataLeft>
      <DataRight>
        <Offsets>
          <Item>
            <Offset value="0.250000" />
            <AngleToBlendInOffset x="0.000000" y="150.000000" />
          </Item>
          <Item>
            <Offset value="0.100000" />
            <AngleToBlendInOffset x="0.000000" y="150.000000" />
          </Item>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="0.000000" y="0.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-3.000000" y="5.000000" />
        <AngleToBlendInExtraPitch x="0.000000" y="150.000000" />
      </DataRight>
      <WheelClipInfo>
        <HeadingLimitsLeft x="0.380000" y="0.430000" />
        <HeadingLimitsRight x="0.480000" y="0.530000" />
        <PitchLimits x="0.470000" y="0.550000" />
        <PitchOffset x="0.000000" y="0.050000" />
        <WheelAngleLimits x="-0.700000" y="0.000000" />
        <WheelAngleOffset x="0.010000" y="0.040000" />
        <MaxWheelOffsetY value="0.170000" />
        <WheelClipLerpInRate value="0.150000" />
        <WheelClipLerpOutRate value="0.180000" />
      </WheelClipInfo>
    </Item>
    <Item type="CFirstPersonDriveByLookAroundData">
      <Name>POLCARACARA_FRONT_LEFT</Name>
      <AllowLookback value="true" />
      <HeadingLimits x="-100.000000" y="160.000000" />
      <DataLeft>
        <Offsets>
          <Item>
            <Offset value="-0.250000" />
            <AngleToBlendInOffset x="60.000000" y="160.000000" />
          </Item>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="0.000000" y="160.000000" />
          </Item>
          <Item>
            <Offset value="0.020000" />
            <AngleToBlendInOffset x="60.000000" y="160.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-20.000000" y="20.000000" />
        <AngleToBlendInExtraPitch x="40.000000" y="120.000000" />
      </DataLeft>
      <DataRight>
        <Offsets>
          <Item>
            <Offset value="-0.000000" />
            <AngleToBlendInOffset x="0.000000" y="0.000000" />
          </Item>
          <Item>
            <Offset value="0.020000" />
            <AngleToBlendInOffset x="0.000000" y="90.000000" />
          </Item>
          <Item>
            <Offset value="0.150000" />
            <AngleToBlendInOffset x="80.000000" y="150.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-2.000000" y="-13.000000" />
        <AngleToBlendInExtraPitch x="0.000000" y="150.000000" />
      </DataRight>
      <WheelClipInfo>
        <HeadingLimitsLeft x="0.350000" y="0.430000" />
        <HeadingLimitsRight x="0.480000" y="0.530000" />
        <PitchLimits x="0.470000" y="0.530000" />
        <PitchOffset x="0.000000" y="0.050000" />
        <WheelAngleLimits x="-0.700000" y="0.000000" />
        <WheelAngleOffset x="0.010000" y="0.040000" />
        <MaxWheelOffsetY value="0.170000" />
        <WheelClipLerpInRate value="0.150000" />
        <WheelClipLerpOutRate value="0.180000" />
      </WheelClipInfo>
    </Item>
    <Item type="CFirstPersonDriveByLookAroundData">
      <Name>POLCARACARA_FRONT_RIGHT</Name>
      <AllowLookback value="true" />
      <HeadingLimits x="-105.000000" y="160.000000" />
      <DataLeft>
        <Offsets>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="0.000000" y="0.000000" />
          </Item>
          <Item>
            <Offset value="0.020000" />
            <AngleToBlendInOffset x="0.000000" y="90.000000" />
          </Item>
          <Item>
            <Offset value="0.150000" />
            <AngleToBlendInOffset x="80.000000" y="150.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-2.000000" y="-13.000000" />
        <AngleToBlendInExtraPitch x="0.000000" y="150.000000" />
      </DataLeft>
      <DataRight>
        <Offsets>
          <Item>
            <Offset value="0.250000" />
            <AngleToBlendInOffset x="60.000000" y="160.000000" />
          </Item>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="0.000000" y="160.000000" />
          </Item>
          <Item>
            <Offset value="0.020000" />
            <AngleToBlendInOffset x="60.000000" y="160.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-20.000000" y="20.000000" />
        <AngleToBlendInExtraPitch x="40.000000" y="120.000000" />
      </DataRight>
      <WheelClipInfo>
        <HeadingLimitsLeft x="0.350000" y="0.430000" />
        <HeadingLimitsRight x="0.480000" y="0.530000" />
        <PitchLimits x="0.470000" y="0.530000" />
        <PitchOffset x="0.000000" y="0.050000" />
        <WheelAngleLimits x="-0.700000" y="0.000000" />
        <WheelAngleOffset x="0.010000" y="0.040000" />
        <MaxWheelOffsetY value="0.170000" />
        <WheelClipLerpInRate value="0.150000" />
        <WheelClipLerpOutRate value="0.180000" />
      </WheelClipInfo>
    </Item>
    <Item type="CFirstPersonDriveByLookAroundData">
      <Name>POLFACTION2_FRONT_LEFT</Name>
      <AllowLookback value="true" />
      <HeadingLimits x="-90.000000" y="160.000000" />
      <DataLeft>
        <Offsets>
          <Item>
            <Offset value="-0.300000" />
            <AngleToBlendInOffset x="20.000000" y="100.000000" />
          </Item>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="0.000000" y="0.000000" />
          </Item>
          <Item>
            <Offset value="0.025000" />
            <AngleToBlendInOffset x="0.000000" y="90.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-10.000000" y="5.000000" />
        <AngleToBlendInExtraPitch x="60.000000" y="100.000000" />
      </DataLeft>
      <DataRight>
        <Offsets>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="0.000000" y="0.000000" />
          </Item>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="0.000000" y="0.000000" />
          </Item>
          <Item>
            <Offset value="0.050000" />
            <AngleToBlendInOffset x="90.000000" y="180.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-12.400000" y="-13.200000" />
        <AngleToBlendInExtraPitch x="0.000000" y="190.000000" />
      </DataRight>
      <WheelClipInfo>
        <HeadingLimitsLeft x="0.380000" y="0.430000" />
        <HeadingLimitsRight x="0.480000" y="0.530000" />
        <PitchLimits x="0.470000" y="0.530000" />
        <PitchOffset x="0.000000" y="0.050000" />
        <WheelAngleLimits x="-0.700000" y="0.000000" />
        <WheelAngleOffset x="0.010000" y="0.040000" />
        <MaxWheelOffsetY value="0.170000" />
        <WheelClipLerpInRate value="0.150000" />
        <WheelClipLerpOutRate value="0.180000" />
      </WheelClipInfo>
    </Item>
    <Item type="CFirstPersonDriveByLookAroundData">
      <Name>POLFACTION2_FRONT_RIGHT</Name>
      <AllowLookback value="true" />
      <HeadingLimits x="-87.000000" y="160.000000" />
      <DataLeft>
        <Offsets>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="0.000000" y="0.000000" />
          </Item>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="0.000000" y="0.000000" />
          </Item>
          <Item>
            <Offset value="0.050000" />
            <AngleToBlendInOffset x="90.000000" y="180.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-12.000000" y="-15.000000" />
        <AngleToBlendInExtraPitch x="0.000000" y="190.000000" />
      </DataLeft>
      <DataRight>
        <Offsets>
          <Item>
            <Offset value="0.300000" />
            <AngleToBlendInOffset x="20.000000" y="100.000000" />
          </Item>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="0.000000" y="0.000000" />
          </Item>
          <Item>
            <Offset value="0.025000" />
            <AngleToBlendInOffset x="0.000000" y="90.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-10.000000" y="5.000000" />
        <AngleToBlendInExtraPitch x="60.000000" y="100.000000" />
      </DataRight>
      <WheelClipInfo>
        <HeadingLimitsLeft x="0.380000" y="0.430000" />
        <HeadingLimitsRight x="0.480000" y="0.530000" />
        <PitchLimits x="0.470000" y="0.530000" />
        <PitchOffset x="0.000000" y="0.050000" />
        <WheelAngleLimits x="-0.700000" y="0.000000" />
        <WheelAngleOffset x="0.010000" y="0.040000" />
        <MaxWheelOffsetY value="0.170000" />
        <WheelClipLerpInRate value="0.150000" />
        <WheelClipLerpOutRate value="0.180000" />
      </WheelClipInfo>
    </Item>
    <Item type="CFirstPersonDriveByLookAroundData">
      <Name>STD_CHAVOSV6_FRONT_LEFT</Name>
      <AllowLookback value="true" />
      <HeadingLimits x="-190.000000" y="160.000000" />
      <DataLeft>
        <Offsets>
          <Item>
            <Offset value="-0.300000" />
            <AngleToBlendInOffset x="50.000000" y="120.000000" />
          </Item>
          <Item>
            <Offset value="0.050000" />
            <AngleToBlendInOffset x="20.000000" y="160.000000" />
          </Item>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="0.000000" y="0.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-15.000000" y="20.000000" />
        <AngleToBlendInExtraPitch x="50.000000" y="120.000000" />
      </DataLeft>
      <DataRight>
        <Offsets>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="45.000000" y="90.000000" />
          </Item>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="0.000000" y="0.000000" />
          </Item>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="0.000000" y="0.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-2.000000" y="-12.500000" />
        <AngleToBlendInExtraPitch x="0.000000" y="100.000000" />
      </DataRight>
      <WheelClipInfo>
        <HeadingLimitsLeft x="0.380000" y="0.430000" />
        <HeadingLimitsRight x="0.480000" y="0.530000" />
        <PitchLimits x="0.470000" y="0.530000" />
        <PitchOffset x="0.000000" y="0.050000" />
        <WheelAngleLimits x="-0.700000" y="0.000000" />
        <WheelAngleOffset x="0.010000" y="0.040000" />
        <MaxWheelOffsetY value="0.170000" />
        <WheelClipLerpInRate value="0.150000" />
        <WheelClipLerpOutRate value="0.180000" />
      </WheelClipInfo>
    </Item>
    <Item type="CFirstPersonDriveByLookAroundData">
      <Name>STD_CHAVOSV6_FRONT_RIGHT</Name>
      <AllowLookback value="true" />
      <HeadingLimits x="-190.000000" y="160.000000" />
      <DataLeft>
        <Offsets>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="45.000000" y="90.000000" />
          </Item>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="0.000000" y="0.000000" />
          </Item>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="0.000000" y="0.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-1.000000" y="-11.000000" />
        <AngleToBlendInExtraPitch x="0.000000" y="100.000000" />
      </DataLeft>
      <DataRight>
        <Offsets>
          <Item>
            <Offset value="0.300000" />
            <AngleToBlendInOffset x="50.000000" y="120.000000" />
          </Item>
          <Item>
            <Offset value="0.050000" />
            <AngleToBlendInOffset x="20.000000" y="160.000000" />
          </Item>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="0.000000" y="0.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-15.000000" y="20.000000" />
        <AngleToBlendInExtraPitch x="50.000000" y="120.000000" />
      </DataRight>
      <WheelClipInfo>
        <HeadingLimitsLeft x="0.380000" y="0.430000" />
        <HeadingLimitsRight x="0.480000" y="0.530000" />
        <PitchLimits x="0.470000" y="0.530000" />
        <PitchOffset x="0.000000" y="0.050000" />
        <WheelAngleLimits x="-0.700000" y="0.000000" />
        <WheelAngleOffset x="0.010000" y="0.040000" />
        <MaxWheelOffsetY value="0.170000" />
        <WheelClipLerpInRate value="0.150000" />
        <WheelClipLerpOutRate value="0.180000" />
      </WheelClipInfo>
    </Item>
    <Item type="CFirstPersonDriveByLookAroundData">
      <Name>STD_CHAVOSV6_REAR_LEFT</Name>
      <AllowLookback value="true" />
      <HeadingLimits x="-190.000000" y="140.000000" />
      <DataLeft>
        <Offsets>
          <Item>
            <Offset value="-0.185000" />
            <AngleToBlendInOffset x="35.000000" y="140.000000" />
          </Item>
          <Item>
            <Offset value="0.275000" />
            <AngleToBlendInOffset x="20.000000" y="140.000000" />
          </Item>
          <Item>
            <Offset value="-0.025000" />
            <AngleToBlendInOffset x="0.000000" y="120.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="0.000000" y="5.000000" />
        <AngleToBlendInExtraPitch x="20.000000" y="100.000000" />
      </DataLeft>
      <DataRight>
        <Offsets>
          <Item>
            <Offset value="-0.150000" />
            <AngleToBlendInOffset x="0.000000" y="90.000000" />
          </Item>
          <Item>
            <Offset value="0.050000" />
            <AngleToBlendInOffset x="0.000000" y="90.000000" />
          </Item>
          <Item>
            <Offset value="0.035000" />
            <AngleToBlendInOffset x="0.000000" y="100.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-3.000000" y="2.100000" />
        <AngleToBlendInExtraPitch x="0.000000" y="190.000000" />
      </DataRight>
      <WheelClipInfo>
        <HeadingLimitsLeft x="0.380000" y="0.430000" />
        <HeadingLimitsRight x="0.480000" y="0.530000" />
        <PitchLimits x="0.470000" y="0.530000" />
        <PitchOffset x="0.000000" y="0.050000" />
        <WheelAngleLimits x="-0.700000" y="0.000000" />
        <WheelAngleOffset x="0.010000" y="0.040000" />
        <MaxWheelOffsetY value="0.170000" />
        <WheelClipLerpInRate value="0.150000" />
        <WheelClipLerpOutRate value="0.180000" />
      </WheelClipInfo>
    </Item>
    <Item type="CFirstPersonDriveByLookAroundData">
      <Name>STD_CHAVOSV6_REAR_RIGHT</Name>
      <AllowLookback value="true" />
      <HeadingLimits x="-190.000000" y="140.000000" />
      <DataLeft>
        <Offsets>
          <Item>
            <Offset value="0.150000" />
            <AngleToBlendInOffset x="0.000000" y="90.000000" />
          </Item>
          <Item>
            <Offset value="0.050000" />
            <AngleToBlendInOffset x="0.000000" y="90.000000" />
          </Item>
          <Item>
            <Offset value="0.035000" />
            <AngleToBlendInOffset x="0.000000" y="100.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-3.000000" y="3.000000" />
        <AngleToBlendInExtraPitch x="0.000000" y="190.000000" />
      </DataLeft>
      <DataRight>
        <Offsets>
          <Item>
            <Offset value="0.185000" />
            <AngleToBlendInOffset x="35.000000" y="140.000000" />
          </Item>
          <Item>
            <Offset value="0.275000" />
            <AngleToBlendInOffset x="20.000000" y="140.000000" />
          </Item>
          <Item>
            <Offset value="-0.025000" />
            <AngleToBlendInOffset x="0.000000" y="120.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="0.000000" y="5.000000" />
        <AngleToBlendInExtraPitch x="20.000000" y="100.000000" />
      </DataRight>
      <WheelClipInfo>
        <HeadingLimitsLeft x="0.380000" y="0.430000" />
        <HeadingLimitsRight x="0.480000" y="0.530000" />
        <PitchLimits x="0.470000" y="0.530000" />
        <PitchOffset x="0.000000" y="0.050000" />
        <WheelAngleLimits x="-0.700000" y="0.000000" />
        <WheelAngleOffset x="0.010000" y="0.040000" />
        <MaxWheelOffsetY value="0.170000" />
        <WheelClipLerpInRate value="0.150000" />
        <WheelClipLerpOutRate value="0.180000" />
      </WheelClipInfo>
    </Item>
    <Item type="CFirstPersonDriveByLookAroundData">
      <Name>STD_FIREBOLT_FRONT_LEFT</Name>
      <AllowLookback value="true" />
      <HeadingLimits x="-178.000000" y="165.000000" />
      <DataLeft>
        <Offsets>
          <Item>
            <Offset value="-0.350000" />
            <AngleToBlendInOffset x="0.000000" y="165.000000" />
          </Item>
          <Item>
            <Offset value="0.050000" />
            <AngleToBlendInOffset x="0.000000" y="165.000000" />
          </Item>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="0.000000" y="0.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-10.000000" y="15.000000" />
        <AngleToBlendInExtraPitch x="50.000000" y="100.000000" />
      </DataLeft>
      <DataRight>
        <Offsets>
          <Item>
            <Offset value="-0.120000" />
            <AngleToBlendInOffset x="50.000000" y="120.000000" />
          </Item>
          <Item>
            <Offset value="0.200000" />
            <AngleToBlendInOffset x="50.000000" y="120.000000" />
          </Item>
          <Item>
            <Offset value="0.070000" />
            <AngleToBlendInOffset x="30.000000" y="120.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-2.000000" y="3.000000" />
        <AngleToBlendInExtraPitch x="0.000000" y="90.000000" />
      </DataRight>
      <WheelClipInfo>
        <HeadingLimitsLeft x="0.380000" y="0.430000" />
        <HeadingLimitsRight x="0.480000" y="0.530000" />
        <PitchLimits x="0.470000" y="0.500000" />
        <PitchOffset x="0.000000" y="0.010000" />
        <WheelAngleLimits x="-0.700000" y="0.000000" />
        <WheelAngleOffset x="0.010000" y="0.020000" />
        <MaxWheelOffsetY value="0.170000" />
        <WheelClipLerpInRate value="0.150000" />
        <WheelClipLerpOutRate value="0.180000" />
      </WheelClipInfo>
    </Item>
    <Item type="CFirstPersonDriveByLookAroundData">
      <Name>STD_FIREBOLT_FRONT_RIGHT</Name>
      <AllowLookback value="true" />
      <HeadingLimits x="-178.000000" y="165.000000" />
      <DataLeft>
        <Offsets>
          <Item>
            <Offset value="0.120000" />
            <AngleToBlendInOffset x="50.000000" y="120.000000" />
          </Item>
          <Item>
            <Offset value="0.200000" />
            <AngleToBlendInOffset x="50.000000" y="120.000000" />
          </Item>
          <Item>
            <Offset value="0.070000" />
            <AngleToBlendInOffset x="30.000000" y="120.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-2.000000" y="3.000000" />
        <AngleToBlendInExtraPitch x="0.000000" y="90.000000" />
      </DataLeft>
      <DataRight>
        <Offsets>
          <Item>
            <Offset value="0.350000" />
            <AngleToBlendInOffset x="0.000000" y="165.000000" />
          </Item>
          <Item>
            <Offset value="0.040000" />
            <AngleToBlendInOffset x="0.000000" y="45.000000" />
          </Item>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="0.000000" y="0.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-10.000000" y="15.000000" />
        <AngleToBlendInExtraPitch x="10.000000" y="90.000000" />
      </DataRight>
      <WheelClipInfo>
        <HeadingLimitsLeft x="0.380000" y="0.430000" />
        <HeadingLimitsRight x="0.480000" y="0.530000" />
        <PitchLimits x="0.470000" y="0.500000" />
        <PitchOffset x="0.000000" y="0.010000" />
        <WheelAngleLimits x="-0.700000" y="0.000000" />
        <WheelAngleOffset x="0.010000" y="0.020000" />
        <MaxWheelOffsetY value="0.170000" />
        <WheelClipLerpInRate value="0.150000" />
        <WheelClipLerpOutRate value="0.180000" />
      </WheelClipInfo>
    </Item>
    <Item type="CFirstPersonDriveByLookAroundData">
      <Name>HELI_CARGOBOB5_FRONT_RIGHT</Name>
      <AllowLookback value="true" />
      <HeadingLimits x="-88.000000" y="110.000000" />
      <DataLeft>
        <Offsets>
          <Item>
            <Offset value="0.050000" />
            <AngleToBlendInOffset x="0.000000" y="88.000000" />
          </Item>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="0.000000" y="0.000000" />
          </Item>
          <Item>
            <Offset value="0.050000" />
            <AngleToBlendInOffset x="90.000000" y="180.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-15.000000" y="15.000000" />
        <AngleToBlendInExtraPitch x="0.000000" y="0.000000" />
      </DataLeft>
      <DataRight>
        <Offsets>
          <Item>
            <Offset value="-0.100000" />
            <AngleToBlendInOffset x="0.000000" y="100.000000" />
          </Item>
          <Item>
            <Offset value="0.050000" />
            <AngleToBlendInOffset x="0.000000" y="100.000000" />
          </Item>
          <Item>
            <Offset value="0.050000" />
            <AngleToBlendInOffset x="0.000000" y="100.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-15.000000" y="15.000000" />
        <AngleToBlendInExtraPitch x="0.000000" y="0.000000" />
      </DataRight>
      <WheelClipInfo>
        <HeadingLimitsLeft x="0.380000" y="0.430000" />
        <HeadingLimitsRight x="0.480000" y="0.530000" />
        <PitchLimits x="0.470000" y="0.530000" />
        <PitchOffset x="0.000000" y="0.050000" />
        <WheelAngleLimits x="-0.700000" y="0.000000" />
        <WheelAngleOffset x="0.010000" y="0.040000" />
        <MaxWheelOffsetY value="0.170000" />
        <WheelClipLerpInRate value="0.150000" />
        <WheelClipLerpOutRate value="0.180000" />
      </WheelClipInfo>
    </Item>
  </FirstPersonDriveByLookAroundData>
</CVehicleMetadataMgr>