<?xml version="1.0" encoding="UTF-8"?>
<CVehicleModelInfoVariation>
  <variationData>
    <Item>
      <modelName>polalamo2</modelName>
      <colors>
        <Item>
          <indices content="char_array">
            5
            5
            0
            0
            0
            0
          </indices>
          <liveries>
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
          </liveries>
        </Item>
      </colors>
      <kits>
        <Item>8004_polalamo2_modkit</Item>
      </kits>
      <windowsWithExposedEdges />
      <plateProbabilities>
        <Probabilities>
          <Item>
            <Name>Police guv plate</Name>
            <Value value="100" />
          </Item>
        </Probabilities>
      </plateProbabilities>
      <lightSettings value="1" />
      <sirenSettings value="240" />
    </Item>
    <Item>
      <modelName>polstorm</modelName>
      <colors>
        <Item>
          <indices content="char_array">
            5
            5
            0
            0
            0
            0
          </indices>
          <liveries>
            <Item value="true" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
          </liveries>
        </Item>
      </colors>
      <kits>
        <Item>8005_polstorm_modkit</Item>
      </kits>
      <windowsWithExposedEdges />
      <plateProbabilities>
        <Probabilities>
          <Item>
            <Name>Standard White</Name>
            <Value value="100" />
          </Item>
        </Probabilities>
      </plateProbabilities>
      <lightSettings value="1" />
      <sirenSettings value="240" />
    </Item>
    <Item>
      <modelName>polstormxl</modelName>
      <colors>
        <Item>
          <indices content="char_array">
            5
            5
            0
            0
            0
            0
          </indices>
          <liveries>
            <Item value="true" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
          </liveries>
        </Item>
      </colors>
      <kits>
        <Item>8006_polstormxl_modkit</Item>
      </kits>
      <windowsWithExposedEdges />
      <plateProbabilities>
        <Probabilities>
          <Item>
            <Name>Police guv plate</Name>
            <Value value="100" />
          </Item>
        </Probabilities>
      </plateProbabilities>
      <lightSettings value="1" />
      <sirenSettings value="240" />
    </Item>
    <Item>
      <modelName>polbuffalo5</modelName>
      <colors>
        <Item>
          <indices content="char_array">
            5
            5
            0
            0
            0
            0
          </indices>
          <liveries>
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
          </liveries>
        </Item>
      </colors>
      <kits>
        <Item>8007_polbuffalo5_modkit</Item>
      </kits>
      <windowsWithExposedEdges />
      <plateProbabilities>
        <Probabilities>
          <Item>
            <Name>Police guv plate</Name>
            <Value value="100" />
          </Item>
        </Probabilities>
      </plateProbabilities>
      <lightSettings value="1" />
      <sirenSettings value="240" />
    </Item>
    <Item>
      <modelName>polbuffalo6</modelName>
      <colors>
        <Item>
          <indices content="char_array">
            5
            5
            0
            0
            0
            0
          </indices>
          <liveries>
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
          </liveries>
        </Item>
      </colors>
      <kits>
        <Item>8020_polbuffalo6_modkit</Item>
      </kits>
      <windowsWithExposedEdges />
      <plateProbabilities>
        <Probabilities>
          <Item>
            <Name>Police guv plate</Name>
            <Value value="100" />
          </Item>
        </Probabilities>
      </plateProbabilities>
      <lightSettings value="1" />
      <sirenSettings value="240" />
    </Item>
    <Item>
      <modelName>poltorrence2</modelName>
      <colors>
        <Item>
          <indices content="char_array">
            5
            5
            0
            0
            0
            0
          </indices>
        </Item>
      </colors>
      <kits>
        <Item>8002_poltorrence2_modkit</Item>
      </kits>
      <windowsWithExposedEdges />
      <plateProbabilities>
        <Probabilities>
          <Item>
            <Name>Police guv plate</Name>
            <Value value="100" />
          </Item>
        </Probabilities>
      </plateProbabilities>
      <lightSettings value="1" />
      <sirenSettings value="240" />
    </Item>
    <Item>
      <modelName>polverus2</modelName>
      <colors>
        <Item>
          <indices content="char_array">
            5
            5
            0
            0
            0
            0
          </indices>
          <liveries>
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
          </liveries>
        </Item>
      </colors>
      <kits>
        <Item>8009_polverus2_modkit</Item>
      </kits>
      <windowsWithExposedEdges />
      <plateProbabilities>
        <Probabilities>
          <Item>
            <Name>Police guv plate</Name>
            <Value value="100" />
          </Item>
        </Probabilities>
      </plateProbabilities>
      <lightSettings value="1" />
      <sirenSettings value="240" />
    </Item>
    <Item>
      <modelName>polvigero3</modelName>
      <colors>
        <Item>
          <indices content="char_array">
            5
            5
            0
            0
            0
            0
          </indices>
          <liveries>
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
          </liveries>
        </Item>
      </colors>
      <kits>
        <Item>8011_polvigero3_modkit</Item>
      </kits>
      <windowsWithExposedEdges />
      <plateProbabilities>
        <Probabilities>
          <Item>
            <Name>Police guv plate</Name>
            <Value value="100" />
          </Item>
        </Probabilities>
      </plateProbabilities>
      <lightSettings value="119" />
      <sirenSettings value="240" />
    </Item>
    <Item>
      <modelName>polvstr2</modelName>
      <colors>
        <Item>
          <indices content="char_array">
            5
            5
            0
            0
            0
            0
            0
          </indices>
          <liveries>
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
          </liveries>
        </Item>
      </colors>
      <kits>
        <Item>8008_polvstr2_modkit</Item>
      </kits>
      <windowsWithExposedEdges />
      <plateProbabilities>
        <Probabilities>
          <Item>
            <Name>Police guv plate</Name>
            <Value value="100" />
          </Item>
        </Probabilities>
      </plateProbabilities>
      <lightSettings value="151" />
      <sirenSettings value="240" />
    </Item>
    <Item>
      <modelName>polconada</modelName>
      <colors>
        <Item>
          <indices content="char_array">
            5
            5
            5
            0
            93
            134
          </indices>
          <liveries>
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
          </liveries>
        </Item>
      </colors>
      <kits>
        <Item>8012_polconada_modkit</Item>
      </kits>
      <windowsWithExposedEdges />
      <plateProbabilities>
        <Probabilities />
      </plateProbabilities>
      <lightSettings value="18" />
      <sirenSettings value="0" />
    </Item>
    <Item>
      <modelName>polcoquette</modelName>
      <colors>
        <Item>
          <indices content="char_array">
            5
            5
            0
            0
            0
            0
            0
          </indices>
          <liveries />
        </Item>
      </colors>
      <kits>
        <Item>8014_polcoquette_modkit</Item>
      </kits>
      <windowsWithExposedEdges />
      <plateProbabilities>
        <Probabilities>
          <Item>
            <Name>Police guv plate</Name>
            <Value value="100" />
          </Item>
        </Probabilities>
      </plateProbabilities>
      <lightSettings value="1" />
      <sirenSettings value="240" />
    </Item>
    <Item>
      <modelName>poldinghy2</modelName>
      <colors>
        <Item>
          <indices content="char_array">
            5
            5
            0
            0
            0
            0
          </indices>
          <liveries>
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
          </liveries>
        </Item>
      </colors>
      <kits>
        <Item>8010_poldinghy2_modkit</Item>
      </kits>
      <windowsWithExposedEdges />
      <plateProbabilities>
        <Probabilities>
          <Item>
            <Name>Police guv plate</Name>
            <Value value="100" />
          </Item>
        </Probabilities>
      </plateProbabilities>
      <lightSettings value="1" />
      <sirenSettings value="241" />
    </Item>
    <Item>
      <modelName>poldominator</modelName>
      <colors>
        <Item>
          <indices content="char_array">
            5
            5
            0
            0
            0
          </indices>
          <liveries />
        </Item>
      </colors>
      <kits>
        <Item>8016_poldominator_modkit</Item>
      </kits>
      <windowsWithExposedEdges />
      <plateProbabilities>
        <Probabilities>
          <Item>
            <Name>Police guv plate</Name>
            <Value value="100" />
          </Item>
        </Probabilities>
      </plateProbabilities>
      <lightSettings value="133" />
      <sirenSettings value="240" />
    </Item>
    <Item>
      <modelName>polmav3</modelName>
      <colors>
        <Item>
          <indices content="char_array">
            112
            112
            5
            156
            0
            0
          </indices>
          <liveries />
        </Item>
      </colors>
      <kits>
        <Item>8013_polmav3_modkit</Item>
      </kits>
      <windowsWithExposedEdges />
      <plateProbabilities>
        <Probabilities />
      </plateProbabilities>
      <lightSettings value="18" />
      <sirenSettings value="0" />
    </Item>
    <Item>
      <modelName>polshinobi</modelName>
      <colors>
        <Item>
          <indices content="char_array">
            5
            5
            5
            40
          </indices>
          <liveries />
        </Item>
      </colors>
      <kits>
        <Item>8015_polshinobi_modkit</Item>
      </kits>
      <windowsWithExposedEdges />
      <plateProbabilities>
        <Probabilities>
          <Item>
            <Name>Police guv plate</Name>
            <Value value="100" />
          </Item>
        </Probabilities>
      </plateProbabilities>
      <lightSettings value="12" />
      <sirenSettings value="242" />
    </Item>
    <Item>
      <modelName>polstanier2</modelName>
      <colors>
        <Item>
          <indices content="char_array">
            5
            5
            0
            0
            0
            0
            0
          </indices>
          <liveries>
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
          </liveries>
        </Item>
      </colors>
      <kits>
        <Item>8001_polstanier2_modkit</Item>
      </kits>
      <windowsWithExposedEdges />
      <plateProbabilities>
        <Probabilities>
          <Item>
            <Name>police guv plate</Name>
            <Value value="100" />
          </Item>
        </Probabilities>
      </plateProbabilities>
      <lightSettings value="1" />
      <sirenSettings value="240" />
    </Item>
    <Item>
      <modelName>polstanier3</modelName>
      <colors>
        <Item>
          <indices content="char_array">
            5
            5
            0
            0
            0
            0
            0
          </indices>
          <liveries>
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
          </liveries>
        </Item>
      </colors>
      <kits>
        <Item>8022_polstanier3_modkit</Item>
      </kits>
      <windowsWithExposedEdges />
      <plateProbabilities>
        <Probabilities>
          <Item>
            <Name>police guv plate</Name>
            <Value value="100" />
          </Item>
        </Probabilities>
      </plateProbabilities>
      <lightSettings value="1" />
      <sirenSettings value="240" />
    </Item>
    <Item>
      <modelName>polgaunt</modelName>
      <colors>
        <Item>
          <indices content="char_array">
            5
            5
            0
            0
            2
            0
          </indices>
          <liveries>
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
          </liveries>
        </Item>
      </colors>
      <kits>
        <Item>8017_polgaunt_modkit</Item>
      </kits>
      <windowsWithExposedEdges />
      <plateProbabilities>
        <Probabilities>
          <Item>
            <Name>Police guv plate</Name>
            <Value value="100" />
          </Item>
        </Probabilities>
      </plateProbabilities>
      <lightSettings value="144" />
      <sirenSettings value="240" />
    </Item>
    <Item>
      <modelName>polgaunt2</modelName>
      <colors>
        <Item>
          <indices content="char_array">
            5
            5
            0
            0
            2
            0
          </indices>
          <liveries>
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
          </liveries>
        </Item>
      </colors>
      <kits>
        <Item>8017_polgaunt_modkit</Item>
      </kits>
      <windowsWithExposedEdges />
      <plateProbabilities>
        <Probabilities>
          <Item>
            <Name>Police guv plate</Name>
            <Value value="100" />
          </Item>
        </Probabilities>
      </plateProbabilities>
      <lightSettings value="144" />
      <sirenSettings value="240" />
    </Item>
    <Item>
      <modelName>polscout3</modelName>
      <colors>
        <Item>
          <indices content="char_array">
            5
            5
            0
            0
            0
            0
            0
          </indices>
          <liveries />
        </Item>
      </colors>
      <kits>
        <Item>8003_polscout3_modkit</Item>
      </kits>
      <windowsWithExposedEdges />
      <plateProbabilities>
        <Probabilities>
          <Item>
            <Name>Police guv plate</Name>
            <Value value="100" />
          </Item>
        </Probabilities>
      </plateProbabilities>
      <lightSettings value="1" />
      <sirenSettings value="240" />
    </Item>
    <Item>
      <modelName>poldominator2</modelName>
      <colors>
        <Item>
          <indices content="char_array">
            5
            5
            0
            0
            0
            0
          </indices>
          <liveries>
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
          </liveries>
        </Item>
      </colors>
      <kits>
        <Item>8018_poldominator2_modkit</Item>
      </kits>
      <windowsWithExposedEdges />
      <plateProbabilities>
        <Probabilities>
          <Item>
            <Name>Standard White</Name>
            <Value value="100" />
          </Item>
        </Probabilities>
      </plateProbabilities>
      <lightSettings value="196" />
      <sirenSettings value="240" />
    </Item>
    <Item>
      <modelName>polaleutian</modelName>
      <colors>
        <Item>
          <indices content="char_array">
            5
            5
            0
            0
            0
            0
          </indices>
          <liveries>
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
          </liveries>
        </Item>
      </colors>
      <kits>
        <Item>8019_polaleutian_modkit</Item>
      </kits>
      <windowsWithExposedEdges />
      <plateProbabilities>
        <Probabilities>
          <Item>
            <Name>Police Guv Plate</Name>
            <Value value="100" />
          </Item>
        </Probabilities>
      </plateProbabilities>
      <lightSettings value="23" />
      <sirenSettings value="240" />
    </Item>
    <Item>
      <modelName>poldora</modelName>
      <colors>
        <Item>
          <indices content="char_array">
            5
            5
            0
            0
            0
            156
          </indices>
          <liveries>
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
          </liveries>
        </Item>
      </colors>
      <kits>
        <Item>8021_poldora_modkit</Item>
      </kits>
      <windowsWithExposedEdges />
      <plateProbabilities>
        <Probabilities>
          <Item>
            <Name>Police guv plate</Name>
            <Value value="100" />
          </Item>
        </Probabilities>
      </plateProbabilities>
      <lightSettings value="0" />
      <sirenSettings value="143" />
    </Item>
    <Item>
      <modelName>polcara2</modelName>
      <colors>
        <Item>
          <indices content="char_array">
            5
            5
            0
            0
            0
            156
          </indices>
          <liveries>
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
          </liveries>
        </Item>
      </colors>
      <kits>
        <Item>8023_polcara2_modkit</Item>
      </kits>
      <windowsWithExposedEdges />
      <plateProbabilities>
        <Probabilities>
          <Item>
            <Name>Standard White</Name>
            <Value value="100" />
          </Item>
        </Probabilities>
      </plateProbabilities>
      <lightSettings value="1" />
      <sirenSettings value="240" />
    </Item>
    <Item>
      <modelName>polstorm2</modelName>
      <colors>
        <Item>
          <indices content="char_array">
            5
            5
            0
            156
          </indices>
          <liveries>
            <Item value="true" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
          </liveries>
        </Item>
      </colors>
      <kits>
        <Item>8024_polstorm2_modkit</Item>
      </kits>
      <windowsWithExposedEdges />
      <plateProbabilities>
        <Probabilities>
          <Item>
            <Name>Police Guv Plate</Name>
            <Value value="100" />
          </Item>
        </Probabilities>
      </plateProbabilities>
      <lightSettings value="119" />
      <sirenSettings value="240" />
    </Item>
    <Item>
      <modelName>polstorm2xl</modelName>
      <colors>
        <Item>
          <indices content="char_array">
            5
            5
            0
            156
          </indices>
          <liveries>
            <Item value="true" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
          </liveries>
        </Item>
      </colors>
      <kits>
        <Item>8025_polstorm2xl_modkit</Item>
      </kits>
      <windowsWithExposedEdges />
      <plateProbabilities>
        <Probabilities>
          <Item>
            <Name>Police Guv Plate</Name>
            <Value value="100" />
          </Item>
        </Probabilities>
      </plateProbabilities>
      <lightSettings value="119" />
      <sirenSettings value="240" />
    </Item>
    <Item>
      <modelName>polstorm2h</modelName>
      <colors>
        <Item>
          <indices content="char_array">
            5
            5
            0
            156
          </indices>
          <liveries>
            <Item value="true" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
          </liveries>
        </Item>
      </colors>
      <kits>
        <Item>8026_polstorm2h_modkit</Item>
      </kits>
      <windowsWithExposedEdges />
      <plateProbabilities>
        <Probabilities>
          <Item>
            <Name>Police Guv Plate</Name>
            <Value value="100" />
          </Item>
        </Probabilities>
      </plateProbabilities>
      <lightSettings value="119" />
      <sirenSettings value="240" />
    </Item>
    <Item>
      <modelName>polriot</modelName>
      <colors>
        <Item>
          <indices content="char_array">
            0
            0
            0
            0
            0
            0
          </indices>
        </Item>
      </colors>
      <kits>
        <Item>8027_polriot_modkit</Item>
      </kits>
      <windowsWithExposedEdges />
      <plateProbabilities>
        <Probabilities>
          <Item>
            <Name>Police guv plate</Name>
            <Value value="100" />
          </Item>
        </Probabilities>
      </plateProbabilities>
      <lightSettings value="1" />
      <sirenSettings value="144" />
    </Item>
    <Item>
      <modelName>polbanshee</modelName>
      <colors>
        <Item>
          <indices content="char_array">
            112
            0
            0
            0
            0
            0
          </indices>
          <liveries>
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
          </liveries>
        </Item>
      </colors>
      <kits>
        <Item>8028_polbanshee_modkit</Item>
      </kits>
      <windowsWithExposedEdges />
      <plateProbabilities>
        <Probabilities>
          <Item>
            <Name>Standard White</Name>
            <Value value="100" />
          </Item>
        </Probabilities>
      </plateProbabilities>
      <lightSettings value="187" />
      <sirenSettings value="240" />
    </Item>
    <Item>
      <modelName>polcoquette2</modelName>
      <colors>
        <Item>
          <indices content="char_array">
            112
            0
            0
            0
            0
            0
          </indices>
          <liveries>
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
          </liveries>
        </Item>
      </colors>
      <kits>
        <Item>8029_polcoquette2_modkit</Item>
      </kits>
      <windowsWithExposedEdges />
      <plateProbabilities>
        <Probabilities>
          <Item>
            <Name>Standard White</Name>
            <Value value="100" />
          </Item>
        </Probabilities>
      </plateProbabilities>
      <lightSettings value="119" />
      <sirenSettings value="240" />
    </Item>
    <Item>
      <modelName>polchavos</modelName>
      <colors>
        <Item>
          <indices content="char_array">
            0
            112
            0
            0
            0
            0
          </indices>
          <liveries>
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
            <Item value="false" />
          </liveries>
        </Item>
      </colors>
      <kits>
        <Item>8030_polchavos_modkit</Item>
      </kits>
      <windowsWithExposedEdges />
      <plateProbabilities>
        <Probabilities>
          <Item>
            <Name>Standard White</Name>
            <Value value="100" />
          </Item>
        </Probabilities>
      </plateProbabilities>
      <lightSettings value="119" />
      <sirenSettings value="240" />
    </Item>
  </variationData>
</CVehicleModelInfoVariation>