<?xml version="1.0" encoding="UTF-8"?>

<CVehicleMetadataMgr>
  <AnimRateSets />
  <ClipSetMaps>
    <Item type="CClipSetMap">
      <Name>ENTRY_CLIPSET_MAP_ALEUTIAN_FRONT_LEFT</Name>
      <Maps>
        <Item>
          <ClipSetId>clipset@anim@veh@granger2@ds@enter_exit</ClipSetId>
          <VarClipSetId>EntryVarClipSet</VarClipSetId>
          <ConditionFlags />
          <InformationFlags />
        </Item>
      </Maps>
    </Item>
    <Item type="CClipSetMap">
      <Name>ENTRY_CLIPSET_MAP_ALEUTIAN_FRONT_RIGHT</Name>
      <Maps>
        <Item>
          <ClipSetId>clipset@anim@veh@granger2@ps@enter_exit</ClipSetId>
          <VarClipSetId>EntryVarClipSet</VarClipSetId>
          <ConditionFlags />
          <InformationFlags />
        </Item>
      </Maps>
    </Item>
  </ClipSetMaps>
  <VehicleCoverBoundOffsetInfos>
    <Item type="CVehicleCoverBoundOffsetInfo">
      <Name>ALEUTIAN_COVER_OFFSET_INFO</Name>
      <ExtraSideOffset value="0.030000" />
      <ExtraForwardOffset value="-0.010000" />
      <ExtraBackwardOffset value="-0.270000" />
      <ExtraZOffset value="0.100000" />
      <CoverBoundInfos />
    </Item>
    <Item type="CVehicleCoverBoundOffsetInfo">
      <Name>JMBEARCAT_COVER_OFFSET_INFO</Name>
      <ExtraSideOffset value="0.000000" />
      <ExtraForwardOffset value="0.000000" />
      <ExtraBackwardOffset value="0.000000" />
      <ExtraZOffset value="0.000000" />
      <CoverBoundInfos>
        <Item>
          <Name>Front</Name>
          <Position x="0.000000" y="2.550000" z="0.250000" />
          <Length value="1.300000" />
          <Width value="2.600000" />
          <Height value="2.000000" />
          <ActiveBoundExclusions />
        </Item>
        <Item>
          <Name>MIDDLE</Name>
          <Position x="0.000000" y="0.400000" z="0.250000" />
          <Length value="3.000000" />
          <Width value="2.850000" />
          <Height value="2.000000" />
          <ActiveBoundExclusions />
        </Item>
        <Item>
          <Name>REAR</Name>
          <Position x="0.000000" y="-1.950000" z="0.250000" />
          <Length value="1.700000" />
          <Width value="2.700000" />
          <Height value="2.000000" />
          <ActiveBoundExclusions />
        </Item>
      </CoverBoundInfos>
    </Item>
  </VehicleCoverBoundOffsetInfos>
  <BicycleInfos />
  <POVTuningInfos />
  <EntryAnimVariations />
  <VehicleExtraPointsInfos>
  </VehicleExtraPointsInfos>
  <DrivebyWeaponGroups />
  <VehicleDriveByAnimInfos>
  </VehicleDriveByAnimInfos>
  <VehicleDriveByInfos>
    <Item type="CVehicleDriveByInfo">
      <Name>GUNPORT_JMBEARCAT_REAR_LEFT</Name>
      <MinAimSweepHeadingAngleDegs value="-88.000000" />
      <MaxAimSweepHeadingAngleDegs value="88.000000" />
      <FirstPersonMinAimSweepHeadingAngleDegs value="-180.000000" />
      <FirstPersonMaxAimSweepHeadingAngleDegs value="180.000000" />
      <FirstPersonUnarmedMinAimSweepHeadingAngleDegs value="-180.000000" />
      <FirstPersonUnarmedMaxAimSweepHeadingAngleDegs value="180.000000" />
      <MinRestrictedAimSweepHeadingAngleDegs value="-180.000000" />
      <MaxRestrictedAimSweepHeadingAngleDegs value="0.000000" />
      <MinSmashWindowAngleDegs value="0.000000" />
      <MaxSmashWindowAngleDegs value="0.000000" />
      <MinSmashWindowAngleFirstPersonDegs value="0.000000" />
      <MaxSmashWindowAngleFirstPersonDegs value="0.000000" />
      <MaxSpeedParam value="0.500000" />
      <MaxLongitudinalLeanBlendWeightDelta value="0.250000" />
      <MaxLateralLeanBlendWeightDelta value="0.250000" />
      <ApproachSpeedToWithinMaxBlendDelta value="1.000000" />
      <SpineAdditiveBlendInDelay value="0.050000" />
      <SpineAdditiveBlendInDurationStill value="0.150000" />
      <SpineAdditiveBlendInDuration value="0.750000" />
      <SpineAdditiveBlendOutDelay value="0.000000" />
      <SpineAdditiveBlendOutDuration value="0.500000" />
      <MinUnarmedDrivebyYawIfWindowRolledUp value="-3.140000" />
      <MaxUnarmedDrivebyYawIfWindowRolledUp value="3.140000" />
      <DriveByAnimInfos />
      <DriveByCamera>APC_SIDE_LEFT_AIM_CAMERA</DriveByCamera>
      <PovDriveByCamera>POV_APC_SIDE_LEFT_CAMERA</PovDriveByCamera>
      <DriveByFlags>DampenRecoil UseThreeAnimIntroOutro</DriveByFlags>
    </Item>
    <Item type="CVehicleDriveByInfo">
      <Name>GUNPORT_JMBEARCAT_REAR_RIGHT</Name>
      <MinAimSweepHeadingAngleDegs value="-88.000000" />
      <MaxAimSweepHeadingAngleDegs value="88.000000" />
      <FirstPersonMinAimSweepHeadingAngleDegs value="-180.000000" />
      <FirstPersonMaxAimSweepHeadingAngleDegs value="180.000000" />
      <FirstPersonUnarmedMinAimSweepHeadingAngleDegs value="-180.000000" />
      <FirstPersonUnarmedMaxAimSweepHeadingAngleDegs value="180.000000" />
      <MinRestrictedAimSweepHeadingAngleDegs value="0.000000" />
      <MaxRestrictedAimSweepHeadingAngleDegs value="180.000000" />
      <MinSmashWindowAngleDegs value="0.000000" />
      <MaxSmashWindowAngleDegs value="0.000000" />
      <MinSmashWindowAngleFirstPersonDegs value="0.000000" />
      <MaxSmashWindowAngleFirstPersonDegs value="0.000000" />
      <MaxSpeedParam value="0.500000" />
      <MaxLongitudinalLeanBlendWeightDelta value="0.250000" />
      <MaxLateralLeanBlendWeightDelta value="0.250000" />
      <ApproachSpeedToWithinMaxBlendDelta value="1.000000" />
      <SpineAdditiveBlendInDelay value="0.050000" />
      <SpineAdditiveBlendInDurationStill value="0.150000" />
      <SpineAdditiveBlendInDuration value="0.750000" />
      <SpineAdditiveBlendOutDelay value="0.000000" />
      <SpineAdditiveBlendOutDuration value="0.500000" />
      <MinUnarmedDrivebyYawIfWindowRolledUp value="-3.140000" />
      <MaxUnarmedDrivebyYawIfWindowRolledUp value="3.140000" />
      <DriveByAnimInfos />
      <DriveByCamera>APC_SIDE_RIGHT_AIM_CAMERA</DriveByCamera>
      <PovDriveByCamera>POV_APC_SIDE_RIGHT_CAMERA</PovDriveByCamera>
      <DriveByFlags>DampenRecoil UseThreeAnimIntroOutro</DriveByFlags>
    </Item>
  </VehicleDriveByInfos>
  <VehicleSeatInfos>
    <Item type="CVehicleSeatInfo"> <!-- DRIVER SEAT -->
      <Name>SEAT_JMBEARCAT_FRONT_LEFT</Name>
      <SeatBoneName>seat_dside_f</SeatBoneName>
      <ShuffleLink>SEAT_JMBEARCAT_FRONT_RIGHT</ShuffleLink>
      <RearSeatLink />
      <DefaultCarTask>TASK_DRIVE_WANDER</DefaultCarTask>
      <SeatFlags>IsFrontSeat KeepOnHeadProp</SeatFlags>
      <HairScale value="0.000000" />
    </Item>
	  <Item type="CVehicleSeatInfo"> <!-- PASSENGER SEAT -->
      <Name>SEAT_JMBEARCAT_FRONT_RIGHT</Name>
      <SeatBoneName>seat_pside_f</SeatBoneName>
      <ShuffleLink>SEAT_JMBEARCAT_FRONT_LEFT</ShuffleLink> 
      <RearSeatLink />
      <DefaultCarTask>TASK_DRIVE_WANDER</DefaultCarTask>
      <SeatFlags>IsFrontSeat KeepOnHeadProp</SeatFlags>
      <HairScale value="0.000000" />
    </Item>
    <Item type="CVehicleSeatInfo"> <!-- LEFT JUMP SEAT -->
      <Name>SEAT_JMBEARCAT_JUMP_LEFT</Name>
      <SeatBoneName>seat_dside_f1</SeatBoneName>
      <ShuffleLink>SEAT_JMBEARCAT_JUMP_RIGHT</ShuffleLink>
      <RearSeatLink />
      <DefaultCarTask>TASK_DRIVE_WANDER</DefaultCarTask>
      <SeatFlags>IsFrontSeat KeepOnHeadProp</SeatFlags>
      <HairScale value="0.000000" />
    </Item>
	  <Item type="CVehicleSeatInfo"> <!-- RIGHT JUMP SEAT -->
      <Name>SEAT_JMBEARCAT_JUMP_RIGHT</Name>
      <SeatBoneName>seat_pside_f1</SeatBoneName>
      <ShuffleLink>SEAT_JMBEARCAT_JUMP_LEFT</ShuffleLink> 
      <RearSeatLink />
      <DefaultCarTask>TASK_DRIVE_WANDER</DefaultCarTask>
      <SeatFlags>IsFrontSeat KeepOnHeadProp</SeatFlags>
      <HairScale value="0.000000" />
    </Item>
    <Item type="CVehicleSeatInfo"> <!-- LEFT REAR SEAT -->
      <Name>SEAT_JMBEARCAT_REAR_LEFT</Name>
      <SeatBoneName>seat_dside_r</SeatBoneName>
      <ShuffleLink />
      <RearSeatLink />
      <DefaultCarTask>TASK_DRIVE_WANDER</DefaultCarTask>
      <SeatFlags>IsIdleSeat KeepOnHeadProp</SeatFlags>
      <HairScale value="0.000000" />
    </Item>
	  <Item type="CVehicleSeatInfo"> <!-- RIGHT REAR SEAT -->
      <Name>SEAT_JMBEARCAT_REAR_RIGHT</Name>
      <SeatBoneName>seat_pside_r</SeatBoneName>
      <ShuffleLink />
      <RearSeatLink />
      <DefaultCarTask>TASK_DRIVE_WANDER</DefaultCarTask>
      <SeatFlags>IsIdleSeat KeepOnHeadProp</SeatFlags>
      <HairScale value="0.000000" />
    </Item>
    <Item type="CVehicleSeatInfo"> <!-- LEFT WARP SEAT -->
      <Name>SEAT_JMBEARCAT_WARP_REAR_LEFT</Name>
      <SeatBoneName>seat_dside_r1</SeatBoneName>
      <ShuffleLink />
      <RearSeatLink />
      <DefaultCarTask>TASK_DRIVE_WANDER</DefaultCarTask>
      <SeatFlags>IsIdleSeat KeepOnHeadProp</SeatFlags>
      <HairScale value="0.000000" />
    </Item>
	  <Item type="CVehicleSeatInfo"> <!-- RIGHT WARP SEAT -->
      <Name>SEAT_JMBEARCAT_WARP_REAR_RIGHT</Name>
      <SeatBoneName>seat_pside_r1</SeatBoneName>
      <ShuffleLink />
      <RearSeatLink />
      <DefaultCarTask>TASK_DRIVE_WANDER</DefaultCarTask>
      <SeatFlags>IsIdleSeat KeepOnHeadProp</SeatFlags>
      <HairScale value="0.000000" />
    </Item>
    <Item type="CVehicleSeatInfo"> <!-- LEFT STANDING SEAT -->
      <Name>SEAT_JMBEARCAT_STANDING_LEFT</Name>
      <SeatBoneName>seat_dside_r2</SeatBoneName>
      <ShuffleLink />
      <RearSeatLink />
      <DefaultCarTask>TASK_DRIVE_WANDER</DefaultCarTask>
      <SeatFlags>KeepOnHeadProp</SeatFlags>
      <HairScale value="0.000000" />
    </Item>
    <Item type="CVehicleSeatInfo"> <!-- RIGHT STANDING SEAT -->
      <Name>SEAT_JMBEARCAT_STANDING_RIGHT</Name>
      <SeatBoneName>seat_pside_r2</SeatBoneName>
      <ShuffleLink />
      <RearSeatLink />
      <DefaultCarTask>TASK_DRIVE_WANDER</DefaultCarTask>
      <SeatFlags>KeepOnHeadProp</SeatFlags>
      <HairScale value="0.000000" />
    </Item>
    <Item type="CVehicleSeatInfo"> <!-- LEFT STANDING EXTRA SEAT -->
      <Name>SEAT_JMBEARCAT_STANDING_LEFT_EXTRA</Name>
      <SeatBoneName>seat_dside_r3</SeatBoneName>
      <ShuffleLink />
      <RearSeatLink />
      <DefaultCarTask>TASK_DRIVE_WANDER</DefaultCarTask>
      <SeatFlags>KeepOnHeadProp</SeatFlags>
      <HairScale value="0.000000" />
    </Item>
    <Item type="CVehicleSeatInfo"> <!-- RIGHT STANDING EXTRA SEAT -->
      <Name>SEAT_JMBEARCAT_STANDING_RIGHT_EXTRA</Name>
      <SeatBoneName>seat_pside_r3</SeatBoneName>
      <ShuffleLink />
      <RearSeatLink />
      <DefaultCarTask>TASK_DRIVE_WANDER</DefaultCarTask>
      <SeatFlags>KeepOnHeadProp</SeatFlags>
      <HairScale value="0.000000" />
    </Item>
    <Item type="CVehicleSeatInfo"> <!-- LEFT STANDING REAR SEAT -->
      <Name>SEAT_JMBEARCAT_STANDING_REAR_LEFT</Name>
      <SeatBoneName>seat_dside_r4</SeatBoneName>
      <ShuffleLink />
      <RearSeatLink />
      <DefaultCarTask>TASK_DRIVE_WANDER</DefaultCarTask>
      <SeatFlags>KeepOnHeadProp DontDetachOnWorldCollision</SeatFlags>
      <HairScale value="0.000000" />
    </Item>
    <Item type="CVehicleSeatInfo"> <!-- RIGHT STANDING REAR SEAT -->
      <Name>SEAT_JMBEARCAT_STANDING_REAR_RIGHT</Name>
      <SeatBoneName>seat_pside_r4</SeatBoneName>
      <ShuffleLink />
      <RearSeatLink />
      <DefaultCarTask>TASK_DRIVE_WANDER</DefaultCarTask>
      <SeatFlags>KeepOnHeadProp DontDetachOnWorldCollision</SeatFlags>
      <HairScale value="0.000000" />
    </Item>
  </VehicleSeatInfos>
  <VehicleSeatAnimInfos>
    <Item type="CVehicleSeatAnimInfo">
      <Name>SEAT_ANIM_JMBEARCAT_FRONT_LEFT</Name>
      <DriveByInfo ref="NULL" />
      <InsideClipSetMap ref="INSIDE_CLIPSET_MAP_VAN_FRONT_LEFT" />
      <PanicClipSet>clipset@veh@van@ds@idle_panic</PanicClipSet>
      <AgitatedClipSet>clipset@veh@van@ds@idle_agitated</AgitatedClipSet>
      <DuckedClipSet>clipset@veh@van@ds@idle_duck</DuckedClipSet>
      <LowLODIdleAnim>VAN_FRONT_DS_IDLE</LowLODIdleAnim>
      <SeatAmbientContext>IN_VAN</SeatAmbientContext>
      <InVehicleMoveNetwork>VEHICLE_DEFAULT</InVehicleMoveNetwork>
      <SeatAnimFlags>UseStandardInVehicleAnims UseCloseDoorBlendAnims</SeatAnimFlags>
      <SteeringSmoothing value="0.100000" />
      <ExitToAimInfoName>MESA</ExitToAimInfoName>
      <MaleGestureClipSetId>ANIM_GROUP_GESTURE_M_CAR_VAN_DS</MaleGestureClipSetId>
      <FemaleGestureClipSetId>ANIM_GROUP_GESTURE_F_CAR_VAN_DS</FemaleGestureClipSetId>
    </Item>
    <Item type="CVehicleSeatAnimInfo">
      <Name>SEAT_ANIM_JMBEARCAT_FRONT_RIGHT</Name>
      <DriveByInfo ref="NULL" />
      <InsideClipSetMap ref="INSIDE_CLIPSET_MAP_VAN_FRONT_RIGHT" />
      <PanicClipSet>clipset@veh@van@ps@idle_panic</PanicClipSet>
      <AgitatedClipSet />
      <DuckedClipSet>clipset@veh@van@ps@idle_duck</DuckedClipSet>
      <LowLODIdleAnim>VAN_FRONT_PS_IDLE</LowLODIdleAnim>
      <SeatAmbientContext>IN_VAN</SeatAmbientContext>
      <InVehicleMoveNetwork>VEHICLE_DEFAULT</InVehicleMoveNetwork>
      <SeatAnimFlags>UseStandardInVehicleAnims PreventShuffleJack UseCloseDoorBlendAnims</SeatAnimFlags>
      <SteeringSmoothing value="0.100000" />
      <ExitToAimInfoName>MESA</ExitToAimInfoName>
      <MaleGestureClipSetId>ANIM_GROUP_GESTURE_M_CAR_VAN_PS</MaleGestureClipSetId>
      <FemaleGestureClipSetId>ANIM_GROUP_GESTURE_F_CAR_VAN_PS</FemaleGestureClipSetId>
    </Item>
    <Item type="CVehicleSeatAnimInfo">
      <Name>SEAT_ANIM_JMBEARCAT_JUMP_LEFT</Name>
      <DriveByInfo ref="NULL" />
      <InsideClipSetMap ref="INSIDE_CLIPSET_MAP_VAN_REAR_LEFT" />
      <PanicClipSet>clipset@veh@van@rds_rear@idle_panic</PanicClipSet>
      <AgitatedClipSet>clipset@veh@van@rds_rear@idle_agitated</AgitatedClipSet>
      <DuckedClipSet />
      <LowLODIdleAnim>VAN_REAR_DS_IDLE</LowLODIdleAnim>
      <SeatAmbientContext />
      <InVehicleMoveNetwork>VEHICLE_DEFAULT</InVehicleMoveNetwork>
      <SeatAnimFlags>UseStandardInVehicleAnims PreventShuffleJack UseCloseDoorBlendAnims</SeatAnimFlags>
      <SteeringSmoothing value="0.100000" />
      <ExitToAimInfoName />
      <MaleGestureClipSetId />
      <FemaleGestureClipSetId />
    </Item>
    <Item type="CVehicleSeatAnimInfo">
      <Name>SEAT_ANIM_JMBEARCAT_JUMP_RIGHT</Name>
      <DriveByInfo ref="NULL" />
      <InsideClipSetMap ref="INSIDE_CLIPSET_MAP_VAN_REAR_RIGHT" />
      <PanicClipSet>clipset@veh@van@rps_rear@idle_panic</PanicClipSet>
      <AgitatedClipSet>clipset@veh@van@rps_rear@idle_agitated</AgitatedClipSet>
      <DuckedClipSet />
      <LowLODIdleAnim>VAN_REAR_PS_IDLE</LowLODIdleAnim>
      <SeatAmbientContext />
      <InVehicleMoveNetwork>VEHICLE_DEFAULT</InVehicleMoveNetwork>
      <SeatAnimFlags>UseStandardInVehicleAnims PreventShuffleJack UseCloseDoorBlendAnims</SeatAnimFlags>
      <SteeringSmoothing value="0.100000" />
      <ExitToAimInfoName />
      <MaleGestureClipSetId />
      <FemaleGestureClipSetId />
    </Item>
    <Item type="CVehicleSeatAnimInfo">
      <Name>SEAT_ANIM_JMBEARCAT_REAR_LEFT</Name>
      <DriveByInfo ref="GUNPORT_JMBEARCAT_REAR_LEFT" /> <!--APC/CARBINE-->
      <InsideClipSetMap ref="INSIDE_CLIPSET_MAP_VAN_REAR_LEFT" />
      <PanicClipSet>clipset@veh@van@rds_rear@idle_panic</PanicClipSet>
      <AgitatedClipSet>clipset@veh@van@rds_rear@idle_agitated</AgitatedClipSet>
      <DuckedClipSet />
      <LowLODIdleAnim>VAN_REAR_DS_IDLE</LowLODIdleAnim>
      <SeatAmbientContext />
      <InVehicleMoveNetwork>VEHICLE_DEFAULT</InVehicleMoveNetwork>
      <SeatAnimFlags>UseStandardInVehicleAnims PreventShuffleJack UseCloseDoorBlendAnims</SeatAnimFlags>
      <SteeringSmoothing value="0.100000" />
      <ExitToAimInfoName />
      <MaleGestureClipSetId />
      <FemaleGestureClipSetId />
    </Item>
    <Item type="CVehicleSeatAnimInfo">
      <Name>SEAT_ANIM_JMBEARCAT_REAR_RIGHT</Name>
      <DriveByInfo ref="GUNPORT_JMBEARCAT_REAR_RIGHT" /> <!--APC/CARBINE-->
      <InsideClipSetMap ref="INSIDE_CLIPSET_MAP_VAN_REAR_RIGHT" />
      <PanicClipSet>clipset@veh@van@rps_rear@idle_panic</PanicClipSet>
      <AgitatedClipSet>clipset@veh@van@rps_rear@idle_agitated</AgitatedClipSet>
      <DuckedClipSet />
      <LowLODIdleAnim>VAN_REAR_PS_IDLE</LowLODIdleAnim>
      <SeatAmbientContext />
      <InVehicleMoveNetwork>VEHICLE_DEFAULT</InVehicleMoveNetwork>
      <SeatAnimFlags>UseStandardInVehicleAnims PreventShuffleJack UseCloseDoorBlendAnims</SeatAnimFlags>
      <SteeringSmoothing value="0.100000" />
      <ExitToAimInfoName />
      <MaleGestureClipSetId />
      <FemaleGestureClipSetId />
    </Item>
  </VehicleSeatAnimInfos>
  <VehicleEntryPointInfos>
    <Item type="CVehicleEntryPointInfo">
      <Name>ENTRY_POINT_JMBEARCAT_FRONT_LEFT</Name>
      <DoorBoneName>door_dside_f</DoorBoneName>
      <SecondDoorBoneName />
      <DoorHandleBoneName>handle_dside_f</DoorHandleBoneName>
      <WindowId>FRONT_LEFT</WindowId>
      <VehicleSide>SIDE_LEFT</VehicleSide>
      <AccessableSeats>
        <Item ref="SEAT_JMBEARCAT_FRONT_LEFT" />
      </AccessableSeats>
      <VehicleExtraPointsInfo ref="EXTRA_VEHICLE_POINTS_STD_LEFT" /> <!--TBC-->
      <Flags>BlockJackReactionUntilJackerIsReady</Flags>
      <BlockJackReactionSides>
        <Item>SIDE_LEFT</Item>
      </BlockJackReactionSides>
    </Item>
    <Item type="CVehicleEntryPointInfo">
      <Name>ENTRY_POINT_JMBEARCAT_FRONT_RIGHT</Name>
      <DoorBoneName>door_pside_f</DoorBoneName>
      <SecondDoorBoneName />
      <DoorHandleBoneName>handle_pside_f</DoorHandleBoneName>
      <WindowId>FRONT_RIGHT</WindowId>
      <VehicleSide>SIDE_RIGHT</VehicleSide>
      <AccessableSeats>
        <Item ref="SEAT_JMBEARCAT_FRONT_RIGHT" />
      </AccessableSeats>
      <VehicleExtraPointsInfo ref="EXTRA_VEHICLE_POINTS_STD_RIGHT" /> <!--TBC-->
      <Flags>BlockJackReactionUntilJackerIsReady</Flags>
      <BlockJackReactionSides>
        <Item>SIDE_RIGHT</Item>
      </BlockJackReactionSides>
    </Item>
    <Item type="CVehicleEntryPointInfo">
      <Name>ENTRY_POINT_JMBEARCAT_JUMP_LEFT</Name>
      <DoorBoneName>boot</DoorBoneName>
      <SecondDoorBoneName />
      <DoorHandleBoneName>handle_boot</DoorHandleBoneName>
      <WindowId>INVALID</WindowId>
      <VehicleSide>SIDE_LEFT</VehicleSide>
      <AccessableSeats>
        <Item ref="SEAT_JMBEARCAT_JUMP_LEFT" />
      </AccessableSeats>
      <VehicleExtraPointsInfo ref="EXTRA_VEHICLE_POINTS_STD_LEFT" /> <!--TBC-->
      <Flags>BlockJackReactionUntilJackerIsReady</Flags>
      <BlockJackReactionSides>
        <Item>SIDE_LEFT</Item>
      </BlockJackReactionSides>
    </Item>
    <Item type="CVehicleEntryPointInfo">
      <Name>ENTRY_POINT_JMBEARCAT_JUMP_RIGHT</Name>
      <DoorBoneName>bonnet</DoorBoneName>
      <SecondDoorBoneName />
      <DoorHandleBoneName>handle_bonnet</DoorHandleBoneName>
      <WindowId>INVALID</WindowId>
      <VehicleSide>SIDE_RIGHT</VehicleSide>
      <AccessableSeats>
        <Item ref="SEAT_JMBEARCAT_JUMP_RIGHT" />
      </AccessableSeats>
      <VehicleExtraPointsInfo ref="EXTRA_VEHICLE_POINTS_STD_RIGHT" /> <!--TBC-->
      <Flags>BlockJackReactionUntilJackerIsReady</Flags>
      <BlockJackReactionSides>
        <Item>SIDE_RIGHT</Item>
      </BlockJackReactionSides>
    </Item>
    <Item type="CVehicleEntryPointInfo">
      <Name>ENTRY_POINT_JMBEARCAT_REAR_LEFT</Name>
      <DoorBoneName>door_dside_r</DoorBoneName>
      <SecondDoorBoneName />
      <DoorHandleBoneName>handle_dside_r</DoorHandleBoneName>
      <WindowId>REAR_LEFT</WindowId> <!--CHECK
      BUS ID-->
      <VehicleSide>SIDE_REAR</VehicleSide>
      <AccessableSeats>
        <Item ref="SEAT_JMBEARCAT_REAR_LEFT" />
      </AccessableSeats>
      <VehicleExtraPointsInfo ref="EXTRA_VEHICLE_POINTS_STD_LEFT" /> <!--TBC-->
      <Flags />
      <BlockJackReactionSides />
    </Item>
    <Item type="CVehicleEntryPointInfo">
      <Name>ENTRY_POINT_JMBEARCAT_REAR_RIGHT</Name>
      <DoorBoneName>door_pside_r</DoorBoneName>
      <SecondDoorBoneName />
      <DoorHandleBoneName>handle_pside_r</DoorHandleBoneName>
      <WindowId>REAR_RIGHT</WindowId> <!--CHECK
      BUS ID-->
      <VehicleSide>SIDE_REAR</VehicleSide>
      <AccessableSeats>
        <Item ref="SEAT_JMBEARCAT_REAR_RIGHT" />
      </AccessableSeats>
      <VehicleExtraPointsInfo ref="EXTRA_VEHICLE_POINTS_STD_RIGHT" /> <!--TBC-->
      <Flags />
      <BlockJackReactionSides />
    </Item>
    <Item type="CVehicleEntryPointInfo">
      <Name>ENTRY_POINT_JMBEARCAT_WARP_REAR_LEFT</Name>
      <DoorBoneName />
      <SecondDoorBoneName />
      <DoorHandleBoneName />
      <WindowId>INVALID</WindowId>
      <VehicleSide>SIDE_LEFT</VehicleSide>
      <AccessableSeats>
        <Item ref="SEAT_JMBEARCAT_WARP_REAR_LEFT" />
      </AccessableSeats>
      <VehicleExtraPointsInfo ref="NULL" />
      <Flags>MPWarpInOut</Flags>
      <BlockJackReactionSides />
    </Item>
    <Item type="CVehicleEntryPointInfo">
      <Name>ENTRY_POINT_JMBEARCAT_WARP_REAR_RIGHT</Name>
      <DoorBoneName />
      <SecondDoorBoneName />
      <DoorHandleBoneName />
      <WindowId>INVALID</WindowId>
      <VehicleSide>SIDE_RIGHT</VehicleSide>
      <AccessableSeats>
        <Item ref="SEAT_JMBEARCAT_WARP_REAR_RIGHT" />
      </AccessableSeats>
      <VehicleExtraPointsInfo ref="NULL" />
      <Flags>MPWarpInOut</Flags>
      <BlockJackReactionSides />
    </Item>
    <Item type="CVehicleEntryPointInfo">
      <Name>ENTRY_POINT_JMBEARCAT_STANDING_LEFT</Name>
      <DoorBoneName />
      <SecondDoorBoneName />
      <DoorHandleBoneName />
      <WindowId>INVALID</WindowId>
      <VehicleSide>SIDE_LEFT</VehicleSide>
      <AccessableSeats>
        <Item ref="SEAT_JMBEARCAT_STANDING_LEFT" />
      </AccessableSeats>
      <VehicleExtraPointsInfo ref="NULL" />
      <Flags />
      <BlockJackReactionSides />
    </Item>
    <Item type="CVehicleEntryPointInfo">
      <Name>ENTRY_POINT_JMBEARCAT_STANDING_RIGHT</Name>
      <DoorBoneName />
      <SecondDoorBoneName />
      <DoorHandleBoneName />
      <WindowId>INVALID</WindowId>
      <VehicleSide>SIDE_RIGHT</VehicleSide>
      <AccessableSeats>
        <Item ref="SEAT_JMBEARCAT_STANDING_RIGHT" />
      </AccessableSeats>
      <VehicleExtraPointsInfo ref="NULL" />
      <Flags />
      <BlockJackReactionSides />
    </Item>
    <Item type="CVehicleEntryPointInfo">
      <Name>ENTRY_POINT_JMBEARCAT_STANDING_LEFT_EXTRA</Name>
      <DoorBoneName />
      <SecondDoorBoneName />
      <DoorHandleBoneName />
      <WindowId>INVALID</WindowId>
      <VehicleSide>SIDE_LEFT</VehicleSide>
      <AccessableSeats>
        <Item ref="SEAT_JMBEARCAT_STANDING_LEFT_EXTRA" />
      </AccessableSeats>
      <VehicleExtraPointsInfo ref="NULL" />
      <Flags />
      <BlockJackReactionSides />
    </Item>
    <Item type="CVehicleEntryPointInfo">
      <Name>ENTRY_POINT_JMBEARCAT_STANDING_RIGHT_EXTRA</Name>
      <DoorBoneName />
      <SecondDoorBoneName />
      <DoorHandleBoneName />
      <WindowId>INVALID</WindowId>
      <VehicleSide>SIDE_RIGHT</VehicleSide>
      <AccessableSeats>
        <Item ref="SEAT_JMBEARCAT_STANDING_RIGHT_EXTRA" />
      </AccessableSeats>
      <VehicleExtraPointsInfo ref="NULL" />
      <Flags />
      <BlockJackReactionSides />
    </Item>
    <Item type="CVehicleEntryPointInfo">
      <Name>ENTRY_POINT_JMBEARCAT_STANDING_REAR_LEFT</Name>
      <DoorBoneName />
      <SecondDoorBoneName />
      <DoorHandleBoneName />
      <WindowId>INVALID</WindowId>
      <VehicleSide>SIDE_LEFT</VehicleSide>
      <AccessableSeats>
        <Item ref="SEAT_JMBEARCAT_STANDING_REAR_LEFT" />
      </AccessableSeats>
      <VehicleExtraPointsInfo ref="NULL" />
      <Flags />
      <BlockJackReactionSides />
    </Item>
    <Item type="CVehicleEntryPointInfo">
      <Name>ENTRY_POINT_JMBEARCAT_STANDING_REAR_RIGHT</Name>
      <DoorBoneName />
      <SecondDoorBoneName />
      <DoorHandleBoneName />
      <WindowId>INVALID</WindowId>
      <VehicleSide>SIDE_RIGHT</VehicleSide>
      <AccessableSeats>
        <Item ref="SEAT_JMBEARCAT_STANDING_REAR_RIGHT" />
      </AccessableSeats>
      <VehicleExtraPointsInfo ref="NULL" />
      <Flags />
      <BlockJackReactionSides />
    </Item>
  </VehicleEntryPointInfos>
  <VehicleEntryPointAnimInfos>
    <Item type="CVehicleEntryPointAnimInfo">
      <Name>ENTRY_POINT_ANIM_ALEUTIAN_FRONT_LEFT</Name>
      <CommonClipSetMap ref="COMMON_CLIPSET_MAP_AUTOMOBILE_LEFT" />
      <EntryClipSetMap ref="ENTRY_CLIPSET_MAP_ALEUTIAN_FRONT_LEFT" />
      <ExitClipSetMap ref="EXIT_CLIPSET_MAP_GRANGER_FRONT_LEFT" />
      <AlternateTryLockedDoorClipId />
      <AlternateForcedEntryClipId />
      <AlternateJackFromOutSideClipId>van_perp_ds_a</AlternateJackFromOutSideClipId>
      <AlternateBeJackedFromOutSideClipId>van_victim_ds_a</AlternateBeJackedFromOutSideClipId>
      <AlternateEntryPointClipSetId />
      <EnterVehicleMoveNetwork>ENTER_VEHICLE_STD</EnterVehicleMoveNetwork>
      <EntryTranslation x="-1.400000" y="-0.500000" z="0.250000" />
      <OpenDoorTranslation x="0.000000" y="0.000000" />
      <OpenDoorHeadingChange value="1.570000" />
      <EntryHeadingChange value="-1.570000" />
      <ExtraZForMPPlaneWarp value="-2.000000" />
      <EntryPointFlags>JackIncludesGetIn UsesNoDoorTransitionForExit UseOpenDoorBlendAnims
        UseGetUpAfterJack JackVariationsIncludeGetIn DeadJackIncludesGetIn HasCombatEntry</EntryPointFlags>
      <EntryAnimVariations ref="ENTRY_ANIM_VARIATIONS_VAN_FRONT_LEFT" />
      <NMJumpFromVehicleTuningSet>SLOWTRUCK</NMJumpFromVehicleTuningSet>
    </Item>
    <Item type="CVehicleEntryPointAnimInfo">
      <Name>ENTRY_POINT_ANIM_ALEUTIAN_FRONT_RIGHT</Name>
      <CommonClipSetMap ref="COMMON_CLIPSET_MAP_AUTOMOBILE_RIGHT" />
      <EntryClipSetMap ref="ENTRY_CLIPSET_MAP_ALEUTIAN_FRONT_RIGHT" />
      <ExitClipSetMap ref="EXIT_CLIPSET_MAP_GRANGER_FRONT_RIGHT" />
      <AlternateTryLockedDoorClipId />
      <AlternateForcedEntryClipId />
      <AlternateJackFromOutSideClipId>van_perp_ps_a</AlternateJackFromOutSideClipId>
      <AlternateBeJackedFromOutSideClipId>van_victim_ps_a</AlternateBeJackedFromOutSideClipId>
      <AlternateEntryPointClipSetId />
      <EnterVehicleMoveNetwork>ENTER_VEHICLE_STD</EnterVehicleMoveNetwork>
      <EntryTranslation x="1.400000" y="-0.500000" z="0.250000" />
      <OpenDoorTranslation x="0.000000" y="0.000000" />
      <OpenDoorHeadingChange value="-1.570000" />
      <EntryHeadingChange value="1.570000" />
      <ExtraZForMPPlaneWarp value="-2.000000" />
      <EntryPointFlags>JackIncludesGetIn UsesNoDoorTransitionForExit UseOpenDoorBlendAnims
        UseGetUpAfterJack JackVariationsIncludeGetIn DeadJackIncludesGetIn HasCombatEntry</EntryPointFlags>
      <EntryAnimVariations ref="ENTRY_ANIM_VARIATIONS_VAN_FRONT_RIGHT" />
      <NMJumpFromVehicleTuningSet />
    </Item>
    <Item type="CVehicleEntryPointAnimInfo">
      <Name>ENTRY_POINT_ANIM_ALEUTIAN_REAR_LEFT</Name>
      <CommonClipSetMap ref="COMMON_CLIPSET_MAP_AUTOMOBILE_LEFT" />
      <EntryClipSetMap ref="ENTRY_CLIPSET_MAP_GRANGER_REAR_LEFT" />
      <ExitClipSetMap ref="EXIT_CLIPSET_MAP_GRANGER_REAR_LEFT" />
      <AlternateTryLockedDoorClipId />
      <AlternateForcedEntryClipId />
      <AlternateJackFromOutSideClipId />
      <AlternateBeJackedFromOutSideClipId />
      <AlternateEntryPointClipSetId />
      <EnterVehicleMoveNetwork>ENTER_VEHICLE_STD</EnterVehicleMoveNetwork>
      <EntryTranslation x="-1.400000" y="-0.600000" z="0.250000" />
      <OpenDoorTranslation x="0.000000" y="0.000000" />
      <OpenDoorHeadingChange value="1.570000" />
      <EntryHeadingChange value="-1.570800" />
      <ExtraZForMPPlaneWarp value="-2.000000" />
      <EntryPointFlags>JackIncludesGetIn UsesNoDoorTransitionForExit UseOpenDoorBlendAnims
        UseGetUpAfterJack JackVariationsIncludeGetIn DeadJackIncludesGetIn</EntryPointFlags>
      <EntryAnimVariations ref="ENTRY_ANIM_VARIATIONS_VAN_FRONT_RIGHT" />
      <NMJumpFromVehicleTuningSet />
    </Item>
    <Item type="CVehicleEntryPointAnimInfo">
      <Name>ENTRY_POINT_ANIM_ALEUTIAN_REAR_RIGHT</Name>
      <CommonClipSetMap ref="COMMON_CLIPSET_MAP_AUTOMOBILE_RIGHT" />
      <EntryClipSetMap ref="ENTRY_CLIPSET_MAP_VAN_FRONT_RIGHT" />
      <ExitClipSetMap ref="EXIT_CLIPSET_MAP_VAN_FRONT_RIGHT" />
      <AlternateTryLockedDoorClipId />
      <AlternateForcedEntryClipId />
      <AlternateJackFromOutSideClipId>van_perp_ps_a</AlternateJackFromOutSideClipId>
      <AlternateBeJackedFromOutSideClipId>van_victim_ps_a</AlternateBeJackedFromOutSideClipId>
      <AlternateEntryPointClipSetId />
      <EnterVehicleMoveNetwork>ENTER_VEHICLE_STD</EnterVehicleMoveNetwork>
      <EntryTranslation x="1.400000" y="-0.600000" z="0.250000" />
      <OpenDoorTranslation x="0.000000" y="0.000000" />
      <OpenDoorHeadingChange value="-1.570000" />
      <EntryHeadingChange value="1.570000" />
      <ExtraZForMPPlaneWarp value="-2.000000" />
      <EntryPointFlags>JackIncludesGetIn UsesNoDoorTransitionForExit UseOpenDoorBlendAnims
        UseGetUpAfterJack JackVariationsIncludeGetIn DeadJackIncludesGetIn HasCombatEntry</EntryPointFlags>
      <EntryAnimVariations ref="ENTRY_ANIM_VARIATIONS_VAN_FRONT_RIGHT" />
      <NMJumpFromVehicleTuningSet />
    </Item>
    <Item type="CVehicleEntryPointAnimInfo">
      <Name>ENTRY_POINT_ANIM_DOMINATOR9_FRONT_LEFT</Name>
      <CommonClipSetMap ref="COMMON_CLIPSET_MAP_AUTOMOBILE_LEFT" />
      <EntryClipSetMap ref="ENTRY_CLIPSET_MAP_STD_FRONT_LEFT" />
      <ExitClipSetMap ref="EXIT_CLIPSET_MAP_STD_FRONT_LEFT" />
      <AlternateTryLockedDoorClipId>STD_Locked_DS</AlternateTryLockedDoorClipId>
      <AlternateForcedEntryClipId>STD_Force_Entry_DS</AlternateForcedEntryClipId>
      <AlternateJackFromOutSideClipId>std_perp_ds</AlternateJackFromOutSideClipId>
      <AlternateBeJackedFromOutSideClipId>std_victim_ds</AlternateBeJackedFromOutSideClipId>
      <AlternateEntryPointClipSetId>clipset@veh@std@ds@female@enter_exit</AlternateEntryPointClipSetId>
      <EnterVehicleMoveNetwork>ENTER_VEHICLE_STD</EnterVehicleMoveNetwork>
      <EntryTranslation x="-0.760000" y="-0.551000" z="0.500000" />
      <OpenDoorTranslation x="-0.050000" y="0.150000" />
      <OpenDoorHeadingChange value="1.570000" />
      <EntryHeadingChange value="-1.570000" />
      <ExtraZForMPPlaneWarp value="-2.000000" />
      <EntryPointFlags>JackIncludesGetIn UsesNoDoorTransitionForExit UseOpenDoorBlendAnims
        UseGetUpAfterJack JackVariationsIncludeGetIn HasCombatEntry</EntryPointFlags>
      <EntryAnimVariations ref="ENTRY_ANIM_VARIATIONS_STANDARD_FRONT_LEFT" />
      <NMJumpFromVehicleTuningSet>STANDARD</NMJumpFromVehicleTuningSet>
    </Item>
  </VehicleEntryPointAnimInfos>
  <VehicleExplosionInfos />
  <VehicleLayoutInfos>
    <Item type="CVehicleLayoutInfo">
      <Name>LAYOUT_STD_POLDOMINATOR2</Name>
      <Seats>
        <Item>
          <SeatInfo ref="SEAT_STANDARD_FRONT_LEFT" />
          <SeatAnimInfo ref="SEAT_ANIM_STANDARD_FRONT_LEFT" />
        </Item>
        <Item>
          <SeatInfo ref="SEAT_STANDARD_FRONT_RIGHT" />
          <SeatAnimInfo ref="SEAT_ANIM_STANDARD_FRONT_RIGHT" />
        </Item>
        <Item>
          <SeatInfo ref="SEAT_STANDARD_REAR_LEFT" />
          <SeatAnimInfo ref="SEAT_ANIM_STANDARD_REAR_LEFT" />
        </Item>
        <Item>
          <SeatInfo ref="SEAT_STANDARD_REAR_RIGHT" />
          <SeatAnimInfo ref="SEAT_ANIM_STANDARD_REAR_RIGHT" />
        </Item>
        <Item>
          <SeatInfo ref="SEAT_EXTRA_LEFT_2" />
          <SeatAnimInfo ref="SEAT_ANIM_STD_STRETCH_EXTRA_LEFT" />
        </Item>
      </Seats>
      <EntryPoints>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_STANDARD_FRONT_LEFT" />
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_DOMINATOR9_FRONT_LEFT" />
        </Item>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_STANDARD_FRONT_RIGHT" />
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_STANDARD_FRONT_RIGHT" />
        </Item>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_STANDARD_REAR_LEFT" />
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_STANDARD_REAR_LEFT" />
        </Item>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_STANDARD_REAR_RIGHT" />
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_STANDARD_REAR_RIGHT" />
        </Item>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_STD_STRETCH_MP_WARP_LEFT" />
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_STD_STRETCH_MP_WARP_LEFT" />
        </Item>
      </EntryPoints>
      <LayoutFlags>StreamAnims UseDoorOscillation UseLeanSteerAnims UseSteeringWheelIk</LayoutFlags>
      <BicycleInfo ref="NULL" />
      <AnimRateSet ref="STD_LOW_ANIM_RATE_SET" />
      <HandsUpClipSetId>busted_vehicle_std</HandsUpClipSetId>
      <SteeringWheelOffset x="0.000000" y="0.350000" z="0.320000" />
      <MaxXAcceleration value="4.000000" />
      <BodyLeanXApproachSpeed value="5.000000" />
      <BodyLeanXSmallDelta value="0.300000" />
      <LookBackApproachSpeedScale value="1.000000" />
      <FirstPersonAdditiveIdleClipSets>
        <Item>clipset@veh@std@ds@idle_a</Item>
        <Item>clipset@veh@std@ds@idle_b</Item>
        <Item>clipset@veh@std@ds@idle_c</Item>
        <Item>clipset@veh@std@ds@idle_d</Item>
        <Item>clipset@veh@std@ds@idle_e</Item>
      </FirstPersonAdditiveIdleClipSets>
      <FirstPersonRoadRageClipSets>
        <Item>clipset@veh@std@ds@hit_wheel@idle_a</Item>
        <Item>clipset@veh@std@ds@hit_wheel@idle_b</Item>
        <Item>clipset@veh@std@ds@hit_wheel@idle_c</Item>
      </FirstPersonRoadRageClipSets>
      <CellphoneClipsets>
        <CellphoneClipsetDS>ANIM_GROUP_CELLPHONE_IN_CAR_DS</CellphoneClipsetDS>
        <CellphoneClipsetPS>ANIM_GROUP_CELLPHONE_IN_CAR_PS</CellphoneClipsetPS>
        <CellphoneClipsetDS_FPS>ANIM_GROUP_CELLPHONE_IN_CAR_DS_FPS</CellphoneClipsetDS_FPS>
        <CellphoneClipsetPS_FPS>ANIM_GROUP_CELLPHONE_IN_CAR_DS_FPS</CellphoneClipsetPS_FPS>
      </CellphoneClipsets>
    </Item>
    <Item type="CVehicleLayoutInfo">
      <Name>LAYOUT_STD_ALEUTIAN</Name>
      <Seats>
        <Item>
          <SeatInfo ref="SEAT_STANDARD_FRONT_LEFT" />
          <SeatAnimInfo ref="SEAT_ANIM_RANGER_FRONT_LEFT" />
        </Item>
        <Item>
          <SeatInfo ref="SEAT_STANDARD_FRONT_RIGHT" />
          <SeatAnimInfo ref="SEAT_ANIM_RANGER_FRONT_RIGHT" />
        </Item>
        <Item>
          <SeatInfo ref="SEAT_STANDARD_REAR_LEFT" />
          <SeatAnimInfo ref="SEAT_ANIM_RANGER_REAR_LEFT" />
        </Item>
        <Item>
          <SeatInfo ref="SEAT_STANDARD_REAR_RIGHT" />
          <SeatAnimInfo ref="SEAT_ANIM_RANGER_REAR_RIGHT" />
        </Item>
      </Seats>
      <EntryPoints>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_STANDARD_FRONT_LEFT" />
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_ALEUTIAN_FRONT_LEFT" />
        </Item>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_STANDARD_FRONT_RIGHT" />
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_ALEUTIAN_FRONT_RIGHT" />
        </Item>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_STANDARD_REAR_LEFT" />
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_ALEUTIAN_REAR_LEFT" />
        </Item>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_STANDARD_REAR_RIGHT" />
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_ALEUTIAN_REAR_RIGHT" />
        </Item>
      </EntryPoints>
      <LayoutFlags>StreamAnims UseVanOpenDoorBlendParams UseDoorOscillation UseLeanSteerAnims
        UseLowerDoorBlockTest</LayoutFlags>
      <BicycleInfo ref="NULL" />
      <AnimRateSet ref="NULL" />
      <HandsUpClipSetId>busted_vehicle_std</HandsUpClipSetId>
      <SteeringWheelOffset x="0.000000" y="0.350000" z="0.320000" />
      <MaxXAcceleration value="4.000000" />
      <BodyLeanXApproachSpeed value="5.000000" />
      <BodyLeanXSmallDelta value="0.300000" />
      <LookBackApproachSpeedScale value="1.000000" />
      <FirstPersonAdditiveIdleClipSets>
        <Item>clipset@veh@van@ds@idle_a</Item>
        <Item>clipset@veh@van@ds@idle_b</Item>
        <Item>clipset@veh@van@ds@idle_c</Item>
        <Item>clipset@veh@van@ds@idle_d</Item>
        <Item>clipset@veh@van@ds@idle_e</Item>
      </FirstPersonAdditiveIdleClipSets>
      <FirstPersonRoadRageClipSets>
        <Item>clipset@veh@van@ds@hit_wheel@idle_a</Item>
        <Item>clipset@veh@van@ds@hit_wheel@idle_b</Item>
        <Item>clipset@veh@van@ds@hit_wheel@idle_c</Item>
      </FirstPersonRoadRageClipSets>
      <CellphoneClipsets>
        <CellphoneClipsetDS>ANIM_GROUP_CELLPHONE_IN_CAR_DS</CellphoneClipsetDS>
        <CellphoneClipsetPS>ANIM_GROUP_CELLPHONE_IN_CAR_PS</CellphoneClipsetPS>
        <CellphoneClipsetDS_FPS>ANIM_GROUP_CELLPHONE_IN_CAR_DS_FPS</CellphoneClipsetDS_FPS>
        <CellphoneClipsetPS_FPS>ANIM_GROUP_CELLPHONE_IN_CAR_DS_FPS</CellphoneClipsetPS_FPS>
      </CellphoneClipsets>
    </Item>
    <Item type="CVehicleLayoutInfo">
			<Name>LAYOUT_STD_POLICE</Name>
			<Seats>
				<Item>
					<SeatInfo ref="SEAT_STANDARD_FRONT_LEFT" />
					<SeatAnimInfo ref="SEAT_ANIM_STANDARD_FRONT_LEFT" />
				</Item>
				<Item>
					<SeatInfo ref="SEAT_STANDARD_FRONT_RIGHT" />
					<SeatAnimInfo ref="SEAT_ANIM_STD_ZTYPE_FRONT_RIGHT" />
				</Item>
				<Item>
					<SeatInfo ref="SEAT_STANDARD_REAR_LEFT" />
					<SeatAnimInfo ref="SEAT_ANIM_STANDARD_REAR_LEFT" />
				</Item>
				<Item>
					<SeatInfo ref="SEAT_STANDARD_REAR_RIGHT" />
					<SeatAnimInfo ref="SEAT_ANIM_STANDARD_REAR_RIGHT" />
				</Item>
				<Item>
					<SeatInfo ref="SEAT_EXTRA_LEFT_2" />
					<SeatAnimInfo ref="SEAT_ANIM_STD_STRETCH_EXTRA_LEFT" />
				</Item>
			</Seats>
			<EntryPoints>
				<Item>
					<EntryPointInfo ref="ENTRY_POINT_STANDARD_FRONT_LEFT" />
					<EntryPointAnimInfo ref="ENTRY_POINT_ANIM_STANDARD_FRONT_LEFT" />
				</Item>
				<Item>
					<EntryPointInfo ref="ENTRY_POINT_STANDARD_FRONT_RIGHT" />
					<EntryPointAnimInfo ref="ENTRY_POINT_ANIM_STANDARD_FRONT_RIGHT" />
				</Item>
				<Item>
					<EntryPointInfo ref="ENTRY_POINT_STANDARD_REAR_LEFT" />
					<EntryPointAnimInfo ref="ENTRY_POINT_ANIM_STANDARD_REAR_LEFT" />
				</Item>
				<Item>
					<EntryPointInfo ref="ENTRY_POINT_STANDARD_REAR_RIGHT" />
					<EntryPointAnimInfo ref="ENTRY_POINT_ANIM_STANDARD_REAR_RIGHT" />
				</Item>
				<Item>
					<EntryPointInfo ref="ENTRY_POINT_STD_STRETCH_MP_WARP_LEFT" />
					<EntryPointAnimInfo ref="ENTRY_POINT_ANIM_STD_STRETCH_MP_WARP_LEFT" />
				</Item>
			</EntryPoints>
			<LayoutFlags>StreamAnims UseDoorOscillation UseLeanSteerAnims UseSteeringWheelIk</LayoutFlags>
			<BicycleInfo ref="NULL" />
			<AnimRateSet ref="STD_LOW_ANIM_RATE_SET" />
			<HandsUpClipSetId>busted_vehicle_std</HandsUpClipSetId>
			<SteeringWheelOffset x="0.000000" y="0.350000" z="0.320000" />
			<MaxXAcceleration value="4.00000" />
			<BodyLeanXApproachSpeed value="5.00000" />
			<BodyLeanXSmallDelta value="0.30000" />
			<FirstPersonAdditiveIdleClipSets>
				<Item>clipset@veh@std@ds@idle_a</Item>
				<Item>clipset@veh@std@ds@idle_b</Item>
				<Item>clipset@veh@std@ds@idle_c</Item>
				<Item>clipset@veh@std@ds@idle_d</Item>
				<Item>clipset@veh@std@ds@idle_e</Item>
			</FirstPersonAdditiveIdleClipSets>
			<FirstPersonRoadRageClipSets>
				<Item>clipset@veh@std@ds@hit_wheel@idle_a</Item>
				<Item>clipset@veh@std@ds@hit_wheel@idle_b</Item>
				<Item>clipset@veh@std@ds@hit_wheel@idle_c</Item>
			</FirstPersonRoadRageClipSets>
		</Item>
		<Item type="CVehicleLayoutInfo">
			<Name>LAYOUT_STDPOL_EXITFIXUP</Name>
			<Seats>
				<Item>
					<SeatInfo ref="SEAT_STANDARD_FRONT_LEFT" />
					<SeatAnimInfo ref="SEAT_ANIM_STANDARD_FRONT_LEFT" />
				</Item>
				<Item>
					<SeatInfo ref="SEAT_STANDARD_FRONT_RIGHT" />
					<SeatAnimInfo ref="SEAT_ANIM_STANDARD_FRONT_RIGHT" />
				</Item>
				<Item>
					<SeatInfo ref="SEAT_STANDARD_REAR_LEFT" />
					<SeatAnimInfo ref="SEAT_ANIM_STANDARD_REAR_LEFT" />
				</Item>
				<Item>
					<SeatInfo ref="SEAT_STANDARD_REAR_RIGHT" />
					<SeatAnimInfo ref="SEAT_ANIM_STANDARD_REAR_RIGHT" />
				</Item>
				<Item>
					<SeatInfo ref="SEAT_EXTRA_LEFT_2" />
					<SeatAnimInfo ref="SEAT_ANIM_STD_STRETCH_EXTRA_LEFT" />
				</Item>
			</Seats>
			<EntryPoints>
				<Item>
					<EntryPointInfo ref="ENTRY_POINT_STANDARD_FRONT_LEFT" />
					<EntryPointAnimInfo ref="ENTRY_POINT_ANIM_STD_EXITFIXUP_FRONT_LEFT" />
				</Item>
				<Item>
					<EntryPointInfo ref="ENTRY_POINT_STANDARD_FRONT_RIGHT" />
					<EntryPointAnimInfo ref="ENTRY_POINT_ANIM_STD_EXITFIXUP_FRONT_RIGHT" />
				</Item>
				<Item>
					<EntryPointInfo ref="ENTRY_POINT_STANDARD_REAR_LEFT" />
					<EntryPointAnimInfo ref="ENTRY_POINT_ANIM_STD_EXITFIXUP_REAR_LEFT" />
				</Item>
				<Item>
					<EntryPointInfo ref="ENTRY_POINT_STANDARD_REAR_RIGHT" />
					<EntryPointAnimInfo ref="ENTRY_POINT_ANIM_STD_EXITFIXUP_REAR_RIGHT" />
				</Item>
				<Item>
					<EntryPointInfo ref="ENTRY_POINT_STD_STRETCH_MP_WARP_LEFT" />
					<EntryPointAnimInfo ref="ENTRY_POINT_ANIM_STD_STRETCH_MP_WARP_LEFT" />
				</Item>
			</EntryPoints>
			<LayoutFlags>StreamAnims UseDoorOscillation UseLeanSteerAnims UseSteeringWheelIk
				UseFinerAlignTolerance</LayoutFlags>
			<BicycleInfo ref="NULL" />
			<AnimRateSet ref="STD_LOW_ANIM_RATE_SET" />
			<HandsUpClipSetId>busted_vehicle_std</HandsUpClipSetId>
			<SteeringWheelOffset x="0.000000" y="0.350000" z="0.320000" />
			<MaxXAcceleration value="4.00000" />
			<BodyLeanXApproachSpeed value="5.00000" />
			<BodyLeanXSmallDelta value="0.30000" />
			<LookBackApproachSpeedScale value="1.00000" />
			<FirstPersonAdditiveIdleClipSets>
				<Item>clipset@veh@std@ds@idle_a</Item>
				<Item>clipset@veh@std@ds@idle_b</Item>
				<Item>clipset@veh@std@ds@idle_c</Item>
				<Item>clipset@veh@std@ds@idle_d</Item>
				<Item>clipset@veh@std@ds@idle_e</Item>
			</FirstPersonAdditiveIdleClipSets>
			<FirstPersonRoadRageClipSets>
				<Item>clipset@veh@std@ds@hit_wheel@idle_a</Item>
				<Item>clipset@veh@std@ds@hit_wheel@idle_b</Item>
				<Item>clipset@veh@std@ds@hit_wheel@idle_c</Item>
			</FirstPersonRoadRageClipSets>
		</Item>
		<Item type="CVehicleLayoutInfo">
			<Name>LAYOUT_POLICE_RANGER</Name>
			<Seats>
				<Item>
					<SeatInfo ref="SEAT_STANDARD_FRONT_LEFT" />
					<SeatAnimInfo ref="SEAT_ANIM_RANGER_FRONT_LEFT" />
				</Item>
				<Item>
					<SeatInfo ref="SEAT_STANDARD_FRONT_RIGHT" />
					<SeatAnimInfo ref="SEAT_ANIM_RANGER_FRONT_RIGHT" />
				</Item>
				<Item>
					<SeatInfo ref="SEAT_STANDARD_REAR_LEFT" />
					<SeatAnimInfo ref="SEAT_ANIM_RANGER_REAR_LEFT" />
				</Item>
				<Item>
					<SeatInfo ref="SEAT_STANDARD_REAR_RIGHT" />
					<SeatAnimInfo ref="SEAT_ANIM_RANGER_REAR_RIGHT" />
				</Item>
				<Item>
					<SeatInfo ref="SEAT_EXTRA_LEFT_2" />
					<SeatAnimInfo ref="SEAT_ANIM_STD_STRETCH_EXTRA_LEFT" />
				</Item>
			</Seats>
			<EntryPoints>
				<Item>
					<EntryPointInfo ref="ENTRY_POINT_STANDARD_FRONT_LEFT" />
					<EntryPointAnimInfo ref="ENTRY_POINT_ANIM_RANGER_FRONT_LEFT" />
				</Item>
				<Item>
					<EntryPointInfo ref="ENTRY_POINT_STANDARD_FRONT_RIGHT" />
					<EntryPointAnimInfo ref="ENTRY_POINT_ANIM_RANGER_FRONT_RIGHT" />
				</Item>
				<Item>
					<EntryPointInfo ref="ENTRY_POINT_STANDARD_REAR_LEFT" />
					<EntryPointAnimInfo ref="ENTRY_POINT_ANIM_RANGER_REAR_LEFT" />
				</Item>
				<Item>
					<EntryPointInfo ref="ENTRY_POINT_STANDARD_REAR_RIGHT" />
					<EntryPointAnimInfo ref="ENTRY_POINT_ANIM_RANGER_REAR_RIGHT" />
				</Item>
				<Item>
					<EntryPointInfo ref="ENTRY_POINT_STD_STRETCH_MP_WARP_LEFT" />
					<EntryPointAnimInfo ref="ENTRY_POINT_ANIM_STD_STRETCH_MP_WARP_LEFT" />
				</Item>
			</EntryPoints>
			<LayoutFlags>StreamAnims UseLeanSteerAnims UseVanOpenDoorBlendParams UseDoorOscillation
				UseLowerDoorBlockTest</LayoutFlags>
			<BicycleInfo ref="NULL" />
			<HandsUpClipSetId>busted_vehicle_std</HandsUpClipSetId>
			<SteeringWheelOffset x="0.000000" y="0.350000" z="0.320000" />
			<MaxXAcceleration value="4.00000" />
			<BodyLeanXApproachSpeed value="5.00000" />
			<BodyLeanXSmallDelta value="0.30000" />
			<FirstPersonAdditiveIdleClipSets>
				<Item>clipset@veh@van@ds@idle_a</Item>
				<Item>clipset@veh@van@ds@idle_b</Item>
				<Item>clipset@veh@van@ds@idle_c</Item>
				<Item>clipset@veh@van@ds@idle_d</Item>
				<Item>clipset@veh@van@ds@idle_e</Item>
			</FirstPersonAdditiveIdleClipSets>
			<FirstPersonRoadRageClipSets>
				<Item>clipset@veh@van@ds@hit_wheel@idle_a</Item>
				<Item>clipset@veh@van@ds@hit_wheel@idle_b</Item>
				<Item>clipset@veh@van@ds@hit_wheel@idle_c</Item>
			</FirstPersonRoadRageClipSets>
		</Item>
		<Item type="CVehicleLayoutInfo">
			<Name>LAYOUT_HELI_POLCONADA</Name>
			<Seats>
				<Item>
					<SeatInfo ref="SEAT_STANDARD_FRONT_LEFT" />
					<SeatAnimInfo ref="SEAT_ANIM_HELI_FRONT_LEFT" />
				</Item>
				<Item>
					<SeatInfo ref="SEAT_STANDARD_FRONT_RIGHT" />
					<SeatAnimInfo ref="SEAT_ANIM_HELI_FRONT_RIGHT" />
				</Item>
				<Item>
					<SeatInfo ref="SEAT_STANDARD_REAR_LEFT" />
					<SeatAnimInfo ref="SEAT_ANIM_HELI_SWIFT_REAR_LEFT" />
				</Item>
				<Item>
					<SeatInfo ref="SEAT_STANDARD_REAR_RIGHT" />
					<SeatAnimInfo ref="SEAT_ANIM_HELI_SWIFT_REAR_RIGHT" />
				</Item>
				<Item>
					<SeatInfo ref="SEAT_EXTRA_NO_SHUFFLE_LEFT_1" />
					<SeatAnimInfo ref="SEAT_ANIM_HELI_SWIFT_REAR_LEFT" />
				</Item>
				<Item>
					<SeatInfo ref="SEAT_EXTRA_NO_SHUFFLE_RIGHT_1" />
					<SeatAnimInfo ref="SEAT_ANIM_HELI_SWIFT_REAR_RIGHT" />
				</Item>
				<Item>
					<SeatInfo ref="SEAT_EXTRA_NO_SHUFFLE_LEFT_2" />
					<SeatAnimInfo ref="SEAT_ANIM_HELI_SWIFT_REAR_LEFT" />
				</Item>
				<Item>
					<SeatInfo ref="SEAT_EXTRA_NO_SHUFFLE_RIGHT_2" />
					<SeatAnimInfo ref="SEAT_ANIM_HELI_SWIFT_REAR_RIGHT" />
				</Item>
			</Seats>
			<EntryPoints>
				<!-- <Item>
				<EntryPointInfo ref="ENTRY_POINT_STANDARD_FRONT_LEFT" />
				<EntryPointAnimInfo ref="ENTRY_POINT_ANIM_HELI_SWIFT_FRONT_LEFT" />
			  </Item>
			  <Item>
				<EntryPointInfo ref="ENTRY_POINT_STANDARD_FRONT_RIGHT" />
				<EntryPointAnimInfo ref="ENTRY_POINT_ANIM_HELI_SWIFT_FRONT_RIGHT" />
			  </Item> -->
				<Item>
					<EntryPointInfo ref="ENTRY_POINT_HELI_SPARROW_FRONT_LEFT" />
					<EntryPointAnimInfo ref="ENTRY_POINT_ANIM_HELI_SPARROW_FRONT_LEFT" />
				</Item>
				<Item>
					<EntryPointInfo ref="ENTRY_POINT_HELI_SPARROW_FRONT_RIGHT" />
					<EntryPointAnimInfo ref="ENTRY_POINT_ANIM_HELI_SPARROW_FRONT_RIGHT" />
				</Item>
				<Item>
					<EntryPointInfo ref="ENTRY_POINT_STANDARD_REAR_LEFT" />
					<EntryPointAnimInfo ref="ENTRY_POINT_ANIM_HELI_SWIFT_REAR_LEFT" />
				</Item>
				<Item>
					<EntryPointInfo ref="ENTRY_POINT_STANDARD_REAR_RIGHT" />
					<EntryPointAnimInfo ref="ENTRY_POINT_ANIM_HELI_SWIFT_REAR_RIGHT" />
				</Item>
				<Item>
					<EntryPointInfo ref="ENTRY_POINT_MP_HELI_CARGOBOB_WARP_REAR_LEFT" />
					<EntryPointAnimInfo ref="ENTRY_POINT_ANIM_HELI_CARGOBOB_REAR_LEFT" />
				</Item>
				<Item>
					<EntryPointInfo ref="ENTRY_POINT_MP_HELI_CARGOBOB_WARP_REAR_RIGHT" />
					<EntryPointAnimInfo ref="ENTRY_POINT_ANIM_HELI_CARGOBOB_REAR_RIGHT" />
				</Item>
			</EntryPoints>
			<LayoutFlags>StreamAnims UseFinerAlignTolerance DisableJackingAndBusting Use2DBodyBlend</LayoutFlags>
			<BicycleInfo ref="NULL" />
			<HandsUpClipSetId>busted_vehicle_std</HandsUpClipSetId>
			<SteeringWheelOffset x="0.000000" y="0.350000" z="0.320000" />
		</Item>
    <Item type="CVehicleLayoutInfo">
      <Name>LAYOUT_JMBEARCAT</Name>
      <Seats>
        <Item>
          <SeatInfo ref="SEAT_JMBEARCAT_FRONT_LEFT" />
          <SeatAnimInfo ref="SEAT_ANIM_JMBEARCAT_FRONT_LEFT" />
        </Item>
        <Item>
          <SeatInfo ref="SEAT_JMBEARCAT_FRONT_RIGHT" />
          <SeatAnimInfo ref="SEAT_ANIM_JMBEARCAT_FRONT_RIGHT" />
        </Item>
        <Item>
          <SeatInfo ref="SEAT_JMBEARCAT_JUMP_LEFT" />
          <SeatAnimInfo ref="SEAT_ANIM_JMBEARCAT_JUMP_RIGHT" />
        </Item>
        <Item>
          <SeatInfo ref="SEAT_JMBEARCAT_JUMP_RIGHT" />
          <SeatAnimInfo ref="SEAT_ANIM_JMBEARCAT_JUMP_RIGHT" />
        </Item>
        <Item>
          <SeatInfo ref="SEAT_JMBEARCAT_REAR_LEFT" />
          <SeatAnimInfo ref="SEAT_ANIM_JMBEARCAT_REAR_LEFT" /> <!--GUNPORT-->
        </Item>
        <Item>
          <SeatInfo ref="SEAT_JMBEARCAT_REAR_RIGHT" />
          <SeatAnimInfo ref="SEAT_ANIM_JMBEARCAT_REAR_RIGHT" /> <!--GUNPORT-->
        </Item>
        <Item>
          <SeatInfo ref="SEAT_JMBEARCAT_WARP_REAR_LEFT" />
          <SeatAnimInfo ref="SEAT_ANIM_JMBEARCAT_JUMP_LEFT" /> <!--GUNPORT
          TBA-->
        </Item>
        <Item>
          <SeatInfo ref="SEAT_JMBEARCAT_WARP_REAR_RIGHT" />
          <SeatAnimInfo ref="SEAT_ANIM_JMBEARCAT_JUMP_RIGHT" /> <!--GUNPORT
          TBA-->
        </Item>
        <Item>
          <SeatInfo ref="SEAT_JMBEARCAT_STANDING_LEFT" />
          <SeatAnimInfo ref="SEAT_ANIM_SWAT_VAN_SIDE_LEFT" />
        </Item>
        <Item>
          <SeatInfo ref="SEAT_JMBEARCAT_STANDING_RIGHT" />
          <SeatAnimInfo ref="SEAT_ANIM_SWAT_VAN_SIDE_RIGHT" />
        </Item>
        <Item>
          <SeatInfo ref="SEAT_JMBEARCAT_STANDING_LEFT_EXTRA" />
          <SeatAnimInfo ref="SEAT_ANIM_SWAT_VAN_SIDE_LEFT" />
        </Item>
        <Item>
          <SeatInfo ref="SEAT_JMBEARCAT_STANDING_RIGHT_EXTRA" />
          <SeatAnimInfo ref="SEAT_ANIM_SWAT_VAN_SIDE_RIGHT" />
        </Item>
        <Item>
          <SeatInfo ref="SEAT_JMBEARCAT_STANDING_REAR_LEFT" />
          <SeatAnimInfo ref="SEAT_ANIM_FIRETRUCK_STANDING_RIGHT" /> <!--FIRE-->
        </Item>
        <Item>
          <SeatInfo ref="SEAT_JMBEARCAT_STANDING_REAR_RIGHT" />
          <SeatAnimInfo ref="SEAT_ANIM_FIRETRUCK_STANDING_LEFT" /> <!--FIRE-->
        </Item>
      </Seats>
      <EntryPoints>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_JMBEARCAT_FRONT_LEFT" />
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_TRUCK_SANDKING_FRONT_LEFT" /> <!--SANDKING-->
        </Item>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_JMBEARCAT_FRONT_RIGHT" />
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_TRUCK_SANDKING_FRONT_RIGHT" /> <!--SANDKING-->
        </Item>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_JMBEARCAT_JUMP_LEFT" />
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_VAN_REAR_LEFT" /> <!--VAN-->
        </Item>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_JMBEARCAT_JUMP_RIGHT" />
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_VAN_REAR_RIGHT" /> <!--VAN-->
        </Item>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_JMBEARCAT_REAR_LEFT" />
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_RIOT_VAN_REAR_LEFT" /> <!--RIOT-->
        </Item>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_JMBEARCAT_REAR_RIGHT" />
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_RIOT_VAN_REAR_RIGHT" /> <!--RIOT-->
        </Item>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_JMBEARCAT_WARP_REAR_LEFT" />
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_RIOT_VAN_REAR_LEFT" /> <!--RIOT-->
        </Item>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_JMBEARCAT_WARP_REAR_RIGHT" />
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_RIOT_VAN_REAR_RIGHT" /> <!--RIOT-->
        </Item>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_JMBEARCAT_STANDING_LEFT" />
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_VAN_SIDE_LEFT" /> <!--RIOT/VAN-->
        </Item>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_JMBEARCAT_STANDING_RIGHT" />
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_VAN_SIDE_RIGHT" /> <!--RIOT/VAN-->
        </Item>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_JMBEARCAT_STANDING_LEFT_EXTRA" />
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_VAN_SIDE_LEFT" /> <!--RIOT/VAN-->
        </Item>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_JMBEARCAT_STANDING_RIGHT_EXTRA" />
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_VAN_SIDE_RIGHT" /> <!--RIOT/VAN-->
        </Item>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_JMBEARCAT_STANDING_REAR_LEFT" />
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_FIRETRUCK_STANDING_LEFT" /> <!--FIRE-->
        </Item>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_JMBEARCAT_STANDING_REAR_RIGHT" />
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_FIRETRUCK_STANDING_RIGHT" /> <!--FIRE-->
        </Item>
      </EntryPoints>
      <LayoutFlags>StreamAnims OnlyExitIfDoorIsClosed DisableJackingAndBusting
        UseVanOpenDoorBlendParams UseDoorOscillation UseLeanSteerAnims UseLowerDoorBlockTest</LayoutFlags>
      <BicycleInfo ref="NULL" />
      <AnimRateSet ref="NULL" />
      <HandsUpClipSetId>busted_vehicle_std</HandsUpClipSetId>
      <SteeringWheelOffset x="0.000000" y="0.350000" z="0.320000" />
      <MaxXAcceleration value="6.000000" />
      <BodyLeanXApproachSpeed value="6.000000" />
      <BodyLeanXSmallDelta value="0.100000" />
      <LookBackApproachSpeedScale value="1.000000" />
      <FirstPersonAdditiveIdleClipSets>
        <Item>clipset@veh@van@ds@idle_a</Item>
        <Item>clipset@veh@van@ds@idle_b</Item>
        <Item>clipset@veh@van@ds@idle_c</Item>
        <Item>clipset@veh@van@ds@idle_d</Item>
        <Item>clipset@veh@van@ds@idle_e</Item>
      </FirstPersonAdditiveIdleClipSets>
      <FirstPersonRoadRageClipSets>
        <Item>clipset@veh@van@ds@hit_wheel@idle_a</Item>
        <Item>clipset@veh@van@ds@hit_wheel@idle_b</Item>
        <Item>clipset@veh@van@ds@hit_wheel@idle_c</Item>
      </FirstPersonRoadRageClipSets>
      <CellphoneClipsets>
        <CellphoneClipsetDS>ANIM_GROUP_CELLPHONE_IN_CAR_DS</CellphoneClipsetDS>
        <CellphoneClipsetPS>ANIM_GROUP_CELLPHONE_IN_CAR_PS</CellphoneClipsetPS>
        <CellphoneClipsetDS_FPS>ANIM_GROUP_CELLPHONE_IN_CAR_DS_FPS</CellphoneClipsetDS_FPS>
        <CellphoneClipsetPS_FPS>ANIM_GROUP_CELLPHONE_IN_CAR_DS_FPS</CellphoneClipsetPS_FPS>
      </CellphoneClipsets>
    </Item>
  </VehicleLayoutInfos>
  <VehicleScenarioLayoutInfos />
  <SeatOverrideAnimInfos />
  <InVehicleOverrideInfos />
  <FirstPersonDriveByLookAroundData>
    <Item type="CFirstPersonDriveByLookAroundData">
      <Name>LOW_DOMINATOR9_FRONT_LEFT</Name>
      <AllowLookback value="true" />
      <HeadingLimits x="-190.000000" y="160.000000" />
      <DataLeft>
        <Offsets>
          <Item>
            <Offset value="-0.320000" />
            <AngleToBlendInOffset x="45.000000" y="120.000000" />
          </Item>
          <Item>
            <Offset value="0.050000" />
            <AngleToBlendInOffset x="45.000000" y="120.000000" />
          </Item>
          <Item>
            <Offset value="0.005000" />
            <AngleToBlendInOffset x="45.000000" y="120.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-10.000000" y="0.000000" />
        <AngleToBlendInExtraPitch x="45.000000" y="110.000000" />
      </DataLeft>
      <DataRight>
        <Offsets>
          <Item>
            <Offset value="-0.010000" />
            <AngleToBlendInOffset x="45.000000" y="90.000000" />
          </Item>
          <Item>
            <Offset value="-0.100000" />
            <AngleToBlendInOffset x="80.000000" y="165.000000" />
          </Item>
          <Item>
            <Offset value="0.075000" />
            <AngleToBlendInOffset x="120.000000" y="160.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-4.000000" y="-13.000000" />
        <AngleToBlendInExtraPitch x="0.000000" y="100.000000" />
      </DataRight>
      <WheelClipInfo>
        <HeadingLimitsLeft x="0.380000" y="0.430000" />
        <HeadingLimitsRight x="0.480000" y="0.530000" />
        <PitchLimits x="0.470000" y="0.530000" />
        <PitchOffset x="0.000000" y="0.050000" />
        <WheelAngleLimits x="-0.700000" y="0.000000" />
        <WheelAngleOffset x="0.010000" y="0.040000" />
        <MaxWheelOffsetY value="0.170000" />
        <WheelClipLerpInRate value="0.150000" />
        <WheelClipLerpOutRate value="0.180000" />
      </WheelClipInfo>
    </Item>
    <Item type="CFirstPersonDriveByLookAroundData">
      <Name>LOW_DOMINATOR9_FRONT_RIGHT</Name>
      <AllowLookback value="true" />
      <HeadingLimits x="-190.000000" y="160.000000" />
      <DataLeft>
        <Offsets>
          <Item>
            <Offset value="0.080000" />
            <AngleToBlendInOffset x="45.000000" y="90.000000" />
          </Item>
          <Item>
            <Offset value="-0.100000" />
            <AngleToBlendInOffset x="80.000000" y="165.000000" />
          </Item>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="0.000000" y="0.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="0.000000" y="-10.500000" />
        <AngleToBlendInExtraPitch x="0.000000" y="57.000000" />
      </DataLeft>
      <DataRight>
        <Offsets>
          <Item>
            <Offset value="0.390000" />
            <AngleToBlendInOffset x="45.000000" y="90.000000" />
          </Item>
          <Item>
            <Offset value="-0.220000" />
            <AngleToBlendInOffset x="45.000000" y="90.000000" />
          </Item>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="45.000000" y="120.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-10.000000" y="0.000000" />
        <AngleToBlendInExtraPitch x="45.000000" y="110.000000" />
      </DataRight>
      <WheelClipInfo>
        <HeadingLimitsLeft x="0.380000" y="0.430000" />
        <HeadingLimitsRight x="0.480000" y="0.530000" />
        <PitchLimits x="0.470000" y="0.530000" />
        <PitchOffset x="0.000000" y="0.050000" />
        <WheelAngleLimits x="-0.700000" y="0.000000" />
        <WheelAngleOffset x="0.010000" y="0.040000" />
        <MaxWheelOffsetY value="0.170000" />
        <WheelClipLerpInRate value="0.150000" />
        <WheelClipLerpOutRate value="0.180000" />
      </WheelClipInfo>
    </Item>
    <Item type="CFirstPersonDriveByLookAroundData">
      <Name>STD_ALEUTIAN_FRONT_LEFT</Name>
      <AllowLookback value="true" />
      <HeadingLimits x="-183.000000" y="173.000000" />
      <DataLeft>
        <Offsets>
          <Item>
            <Offset value="-0.480000" />
            <AngleToBlendInOffset x="20.000000" y="120.000000" />
          </Item>
          <Item>
            <Offset value="0.050000" />
            <AngleToBlendInOffset x="20.000000" y="120.000000" />
          </Item>
          <Item>
            <Offset value="0.030000" />
            <AngleToBlendInOffset x="5.000000" y="50.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-10.000000" y="2.000000" />
        <AngleToBlendInExtraPitch x="5.000000" y="110.000000" />
      </DataLeft>
      <DataRight>
        <Offsets>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="0.000000" y="0.000000" />
          </Item>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="0.000000" y="0.000000" />
          </Item>
          <Item>
            <Offset value="0.050000" />
            <AngleToBlendInOffset x="5.000000" y="50.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-3.000000" y="-13.000000" />
        <AngleToBlendInExtraPitch x="0.000000" y="70.000000" />
      </DataRight>
      <WheelClipInfo>
        <HeadingLimitsLeft x="0.380000" y="0.430000" />
        <HeadingLimitsRight x="0.480000" y="0.530000" />
        <PitchLimits x="0.470000" y="0.530000" />
        <PitchOffset x="0.000000" y="0.080000" />
        <WheelAngleLimits x="-0.700000" y="0.000000" />
        <WheelAngleOffset x="0.010000" y="0.020000" />
        <MaxWheelOffsetY value="0.170000" />
        <WheelClipLerpInRate value="0.150000" />
        <WheelClipLerpOutRate value="0.180000" />
      </WheelClipInfo>
    </Item>
    <Item type="CFirstPersonDriveByLookAroundData">
      <Name>STD_ALEUTIAN_FRONT_RIGHT</Name>
      <AllowLookback value="true" />
      <HeadingLimits x="-183.000000" y="165.000000" />
      <DataLeft>
        <Offsets>
          <Item>
            <Offset value="0.100000" />
            <AngleToBlendInOffset x="20.000000" y="120.000000" />
          </Item>
          <Item>
            <Offset value="0.050000" />
            <AngleToBlendInOffset x="60.000000" y="120.000000" />
          </Item>
          <Item>
            <Offset value="-0.035000" />
            <AngleToBlendInOffset x="5.000000" y="50.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-1.000000" y="-11.000000" />
        <AngleToBlendInExtraPitch x="5.000000" y="110.000000" />
      </DataLeft>
      <DataRight>
        <Offsets>
          <Item>
            <Offset value="0.470000" />
            <AngleToBlendInOffset x="40.000000" y="160.000000" />
          </Item>
          <Item>
            <Offset value="0.100000" />
            <AngleToBlendInOffset x="40.000000" y="160.000000" />
          </Item>
          <Item>
            <Offset value="-0.050000" />
            <AngleToBlendInOffset x="10.000000" y="50.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-5.000000" y="3.000000" />
        <AngleToBlendInExtraPitch x="0.000000" y="70.000000" />
      </DataRight>
      <WheelClipInfo>
        <HeadingLimitsLeft x="0.380000" y="0.430000" />
        <HeadingLimitsRight x="0.480000" y="0.530000" />
        <PitchLimits x="0.470000" y="0.530000" />
        <PitchOffset x="0.000000" y="0.050000" />
        <WheelAngleLimits x="-0.700000" y="0.000000" />
        <WheelAngleOffset x="0.010000" y="0.040000" />
        <MaxWheelOffsetY value="0.170000" />
        <WheelClipLerpInRate value="0.150000" />
        <WheelClipLerpOutRate value="0.180000" />
      </WheelClipInfo>
    </Item>
    <Item type="CFirstPersonDriveByLookAroundData">
      <Name>STD_ALEUTIAN_REAR_LEFT</Name>
      <AllowLookback value="true" />
      <HeadingLimits x="-185.000000" y="160.000000" />
      <DataLeft>
        <Offsets>
          <Item>
            <Offset value="-0.475000" />
            <AngleToBlendInOffset x="40.000000" y="120.000000" />
          </Item>
          <Item>
            <Offset value="0.200000" />
            <AngleToBlendInOffset x="0.000000" y="120.000000" />
          </Item>
          <Item>
            <Offset value="-0.000000" />
            <AngleToBlendInOffset x="0.000000" y="0.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-10.000000" y="10.000000" />
        <AngleToBlendInExtraPitch x="5.000000" y="70.000000" />
      </DataLeft>
      <DataRight>
        <Offsets>
          <Item>
            <Offset value="-0.200000" />
            <AngleToBlendInOffset x="0.000000" y="120.000000" />
          </Item>
          <Item>
            <Offset value="-0.070000" />
            <AngleToBlendInOffset x="20.000000" y="120.000000" />
          </Item>
          <Item>
            <Offset value="0.125000" />
            <AngleToBlendInOffset x="20.000000" y="90.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-8.500000" y="-12.000000" />
        <AngleToBlendInExtraPitch x="0.000000" y="70.000000" />
      </DataRight>
      <WheelClipInfo>
        <HeadingLimitsLeft x="0.380000" y="0.430000" />
        <HeadingLimitsRight x="0.480000" y="0.530000" />
        <PitchLimits x="0.470000" y="0.530000" />
        <PitchOffset x="0.000000" y="0.050000" />
        <WheelAngleLimits x="-0.700000" y="0.000000" />
        <WheelAngleOffset x="0.010000" y="0.040000" />
        <MaxWheelOffsetY value="0.170000" />
        <WheelClipLerpInRate value="0.150000" />
        <WheelClipLerpOutRate value="0.180000" />
      </WheelClipInfo>
    </Item>
    <Item type="CFirstPersonDriveByLookAroundData">
      <Name>STD_ALEUTIAN_REAR_RIGHT</Name>
      <AllowLookback value="true" />
      <HeadingLimits x="-185.000000" y="160.000000" />
      <DataLeft>
        <Offsets>
          <Item>
            <Offset value="0.200000" />
            <AngleToBlendInOffset x="0.000000" y="120.000000" />
          </Item>
          <Item>
            <Offset value="-0.070000" />
            <AngleToBlendInOffset x="20.000000" y="120.000000" />
          </Item>
          <Item>
            <Offset value="0.085000" />
            <AngleToBlendInOffset x="20.000000" y="90.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-7.000000" y="-10.000000" />
        <AngleToBlendInExtraPitch x="0.000000" y="70.000000" />
      </DataLeft>
      <DataRight>
        <Offsets>
          <Item>
            <Offset value="0.475000" />
            <AngleToBlendInOffset x="40.000000" y="120.000000" />
          </Item>
          <Item>
            <Offset value="0.100000" />
            <AngleToBlendInOffset x="40.000000" y="120.000000" />
          </Item>
          <Item>
            <Offset value="-0.000000" />
            <AngleToBlendInOffset x="0.000000" y="0.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-10.000000" y="5.000000" />
        <AngleToBlendInExtraPitch x="5.000000" y="70.000000" />
      </DataRight>
      <WheelClipInfo>
        <HeadingLimitsLeft x="0.380000" y="0.430000" />
        <HeadingLimitsRight x="0.480000" y="0.530000" />
        <PitchLimits x="0.470000" y="0.530000" />
        <PitchOffset x="0.000000" y="0.050000" />
        <WheelAngleLimits x="-0.700000" y="0.000000" />
        <WheelAngleOffset x="0.010000" y="0.040000" />
        <MaxWheelOffsetY value="0.170000" />
        <WheelClipLerpInRate value="0.150000" />
        <WheelClipLerpOutRate value="0.180000" />
      </WheelClipInfo>
    </Item>
    <Item type="CFirstPersonDriveByLookAroundData">
      <Name>STD_POLDORADO_FRONT_LEFT</Name>
      <AllowLookback value="true" />
      <HeadingLimits x="-95.000000" y="173.000000" />
      <DataLeft>
        <Offsets>
          <Item>
            <Offset value="-0.415000" />
            <AngleToBlendInOffset x="40.000000" y="120.000000" />
          </Item>
          <Item>
            <Offset value="0.150000" />
            <AngleToBlendInOffset x="50.000000" y="120.000000" />
          </Item>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="0.000000" y="0.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-17.000000" y="10.000000" />
        <AngleToBlendInExtraPitch x="5.000000" y="110.000000" />
      </DataLeft>
      <DataRight>
        <Offsets>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="0.000000" y="0.000000" />
          </Item>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="0.000000" y="0.000000" />
          </Item>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="0.000000" y="0.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="0.300000" y="-8.500000" />
        <AngleToBlendInExtraPitch x="0.000000" y="70.000000" />
      </DataRight>
      <WheelClipInfo>
        <HeadingLimitsLeft x="0.380000" y="0.430000" />
        <HeadingLimitsRight x="0.480000" y="0.530000" />
        <PitchLimits x="0.470000" y="0.530000" />
        <PitchOffset x="0.000000" y="0.010000" />
        <WheelAngleLimits x="-0.700000" y="0.000000" />
        <WheelAngleOffset x="0.010000" y="0.020000" />
        <MaxWheelOffsetY value="0.170000" />
        <WheelClipLerpInRate value="0.150000" />
        <WheelClipLerpOutRate value="0.180000" />
      </WheelClipInfo>
    </Item>
    <Item type="CFirstPersonDriveByLookAroundData">
      <Name>STD_POLDORADO_FRONT_RIGHT</Name>
      <AllowLookback value="true" />
      <HeadingLimits x="-95.000000" y="165.000000" />
      <DataLeft>
        <Offsets>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="0.000000" y="79.000000" />
          </Item>
          <Item>
            <Offset value="0.090000" />
            <AngleToBlendInOffset x="0.000000" y="79.000000" />
          </Item>
          <Item>
            <Offset value="-0.000000" />
            <AngleToBlendInOffset x="0.000000" y="0.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="0.300000" y="-8.500000" />
        <AngleToBlendInExtraPitch x="0.000000" y="70.000000" />
      </DataLeft>
      <DataRight>
        <Offsets>
          <Item>
            <Offset value="0.350000" />
            <AngleToBlendInOffset x="60.000000" y="120.000000" />
          </Item>
          <Item>
            <Offset value="0.150000" />
            <AngleToBlendInOffset x="60.000000" y="120.000000" />
          </Item>
          <Item>
            <Offset value="0.030000" />
            <AngleToBlendInOffset x="5.000000" y="60.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-20.000000" y="5.000000" />
        <AngleToBlendInExtraPitch x="5.000000" y="90.000000" />
      </DataRight>
      <WheelClipInfo>
        <HeadingLimitsLeft x="0.380000" y="0.430000" />
        <HeadingLimitsRight x="0.480000" y="0.530000" />
        <PitchLimits x="0.470000" y="0.530000" />
        <PitchOffset x="0.000000" y="0.050000" />
        <WheelAngleLimits x="-0.700000" y="0.000000" />
        <WheelAngleOffset x="0.010000" y="0.040000" />
        <MaxWheelOffsetY value="0.170000" />
        <WheelClipLerpInRate value="0.150000" />
        <WheelClipLerpOutRate value="0.180000" />
      </WheelClipInfo>
    </Item>
    <Item type="CFirstPersonDriveByLookAroundData">
      <Name>STD_POLDORADO_REAR_LEFT</Name>
      <AllowLookback value="true" />
      <HeadingLimits x="-190.000000" y="140.000000" />
      <DataLeft>
        <Offsets>
          <Item>
            <Offset value="-0.250000" />
            <AngleToBlendInOffset x="25.000000" y="140.000000" />
          </Item>
          <Item>
            <Offset value="0.400000" />
            <AngleToBlendInOffset x="0.000000" y="120.000000" />
          </Item>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="0.000000" y="140.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-10.000000" y="10.000000" />
        <AngleToBlendInExtraPitch x="20.000000" y="100.000000" />
      </DataLeft>
      <DataRight>
        <Offsets>
          <Item>
            <Offset value="-0.140000" />
            <AngleToBlendInOffset x="0.000000" y="190.000000" />
          </Item>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="0.000000" y="190.000000" />
          </Item>
          <Item>
            <Offset value="0.050000" />
            <AngleToBlendInOffset x="0.000000" y="190.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-7.500000" y="-9.000000" />
        <AngleToBlendInExtraPitch x="0.000000" y="190.000000" />
      </DataRight>
      <WheelClipInfo>
        <HeadingLimitsLeft x="0.380000" y="0.430000" />
        <HeadingLimitsRight x="0.480000" y="0.530000" />
        <PitchLimits x="0.470000" y="0.530000" />
        <PitchOffset x="0.000000" y="0.050000" />
        <WheelAngleLimits x="-0.700000" y="0.000000" />
        <WheelAngleOffset x="0.010000" y="0.040000" />
        <MaxWheelOffsetY value="0.170000" />
        <WheelClipLerpInRate value="0.150000" />
        <WheelClipLerpOutRate value="0.180000" />
      </WheelClipInfo>
    </Item>
    <Item type="CFirstPersonDriveByLookAroundData">
      <Name>STD_POLDORADO_REAR_RIGHT</Name>
      <AllowLookback value="true" />
      <HeadingLimits x="-190.000000" y="140.000000" />
      <DataLeft>
        <Offsets>
          <Item>
            <Offset value="0.140000" />
            <AngleToBlendInOffset x="0.000000" y="190.000000" />
          </Item>
          <Item>
            <Offset value="-0.050000" />
            <AngleToBlendInOffset x="0.000000" y="190.000000" />
          </Item>
          <Item>
            <Offset value="0.050000" />
            <AngleToBlendInOffset x="0.000000" y="190.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-8.000000" y="-8.500000" />
        <AngleToBlendInExtraPitch x="0.000000" y="190.000000" />
      </DataLeft>
      <DataRight>
        <Offsets>
          <Item>
            <Offset value="0.250000" />
            <AngleToBlendInOffset x="25.000000" y="140.000000" />
          </Item>
          <Item>
            <Offset value="0.400000" />
            <AngleToBlendInOffset x="0.000000" y="120.000000" />
          </Item>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="0.000000" y="140.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-10.000000" y="10.000000" />
        <AngleToBlendInExtraPitch x="20.000000" y="100.000000" />
      </DataRight>
      <WheelClipInfo>
        <HeadingLimitsLeft x="0.380000" y="0.430000" />
        <HeadingLimitsRight x="0.480000" y="0.530000" />
        <PitchLimits x="0.470000" y="0.530000" />
        <PitchOffset x="0.000000" y="0.050000" />
        <WheelAngleLimits x="-0.700000" y="0.000000" />
        <WheelAngleOffset x="0.010000" y="0.040000" />
        <MaxWheelOffsetY value="0.170000" />
        <WheelClipLerpInRate value="0.150000" />
        <WheelClipLerpOutRate value="0.180000" />
      </WheelClipInfo>
    </Item>
  </FirstPersonDriveByLookAroundData>
</CVehicleMetadataMgr>