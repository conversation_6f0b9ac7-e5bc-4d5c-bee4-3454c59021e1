<?xml version="1.0" encoding="UTF-8"?>

<CVehicleMetadataMgr>
  <VehicleSeatInfos>
    <Item type="CVehicleSeatInfo">
      <Name>SEAT_VAN_RUMPOESC_REAR_LEFT</Name>
      <SeatBoneName>seat_dside_r1</SeatBoneName>
      <ShuffleLink>SEAT_VAN_RUMPOESC_REAR_RIGHT</ShuffleLink>
      <RearSeatLink />
      <DefaultCarTask>TASK_DRIVE_WANDER</DefaultCarTask>
      <SeatFlags>IsIdleSeat</SeatFlags>
      <HairScale value="0.000000" />
    </Item>
    <Item type="CVehicleSeatInfo">
      <Name>SEAT_VAN_RUMPOESC_REAR_RIGHT</Name>
      <SeatBoneName>seat_pside_r1</SeatBoneName>
      <ShuffleLink>SEAT_VAN_RUMPOESC_REAR_LEFT</ShuffleLink>
      <RearSeatLink />
      <DefaultCarTask>TASK_DRIVE_WANDER</DefaultCarTask>
      <SeatFlags>IsIdleSeat</SeatFlags>
      <HairScale value="0.000000" />
    </Item>
  </VehicleSeatInfos>
  <VehicleEntryPointInfos>
    <Item type="CVehicleEntryPointInfo">
      <Name>ENTRY_POINT_VAN_RUMPOESC_REAR_LEFT</Name>
      <DoorBoneName>boot</DoorBoneName>
      <SecondDoorBoneName />
      <DoorHandleBoneName>handle_boot</DoorHandleBoneName>
      <WindowId>REAR_LEFT</WindowId>
      <VehicleSide>SIDE_REAR</VehicleSide>
      <AccessableSeats>
        <Item ref="SEAT_VAN_RUMPOESC_REAR_LEFT" />
      </AccessableSeats>
      <VehicleExtraPointsInfo ref="NULL" />
      <Flags />
      <BlockJackReactionSides />
    </Item>
    <Item type="CVehicleEntryPointInfo">
      <Name>ENTRY_POINT_VAN_RUMPOESC_REAR_RIGHT</Name>
      <DoorBoneName>boot</DoorBoneName>
      <SecondDoorBoneName />
      <DoorHandleBoneName>handle_boot</DoorHandleBoneName>
      <WindowId>REAR_RIGHT</WindowId>
      <VehicleSide>SIDE_REAR</VehicleSide>
      <AccessableSeats>
        <Item ref="SEAT_VAN_RUMPOESC_REAR_RIGHT" />
      </AccessableSeats>
      <VehicleExtraPointsInfo ref="NULL" />
      <Flags />
      <BlockJackReactionSides />
    </Item>
  </VehicleEntryPointInfos>
  <VehicleSeatAnimInfos>
    <Item type="CVehicleSeatAnimInfo">
      <Name>SEAT_ANIM_LADYBIRD_REAR_LEFT</Name>
      <DriveByInfo ref="DRIVEBY_STANDARD_REAR_LEFT"/>
      <InsideClipSetMap ref="INSIDE_CLIPSET_MAP_STD_REAR_LEFT"/>
      <PanicClipSet>clipset@veh@std@rds@idle_panic</PanicClipSet>
      <AgitatedClipSet/>
      <DuckedClipSet>clipset@veh@std@rds@idle_duck</DuckedClipSet>
      <FemaleClipSet/>
      <LowLODIdleAnim>STD_CAR_REAR_DS_IDLE</LowLODIdleAnim>
      <SeatAmbientContext>IN_CAR_STANDARD</SeatAmbientContext>
      <InVehicleMoveNetwork>VEHICLE_DEFAULT</InVehicleMoveNetwork>
      <SeatAnimFlags>UseStandardInVehicleAnims HasPanicAnims UseCloseDoorBlendAnims</SeatAnimFlags>
      <SteeringSmoothing value="0.100000"/>
      <ExitToAimInfoName>STANDARD</ExitToAimInfoName>
      <MaleGestureClipSetId>ANIM_GROUP_GESTURE_M_CAR_STD_PS</MaleGestureClipSetId>
      <FemaleGestureClipSetId>ANIM_GROUP_GESTURE_F_CAR_STD_PS</FemaleGestureClipSetId>
    </Item>
    <Item type="CVehicleSeatAnimInfo">
      <Name>SEAT_ANIM_LADYBIRD_REAR_RIGHT</Name>
      <DriveByInfo ref="DRIVEBY_STANDARD_REAR_RIGHT"/>
      <InsideClipSetMap ref="INSIDE_CLIPSET_MAP_STD_REAR_RIGHT"/>
      <PanicClipSet>clipset@veh@std@rps@idle_panic</PanicClipSet>
      <AgitatedClipSet/>
      <DuckedClipSet>clipset@veh@std@rps@idle_duck</DuckedClipSet>
      <FemaleClipSet/>
      <LowLODIdleAnim>STD_CAR_REAR_PS_IDLE</LowLODIdleAnim>
      <SeatAmbientContext>IN_CAR_STANDARD</SeatAmbientContext>
      <InVehicleMoveNetwork>VEHICLE_DEFAULT</InVehicleMoveNetwork>
      <SeatAnimFlags>WeaponAttachedToLeftHand UseStandardInVehicleAnims HasPanicAnims UseCloseDoorBlendAnims</SeatAnimFlags>
      <SteeringSmoothing value="0.100000"/>
      <ExitToAimInfoName>STANDARD</ExitToAimInfoName>
      <MaleGestureClipSetId>ANIM_GROUP_GESTURE_M_CAR_STD_PS</MaleGestureClipSetId>
      <FemaleGestureClipSetId>ANIM_GROUP_GESTURE_F_CAR_STD_PS</FemaleGestureClipSetId>
    </Item>
  </VehicleSeatAnimInfos>
  <VehicleEntryPointInfos>
    <Item type="CVehicleEntryPointInfo">
      <Name>ENTRY_POINT_LADYBIRD_REAR_LEFT</Name>
      <DoorBoneName></DoorBoneName>
      <SecondDoorBoneName/>
      <DoorHandleBoneName></DoorHandleBoneName>
      <WindowId>REAR_LEFT</WindowId>
      <VehicleSide>SIDE_LEFT</VehicleSide>
      <AccessableSeats>
        <Item ref="SEAT_STANDARD_REAR_LEFT"/>
      </AccessableSeats>
      <VehicleExtraPointsInfo ref="EXTRA_VEHICLE_POINTS_STD_LEFT"/>
      <Flags>NotUsableOutsideVehicle</Flags>
      <BlockJackReactionSides>
        <Item>SIDE_LEFT</Item>
      </BlockJackReactionSides>
    </Item>
    <Item type="CVehicleEntryPointInfo">
      <Name>ENTRY_POINT_LADYBIRD_REAR_RIGHT</Name>
      <DoorBoneName></DoorBoneName>
      <SecondDoorBoneName/>
      <DoorHandleBoneName></DoorHandleBoneName>
      <WindowId>REAR_RIGHT</WindowId>
      <VehicleSide>SIDE_RIGHT</VehicleSide>
      <AccessableSeats>
        <Item ref="SEAT_STANDARD_REAR_RIGHT"/>
      </AccessableSeats>
      <VehicleExtraPointsInfo ref="EXTRA_VEHICLE_POINTS_STD_RIGHT"/>
      <Flags>NotUsableOutsideVehicle</Flags>
      <BlockJackReactionSides>
        <Item>SIDE_RIGHT</Item>
      </BlockJackReactionSides>
    </Item>
  </VehicleEntryPointInfos>
  <VehicleLayoutInfos>
    <Item type="CVehicleLayoutInfo">
      <Name>LAYOUT_VAN_RUMPOESC</Name>
      <Seats>
        <Item>
          <SeatInfo ref="SEAT_STANDARD_FRONT_LEFT" />
          <SeatAnimInfo ref="SEAT_ANIM_VAN_FRONT_LEFT" />
        </Item>
        <Item>
          <SeatInfo ref="SEAT_STANDARD_FRONT_RIGHT" />
          <SeatAnimInfo ref="SEAT_ANIM_VAN_FRONT_RIGHT" />
        </Item>
        <Item>
          <SeatInfo ref="SEAT_STANDARD_REAR_LEFT" />
          <SeatAnimInfo ref="SEAT_ANIM_VAN_RUMPO3_SIDEDOOR_REAR_LEFT" />
        </Item>
        <Item>
          <SeatInfo ref="SEAT_STANDARD_REAR_RIGHT" />
          <SeatAnimInfo ref="SEAT_ANIM_VAN_RUMPO3_SIDEDOOR_REAR_RIGHT" />
        </Item>
        <Item>
          <SeatInfo ref="SEAT_VAN_RUMPOESC_REAR_LEFT" />
          <SeatAnimInfo ref="SEAT_ANIM_VAN_REAR_LEFT" />
        </Item>
        <Item>
          <SeatInfo ref="SEAT_VAN_RUMPOESC_REAR_RIGHT" />
          <SeatAnimInfo ref="SEAT_ANIM_VAN_REAR_RIGHT" />
        </Item>
      </Seats>
      <EntryPoints>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_STANDARD_FRONT_LEFT" />
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_VAN_FRONT_LEFT" />
        </Item>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_STANDARD_FRONT_RIGHT" />
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_VAN_FRONT_RIGHT" />
        </Item>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_STANDARD_REAR_LEFT" />
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_VAN_RUMPO3_SIDEDOOR_REAR_LEFT" />
        </Item>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_STANDARD_REAR_RIGHT" />
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_VAN_RUMPO3_SIDEDOOR_REAR_RIGHT" />
        </Item>
      </EntryPoints>
      <LayoutFlags>StreamAnims UseVanOpenDoorBlendParams UseDoorOscillation UseLeanSteerAnims
        DisableTargetRearDoorOpenRatio UseLowerDoorBlockTest</LayoutFlags>
      <BicycleInfo ref="NULL" />
      <AnimRateSet ref="NULL" />
      <HandsUpClipSetId>busted_vehicle_van</HandsUpClipSetId>
      <SteeringWheelOffset x="0.006700" y="0.330000" z="0.270000" />
      <MaxXAcceleration value="4.000000" />
      <BodyLeanXApproachSpeed value="5.000000" />
      <BodyLeanXSmallDelta value="0.300000" />
      <LookBackApproachSpeedScale value="1.000000" />
      <FirstPersonAdditiveIdleClipSets>
        <Item>clipset@veh@van@ds@idle_a</Item>
        <Item>clipset@veh@van@ds@idle_b</Item>
        <Item>clipset@veh@van@ds@idle_c</Item>
        <Item>clipset@veh@van@ds@idle_d</Item>
        <Item>clipset@veh@van@ds@idle_e</Item>
      </FirstPersonAdditiveIdleClipSets>
      <FirstPersonRoadRageClipSets>
        <Item>clipset@veh@van@ds@hit_wheel@idle_a</Item>
        <Item>clipset@veh@van@ds@hit_wheel@idle_b</Item>
        <Item>clipset@veh@van@ds@hit_wheel@idle_c</Item>
      </FirstPersonRoadRageClipSets>
      <CellphoneClipsets>
        <CellphoneClipsetDS>ANIM_GROUP_CELLPHONE_IN_CAR_DS</CellphoneClipsetDS>
        <CellphoneClipsetPS>ANIM_GROUP_CELLPHONE_IN_CAR_PS</CellphoneClipsetPS>
        <CellphoneClipsetDS_FPS>ANIM_GROUP_CELLPHONE_IN_CAR_DS_FPS</CellphoneClipsetDS_FPS>
        <CellphoneClipsetPS_FPS>ANIM_GROUP_CELLPHONE_IN_CAR_DS_FPS</CellphoneClipsetPS_FPS>
      </CellphoneClipsets>
    </Item>
    <Item type="CVehicleLayoutInfo">
      <Name>LAYOUT_LADYBIRD</Name>
      <Seats>
        <Item>
          <SeatInfo ref="SEAT_STANDARD_FRONT_LEFT"/>
          <SeatAnimInfo ref="SEAT_ANIM_STANDARD_FRONT_LEFT"/>
        </Item>
        <Item>
          <SeatInfo ref="SEAT_STANDARD_FRONT_RIGHT"/>
          <SeatAnimInfo ref="SEAT_ANIM_STANDARD_FRONT_RIGHT"/>
        </Item>
        <Item>
          <SeatInfo ref="SEAT_STANDARD_REAR_LEFT"/>
          <SeatAnimInfo ref="SEAT_ANIM_LADYBIRD_REAR_LEFT"/>
        </Item>
        <Item>
          <SeatInfo ref="SEAT_STANDARD_REAR_RIGHT"/>
          <SeatAnimInfo ref="SEAT_ANIM_LADYBIRD_REAR_RIGHT"/>
        </Item>
      </Seats>
      <EntryPoints>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_STANDARD_FRONT_LEFT"/>
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_STANDARD_FRONT_LEFT"/>
        </Item>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_STANDARD_FRONT_RIGHT"/>
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_STANDARD_FRONT_RIGHT"/>
        </Item>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_LADYBIRD_REAR_LEFT"/>
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_STANDARD_REAR_LEFT"/>
        </Item>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_LADYBIRD_REAR_RIGHT"/>
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_STANDARD_REAR_RIGHT"/>
        </Item>
      </EntryPoints>
      <LayoutFlags>StreamAnims UseDoorOscillation UseLeanSteerAnims UseSteeringWheelIk</LayoutFlags>
      <BicycleInfo ref="NULL"/>
      <AnimRateSet ref="STD_LOW_ANIM_RATE_SET"/>
      <HandsUpClipSetId>busted_vehicle_std</HandsUpClipSetId>
      <SteeringWheelOffset x="0.000000" y="0.350000" z="0.320000"/>
      <MaxXAcceleration value="4.00000"/>
      <BodyLeanXApproachSpeed value="5.00000"/>
      <BodyLeanXSmallDelta value="0.30000"/>
      <LookBackApproachSpeedScale value="1.00000"/>
      <FirstPersonAdditiveIdleClipSets>
        <Item>clipset@veh@std@ds@idle_a</Item>
        <Item>clipset@veh@std@ds@idle_b</Item>
        <Item>clipset@veh@std@ds@idle_c</Item>
        <Item>clipset@veh@std@ds@idle_d</Item>
        <Item>clipset@veh@std@ds@idle_e</Item>
      </FirstPersonAdditiveIdleClipSets>
      <FirstPersonRoadRageClipSets>
        <Item>clipset@veh@std@ds@hit_wheel@idle_a</Item>
        <Item>clipset@veh@std@ds@hit_wheel@idle_b</Item>
        <Item>clipset@veh@std@ds@hit_wheel@idle_c</Item>
      </FirstPersonRoadRageClipSets>
    </Item>
  </VehicleLayoutInfos>
</CVehicleMetadataMgr>