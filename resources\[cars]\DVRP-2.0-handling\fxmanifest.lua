fx_version 'cerulean'
game 'gta5'
name 'DVRP Handling'

lua54 'yes'

client_script {
	'car_names.lua',
    -- 'data/**/**/vehicle_names.lua'
}

files {
    -- 'data/**/**/handling.meta',
    -- 'data/**/**/vehiclelayouts.meta',
    -- 'data/**/**/vehicles.meta',
    -- 'data/**/**/carcols.meta',
    -- 'data/**/**/carvariations.meta',
    -- 'data/**/**/vfxvehicleinfo',
    -- 'data/**/**/weapons_dish.meta',
    -- 'data/**/**/vehiclelayouts_newsvan.meta',
    -- 'data/**/**/vehiclelayouts_mrtasty.meta',
	'baseGame/**/**/handling.meta',
    'baseGame/**/**/vehicles.meta',
    'baseGame/**/**/carcols.meta',
    'baseGame/**/**/carvariations.meta',
    -- 'vehiclemodelsets.meta',
}

--data_file 'VEHICLE_LAYOUTS_FILE' 'data/**/**/vehiclelayouts_newsvan.meta'
-- data_file 'VEHICLE_LAYOUTS_FILE' 'data/**/**/vehiclelayouts_mrtasty.meta'

-- data_file 'HANDLING_FILE' 'data/**/**/handling.meta'
data_file 'HANDLING_FILE' 'baseGame/**/**/handling.meta'
-- data_file 'VEHICLE_LAYOUTS_FILE' 'data/**/**/vehiclelayouts.meta'
-- data_file 'VEHICLE_METADATA_FILE' 'data/**/**/vehicles.meta'
data_file 'VEHICLE_METADATA_FILE' 'baseGame/**/**/vehicles.meta'
-- data_file 'CARCOLS_FILE' 'data/**/**/carcols.meta'
data_file 'CARCOLS_FILE' 'baseGame/**/**/carcols.meta'
-- data_file 'VEHICLE_VARIATION_FILE' 'data/**/**/carvariations.meta'
data_file 'VEHICLE_VARIATION_FILE' 'baseGame/**/**/carvariations.meta'
--data_file 'VFXVEHICLEINFO_FILE' 'data/**/**/vfxvehicleinfo'




-- data_file 'WEAPONINFO_FILE' 	'data/**/**/vehicleweapons_*.meta'


data_file 'WEAPONINFO_FILE' 'data/weapons_fpmrsa.meta'


-- data_file 'AMBIENT_VEHICLE_MODEL_SET_FILE' 'vehiclemodelsets.meta'