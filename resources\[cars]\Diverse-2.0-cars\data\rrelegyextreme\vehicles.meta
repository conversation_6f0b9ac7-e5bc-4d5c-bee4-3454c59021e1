<?xml version="1.0" encoding="utf-8"?>

<CVehicleModelInfo__InitDataList>
	<residentTxd>vehshare</residentTxd>
	<residentAnims />
	<InitDatas>
	<Item>
		<modelName>rrelegyextreme</modelName>
		<txdName>rrelegyextreme</txdName>
		<handlingId>rrelegyextreme</handlingId>
		<gameName>Custom Elegy Extreme</gameName>
		<vehicleMakeName>BBC</vehicleMakeName>
		<expressionDictName>null</expressionDictName>
		<expressionName>null</expressionName>
		<animConvRoofDictName>null</animConvRoofDictName>
		<animConvRoofName>null</animConvRoofName>
		<animConvRoofWindowsAffected />
		<ptfxAssetName>null</ptfxAssetName>
		<audioNameHash>jestermk4</audioNameHash>
		<layout>LAYOUT_STANDARD</layout>
		<coverBoundOffsets>AIRTUG_COVER_OFFSET_INFO</coverBoundOffsets>
		<explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <layout>LAYOUT_STANDARD</layout>
      <coverBoundOffsets>MINIVAN_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout />
      <cameraName>DEFAULT_FOLLOW_VEHICLE_CAMERA</cameraName>
      <aimCameraName>DEFAULT_THIRD_PERSON_VEHICLE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>VEHICLE_BONNET_CAMERA_LOW</bonnetCameraName>
      <povCameraName>DEFAULT_POV_CAMERA_LOOKAROUND_MID</povCameraName>
      <FirstPersonDriveByIKOffset x="0.000000" y="-0.055000" z="-0.025000" />
      <FirstPersonDriveByUnarmedIKOffset x="0.000000" y="0.000000" z="-0.020000" />
	  <FirstPersonProjectileDriveByIKOffset x="0.000000" y="0.000000" z="0.000000" />
	  <FirstPersonProjectileDriveByPassengerIKOffset x="0.000000" y="0.000000" z="0.000000" />
	  <FirstPersonProjectileDriveByRearLeftIKOffset x="0.000000" y="0.020000" z="0.000000" />
	  <FirstPersonProjectileDriveByRearRightIKOffset x="0.000000" y="0.020000" z="0.000000" />
	  <FirstPersonDriveByLeftPassengerIKOffset x="0.000000" y="0.000000" z="0.000000" />
	  <FirstPersonDriveByRightPassengerIKOffset x="0.000000" y="-0.055000" z="-0.055000" />
	  <FirstPersonDriveByRightRearPassengerIKOffset x="0.000000" y="0.000000" z="0.000000" />
	  <FirstPersonDriveByLeftPassengerUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000" />
	  <FirstPersonDriveByRightPassengerUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000" />
	  <FirstPersonMobilePhoneOffset x="0.148000" y="0.268000" z="0.458000" />
      <FirstPersonPassengerMobilePhoneOffset x="0.136000" y="0.223000" z="0.425000" />
	  <FirstPersonMobilePhoneSeatIKOffset>
		<Item>
			<Offset x="0.136000" y="0.136000" z="0.455000" />
			<SeatIndex value="2" />
		</Item>
		<Item>
			<Offset x="0.136000" y="0.136000" z="0.455000" />
			<SeatIndex value="3" />
		</Item>
	  </FirstPersonMobilePhoneSeatIKOffset> 
      <PovCameraOffset x="0.000000" y="-0.135000" z="0.585000" />
      <PovCameraVerticalAdjustmentForRollCage value="0.000000" />
      <PovPassengerCameraOffset x="0.000000" y="0.000000" z="0.070000" />
      <PovRearPassengerCameraOffset x="0.000000" y="-0.125000" z="0.070000" />
      <vfxInfoName>VFXVEHICLEINFO_CAR_GENERIC</vfxInfoName>
      <shouldUseCinematicViewMode value="true" />
      <shouldCameraTransitionOnClimbUpDown value="false" />
      <shouldCameraIgnoreExiting value="false" />
      <AllowPretendOccupants value="true" />
      <AllowJoyriding value="true" />
      <AllowSundayDriving value="true" />
      <AllowBodyColorMapping value="true" />
      <wheelScale value="0.254000" />
      <wheelScaleRear value="0.254000" />
      <dirtLevelMin value="0.000000" />
      <dirtLevelMax value="0.00000" />
      <envEffScaleMin value="0.000000" />
      <envEffScaleMax value="1.000000" />
      <envEffScaleMin2 value="0.000000" />
      <envEffScaleMax2 value="1.000000" />
      <damageMapScale value="0.600000" />
      <damageOffsetScale value="1.000000" />
      <diffuseTint value="0x00FFFFFF" />
      <steerWheelMult value="1.000000" />
      <HDTextureDist value="5.000000" />
      <lodDistances content="float_array">
        15.000000
        30.000000
        60.000000	
        120.000000	
        500.000000	
        500.000000
      </lodDistances>
      <minSeatHeight value="0.873" />
      <identicalModelSpawnDistance value="20" />
      <maxNumOfSameColor value="10" />
      <defaultBodyHealth value="1000.000000" />
      <pretendOccupantsScale value="1.000000" />
      <visibleSpawnDistScale value="1.000000" />
      <trackerPathWidth value="2.000000" />
      <weaponForceMult value="0.900000" />
      <frequency value="100" />
      <swankness>SWANKNESS_1</swankness>
      <maxNum value="999" />
		<flags>FLAG_HAS_LIVERY FLAG_SPORTS FLAG_EXTRAS_STRONG FLAG_RICH_CAR FLAG_NO_BROKEN_DOWN_SCENARIO FLAG_HAS_INTERIOR_EXTRAS FLAG_RECESSED_TAILLIGHT_CORONAS</flags>
		<type>VEHICLE_TYPE_CAR</type>
		<plateType>VPT_BACK_PLATES</plateType>
		<dashboardType>VDT_RACE</dashboardType>
		<vehicleClass>VC_SPORT</vehicleClass>
		<wheelType>VWT_SPORT</wheelType>
		<trailers />
		<additionalTrailers />
		<drivers />
		<extraIncludes />
		<doorsWithCollisionWhenClosed />
		<driveableDoors />
		<bumpersNeedToCollideWithMap value="false" />
		<needsRopeTexture value="false" />
		<requiredExtras />
		<rewards />
		<cinematicPartCamera>
			<Item>WHEEL_FRONT_RIGHT_CAMERA</Item>
			<Item>WHEEL_FRONT_LEFT_CAMERA</Item>
			<Item>WHEEL_REAR_RIGHT_CAMERA</Item>
			<Item>WHEEL_REAR_LEFT_CAMERA</Item>
		</cinematicPartCamera>
		<NmBraceOverrideSet />
		<buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000" />
		<buoyancySphereSizeScale value="1.000000" />
		<pOverrideRagdollThreshold type="NULL" />
		<firstPersonDrivebyData>
				<Item>LOW_SURANO_FRONT_LEFT</Item>
				<Item>LOW_SURANO_FRONT_RIGHT</Item>
				<Item>LOW_SURANO_REAR_LEFT</Item>
				<Item>LOW_SURANO_REAR_RIGHT</Item>
			</firstPersonDrivebyData>
		</Item>
	</InitDatas>
	<txdRelationships>
	<Item>
		<parent>rrelegyextreme1</parent>
		<child>rrelegyextreme</child>
	</Item>
	<Item>
		<parent>rrelegyextreme2</parent>
		<child>rrelegyextreme1</child>
	</Item>
	<Item>
		<parent>vehicles_banshee_interior</parent>
		<child>rrelegyextreme2</child>
	</Item>
	</txdRelationships>
</CVehicleModelInfo__InitDataList>