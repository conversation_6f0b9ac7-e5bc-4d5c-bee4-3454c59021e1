<?xml version="1.0" encoding="utf-8"?>
<CVehicleModelInfoVarGlobal>
  <!--  Exported by L_kid73 on 2023-09-22 @ 14:43 using LiveLights 1.0.8333.746 by PNWParksFan  -->
  <!--  LiveLights is open source! Download or learn more at https://github.com/pnwparksfan/rph-live-lights/  -->
  <Sirens>
    <Item>
      <id value="9200" />
      <name>emsscout</name>
      <timeMultiplier value="1" />
      <lightFalloffMax value="100" />
      <lightFalloffExponent value="100" />
      <lightInnerConeAngle value="15" />
      <lightOuterConeAngle value="70" />
      <lightOffset value="0" />
      <textureName>VehicleLight_car_LED1</textureName>
      <sequencerBpm value="220" />
      <leftHeadLight>
        <sequencer value="0" />
      </leftHeadLight>
      <rightHeadLight>
        <sequencer value="0" />
      </rightHeadLight>
      <leftTailLight>
        <sequencer value="0" />
      </leftTailLight>
      <rightTailLight>
        <sequencer value="0" />
      </rightTailLight>
      <leftHeadLightMultiples value="1" />
      <rightHeadLightMultiples value="1" />
      <leftTailLightMultiples value="1" />
      <rightTailLightMultiples value="2" />
      <useRealLights value="true" />
      <sirens>
        <!-- Siren 1 -->
        <Item>
          <rotation>
            <delta value="0" />
            <start value="0" />
            <speed value="3" />
            <sequencer value="2840237354" />
            <multiples value="1" />
            <direction value="false" />
            <syncToBpm value="true" />
          </rotation>
          <flashiness>
            <delta value="1.57079637" />
            <start value="0" />
            <speed value="150" />
            <sequencer value="2840237354" />
            <multiples value="2" />
            <direction value="true" />
            <syncToBpm value="true" />
          </flashiness>
          <corona>
            <intensity value="50" />
            <size value="1" />
            <pull value="0.03" />
            <faceCamera value="false" />
          </corona>
          <color value="0xFFFF0300" />
          <intensity value="1" />
          <lightGroup value="0" />
          <rotate value="false" />
          <scale value="true" />
          <scaleFactor value="10" />
          <flash value="true" />
          <light value="true" />
          <spotLight value="true" />
          <castShadows value="false" />
        </Item>
        <!-- Siren 2 -->
        <Item>
          <rotation>
            <delta value="0" />
            <start value="0" />
            <speed value="3" />
            <sequencer value="2840242858" />
            <multiples value="1" />
            <direction value="false" />
            <syncToBpm value="true" />
          </rotation>
          <flashiness>
            <delta value="0" />
            <start value="0" />
            <speed value="150" />
            <sequencer value="2840242858" />
            <multiples value="2" />
            <direction value="true" />
            <syncToBpm value="true" />
          </flashiness>
          <corona>
            <intensity value="50" />
            <size value="1" />
            <pull value="0.03" />
            <faceCamera value="false" />
          </corona>
          <color value="0xFFFF0300" />
          <intensity value="1" />
          <lightGroup value="0" />
          <rotate value="false" />
          <scale value="true" />
          <scaleFactor value="10" />
          <flash value="true" />
          <light value="true" />
          <spotLight value="true" />
          <castShadows value="false" />
        </Item>
        <!-- Siren 3 -->
        <Item>
          <rotation>
            <delta value="0" />
            <start value="0" />
            <speed value="3" />
            <sequencer value="2840237354" />
            <multiples value="1" />
            <direction value="false" />
            <syncToBpm value="true" />
          </rotation>
          <flashiness>
            <delta value="0" />
            <start value="0" />
            <speed value="150" />
            <sequencer value="2840237354" />
            <multiples value="2" />
            <direction value="true" />
            <syncToBpm value="true" />
          </flashiness>
          <corona>
            <intensity value="50" />
            <size value="1" />
            <pull value="0.03" />
            <faceCamera value="false" />
          </corona>
          <color value="0xFFFF0300" />
          <intensity value="1" />
          <lightGroup value="0" />
          <rotate value="false" />
          <scale value="true" />
          <scaleFactor value="10" />
          <flash value="true" />
          <light value="true" />
          <spotLight value="true" />
          <castShadows value="false" />
        </Item>
        <!-- Siren 4 -->
        <Item>
          <rotation>
            <delta value="0" />
            <start value="0" />
            <speed value="3" />
            <sequencer value="2840242858" />
            <multiples value="1" />
            <direction value="false" />
            <syncToBpm value="true" />
          </rotation>
          <flashiness>
            <delta value="0" />
            <start value="0" />
            <speed value="150" />
            <sequencer value="2840242858" />
            <multiples value="2" />
            <direction value="true" />
            <syncToBpm value="true" />
          </flashiness>
          <corona>
            <intensity value="50" />
            <size value="1" />
            <pull value="0.03" />
            <faceCamera value="false" />
          </corona>
          <color value="0xFFFFFFFF" />
          <intensity value="1" />
          <lightGroup value="0" />
          <rotate value="false" />
          <scale value="true" />
          <scaleFactor value="10" />
          <flash value="true" />
          <light value="true" />
          <spotLight value="true" />
          <castShadows value="false" />
        </Item>
        <!-- Siren 5 -->
        <Item>
          <rotation>
            <delta value="1.57079637" />
            <start value="0" />
            <speed value="3" />
            <sequencer value="2840237354" />
            <multiples value="1" />
            <direction value="false" />
            <syncToBpm value="true" />
          </rotation>
          <flashiness>
            <delta value="0" />
            <start value="0" />
            <speed value="0" />
            <sequencer value="2840237354" />
            <multiples value="2" />
            <direction value="false" />
            <syncToBpm value="true" />
          </flashiness>
          <corona>
            <intensity value="10" />
            <size value="1" />
            <pull value="0.03" />
            <faceCamera value="false" />
          </corona>
          <color value="0xFFFFFFFF" />
          <intensity value="1" />
          <lightGroup value="0" />
          <rotate value="false" />
          <scale value="true" />
          <scaleFactor value="10" />
          <flash value="true" />
          <light value="true" />
          <spotLight value="true" />
          <castShadows value="false" />
        </Item>
        <!-- Siren 6 -->
        <Item>
          <rotation>
            <delta value="-1.57079637" />
            <start value="0" />
            <speed value="3" />
            <sequencer value="2840242858" />
            <multiples value="1" />
            <direction value="false" />
            <syncToBpm value="true" />
          </rotation>
          <flashiness>
            <delta value="0" />
            <start value="0" />
            <speed value="0" />
            <sequencer value="2840242858" />
            <multiples value="2" />
            <direction value="false" />
            <syncToBpm value="true" />
          </flashiness>
          <corona>
            <intensity value="50" />
            <size value="1" />
            <pull value="0.03" />
            <faceCamera value="false" />
          </corona>
          <color value="0xFFFF0300" />
          <intensity value="1" />
          <lightGroup value="0" />
          <rotate value="false" />
          <scale value="true" />
          <scaleFactor value="10" />
          <flash value="true" />
          <light value="true" />
          <spotLight value="true" />
          <castShadows value="false" />
        </Item>
        <!-- Siren 7 -->
        <Item>
          <rotation>
            <delta value="3.14159274" />
            <start value="0" />
            <speed value="3" />
            <sequencer value="2840237354" />
            <multiples value="1" />
            <direction value="false" />
            <syncToBpm value="true" />
          </rotation>
          <flashiness>
            <delta value="0" />
            <start value="0" />
            <speed value="0" />
            <sequencer value="2840237354" />
            <multiples value="2" />
            <direction value="true" />
            <syncToBpm value="true" />
          </flashiness>
          <corona>
            <intensity value="50" />
            <size value="1" />
            <pull value="0.03" />
            <faceCamera value="false" />
          </corona>
          <color value="0xFFFF0000" />
          <intensity value="1" />
          <lightGroup value="0" />
          <rotate value="false" />
          <scale value="true" />
          <scaleFactor value="10" />
          <flash value="true" />
          <light value="true" />
          <spotLight value="true" />
          <castShadows value="false" />
        </Item>
        <!-- Siren 8 -->
        <Item>
          <rotation>
            <delta value="3.14159274" />
            <start value="0" />
            <speed value="3" />
            <sequencer value="2840242858" />
            <multiples value="1" />
            <direction value="false" />
            <syncToBpm value="true" />
          </rotation>
          <flashiness>
            <delta value="4.712389" />
            <start value="0" />
            <speed value="0" />
            <sequencer value="2840242858" />
            <multiples value="3" />
            <direction value="false" />
            <syncToBpm value="true" />
          </flashiness>
          <corona>
            <intensity value="50" />
            <size value="1" />
            <pull value="0.03" />
            <faceCamera value="false" />
          </corona>
          <color value="0xFFFF0000" />
          <intensity value="1" />
          <lightGroup value="0" />
          <rotate value="false" />
          <scale value="true" />
          <scaleFactor value="10" />
          <flash value="true" />
          <light value="true" />
          <spotLight value="true" />
          <castShadows value="false" />
        </Item>
        <!-- Siren 9 -->
        <Item>
          <rotation>
            <delta value="3.14159274" />
            <start value="0" />
            <speed value="3" />
            <sequencer value="2863311530" />
            <multiples value="1" />
            <direction value="false" />
            <syncToBpm value="true" />
          </rotation>
          <flashiness>
            <delta value="3.14159274" />
            <start value="0" />
            <speed value="0" />
            <sequencer value="1431655765" />
            <multiples value="2" />
            <direction value="true" />
            <syncToBpm value="true" />
          </flashiness>
          <corona>
            <intensity value="50" />
            <size value="1" />
            <pull value="0.03" />
            <faceCamera value="false" />
          </corona>
          <color value="0xFFFF4800" />
          <intensity value="1" />
          <lightGroup value="0" />
          <rotate value="false" />
          <scale value="true" />
          <scaleFactor value="10" />
          <flash value="true" />
          <light value="true" />
          <spotLight value="true" />
          <castShadows value="false" />
        </Item>
        <!-- Siren 10 -->
        <Item>
          <rotation>
            <delta value="3.14159274" />
            <start value="0" />
            <speed value="3" />
            <sequencer value="2863311530" />
            <multiples value="1" />
            <direction value="false" />
            <syncToBpm value="true" />
          </rotation>
          <flashiness>
            <delta value="3.14159274" />
            <start value="0" />
            <speed value="0" />
            <sequencer value="2863311530" />
            <multiples value="2" />
            <direction value="false" />
            <syncToBpm value="true" />
          </flashiness>
          <corona>
            <intensity value="50" />
            <size value="1" />
            <pull value="0.03" />
            <faceCamera value="false" />
          </corona>
          <color value="0xFFFF4800" />
          <intensity value="1" />
          <lightGroup value="0" />
          <rotate value="false" />
          <scale value="true" />
          <scaleFactor value="10" />
          <flash value="true" />
          <light value="true" />
          <spotLight value="true" />
          <castShadows value="false" />
        </Item>
        <!-- Siren 11 -->
        <Item>
          <rotation>
            <delta value="3.14159274" />
            <start value="0" />
            <speed value="3" />
            <sequencer value="2840941909" />
            <multiples value="1" />
            <direction value="false" />
            <syncToBpm value="true" />
          </rotation>
          <flashiness>
            <delta value="3.14159274" />
            <start value="0" />
            <speed value="0" />
            <sequencer value="1431655765" />
            <multiples value="3" />
            <direction value="true" />
            <syncToBpm value="true" />
          </flashiness>
          <corona>
            <intensity value="50" />
            <size value="1" />
            <pull value="0.03" />
            <faceCamera value="false" />
          </corona>
          <color value="0xFFFF4800" />
          <intensity value="1" />
          <lightGroup value="0" />
          <rotate value="false" />
          <scale value="true" />
          <scaleFactor value="10" />
          <flash value="true" />
          <light value="true" />
          <spotLight value="true" />
          <castShadows value="false" />
        </Item>
        <!-- Siren 12 -->
        <Item>
          <rotation>
            <delta value="3.14159274" />
            <start value="0" />
            <speed value="3" />
            <sequencer value="2863311530" />
            <multiples value="1" />
            <direction value="false" />
            <syncToBpm value="true" />
          </rotation>
          <flashiness>
            <delta value="3.14159274" />
            <start value="0" />
            <speed value="0" />
            <sequencer value="2863311530" />
            <multiples value="2" />
            <direction value="true" />
            <syncToBpm value="true" />
          </flashiness>
          <corona>
            <intensity value="50" />
            <size value="0" />
            <pull value="0.03" />
            <faceCamera value="false" />
          </corona>
          <color value="0xFFFF4800" />
          <intensity value="0" />
          <lightGroup value="0" />
          <rotate value="false" />
          <scale value="true" />
          <scaleFactor value="20" />
          <flash value="false" />
          <light value="true" />
          <spotLight value="true" />
          <castShadows value="false" />
        </Item>
        <!-- Siren 13 -->
        <Item>
          <rotation>
            <delta value="0" />
            <start value="0" />
            <speed value="3" />
            <sequencer value="2863311530" />
            <multiples value="1" />
            <direction value="false" />
            <syncToBpm value="true" />
          </rotation>
          <flashiness>
            <delta value="0" />
            <start value="0" />
            <speed value="0" />
            <sequencer value="2863311530" />
            <multiples value="2" />
            <direction value="true" />
            <syncToBpm value="true" />
          </flashiness>
          <corona>
            <intensity value="50" />
            <size value="1.1" />
            <pull value="0.15" />
            <faceCamera value="false" />
          </corona>
          <color value="0xFFFF0300" />
          <intensity value="1" />
          <lightGroup value="0" />
          <rotate value="false" />
          <scale value="true" />
          <scaleFactor value="20" />
          <flash value="true" />
          <light value="false" />
          <spotLight value="true" />
          <castShadows value="false" />
        </Item>
        <!-- Siren 14 -->
        <Item>
          <rotation>
            <delta value="-0.0100000007" />
            <start value="0" />
            <speed value="3" />
            <sequencer value="2863311530" />
            <multiples value="1" />
            <direction value="false" />
            <syncToBpm value="true" />
          </rotation>
          <flashiness>
            <delta value="0" />
            <start value="0" />
            <speed value="0" />
            <sequencer value="2863311530" />
            <multiples value="2" />
            <direction value="false" />
            <syncToBpm value="true" />
          </flashiness>
          <corona>
            <intensity value="50" />
            <size value="1.1" />
            <pull value="0.15" />
            <faceCamera value="false" />
          </corona>
          <color value="0xFFFF0300" />
          <intensity value="1" />
          <lightGroup value="1" />
          <rotate value="false" />
          <scale value="true" />
          <scaleFactor value="10" />
          <flash value="true" />
          <light value="false" />
          <spotLight value="true" />
          <castShadows value="false" />
        </Item>
        <!-- Siren 15 -->
        <Item>
          <rotation>
            <delta value="0" />
            <start value="0" />
            <speed value="3" />
            <sequencer value="2863311530" />
            <multiples value="1" />
            <direction value="false" />
            <syncToBpm value="true" />
          </rotation>
          <flashiness>
            <delta value="0" />
            <start value="0" />
            <speed value="0" />
            <sequencer value="2863311530" />
            <multiples value="3" />
            <direction value="true" />
            <syncToBpm value="true" />
          </flashiness>
          <corona>
            <intensity value="50" />
            <size value="1.1" />
            <pull value="0.15" />
            <faceCamera value="false" />
          </corona>
          <color value="0xFFFF0300" />
          <intensity value="1" />
          <lightGroup value="0" />
          <rotate value="false" />
          <scale value="true" />
          <scaleFactor value="2" />
          <flash value="true" />
          <light value="false" />
          <spotLight value="true" />
          <castShadows value="false" />
        </Item>
        <!-- Siren 16 -->
        <Item>
          <rotation>
            <delta value="-0.0100000007" />
            <start value="0" />
            <speed value="3" />
            <sequencer value="2863311530" />
            <multiples value="1" />
            <direction value="false" />
            <syncToBpm value="true" />
          </rotation>
          <flashiness>
            <delta value="0" />
            <start value="0" />
            <speed value="0" />
            <sequencer value="2863311530" />
            <multiples value="3" />
            <direction value="false" />
            <syncToBpm value="true" />
          </flashiness>
          <corona>
            <intensity value="50" />
            <size value="1.1" />
            <pull value="0.15" />
            <faceCamera value="false" />
          </corona>
          <color value="0xFF000AFF" />
          <intensity value="1" />
          <lightGroup value="1" />
          <rotate value="false" />
          <scale value="true" />
          <scaleFactor value="2" />
          <flash value="true" />
          <light value="false" />
          <spotLight value="true" />
          <castShadows value="false" />
        </Item>
        <!-- Siren 17 -->
        <Item>
          <rotation>
            <delta value="0" />
            <start value="0" />
            <speed value="3" />
            <sequencer value="2863311530" />
            <multiples value="1" />
            <direction value="false" />
            <syncToBpm value="true" />
          </rotation>
          <flashiness>
            <delta value="3.141593" />
            <start value="0" />
            <speed value="0" />
            <sequencer value="2863311530" />
            <multiples value="2" />
            <direction value="true" />
            <syncToBpm value="true" />
          </flashiness>
          <corona>
            <intensity value="50" />
            <size value="1.1" />
            <pull value="0.2" />
            <faceCamera value="false" />
          </corona>
          <color value="0xFFFF0300" />
          <intensity value="1" />
          <lightGroup value="0" />
          <rotate value="false" />
          <scale value="true" />
          <scaleFactor value="2" />
          <flash value="true" />
          <light value="false" />
          <spotLight value="true" />
          <castShadows value="false" />
        </Item>
        <!-- Siren 18 -->
        <Item>
          <rotation>
            <delta value="0" />
            <start value="0" />
            <speed value="3" />
            <sequencer value="2863311530" />
            <multiples value="1" />
            <direction value="false" />
            <syncToBpm value="true" />
          </rotation>
          <flashiness>
            <delta value="3.141593" />
            <start value="0" />
            <speed value="0" />
            <sequencer value="2863311530" />
            <multiples value="2" />
            <direction value="true" />
            <syncToBpm value="true" />
          </flashiness>
          <corona>
            <intensity value="50" />
            <size value="1.1" />
            <pull value="0.15" />
            <faceCamera value="false" />
          </corona>
          <color value="0xFFFF0300" />
          <intensity value="1" />
          <lightGroup value="0" />
          <rotate value="false" />
          <scale value="true" />
          <scaleFactor value="2" />
          <flash value="true" />
          <light value="false" />
          <spotLight value="true" />
          <castShadows value="false" />
        </Item>
        <!-- Siren 19 -->
        <Item>
          <rotation>
            <delta value="3.14159274" />
            <start value="0" />
            <speed value="3" />
            <sequencer value="2863311530" />
            <multiples value="1" />
            <direction value="false" />
            <syncToBpm value="true" />
          </rotation>
          <flashiness>
            <delta value="0.34906584" />
            <start value="0" />
            <speed value="0" />
            <sequencer value="2863311530" />
            <multiples value="2" />
            <direction value="false" />
            <syncToBpm value="true" />
          </flashiness>
          <corona>
            <intensity value="50" />
            <size value="1.1" />
            <pull value="0.15" />
            <faceCamera value="false" />
          </corona>
          <color value="0xFFFF0300" />
          <intensity value="1" />
          <lightGroup value="1" />
          <rotate value="false" />
          <scale value="true" />
          <scaleFactor value="4" />
          <flash value="true" />
          <light value="false" />
          <spotLight value="true" />
          <castShadows value="false" />
        </Item>
        <!-- Siren 20 -->
        <Item>
          <rotation>
            <delta value="-0.0100000007" />
            <start value="0" />
            <speed value="3" />
            <sequencer value="2863311530" />
            <multiples value="1" />
            <direction value="false" />
            <syncToBpm value="true" />
          </rotation>
          <flashiness>
            <delta value="-0.34906584" />
            <start value="0" />
            <speed value="0" />
            <sequencer value="2863311530" />
            <multiples value="2" />
            <direction value="false" />
            <syncToBpm value="true" />
          </flashiness>
          <corona>
            <intensity value="50" />
            <size value="1.1" />
            <pull value="0.15" />
            <faceCamera value="false" />
          </corona>
          <color value="0xFFFF0300" />
          <intensity value="1" />
          <lightGroup value="1" />
          <rotate value="false" />
          <scale value="true" />
          <scaleFactor value="4" />
          <flash value="true" />
          <light value="false" />
          <spotLight value="true" />
          <castShadows value="false" />
        </Item>
        <!-- Siren 21 -->
        <Item>
          <rotation>
            <delta value="0" />
            <start value="0" />
            <speed value="0" />
            <sequencer value="0" />
            <multiples value="0" />
            <direction value="false" />
            <syncToBpm value="false" />
          </rotation>
          <flashiness>
            <delta value="1.57079637" />
            <start value="0" />
            <speed value="0" />
            <sequencer value="2841285930" />
            <multiples value="2" />
            <direction value="true" />
            <syncToBpm value="true" />
          </flashiness>
          <corona>
            <intensity value="50" />
            <size value="1" />
            <pull value="0.3" />
            <faceCamera value="false" />
          </corona>
          <color value="0xFFFF0300" />
          <intensity value="1" />
          <lightGroup value="0" />
          <rotate value="false" />
          <scale value="true" />
          <scaleFactor value="0" />
          <flash value="false" />
          <light value="true" />
          <spotLight value="true" />
          <castShadows value="false" />
        </Item>
        <!-- Siren 22 -->
        <Item>
          <rotation>
            <delta value="0" />
            <start value="0" />
            <speed value="0" />
            <sequencer value="0" />
            <multiples value="0" />
            <direction value="false" />
            <syncToBpm value="false" />
          </rotation>
          <flashiness>
            <delta value="4.712389" />
            <start value="0" />
            <speed value="0" />
            <sequencer value="1431661269" />
            <multiples value="2" />
            <direction value="true" />
            <syncToBpm value="true" />
          </flashiness>
          <corona>
            <intensity value="50" />
            <size value="1" />
            <pull value="0.3" />
            <faceCamera value="false" />
          </corona>
          <color value="0xFFFF0300" />
          <intensity value="1" />
          <lightGroup value="0" />
          <rotate value="false" />
          <scale value="true" />
          <scaleFactor value="10" />
          <flash value="false" />
          <light value="true" />
          <spotLight value="true" />
          <castShadows value="false" />
        </Item>
        <!-- Siren 23 -->
        <Item>
          <rotation>
            <delta value="0" />
            <start value="0" />
            <speed value="0" />
            <sequencer value="0" />
            <multiples value="0" />
            <direction value="false" />
            <syncToBpm value="false" />
          </rotation>
          <flashiness>
            <delta value="1.57079637" />
            <start value="0" />
            <speed value="0" />
            <sequencer value="2841285930" />
            <multiples value="2" />
            <direction value="true" />
            <syncToBpm value="true" />
          </flashiness>
          <corona>
            <intensity value="50" />
            <size value="1" />
            <pull value="0.3" />
            <faceCamera value="false" />
          </corona>
          <color value="0xFFFF0300" />
          <intensity value="1" />
          <lightGroup value="0" />
          <rotate value="false" />
          <scale value="true" />
          <scaleFactor value="10" />
          <flash value="false" />
          <light value="true" />
          <spotLight value="true" />
          <castShadows value="false" />
        </Item>
        <!-- Siren 24 -->
        <Item>
          <rotation>
            <delta value="0" />
            <start value="0" />
            <speed value="0" />
            <sequencer value="0" />
            <multiples value="0" />
            <direction value="false" />
            <syncToBpm value="false" />
          </rotation>
          <flashiness>
            <delta value="4.712389" />
            <start value="0" />
            <speed value="0" />
            <sequencer value="1431661269" />
            <multiples value="2" />
            <direction value="true" />
            <syncToBpm value="true" />
          </flashiness>
          <corona>
            <intensity value="50" />
            <size value="1" />
            <pull value="0.3" />
            <faceCamera value="false" />
          </corona>
          <color value="0xFFFF0300" />
          <intensity value="1" />
          <lightGroup value="0" />
          <rotate value="false" />
          <scale value="true" />
          <scaleFactor value="10" />
          <flash value="false" />
          <light value="true" />
          <spotLight value="true" />
          <castShadows value="false" />
        </Item>
        <!-- Siren 25 -->
        <Item>
          <rotation>
            <delta value="0" />
            <start value="0" />
            <speed value="0" />
            <sequencer value="0" />
            <multiples value="0" />
            <direction value="false" />
            <syncToBpm value="false" />
          </rotation>
          <flashiness>
            <delta value="0" />
            <start value="0" />
            <speed value="0" />
            <sequencer value="0" />
            <multiples value="0" />
            <direction value="false" />
            <syncToBpm value="false" />
          </flashiness>
          <corona>
            <intensity value="0" />
            <size value="0" />
            <pull value="0" />
            <faceCamera value="false" />
          </corona>
          <color value="0x00000000" />
          <intensity value="0" />
          <lightGroup value="0" />
          <rotate value="false" />
          <scale value="false" />
          <scaleFactor value="0" />
          <flash value="false" />
          <light value="false" />
          <spotLight value="false" />
          <castShadows value="false" />
        </Item>
        <!-- Siren 26 -->
        <Item>
          <rotation>
            <delta value="0" />
            <start value="0" />
            <speed value="0" />
            <sequencer value="0" />
            <multiples value="0" />
            <direction value="false" />
            <syncToBpm value="false" />
          </rotation>
          <flashiness>
            <delta value="0" />
            <start value="0" />
            <speed value="0" />
            <sequencer value="0" />
            <multiples value="0" />
            <direction value="false" />
            <syncToBpm value="false" />
          </flashiness>
          <corona>
            <intensity value="0" />
            <size value="0" />
            <pull value="0" />
            <faceCamera value="false" />
          </corona>
          <color value="0x00000000" />
          <intensity value="0" />
          <lightGroup value="0" />
          <rotate value="false" />
          <scale value="false" />
          <scaleFactor value="0" />
          <flash value="false" />
          <light value="false" />
          <spotLight value="false" />
          <castShadows value="false" />
        </Item>
        <!-- Siren 27 -->
        <Item>
          <rotation>
            <delta value="0" />
            <start value="0" />
            <speed value="0" />
            <sequencer value="0" />
            <multiples value="0" />
            <direction value="false" />
            <syncToBpm value="false" />
          </rotation>
          <flashiness>
            <delta value="0" />
            <start value="0" />
            <speed value="0" />
            <sequencer value="0" />
            <multiples value="0" />
            <direction value="false" />
            <syncToBpm value="false" />
          </flashiness>
          <corona>
            <intensity value="0" />
            <size value="0" />
            <pull value="0" />
            <faceCamera value="false" />
          </corona>
          <color value="0x00000000" />
          <intensity value="0" />
          <lightGroup value="0" />
          <rotate value="false" />
          <scale value="false" />
          <scaleFactor value="0" />
          <flash value="false" />
          <light value="false" />
          <spotLight value="false" />
          <castShadows value="false" />
        </Item>
        <!-- Siren 28 -->
        <Item>
          <rotation>
            <delta value="0" />
            <start value="0" />
            <speed value="0" />
            <sequencer value="0" />
            <multiples value="0" />
            <direction value="false" />
            <syncToBpm value="false" />
          </rotation>
          <flashiness>
            <delta value="0" />
            <start value="0" />
            <speed value="0" />
            <sequencer value="0" />
            <multiples value="0" />
            <direction value="false" />
            <syncToBpm value="false" />
          </flashiness>
          <corona>
            <intensity value="0" />
            <size value="0" />
            <pull value="0" />
            <faceCamera value="false" />
          </corona>
          <color value="0x00000000" />
          <intensity value="0" />
          <lightGroup value="0" />
          <rotate value="false" />
          <scale value="false" />
          <scaleFactor value="0" />
          <flash value="false" />
          <light value="false" />
          <spotLight value="false" />
          <castShadows value="false" />
        </Item>
        <!-- Siren 29 -->
        <Item>
          <rotation>
            <delta value="0" />
            <start value="0" />
            <speed value="0" />
            <sequencer value="0" />
            <multiples value="0" />
            <direction value="false" />
            <syncToBpm value="false" />
          </rotation>
          <flashiness>
            <delta value="0" />
            <start value="0" />
            <speed value="0" />
            <sequencer value="0" />
            <multiples value="0" />
            <direction value="false" />
            <syncToBpm value="false" />
          </flashiness>
          <corona>
            <intensity value="0" />
            <size value="0" />
            <pull value="0" />
            <faceCamera value="false" />
          </corona>
          <color value="0x00000000" />
          <intensity value="0" />
          <lightGroup value="0" />
          <rotate value="false" />
          <scale value="false" />
          <scaleFactor value="0" />
          <flash value="false" />
          <light value="false" />
          <spotLight value="false" />
          <castShadows value="false" />
        </Item>
        <!-- Siren 30 -->
        <Item>
          <rotation>
            <delta value="0" />
            <start value="0" />
            <speed value="0" />
            <sequencer value="0" />
            <multiples value="0" />
            <direction value="false" />
            <syncToBpm value="false" />
          </rotation>
          <flashiness>
            <delta value="0" />
            <start value="0" />
            <speed value="0" />
            <sequencer value="0" />
            <multiples value="0" />
            <direction value="false" />
            <syncToBpm value="false" />
          </flashiness>
          <corona>
            <intensity value="0" />
            <size value="0" />
            <pull value="0" />
            <faceCamera value="false" />
          </corona>
          <color value="0x00000000" />
          <intensity value="0" />
          <lightGroup value="0" />
          <rotate value="false" />
          <scale value="false" />
          <scaleFactor value="0" />
          <flash value="false" />
          <light value="false" />
          <spotLight value="false" />
          <castShadows value="false" />
        </Item>
        <!-- Siren 31 -->
        <Item>
          <rotation>
            <delta value="0" />
            <start value="0" />
            <speed value="0" />
            <sequencer value="0" />
            <multiples value="0" />
            <direction value="false" />
            <syncToBpm value="false" />
          </rotation>
          <flashiness>
            <delta value="0" />
            <start value="0" />
            <speed value="0" />
            <sequencer value="0" />
            <multiples value="0" />
            <direction value="false" />
            <syncToBpm value="false" />
          </flashiness>
          <corona>
            <intensity value="0" />
            <size value="0" />
            <pull value="0" />
            <faceCamera value="false" />
          </corona>
          <color value="0x00000000" />
          <intensity value="0" />
          <lightGroup value="0" />
          <rotate value="false" />
          <scale value="false" />
          <scaleFactor value="0" />
          <flash value="false" />
          <light value="false" />
          <spotLight value="false" />
          <castShadows value="false" />
        </Item>
        <!-- Siren 32 -->
        <Item>
          <rotation>
            <delta value="0" />
            <start value="0" />
            <speed value="0" />
            <sequencer value="0" />
            <multiples value="0" />
            <direction value="false" />
            <syncToBpm value="false" />
          </rotation>
          <flashiness>
            <delta value="0" />
            <start value="0" />
            <speed value="0" />
            <sequencer value="0" />
            <multiples value="0" />
            <direction value="false" />
            <syncToBpm value="false" />
          </flashiness>
          <corona>
            <intensity value="0" />
            <size value="0" />
            <pull value="0" />
            <faceCamera value="false" />
          </corona>
          <color value="0x00000000" />
          <intensity value="0" />
          <lightGroup value="0" />
          <rotate value="false" />
          <scale value="false" />
          <scaleFactor value="0" />
          <flash value="false" />
          <light value="false" />
          <spotLight value="false" />
          <castShadows value="false" />
        </Item>
      </sirens>
    </Item>
  </Sirens>
</CVehicleModelInfoVarGlobal>