<?xml version="1.0" encoding="UTF-8"?>
<CVehicleModelInfo__InitDataList>
	<residentTxd>vehshare</residentTxd>
	<residentAnims />
	<InitDatas>
		<Item>
			<modelName>emsscout</modelName>
			<txdName>emsscout</txdName>
			<handlingId>emsscout</handlingId>
			<gameName>emsscout</gameName>
			<vehicleMakeName>VAPID</vehicleMakeName>
			<expressionDictName>null</expressionDictName>
			<expressionName>null</expressionName>
			<animConvRoofDictName>null</animConvRoofDictName>
			<animConvRoofName>null</animConvRoofName>
			<animConvRoofWindowsAffected/>
			<ptfxAssetName>null</ptfxAssetName>
			<audioNameHash>sheriff2</audioNameHash>
			<layout>LAYOUT_RANGER</layout>
			<coverBoundOffsets>BALLER_COVER_OFFSET_INFO</coverBoundOffsets>
			<explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
			<scenarioLayout/>
			<cameraName>DEFAULT_FOLLOW_VEHICLE_CAMERA</cameraName>
			<aimCameraName>DEFAULT_THIRD_PERSON_VEHICLE_AIM_CAMERA</aimCameraName>
			<bonnetCameraName>VEHICLE_BONNET_CAMERA_MID</bonnetCameraName>
			<povCameraName>DEFAULT_POV_CAMERA</povCameraName>
			<FirstPersonDriveByIKOffset x="0.000000" y="-0.050000" z="-0.050000"/>
			<FirstPersonDriveByUnarmedIKOffset x="0.000000" y="0.000000" z="-0.020000"/>
			<FirstPersonProjectileDriveByIKOffset x="0.030000" y="0.000000" z="0.000000"/>
			<FirstPersonProjectileDriveByPassengerIKOffset x="-0.030000" y="0.000000" z="0.000000"/>
			<FirstPersonProjectileDriveByRearLeftIKOffset x="0.000000" y="0.025000" z="-0.035000"/>
			<FirstPersonProjectileDriveByRearRightIKOffset x="0.000000" y="0.030000" z="-0.050000"/>
			<FirstPersonDriveByLeftPassengerIKOffset x="0.000000" y="0.000000" z="-0.010000"/>
			<FirstPersonDriveByRightPassengerIKOffset x="0.000000" y="-0.050000" z="-0.050000"/>
			<FirstPersonDriveByRightRearPassengerIKOffset x="0.000000" y="0.000000" z="-0.010000"/>
			<FirstPersonDriveByLeftPassengerUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000"/>
			<FirstPersonDriveByRightPassengerUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000"/>
			<FirstPersonMobilePhoneOffset x="0.135000" y="0.240000" z="0.530000"/>
			<FirstPersonPassengerMobilePhoneOffset x="0.136000" y="0.223000" z="0.425000"/>
			<FirstPersonMobilePhoneSeatIKOffset>
				<Item>
					<Offset x="0.136000" y="0.136000" z="0.445000"/>
					<SeatIndex value="2"/>
				</Item>
				<Item>
					<Offset x="0.136000" y="0.136000" z="0.445000"/>
					<SeatIndex value="3"/>
				</Item>
			</FirstPersonMobilePhoneSeatIKOffset>
			<PovCameraOffset x="0.000000" y="-0.165000" z="0.645000"/>
			<PovCameraVerticalAdjustmentForRollCage value="0.000000"/>
			<PovPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000"/>
			<PovRearPassengerCameraOffset x="0.000000" y="0.010000" z="0.030000"/>
			<vfxInfoName>VFXVEHICLEINFO_CAR_GENERIC</vfxInfoName>
			<shouldUseCinematicViewMode value="true"/>
			<shouldCameraTransitionOnClimbUpDown value="false"/>
			<shouldCameraIgnoreExiting value="false"/>
			<AllowPretendOccupants value="true"/>
			<AllowJoyriding value="true"/>
			<AllowSundayDriving value="true"/>
			<AllowBodyColorMapping value="true"/>
			<wheelScale value="0.304000"/>
			<wheelScaleRear value="0.304000"/>
			<dirtLevelMin value="0.000000"/>
			<dirtLevelMax value="0.550000"/>
			<envEffScaleMin value="0.000000"/>
			<envEffScaleMax value="1.000000"/>
			<envEffScaleMin2 value="0.000000"/>
			<envEffScaleMax2 value="1.000000"/>
			<damageMapScale value="0.300000"/>
			<damageOffsetScale value="1.000000"/>
			<diffuseTint value="0x00FFFFFF"/>
			<steerWheelMult value="1.000000"/>
			<HDTextureDist value="5.000000"/>
			<lodDistances content="float_array">
				15.000000
				25.000000
				70.000000
				140.000000
				350.000000
				350.000000
			</lodDistances>
			<minSeatHeight value="0.887"/>
			<identicalModelSpawnDistance value="20"/>
			<maxNumOfSameColor value="3"/>
			<defaultBodyHealth value="1000.000000"/>
			<pretendOccupantsScale value="1.000000"/>
			<visibleSpawnDistScale value="1.000000"/>
			<trackerPathWidth value="2.000000"/>
			<weaponForceMult value="1.000000"/>
			<frequency value="30"/>
			<swankness>SWANKNESS_3</swankness>
			<maxNum value="7"/>
			<flags>FLAG_HAS_LIVERY FLAG_EMERGENCY_SERVICE FLAG_NO_RESPRAY FLAG_REPORT_CRIME_IF_STANDING_ON FLAG_USE_INTERIOR_RED_LIGHT FLAG_DONT_SPAWN_AS_AMBIENT FLAG_EXTRAS_STRONG FLAG_IS_BULKY</flags>
			<type>VEHICLE_TYPE_CAR</type>
			<plateType>VPT_FRONT_AND_BACK_PLATES</plateType>
			<dashboardType>VDT_RACE</dashboardType>
			<vehicleClass>VC_EMERGENCY</vehicleClass>
			<wheelType>VWT_SUV</wheelType>
			<trailers/>
			<additionalTrailers/>
			<drivers/>
			<extraIncludes/>
			<doorsWithCollisionWhenClosed/>
			<driveableDoors/>
			<bumpersNeedToCollideWithMap value="false"/>
			<needsRopeTexture value="false"/>
			<requiredExtras/>
			<rewards/>
			<cinematicPartCamera>
				<Item>WHEEL_FRONT_RIGHT_CAMERA</Item>
				<Item>WHEEL_FRONT_LEFT_CAMERA</Item>
				<Item>WHEEL_REAR_RIGHT_CAMERA</Item>
				<Item>WHEEL_REAR_LEFT_CAMERA</Item>
			</cinematicPartCamera>
			<NmBraceOverrideSet>Truck</NmBraceOverrideSet>
			<buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000"/>
			<buoyancySphereSizeScale value="1.000000"/>
			<pOverrideRagdollThreshold type="NULL"/>
			<firstPersonDrivebyData>
				<Item>STD_BUFFALO_FRONT_LEFT</Item>
				<Item>STD_BUFFALO_FRONT_RIGHT</Item>
				<Item>STD_BALLER3_REAR_LEFT</Item>
				<Item>STD_BALLER3_REAR_RIGHT</Item>
			</firstPersonDrivebyData>
		</Item>
	</InitDatas>
	<txdRelationships>
		<Item>
			<parent>vehshare</parent>
			<child>vehicles_gendials</child>
		</Item>
		<Item>
			<parent>vehicles_cav_interior</parent>
			<child>emsscout</child>
		</Item>
		<Item>
			<parent>granger2</parent>
			<child>emsscout</child>
		</Item>
	</txdRelationships>
</CVehicleModelInfo__InitDataList>