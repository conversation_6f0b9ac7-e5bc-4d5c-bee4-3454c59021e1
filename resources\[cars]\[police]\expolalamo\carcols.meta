﻿<?xml version="1.0" encoding="UTF-8"?>
<CVehicleModelInfoVarGlobal> 
  <Kits>
    <Item>
      <kitName>5899_expolalamo_modkit</kitName>
      <id value="5899" />
      <kitType>MKT_SPECIAL</kitType>
    <visibleMods>
      <!-- Police Equipments -->
        <Item>
          <modelName>expolalamo_livery1</modelName>
          <modShopLabel>LIV_LSPD</modShopLabel>
          <linkedModels />
          <turnOffBones>
          </turnOffBones>
          <type>VMT_LIVERY_MOD</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>expolalamo_livery2</modelName>
          <modShopLabel>LIV_LSPD_SUPERVISOR</modShopLabel>
          <linkedModels />
          <turnOffBones>
          </turnOffBones>
          <type>VMT_LIVERY_MOD</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
          <modelName>expolalamo_livery3</modelName>
          <modShopLabel>LIV_LSPD_K9</modShopLabel>
          <linkedModels />
          <turnOffBones>
          </turnOffBones>
          <type>VMT_LIVERY_MOD</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        </Item>
          <modelName>expolalamo_livery4</modelName>
          <modShopLabel>LIV_LSPD_K9_SUPERVISOR</modShopLabel>
          <linkedModels />
          <turnOffBones>
          </turnOffBones>
          <type>VMT_LIVERY_MOD</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>null</modelName>
          <modShopLabel>POL_SLICKTOP</modShopLabel>
          <linkedModels />
          <turnOffBones>
            <Item>extra_1</Item>
          </turnOffBones>
          <type>VMT_ROOF</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>null</modelName>
          <modShopLabel>POL_PERFORMANCE</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_SPOILER</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>expolalamo_ramb2</modelName>
          <modShopLabel>POL_RAMB</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_HYDRO</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>expolalamo_ramb2</modelName>
          <modShopLabel>POL_RAMB2</modShopLabel>
          <linkedModels>
          <item>expolalamo_ramb3</item>
          </linkedModels>
          <turnOffBones />
          <type>VMT_HYDRO</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>expolalamo_null</modelName>
          <modShopLabel>POL_RAMB3</modShopLabel>
          <linkedModels />
          <turnOffBones>
           <Item>extra_4</Item>
          </turnOffBones>
          <type>VMT_HYDRO</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>expolalamok9_seat</modelName>
          <modShopLabel>POL_K9</modShopLabel>
          <linkedModels />
          <turnOffBones>
            <Item>misc_g</Item>
            <Item>misc_f</Item>
            <Item>misc_b</Item>
          </turnOffBones>
          <type>VMT_SEATS</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>expolalamo_console</modelName>
          <modShopLabel>POL_CONSOLE</modShopLabel>
          <linkedModels>
           <Item>expolalamo_radar</Item>
          </linkedModels>
          <turnOffBones />
          <type>VMT_INTERIOR3</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>expolalamo_trunk</modelName>
          <modShopLabel>POL_TRUNK</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_TRUNK</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>expolalamo_gunrack</modelName>
          <modShopLabel>POL_GUNRACK</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_CHASSIS</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="0" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>expolalamo_null</modelName>
          <modShopLabel>POL_DIV</modShopLabel>
          <linkedModels />
          <turnOffBones>
          <item>misc_d</item>
          </turnOffBones>
          <type>VMT_CHASSIS</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="0" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>expolalamo_anta</modelName>
          <modShopLabel>POL_ANTA</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_CHASSIS3</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="0" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>expolalamo_antb</modelName>
          <modShopLabel>POL_ANTB</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_CHASSIS3</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="0" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>expolalamo_antd</modelName>
          <modShopLabel>POL_ANTD</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_CHASSIS3</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="0" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>expolalamo_ante</modelName>
          <modShopLabel>POL_ANTE</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_CHASSIS3</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="0" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>expolalamo_antf</modelName>
          <modShopLabel>POL_ANTF</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_CHASSIS3</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="0" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>expolalamo_antg</modelName>
          <modShopLabel>POL_ANTG</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_CHASSIS3</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="0" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>expolalamo_slight_1</modelName>
          <modShopLabel>POL_SLIGHT1</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_INTERIOR5</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="0" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>expolalamo_slight_2</modelName>
          <modShopLabel>POL_SLIGHT2</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_INTERIOR5</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="0" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>expolalamo_slight_3</modelName>
          <modShopLabel>POL_SLIGHT3</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_INTERIOR5</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="0" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>expolalamo_dsidertag_s</modelName>
          <modShopLabel>POL_TAG_S</modShopLabel>
          <linkedModels>
            <Item>expolalamo_psidertag_s</Item>
          </linkedModels>
          <turnOffBones />
          <type>VMT_DOOR_L</type>
          <bone>door_dside_r</bone>
          <collisionBone>door_dside_r</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>expolalamo_dsidertag_c</modelName>
          <modShopLabel>POL_TAG_C</modShopLabel>
          <linkedModels>
            <Item>expolalamo_psidertag_c</Item>
          </linkedModels>
          <turnOffBones />
          <type>VMT_DOOR_L</type>
          <bone>door_dside_r</bone>
          <collisionBone>door_dside_r</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item> 
        <Item>
          <modelName>expolalamo_dsidertag_ins</modelName>
          <modShopLabel>POL_TAG_INS</modShopLabel>
          <linkedModels>
            <Item>expolalamo_psidertag_ins</Item>
          </linkedModels>
          <turnOffBones />
          <type>VMT_DOOR_L</type>
          <bone>door_dside_r</bone>
          <collisionBone>door_dside_r</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>expolalamo_dsidertag_d</modelName>
          <modShopLabel>POL_TAG_D</modShopLabel>
          <linkedModels>
            <Item>expolalamo_psidertag_d</Item>
          </linkedModels>
          <turnOffBones />
          <type>VMT_DOOR_L</type>
          <bone>door_dside_r</bone>
          <collisionBone>door_dside_r</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>expolalamo_dsidertag_swat</modelName>
          <modShopLabel>POL_TAG_SWAT</modShopLabel>
          <linkedModels>
            <Item>expolalamo_psidertag_swat</Item>
          </linkedModels>
          <turnOffBones />
          <type>VMT_DOOR_L</type>
          <bone>door_dside_r</bone>
          <collisionBone>door_dside_r</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>expolalamo_dsidertag_cautionk9</modelName>
          <modShopLabel>POL_TAG_CAUTIONK9</modShopLabel>
          <linkedModels>
            <Item>expolalamo_psidertag_cautionk9</Item>
          </linkedModels>
          <turnOffBones />
          <type>VMT_DOOR_L</type>
          <bone>door_dside_r</bone>
          <collisionBone>door_dside_r</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>expolalamo_dsidertag_cautionk9b</modelName>
          <modShopLabel>POL_TAG_CAUTIONK9</modShopLabel>
          <linkedModels>
            <Item>expolalamo_psidertag_cautionk9b</Item>
          </linkedModels>
          <turnOffBones />
          <type>VMT_DOOR_L</type>
          <bone>door_dside_r</bone>
          <collisionBone>door_dside_r</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>expolalamo_dsidertag_k9</modelName>
          <modShopLabel>POL_TAG_K9</modShopLabel>
          <linkedModels>
            <Item>expolalamo_psidertag_k9</Item>
          </linkedModels>
          <turnOffBones />
          <type>VMT_DOOR_L</type>
          <bone>door_dside_r</bone>
          <collisionBone>door_dside_r</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>expolalamo_dsidertag_k9b</modelName>
          <modShopLabel>POL_TAG_K9</modShopLabel>
          <linkedModels>
            <Item>expolalamo_psidertag_k9b</Item>
          </linkedModels>
          <turnOffBones />
          <type>VMT_DOOR_L</type>
          <bone>door_dside_r</bone>
          <collisionBone>door_dside_r</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>expolalamo_dsidertag_policek9</modelName>
          <modShopLabel>POL_TAG_POLICEK9</modShopLabel>
          <linkedModels>
            <Item>expolalamo_psidertag_policek9</Item>
          </linkedModels>
          <turnOffBones />
          <type>VMT_DOOR_L</type>
          <bone>door_dside_r</bone>
          <collisionBone>door_dside_r</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>expolalamo_dsidertag_policek9b</modelName>
          <modShopLabel>POL_TAG_POLICEK9</modShopLabel>
          <linkedModels>
            <Item>expolalamo_psidertag_policek9b</Item>
          </linkedModels>
          <turnOffBones />
          <type>VMT_DOOR_L</type>
          <bone>door_dside_r</bone>
          <collisionBone>door_dside_r</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <!-- Callsings Digit 1 -->
        <Item>
          <modelName>expolalamo_csign_a0</modelName>
          <modShopLabel>POL_C0</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_CHASSIS2</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>expolalamo_csign_a1</modelName>
          <modShopLabel>POL_C1</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_CHASSIS2</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>expolalamo_csign_a2</modelName>
          <modShopLabel>POL_C2</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_CHASSIS2</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>expolalamo_csign_a3</modelName>
          <modShopLabel>POL_C3</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_CHASSIS2</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>expolalamo_csign_a4</modelName>
          <modShopLabel>POL_C4</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_CHASSIS2</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>expolalamo_csign_a5</modelName>
          <modShopLabel>POL_C5</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_CHASSIS2</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>expolalamo_csign_a6</modelName>
          <modShopLabel>POL_C6</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_CHASSIS2</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>expolalamo_csign_a7</modelName>
          <modShopLabel>POL_C7</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_CHASSIS2</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>expolalamo_csign_a8</modelName>
          <modShopLabel>POL_C8</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_CHASSIS2</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>expolalamo_csign_a9</modelName>
          <modShopLabel>POL_C9</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_CHASSIS2</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <!-- Callsings Digit 2 -->
        <Item>
          <modelName>expolalamo_csign_b0</modelName>
          <modShopLabel>POL_C0</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_CHASSIS4</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>expolalamo_csign_b1</modelName>
          <modShopLabel>POL_C1</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_CHASSIS4</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>expolalamo_csign_b2</modelName>
          <modShopLabel>POL_C2</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_CHASSIS4</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>expolalamo_csign_b3</modelName>
          <modShopLabel>POL_C3</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_CHASSIS4</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>expolalamo_csign_b4</modelName>
          <modShopLabel>POL_C4</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_CHASSIS4</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>expolalamo_csign_b5</modelName>
          <modShopLabel>POL_C5</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_CHASSIS4</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>expolalamo_csign_b6</modelName>
          <modShopLabel>POL_C6</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_CHASSIS4</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>expolalamo_csign_b7</modelName>
          <modShopLabel>POL_C7</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_CHASSIS4</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>expolalamo_csign_b8</modelName>
          <modShopLabel>POL_C8</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_CHASSIS4</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>expolalamo_csign_b9</modelName>
          <modShopLabel>POL_C9</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_CHASSIS4</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <!-- Callsings Digit 3 -->
        <Item>
          <modelName>expolalamo_csign_c0</modelName>
          <modShopLabel>POL_C0</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_CHASSIS5</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>expolalamo_csign_c1</modelName>
          <modShopLabel>POL_C1</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_CHASSIS5</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>expolalamo_csign_c2</modelName>
          <modShopLabel>POL_C2</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_CHASSIS5</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>expolalamo_csign_c3</modelName>
          <modShopLabel>POL_C3</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_CHASSIS5</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>expolalamo_csign_c4</modelName>
          <modShopLabel>POL_C4</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_CHASSIS5</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>expolalamo_csign_c5</modelName>
          <modShopLabel>POL_C5</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_CHASSIS5</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>expolalamo_csign_c6</modelName>
          <modShopLabel>POL_C6</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_CHASSIS5</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>expolalamo_csign_c7</modelName>
          <modShopLabel>POL_C7</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_CHASSIS5</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>expolalamo_csign_c8</modelName>
          <modShopLabel>POL_C8</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_CHASSIS5</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>expolalamo_csign_c9</modelName>
          <modShopLabel>POL_C9</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_CHASSIS5</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <!-- Boot Callsigns -->
        <!-- Callsings Digit 4 -->
        <Item>
          <modelName>expolalamo_csign_d0</modelName>
          <modShopLabel>POL_DEPC0</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_WING_L</type>
          <bone>chassis</bone>
          <collisionBone>boot</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>expolalamo_csign_d1</modelName>
          <modShopLabel>POL_DEPC1</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_WING_L</type>
          <bone>chassis</bone>
          <collisionBone>boot</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>expolalamo_csign_d2</modelName>
          <modShopLabel>POL_DEPC2</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_WING_L</type>
          <bone>chassis</bone>
          <collisionBone>boot</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>expolalamo_csign_d3</modelName>
          <modShopLabel>POL_DEPC3</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_WING_L</type>
          <bone>chassis</bone>
          <collisionBone>boot</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>expolalamo_csign_d4</modelName>
          <modShopLabel>POL_DEPC4</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_WING_L</type>
          <bone>chassis</bone>
          <collisionBone>boot</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>expolalamo_csign_d5</modelName>
          <modShopLabel>POL_DEPC5</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_WING_L</type>
          <bone>chassis</bone>
          <collisionBone>boot</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>expolalamo_csign_d6</modelName>
          <modShopLabel>POL_DEPC6</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_WING_L</type>
          <bone>chassis</bone>
          <collisionBone>boot</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>expolalamo_csign_d7</modelName>
          <modShopLabel>POL_DEPC7</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_WING_L</type>
          <bone>chassis</bone>
          <collisionBone>boot</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>expolalamo_csign_d8</modelName>
          <modShopLabel>POL_DEPC8</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_WING_L</type>
          <bone>chassis</bone>
          <collisionBone>boot</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>expolalamo_csign_d9</modelName>
          <modShopLabel>POL_DEPC9</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_WING_L</type>
          <bone>chassis</bone>
          <collisionBone>boot</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <!-- Callsings Digit 5 -->
        <Item>
          <modelName>expolalamo_csign_e0</modelName>
          <modShopLabel>POL_RANKC0</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_WING_R</type>
          <bone>chassis</bone>
          <collisionBone>boot</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>expolalamo_csign_e1</modelName>
          <modShopLabel>POL_RANKC1</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_WING_R</type>
          <bone>chassis</bone>
          <collisionBone>boot</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>expolalamo_csign_e2</modelName>
          <modShopLabel>POL_RANKC2</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_WING_R</type>
          <bone>chassis</bone>
          <collisionBone>boot</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>expolalamo_csign_e3</modelName>
          <modShopLabel>POL_RANKC3</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_WING_R</type>
          <bone>chassis</bone>
          <collisionBone>boot</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>expolalamo_csign_e4</modelName>
          <modShopLabel>POL_RANKC4</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_WING_R</type>
          <bone>chassis</bone>
          <collisionBone>boot</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>expolalamo_csign_e5</modelName>
          <modShopLabel>POL_RANKC5</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_WING_R</type>
          <bone>chassis</bone>
          <collisionBone>boot</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>expolalamo_csign_e6</modelName>
          <modShopLabel>POL_RANKC6</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_WING_R</type>
          <bone>chassis</bone>
          <collisionBone>boot</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>expolalamo_csign_e7</modelName>
          <modShopLabel>POL_RANKC7</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_WING_R</type>
          <bone>chassis</bone>
          <collisionBone>boot</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>expolalamo_csign_e8</modelName>
          <modShopLabel>POL_RANKC8</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_WING_R</type>
          <bone>chassis</bone>
          <collisionBone>boot</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>expolalamo_csign_e9</modelName>
          <modShopLabel>POL_RANKC9</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_WING_R</type>
          <bone>chassis</bone>
          <collisionBone>boot</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <!-- Vanilla Mods -->
	  <!-- ALAMO_TRIM -->
		       <!-- <Item>
          <modelName>tfdecl_trimrear</modelName>
          <modShopLabel>tfdecl_trimrear</modShopLabel>
		  <linkedModels>
		  <Item>tfdecl_trimdsidef1</Item>
		  <Item>tfdecl_trimdsider1</Item>
		  <Item>tfdecl_trimpsider1</Item>
		  <Item>tfdecl_trimpsidef1</Item>
		  </linkedModels>
          <turnOffBones>
		  <Item>misc_i</Item>
		  <Item>misc_g</Item>
		  <Item>misc_h</Item>
		  <Item>misc_f</Item>
		  <Item>misc_e</Item>
		  </turnOffBones>
          <type>VMT_DOOR_L</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item> -->
	  	  	  	  <!-- ALAMO_RBUMPER -->
		  <Item>
		  <modelName>tfdecl_rbumperaccs1</modelName>
          <modShopLabel>tfdecl_rbumperaccs1</modShopLabel>
          <linkedModels/>
          <turnOffBones>
		  <Item>misc_y</Item>
		  </turnOffBones>
          <type>VMT_BUMPER_R</type>
          <bone>bumper_r</bone>
          <collisionBone>bumper_r</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="0" />
          <turnOffExtra value="true" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
	  	  	  <!-- ALAMO_EXHAUST -->
		  <Item>
		  <modelName>tfdecl_exhaust_1</modelName>
          <modShopLabel>tfdecl_exhaust_1</modShopLabel>
          <linkedModels>
		 <Item>tfdecl_exhaustaccs1</Item>
		 </linkedModels>
          <turnOffBones>
		  <Item>exhaust</Item>
		  <Item>misc_v</Item>
		  <Item>misc_z</Item>
		  </turnOffBones>
          <type>VMT_EXHAUST</type>
          <bone>engineblock</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="0" />
          <turnOffExtra value="true" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
		  <modelName>tfdecl_exhaust_2</modelName>
          <modShopLabel>tfdecl_exhaust_2</modShopLabel>
          <linkedModels>
		 <Item>tfdecl_exhaustaccs1</Item>
		 </linkedModels>
          <turnOffBones>
		  <Item>exhaust</Item>
		  <Item>misc_v</Item>
		  <Item>misc_z</Item>
		  </turnOffBones>
          <type>VMT_EXHAUST</type>
          <bone>engineblock</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="0" />
          <turnOffExtra value="true" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
	  	  	  <!-- ALAMO_BOOT_TRIM -->
		  <Item>
		  <modelName>tfdecl_boottrim1</modelName>
          <modShopLabel>tfdecl_boottrim1</modShopLabel>
          <linkedModels/>
          <turnOffBones>
		  <Item>misc_q</Item>
		  </turnOffBones>
          <type>VMT_ICE</type>
          <bone>boot</bone>
          <collisionBone>boot</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="0" />
          <turnOffExtra value="true" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
		  <modelName>tfdecl_boottrim2</modelName>
          <modShopLabel>tfdecl_boottrim2</modShopLabel>
          <linkedModels/>
          <turnOffBones>
		  <Item>misc_q</Item>
		  </turnOffBones>
          <type>VMT_ICE</type>
          <bone>boot</bone>
          <collisionBone>boot</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="0" />
          <turnOffExtra value="true" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
		  <modelName>tfdecl_boottrim3</modelName>
          <modShopLabel>tfdecl_boottrim3</modShopLabel>
          <linkedModels/>
          <turnOffBones>
		  <Item>misc_q</Item>
		  </turnOffBones>
          <type>VMT_ICE</type>
          <bone>boot</bone>
          <collisionBone>boot</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="0" />
          <turnOffExtra value="true" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
	  	  <!-- ALAMO_FBUMPER -->
		  <Item>
		  <modelName>tfdecl_fbumper1</modelName>
          <modShopLabel>tfdecl_fbumper1</modShopLabel>
          <linkedModels/>
          <turnOffBones>
		  <Item>bumper_f</Item>
		  </turnOffBones>
          <type>VMT_BUMPER_F</type>
          <bone>bumper_f</bone>
          <collisionBone>bumper_f</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="0" />
          <turnOffExtra value="true" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
				  <Item>
		  <modelName>tfdecl_fbumper2</modelName>
          <modShopLabel>tfdecl_fbumper2</modShopLabel>
          <linkedModels/>
          <turnOffBones>
		  <Item>bumper_f</Item>
		  </turnOffBones>
          <type>VMT_BUMPER_F</type>
          <bone>bumper_f</bone>
          <collisionBone>bumper_f</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="0" />
          <turnOffExtra value="true" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
		  <modelName>tfdecl_fbumper3</modelName>
          <modShopLabel>tfdecl_fbumper3</modShopLabel>
          <linkedModels>
		  <Item>tfdecl_fbumper3accs1</Item>
		  </linkedModels>
          <turnOffBones>
		  <Item>bumper_f</Item>
		  </turnOffBones>
          <type>VMT_BUMPER_F</type>
          <bone>bumper_f</bone>
          <collisionBone>bumper_f</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="0" />
          <turnOffExtra value="true" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
				<!-- ALAMO_GRILLE -->
		<Item>
		<modelName>tfdecl_grille1</modelName>
		<modShopLabel>tfdecl_grille1</modShopLabel>
        <linkedModels/>
        <turnOffBones>
		<item>misc_a</item>
		</turnOffBones>
		<type>VMT_GRILL</type>
		<bone>chassis</bone>
		<collisionBone>chassis</collisionBone>
		<cameraPos>VMCP_DEFAULT</cameraPos>
		<audioApply value="1.00000000"/>
		<weight value="10"/>
		<turnOffExtra value="false"/>
		<disableBonnetCamera value="false"/>
		<allowBonnetSlide value="true"/>
		</Item>
		<Item>
		<modelName>tfdecl_grille2</modelName>
		<modShopLabel>tfdecl_grille2</modShopLabel>
        <linkedModels/>
        <turnOffBones>
		<item>misc_a</item>
		</turnOffBones>
		<type>VMT_GRILL</type>
		<bone>chassis</bone>
		<collisionBone>chassis</collisionBone>
		<cameraPos>VMCP_DEFAULT</cameraPos>
		<audioApply value="1.00000000"/>
		<weight value="10"/>
		<turnOffExtra value="false"/>
		<disableBonnetCamera value="false"/>
		<allowBonnetSlide value="true"/>
		</Item>
		<Item>
		<modelName>tfdecl_grille3</modelName>
		<modShopLabel>tfdecl_grille3</modShopLabel>
		<linkedModels/>
        <turnOffBones>
		<item>misc_a</item>
		</turnOffBones>
		<type>VMT_GRILL</type>
		<bone>chassis</bone>
		<collisionBone>chassis</collisionBone>
		<cameraPos>VMCP_DEFAULT</cameraPos>
		<audioApply value="1.00000000"/>
		<weight value="10"/>
		<turnOffExtra value="false"/>
		<disableBonnetCamera value="false"/>
		<allowBonnetSlide value="true"/>
		</Item>
			  		<!-- TFALAMO_ROOF_LIGHT -->
        <Item>
          <modelName>tfdecl_rooflite_01</modelName>
          <modShopLabel>tfdecl_rooflite_01</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_CHASSIS4</type>
          <bone>chassis</bone>
          <collisionBone>bodyshell</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		        <Item>
          <modelName>tfdecl_rooflite_02</modelName>
          <modShopLabel>tfdecl_rooflite_02</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_CHASSIS4</type>
          <bone>chassis</bone>
          <collisionBone>bodyshell</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
				<!-- TFALAMO_ROOFRAILS -->
				<Item>
		<modelName>tfdecl_roofrails1</modelName>
		<modShopLabel>tfdecl_roofrails1</modShopLabel>
		<linkedModels/>
        <turnOffBones/>
		<type>VMT_SPOILER</type>
		<bone>chassis</bone>
		<collisionBone>chassis</collisionBone>
		<cameraPos>VMCP_DEFAULT</cameraPos>
		<audioApply value="1.00000000"/>
		<weight value="10"/>
		<turnOffExtra value="false"/>
		<disableBonnetCamera value="false"/>
		<allowBonnetSlide value="true"/>
		</Item>
		<Item>
		<modelName>tfdecl_roofrails1</modelName>
		<modShopLabel>tfdecl_roofrails1accs1</modShopLabel>
		<linkedModels>
		<Item>tfdecl_roof1accs1</Item>
		</linkedModels>
        <turnOffBones/>
		<type>VMT_SPOILER</type>
		<bone>chassis</bone>
		<collisionBone>chassis</collisionBone>
		<cameraPos>VMCP_DEFAULT</cameraPos>
		<audioApply value="1.00000000"/>
		<weight value="10"/>
		<turnOffExtra value="false"/>
		<disableBonnetCamera value="false"/>
		<allowBonnetSlide value="true"/>
		</Item>
		<Item>
		<modelName>tfdecl_roofrails1</modelName>
		<modShopLabel>tfdecl_roofrails1accs12</modShopLabel>
		<linkedModels>
		<Item>tfdecl_roof1accs1</Item>
		<Item>tfdecl_roof1accs2</Item>
		</linkedModels>
        <turnOffBones/>
		<type>VMT_SPOILER</type>
		<bone>chassis</bone>
		<collisionBone>chassis</collisionBone>
		<cameraPos>VMCP_DEFAULT</cameraPos>
		<audioApply value="1.00000000"/>
		<weight value="10"/>
		<turnOffExtra value="false"/>
		<disableBonnetCamera value="false"/>
		<allowBonnetSlide value="true"/>
		</Item>
				<Item>
		<modelName>tfdecl_roofrails1</modelName>
		<modShopLabel>tfdecl_roofrails1accs13</modShopLabel>
		<linkedModels>
		<Item>tfdecl_roof1accs1</Item>
		<Item>tfdecl_roof1accs3</Item>
		</linkedModels>
        <turnOffBones/>
		<type>VMT_SPOILER</type>
		<bone>chassis</bone>
		<collisionBone>chassis</collisionBone>
		<cameraPos>VMCP_DEFAULT</cameraPos>
		<audioApply value="1.00000000"/>
		<weight value="10"/>
		<turnOffExtra value="false"/>
		<disableBonnetCamera value="false"/>
		<allowBonnetSlide value="true"/>
		</Item>
						<Item>
		<modelName>tfdecl_roofrails1</modelName>
		<modShopLabel>tfdecl_roofrails1accs14</modShopLabel>
		<linkedModels>
		<Item>tfdecl_roof1accs1</Item>
		<Item>tfdecl_roof1accs4</Item>
		</linkedModels>
        <turnOffBones/>
		<type>VMT_SPOILER</type>
		<bone>chassis</bone>
		<collisionBone>chassis</collisionBone>
		<cameraPos>VMCP_DEFAULT</cameraPos>
		<audioApply value="1.00000000"/>
		<weight value="10"/>
		<turnOffExtra value="false"/>
		<disableBonnetCamera value="false"/>
		<allowBonnetSlide value="true"/>
		</Item>
								<Item>
		<modelName>tfdecl_roofrails1</modelName>
		<modShopLabel>tfdecl_roofrails1accs15</modShopLabel>
		<linkedModels>
		<Item>tfdecl_roof1accs1</Item>
		<Item>tfdecl_roof1accs5</Item>
		</linkedModels>
        <turnOffBones/>
		<type>VMT_SPOILER</type>
		<bone>chassis</bone>
		<collisionBone>chassis</collisionBone>
		<cameraPos>VMCP_DEFAULT</cameraPos>
		<audioApply value="1.00000000"/>
		<weight value="10"/>
		<turnOffExtra value="false"/>
		<disableBonnetCamera value="false"/>
		<allowBonnetSlide value="true"/>
		</Item>
										<Item>
		<modelName>tfdecl_roofrails1</modelName>
		<modShopLabel>tfdecl_roofrails1accs16</modShopLabel>
		<linkedModels>
		<Item>tfdecl_roof1accs1</Item>
		<Item>tfdecl_roof1accs5</Item>
		<Item>tfdecl_roof1accs6</Item>
		</linkedModels>
        <turnOffBones/>
		<type>VMT_SPOILER</type>
		<bone>chassis</bone>
		<collisionBone>chassis</collisionBone>
		<cameraPos>VMCP_DEFAULT</cameraPos>
		<audioApply value="1.00000000"/>
		<weight value="10"/>
		<turnOffExtra value="false"/>
		<disableBonnetCamera value="false"/>
		<allowBonnetSlide value="true"/>
		</Item>
						<Item>
		<modelName>tfdecl_roofrails2</modelName>
		<modShopLabel>tfdecl_roofrails2</modShopLabel>
		<linkedModels/>
        <turnOffBones/>
		<type>VMT_SPOILER</type>
		<bone>chassis</bone>
		<collisionBone>chassis</collisionBone>
		<cameraPos>VMCP_DEFAULT</cameraPos>
		<audioApply value="1.00000000"/>
		<weight value="10"/>
		<turnOffExtra value="false"/>
		<disableBonnetCamera value="false"/>
		<allowBonnetSlide value="true"/>
		</Item>
				<Item>
		<modelName>tfdecl_roofrails2</modelName>
		<modShopLabel>tfdecl_roofrails2accs1</modShopLabel>
		<linkedModels>
		<Item>tfdecl_roof1accs1</Item>
		</linkedModels>
        <turnOffBones/>
		<type>VMT_SPOILER</type>
		<bone>chassis</bone>
		<collisionBone>chassis</collisionBone>
		<cameraPos>VMCP_DEFAULT</cameraPos>
		<audioApply value="1.00000000"/>
		<weight value="10"/>
		<turnOffExtra value="false"/>
		<disableBonnetCamera value="false"/>
		<allowBonnetSlide value="true"/>
		</Item>
		<Item>
		<modelName>tfdecl_roofrails2</modelName>
		<modShopLabel>tfdecl_roofrails2accs12</modShopLabel>
		<linkedModels>
		<Item>tfdecl_roof1accs1</Item>
		<Item>tfdecl_roof1accs2</Item>
		</linkedModels>
        <turnOffBones/>
		<type>VMT_SPOILER</type>
		<bone>chassis</bone>
		<collisionBone>chassis</collisionBone>
		<cameraPos>VMCP_DEFAULT</cameraPos>
		<audioApply value="1.00000000"/>
		<weight value="10"/>
		<turnOffExtra value="false"/>
		<disableBonnetCamera value="false"/>
		<allowBonnetSlide value="true"/>
		</Item>
				<Item>
		<modelName>tfdecl_roofrails2</modelName>
		<modShopLabel>tfdecl_roofrails2accs13</modShopLabel>
		<linkedModels>
		<Item>tfdecl_roof1accs1</Item>
		<Item>tfdecl_roof1accs3</Item>
		</linkedModels>
        <turnOffBones/>
		<type>VMT_SPOILER</type>
		<bone>chassis</bone>
		<collisionBone>chassis</collisionBone>
		<cameraPos>VMCP_DEFAULT</cameraPos>
		<audioApply value="1.00000000"/>
		<weight value="10"/>
		<turnOffExtra value="false"/>
		<disableBonnetCamera value="false"/>
		<allowBonnetSlide value="true"/>
		</Item>
								<Item>
		<modelName>tfdecl_roofrails2</modelName>
		<modShopLabel>tfdecl_roofrails2accs14</modShopLabel>
		<linkedModels>
		<Item>tfdecl_roof1accs1</Item>
		<Item>tfdecl_roof1accs4</Item>
		</linkedModels>
        <turnOffBones/>
		<type>VMT_SPOILER</type>
		<bone>chassis</bone>
		<collisionBone>chassis</collisionBone>
		<cameraPos>VMCP_DEFAULT</cameraPos>
		<audioApply value="1.00000000"/>
		<weight value="10"/>
		<turnOffExtra value="false"/>
		<disableBonnetCamera value="false"/>
		<allowBonnetSlide value="true"/>
		</Item>
								<Item>
		<modelName>tfdecl_roofrails2</modelName>
		<modShopLabel>tfdecl_roofrails2accs15</modShopLabel>
		<linkedModels>
		<Item>tfdecl_roof1accs1</Item>
		<Item>tfdecl_roof1accs5</Item>
		</linkedModels>
        <turnOffBones/>
		<type>VMT_SPOILER</type>
		<bone>chassis</bone>
		<collisionBone>chassis</collisionBone>
		<cameraPos>VMCP_DEFAULT</cameraPos>
		<audioApply value="1.00000000"/>
		<weight value="10"/>
		<turnOffExtra value="false"/>
		<disableBonnetCamera value="false"/>
		<allowBonnetSlide value="true"/>
		</Item>
										<Item>
		<modelName>tfdecl_roofrails2</modelName>
		<modShopLabel>tfdecl_roofrails2accs16</modShopLabel>
		<linkedModels>
		<Item>tfdecl_roof1accs1</Item>
		<Item>tfdecl_roof1accs5</Item>
		<Item>tfdecl_roof1accs6</Item>
		</linkedModels>
        <turnOffBones/>
		<type>VMT_SPOILER</type>
		<bone>chassis</bone>
		<collisionBone>chassis</collisionBone>
		<cameraPos>VMCP_DEFAULT</cameraPos>
		<audioApply value="1.00000000"/>
		<weight value="10"/>
		<turnOffExtra value="false"/>
		<disableBonnetCamera value="false"/>
		<allowBonnetSlide value="true"/>
		</Item>
	</visibleMods>
	<linkMods>
  <Item>
          <modelName>expolalamo_door_psider</modelName>
          <bone>door_pside_r</bone>
          <turnOffExtra value="false" />
        </Item>
        <Item>
          <modelName>expolalamo_door_dsider</modelName>
          <bone>door_dside_r</bone>
          <turnOffExtra value="false" />
        </Item>
  <Item>
          <modelName>expolalamo_psidertag_c</modelName>
          <bone>door_pside_r</bone>
          <turnOffExtra value="false" />
        </Item>
        <Item>
          <modelName>expolalamo_psidertag_s</modelName>
          <bone>door_pside_r</bone>
          <turnOffExtra value="false" />
        </Item>
        <Item>
          <modelName>expolalamo_psidertag_d</modelName>
          <bone>door_pside_r</bone>
          <turnOffExtra value="false" />
        </Item>
        <Item>
          <modelName>expolalamo_psidertag_ins</modelName>
          <bone>door_pside_r</bone>
          <turnOffExtra value="false" />
        </Item>
        <Item>
          <modelName>expolalamo_psidertag_swat</modelName>
          <bone>door_pside_r</bone>
          <turnOffExtra value="false" />
        </Item>
        <Item>
          <modelName>expolalamo_psidertag_cautionk9</modelName>
          <bone>door_pside_r</bone>
          <turnOffExtra value="false" />
        </Item>
        <Item>
          <modelName>expolalamo_psidertag_k9</modelName>
          <bone>door_pside_r</bone>
          <turnOffExtra value="false" />
        </Item>
        <Item>
          <modelName>expolalamo_psidertag_policek9</modelName>
          <bone>door_pside_r</bone>
          <turnOffExtra value="false" />
        </Item>
        <Item>
          <modelName>expolalamo_psidertag_cautionk9b</modelName>
          <bone>door_pside_r</bone>
          <turnOffExtra value="false" />
        </Item>
        <Item>
          <modelName>expolalamo_psidertag_k9b</modelName>
          <bone>door_pside_r</bone>
          <turnOffExtra value="false" />
        </Item>
        <Item>
          <modelName>expolalamo_psidertag_policek9b</modelName>
          <bone>door_pside_r</bone>
          <turnOffExtra value="false" />
        </Item>
      <Item>
          <modelName>expolalamo_radar</modelName>
          <bone>chassis</bone>
          <turnOffExtra value="false" />
        </Item>
        <Item>
          <modelName>expolalamo_ramb2</modelName>
          <bone>chassis</bone>
          <turnOffExtra value="false" />
        </Item>
        <Item>
          <modelName>expolalamo_ramb3</modelName>
          <bone>chassis</bone>
          <turnOffExtra value="false" />
        </Item>
       <!-- TFALAMO_LINKMODS_START -->
	  <Item>
	  <modelName>tfdecl_trimdsider1</modelName>
      <bone>door_dside_r</bone>
      <turnOffExtra value="false" />
      </Item>
	  	<Item>
	  <modelName>tfdecl_trimdsidef1</modelName>
      <bone>door_dside_f</bone>
      <turnOffExtra value="false" />
      </Item>
	  	<Item>
	  <modelName>tfdecl_trimpsider1</modelName>
      <bone>door_pside_r</bone>
      <turnOffExtra value="false" />
      </Item>
	  	<Item>
	  <modelName>tfdecl_trimpsidef1</modelName>
      <bone>door_pside_f</bone>
      <turnOffExtra value="false" />
      </Item>
	  <Item>
	  <modelName>tfdecl_exhaustaccs1</modelName>
      <bone>bumper_r</bone>
      <turnOffExtra value="false" />
      </Item>
	  <Item>
	  <modelName>tfdecl_fbumper3accs1</modelName>
      <bone>bumper_f</bone>
      <turnOffExtra value="false" />
      </Item>
	  <Item>
	  <modelName>tfdecl_fbumper3accs2</modelName>
      <bone>bumper_f</bone>
      <turnOffExtra value="false" />
      </Item>
	  	  <Item>
	  <modelName>tfdecl_fbumper3accs3</modelName>
      <bone>bumper_f</bone>
      <turnOffExtra value="false" />
      </Item>
	  	  <Item>
	  <modelName>tfdecl_grilleaccs1</modelName>
      <bone>chassis</bone>
      <turnOffExtra value="false" />
      </Item>
	  	  	  <Item>
	  <modelName>tfdecl_grilleaccs2</modelName>
      <bone>chassis</bone>
      <turnOffExtra value="false" />
      </Item>
	  <Item>
	  <modelName>tfdecl_roof1accs1</modelName>
      <bone>chassis</bone>
      <turnOffExtra value="false" />
      </Item>
	  	  <Item>
	  <modelName>tfdecl_roof1accs2</modelName>
      <bone>chassis</bone>
      <turnOffExtra value="false" />
      </Item>
	  	  <Item>
	  <modelName>tfdecl_roof1accs3</modelName>
      <bone>chassis</bone>
      <turnOffExtra value="false" />
      </Item>
	  	  	  <Item>
	  <modelName>tfdecl_roof1accs4</modelName>
      <bone>chassis</bone>
      <turnOffExtra value="false" />
      </Item>
	  	  	  <Item>
	  <modelName>tfdecl_roof1accs5</modelName>
      <bone>chassis</bone>
      <turnOffExtra value="false" />
      </Item>
	  	  	  <Item>
	  <modelName>tfdecl_roof1accs6</modelName>
      <bone>chassis</bone>
      <turnOffExtra value="false" />
      </Item>
	  <!-- TFALAMO_LINKMODS_STOP -->
	</linkMods>
    <statMods>
        <Item>
          <identifier />
          <modifier value="20" />
          <audioApply value="1.000000" />
          <weight value="20" />
          <type>VMT_ENGINE</type>
        </Item>
        <Item>
          <identifier />
          <modifier value="40" />
          <audioApply value="1.000000" />
          <weight value="20" />
          <type>VMT_ENGINE</type>
        </Item>
        <Item>
          <identifier />
          <modifier value="70" />
          <audioApply value="1.000000" />
          <weight value="20" />
          <type>VMT_ENGINE</type>
        </Item>
        <Item>
          <identifier />
          <modifier value="100" />
          <audioApply value="1.000000" />
          <weight value="20" />
          <type>VMT_ENGINE</type>
        </Item>
        <Item>
          <identifier />
          <modifier value="25" />
          <audioApply value="1.000000" />
          <weight value="5" />
          <type>VMT_BRAKES</type>
        </Item>
        <Item>
          <identifier />
          <modifier value="60" />
          <audioApply value="1.000000" />
          <weight value="5" />
          <type>VMT_BRAKES</type>
        </Item>
        <Item>
          <identifier />
          <modifier value="120" />
          <audioApply value="1.000000" />
          <weight value="5" />
          <type>VMT_BRAKES</type>
        </Item>
        <Item>
          <identifier />
          <modifier value="50" />
          <audioApply value="1.000000" />
          <weight value="5" />
          <type>VMT_GEARBOX</type>
        </Item>
        <Item>
          <identifier />
          <modifier value="100" />
          <audioApply value="1.000000" />
          <weight value="5" />
          <type>VMT_GEARBOX</type>
        </Item>
        <Item>
          <identifier />
          <modifier value="200" />
          <audioApply value="1.000000" />
          <weight value="5" />
          <type>VMT_GEARBOX</type>
        </Item>
        <Item>
          <identifier />
          <modifier value="10" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_SUSPENSION</type>
        </Item>
        <Item>
          <identifier />
          <modifier value="20" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_SUSPENSION</type>
        </Item>
        <Item>
          <identifier />
          <modifier value="30" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_SUSPENSION</type>
        </Item>
        <Item>
          <identifier />
          <modifier value="40" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_SUSPENSION</type>
        </Item>
        <Item>
          <identifier />
          <modifier value="20" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_ARMOUR</type>
        </Item>
        <Item>
          <identifier />
          <modifier value="40" />
          <audioApply value="1.000000" />
          <weight value="10" />
          <type>VMT_ARMOUR</type>
        </Item>
        <Item>
          <identifier />
          <modifier value="60" />
          <audioApply value="1.000000" />
          <weight value="20" />
          <type>VMT_ARMOUR</type>
        </Item>
        <Item>
          <identifier />
          <modifier value="80" />
          <audioApply value="1.000000" />
          <weight value="30" />
          <type>VMT_ARMOUR</type>
        </Item>
        <Item>
          <identifier />
          <modifier value="100" />
          <audioApply value="1.000000" />
          <weight value="40" />
          <type>VMT_ARMOUR</type>
        </Item>
        <Item>
          <identifier>HORN_TRUCK</identifier>
          <modifier value="1766676233" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>HORN_COP</identifier>
          <modifier value="2904189469" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>HORN_CLOWN</identifier>
          <modifier value="2543206147" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>HORN_MUSICAL_1</identifier>
          <modifier value="1732399718" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>HORN_MUSICAL_2</identifier>
          <modifier value="2046162893" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>HORN_MUSICAL_3</identifier>
          <modifier value="2194999691" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>HORN_MUSICAL_4</identifier>
          <modifier value="2508304100" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>HORN_MUSICAL_5</identifier>
          <modifier value="3707223535" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
		<Item>
          <identifier>HORN_SAD_TROMBONE</identifier>
          <modifier value="632950117" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>MUSICAL_HORN_BUSINESS_1</identifier>
          <modifier value="3628534289" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>MUSICAL_HORN_BUSINESS_2</identifier>
          <modifier value="3892554122" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>MUSICAL_HORN_BUSINESS_3</identifier>
          <modifier value="4112892878" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>MUSICAL_HORN_BUSINESS_4</identifier>
          <modifier value="116877169" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>MUSICAL_HORN_BUSINESS_5</identifier>
          <modifier value="2684983719" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>MUSICAL_HORN_BUSINESS_6</identifier>
          <modifier value="2982690084" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
		</Item>
		<Item>
          <identifier>MUSICAL_HORN_BUSINESS_7</identifier>
          <modifier value="3203290992" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
		</Item>
		<Item>
          <identifier>DLC_BUSI2_C_MAJOR_NOTES_C0</identifier>
          <modifier value="771284519" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
		</Item>
		<Item>
          <identifier>DLC_BUSI2_C_MAJOR_NOTES_D0</identifier>
          <modifier value="2586621229" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
		</Item>
		<Item>
          <identifier>DLC_BUSI2_C_MAJOR_NOTES_E0</identifier>
          <modifier value="283386134" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
		</Item>
		<Item>
          <identifier>DLC_BUSI2_C_MAJOR_NOTES_F0</identifier>
          <modifier value="3884502400" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
		</Item>
		<Item>
          <identifier>DLC_BUSI2_C_MAJOR_NOTES_G0</identifier>
          <modifier value="265723083" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
		</Item>
		<Item>
          <identifier>DLC_BUSI2_C_MAJOR_NOTES_A0</identifier>
          <modifier value="1746883687" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
		</Item>
		<Item>
          <identifier>DLC_BUSI2_C_MAJOR_NOTES_B0</identifier>
          <modifier value="1919870950" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
		</Item>
		<Item>
          <identifier>DLC_BUSI2_C_MAJOR_NOTES_C1</identifier>
          <modifier value="1085277077" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
		</Item>
        <Item>
          <identifier>HIPSTER_HORN_1</identifier>
          <modifier value="444549672" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>HIPSTER_HORN_2</identifier>
          <modifier value="1603064898" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>HIPSTER_HORN_3</identifier>
          <modifier value="240366033" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>HIPSTER_HORN_4</identifier>
          <modifier value="960137118" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>INDEP_HORN_1</identifier>
          <modifier value="3572144790" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>INDEP_HORN_2</identifier>
          <modifier value="3801396714" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>INDEP_HORN_3</identifier>
          <modifier value="2843657151" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>INDEP_HORN_4</identifier>
          <modifier value="3341811489" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>LUXE_HORN_1</identifier>
          <modifier value="3199657341" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
    <Item>
          <identifier>LUXE_HORN_2</identifier>
          <modifier value="2900378064" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
    <Item>
          <identifier>LUXE_HORN_3</identifier>
          <modifier value="3956195248" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
		<!-- PREVIEW HORN - FOR MOD SHOP USE ONLY -->
		<Item>
          <identifier>LUXORY_HORN_1</identifier>
          <modifier value="676333254" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
		<!-- PREVIEW HORN - FOR MOD SHOP USE ONLY -->
		<Item>
          <identifier>LUXURY_HORN_2</identifier>
          <modifier value="2099578296" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
		<!-- PREVIEW HORN - FOR MOD SHOP USE ONLY -->
		<Item>
          <identifier>LUXURY_HORN_3</identifier>
          <modifier value="1373384483" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
		<Item>
          <identifier>ORGAN_HORN_LOOP_01</identifier>
          <modifier value="2916775806" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
		<!-- PREVIEW HORN - FOR MOD SHOP USE ONLY -->
		<Item>
          <identifier>ORGAN_HORN_LOOP_01_PREVIEW</identifier>
          <modifier value="3714706952" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
		<Item>
          <identifier>ORGAN_HORN_LOOP_02</identifier>
          <modifier value="2611860261" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
		<!-- PREVIEW HORN - FOR MOD SHOP USE ONLY -->
		<Item>
          <identifier>ORGAN_HORN_LOOP_02_PREVIEW</identifier>
          <modifier value="3206770359" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
		<Item>
          <identifier>LOWRIDER_HORN_1</identifier>
          <modifier value="310529291" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
		<!-- PREVIEW HORN - FOR MOD SHOP USE ONLY -->
		<Item>
          <identifier>LOWRIDER_HORN_1_PREVIEW</identifier>
          <modifier value="2965568987" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
		<Item>
          <identifier>LOWRIDER_HORN_2</identifier>
          <modifier value="55291550" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
		<!-- PREVIEW HORN - FOR MOD SHOP USE ONLY -->
		<Item>
          <identifier>LOWRIDER_HORN_2_PREVIEW</identifier>
          <modifier value="965054819" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
		<Item>
          <identifier>XM15_HORN_01</identifier>
          <modifier value="55862314" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
		<!-- PREVIEW HORN - FOR MOD SHOP USE ONLY -->
		<Item>
          <identifier>XM15_HORN_01_PREVIEW</identifier>
          <modifier value="2156743178" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
		<Item>
          <identifier>XM15_HORN_02</identifier>
          <modifier value="400002352" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
		<!-- PREVIEW HORN - FOR MOD SHOP USE ONLY -->
		<Item>
          <identifier>XM15_HORN_02_PREVIEW</identifier>
          <modifier value="897484282" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
		<Item>
          <identifier>XM15_HORN_03</identifier>
          <modifier value="560832604" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
		<!-- PREVIEW HORN - FOR MOD SHOP USE ONLY -->
		<Item>
          <identifier>XM15_HORN_03_PREVIEW</identifier>
          <modifier value="314232747" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
   		<Item>
          <identifier>DLC_AW_Airhorn_01</identifier>
          <modifier value="3851180092" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
		<!-- PREVIEW HORN - FOR MOD SHOP USE ONLY -->
		<Item>
          <identifier>DLC_AW_Airhorn_01_Preview</identifier>
          <modifier value="246182814" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
		<Item>
          <identifier>DLC_AW_Airhorn_02</identifier>
          <modifier value="3412861948" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
		<!-- PREVIEW HORN - FOR MOD SHOP USE ONLY -->
		<Item>
          <identifier>DLC_AW_Airhorn_02_Preview</identifier>
          <modifier value="1804608241" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
		<Item>
          <identifier>DLC_AW_Airhorn_03</identifier>
          <modifier value="3374260066" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
		<!-- PREVIEW HORN - FOR MOD SHOP USE ONLY -->
		<Item>
          <identifier>DLC_AW_Airhorn_03_Preview</identifier>
          <modifier value="2798044638" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
      </statMods>
       <slotNames>
       <Item>
          <slot>VMT_DOOR_L</slot>
          <name>POL_TAGS</name>
        </Item>
      </slotNames>
	  <liveryNames />
      </Item>
 </Kits>
 <Sirens>
    <Item>
      <id value="7686787"/>
		  <name>EXPOLALAMO_SIRENS</name>      
      <timeMultiplier value="1.00000000"/>
		  <lightFalloffMax value="80.00000000"/>
		  <lightFalloffExponent value="55.00000000"/>
		  <lightInnerConeAngle value="2.00000000"/>
	    <lightOuterConeAngle value="70.00000000"/>
	    <lightOffset value="0.00000000"/>
	    <textureName>VehicleLight_sirenlight</textureName>
      <sequencerBpm value="200"/>
		  <leftHeadLight>
		    <sequencer value="0"/>
		  </leftHeadLight>
		  <rightHeadLight>
		    <sequencer value="0"/>
		  </rightHeadLight>
		  <leftTailLight>
		    <sequencer value="1431655765"/>
		  </leftTailLight>
		  <rightTailLight>
		    <sequencer value="2863311530"/>
		  </rightTailLight>
		  <leftHeadLightMultiples value="1"/>
		  <rightHeadLightMultiples value="1"/>
	    <leftTailLightMultiples value="1"/>
	    <rightTailLightMultiples value="1"/>
		  <useRealLights value="true"/>
		    <sirens>                
          <!--Siren 1 : Red-->
        <Item>
          <rotation>
            <delta value="0.00000000" />
            <start value="0.00000000" />
            <speed value="3.00000000" />
            <sequencer value="4294967295" />
            <multiples value="1" />
            <direction value="false" />
            <syncToBpm value="true" />
          </rotation>
          <flashiness>
            <delta value="1.57079633" />
            <start value="0.00000000" />
            <speed value="0.00000000" />
            <sequencer value="2840947370" />
            <multiples value="3" />
            <direction value="true" />
            <syncToBpm value="true" />
          </flashiness>
          <corona>
            <intensity value="50.00000000" />
            <size value="0.10000000" />
            <pull value="0.15000000" />
            <faceCamera value="false" />
          </corona>
          <color value="0xFFFF0000" />
          <intensity value="1.00000000" />
          <lightGroup value="2" />
          <rotate value="false" />
          <scale value="true" />
          <scaleFactor value="4" />
          <flash value="true" />
          <light value="true" />
          <spotLight value="true" />
          <castShadows value="false" />
        </Item>
        <Item>
          <!--Siren 2 : Red-->
          <rotation>
            <delta value="0.00000000" />
            <start value="0.00000000" />
            <speed value="3.00000000" />
            <sequencer value="4294967295" />
            <multiples value="1" />
            <direction value="false" />
            <syncToBpm value="true" />
          </rotation>
          <flashiness>
            <delta value="3.14159300" />
            <start value="0.00000000" />
            <speed value="0.00000000" />
            <sequencer value="2841029290" />
            <multiples value="2" />
            <direction value="true" />
            <syncToBpm value="true" />
          </flashiness>
          <corona>
            <intensity value="50.00000000" />
            <size value="0.10000000" />
            <pull value="0.15000000" />
            <faceCamera value="false" />
          </corona>
          <color value="0xFFFF0000" />
          <intensity value="1.00000000" />
          <lightGroup value="1" />
          <rotate value="false" />
          <scale value="true" />
          <scaleFactor value="2" />
          <flash value="true" />
          <light value="true" />
          <spotLight value="true" />
          <castShadows value="false" />
        </Item>
        <Item>
          <!--Siren 3 : Amber-->
          <rotation>
            <delta value="0.00000000" />
            <start value="0.00000000" />
            <speed value="3.00000000" />
            <sequencer value="4294967295" />
            <multiples value="1" />
            <direction value="false" />
            <syncToBpm value="true" />
          </rotation>
          <flashiness>
            <delta value="3.14159300" />
            <start value="0.00000000" />
            <speed value="0.00000000" />
            <sequencer value="2863224406" />
            <multiples value="2" />
            <direction value="true" />
            <syncToBpm value="true" />
          </flashiness>
          <corona>
            <intensity value="50.00000000" />
            <size value="0.10000000" />
            <pull value="0.15000000" />
            <faceCamera value="false" />
          </corona>
          <color value="0xFFFFD700" />
          <intensity value="1.00000000" />
          <lightGroup value="1" />
          <rotate value="false" />
          <scale value="true" />
          <scaleFactor value="2" />
          <flash value="true" />
          <light value="true" />
          <spotLight value="true" />
          <castShadows value="false" />
        </Item>
        <Item>
          <!--Siren 4 : Amber-->
          <rotation>
            <delta value="-0.01000000" />
            <start value="0.00000000" />
            <speed value="3.00000000" />
            <sequencer value="4294967295" />
            <multiples value="1" />
            <direction value="false" />
            <syncToBpm value="true" />
          </rotation>
          <flashiness>
            <delta value="3.14159300" />
            <start value="0.00000000" />
            <speed value="0.00000000" />
            <sequencer value="2863306326" />
            <multiples value="3" />
            <direction value="true" />
            <syncToBpm value="true" />
          </flashiness>
          <corona>
            <intensity value="50.00000000" />
            <size value="0.10000000" />
            <pull value="0.15000000" />
            <faceCamera value="false" />
          </corona>
          <color value="0xFFFFD700" />
          <intensity value="1.00000000" />
          <lightGroup value="1" />
          <rotate value="false" />
          <scale value="true" />
          <scaleFactor value="2" />
          <flash value="true" />
          <light value="true" />
          <spotLight value="true" />
          <castShadows value="false" />
        </Item>
        <Item>
          <!--Siren 5 : Amber-->
          <rotation>
            <delta value="-0.01000000" />
            <start value="0.00000000" />
            <speed value="3.00000000" />
            <sequencer value="4294967295" />
            <multiples value="1" />
            <direction value="false" />
            <syncToBpm value="true" />
          </rotation>
          <flashiness>
            <delta value="3.14159300" />
            <start value="0.00000000" />
            <speed value="0.00000000" />
            <sequencer value="1453938089" />
            <multiples value="2" />
            <direction value="true" />
            <syncToBpm value="true" />
          </flashiness>
          <corona>
            <intensity value="50.00000000" />
            <size value="0.10000000" />
            <pull value="0.15000000" />
            <faceCamera value="false" />
          </corona>
          <color value="0xFFFFD700" />
          <intensity value="1.00000000" />
          <lightGroup value="1" />
          <rotate value="false" />
          <scale value="true" />
          <scaleFactor value="2" />
          <flash value="true" />
          <light value="true" />
          <spotLight value="true" />
          <castShadows value="false" />
        </Item>
        <Item>
          <!--Siren 6 : Amber-->
          <rotation>
            <delta value="-0.01000000" />
            <start value="0.00000000" />
            <speed value="3.00000000" />
            <sequencer value="4294967295" />
            <multiples value="1" />
            <direction value="false" />
            <syncToBpm value="true" />
          </rotation>
          <flashiness>
            <delta value="3.14159300" />
            <start value="0.00000000" />
            <speed value="0.00000000" />
            <sequencer value="1454020009" />
            <multiples value="2" />
            <direction value="true" />
            <syncToBpm value="true" />
          </flashiness>
          <corona>
            <intensity value="50.00000000" />
            <size value="0.10000000" />
            <pull value="0.15000000" />
            <faceCamera value="false" />
          </corona>
          <color value="0xFFFFD700" />
          <intensity value="1.00000000" />
          <lightGroup value="1" />
          <rotate value="false" />
          <scale value="true" />
          <scaleFactor value="2" />
          <flash value="true" />
          <light value="true" />
          <spotLight value="true" />
          <castShadows value="false" />
        </Item>
        <Item>
          <!--Siren 7 : Red-->
          <rotation>
            <delta value="0.00000000" />
            <start value="0.00000000" />
            <speed value="3.00000000" />
            <sequencer value="4294967295" />
            <multiples value="1" />
            <direction value="false" />
            <syncToBpm value="true" />
          </rotation>
          <flashiness>
            <delta value="0.00000000" />
            <start value="0.00000000" />
            <speed value="0.00000000" />
            <sequencer value="2863224406" />
            <multiples value="2" />
            <direction value="true" />
            <syncToBpm value="true" />
          </flashiness>
          <corona>
            <intensity value="50.00000000" />
            <size value="0.10000000" />
            <pull value="0.15000000" />
            <faceCamera value="false" />
          </corona>
          <color value="0xFFFF0000" />
          <intensity value="1.00000000" />
          <lightGroup value="1" />
          <rotate value="false" />
          <scale value="true" />
          <scaleFactor value="2" />
          <flash value="true" />
          <light value="true" />
          <spotLight value="true" />
          <castShadows value="false" />
        </Item>
        <Item>
          <!--Siren 8 : Blue-->
          <rotation>
            <delta value="-0.01000000" />
            <start value="0.00000000" />
            <speed value="3.00000000" />
            <sequencer value="4294967295" />
            <multiples value="1" />
            <direction value="false" />
            <syncToBpm value="true" />
          </rotation>
          <flashiness>
            <delta value="0.00000000" />
            <start value="0.00000000" />
            <speed value="0.00000000" />
            <sequencer value="1454020009" />
            <multiples value="2" />
            <direction value="false" />
            <syncToBpm value="true" />
          </flashiness>
          <corona>
            <intensity value="50.00000000" />
            <size value="0.10000000" />
            <pull value="0.15000000" />
            <faceCamera value="false" />
          </corona>
          <color value="0xFF000AFF" />
          <intensity value="1.00000000" />
          <lightGroup value="1" />
          <rotate value="false" />
          <scale value="true" />
          <scaleFactor value="2" />
          <flash value="true" />
          <light value="true" />
          <spotLight value="true" />
          <castShadows value="false" />
        </Item>
        <Item>
          <!--Siren 9 : Blue-->
          <rotation>
            <delta value="0.00000000" />
            <start value="0.00000000" />
            <speed value="3.00000000" />
            <sequencer value="4294967295" />
            <multiples value="1" />
            <direction value="false" />
            <syncToBpm value="true" />
          </rotation>
          <flashiness>
            <delta value="3.14159300" />
            <start value="0.00000000" />
            <speed value="0.00000000" />
            <sequencer value="1431660885" />
            <multiples value="2" />
            <direction value="true" />
            <syncToBpm value="true" />
          </flashiness>
          <corona>
            <intensity value="50.00000000" />
            <size value="0.10000000" />
            <pull value="0.15000000" />
            <faceCamera value="false" />
          </corona>
          <color value="0xFF000AFF" />
          <intensity value="1.00000000" />
          <lightGroup value="1" />
          <rotate value="false" />
          <scale value="true" />
          <scaleFactor value="2" />
          <flash value="true" />
          <light value="true" />
          <spotLight value="true" />
          <castShadows value="false" />
        </Item>
        <Item>
          <!--Siren 10 : Blue-->
          <rotation>
            <delta value="-0.01000000" />
            <start value="0.00000000" />
            <speed value="3.00000000" />
            <sequencer value="4294967295" />
            <multiples value="1" />
            <direction value="false" />
            <syncToBpm value="true" />
          </rotation>
          <flashiness>
            <delta value="-1.57079633" />
            <start value="0.00000000" />
            <speed value="0.00000000" />
            <sequencer value="1431742805" />
            <multiples value="2" />
            <direction value="false" />
            <syncToBpm value="true" />
          </flashiness>
          <corona>
            <intensity value="50.00000000" />
            <size value="0.10000000" />
            <pull value="0.15000000" />
            <faceCamera value="false" />
          </corona>
          <color value="0xFF000AFF" />
          <intensity value="1.00000000" />
          <lightGroup value="1" />
          <rotate value="false" />
          <scale value="true" />
          <scaleFactor value="4" />
          <flash value="true" />
          <light value="true" />
          <spotLight value="true" />
          <castShadows value="false" />
        </Item>           
        <!--Siren 11 : Red -->
        <Item>
            <rotation>
                <delta value="3.14159265"/>
                <start value="0.00000000"/>
                <speed value="1.00000000"/>
                <sequencer value="4294967295"/>
                <multiples value="1"/>
                <direction value="false"/>
                <syncToBpm value="true"/>
            </rotation>
            <flashiness>
                <delta value="3.14159265"/>
                <start value="0.00000000"/>
                <speed value="1.00000000"/>
                <sequencer value="2841029290"/>
                <multiples value="1"/>
                <direction value="true"/>
                <syncToBpm value="true"/>
            </flashiness>
            <corona>
                <intensity value="40.00000000"/>
                <size value="0.60000000"/>
                <pull value="0.01500000"/>
                <faceCamera value="false"/>
            </corona>
            <color value="0xFFFF0000"/>
            <intensity value="1.00000000"/>
            <lightGroup value="0"/>
            <rotate value="false"/>
            <scale value="true"/>
            <scaleFactor value="2"/>
            <flash value="true"/>
            <light value="true"/>
            <spotLight value="true"/>
            <castShadows value="false"/>
        </Item>                
        <!--Siren 12 : Amber -->
        <Item>
            <rotation>
                <delta value="3.14159265"/>
                <start value="0.00000000"/>
                <speed value="1.00000000"/>
                <sequencer value="4294967295"/>
                <multiples value="1"/>
                <direction value="false"/>
                <syncToBpm value="true"/>
            </rotation>
            <flashiness>
                <delta value="3.14159265"/>
                <start value="0.00000000"/>
                <speed value="1.00000000"/>
                <sequencer value="2863224406"/>
                <multiples value="1"/>
                <direction value="true"/>
                <syncToBpm value="true"/>
            </flashiness>
            <corona>
                <intensity value="40.00000000"/>
                <size value="0.60000000"/>
                <pull value="0.01500000"/>
                <faceCamera value="false"/>
            </corona>
            <color value="0xFFFFD700"/>
            <intensity value="1.00000000"/>
            <lightGroup value="0"/>
            <rotate value="false"/>
            <scale value="true"/>
            <scaleFactor value="2"/>
            <flash value="true"/>
            <light value="true"/>
            <spotLight value="true"/>
            <castShadows value="false"/>
        </Item>                
        <!--Siren 13 : Amber -->
        <Item>
            <rotation>
                <delta value="3.14159265"/>
                <start value="0.00000000"/>
                <speed value="1.00000000"/>
                <sequencer value="4294967295"/>
                <multiples value="1"/>
                <direction value="false"/>
                <syncToBpm value="true"/>
            </rotation>
            <flashiness>
                <delta value="3.14159265"/>
                <start value="0.00000000"/>
                <speed value="1.00000000"/>
                <sequencer value="1454020009"/>
                <multiples value="1"/>
                <direction value="true"/>
                <syncToBpm value="true"/>
            </flashiness>
            <corona>
                <intensity value="40.00000000"/>
                <size value="0.60000000"/>
                <pull value="0.01500000"/>
                <faceCamera value="false"/>
            </corona>
            <color value="0xFFFFD700"/>
            <intensity value="1.00000000"/>
            <lightGroup value="0"/>
            <rotate value="false"/>
            <scale value="true"/>
            <scaleFactor value="2"/>
            <flash value="true"/>
            <light value="true"/>
            <spotLight value="true"/>
            <castShadows value="false"/>
        </Item>                
        <!--Siren 14 : Blue -->
        <Item>
            <rotation>
                <delta value="3.14159265"/>
                <start value="0.00000000"/>
                <speed value="1.00000000"/>
                <sequencer value="4294967295"/>
                <multiples value="1"/>
                <direction value="false"/>
                <syncToBpm value="true"/>
            </rotation>
            <flashiness>
                <delta value="3.14159265"/>
                <start value="0.00000000"/>
                <speed value="1.00000000"/>
                <sequencer value="1431660885"/>
                <multiples value="1"/>
                <direction value="true"/>
                <syncToBpm value="true"/>
            </flashiness>
            <corona>
                <intensity value="40.00000000"/>
                <size value="0.60000000"/>
                <pull value="0.01500000"/>
                <faceCamera value="false"/>
            </corona>
            <color value="0xFF000AFF"/>
            <intensity value="1.00000000"/>
            <lightGroup value="0"/>
            <rotate value="false"/>
            <scale value="true"/>
            <scaleFactor value="2"/>
            <flash value="true"/>
            <light value="true"/>
            <spotLight value="true"/>
            <castShadows value="false"/>
        </Item>                
        <!--Siren 15 : Blue-->
        <Item>
            <rotation>
                <delta value="0.00000000"/>
                <start value="0.00000000"/>
                <speed value="1.00000000"/>
                <sequencer value="4294967295"/>
                <multiples value="1"/>
                <direction value="false"/>
                <syncToBpm value="true"/>
            </rotation>
            <flashiness>
                <delta value="0.00000000"/>
                <start value="0.00000000"/>
                <speed value="1.00000000"/>
                <sequencer value="2863311530"/>
                <multiples value="1"/>
                <direction value="true"/>
                <syncToBpm value="true"/>
            </flashiness>
            <corona>
                <intensity value="40.00000000"/>
                <size value="0.60000000"/>
                <pull value="0.01500000"/>
                <faceCamera value="false"/>
            </corona>
            <color value="0xFF0000FF"/>
            <intensity value="1.00000000"/>
            <lightGroup value="0"/>
            <rotate value="false"/>
            <scale value="true"/>
            <scaleFactor value="2"/>
            <flash value="true"/>
            <light value="true"/>
            <spotLight value="true"/>
            <castShadows value="false"/>
        </Item>                
        <!--Siren 16 : Blue-->
        <Item>
            <rotation>
                <delta value="0.00000000"/>
                <start value="0.00000000"/>
                <speed value="1.00000000"/>
                <sequencer value="4294967295"/>
                <multiples value="1"/>
                <direction value="false"/>
                <syncToBpm value="true"/>
            </rotation>
            <flashiness>
                <delta value="0.00000000"/>
                <start value="0.00000000"/>
                <speed value="1.00000000"/>
                <sequencer value="2840943274"/>
                <multiples value="1"/>
                <direction value="true"/>
                <syncToBpm value="true"/>
            </flashiness>
            <corona>
                <intensity value="40.00000000"/>
                <size value="0.60000000"/>
                <pull value="0.01500000"/>
                <faceCamera value="false"/>
            </corona>
            <color value="0xFF0000FF"/>
            <intensity value="1.00000000"/>
            <lightGroup value="0"/>
            <rotate value="false"/>
            <scale value="true"/>
            <scaleFactor value="2"/>
            <flash value="true"/>
            <light value="true"/>
            <spotLight value="true"/>
            <castShadows value="false"/>
        </Item>                
        <!--Siren 17 : Red -->
        <Item>
            <rotation>
                <delta value="0.00000000"/>
                <start value="0.00000000"/>
                <speed value="1.00000000"/>
                <sequencer value="4294967295"/>
                <multiples value="1"/>
                <direction value="false"/>
                <syncToBpm value="true"/>
            </rotation>
            <flashiness>
                <delta value="0.00000000"/>
                <start value="0.00000000"/>
                <speed value="1.00000000"/>
                <sequencer value="1454024021"/>
                <multiples value="1"/>
                <direction value="true"/>
                <syncToBpm value="true"/>
            </flashiness>
            <corona>
                <intensity value="40.00000000"/>
                <size value="0.60000000"/>
                <pull value="0.01500000"/>
                <faceCamera value="false"/>
            </corona>
            <color value="0xFFFF0000"/>
            <intensity value="1.00000000"/>
            <lightGroup value="0"/>
            <rotate value="false"/>
            <scale value="true"/>
            <scaleFactor value="2"/>
            <flash value="true"/>
            <light value="true"/>
            <spotLight value="true"/>
            <castShadows value="false"/>
        </Item>                
        <!--Siren 18 : Blue -->
        <Item>
            <rotation>
                <delta value="0.00000000"/>
                <start value="0.00000000"/>
                <speed value="1.00000000"/>
                <sequencer value="4294967295"/>
                <multiples value="1"/>
                <direction value="false"/>
                <syncToBpm value="true"/>
            </rotation>
            <flashiness>
                <delta value="0.00000000"/>
                <start value="0.00000000"/>
                <speed value="1.00000000"/>
                <sequencer value="1431655765"/>
                <multiples value="1"/>
                <direction value="true"/>
                <syncToBpm value="true"/>
            </flashiness>
            <corona>
                <intensity value="40.00000000"/>
                <size value="0.60000000"/>
                <pull value="0.01500000"/>
                <faceCamera value="false"/>
            </corona>
            <color value="0xFFFF0000"/>
            <intensity value="1.00000000"/>
            <lightGroup value="0"/>
            <rotate value="false"/>
            <scale value="true"/>
            <scaleFactor value="2"/>
            <flash value="true"/>
            <light value="true"/>
            <spotLight value="true"/>
            <castShadows value="false"/>
        </Item>                
        <!--Siren 19 : Red -->
        <Item>
            <rotation>
                <delta value="0.00000000"/>
                <start value="0.00000000"/>
                <speed value="1.00000000"/>
                <sequencer value="4294967295"/>
                <multiples value="1"/>
                <direction value="false"/>
                <syncToBpm value="true"/>
            </rotation>
            <flashiness>
                <delta value="0.00000000"/>
                <start value="0.00000000"/>
                <speed value="1.00000000"/>
                <sequencer value="1431655765"/>
                <multiples value="1"/>
                <direction value="true"/>
                <syncToBpm value="true"/>
            </flashiness>
            <corona>
                <intensity value="40.00000000"/>
                <size value="0.60000000"/>
                <pull value="0.01500000"/>
                <faceCamera value="false"/>
            </corona>
            <color value="0xFFFF0000"/>
            <intensity value="1.00000000"/>
            <lightGroup value="0"/>
            <rotate value="false"/>
            <scale value="true"/>
            <scaleFactor value="2"/>
            <flash value="true"/>
            <light value="true"/>
            <spotLight value="true"/>
            <castShadows value="false"/>
        </Item>                
        <!--Siren 20 : Blue -->
        <Item>
            <rotation>
                <delta value="0.00000000"/>
                <start value="0.00000000"/>
                <speed value="1.00000000"/>
                <sequencer value="4294967295"/>
                <multiples value="1"/>
                <direction value="false"/>
                <syncToBpm value="true"/>
            </rotation>
            <flashiness>
                <delta value="0.00000000"/>
                <start value="0.00000000"/>
                <speed value="1.00000000"/>
                <sequencer value="2863311530"/>
                <multiples value="1"/>
                <direction value="true"/>
                <syncToBpm value="true"/>
            </flashiness>
            <corona>
                <intensity value="40.00000000"/>
                <size value="0.60000000"/>
                <pull value="0.01500000"/>
                <faceCamera value="false"/>
            </corona>
            <color value="0xFF0000FF"/>
            <intensity value="1.00000000"/>
            <lightGroup value="0"/>
            <rotate value="false"/>
            <scale value="true"/>
            <scaleFactor value="2"/>
            <flash value="true"/>
            <light value="true"/>
            <spotLight value="true"/>
            <castShadows value="false"/>
        </Item>
      </sirens>
    </Item>
  </Sirens>

</CVehicleModelInfoVarGlobal>