<?xml version="1.0" encoding="UTF-8"?>
<CVehicleModelInfo__InitDataList>
  <residentTxd>vehshare</residentTxd>
  <residentAnims />
  <InitDatas>
  <Item>
      <modelName>expolalamo</modelName>
      <txdName>expolalamo</txdName>
      <handlingId>expolalamo</handlingId>
      <gameName>expolalamo</gameName>
      <vehicleMakeName>DECLASSE</vehicleMakeName>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected />
      <ptfxAssetName>null</ptfxAssetName>
      <audioNameHash>jubilee</audioNameHash>
      <layout>LAYOUT_5SEATSUV</layout>
      <coverBoundOffsets>GRANGER2_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout />
	  <cameraName>DEFAULT_FOLLOW_VEHICLE_CAMERA</cameraName>
      <aimCameraName>DEFAULT_THIRD_PERSON_VEHICLE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>VEHICLE_BONNET_CAMERA_SPEEDO4</bonnetCameraName>
      <povCameraName>REDUCED_NEAR_CLIP_POV_CAMERA</povCameraName>
      <FirstPersonDriveByIKOffset x="0.030000" y="-0.080000" z="-0.060000" />
      <FirstPersonDriveByUnarmedIKOffset x="-0.020000" y="-0.140000" z="-0.030000" />
	  <FirstPersonProjectileDriveByIKOffset x="0.090000" y="-0.160000" z="-0.060000" />
	  <FirstPersonProjectileDriveByPassengerIKOffset x="-0.090000" y="-0.160000" z="-0.060000" />
	  <FirstPersonProjectileDriveByRearLeftIKOffset x="-0.010000" y="-0.100000" z="-0.050000" />
	  <FirstPersonProjectileDriveByRearRightIKOffset x="0.010000" y="-0.080000" z="-0.070000" />
	  <FirstPersonDriveByLeftPassengerIKOffset x="0.040000" y="-0.050000" z="-0.030000" />
	  <FirstPersonDriveByRightPassengerIKOffset x="-0.030000" y="-0.080000" z="-0.060000" />
	  <FirstPersonDriveByRightRearPassengerIKOffset x="-0.040000" y="-0.050000" z="-0.030000" />
	  <FirstPersonDriveByLeftPassengerUnarmedIKOffset x="0.020000" y="-0.200000" z="-0.020000" />
	  <FirstPersonDriveByRightPassengerUnarmedIKOffset x="-0.020000" y="-0.140000" z="-0.030000" />
	  <FirstPersonMobilePhoneOffset x="0.133000" y="0.193000" z="0.538000" />
      <FirstPersonPassengerMobilePhoneOffset x="0.231000" y="0.193000" z="0.430000" />
	  <FirstPersonMobilePhoneSeatIKOffset>
		<Item>
			<Offset x="0.211000" y="0.123000" z="0.435000" />
			<SeatIndex value="2" />
		</Item>
		<Item>
			<Offset x="0.211000" y="0.123000" z="0.435000" />
			<SeatIndex value="3" />
		</Item>
	  </FirstPersonMobilePhoneSeatIKOffset> 
      <PovCameraOffset x="0.000000" y="-0.190000" z="0.660000" />
      <PovCameraVerticalAdjustmentForRollCage value="0.000000" />
      <PovPassengerCameraOffset x="0.000000" y="0.050000" z="0.000000" />
      <PovRearPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000" />
      <vfxInfoName>VFXVEHICLEINFO_CAR_GENERIC</vfxInfoName>
      <shouldUseCinematicViewMode value="true" />
      <shouldCameraTransitionOnClimbUpDown value="false" />
      <shouldCameraIgnoreExiting value="false" />
      <AllowPretendOccupants value="true" />
      <AllowJoyriding value="true" />
      <AllowSundayDriving value="true" />
      <AllowBodyColorMapping value="true" />
      <wheelScale value="0.288652" />
      <wheelScaleRear value="0.288652" />
      <dirtLevelMin value="0.000000" />
      <dirtLevelMax value="0.300000" />
      <envEffScaleMin value="0.000000" />
      <envEffScaleMax value="1.000000" />
      <envEffScaleMin2 value="0.000000" />
      <envEffScaleMax2 value="1.000000" />
      <damageMapScale value="0.400000" />
      <damageOffsetScale value="0.400000" />
      <diffuseTint value="0xC4000000" />
      <steerWheelMult value="1.000000" />
      <HDTextureDist value="5.000000" />
      <lodDistances content="float_array">
        15.000000
        30.000000
        70.000000	
        140.000000	
        300.000000	
        300.000000
      </lodDistances>
      <minSeatHeight value="0.481" />
      <identicalModelSpawnDistance value="20" />
      <maxNumOfSameColor value="1" />
      <defaultBodyHealth value="1000.000000" />
      <pretendOccupantsScale value="1.000000" />
      <visibleSpawnDistScale value="1.000000" />
      <trackerPathWidth value="2.000000" />
      <weaponForceMult value="1.000000" />
      <frequency value="50" />
      <swankness>SWANKNESS_5</swankness>
      <maxNum value="5" />
      <flags>FLAG_SPOILER_MOD_DOESNT_INCREASE_GRIP FLAG_AVERAGE_CAR
      FLAG_EXTRAS_STRONG FLAG_LAW_ENFORCEMENT FLAG_EMERGENCY_SERVICE FLAG_DONT_SPAWN_IN_CARGEN FLAG_REPORT_CRIME_IF_STANDING_ON FLAG_EXTRAS_ALL</flags>
      <type>VEHICLE_TYPE_CAR</type>
      <plateType>VPT_BACK_PLATES</plateType>
      <dashboardType>VDT_RACE</dashboardType>
      <vehicleClass>VC_EMERGENCY</vehicleClass>
      <wheelType>VWT_HIEND</wheelType>
      <trailers />
      <additionalTrailers />
      <drivers />
      <extraIncludes />
      <doorsWithCollisionWhenClosed />
      <driveableDoors />
      <bumpersNeedToCollideWithMap value="false" />
      <needsRopeTexture value="false" />
      <requiredExtras />
      <rewards />
      <cinematicPartCamera>
        <Item>WHEEL_FRONT_RIGHT_CAMERA</Item>
        <Item>WHEEL_FRONT_LEFT_CAMERA</Item>
        <Item>WHEEL_REAR_RIGHT_CAMERA</Item>
        <Item>WHEEL_REAR_LEFT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet />
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000" />
      <buoyancySphereSizeScale value="1.000000" />
      <pOverrideRagdollThreshold type="NULL" />
	  <firstPersonDrivebyData>
        <Item>STD_VSTR_FRONT_LEFT</Item>
        <Item>STD_VSTR_FRONT_RIGHT</Item>
		<Item>STD_VSTR_REAR_LEFT</Item>
		<Item>STD_VSTR_REAR_RIGHT</Item>
      </firstPersonDrivebyData>
    </Item>
  </InitDatas>
    <txdRelationships>
    <Item>
      <parent>vehicles_sup1_interior</parent>
      <child>vehicles_expolalamo_interior</child>
    </Item>
	    <Item>
      <parent>vehicles_expolalamo_interior</parent>
      <child>expolalamo</child>
    </Item>
</txdRelationships>
</CVehicleModelInfo__InitDataList>