<?xml version="1.0" encoding="UTF-8"?>
<CVehicleModelInfo__InitDataList>
  <residentTxd>vehshare</residentTxd>
  <residentAnims />
  <InitDatas>
 <Item>
      <modelName>sent5pd</modelName>
      <txdName>sent5pd</txdName>
      <handlingId>sent5pd</handlingId>
      <gameName>sent5pd</gameName>
      <vehicleMakeName>UBERMACHT</vehicleMakeName>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected />
      <ptfxAssetName>null</ptfxAssetName>
      <audioNameHash>CYPHER</audioNameHash>
      <layout>LAYOUT_STANDARD</layout>
      <coverBoundOffsets>SENTINEL5_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout />
      <cameraName>FOLLOW_CHEETAH_CAMERA</cameraName>
      <aimCameraName>BOX_VEHICLE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>VEHICLE_BONNET_CAMERA_STANDARD_LONG_EXTRA_LOW</bonnetCameraName>
      <povCameraName>REDUCED_NEAR_CLIP_POV_CAMERA</povCameraName>
      <FirstPersonDriveByIKOffset x="0.050000" y="-0.100000" z="-0.050000" />
      <FirstPersonDriveByUnarmedIKOffset x="0.000000" y="-0.020000" z="-0.010000" />
	  <FirstPersonProjectileDriveByIKOffset x="0.080000" y="-0.110000" z="-0.070000" />
	  <FirstPersonProjectileDriveByPassengerIKOffset x="-0.050000" y="-0.140000" z="-0.050000" />
	  <FirstPersonDriveByRightPassengerIKOffset x="-0.050000" y="-0.090000" z="-0.050000" />
	  <FirstPersonDriveByRightPassengerUnarmedIKOffset x="0.000000" y="-0.040000" z="-0.020000" />
	  <FirstPersonMobilePhoneOffset x="0.125000" y="0.160000" z="0.550000" />
	  <FirstPersonProjectileDriveByRearLeftIKOffset x="0.000000" y="0.000000" z="-0.10000" />
	  <FirstPersonProjectileDriveByRearRightIKOffset x="0.000000" y="0.000000" z="-0.10000" />
      <FirstPersonPassengerMobilePhoneOffset x="0.223000" y="0.160000" z="0.415000" />
	  <FirstPersonMobilePhoneSeatIKOffset />
      <PovCameraOffset x="0.000000" y="-0.100000" z="0.6650000" />
      <PovCameraVerticalAdjustmentForRollCage value="-0.025000" />
      <PovPassengerCameraOffset x="0.000000" y="0.000000" z="0.00000" />
      <PovRearPassengerCameraOffset x="0.000000" y="-0.200000" z="0.000000" />
      <vfxInfoName>VFXVEHICLEINFO_CAR_GENERIC</vfxInfoName>
      <shouldUseCinematicViewMode value="true" />
      <shouldCameraTransitionOnClimbUpDown value="false" />
      <shouldCameraIgnoreExiting value="false" />
      <AllowPretendOccupants value="true" />
      <AllowJoyriding value="false" />
      <AllowSundayDriving value="false" />
      <AllowBodyColorMapping value="true" />
      <wheelScale value="0.265500" /> 
      <wheelScaleRear value="0.265500" />
      <dirtLevelMin value="0.000000" />
      <dirtLevelMax value="1.000000" />
      <envEffScaleMin value="0.300000" />
      <envEffScaleMax value="0.400000" />
      <envEffScaleMin2 value="0.000000" />
      <envEffScaleMax2 value="1.000000" />
      <damageMapScale value="0.400000" />
      <damageOffsetScale value="0.400000" />
      <diffuseTint value="0x00FFFFFF" />
      <steerWheelMult value="0.650000" />
      <HDTextureDist value="5.000000" />
      <lodDistances content="float_array">
        15.000000
        25.000000
        70.000000
        140.000000
        500.000000
        500.000000
      </lodDistances>
      <minSeatHeight value="0.988" />
      <identicalModelSpawnDistance value="20" />
      <maxNumOfSameColor value="10" />
      <defaultBodyHealth value="1000.000000" />
      <pretendOccupantsScale value="1.000000" />
      <visibleSpawnDistScale value="1.000000" />
      <trackerPathWidth value="2.000000" />
      <weaponForceMult value="1.000000" />
      <frequency value="5" />
      <swankness>SWANKNESS_3</swankness>
      <maxNum value="1" />
      <flags>FLAG_LAW_ENFORCEMENT FLAG_EMERGENCY_SERVICE FLAG_HAS_LIVERY FLAG_SPORTS FLAG_SPAWN_ON_TRAILER FLAG_RICH_CAR FLAG_NO_BROKEN_DOWN_SCENARIO FLAG_CAN_HAVE_NEONS FLAG_RECESSED_HEADLIGHT_CORONAS FLAG_RECESSED_TAILLIGHT_CORONAS</flags>
      <type>VEHICLE_TYPE_CAR</type>
      <plateType>VPT_BACK_PLATES</plateType>
      <dashboardType>VDT_RACE</dashboardType>
      <vehicleClass>VC_EMERGENCY</vehicleClass>
      <wheelType>VWT_HIEND</wheelType>
      <trailers />
      <additionalTrailers />
      <drivers />
      <extraIncludes />
      <doorsWithCollisionWhenClosed>
        <Item>VEH_EXT_BOOT</Item>
      </doorsWithCollisionWhenClosed>
      <driveableDoors />
      <bumpersNeedToCollideWithMap value="false" />
      <needsRopeTexture value="true" />
      <requiredExtras />
      <rewards />
      <cinematicPartCamera>
        <Item>WHEEL_WIDE_REAR_RIGHT_CAMERA</Item>
        <Item>WHEEL_WIDE_REAR_LEFT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet>Truck</NmBraceOverrideSet>
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000" />
      <buoyancySphereSizeScale value="1.000000" />
      <pOverrideRagdollThreshold type="NULL" />
	  <firstPersonDrivebyData>
        <Item>STD_SENTINEL5_FRONT_LEFT</Item>
        <Item>STD_SENTINEL5_FRONT_RIGHT</Item>
		<Item>STD_SENTINEL5_REAR_LEFT</Item>
		<Item>STD_SENTINEL5_REAR_RIGHT</Item>
      </firstPersonDrivebyData>
	  <numSeatsOverride value="2" />
    </Item>
 </InitDatas>
  <txdRelationships>
    <Item>
      <parent>vehicles_cav_interior</parent>
      <child>everon3</child>
    </Item>
    <Item>
      <parent>vehicles_lowrider</parent>
      <child>vehicles_bob_w_interior</child>
    </Item>
  <Item>
      <parent>vehicles_muscle_supercharger</parent>
      <child>vehicles_muscle_n_w_interior</child>
    </Item>
    <Item>
      <parent>vehicles_bob_w_interior</parent>
      <child>l352</child>
    </Item>
    <Item>
      <parent>vehicles_elegy_race</parent>
      <child>driftl352</child>
    </Item>
	<Item>
      <parent>vehicles_muscle_o_race_interior</parent>
      <child>driftdominator10</child>
    </Item>
    <Item>
      <parent>vehicles_muscle_n_w_interior</parent>
      <child>driftgauntlet4</child>
    </Item>
	<Item>
      <parent>vehicles_feroci_interior</parent>
      <child>driftchavosv6</child>
    </Item>
    <Item>
      <parent>vehicles_chopperbk_interior</parent>
      <child>policeb2</child>
    </Item>
    <Item>
      <parent>vehshare_truck</parent>
      <child>stockade4</child>
    </Item>
    <Item>
      <parent>vehshare_truck</parent>
      <child>flatbed2</child>
    </Item>
    <Item>
      <parent>vehicles_jet_interior</parent>
      <child>luxor3</child>
    </Item>
    <Item>
      <parent>vehicles_dom_interior</parent>
      <child>cheetah3</child>
    </Item>
    <Item>
      <parent>vehicles_jugular_interior</parent>
      <child>sent5pd</child>
    </Item>
	<Item>
      <parent>vehicles_elegy_race</parent>
      <child>hardy</child>
    </Item>
	<Item>
      <parent>vehicles_elegy_race</parent>
      <child>drifthardy</child>
    </Item>
    <Item>
      <parent>vehicles_monster_interior</parent>
      <child>tampa4</child>
    </Item>
    <Item>
      <parent>vehicles_flyer_interior</parent>
      <child>maverick2</child>
    </Item>
	<Item>
      <parent>vehicles_sultan_interior</parent>
      <child>woodlander</child>
    </Item>
	<Item>
      <parent>vehicles_muscle_n_w_interior</parent>
      <child>minimus</child>
    </Item>
	<Item>
      <parent>vehicles_proto_w_interior</parent>
      <child>suzume</child>
    </Item>
		<Item>
      <parent>vehicles_fmj_w_race_interior</parent>
      <child>rapidgt4</child>
    </Item>
  </txdRelationships>
</CVehicleModelInfo__InitDataList>
