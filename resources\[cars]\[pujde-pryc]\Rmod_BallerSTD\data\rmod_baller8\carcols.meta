<?xml version="1.0" encoding="UTF-8"?>
<CVehicleModelInfoVarGlobal>    
  <Kits>
   <Item>
      <kitName>4189_rmod_baller8_modkit</kitName>
      <id value="4189" />	 	  
      <kitType>MKT_SPECIAL</kitType>
      <visibleMods>
	  <Item>
          <modelName>baller8_diff_1</modelName>
          <modShopLabel>BALL8_DIFF1</modShopLabel>
          <linkedModels/>
		  <turnOffBones>
		  <Item>misc_a</Item>
		  </turnOffBones> 
          <type>VMT_BUMPER_R</type>
          <bone>bumper_r</bone>
          <collisionBone>bumper_r</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>baller8_diff_2</modelName>
          <modShopLabel>BALL8_DIFF2</modShopLabel>
          <linkedModels/>
		  <turnOffBones>
		  <Item>misc_a</Item>
		  </turnOffBones> 
          <type>VMT_BUMPER_R</type>
          <bone>bumper_r</bone>
          <collisionBone>bumper_r</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>baller8_diff_3</modelName>
          <modShopLabel>BALL8_DIFF3</modShopLabel>
          <linkedModels/>
		  <turnOffBones>
		  <Item>misc_a</Item>
		  </turnOffBones> 
          <type>VMT_BUMPER_R</type>
          <bone>bumper_r</bone>
          <collisionBone>bumper_r</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>baller8_splt_a</modelName>
          <modShopLabel>BALL8_SPLT1</modShopLabel>
          <linkedModels/>
		  <turnOffBones>
		  <Item>misc_b</Item>
		  </turnOffBones> 
          <type>VMT_BUMPER_F</type>
          <bone>bumper_f</bone>
          <collisionBone>bumper_f</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>baller8_splt_2</modelName>
          <modShopLabel>BALL8_SPLT3</modShopLabel>
          <linkedModels/>
		  <turnOffBones>
		  <Item>misc_b</Item>
		  </turnOffBones> 
          <type>VMT_BUMPER_F</type>
          <bone>bumper_f</bone>
          <collisionBone>bumper_f</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
	  	  <Item>
          <modelName>dummy_mod_frag_model</modelName>
          <modShopLabel>RC_UNIT</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_CHASSIS4</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
	   <Item>
          <modelName>dummy_mod_frag_model</modelName>
          <modShopLabel>MLJ_UNIT</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_CHASSIS4</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
	  <Item>
          <modelName>baller8_livery_1</modelName>
          <modShopLabel>BALL8_LIV1</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_LIVERY_MOD</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
	  <Item>
          <modelName>baller8_livery_2</modelName>
          <modShopLabel>BALL8_LIV2</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_LIVERY_MOD</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
	  <Item>
          <modelName>baller8_livery_3</modelName>
          <modShopLabel>BALL8_LIV3</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_LIVERY_MOD</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
	  <Item>
          <modelName>baller8_livery_4</modelName>
          <modShopLabel>BALL8_LIV4</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_LIVERY_MOD</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
	  <Item>
          <modelName>baller8_livery_5</modelName>
          <modShopLabel>BALL8_LIV5</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_LIVERY_MOD</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
	  <Item>
          <modelName>baller8_livery_6</modelName>
          <modShopLabel>BALL8_LIV6</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_LIVERY_MOD</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
	  <Item>
          <modelName>baller8_livery_7</modelName>
          <modShopLabel>BALL8_LIV7</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_LIVERY_MOD</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
	  <Item>
          <modelName>baller8_livery_8</modelName>
          <modShopLabel>BALL8_LIV8</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_LIVERY_MOD</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
	  <Item>
          <modelName>baller8_livery_9</modelName>
          <modShopLabel>BALL8_LIV9</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_LIVERY_MOD</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
	  <Item>
          <modelName>baller8_livery_10</modelName>
          <modShopLabel>BALL8_LIV10</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_LIVERY_MOD</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
	  <Item>
          <modelName>baller8_livery_11</modelName>
          <modShopLabel>BALL8_LIV11</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_LIVERY_MOD</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
	  <Item>
          <modelName>baller8_livery_12</modelName>
          <modShopLabel>BALL8_LIV12</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_LIVERY_MOD</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>baller8_sust_1</modelName>
          <modShopLabel>BALL8_SUST1</modShopLabel>
          <linkedModels/>
		  <turnOffBones/>
          <type>VMT_WING_R</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>baller8_sust_2</modelName>
          <modShopLabel>BALL8_SUST2</modShopLabel>
          <linkedModels/>
		  <turnOffBones/>
          <type>VMT_WING_R</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>baller8_sust_3</modelName>
          <modShopLabel>BALL8_SUST3</modShopLabel>
          <linkedModels/>
		  <turnOffBones/>
          <type>VMT_WING_R</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>baller8_mdflaps_1</modelName>
          <modShopLabel>BALL8_MDGD1</modShopLabel>
          <linkedModels/>
		  <turnOffBones/>
          <type>VMT_SKIRT</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>baller8_boot_1</modelName>
          <modShopLabel>BALL8_BOOT1</modShopLabel>
          <linkedModels/>
		  <turnOffBones>
		  <Item>misc_h</Item>
		  </turnOffBones> 
          <type>VMT_SPOILER</type>
          <bone>boot</bone>
          <collisionBone>boot</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>baller8_boot_12</modelName>
          <modShopLabel>BALL8_BOOT2</modShopLabel>
          <linkedModels/>
		  <turnOffBones>
		  <Item>misc_h</Item>
		  </turnOffBones> 
          <type>VMT_SPOILER</type>
          <bone>boot</bone>
          <collisionBone>boot</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>baller8_boot_1c</modelName>
          <modShopLabel>BALL8_BOOT3</modShopLabel>
          <linkedModels/>
		  <turnOffBones>
		  <Item>misc_h</Item>
		  </turnOffBones> 
          <type>VMT_SPOILER</type>
          <bone>boot</bone>
          <collisionBone>boot</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>baller8_boot_1b</modelName>
          <modShopLabel>BALL8_BOOT4</modShopLabel>
          <linkedModels/>
		  <turnOffBones>
		  <Item>misc_h</Item>
		  </turnOffBones> 
          <type>VMT_SPOILER</type>
          <bone>boot</bone>
          <collisionBone>boot</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>baller8_boot_2</modelName>
          <modShopLabel>BALL8_BOOT5</modShopLabel>
          <linkedModels/>
		  <turnOffBones>
		  <Item>misc_h</Item>
		  </turnOffBones> 
          <type>VMT_SPOILER</type>
          <bone>boot</bone>
          <collisionBone>boot</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>baller8_boot_22</modelName>
          <modShopLabel>BALL8_BOOT6</modShopLabel>
          <linkedModels/>
		  <turnOffBones>
		  <Item>misc_h</Item>
		  </turnOffBones> 
          <type>VMT_SPOILER</type>
          <bone>boot</bone>
          <collisionBone>boot</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>baller8_boot_2c</modelName>
          <modShopLabel>BALL8_BOOT7</modShopLabel>
          <linkedModels/>
		  <turnOffBones>
		  <Item>misc_h</Item>
		  </turnOffBones> 
          <type>VMT_SPOILER</type>
          <bone>boot</bone>
          <collisionBone>boot</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>baller8_boot_2b</modelName>
          <modShopLabel>BALL8_BOOT8</modShopLabel>
          <linkedModels/>
		  <turnOffBones>
		  <Item>misc_h</Item>
		  </turnOffBones> 
          <type>VMT_SPOILER</type>
          <bone>boot</bone>
          <collisionBone>boot</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>baller8_boot_3</modelName>
          <modShopLabel>BALL8_BOOT9</modShopLabel>
          <linkedModels/>
		  <turnOffBones>
		  <Item>misc_h</Item>
		  </turnOffBones> 
          <type>VMT_SPOILER</type>
          <bone>boot</bone>
          <collisionBone>boot</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>baller8_boot_32</modelName>
          <modShopLabel>BALL8_BOOT10</modShopLabel>
          <linkedModels/>
		  <turnOffBones>
		  <Item>misc_h</Item>
		  </turnOffBones> 
          <type>VMT_SPOILER</type>
          <bone>boot</bone>
          <collisionBone>boot</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>baller8_boot_3c</modelName>
          <modShopLabel>BALL8_BOOT11</modShopLabel>
          <linkedModels/>
		  <turnOffBones>
		  <Item>misc_h</Item>
		  </turnOffBones> 
          <type>VMT_SPOILER</type>
          <bone>boot</bone>
          <collisionBone>boot</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>baller8_boot_3b</modelName>
          <modShopLabel>BALL8_BOOT12</modShopLabel>
          <linkedModels/>
		  <turnOffBones>
		  <Item>misc_h</Item>
		  </turnOffBones> 
          <type>VMT_SPOILER</type>
          <bone>boot</bone>
          <collisionBone>boot</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>baller8_wing_1</modelName>
          <modShopLabel>BALL8_BOOT13</modShopLabel>
          <linkedModels/>
		  <turnOffBones/>
          <type>VMT_SPOILER</type>
          <bone>boot</bone>
          <collisionBone>mod_col_1</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>baller8_wing_2</modelName>
          <modShopLabel>BALL8_BOOT14</modShopLabel>
          <linkedModels/>
		  <turnOffBones/>
          <type>VMT_SPOILER</type>
          <bone>boot</bone>
          <collisionBone>mod_col_9</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>baller8_wing_3</modelName>
          <modShopLabel>BALL8_BOOT15</modShopLabel>
          <linkedModels/>
		  <turnOffBones/>
          <type>VMT_SPOILER</type>
          <bone>chassis</bone>
          <collisionBone>mod_col_5</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>baller8_wing_4</modelName>
          <modShopLabel>BALL8_BOOT16</modShopLabel>
          <linkedModels/>
		  <turnOffBones/>
          <type>VMT_SPOILER</type>
          <bone>boot</bone>
          <collisionBone>mod_col_6</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>baller8_roofp_1</modelName>
          <modShopLabel>BALL8_ROOFP1</modShopLabel>
          <linkedModels/>
		  <turnOffBones>
		  <Item>misc_r</Item>
		  </turnOffBones> 
          <type>VMT_ROOF</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>baller8_roofp_12</modelName>
          <modShopLabel>BALL8_ROOFP2</modShopLabel>
          <linkedModels/>
		  <turnOffBones>
		  <Item>misc_r</Item>
		  </turnOffBones> 
          <type>VMT_ROOF</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>baller8_roofp_1c</modelName>
          <modShopLabel>BALL8_ROOFP3</modShopLabel>
          <linkedModels/>
		  <turnOffBones>
		  <Item>misc_r</Item>
		  </turnOffBones> 
          <type>VMT_ROOF</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>baller8_roofp_1b</modelName>
          <modShopLabel>BALL8_ROOFP4</modShopLabel>
          <linkedModels/>
		  <turnOffBones>
		  <Item>misc_r</Item>
		  </turnOffBones> 
          <type>VMT_ROOF</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>baller8_hood_1</modelName>
          <modShopLabel>BALL8_HOOD1</modShopLabel>
          <linkedModels/>
		  <turnOffBones>
		  <Item>misc_g</Item>
		  </turnOffBones> 
          <type>VMT_BONNET</type>
          <bone>bonnet</bone>
          <collisionBone>bonnet</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>baller8_hood_2</modelName>
          <modShopLabel>BALL8_HOOD2</modShopLabel>
          <linkedModels/>
		  <turnOffBones>
		  <Item>misc_g</Item>
		  </turnOffBones> 
          <type>VMT_BONNET</type>
          <bone>bonnet</bone>
          <collisionBone>bonnet</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>baller8_hood_2a</modelName>
          <modShopLabel>BALL8_HOOD3</modShopLabel>
          <linkedModels/>
		  <turnOffBones>
		  <Item>misc_g</Item>
		  </turnOffBones> 
          <type>VMT_BONNET</type>
          <bone>bonnet</bone>
          <collisionBone>bonnet</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>baller8_hood_2b</modelName>
          <modShopLabel>BALL8_HOOD4</modShopLabel>
          <linkedModels/>
		  <turnOffBones>
		  <Item>misc_g</Item>
		  </turnOffBones> 
          <type>VMT_BONNET</type>
          <bone>bonnet</bone>
          <collisionBone>bonnet</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>baller8_hood_3</modelName>
          <modShopLabel>BALL8_HOOD5</modShopLabel>
          <linkedModels/>
		  <turnOffBones>
		  <Item>misc_g</Item>
		  </turnOffBones> 
          <type>VMT_BONNET</type>
          <bone>bonnet</bone>
          <collisionBone>bonnet</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>baller8_hood_4</modelName>
          <modShopLabel>BALL8_HOOD6</modShopLabel>
          <linkedModels/>
		  <turnOffBones>
		  <Item>misc_g</Item>
		  </turnOffBones> 
          <type>VMT_BONNET</type>
          <bone>bonnet</bone>
          <collisionBone>bonnet</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>baller8_hood_4a</modelName>
          <modShopLabel>BALL8_HOOD7</modShopLabel>
          <linkedModels/>
		  <turnOffBones>
		  <Item>misc_g</Item>
		  </turnOffBones> 
          <type>VMT_BONNET</type>
          <bone>bonnet</bone>
          <collisionBone>bonnet</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>baller8_exh_1</modelName>
          <modShopLabel>BALL8_EX1</modShopLabel>
          <linkedModels/>
		  <turnOffBones>
		  <Item>misc_e</Item>
		  <Item>exhaust</Item>
		  <Item>exhaust_2</Item>
		  </turnOffBones> 
          <type>VMT_EXHAUST</type>
          <bone>misc_e</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>baller8_exh_2</modelName>
          <modShopLabel>BALL8_EX2</modShopLabel>
          <linkedModels/>
		  <turnOffBones>
		  <Item>misc_e</Item>
		  <Item>exhaust</Item>
		  <Item>exhaust_2</Item>
		  </turnOffBones> 
          <type>VMT_EXHAUST</type>
          <bone>misc_e</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>baller8_exh_3</modelName>
          <modShopLabel>BALL8_EX3</modShopLabel>
          <linkedModels/>
		  <turnOffBones>
		  <Item>misc_e</Item>
		  <Item>exhaust</Item>
		  <Item>exhaust_2</Item>
		  </turnOffBones> 
          <type>VMT_EXHAUST</type>
          <bone>misc_e</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>baller8_exh_4</modelName>
          <modShopLabel>BALL8_EX4</modShopLabel>
          <linkedModels/>
		  <turnOffBones>
		  <Item>misc_e</Item>
		  <Item>exhaust</Item>
		  <Item>exhaust_2</Item>
		  </turnOffBones> 
          <type>VMT_EXHAUST</type>
          <bone>misc_e</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>baller8_exh_5</modelName>
          <modShopLabel>BALL8_EX5</modShopLabel>
          <linkedModels/>
		  <turnOffBones>
		  <Item>misc_e</Item>
		  <Item>exhaust</Item>
		  <Item>exhaust_2</Item>
		  </turnOffBones> 
          <type>VMT_EXHAUST</type>
          <bone>misc_e</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>baller8_exh_6</modelName>
          <modShopLabel>BALL8_EX6</modShopLabel>
          <linkedModels/>
		  <turnOffBones>
		  <Item>misc_e</Item>
		  <Item>exhaust</Item>
		  <Item>exhaust_2</Item>
		  </turnOffBones> 
          <type>VMT_EXHAUST</type>
          <bone>misc_e</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>baller8_exh_7</modelName>
          <modShopLabel>BALL8_EX7</modShopLabel>
          <linkedModels/>
		  <turnOffBones>
		  <Item>misc_e</Item>
		  <Item>exhaust</Item>
		  <Item>exhaust_2</Item>
		  </turnOffBones> 
          <type>VMT_EXHAUST</type>
          <bone>misc_e</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>rmod_baller8_exh1</modelName>
          <modShopLabel>rmod_baller8_exh1</modShopLabel>
          <linkedModels/>
		  <turnOffBones>
		  <Item>misc_e</Item>
		  <Item>exhaust</Item>
		  <Item>exhaust_2</Item>
		  </turnOffBones> 
          <type>VMT_EXHAUST</type>
          <bone>misc_e</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>baller8_trim_lf_1</modelName>
          <modShopLabel>BALL8_TRIM1</modShopLabel>
          <linkedModels>
		  <Item>baller8_trim_rf_1</Item>
		  <Item>baller8_trim_lr_1</Item>
		  <Item>baller8_trim_rr_1</Item>
		  <Item>baller8_trim_r_1</Item>
		  <Item>baller8_trim_f_1</Item>
		  <Item>baller8_trim_bd_1</Item>
          </linkedModels>
		  <turnOffBones>
		  <Item>misc_x</Item>
		  <Item>misc_s</Item>
		  <Item>misc_v</Item>
		  <Item>misc_f</Item>
		  <Item>misc_t</Item>
		  <Item>misc_z</Item>
		  <Item>misc_y</Item>
		  </turnOffBones> 
          <type>VMT_CHASSIS</type>
          <bone>door_dside_f</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>baller8_trim_lf_2</modelName>
          <modShopLabel>BALL8_TRIM2</modShopLabel>
          <linkedModels>
		  <Item>baller8_trim_rf_2</Item>
		  <Item>baller8_trim_lr_2</Item>
		  <Item>baller8_trim_rr_2</Item>
		  <Item>baller8_trim_r_2</Item>
		  <Item>baller8_trim_bd_2</Item>
          </linkedModels>
		  <turnOffBones>
		  <Item>misc_x</Item>
		  <Item>misc_s</Item>
		  <Item>misc_v</Item>
		  <Item>misc_f</Item>
		  <Item>misc_t</Item>
		  <Item>misc_z</Item>
		  <Item>misc_y</Item>
		  </turnOffBones> 
          <type>VMT_CHASSIS</type>
          <bone>door_dside_f</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>baller8_trim_lf_c</modelName>
          <modShopLabel>BALL8_TRIM3</modShopLabel>
          <linkedModels>
		  <Item>baller8_trim_rf_c</Item>
		  <Item>baller8_trim_lr_c</Item>
		  <Item>baller8_trim_rr_c</Item>
		  <Item>baller8_trim_r_c</Item>
		  <Item>baller8_trim_bd_c</Item>
          </linkedModels>
		  <turnOffBones>
		  <Item>misc_x</Item>
		  <Item>misc_s</Item>
		  <Item>misc_v</Item>
		  <Item>misc_f</Item>
		  <Item>misc_t</Item>
		  <Item>misc_z</Item>
		  <Item>misc_y</Item>
		  </turnOffBones> 
          <type>VMT_CHASSIS</type>
          <bone>door_dside_f</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>baller8_trim_lf_b</modelName>
          <modShopLabel>BALL8_TRIM4</modShopLabel>
          <linkedModels>
		  <Item>baller8_trim_rf_b</Item>
		  <Item>baller8_trim_lr_b</Item>
		  <Item>baller8_trim_rr_b</Item>
		  <Item>baller8_trim_r_b</Item>
		  <Item>baller8_trim_f_b</Item>
		  <Item>baller8_trim_bd_b</Item>
          </linkedModels>
		  <turnOffBones>
		  <Item>misc_x</Item>
		  <Item>misc_s</Item>
		  <Item>misc_v</Item>
		  <Item>misc_f</Item>
		  <Item>misc_t</Item>
		  <Item>misc_z</Item>
		  <Item>misc_y</Item>
		  </turnOffBones> 
          <type>VMT_CHASSIS</type>
          <bone>door_dside_f</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>baller8_mirr_1l2</modelName>
          <modShopLabel>BALL8_MIRR1</modShopLabel>
          <linkedModels>
		  <Item>baller8_mirr_1r2</Item>
          </linkedModels>
		  <turnOffBones>
		  <Item>misc_l</Item>
		  <Item>misc_u</Item>
		  </turnOffBones> 
          <type>VMT_WING_L</type>
          <bone>door_dside_f</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>baller8_mirr_1lc</modelName>
          <modShopLabel>BALL8_MIRR3</modShopLabel>
          <linkedModels>
		  <Item>baller8_mirr_1rc</Item>
          </linkedModels>
		  <turnOffBones>
		  <Item>misc_l</Item>
		  <Item>misc_u</Item>
		  </turnOffBones> 
          <type>VMT_WING_L</type>
          <bone>door_dside_f</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>baller8_mirr_1lb</modelName>
          <modShopLabel>BALL8_MIRR4</modShopLabel>
          <linkedModels>
		  <Item>baller8_mirr_1rb</Item>
          </linkedModels>
		  <turnOffBones>
		  <Item>misc_l</Item>
		  <Item>misc_u</Item>
		  </turnOffBones> 
          <type>VMT_WING_L</type>
          <bone>door_dside_f</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>baller8_mirr_2l</modelName>
          <modShopLabel>BALL8_MIRR5</modShopLabel>
          <linkedModels>
		  <Item>baller8_mirr_2r</Item>
          </linkedModels>
		  <turnOffBones>
		  <Item>misc_l</Item>
		  <Item>misc_u</Item>
		  </turnOffBones> 
          <type>VMT_WING_L</type>
          <bone>door_dside_f</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>baller8_mirr_2l2</modelName>
          <modShopLabel>BALL8_MIRR6</modShopLabel>
          <linkedModels>
		  <Item>baller8_mirr_2r2</Item>
          </linkedModels>
		  <turnOffBones>
		  <Item>misc_l</Item>
		  <Item>misc_u</Item>
		  </turnOffBones> 
          <type>VMT_WING_L</type>
          <bone>door_dside_f</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>baller8_mirr_2lc</modelName>
          <modShopLabel>BALL8_MIRR7</modShopLabel>
          <linkedModels>
		  <Item>baller8_mirr_2rc</Item>
          </linkedModels>
		  <turnOffBones>
		  <Item>misc_l</Item>
		  <Item>misc_u</Item>
		  </turnOffBones> 
          <type>VMT_WING_L</type>
          <bone>door_dside_f</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>baller8_mirr_2lb</modelName>
          <modShopLabel>BALL8_MIRR8</modShopLabel>
          <linkedModels>
		  <Item>baller8_mirr_2rb</Item>
          </linkedModels>
		  <turnOffBones>
		  <Item>misc_l</Item>
		  <Item>misc_u</Item>
		  </turnOffBones> 
          <type>VMT_WING_L</type>
          <bone>door_dside_f</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
      </visibleMods>
	  <linkMods>
	  		<Item>
          <modelName>baller8_mirr_1r</modelName>
          <bone>door_pside_f</bone>
          <turnOffExtra value="false" />
		</Item>
		<Item>
          <modelName>baller8_mirr_2r</modelName>
          <bone>door_pside_f</bone>
          <turnOffExtra value="false" />
		</Item>
		<Item>
          <modelName>baller8_mirr_1r2</modelName>
          <bone>door_pside_f</bone>
          <turnOffExtra value="false" />
		</Item>
		<Item>
          <modelName>baller8_mirr_2r2</modelName>
          <bone>door_pside_f</bone>
          <turnOffExtra value="false" />
		</Item>
		<Item>
          <modelName>baller8_mirr_1rc</modelName>
          <bone>door_pside_f</bone>
          <turnOffExtra value="false" />
		</Item>
		<Item>
          <modelName>baller8_mirr_2rc</modelName>
          <bone>door_pside_f</bone>
          <turnOffExtra value="false" />
		</Item>
		<Item>
          <modelName>baller8_mirr_1rb</modelName>
          <bone>door_pside_f</bone>
          <turnOffExtra value="false" />
		</Item>
		<Item>
          <modelName>baller8_mirr_2rb</modelName>
          <bone>door_pside_f</bone>
          <turnOffExtra value="false" />
		</Item>
		<Item>
          <modelName>baller8_fend_dr_1</modelName>
          <bone>door_dside_r</bone>
          <turnOffExtra value="false" />
		</Item>
		<Item>
          <modelName>baller8_fend_bd_1</modelName>
          <bone>chassis</bone>
          <turnOffExtra value="false" />
		</Item>
		<Item>
          <modelName>baller8_fend_pr_1</modelName>
          <bone>door_pside_r</bone>
          <turnOffExtra value="false" />
		</Item>
		<Item>
          <modelName>baller8_fend_wrf_1</modelName>
          <bone>wing_rf</bone>
          <turnOffExtra value="false" />
		</Item>
		<Item>
          <modelName>baller8_fend_bf_1</modelName>
          <bone>bumper_f</bone>
          <turnOffExtra value="false" />
		</Item>
		<Item>
          <modelName>baller8_fend_dr_2</modelName>
          <bone>door_dside_r</bone>
          <turnOffExtra value="false" />
		</Item>
		<Item>
          <modelName>baller8_fend_bd_2</modelName>
          <bone>chassis</bone>
          <turnOffExtra value="false" />
		</Item>
		<Item>
          <modelName>baller8_fend_pr_2</modelName>
          <bone>door_pside_r</bone>
          <turnOffExtra value="false" />
		</Item>
		<Item>
          <modelName>baller8_fend_wrf_2</modelName>
          <bone>wing_rf</bone>
          <turnOffExtra value="false" />
		</Item>
		<Item>
          <modelName>baller8_fend_bf_2</modelName>
          <bone>bumper_f</bone>
          <turnOffExtra value="false" />
		</Item>
		<Item>
          <modelName>baller8_fend_dr_c</modelName>
          <bone>door_dside_r</bone>
          <turnOffExtra value="false" />
		</Item>
		<Item>
          <modelName>baller8_fend_bd_c</modelName>
          <bone>chassis</bone>
          <turnOffExtra value="false" />
		</Item>
		<Item>
          <modelName>baller8_fend_pr_c</modelName>
          <bone>door_pside_r</bone>
          <turnOffExtra value="false" />
		</Item>
		<Item>
          <modelName>baller8_fend_wrf_c</modelName>
          <bone>wing_rf</bone>
          <turnOffExtra value="false" />
		</Item>
		<Item>
          <modelName>baller8_fend_bf_c</modelName>
          <bone>bumper_f</bone>
          <turnOffExtra value="false" />
		</Item>
		<Item>
          <modelName>baller8_fend_dr_b</modelName>
          <bone>door_dside_r</bone>
          <turnOffExtra value="false" />
		</Item>
		<Item>
          <modelName>baller8_fend_bd_b</modelName>
          <bone>chassis</bone>
          <turnOffExtra value="false" />
		</Item>
		<Item>
          <modelName>baller8_fend_pr_b</modelName>
          <bone>door_pside_r</bone>
          <turnOffExtra value="false" />
		</Item>
		<Item>
          <modelName>baller8_fend_wrf_b</modelName>
          <bone>wing_rf</bone>
          <turnOffExtra value="false" />
		</Item>
		<Item>
          <modelName>baller8_fend_bf_b</modelName>
          <bone>bumper_f</bone>
          <turnOffExtra value="false" />
		</Item>
		<Item>
          <modelName>baller8_trim_rf_1</modelName>
          <bone>door_pside_f</bone>
          <turnOffExtra value="false" />
		</Item>
		<Item>
          <modelName>baller8_trim_lr_1</modelName>
          <bone>door_dside_r</bone>
          <turnOffExtra value="false" />
		</Item>
		<Item>
          <modelName>baller8_trim_rr_1</modelName>
          <bone>door_pside_r</bone>
          <turnOffExtra value="false" />
		</Item>
		<Item>
          <modelName>baller8_trim_r_1</modelName>
          <bone>boot</bone>
          <turnOffExtra value="false" />
		</Item>
		<Item>
          <modelName>baller8_trim_bd_1</modelName>
          <bone>chassis</bone>
          <turnOffExtra value="false" />
		</Item>
		<Item>
          <modelName>baller8_trim_rf_2</modelName>
          <bone>door_pside_f</bone>
          <turnOffExtra value="false" />
		</Item>
		<Item>
          <modelName>baller8_trim_lr_2</modelName>
          <bone>door_dside_r</bone>
          <turnOffExtra value="false" />
		</Item>
		<Item>
          <modelName>baller8_trim_rr_2</modelName>
          <bone>door_pside_r</bone>
          <turnOffExtra value="false" />
		</Item>
		<Item>
          <modelName>baller8_trim_r_2</modelName>
          <bone>boot</bone>
          <turnOffExtra value="false" />
		</Item>
		<Item>
          <modelName>baller8_trim_bd_2</modelName>
          <bone>chassis</bone>
          <turnOffExtra value="false" />
		</Item>
		<Item>
          <modelName>baller8_trim_rf_c</modelName>
          <bone>door_pside_f</bone>
          <turnOffExtra value="false" />
		</Item>
		<Item>
          <modelName>baller8_trim_lr_c</modelName>
          <bone>door_dside_r</bone>
          <turnOffExtra value="false" />
		</Item>
		<Item>
          <modelName>baller8_trim_rr_c</modelName>
          <bone>door_pside_r</bone>
          <turnOffExtra value="false" />
		</Item>
		<Item>
          <modelName>baller8_trim_r_c</modelName>
          <bone>boot</bone>
          <turnOffExtra value="false" />
		</Item>
		<Item>
          <modelName>baller8_trim_bd_c</modelName>
          <bone>chassis</bone>
          <turnOffExtra value="false" />
		</Item>
		<Item>
          <modelName>baller8_trim_rf_b</modelName>
          <bone>door_pside_f</bone>
          <turnOffExtra value="false" />
		</Item>
		<Item>
          <modelName>baller8_trim_lr_b</modelName>
          <bone>door_dside_r</bone>
          <turnOffExtra value="false" />
		</Item>
		<Item>
          <modelName>baller8_trim_rr_b</modelName>
          <bone>door_pside_r</bone>
          <turnOffExtra value="false" />
		</Item>
		<Item>
          <modelName>baller8_trim_r_b</modelName>
          <bone>boot</bone>
          <turnOffExtra value="false" />
		</Item>
		<Item>
          <modelName>baller8_trim_bd_b</modelName>
          <bone>chassis</bone>
          <turnOffExtra value="false" />
		</Item>
	  </linkMods>
      <statMods>
        <Item>
          <identifier />
          <modifier value="100" />
          <audioApply value="1.000000" />
          <weight value="20" />
          <type>VMT_ENGINE</type>
        </Item>
        <Item>
          <identifier />
          <modifier value="150" />
          <audioApply value="1.000000" />
          <weight value="20" />
          <type>VMT_ENGINE</type>
        </Item>
        <Item>
          <identifier />
          <modifier value="230" />
          <audioApply value="1.000000" />
          <weight value="20" />
          <type>VMT_ENGINE</type>
        </Item>
        <Item>
          <identifier />
          <modifier value="280" />
          <audioApply value="1.000000" />
          <weight value="20" />
          <type>VMT_ENGINE</type>
        </Item>
        <Item>
          <identifier />
          <modifier value="25" />
          <audioApply value="1.000000" />
          <weight value="5" />
          <type>VMT_BRAKES</type>
        </Item>
        <Item>
          <identifier />
          <modifier value="50" />
          <audioApply value="1.000000" />
          <weight value="5" />
          <type>VMT_BRAKES</type>
        </Item>
        <Item>
          <identifier />
          <modifier value="100" />
          <audioApply value="1.000000" />
          <weight value="5" />
          <type>VMT_BRAKES</type>
        </Item>
        <Item>
          <identifier />
          <modifier value="25" />
          <audioApply value="1.000000" />
          <weight value="5" />
          <type>VMT_GEARBOX</type>
        </Item>
        <Item>
          <identifier />
          <modifier value="50" />
          <audioApply value="1.000000" />
          <weight value="5" />
          <type>VMT_GEARBOX</type>
        </Item>
        <Item>
          <identifier />
          <modifier value="100" />
          <audioApply value="1.000000" />
          <weight value="5" />
          <type>VMT_GEARBOX</type>
        </Item>
        <Item>
          <identifier />
          <modifier value="20" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_ARMOUR</type>
        </Item>
        <Item>
          <identifier />
          <modifier value="40" />
          <audioApply value="1.000000" />
          <weight value="10" />
          <type>VMT_ARMOUR</type>
        </Item>
        <Item>
          <identifier />
          <modifier value="60" />
          <audioApply value="1.000000" />
          <weight value="20" />
          <type>VMT_ARMOUR</type>
        </Item>
        <Item>
          <identifier />
          <modifier value="80" />
          <audioApply value="1.000000" />
          <weight value="30" />
          <type>VMT_ARMOUR</type>
        </Item>
        <Item>
          <identifier />
          <modifier value="100" />
          <audioApply value="1.000000" />
          <weight value="40" />
          <type>VMT_ARMOUR</type>
        </Item>
        <Item>
          <identifier>HORN_TRUCK</identifier>
          <modifier value="1766676233" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>HORN_COP</identifier>
          <modifier value="2904189469" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>HORN_CLOWN</identifier>
          <modifier value="2543206147" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>HORN_MUSICAL_1</identifier>
          <modifier value="1732399718" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>HORN_MUSICAL_2</identifier>
          <modifier value="2046162893" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>HORN_MUSICAL_3</identifier>
          <modifier value="2194999691" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>HORN_MUSICAL_4</identifier>
          <modifier value="2508304100" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>HORN_MUSICAL_5</identifier>
          <modifier value="3707223535" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
		<Item>
          <identifier>HORN_SAD_TROMBONE</identifier>
          <modifier value="632950117" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>MUSICAL_HORN_BUSINESS_1</identifier>
          <modifier value="3628534289" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>MUSICAL_HORN_BUSINESS_2</identifier>
          <modifier value="3892554122" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>MUSICAL_HORN_BUSINESS_3</identifier>
          <modifier value="4112892878" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>MUSICAL_HORN_BUSINESS_4</identifier>
          <modifier value="116877169" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>MUSICAL_HORN_BUSINESS_5</identifier>
          <modifier value="2684983719" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>MUSICAL_HORN_BUSINESS_6</identifier>
          <modifier value="2982690084" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
		</Item>
		<Item>
          <identifier>MUSICAL_HORN_BUSINESS_7</identifier>
          <modifier value="3203290992" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
		</Item>
		<Item>
          <identifier>DLC_BUSI2_C_MAJOR_NOTES_C0</identifier>
          <modifier value="771284519" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
		</Item>
		<Item>
          <identifier>DLC_BUSI2_C_MAJOR_NOTES_D0</identifier>
          <modifier value="2586621229" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
		</Item>
		<Item>
          <identifier>DLC_BUSI2_C_MAJOR_NOTES_E0</identifier>
          <modifier value="283386134" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
		</Item>
		<Item>
          <identifier>DLC_BUSI2_C_MAJOR_NOTES_F0</identifier>
          <modifier value="3884502400" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
		</Item>
		<Item>
          <identifier>DLC_BUSI2_C_MAJOR_NOTES_G0</identifier>
          <modifier value="265723083" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
		</Item>
		<Item>
          <identifier>DLC_BUSI2_C_MAJOR_NOTES_A0</identifier>
          <modifier value="1746883687" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
		</Item>
		<Item>
          <identifier>DLC_BUSI2_C_MAJOR_NOTES_B0</identifier>
          <modifier value="1919870950" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
		</Item>
		<Item>
          <identifier>DLC_BUSI2_C_MAJOR_NOTES_C1</identifier>
          <modifier value="1085277077" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
		</Item>
        <Item>
          <identifier>HIPSTER_HORN_1</identifier>
          <modifier value="444549672" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>HIPSTER_HORN_2</identifier>
          <modifier value="1603064898" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>HIPSTER_HORN_3</identifier>
          <modifier value="240366033" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>HIPSTER_HORN_4</identifier>
          <modifier value="960137118" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>INDEP_HORN_1</identifier>
          <modifier value="3572144790" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>INDEP_HORN_2</identifier>
          <modifier value="3801396714" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>INDEP_HORN_3</identifier>
          <modifier value="2843657151" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>INDEP_HORN_4</identifier>
          <modifier value="3341811489" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
		<Item>
          <identifier>LUXE_HORN_1</identifier>
          <modifier value="3199657341" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
		<Item>
          <identifier>LUXE_HORN_2</identifier>
          <modifier value="2900378064" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
		<Item>
          <identifier>LUXE_HORN_3</identifier>
          <modifier value="3956195248" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
		<!-- PREVIEW HORN - FOR MOD SHOP USE ONLY -->
		<Item>
          <identifier>LUXORY_HORN_1</identifier>
          <modifier value="676333254" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
		<!-- PREVIEW HORN - FOR MOD SHOP USE ONLY -->
		<Item>
          <identifier>LUXURY_HORN_2</identifier>
          <modifier value="2099578296" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
		<!-- PREVIEW HORN - FOR MOD SHOP USE ONLY -->
		<Item>
          <identifier>LUXURY_HORN_3</identifier>
          <modifier value="1373384483" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
		<Item>
          <identifier>ORGAN_HORN_LOOP_01</identifier>
          <modifier value="2916775806" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
		<!-- PREVIEW HORN - FOR MOD SHOP USE ONLY -->
		<Item>
          <identifier>ORGAN_HORN_LOOP_01_PREVIEW</identifier>
          <modifier value="3714706952" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
		<Item>
          <identifier>ORGAN_HORN_LOOP_02</identifier>
          <modifier value="2611860261" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
		<!-- PREVIEW HORN - FOR MOD SHOP USE ONLY -->
		<Item>
          <identifier>ORGAN_HORN_LOOP_02_PREVIEW</identifier>
          <modifier value="3206770359" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
		<Item>
          <identifier>LOWRIDER_HORN_1</identifier>
          <modifier value="310529291" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
		<!-- PREVIEW HORN - FOR MOD SHOP USE ONLY -->
		<Item>
          <identifier>LOWRIDER_HORN_1_PREVIEW</identifier>
          <modifier value="2965568987" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
		<Item>
          <identifier>LOWRIDER_HORN_2</identifier>
          <modifier value="55291550" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
		<!-- PREVIEW HORN - FOR MOD SHOP USE ONLY -->
		<Item>
          <identifier>LOWRIDER_HORN_2_PREVIEW</identifier>
          <modifier value="965054819" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier />
          <modifier value="25" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_SUSPENSION</type>
        </Item>
        <Item>
          <identifier />
          <modifier value="50" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_SUSPENSION</type>
        </Item>
        <Item>
          <identifier />
          <modifier value="75" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_SUSPENSION</type>
        </Item>
        <Item>
          <identifier />
          <modifier value="100" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_SUSPENSION</type>
        </Item>
      </statMods>
      <slotNames />
      <liveryNames>
      </liveryNames>
    </Item>
  </Kits>
      <Lights>
      <Item>
      <id value="4189" />
      <indicator>
        <intensity value="0.375000" />
        <falloffMax value="2.500000" />
        <falloffExponent value="8.000000" />
        <innerConeAngle value="20.000000" />
        <outerConeAngle value="50.000000" />
        <emmissiveBoost value="false" />
        <color value="0xFFFF7300" />
      </indicator>
      <rearIndicatorCorona>
        <size value="0.000000" />
        <size_far value="0.000000" />
        <intensity value="0.000000" />
        <intensity_far value="0.000000" />
        <color value="0x00000000" />
        <numCoronas value="0" />
        <distBetweenCoronas value="128" />
        <distBetweenCoronas_far value="255" />
        <xRotation value="0.000000" />
        <yRotation value="0.000000" />
        <zRotation value="0.000000" />
        <zBias value="0.250000" />
        <pullCoronaIn value="false" />
      </rearIndicatorCorona>
      <frontIndicatorCorona>
        <size value="0.000000" />
        <size_far value="0.000000" />
        <intensity value="0.000000" />
        <intensity_far value="0.000000" />
        <color value="0x00000000" />
        <numCoronas value="1" />
        <distBetweenCoronas value="128" />
        <distBetweenCoronas_far value="255" />
        <xRotation value="0.000000" />
        <yRotation value="0.000000" />
        <zRotation value="0.000000" />
        <zBias value="0.250000" />
        <pullCoronaIn value="false" />
      </frontIndicatorCorona>
      <tailLight>
        <intensity value="0.250000" />
        <falloffMax value="4.000000" />
        <falloffExponent value="16.000000" />
        <innerConeAngle value="45.000000" />
        <outerConeAngle value="90.000000" />
        <emmissiveBoost value="false" />
        <color value="0xFFFF0000" />
      </tailLight>
      <tailLightCorona>
        <size value="1.200000" />
        <size_far value="2.500000" />
        <intensity value="5.000000" />
        <intensity_far value="1.000000" />
        <color value="0xFFFF0F05" />
        <numCoronas value="0" />
        <distBetweenCoronas value="25" />
        <distBetweenCoronas_far value="49" />
        <xRotation value="0.270177" />
        <yRotation value="1.407433" />
        <zRotation value="0.000000" />
        <zBias value="0.250000" />
        <pullCoronaIn value="false" />
      </tailLightCorona>
      <tailLightMiddleCorona>
        <size value="0.000000" />
        <size_far value="0.000000" />
        <intensity value="0.000000" />
        <intensity_far value="0.000000" />
        <color value="0x00000000" />
        <numCoronas value="0" />
        <distBetweenCoronas value="128" />
        <distBetweenCoronas_far value="255" />
        <xRotation value="0.000000" />
        <yRotation value="0.000000" />
        <zRotation value="0.000000" />
        <zBias value="0.250000" />
        <pullCoronaIn value="false" />
      </tailLightMiddleCorona>
      <headLight>
        <intensity value="1.500000" />
        <falloffMax value="35.000000" />
        <falloffExponent value="16.000000" />
        <innerConeAngle value="0.000000" />
        <outerConeAngle value="60.000000" />
        <emmissiveBoost value="false" />
        <color value="0xFF7FA7E3" />
        <textureName>VehicleLight_car_standardmodern</textureName>
        <mirrorTexture value="false" />
      </headLight>
      <headLightCorona>
        <size value="0.100000" />
        <size_far value="7.000000" />
        <intensity value="7.000000" />
        <intensity_far value="5.000000" />
        <color value="0xFF61A5FF" />
        <numCoronas value="1" />
        <distBetweenCoronas value="27" />
        <distBetweenCoronas_far value="89" />
        <xRotation value="0.000000" />
        <yRotation value="0.175929" />
        <zRotation value="0.527788" />
        <zBias value="0.250000" />
        <pullCoronaIn value="false" />
      </headLightCorona>
      <reversingLight>
        <intensity value="0.500000" />
        <falloffMax value="7.200000" />
        <falloffExponent value="32.000000" />
        <innerConeAngle value="20.000000" />
        <outerConeAngle value="78.000000" />
        <emmissiveBoost value="false" />
        <color value="0xFFFFFFFF" />
      </reversingLight>
      <reversingLightCorona>
        <size value="0.800000" />
        <size_far value="2.000000" />
        <intensity value="1.500000" />
        <intensity_far value="1.000000" />
        <color value="0x00F7F7F7" />
        <numCoronas value="0" />
        <distBetweenCoronas value="128" />
        <distBetweenCoronas_far value="255" />
        <xRotation value="0.000000" />
        <yRotation value="0.000000" />
        <zRotation value="0.000000" />
        <zBias value="0.250000" />
        <pullCoronaIn value="false" />
      </reversingLightCorona>
      <name>rmod_baller8</name>
    </Item>
    </Lights>
</CVehicleModelInfoVarGlobal>