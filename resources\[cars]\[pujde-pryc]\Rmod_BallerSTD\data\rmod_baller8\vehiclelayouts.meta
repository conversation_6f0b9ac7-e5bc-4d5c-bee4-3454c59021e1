<?xml version="1.0" encoding="UTF-8"?>

<CVehicleMetadataMgr>
  <AnimRateSets />
  <ClipSetMaps>
    <Item type="CClipSetMap">
      <Name>ENTRY_CLIPSET_MAP_ALEUTIAN_FRONT_LEFT</Name>
      <Maps>
        <Item>
          <ClipSetId>clipset@anim@veh@granger2@ds@enter_exit</ClipSetId>
          <VarClipSetId>EntryVarClipSet</VarClipSetId>
          <ConditionFlags />
          <InformationFlags />
        </Item>
      </Maps>
    </Item>
    <Item type="CClipSetMap">
      <Name>ENTRY_CLIPSET_MAP_ALEUTIAN_FRONT_RIGHT</Name>
      <Maps>
        <Item>
          <ClipSetId>clipset@anim@veh@granger2@ps@enter_exit</ClipSetId>
          <VarClipSetId>EntryVarClipSet</VarClipSetId>
          <ConditionFlags />
          <InformationFlags />
        </Item>
      </Maps>
    </Item>
    <Item type="CClipSetMap">
      <Name>ENTRY_CLIPSET_MAP_FREIGHT2_LEFT</Name>
      <Maps>
        <Item>
          <ClipSetId>clipset@veh@train@front@ds@enter_exit</ClipSetId>
          <VarClipSetId>EntryVarClipSet</VarClipSetId>
          <ConditionFlags />
          <InformationFlags />
        </Item>
      </Maps>
    </Item>
    <Item type="CClipSetMap">
      <Name>EXIT_CLIPSET_MAP_FREIGHT2_LEFT</Name>
      <Maps>
        <Item>
          <ClipSetId>clipset@veh@train@front@ds@enter_exit</ClipSetId>
          <VarClipSetId>ExitVarClipSet</VarClipSetId>
          <ConditionFlags />
          <InformationFlags />
        </Item>
      </Maps>
    </Item>
    <Item type="CClipSetMap">
      <Name>ENTRY_CLIPSET_MAP_FREIGHT2_RIGHT</Name>
      <Maps>
        <Item>
          <ClipSetId>clipset@veh@train@front@ps@enter_exit</ClipSetId>
          <VarClipSetId>EntryVarClipSet</VarClipSetId>
          <ConditionFlags />
          <InformationFlags />
        </Item>
      </Maps>
    </Item>
    <Item type="CClipSetMap">
      <Name>EXIT_CLIPSET_MAP_FREIGHT2_RIGHT</Name>
      <Maps>
        <Item>
          <ClipSetId>clipset@veh@train@front@ps@enter_exit</ClipSetId>
          <VarClipSetId>ExitVarClipSet</VarClipSetId>
          <ConditionFlags />
          <InformationFlags />
        </Item>
      </Maps>
    </Item>
    <Item type="CClipSetMap">
      <Name>INSIDE_CLIPSET_MAP_FREIGHT2_FRONT_LEFT</Name>
      <Maps>
        <Item>
          <ClipSetId>clipset@veh@train@front@ds@base</ClipSetId>
          <VarClipSetId>InsideVarClipSet</VarClipSetId>
          <ConditionFlags />
          <InformationFlags />
        </Item>
      </Maps>
    </Item>
    <Item type="CClipSetMap">
      <Name>INSIDE_CLIPSET_MAP_FREIGHT2_FRONT_RIGHT</Name>
      <Maps>
        <Item>
          <ClipSetId>clipset@veh@train@front@ps@base</ClipSetId>
          <VarClipSetId>InsideVarClipSet</VarClipSetId>
          <ConditionFlags />
          <InformationFlags />
        </Item>
      </Maps>
    </Item>
    <Item type="CClipSetMap">
      <Name>ENTRY_CLIPSET_MAP_TERMINUS_FRONT_LEFT</Name>
      <Maps>
        <Item>
          <ClipSetId>anim@veh@van@btype2@ds@enter_exit</ClipSetId>
          <VarClipSetId>EntryVarClipSet</VarClipSetId>
          <ConditionFlags />
          <InformationFlags />
        </Item>
      </Maps>
    </Item>
    <Item type="CClipSetMap">
      <Name>EXIT_CLIPSET_MAP_TERMINUS_FRONT_LEFT</Name>
      <Maps>
        <Item>
          <ClipSetId>anim@veh@van@btype2@ds@enter_exit</ClipSetId>
          <VarClipSetId>ExitVarClipSet</VarClipSetId>
          <ConditionFlags />
          <InformationFlags />
        </Item>
      </Maps>
    </Item>
    <Item type="CClipSetMap">
      <Name>ENTRY_CLIPSET_MAP_TERMINUS_FRONT_RIGHT</Name>
      <Maps>
        <Item>
          <ClipSetId>anim@veh@van@btype2@ps@enter_exit</ClipSetId>
          <VarClipSetId>EntryVarClipSet</VarClipSetId>
          <ConditionFlags />
          <InformationFlags />
        </Item>
      </Maps>
    </Item>
    <Item type="CClipSetMap">
      <Name>EXIT_CLIPSET_MAP_TERMINUS_FRONT_RIGHT</Name>
      <Maps>
        <Item>
          <ClipSetId>anim@veh@van@btype2@ps@enter_exit</ClipSetId>
          <VarClipSetId>ExitVarClipSet</VarClipSetId>
          <ConditionFlags />
          <InformationFlags />
        </Item>
      </Maps>
    </Item>
    <Item type="CClipSetMap">
      <Name>ENTRY_CLIPSET_MAP_VAN_VIVANITE_SIDEDOOR_REAR_LEFT</Name>
      <Maps>
        <Item>
          <ClipSetId>clipset@veh@van@youga4@rds@enter_exit</ClipSetId>
          <VarClipSetId>EntryVarClipSet</VarClipSetId>
          <ConditionFlags />
          <InformationFlags />
        </Item>
      </Maps>
    </Item>
    <Item type="CClipSetMap">
      <Name>EXIT_CLIPSET_MAP_VAN_VIVANITE_SIDEDOOR_REAR_LEFT</Name>
      <Maps>
        <Item>
          <ClipSetId>anim@veh@lowrider@std@moonbeam@rds@enter_exit</ClipSetId>
          <VarClipSetId>ExitVarClipSet</VarClipSetId>
          <ConditionFlags />
          <InformationFlags />
        </Item>
      </Maps>
    </Item>
    <Item type="CClipSetMap">
      <Name>INSIDE_CLIPSET_MAP_VAN_VIVANITE_SIDEDOOR_REAR_LEFT</Name>
      <Maps>
        <Item>
          <ClipSetId>anim@veh@lowrider@std@moonbeam@rds@base</ClipSetId>
          <VarClipSetId>InsideVarClipSet</VarClipSetId>
          <ConditionFlags />
          <InformationFlags />
        </Item>
      </Maps>
    </Item>
    <Item type="CClipSetMap">
      <Name>ENTRY_CLIPSET_MAP_VAN_VIVANITE_SIDEDOOR_REAR_RIGHT</Name>
      <Maps>
        <Item>
          <ClipSetId>clipset@veh@van@youga4@rps@enter_exit</ClipSetId>
          <VarClipSetId>EntryVarClipSet</VarClipSetId>
          <ConditionFlags />
          <InformationFlags />
        </Item>
      </Maps>
    </Item>
    <Item type="CClipSetMap">
      <Name>EXIT_CLIPSET_MAP_VAN_VIVANITE_SIDEDOOR_REAR_RIGHT</Name>
      <Maps>
        <Item>
          <ClipSetId>anim@veh@lowrider@std@moonbeam@rps@enter_exit</ClipSetId>
          <VarClipSetId>ExitVarClipSet</VarClipSetId>
          <ConditionFlags />
          <InformationFlags />
        </Item>
      </Maps>
    </Item>
    <Item type="CClipSetMap">
      <Name>INSIDE_CLIPSET_MAP_VAN_VIVANITE_SIDEDOOR_REAR_RIGHT</Name>
      <Maps>
        <Item>
          <ClipSetId>anim@veh@lowrider@std@moonbeam@rps@base</ClipSetId>
          <VarClipSetId>InsideVarClipSet</VarClipSetId>
          <ConditionFlags />
          <InformationFlags />
        </Item>
      </Maps>
    </Item>
  </ClipSetMaps>
  <VehicleCoverBoundOffsetInfos>
    <Item type="CVehicleCoverBoundOffsetInfo">
      <Name>ALEUTIAN_COVER_OFFSET_INFO</Name>
      <ExtraSideOffset value="0.030000" />
      <ExtraForwardOffset value="-0.010000" />
      <ExtraBackwardOffset value="-0.270000" />
      <ExtraZOffset value="0.100000" />
      <CoverBoundInfos />
    </Item>
    <Item type="CVehicleCoverBoundOffsetInfo">
      <Name>ASTEROPE2_COVER_OFFSET_INFO</Name>
      <ExtraSideOffset value="0.000000" />
      <ExtraForwardOffset value="-0.150000" />
      <ExtraBackwardOffset value="-0.150000" />
      <ExtraZOffset value="0.200000" />
      <CoverBoundInfos />
    </Item>
    <Item type="CVehicleCoverBoundOffsetInfo">
      <Name>BALLER8_COVER_OFFSET_INFO</Name>
      <ExtraSideOffset value="0.030000" />
      <ExtraForwardOffset value="0.030000" />
      <ExtraBackwardOffset value="-0.320000" />
      <ExtraZOffset value="0.350000" />
      <CoverBoundInfos />
    </Item>
    <Item type="CVehicleCoverBoundOffsetInfo">
      <Name>DORADO_COVER_OFFSET_INFO</Name>
      <ExtraSideOffset value="0.000000" />
      <ExtraForwardOffset value="0.000000" />
      <ExtraBackwardOffset value="-0.330000" />
      <ExtraZOffset value="0.100000" />
      <CoverBoundInfos />
    </Item>
    <Item type="CVehicleCoverBoundOffsetInfo">
      <Name>CAVALCADE3_COVER_OFFSET_INFO</Name>
      <ExtraSideOffset value="0.030000" />
      <ExtraForwardOffset value="-0.120000" />
      <ExtraBackwardOffset value="-0.100000" />
      <ExtraZOffset value="0.250000" />
      <CoverBoundInfos />
    </Item>
    <Item type="CVehicleCoverBoundOffsetInfo">
      <Name>FR36_COVER_OFFSET_INFO</Name>
      <ExtraSideOffset value="-0.055000" />
      <ExtraForwardOffset value="-0.120000" />
      <ExtraBackwardOffset value="-0.100000" />
      <ExtraZOffset value="-0.100000" />
      <CoverBoundInfos />
    </Item>
    <Item type="CVehicleCoverBoundOffsetInfo">
      <Name>IMPALER5_COVER_OFFSET_INFO</Name>
      <ExtraSideOffset value="-0.050000" />
      <ExtraForwardOffset value="0.050000" />
      <ExtraBackwardOffset value="-0.350000" />
      <ExtraZOffset value="0.300000" />
      <CoverBoundInfos>
        <Item>
          <Name>All</Name>
          <Position x="0.000000" y="-0.170000" z="0.230000" />
          <Length value="5.420000" />
          <Width value="2.000000" />
          <Height value="1.000000" />
          <ActiveBoundExclusions />
        </Item>
      </CoverBoundInfos>
    </Item>
    <Item type="CVehicleCoverBoundOffsetInfo">
      <Name>IMPALER6_COVER_OFFSET_INFO</Name>
      <ExtraSideOffset value="-0.030000" />
      <ExtraForwardOffset value="-0.100000" />
      <ExtraBackwardOffset value="-0.110000" />
      <ExtraZOffset value="-0.050000" />
      <CoverBoundInfos />
    </Item>
    <Item type="CVehicleCoverBoundOffsetInfo">
      <Name>PHANTOM4_COVER_OFFSET_INFO</Name>
      <ExtraSideOffset value="0.100000" />
      <ExtraForwardOffset value="-0.200000" />
      <ExtraBackwardOffset value="-0.250000" />
      <ExtraZOffset value="1.000000" />
      <CoverBoundInfos>
        <Item>
          <Name>Front</Name>
          <Position x="0.000000" y="3.040000" z="-0.020000" />
          <Length value="3.290000" />
          <Width value="1.700000" />
          <Height value="2.100000" />
          <ActiveBoundExclusions />
        </Item>
        <Item>
          <Name>FRONT_WHEELS</Name>
          <Position x="0.000000" y="3.040000" z="-0.370000" />
          <Length value="3.290000" />
          <Width value="2.860000" />
          <Height value="1.400000" />
          <ActiveBoundExclusions />
        </Item>
        <Item>
          <Name>Main</Name>
          <Position x="0.000000" y="0.440000" z="0.000000" />
          <Length value="1.900000" />
          <Width value="2.800000" />
          <Height value="2.000000" />
          <ActiveBoundExclusions />
        </Item>
        <Item>
          <Name>REAR</Name>
          <Position x="0.000000" y="-2.540000" z="-0.290000" />
          <Length value="4.060000" />
          <Width value="2.900000" />
          <Height value="1.500000" />
          <ActiveBoundExclusions />
        </Item>
      </CoverBoundInfos>
    </Item>
    <Item type="CVehicleCoverBoundOffsetInfo">
      <Name>POLGAUNTLET_COVER_OFFSET_INFO</Name>
      <ExtraSideOffset value="-0.020000" />
      <ExtraForwardOffset value="-0.080000" />
      <ExtraBackwardOffset value="-0.200000" />
      <ExtraZOffset value="-0.100000" />
      <CoverBoundInfos />
    </Item>
    <Item type="CVehicleCoverBoundOffsetInfo">
      <Name>TERMINUS_COVER_OFFSET_INFO</Name>
      <ExtraSideOffset value="-0.030000" />
      <ExtraForwardOffset value="-0.010000" />
      <ExtraBackwardOffset value="-0.090000" />
      <ExtraZOffset value="0.100000" />
      <CoverBoundInfos />
    </Item>
    <Item type="CVehicleCoverBoundOffsetInfo">
      <Name>TRAILERS5_COVER_OFFSET_INFO</Name>
      <ExtraSideOffset value="0.100000" />
      <ExtraForwardOffset value="-0.200000" />
      <ExtraBackwardOffset value="-0.250000" />
      <ExtraZOffset value="1.000000" />
      <CoverBoundInfos>
        <Item>
          <Name>WHEELS_L</Name>
          <Position x="-1.350000" y="-1.800000" z="-2.140000" />
          <Length value="7.500000" />
          <Width value="0.250000" />
          <Height value="1.500000" />
          <ActiveBoundExclusions />
        </Item>
        <Item>
          <Name>WHEELS_R</Name>
          <Position x="1.350000" y="-1.800000" z="-2.140000" />
          <Length value="7.500000" />
          <Width value="0.250000" />
          <Height value="1.500000" />
          <ActiveBoundExclusions />
        </Item>
        <Item>
          <Name>REAR</Name>
          <Position x="0.000000" y="-5.900000" z="-1.890000" />
          <Length value="0.700000" />
          <Width value="2.850000" />
          <Height value="2.000000" />
          <ActiveBoundExclusions />
        </Item>
        <Item>
          <Name>TOP_REAR</Name>
          <Position x="0.000000" y="-4.450000" z="0.270000" />
          <Length value="3.520000" />
          <Width value="2.840000" />
          <Height value="2.300000" />
          <ActiveBoundExclusions />
        </Item>
        <Item>
          <Name>TOP_FRONT</Name>
          <Position x="0.000000" y="1.650000" z="0.020000" />
          <Length value="8.700000" />
          <Width value="2.840000" />
          <Height value="2.840000" />
          <ActiveBoundExclusions />
        </Item>
      </CoverBoundInfos>
    </Item>
    <Item type="CVehicleCoverBoundOffsetInfo">
      <Name>VIVANITE_COVER_OFFSET_INFO</Name>
      <ExtraSideOffset value="0.075000" />
      <ExtraForwardOffset value="-0.050000" />
      <ExtraBackwardOffset value="-0.250000" />
      <ExtraZOffset value="0.100000" />
      <CoverBoundInfos />
    </Item>
  </VehicleCoverBoundOffsetInfos>
  <BicycleInfos />
  <POVTuningInfos />
  <EntryAnimVariations />
  <VehicleExtraPointsInfos>
    <Item type="CVehicleExtraPointsInfo">
      <Name>EXTRA_VEHICLE_POINTS_FREIGHT2_LEFT</Name>
      <ExtraVehiclePoints>
        <Item>
          <LocationType>SEAT_RELATIVE</LocationType>
          <PointType>GET_IN</PointType>
          <Position x="2.500000" y="-0.950000" z="0.320000" />
          <Heading value="0.000000" />
          <Pitch value="0.000000" />
          <BoneName />
        </Item>
        <Item>
          <LocationType>SEAT_RELATIVE</LocationType>
          <PointType>GET_OUT</PointType>
          <Position x="2.495000" y="-1.030000" z="0.360000" />
          <Heading value="3.160000" />
          <Pitch value="0.000000" />
          <BoneName />
        </Item>
      </ExtraVehiclePoints>
    </Item>
    <Item type="CVehicleExtraPointsInfo">
      <Name>EXTRA_VEHICLE_POINTS_FREIGHT2_RIGHT</Name>
      <ExtraVehiclePoints>
        <Item>
          <LocationType>SEAT_RELATIVE</LocationType>
          <PointType>GET_IN</PointType>
          <Position x="0.100000" y="-0.950000" z="0.320000" />
          <Heading value="0.000000" />
          <Pitch value="0.000000" />
          <BoneName />
        </Item>
        <Item>
          <LocationType>SEAT_RELATIVE</LocationType>
          <PointType>GET_OUT</PointType>
          <Position x="0.070000" y="-1.050000" z="0.400000" />
          <Heading value="3.160000" />
          <Pitch value="0.000000" />
          <BoneName />
        </Item>
      </ExtraVehiclePoints>
    </Item>
  </VehicleExtraPointsInfos>
  <DrivebyWeaponGroups />
  <VehicleDriveByAnimInfos>
    <Item type="CVehicleDriveByAnimInfo">
      <Name>STD_ASTEROPE2_ANIM_INFO_ONE_HANDED_PS</Name>
      <WeaponGroup ref="DRIVEBY_DEFAULT_ONE_HANDED" />
      <DriveByClipSet>clipset@veh@driveby@astron2@ps</DriveByClipSet>
      <FirstPersonDriveByClipSet>driveby@first_person@passenger_left_handed@1h</FirstPersonDriveByClipSet>
      <AltFirstPersonDriveByClipSets>
        <Item>
          <Weapons>
            <Item>WEAPON_MICROSMG</Item>
          </Weapons>
          <ClipSet>driveby@first_person@passenger_left_handed@smg</ClipSet>
        </Item>
      </AltFirstPersonDriveByClipSets>
      <RestrictedDriveByClipSet />
      <VehicleMeleeClipSet />
      <FirstPersonVehicleMeleeClipSet />
      <Network>STD_CAR_DRIVEBY</Network>
      <UseOverrideAngles value="false" />
      <OverrideAnglesInThirdPersonOnly value="false" />
      <OverrideMinAimAngle value="0.000000" />
      <OverrideMaxAimAngle value="0.000000" />
      <OverrideMinRestrictedAimAngle value="0.000000" />
      <OverrideMaxRestrictedAimAngle value="0.000000" />
    </Item>
    <Item type="CVehicleDriveByAnimInfo">
      <Name>STD_FREIGHT2_ANIM_INFO_THROW_DS</Name>
      <WeaponGroup ref="DRIVEBY_THROW" />
      <DriveByClipSet>driveby@freight2@ds@throw</DriveByClipSet>
      <FirstPersonDriveByClipSet>driveby@first_person@freight2@ds@throw</FirstPersonDriveByClipSet>
      <AltFirstPersonDriveByClipSets />
      <RestrictedDriveByClipSet />
      <VehicleMeleeClipSet />
      <FirstPersonVehicleMeleeClipSet />
      <Network>MOUNTED_THROW</Network>
      <UseOverrideAngles value="false" />
      <OverrideAnglesInThirdPersonOnly value="false" />
      <OverrideMinAimAngle value="0.000000" />
      <OverrideMaxAimAngle value="0.000000" />
      <OverrideMinRestrictedAimAngle value="0.000000" />
      <OverrideMaxRestrictedAimAngle value="0.000000" />
    </Item>
    <Item type="CVehicleDriveByAnimInfo">
      <Name>STD_FREIGHT2_ANIM_INFO_ONE_HANDED_DS</Name>
      <WeaponGroup ref="DRIVEBY_DEFAULT_ONE_HANDED" />
      <DriveByClipSet>driveby@freight2@ds</DriveByClipSet>
      <FirstPersonDriveByClipSet>driveby@first_person@freight2@ds@1h</FirstPersonDriveByClipSet>
      <AltFirstPersonDriveByClipSets>
        <Item>
          <Weapons>
            <Item>WEAPON_MICROSMG</Item>
            <Item>WEAPON_TECPISTOL</Item>
          </Weapons>
          <ClipSet>driveby@first_person@driver@smg</ClipSet>
        </Item>
      </AltFirstPersonDriveByClipSets>
      <RestrictedDriveByClipSet />
      <VehicleMeleeClipSet />
      <FirstPersonVehicleMeleeClipSet />
      <Network>STD_CAR_DRIVEBY</Network>
      <UseOverrideAngles value="false" />
      <OverrideAnglesInThirdPersonOnly value="false" />
      <OverrideMinAimAngle value="0.000000" />
      <OverrideMaxAimAngle value="0.000000" />
      <OverrideMinRestrictedAimAngle value="0.000000" />
      <OverrideMaxRestrictedAimAngle value="0.000000" />
    </Item>
    <Item type="CVehicleDriveByAnimInfo">
      <Name>STD_FREIGHT2_ANIM_INFO_THROW_PS</Name>
      <WeaponGroup ref="DRIVEBY_THROW" />
      <DriveByClipSet>driveby@freight2@ps@throw</DriveByClipSet>
      <FirstPersonDriveByClipSet>driveby@first_person@freight2@ps@throw</FirstPersonDriveByClipSet>
      <AltFirstPersonDriveByClipSets />
      <RestrictedDriveByClipSet />
      <VehicleMeleeClipSet />
      <FirstPersonVehicleMeleeClipSet />
      <Network>MOUNTED_THROW</Network>
      <UseOverrideAngles value="false" />
      <OverrideAnglesInThirdPersonOnly value="false" />
      <OverrideMinAimAngle value="0.000000" />
      <OverrideMaxAimAngle value="0.000000" />
      <OverrideMinRestrictedAimAngle value="0.000000" />
      <OverrideMaxRestrictedAimAngle value="0.000000" />
    </Item>
    <Item type="CVehicleDriveByAnimInfo">
      <Name>STD_FREIGHT2_ANIM_INFO_ONE_HANDED_PS</Name>
      <WeaponGroup ref="DRIVEBY_DEFAULT_ONE_HANDED" />
      <DriveByClipSet>driveby@freight2@ps</DriveByClipSet>
      <FirstPersonDriveByClipSet>driveby@first_person@freight2@ps@1h</FirstPersonDriveByClipSet>
      <AltFirstPersonDriveByClipSets />
      <RestrictedDriveByClipSet />
      <VehicleMeleeClipSet />
      <FirstPersonVehicleMeleeClipSet />
      <Network>STD_CAR_DRIVEBY</Network>
      <UseOverrideAngles value="false" />
      <OverrideAnglesInThirdPersonOnly value="false" />
      <OverrideMinAimAngle value="0.000000" />
      <OverrideMaxAimAngle value="0.000000" />
      <OverrideMinRestrictedAimAngle value="0.000000" />
      <OverrideMaxRestrictedAimAngle value="0.000000" />
    </Item>
    <Item type="CVehicleDriveByAnimInfo">
      <Name>VAN_VIVANITE_SIDEDOOR_DB_ANIM_INFO_TWO_HANDED_RDS</Name>
      <WeaponGroup ref="DRIVEBY_DEFAULT_TWO_HANDED" />
      <DriveByClipSet>drive_by@heli@frogger_rds_2h</DriveByClipSet>
      <FirstPersonDriveByClipSet />
      <AltFirstPersonDriveByClipSets />
      <RestrictedDriveByClipSet />
      <VehicleMeleeClipSet />
      <FirstPersonVehicleMeleeClipSet />
      <Network>REAR_HELI_DRIVEBY</Network>
      <UseOverrideAngles value="true" />
      <OverrideAnglesInThirdPersonOnly value="false" />
      <OverrideMinAimAngle value="-180.000000" />
      <OverrideMaxAimAngle value="0.000000" />
      <OverrideMinRestrictedAimAngle value="-180.000000" />
      <OverrideMaxRestrictedAimAngle value="10.000000" />
    </Item>
    <Item type="CVehicleDriveByAnimInfo">
      <Name>VAN_VIVANITE_SIDEDOOR_DB_ANIM_INFO_TWO_HANDED_RPS</Name>
      <WeaponGroup ref="DRIVEBY_DEFAULT_TWO_HANDED" />
      <DriveByClipSet>drive_by@heli@frogger_rps_2h</DriveByClipSet>
      <FirstPersonDriveByClipSet />
      <AltFirstPersonDriveByClipSets />
      <RestrictedDriveByClipSet />
      <VehicleMeleeClipSet />
      <FirstPersonVehicleMeleeClipSet />
      <Network>REAR_HELI_DRIVEBY</Network>
      <UseOverrideAngles value="true" />
      <OverrideAnglesInThirdPersonOnly value="false" />
      <OverrideMinAimAngle value="0.000000" />
      <OverrideMaxAimAngle value="180.000000" />
      <OverrideMinRestrictedAimAngle value="-10.000000" />
      <OverrideMaxRestrictedAimAngle value="180.000000" />
    </Item>
  </VehicleDriveByAnimInfos>
  <VehicleDriveByInfos>
    <Item type="CVehicleDriveByInfo">
      <Name>DRIVEBY_STD_ASTEROPE2_FRONT_RIGHT</Name>
      <MinAimSweepHeadingAngleDegs value="-225.000000" />
      <MaxAimSweepHeadingAngleDegs value="225.000000" />
      <FirstPersonMinAimSweepHeadingAngleDegs value="-180.000000" />
      <FirstPersonMaxAimSweepHeadingAngleDegs value="180.000000" />
      <FirstPersonUnarmedMinAimSweepHeadingAngleDegs value="-180.000000" />
      <FirstPersonUnarmedMaxAimSweepHeadingAngleDegs value="180.000000" />
      <MinRestrictedAimSweepHeadingAngleDegs value="-225.000000" />
      <MaxRestrictedAimSweepHeadingAngleDegs value="225.000000" />
      <MinSmashWindowAngleDegs value="17.500000" />
      <MaxSmashWindowAngleDegs value="180.000000" />
      <MinSmashWindowAngleFirstPersonDegs value="35.000000" />
      <MaxSmashWindowAngleFirstPersonDegs value="180.000000" />
      <MaxSpeedParam value="0.500000" />
      <MaxLongitudinalLeanBlendWeightDelta value="0.250000" />
      <MaxLateralLeanBlendWeightDelta value="0.250000" />
      <ApproachSpeedToWithinMaxBlendDelta value="1.000000" />
      <SpineAdditiveBlendInDelay value="0.050000" />
      <SpineAdditiveBlendInDurationStill value="0.150000" />
      <SpineAdditiveBlendInDuration value="0.750000" />
      <SpineAdditiveBlendOutDelay value="0.000000" />
      <SpineAdditiveBlendOutDuration value="0.500000" />
      <MinUnarmedDrivebyYawIfWindowRolledUp value="-3.140000" />
      <MaxUnarmedDrivebyYawIfWindowRolledUp value="0.925000" />
      <DriveByAnimInfos>
        <Item ref="STD_DB_ANIM_INFO_UNARMED_PS" />
        <Item ref="STD_ASTEROPE2_ANIM_INFO_ONE_HANDED_PS" />
        <Item ref="STD_DB_ANIM_INFO_THROW_PS" />
      </DriveByAnimInfos>
      <DriveByCamera />
      <PovDriveByCamera />
      <DriveByFlags>UseMountedProjectileTask LeftHandedFirstPersonAnims LeftHandedUnarmedFirstPersonAnims</DriveByFlags>
    </Item>
    <Item type="CVehicleDriveByInfo">
      <Name>DRIVEBY_FREIGHT2_FRONT_LEFT</Name>
      <MinAimSweepHeadingAngleDegs value="-190.000000" />
      <MaxAimSweepHeadingAngleDegs value="190.000000" />
      <FirstPersonMinAimSweepHeadingAngleDegs value="-180.000000" />
      <FirstPersonMaxAimSweepHeadingAngleDegs value="180.000000" />
      <FirstPersonUnarmedMinAimSweepHeadingAngleDegs value="-180.000000" />
      <FirstPersonUnarmedMaxAimSweepHeadingAngleDegs value="180.000000" />
      <MinRestrictedAimSweepHeadingAngleDegs value="-190.000000" />
      <MaxRestrictedAimSweepHeadingAngleDegs value="190.000000" />
      <MinSmashWindowAngleDegs value="-180.000000" />
      <MaxSmashWindowAngleDegs value="-17.500000" />
      <MinSmashWindowAngleFirstPersonDegs value="-180.000000" />
      <MaxSmashWindowAngleFirstPersonDegs value="-35.000000" />
      <MaxSpeedParam value="0.500000" />
      <MaxLongitudinalLeanBlendWeightDelta value="0.250000" />
      <MaxLateralLeanBlendWeightDelta value="0.250000" />
      <ApproachSpeedToWithinMaxBlendDelta value="1.000000" />
      <SpineAdditiveBlendInDelay value="0.050000" />
      <SpineAdditiveBlendInDurationStill value="0.150000" />
      <SpineAdditiveBlendInDuration value="0.750000" />
      <SpineAdditiveBlendOutDelay value="0.000000" />
      <SpineAdditiveBlendOutDuration value="0.500000" />
      <MinUnarmedDrivebyYawIfWindowRolledUp value="-0.175000" />
      <MaxUnarmedDrivebyYawIfWindowRolledUp value="3.140000" />
      <DriveByAnimInfos>
        <Item ref="STD_FREIGHT2_ANIM_INFO_ONE_HANDED_DS" />
        <Item ref="STD_FREIGHT2_ANIM_INFO_THROW_DS" />
      </DriveByAnimInfos>
      <DriveByCamera />
      <PovDriveByCamera />
      <DriveByFlags>UseMountedProjectileTask LeftHandedUnarmedFirstPersonAnims</DriveByFlags>
    </Item>
    <Item type="CVehicleDriveByInfo">
      <Name>DRIVEBY_FREIGHT2_FRONT_RIGHT</Name>
      <MinAimSweepHeadingAngleDegs value="-225.000000" />
      <MaxAimSweepHeadingAngleDegs value="225.000000" />
      <FirstPersonMinAimSweepHeadingAngleDegs value="-180.000000" />
      <FirstPersonMaxAimSweepHeadingAngleDegs value="180.000000" />
      <FirstPersonUnarmedMinAimSweepHeadingAngleDegs value="-180.000000" />
      <FirstPersonUnarmedMaxAimSweepHeadingAngleDegs value="180.000000" />
      <MinRestrictedAimSweepHeadingAngleDegs value="-225.000000" />
      <MaxRestrictedAimSweepHeadingAngleDegs value="225.000000" />
      <MinSmashWindowAngleDegs value="17.500000" />
      <MaxSmashWindowAngleDegs value="180.000000" />
      <MinSmashWindowAngleFirstPersonDegs value="35.000000" />
      <MaxSmashWindowAngleFirstPersonDegs value="180.000000" />
      <MaxSpeedParam value="0.500000" />
      <MaxLongitudinalLeanBlendWeightDelta value="0.250000" />
      <MaxLateralLeanBlendWeightDelta value="0.250000" />
      <ApproachSpeedToWithinMaxBlendDelta value="1.000000" />
      <SpineAdditiveBlendInDelay value="0.050000" />
      <SpineAdditiveBlendInDurationStill value="0.150000" />
      <SpineAdditiveBlendInDuration value="0.750000" />
      <SpineAdditiveBlendOutDelay value="0.000000" />
      <SpineAdditiveBlendOutDuration value="0.500000" />
      <MinUnarmedDrivebyYawIfWindowRolledUp value="-3.140000" />
      <MaxUnarmedDrivebyYawIfWindowRolledUp value="0.925000" />
      <DriveByAnimInfos>
        <Item ref="STD_FREIGHT2_ANIM_INFO_ONE_HANDED_PS" />
        <Item ref="STD_FREIGHT2_ANIM_INFO_THROW_PS" />
      </DriveByAnimInfos>
      <DriveByCamera />
      <PovDriveByCamera />
      <DriveByFlags>UseMountedProjectileTask</DriveByFlags>
    </Item>
    <Item type="CVehicleDriveByInfo">
      <Name>DRIVEBY_STD_TERMINUS_FRONT_RIGHT</Name>
      <MinAimSweepHeadingAngleDegs value="-225.000000" />
      <MaxAimSweepHeadingAngleDegs value="225.000000" />
      <FirstPersonMinAimSweepHeadingAngleDegs value="-180.000000" />
      <FirstPersonMaxAimSweepHeadingAngleDegs value="180.000000" />
      <FirstPersonUnarmedMinAimSweepHeadingAngleDegs value="-180.000000" />
      <FirstPersonUnarmedMaxAimSweepHeadingAngleDegs value="180.000000" />
      <MinRestrictedAimSweepHeadingAngleDegs value="-225.000000" />
      <MaxRestrictedAimSweepHeadingAngleDegs value="225.000000" />
      <MinSmashWindowAngleDegs value="17.500000" />
      <MaxSmashWindowAngleDegs value="180.000000" />
      <MinSmashWindowAngleFirstPersonDegs value="35.000000" />
      <MaxSmashWindowAngleFirstPersonDegs value="180.000000" />
      <MaxSpeedParam value="0.500000" />
      <MaxLongitudinalLeanBlendWeightDelta value="0.250000" />
      <MaxLateralLeanBlendWeightDelta value="0.250000" />
      <ApproachSpeedToWithinMaxBlendDelta value="1.000000" />
      <SpineAdditiveBlendInDelay value="0.050000" />
      <SpineAdditiveBlendInDurationStill value="0.150000" />
      <SpineAdditiveBlendInDuration value="0.750000" />
      <SpineAdditiveBlendOutDelay value="0.000000" />
      <SpineAdditiveBlendOutDuration value="0.500000" />
      <MinUnarmedDrivebyYawIfWindowRolledUp value="-3.140000" />
      <MaxUnarmedDrivebyYawIfWindowRolledUp value="0.925000" />
      <DriveByAnimInfos>
        <Item ref="STD_DB_ANIM_INFO_UNARMED_PS" />
        <Item ref="STD_ASTEROPE2_ANIM_INFO_ONE_HANDED_PS" />
        <Item ref="STD_DB_ANIM_INFO_THROW_PS" />
      </DriveByAnimInfos>
      <DriveByCamera />
      <PovDriveByCamera />
      <DriveByFlags>UseMountedProjectileTask LeftHandedFirstPersonAnims LeftHandedUnarmedFirstPersonAnims</DriveByFlags>
    </Item>
    <Item type="CVehicleDriveByInfo">
      <Name>DRIVEBY_VAN_VIVANITE_FRONT_LEFT</Name>
      <MinAimSweepHeadingAngleDegs value="-190.000000" />
      <MaxAimSweepHeadingAngleDegs value="190.000000" />
      <FirstPersonMinAimSweepHeadingAngleDegs value="-180.000000" />
      <FirstPersonMaxAimSweepHeadingAngleDegs value="180.000000" />
      <FirstPersonUnarmedMinAimSweepHeadingAngleDegs value="-180.000000" />
      <FirstPersonUnarmedMaxAimSweepHeadingAngleDegs value="180.000000" />
      <MinRestrictedAimSweepHeadingAngleDegs value="-190.000000" />
      <MaxRestrictedAimSweepHeadingAngleDegs value="190.000000" />
      <MinSmashWindowAngleDegs value="-180.000000" />
      <MaxSmashWindowAngleDegs value="-25.000000" />
      <MinSmashWindowAngleFirstPersonDegs value="-180.000000" />
      <MaxSmashWindowAngleFirstPersonDegs value="-35.000000" />
      <MaxSpeedParam value="0.500000" />
      <MaxLongitudinalLeanBlendWeightDelta value="0.250000" />
      <MaxLateralLeanBlendWeightDelta value="0.250000" />
      <ApproachSpeedToWithinMaxBlendDelta value="1.000000" />
      <SpineAdditiveBlendInDelay value="0.050000" />
      <SpineAdditiveBlendInDurationStill value="0.150000" />
      <SpineAdditiveBlendInDuration value="0.750000" />
      <SpineAdditiveBlendOutDelay value="0.000000" />
      <SpineAdditiveBlendOutDuration value="0.500000" />
      <MinUnarmedDrivebyYawIfWindowRolledUp value="-0.400000" />
      <MaxUnarmedDrivebyYawIfWindowRolledUp value="3.140000" />
      <DriveByAnimInfos>
        <Item ref="VAN_DB_ANIM_INFO_UNARMED_DS" />
        <Item ref="RIOT_VAN_DB_ANIM_INFO_ONE_HANDED_DS" />
        <Item ref="RIOT_VAN_DB_ANIM_INFO_THROW_DS" />
      </DriveByAnimInfos>
      <DriveByCamera />
      <PovDriveByCamera />
      <DriveByFlags>UseMountedProjectileTask UseThreeAnimThrow LeftHandedProjctiles LeftHandedUnarmedFirstPersonAnims</DriveByFlags>
    </Item>
    <Item type="CVehicleDriveByInfo">
      <Name>DRIVEBY_VAN_VIVANITE_FRONT_RIGHT</Name>
      <MinAimSweepHeadingAngleDegs value="-190.000000" />
      <MaxAimSweepHeadingAngleDegs value="190.000000" />
      <FirstPersonMinAimSweepHeadingAngleDegs value="-180.000000" />
      <FirstPersonMaxAimSweepHeadingAngleDegs value="180.000000" />
      <FirstPersonUnarmedMinAimSweepHeadingAngleDegs value="-180.000000" />
      <FirstPersonUnarmedMaxAimSweepHeadingAngleDegs value="180.000000" />
      <MinRestrictedAimSweepHeadingAngleDegs value="-190.000000" />
      <MaxRestrictedAimSweepHeadingAngleDegs value="190.000000" />
      <MinSmashWindowAngleDegs value="25.000000" />
      <MaxSmashWindowAngleDegs value="180.000000" />
      <MinSmashWindowAngleFirstPersonDegs value="35.000000" />
      <MaxSmashWindowAngleFirstPersonDegs value="180.000000" />
      <MaxSpeedParam value="0.500000" />
      <MaxLongitudinalLeanBlendWeightDelta value="0.250000" />
      <MaxLateralLeanBlendWeightDelta value="0.250000" />
      <ApproachSpeedToWithinMaxBlendDelta value="1.000000" />
      <SpineAdditiveBlendInDelay value="0.050000" />
      <SpineAdditiveBlendInDurationStill value="0.150000" />
      <SpineAdditiveBlendInDuration value="0.750000" />
      <SpineAdditiveBlendOutDelay value="0.000000" />
      <SpineAdditiveBlendOutDuration value="0.500000" />
      <MinUnarmedDrivebyYawIfWindowRolledUp value="-3.140000" />
      <MaxUnarmedDrivebyYawIfWindowRolledUp value="0.400000" />
      <DriveByAnimInfos>
        <Item ref="VAN_DB_ANIM_INFO_UNARMED_PS" />
        <Item ref="RIOT_VAN_DB_ANIM_INFO_ONE_HANDED_PS" />
        <Item ref="RIOT_VAN_DB_ANIM_INFO_THROW_PS" />
      </DriveByAnimInfos>
      <DriveByCamera />
      <PovDriveByCamera />
      <DriveByFlags>UseMountedProjectileTask UseThreeAnimThrow LeftHandedFirstPersonAnims LeftHandedUnarmedFirstPersonAnims</DriveByFlags>
    </Item>
    <Item type="CVehicleDriveByInfo">
      <Name>DRIVEBY_VAN_VIVANITE_SIDEDOOR_REAR_LEFT</Name>
      <MinAimSweepHeadingAngleDegs value="-190.000000" />
      <MaxAimSweepHeadingAngleDegs value="190.000000" />
      <FirstPersonMinAimSweepHeadingAngleDegs value="-180.000000" />
      <FirstPersonMaxAimSweepHeadingAngleDegs value="180.000000" />
      <FirstPersonUnarmedMinAimSweepHeadingAngleDegs value="-180.000000" />
      <FirstPersonUnarmedMaxAimSweepHeadingAngleDegs value="180.000000" />
      <MinRestrictedAimSweepHeadingAngleDegs value="-190.000000" />
      <MaxRestrictedAimSweepHeadingAngleDegs value="190.000000" />
      <MinSmashWindowAngleDegs value="0.000000" />
      <MaxSmashWindowAngleDegs value="0.000000" />
      <MinSmashWindowAngleFirstPersonDegs value="0.000000" />
      <MaxSmashWindowAngleFirstPersonDegs value="0.000000" />
      <MaxSpeedParam value="0.500000" />
      <MaxLongitudinalLeanBlendWeightDelta value="0.250000" />
      <MaxLateralLeanBlendWeightDelta value="0.250000" />
      <ApproachSpeedToWithinMaxBlendDelta value="1.000000" />
      <SpineAdditiveBlendInDelay value="0.050000" />
      <SpineAdditiveBlendInDurationStill value="0.150000" />
      <SpineAdditiveBlendInDuration value="0.750000" />
      <SpineAdditiveBlendOutDelay value="0.000000" />
      <SpineAdditiveBlendOutDuration value="0.500000" />
      <MinUnarmedDrivebyYawIfWindowRolledUp value="-3.140000" />
      <MaxUnarmedDrivebyYawIfWindowRolledUp value="3.140000" />
      <DriveByAnimInfos>
        <Item ref="RANGER_DB_ANIM_INFO_UNARMED_RDS" />
        <Item ref="TRUCK_DB_ANIM_INFO_ONE_HANDED_RDS" />
        <Item ref="VAN_VIVANITE_SIDEDOOR_DB_ANIM_INFO_TWO_HANDED_RDS" />
        <Item ref="RANGER_DB_ANIM_INFO_THROW_RDS" />
      </DriveByAnimInfos>
      <DriveByCamera />
      <PovDriveByCamera />
      <DriveByFlags>DampenRecoil NeedToOpenDoors UseThreeAnimIntroOutro UseMountedProjectileTask UseThreeAnimThrow LeftHandedProjctiles UseBlockingAnimsOutsideAimRange</DriveByFlags>
    </Item>
    <Item type="CVehicleDriveByInfo">
      <Name>DRIVEBY_VAN_VIVANITE_SIDEDOOR_REAR_RIGHT</Name>
      <MinAimSweepHeadingAngleDegs value="-190.000000" />
      <MaxAimSweepHeadingAngleDegs value="190.000000" />
      <FirstPersonMinAimSweepHeadingAngleDegs value="-180.000000" />
      <FirstPersonMaxAimSweepHeadingAngleDegs value="180.000000" />
      <FirstPersonUnarmedMinAimSweepHeadingAngleDegs value="-180.000000" />
      <FirstPersonUnarmedMaxAimSweepHeadingAngleDegs value="180.000000" />
      <MinRestrictedAimSweepHeadingAngleDegs value="-190.000000" />
      <MaxRestrictedAimSweepHeadingAngleDegs value="190.000000" />
      <MinSmashWindowAngleDegs value="0.000000" />
      <MaxSmashWindowAngleDegs value="0.000000" />
      <MinSmashWindowAngleFirstPersonDegs value="0.000000" />
      <MaxSmashWindowAngleFirstPersonDegs value="0.000000" />
      <MaxSpeedParam value="0.500000" />
      <MaxLongitudinalLeanBlendWeightDelta value="0.250000" />
      <MaxLateralLeanBlendWeightDelta value="0.250000" />
      <ApproachSpeedToWithinMaxBlendDelta value="1.000000" />
      <SpineAdditiveBlendInDelay value="0.050000" />
      <SpineAdditiveBlendInDurationStill value="0.150000" />
      <SpineAdditiveBlendInDuration value="0.750000" />
      <SpineAdditiveBlendOutDelay value="0.000000" />
      <SpineAdditiveBlendOutDuration value="0.500000" />
      <MinUnarmedDrivebyYawIfWindowRolledUp value="-3.140000" />
      <MaxUnarmedDrivebyYawIfWindowRolledUp value="3.140000" />
      <DriveByAnimInfos>
        <Item ref="RANGER_DB_ANIM_INFO_UNARMED_RPS" />
        <Item ref="TRUCK_DB_ANIM_INFO_ONE_HANDED_RPS" />
        <Item ref="VAN_VIVANITE_SIDEDOOR_DB_ANIM_INFO_TWO_HANDED_RPS" />
        <Item ref="RANGER_DB_ANIM_INFO_THROW_RPS" />
      </DriveByAnimInfos>
      <DriveByCamera />
      <PovDriveByCamera />
      <DriveByFlags>DampenRecoil NeedToOpenDoors UseThreeAnimIntroOutro UseMountedProjectileTask UseThreeAnimThrow LeftHandedFirstPersonAnims WeaponAttachedToLeftHand1HOnly UseBlockingAnimsOutsideAimRange</DriveByFlags>
    </Item>
  </VehicleDriveByInfos>
  <VehicleSeatInfos>
    <Item type="CVehicleSeatInfo">
      <Name>SEAT_FREIGHT2_FRONT_LEFT</Name>
      <SeatBoneName>seat_dside_f</SeatBoneName>
      <ShuffleLink />
      <RearSeatLink />
      <DefaultCarTask>TASK_DRIVE_WANDER</DefaultCarTask>
      <SeatFlags>IsFrontSeat</SeatFlags>
      <HairScale value="-0.275000" />
      <ShuffleLink2 />
    </Item>
    <Item type="CVehicleSeatInfo">
      <Name>SEAT_FREIGHT2_FRONT_RIGHT</Name>
      <SeatBoneName>seat_pside_f</SeatBoneName>
      <ShuffleLink>SEAT_FREIGHT2_FRONT_LEFT</ShuffleLink>
      <RearSeatLink />
      <DefaultCarTask>TASK_DRIVE_WANDER</DefaultCarTask>
      <SeatFlags>IsFrontSeat</SeatFlags>
      <HairScale value="-0.275000" />
      <ShuffleLink2 />
    </Item>
  </VehicleSeatInfos>
  <VehicleSeatAnimInfos>
    <Item type="CVehicleSeatAnimInfo">
      <Name>SEAT_ANIM_STD_ASTEROPE2_FRONT_RIGHT</Name>
      <DriveByInfo ref="DRIVEBY_STD_ASTEROPE2_FRONT_RIGHT" />
      <InsideClipSetMap ref="INSIDE_CLIPSET_MAP_STD_FRONT_RIGHT" />
      <PanicClipSet>clipset@veh@std@ps@idle_panic</PanicClipSet>
      <AgitatedClipSet />
      <DuckedClipSet>clipset@veh@std@ps@idle_duck</DuckedClipSet>
      <FemaleClipSet />
      <LowriderLeanClipSet />
      <AltLowriderLeanClipSet />
      <LowLODIdleAnim>STD_CAR_FRONT_PS_IDLE</LowLODIdleAnim>
      <SeatAmbientContext>IN_CAR_STANDARD</SeatAmbientContext>
      <InVehicleMoveNetwork>VEHICLE_DEFAULT</InVehicleMoveNetwork>
      <SeatAnimFlags>UseStandardInVehicleAnims HasPanicAnims UseCloseDoorBlendAnims PreventShuffleJack</SeatAnimFlags>
      <SteeringSmoothing value="0.100000" />
      <ExitToAimInfoName>MESA</ExitToAimInfoName>
      <MaleGestureClipSetId>ANIM_GROUP_GESTURE_M_CAR_STD_PS</MaleGestureClipSetId>
      <FemaleGestureClipSetId>ANIM_GROUP_GESTURE_F_CAR_STD_PS</FemaleGestureClipSetId>
      <FPSMinSteeringRateOverride value="-1.000000" />
      <FPSMaxSteeringRateOverride value="-1.000000" />
      <SeatCollisionBoundsOffset x="0.000000" y="0.000000" z="0.000000" />
    </Item>
    <Item type="CVehicleSeatAnimInfo">
      <Name>SEAT_ANIM_FREIGHT2_FRONT_LEFT</Name>
      <DriveByInfo ref="DRIVEBY_FREIGHT2_FRONT_LEFT" />
      <InsideClipSetMap ref="INSIDE_CLIPSET_MAP_FREIGHT2_FRONT_LEFT" />
      <PanicClipSet />
      <AgitatedClipSet />
      <DuckedClipSet />
      <FemaleClipSet />
      <LowriderLeanClipSet />
      <AltLowriderLeanClipSet />
      <LowLODIdleAnim>STD_CAR_FRONT_DS_IDLE</LowLODIdleAnim>
      <SeatAmbientContext />
      <InVehicleMoveNetwork>VEHICLE_DEFAULT</InVehicleMoveNetwork>
      <SeatAnimFlags>UseStandardInVehicleAnims PreventShuffleJack NoShunts</SeatAnimFlags>
      <SteeringSmoothing value="0.100000" />
      <ExitToAimInfoName />
      <MaleGestureClipSetId />
      <FemaleGestureClipSetId />
      <FPSMinSteeringRateOverride value="-1.000000" />
      <FPSMaxSteeringRateOverride value="-1.000000" />
      <SeatCollisionBoundsOffset x="0.000000" y="0.000000" z="0.000000" />
    </Item>
    <Item type="CVehicleSeatAnimInfo">
      <Name>SEAT_ANIM_FREIGHT2_FRONT_RIGHT</Name>
      <DriveByInfo ref="DRIVEBY_FREIGHT2_FRONT_RIGHT" />
      <InsideClipSetMap ref="INSIDE_CLIPSET_MAP_FREIGHT2_FRONT_RIGHT" />
      <PanicClipSet />
      <AgitatedClipSet />
      <DuckedClipSet />
      <FemaleClipSet />
      <LowriderLeanClipSet />
      <AltLowriderLeanClipSet />
      <LowLODIdleAnim>STD_CAR_FRONT_PS_IDLE</LowLODIdleAnim>
      <SeatAmbientContext />
      <InVehicleMoveNetwork>VEHICLE_DEFAULT</InVehicleMoveNetwork>
      <SeatAnimFlags>UseStandardInVehicleAnims PreventShuffleJack NoShunts</SeatAnimFlags>
      <SteeringSmoothing value="0.100000" />
      <ExitToAimInfoName />
      <MaleGestureClipSetId />
      <FemaleGestureClipSetId />
      <FPSMinSteeringRateOverride value="-1.000000" />
      <FPSMaxSteeringRateOverride value="-1.000000" />
      <SeatCollisionBoundsOffset x="0.000000" y="0.000000" z="0.000000" />
    </Item>
    <Item type="CVehicleSeatAnimInfo">
      <Name>SEAT_ANIM_STD_TERMINUS_FRONT_RIGHT</Name>
      <DriveByInfo ref="DRIVEBY_STD_TERMINUS_FRONT_RIGHT" />
      <InsideClipSetMap ref="INSIDE_CLIPSET_MAP_STD_FRONT_RIGHT" />
      <PanicClipSet>clipset@veh@std@ps@idle_panic</PanicClipSet>
      <AgitatedClipSet />
      <DuckedClipSet>clipset@veh@std@ps@idle_duck</DuckedClipSet>
      <FemaleClipSet />
      <LowriderLeanClipSet />
      <AltLowriderLeanClipSet />
      <LowLODIdleAnim>STD_CAR_FRONT_PS_IDLE</LowLODIdleAnim>
      <SeatAmbientContext>IN_CAR_STANDARD</SeatAmbientContext>
      <InVehicleMoveNetwork>VEHICLE_DEFAULT</InVehicleMoveNetwork>
      <SeatAnimFlags>UseStandardInVehicleAnims HasPanicAnims UseCloseDoorBlendAnims PreventShuffleJack</SeatAnimFlags>
      <SteeringSmoothing value="0.100000" />
      <ExitToAimInfoName>STANDARD</ExitToAimInfoName>
      <MaleGestureClipSetId>ANIM_GROUP_GESTURE_M_CAR_STD_PS</MaleGestureClipSetId>
      <FemaleGestureClipSetId>ANIM_GROUP_GESTURE_F_CAR_STD_PS</FemaleGestureClipSetId>
      <FPSMinSteeringRateOverride value="-1.000000" />
      <FPSMaxSteeringRateOverride value="-1.000000" />
      <SeatCollisionBoundsOffset x="0.000000" y="0.000000" z="0.000000" />
    </Item>
    <Item type="CVehicleSeatAnimInfo">
      <Name>SEAT_ANIM_VAN_VIVANITE_FRONT_LEFT</Name>
      <DriveByInfo ref="DRIVEBY_VAN_VIVANITE_FRONT_LEFT" />
      <InsideClipSetMap ref="INSIDE_CLIPSET_MAP_VAN_FRONT_LEFT" />
      <PanicClipSet>clipset@veh@van@ds@idle_panic</PanicClipSet>
      <AgitatedClipSet>clipset@veh@van@ds@idle_agitated</AgitatedClipSet>
      <DuckedClipSet>clipset@veh@van@ds@idle_duck</DuckedClipSet>
      <FemaleClipSet />
      <LowriderLeanClipSet />
      <AltLowriderLeanClipSet />
      <LowLODIdleAnim>VAN_FRONT_DS_IDLE</LowLODIdleAnim>
      <SeatAmbientContext>IN_VAN</SeatAmbientContext>
      <InVehicleMoveNetwork>VEHICLE_DEFAULT</InVehicleMoveNetwork>
      <SeatAnimFlags>UseStandardInVehicleAnims UseCloseDoorBlendAnims</SeatAnimFlags>
      <SteeringSmoothing value="0.100000" />
      <ExitToAimInfoName>MESA</ExitToAimInfoName>
      <MaleGestureClipSetId>ANIM_GROUP_GESTURE_M_CAR_VAN_DS</MaleGestureClipSetId>
      <FemaleGestureClipSetId>ANIM_GROUP_GESTURE_F_CAR_VAN_DS</FemaleGestureClipSetId>
      <FPSMinSteeringRateOverride value="-1.000000" />
      <FPSMaxSteeringRateOverride value="-1.000000" />
      <SeatCollisionBoundsOffset x="0.000000" y="0.000000" z="0.000000" />
    </Item>
    <Item type="CVehicleSeatAnimInfo">
      <Name>SEAT_ANIM_VAN_VIVANITE_FRONT_RIGHT</Name>
      <DriveByInfo ref="DRIVEBY_VAN_VIVANITE_FRONT_RIGHT" />
      <InsideClipSetMap ref="INSIDE_CLIPSET_MAP_VAN_FRONT_RIGHT" />
      <PanicClipSet>clipset@veh@van@ps@idle_panic</PanicClipSet>
      <AgitatedClipSet />
      <DuckedClipSet>clipset@veh@van@ps@idle_duck</DuckedClipSet>
      <FemaleClipSet />
      <LowriderLeanClipSet />
      <AltLowriderLeanClipSet />
      <LowLODIdleAnim>VAN_FRONT_PS_IDLE</LowLODIdleAnim>
      <SeatAmbientContext>IN_VAN</SeatAmbientContext>
      <InVehicleMoveNetwork>VEHICLE_DEFAULT</InVehicleMoveNetwork>
      <SeatAnimFlags>WeaponAttachedToLeftHand UseStandardInVehicleAnims UseCloseDoorBlendAnims PreventShuffleJack</SeatAnimFlags>
      <SteeringSmoothing value="0.100000" />
      <ExitToAimInfoName>MESA</ExitToAimInfoName>
      <MaleGestureClipSetId>ANIM_GROUP_GESTURE_M_CAR_VAN_PS</MaleGestureClipSetId>
      <FemaleGestureClipSetId>ANIM_GROUP_GESTURE_F_CAR_VAN_PS</FemaleGestureClipSetId>
      <FPSMinSteeringRateOverride value="-1.000000" />
      <FPSMaxSteeringRateOverride value="-1.000000" />
      <SeatCollisionBoundsOffset x="0.000000" y="0.000000" z="0.000000" />
    </Item>
    <Item type="CVehicleSeatAnimInfo">
      <Name>SEAT_ANIM_VAN_VIVANITE_SIDEDOOR_REAR_LEFT</Name>
      <DriveByInfo ref="DRIVEBY_VAN_VIVANITE_SIDEDOOR_REAR_LEFT" />
      <InsideClipSetMap ref="INSIDE_CLIPSET_MAP_VAN_VIVANITE_SIDEDOOR_REAR_LEFT" />
      <PanicClipSet>ANIM@VEH@LOWRIDER@STD@MOONBEAM@RDS@IDLE_PANIC</PanicClipSet>
      <AgitatedClipSet />
      <DuckedClipSet>clipset@veh@van@ps@idle_duck</DuckedClipSet>
      <FemaleClipSet />
      <LowriderLeanClipSet />
      <AltLowriderLeanClipSet />
      <LowLODIdleAnim>STD_CAR_FRONT_PS_IDLE</LowLODIdleAnim>
      <SeatAmbientContext />
      <InVehicleMoveNetwork>VEHICLE_DEFAULT</InVehicleMoveNetwork>
      <SeatAnimFlags>UseStandardInVehicleAnims HasPanicAnims PreventShuffleJack</SeatAnimFlags>
      <SteeringSmoothing value="0.100000" />
      <ExitToAimInfoName />
      <MaleGestureClipSetId />
      <FemaleGestureClipSetId />
      <FPSMinSteeringRateOverride value="-1.000000" />
      <FPSMaxSteeringRateOverride value="-1.000000" />
      <SeatCollisionBoundsOffset x="0.000000" y="0.000000" z="0.000000" />
    </Item>
    <Item type="CVehicleSeatAnimInfo">
      <Name>SEAT_ANIM_VAN_VIVANITE_SIDEDOOR_REAR_RIGHT</Name>
      <DriveByInfo ref="DRIVEBY_VAN_VIVANITE_SIDEDOOR_REAR_RIGHT" />
      <InsideClipSetMap ref="INSIDE_CLIPSET_MAP_VAN_VIVANITE_SIDEDOOR_REAR_RIGHT" />
      <PanicClipSet>ANIM@VEH@LOWRIDER@STD@MOONBEAM@RPS@IDLE_PANIC</PanicClipSet>
      <AgitatedClipSet />
      <DuckedClipSet>clipset@veh@van@ps@idle_duck</DuckedClipSet>
      <FemaleClipSet />
      <LowriderLeanClipSet />
      <AltLowriderLeanClipSet />
      <LowLODIdleAnim>STD_CAR_FRONT_PS_IDLE</LowLODIdleAnim>
      <SeatAmbientContext />
      <InVehicleMoveNetwork>VEHICLE_DEFAULT</InVehicleMoveNetwork>
      <SeatAnimFlags>WeaponAttachedToLeftHand UseStandardInVehicleAnims HasPanicAnims PreventShuffleJack</SeatAnimFlags>
      <SteeringSmoothing value="0.100000" />
      <ExitToAimInfoName />
      <MaleGestureClipSetId />
      <FemaleGestureClipSetId />
      <FPSMinSteeringRateOverride value="-1.000000" />
      <FPSMaxSteeringRateOverride value="-1.000000" />
      <SeatCollisionBoundsOffset x="0.000000" y="0.000000" z="0.000000" />
    </Item>
  </VehicleSeatAnimInfos>
  <VehicleEntryPointInfos>
    <Item type="CVehicleEntryPointInfo">
      <Name>ENTRY_POINT_FREIGHT2_FRONT_LEFT</Name>
      <DoorBoneName />
      <SecondDoorBoneName />
      <DoorHandleBoneName />
      <WindowId>FRONT_LEFT</WindowId>
      <VehicleSide>SIDE_RIGHT</VehicleSide>
      <AccessableSeats>
        <Item ref="SEAT_FREIGHT2_FRONT_LEFT" />
      </AccessableSeats>
      <VehicleExtraPointsInfo ref="EXTRA_VEHICLE_POINTS_FREIGHT2_LEFT" />
      <Flags>IgnoreSmashWindowCheck</Flags>
      <BlockJackReactionSides />
      <BreakoutTestPoint>POINT_TYPE_MAX</BreakoutTestPoint>
    </Item>
    <Item type="CVehicleEntryPointInfo">
      <Name>ENTRY_POINT_FREIGHT2_FRONT_RIGHT</Name>
      <DoorBoneName />
      <SecondDoorBoneName />
      <DoorHandleBoneName />
      <WindowId>FRONT_RIGHT</WindowId>
      <VehicleSide>SIDE_RIGHT</VehicleSide>
      <AccessableSeats>
        <Item ref="SEAT_FREIGHT2_FRONT_RIGHT" />
      </AccessableSeats>
      <VehicleExtraPointsInfo ref="EXTRA_VEHICLE_POINTS_FREIGHT2_RIGHT" />
      <Flags>IgnoreSmashWindowCheck</Flags>
      <BlockJackReactionSides />
      <BreakoutTestPoint>POINT_TYPE_MAX</BreakoutTestPoint>
    </Item>
  </VehicleEntryPointInfos>
  <VehicleEntryPointAnimInfos>
    <Item type="CVehicleEntryPointAnimInfo">
      <Name>ENTRY_POINT_ANIM_ALEUTIAN_FRONT_LEFT</Name>
      <CommonClipSetMap ref="COMMON_CLIPSET_MAP_AUTOMOBILE_LEFT" />
      <EntryClipSetMap ref="ENTRY_CLIPSET_MAP_ALEUTIAN_FRONT_LEFT" />
      <ExitClipSetMap ref="EXIT_CLIPSET_MAP_GRANGER_FRONT_LEFT" />
      <AlternateTryLockedDoorClipId />
      <AlternateForcedEntryClipId />
      <AlternateJackFromOutSideClipId>van_perp_ds_a</AlternateJackFromOutSideClipId>
      <AlternateBeJackedFromOutSideClipId>van_victim_ds_a</AlternateBeJackedFromOutSideClipId>
      <AlternateEntryPointClipSetId />
      <EnterVehicleMoveNetwork>ENTER_VEHICLE_STD</EnterVehicleMoveNetwork>
      <EntryTranslation x="-1.400000" y="-0.500000" z="0.250000" />
      <OpenDoorTranslation x="0.000000" y="0.000000" />
      <OpenDoorHeadingChange value="1.570000" />
      <EntryHeadingChange value="-1.570000" />
      <ExtraZForMPPlaneWarp value="-2.000000" />
      <EntryPointFlags>JackIncludesGetIn UsesNoDoorTransitionForExit UseOpenDoorBlendAnims UseGetUpAfterJack JackVariationsIncludeGetIn DeadJackIncludesGetIn HasCombatEntry</EntryPointFlags>
      <EntryAnimVariations ref="ENTRY_ANIM_VARIATIONS_VAN_FRONT_LEFT" />
      <NMJumpFromVehicleTuningSet>SLOWTRUCK</NMJumpFromVehicleTuningSet>
    </Item>
    <Item type="CVehicleEntryPointAnimInfo">
      <Name>ENTRY_POINT_ANIM_ALEUTIAN_FRONT_RIGHT</Name>
      <CommonClipSetMap ref="COMMON_CLIPSET_MAP_AUTOMOBILE_RIGHT" />
      <EntryClipSetMap ref="ENTRY_CLIPSET_MAP_ALEUTIAN_FRONT_RIGHT" />
      <ExitClipSetMap ref="EXIT_CLIPSET_MAP_GRANGER_FRONT_RIGHT" />
      <AlternateTryLockedDoorClipId />
      <AlternateForcedEntryClipId />
      <AlternateJackFromOutSideClipId>van_perp_ps_a</AlternateJackFromOutSideClipId>
      <AlternateBeJackedFromOutSideClipId>van_victim_ps_a</AlternateBeJackedFromOutSideClipId>
      <AlternateEntryPointClipSetId />
      <EnterVehicleMoveNetwork>ENTER_VEHICLE_STD</EnterVehicleMoveNetwork>
      <EntryTranslation x="1.400000" y="-0.500000" z="0.250000" />
      <OpenDoorTranslation x="0.000000" y="0.000000" />
      <OpenDoorHeadingChange value="-1.570000" />
      <EntryHeadingChange value="1.570000" />
      <ExtraZForMPPlaneWarp value="-2.000000" />
      <EntryPointFlags>JackIncludesGetIn UsesNoDoorTransitionForExit UseOpenDoorBlendAnims UseGetUpAfterJack JackVariationsIncludeGetIn DeadJackIncludesGetIn HasCombatEntry</EntryPointFlags>
      <EntryAnimVariations ref="ENTRY_ANIM_VARIATIONS_VAN_FRONT_RIGHT" />
      <NMJumpFromVehicleTuningSet />
    </Item>
    <Item type="CVehicleEntryPointAnimInfo">
      <Name>ENTRY_POINT_ANIM_ALEUTIAN_REAR_LEFT</Name>
      <CommonClipSetMap ref="COMMON_CLIPSET_MAP_AUTOMOBILE_LEFT" />
      <EntryClipSetMap ref="ENTRY_CLIPSET_MAP_GRANGER_REAR_LEFT" />
      <ExitClipSetMap ref="EXIT_CLIPSET_MAP_GRANGER_REAR_LEFT" />
      <AlternateTryLockedDoorClipId />
      <AlternateForcedEntryClipId />
      <AlternateJackFromOutSideClipId />
      <AlternateBeJackedFromOutSideClipId />
      <AlternateEntryPointClipSetId />
      <EnterVehicleMoveNetwork>ENTER_VEHICLE_STD</EnterVehicleMoveNetwork>
      <EntryTranslation x="-1.400000" y="-0.600000" z="0.250000" />
      <OpenDoorTranslation x="0.000000" y="0.000000" />
      <OpenDoorHeadingChange value="1.570000" />
      <EntryHeadingChange value="-1.570800" />
      <ExtraZForMPPlaneWarp value="-2.000000" />
      <EntryPointFlags>JackIncludesGetIn UsesNoDoorTransitionForExit UseOpenDoorBlendAnims UseGetUpAfterJack JackVariationsIncludeGetIn DeadJackIncludesGetIn</EntryPointFlags>
      <EntryAnimVariations ref="ENTRY_ANIM_VARIATIONS_VAN_FRONT_RIGHT" />
      <NMJumpFromVehicleTuningSet />
    </Item>
    <Item type="CVehicleEntryPointAnimInfo">
      <Name>ENTRY_POINT_ANIM_ALEUTIAN_REAR_RIGHT</Name>
      <CommonClipSetMap ref="COMMON_CLIPSET_MAP_AUTOMOBILE_RIGHT" />
      <EntryClipSetMap ref="ENTRY_CLIPSET_MAP_VAN_FRONT_RIGHT" />
      <ExitClipSetMap ref="EXIT_CLIPSET_MAP_VAN_FRONT_RIGHT" />
      <AlternateTryLockedDoorClipId />
      <AlternateForcedEntryClipId />
      <AlternateJackFromOutSideClipId>van_perp_ps_a</AlternateJackFromOutSideClipId>
      <AlternateBeJackedFromOutSideClipId>van_victim_ps_a</AlternateBeJackedFromOutSideClipId>
      <AlternateEntryPointClipSetId />
      <EnterVehicleMoveNetwork>ENTER_VEHICLE_STD</EnterVehicleMoveNetwork>
      <EntryTranslation x="1.400000" y="-0.600000" z="0.250000" />
      <OpenDoorTranslation x="0.000000" y="0.000000" />
      <OpenDoorHeadingChange value="-1.570000" />
      <EntryHeadingChange value="1.570000" />
      <ExtraZForMPPlaneWarp value="-2.000000" />
      <EntryPointFlags>JackIncludesGetIn UsesNoDoorTransitionForExit UseOpenDoorBlendAnims UseGetUpAfterJack JackVariationsIncludeGetIn DeadJackIncludesGetIn HasCombatEntry</EntryPointFlags>
      <EntryAnimVariations ref="ENTRY_ANIM_VARIATIONS_VAN_FRONT_RIGHT" />
      <NMJumpFromVehicleTuningSet />
    </Item>
    <Item type="CVehicleEntryPointAnimInfo">
      <Name>ENTRY_POINT_ANIM_STD_BALLER8_REAR_LEFT</Name>
      <CommonClipSetMap ref="COMMON_CLIPSET_MAP_AUTOMOBILE_LEFT" />
      <EntryClipSetMap ref="ENTRY_CLIPSET_MAP_STD_REAR_LEFT" />
      <ExitClipSetMap ref="EXIT_CLIPSET_MAP_STD_REAR_LEFT" />
      <AlternateTryLockedDoorClipId />
      <AlternateForcedEntryClipId />
      <AlternateJackFromOutSideClipId />
      <AlternateBeJackedFromOutSideClipId />
      <AlternateEntryPointClipSetId />
      <EnterVehicleMoveNetwork>ENTER_VEHICLE_STD</EnterVehicleMoveNetwork>
      <EntryTranslation x="-1.000000" y="-0.850000" z="0.500000" />
      <OpenDoorTranslation x="-0.050000" y="0.150000" />
      <OpenDoorHeadingChange value="1.570000" />
      <EntryHeadingChange value="-1.570000" />
      <ExtraZForMPPlaneWarp value="-2.000000" />
      <EntryPointFlags>JackIncludesGetIn UsesNoDoorTransitionForExit UseOpenDoorBlendAnims FixUpMoverToEntryPointOnExit UseGetUpAfterJack</EntryPointFlags>
      <EntryAnimVariations ref="ENTRY_ANIM_VARIATIONS_STANDARD_REAR_LEFT" />
      <NMJumpFromVehicleTuningSet />
    </Item>
    <Item type="CVehicleEntryPointAnimInfo">
      <Name>ENTRY_POINT_ANIM_STD_BALLER8_REAR_RIGHT</Name>
      <CommonClipSetMap ref="COMMON_CLIPSET_MAP_AUTOMOBILE_RIGHT" />
      <EntryClipSetMap ref="ENTRY_CLIPSET_MAP_STD_REAR_RIGHT" />
      <ExitClipSetMap ref="EXIT_CLIPSET_MAP_STD_REAR_RIGHT" />
      <AlternateTryLockedDoorClipId />
      <AlternateForcedEntryClipId />
      <AlternateJackFromOutSideClipId />
      <AlternateBeJackedFromOutSideClipId />
      <AlternateEntryPointClipSetId />
      <EnterVehicleMoveNetwork>ENTER_VEHICLE_STD</EnterVehicleMoveNetwork>
      <EntryTranslation x="1.000000" y="-0.850000" z="0.500000" />
      <OpenDoorTranslation x="0.050000" y="0.150000" />
      <OpenDoorHeadingChange value="-1.570000" />
      <EntryHeadingChange value="1.570000" />
      <ExtraZForMPPlaneWarp value="-2.000000" />
      <EntryPointFlags>JackIncludesGetIn UsesNoDoorTransitionForExit UseOpenDoorBlendAnims FixUpMoverToEntryPointOnExit UseGetUpAfterJack</EntryPointFlags>
      <EntryAnimVariations ref="ENTRY_ANIM_VARIATIONS_STANDARD_REAR_RIGHT" />
      <NMJumpFromVehicleTuningSet />
    </Item>
    <Item type="CVehicleEntryPointAnimInfo">
      <Name>ENTRY_POINT_ANIM_DORADO_FRONT_LEFT</Name>
      <CommonClipSetMap ref="COMMON_CLIPSET_MAP_AUTOMOBILE_LEFT" />
      <EntryClipSetMap ref="ENTRY_CLIPSET_MAP_GRANGER_FRONT_LEFT" />
      <ExitClipSetMap ref="EXIT_CLIPSET_MAP_GRANGER_FRONT_LEFT" />
      <AlternateTryLockedDoorClipId />
      <AlternateForcedEntryClipId />
      <AlternateJackFromOutSideClipId>van_perp_ds_a</AlternateJackFromOutSideClipId>
      <AlternateBeJackedFromOutSideClipId>van_victim_ds_a</AlternateBeJackedFromOutSideClipId>
      <AlternateEntryPointClipSetId />
      <EnterVehicleMoveNetwork>ENTER_VEHICLE_STD</EnterVehicleMoveNetwork>
      <EntryTranslation x="-1.000000" y="-0.450000" z="0.250000" />
      <OpenDoorTranslation x="0.000000" y="0.000000" />
      <OpenDoorHeadingChange value="1.570000" />
      <EntryHeadingChange value="-1.570000" />
      <ExtraZForMPPlaneWarp value="-2.000000" />
      <EntryPointFlags>JackIncludesGetIn UsesNoDoorTransitionForExit UseOpenDoorBlendAnims UseGetUpAfterJack JackVariationsIncludeGetIn DeadJackIncludesGetIn HasCombatEntry</EntryPointFlags>
      <EntryAnimVariations ref="ENTRY_ANIM_VARIATIONS_VAN_FRONT_LEFT" />
      <NMJumpFromVehicleTuningSet>SLOWTRUCK</NMJumpFromVehicleTuningSet>
    </Item>
    <Item type="CVehicleEntryPointAnimInfo">
      <Name>ENTRY_POINT_ANIM_DORADO_FRONT_RIGHT</Name>
      <CommonClipSetMap ref="COMMON_CLIPSET_MAP_AUTOMOBILE_RIGHT" />
      <EntryClipSetMap ref="ENTRY_CLIPSET_MAP_GRANGER_FRONT_RIGHT" />
      <ExitClipSetMap ref="EXIT_CLIPSET_MAP_GRANGER_FRONT_RIGHT" />
      <AlternateTryLockedDoorClipId />
      <AlternateForcedEntryClipId />
      <AlternateJackFromOutSideClipId>van_perp_ps_a</AlternateJackFromOutSideClipId>
      <AlternateBeJackedFromOutSideClipId>van_victim_ps_a</AlternateBeJackedFromOutSideClipId>
      <AlternateEntryPointClipSetId />
      <EnterVehicleMoveNetwork>ENTER_VEHICLE_STD</EnterVehicleMoveNetwork>
      <EntryTranslation x="1.000000" y="-0.250000" z="0.250000" />
      <OpenDoorTranslation x="0.000000" y="0.000000" />
      <OpenDoorHeadingChange value="-1.570000" />
      <EntryHeadingChange value="1.570000" />
      <ExtraZForMPPlaneWarp value="-2.000000" />
      <EntryPointFlags>JackIncludesGetIn UsesNoDoorTransitionForExit UseOpenDoorBlendAnims UseGetUpAfterJack JackVariationsIncludeGetIn DeadJackIncludesGetIn HasCombatEntry</EntryPointFlags>
      <EntryAnimVariations ref="ENTRY_ANIM_VARIATIONS_VAN_FRONT_RIGHT" />
      <NMJumpFromVehicleTuningSet />
    </Item>
    <Item type="CVehicleEntryPointAnimInfo">
      <Name>ENTRY_POINT_ANIM_FREIGHT2_LEFT</Name>
      <CommonClipSetMap ref="COMMON_CLIPSET_MAP_AUTOMOBILE_RIGHT" />
      <EntryClipSetMap ref="ENTRY_CLIPSET_MAP_FREIGHT2_LEFT" />
      <ExitClipSetMap ref="EXIT_CLIPSET_MAP_FREIGHT2_LEFT" />
      <AlternateTryLockedDoorClipId />
      <AlternateForcedEntryClipId />
      <AlternateJackFromOutSideClipId />
      <AlternateBeJackedFromOutSideClipId />
      <AlternateEntryPointClipSetId />
      <EnterVehicleMoveNetwork>ENTER_VEHICLE_STD</EnterVehicleMoveNetwork>
      <EntryTranslation x="3.641000" y="-2.300000" z="-1.638000" />
      <OpenDoorTranslation x="0.000000" y="0.000000" />
      <OpenDoorHeadingChange value="0.000000" />
      <EntryHeadingChange value="1.560000" />
      <ExtraZForMPPlaneWarp value="-2.000000" />
      <EntryPointFlags>HasClimbUp PreventJackInterrupt HasOnVehicleEntry HasClimbDown</EntryPointFlags>
      <EntryAnimVariations ref="NULL" />
      <NMJumpFromVehicleTuningSet />
    </Item>
    <Item type="CVehicleEntryPointAnimInfo">
      <Name>ENTRY_POINT_ANIM_FREIGHT2_RIGHT</Name>
      <CommonClipSetMap ref="COMMON_CLIPSET_MAP_AUTOMOBILE_RIGHT" />
      <EntryClipSetMap ref="ENTRY_CLIPSET_MAP_FREIGHT2_RIGHT" />
      <ExitClipSetMap ref="EXIT_CLIPSET_MAP_FREIGHT2_RIGHT" />
      <AlternateTryLockedDoorClipId />
      <AlternateForcedEntryClipId />
      <AlternateJackFromOutSideClipId />
      <AlternateBeJackedFromOutSideClipId />
      <AlternateEntryPointClipSetId />
      <EnterVehicleMoveNetwork>ENTER_VEHICLE_STD</EnterVehicleMoveNetwork>
      <EntryTranslation x="1.141000" y="-2.300000" z="-1.638000" />
      <OpenDoorTranslation x="0.000000" y="0.000000" />
      <OpenDoorHeadingChange value="0.000000" />
      <EntryHeadingChange value="1.560000" />
      <ExtraZForMPPlaneWarp value="-2.000000" />
      <EntryPointFlags>HasClimbUp PreventJackInterrupt HasOnVehicleEntry HasClimbDown</EntryPointFlags>
      <EntryAnimVariations ref="NULL" />
      <NMJumpFromVehicleTuningSet />
    </Item>
    <Item type="CVehicleEntryPointAnimInfo">
      <Name>ENTRY_POINT_ANIM_DOMINATOR9_FRONT_LEFT</Name>
      <CommonClipSetMap ref="COMMON_CLIPSET_MAP_AUTOMOBILE_LEFT" />
      <EntryClipSetMap ref="ENTRY_CLIPSET_MAP_STD_FRONT_LEFT" />
      <ExitClipSetMap ref="EXIT_CLIPSET_MAP_STD_FRONT_LEFT" />
      <AlternateTryLockedDoorClipId>STD_Locked_DS</AlternateTryLockedDoorClipId>
      <AlternateForcedEntryClipId>STD_Force_Entry_DS</AlternateForcedEntryClipId>
      <AlternateJackFromOutSideClipId>std_perp_ds</AlternateJackFromOutSideClipId>
      <AlternateBeJackedFromOutSideClipId>std_victim_ds</AlternateBeJackedFromOutSideClipId>
      <AlternateEntryPointClipSetId>clipset@veh@std@ds@female@enter_exit</AlternateEntryPointClipSetId>
      <EnterVehicleMoveNetwork>ENTER_VEHICLE_STD</EnterVehicleMoveNetwork>
      <EntryTranslation x="-0.760000" y="-0.551000" z="0.500000" />
      <OpenDoorTranslation x="-0.050000" y="0.150000" />
      <OpenDoorHeadingChange value="1.570000" />
      <EntryHeadingChange value="-1.570000" />
      <ExtraZForMPPlaneWarp value="-2.000000" />
      <EntryPointFlags>JackIncludesGetIn UsesNoDoorTransitionForExit UseOpenDoorBlendAnims UseGetUpAfterJack JackVariationsIncludeGetIn HasCombatEntry</EntryPointFlags>
      <EntryAnimVariations ref="ENTRY_ANIM_VARIATIONS_STANDARD_FRONT_LEFT" />
      <NMJumpFromVehicleTuningSet>STANDARD</NMJumpFromVehicleTuningSet>
    </Item>
    <Item type="CVehicleEntryPointAnimInfo">
      <Name>ENTRY_POINT_ANIM_STD_IMPALER5_FRONT_LEFT</Name>
      <CommonClipSetMap ref="COMMON_CLIPSET_MAP_AUTOMOBILE_LEFT" />
      <EntryClipSetMap ref="ENTRY_CLIPSET_MAP_STD_FRONT_LEFT" />
      <ExitClipSetMap ref="EXIT_CLIPSET_MAP_STD_FRONT_LEFT" />
      <AlternateTryLockedDoorClipId>STD_Locked_DS</AlternateTryLockedDoorClipId>
      <AlternateForcedEntryClipId>STD_Force_Entry_DS</AlternateForcedEntryClipId>
      <AlternateJackFromOutSideClipId>std_perp_ds</AlternateJackFromOutSideClipId>
      <AlternateBeJackedFromOutSideClipId>std_victim_ds</AlternateBeJackedFromOutSideClipId>
      <AlternateEntryPointClipSetId>clipset@veh@std@ds@female@enter_exit</AlternateEntryPointClipSetId>
      <EnterVehicleMoveNetwork>ENTER_VEHICLE_STD</EnterVehicleMoveNetwork>
      <EntryTranslation x="-0.940000" y="-0.620000" z="0.500000" />
      <OpenDoorTranslation x="-0.050000" y="0.150000" />
      <OpenDoorHeadingChange value="1.570000" />
      <EntryHeadingChange value="-1.570000" />
      <ExtraZForMPPlaneWarp value="-2.000000" />
      <EntryPointFlags>JackIncludesGetIn UsesNoDoorTransitionForExit UseOpenDoorBlendAnims FixUpMoverToEntryPointOnExit UseGetUpAfterJack JackVariationsIncludeGetIn HasCombatEntry</EntryPointFlags>
      <EntryAnimVariations ref="ENTRY_ANIM_VARIATIONS_STANDARD_FRONT_LEFT" />
      <NMJumpFromVehicleTuningSet>STANDARD</NMJumpFromVehicleTuningSet>
    </Item>
    <Item type="CVehicleEntryPointAnimInfo">
      <Name>ENTRY_POINT_ANIM_STD_IMPALER5_FRONT_RIGHT</Name>
      <CommonClipSetMap ref="COMMON_CLIPSET_MAP_AUTOMOBILE_RIGHT" />
      <EntryClipSetMap ref="ENTRY_CLIPSET_MAP_STD_FRONT_RIGHT" />
      <ExitClipSetMap ref="EXIT_CLIPSET_MAP_STD_FRONT_RIGHT" />
      <AlternateTryLockedDoorClipId>STD_Locked_PS</AlternateTryLockedDoorClipId>
      <AlternateForcedEntryClipId>STD_Force_Entry_PS</AlternateForcedEntryClipId>
      <AlternateJackFromOutSideClipId>std_perp_ps</AlternateJackFromOutSideClipId>
      <AlternateBeJackedFromOutSideClipId>std_victim_ps</AlternateBeJackedFromOutSideClipId>
      <AlternateEntryPointClipSetId>clipset@veh@std@ps@female@enter_exit</AlternateEntryPointClipSetId>
      <EnterVehicleMoveNetwork>ENTER_VEHICLE_STD</EnterVehicleMoveNetwork>
      <EntryTranslation x="0.950000" y="-0.620000" z="0.500000" />
      <OpenDoorTranslation x="0.050000" y="0.150000" />
      <OpenDoorHeadingChange value="-1.570000" />
      <EntryHeadingChange value="1.570000" />
      <ExtraZForMPPlaneWarp value="-2.000000" />
      <EntryPointFlags>JackIncludesGetIn UsesNoDoorTransitionForExit UseOpenDoorBlendAnims FixUpMoverToEntryPointOnExit UseGetUpAfterJack JackVariationsIncludeGetIn HasCombatEntry</EntryPointFlags>
      <EntryAnimVariations ref="ENTRY_ANIM_VARIATIONS_STANDARD_FRONT_RIGHT" />
      <NMJumpFromVehicleTuningSet />
    </Item>
    <Item type="CVehicleEntryPointAnimInfo">
      <Name>ENTRY_POINT_ANIM_STD_IMPALER5_REAR_LEFT</Name>
      <CommonClipSetMap ref="COMMON_CLIPSET_MAP_AUTOMOBILE_LEFT" />
      <EntryClipSetMap ref="ENTRY_CLIPSET_MAP_STD_REAR_LEFT" />
      <ExitClipSetMap ref="EXIT_CLIPSET_MAP_STD_REAR_LEFT" />
      <AlternateTryLockedDoorClipId />
      <AlternateForcedEntryClipId />
      <AlternateJackFromOutSideClipId />
      <AlternateBeJackedFromOutSideClipId />
      <AlternateEntryPointClipSetId />
      <EnterVehicleMoveNetwork>ENTER_VEHICLE_STD</EnterVehicleMoveNetwork>
      <EntryTranslation x="-0.940000" y="-0.620000" z="0.500000" />
      <OpenDoorTranslation x="-0.200000" y="0.000000" />
      <OpenDoorHeadingChange value="1.570000" />
      <EntryHeadingChange value="-1.570000" />
      <ExtraZForMPPlaneWarp value="-2.000000" />
      <EntryPointFlags>JackIncludesGetIn UsesNoDoorTransitionForExit UseOpenDoorBlendAnims UseGetUpAfterJack</EntryPointFlags>
      <EntryAnimVariations ref="ENTRY_ANIM_VARIATIONS_STANDARD_REAR_LEFT" />
      <NMJumpFromVehicleTuningSet />
    </Item>
    <Item type="CVehicleEntryPointAnimInfo">
      <Name>ENTRY_POINT_ANIM_STD_IMPALER5_REAR_RIGHT</Name>
      <CommonClipSetMap ref="COMMON_CLIPSET_MAP_AUTOMOBILE_RIGHT" />
      <EntryClipSetMap ref="ENTRY_CLIPSET_MAP_STD_REAR_RIGHT" />
      <ExitClipSetMap ref="EXIT_CLIPSET_MAP_STD_REAR_RIGHT" />
      <AlternateTryLockedDoorClipId />
      <AlternateForcedEntryClipId />
      <AlternateJackFromOutSideClipId />
      <AlternateBeJackedFromOutSideClipId />
      <AlternateEntryPointClipSetId />
      <EnterVehicleMoveNetwork>ENTER_VEHICLE_STD</EnterVehicleMoveNetwork>
      <EntryTranslation x="0.950000" y="-0.620000" z="0.500000" />
      <OpenDoorTranslation x="0.200000" y="0.000000" />
      <OpenDoorHeadingChange value="-1.570000" />
      <EntryHeadingChange value="1.570000" />
      <ExtraZForMPPlaneWarp value="-2.000000" />
      <EntryPointFlags>JackIncludesGetIn UsesNoDoorTransitionForExit UseOpenDoorBlendAnims UseGetUpAfterJack</EntryPointFlags>
      <EntryAnimVariations ref="ENTRY_ANIM_VARIATIONS_STANDARD_REAR_RIGHT" />
      <NMJumpFromVehicleTuningSet />
    </Item>
    <Item type="CVehicleEntryPointAnimInfo">
      <Name>ENTRY_POINT_ANIM_STD_POLGAUNTLET_FRONT_LEFT</Name>
      <CommonClipSetMap ref="COMMON_CLIPSET_MAP_AUTOMOBILE_LEFT" />
      <EntryClipSetMap ref="ENTRY_CLIPSET_MAP_STD_FRONT_LEFT" />
      <ExitClipSetMap ref="EXIT_CLIPSET_MAP_STD_FRONT_LEFT" />
      <AlternateTryLockedDoorClipId>STD_Locked_DS</AlternateTryLockedDoorClipId>
      <AlternateForcedEntryClipId>STD_Force_Entry_DS</AlternateForcedEntryClipId>
      <AlternateJackFromOutSideClipId>std_perp_ds</AlternateJackFromOutSideClipId>
      <AlternateBeJackedFromOutSideClipId>std_victim_ds</AlternateBeJackedFromOutSideClipId>
      <AlternateEntryPointClipSetId>clipset@veh@std@ds@female@enter_exit</AlternateEntryPointClipSetId>
      <EnterVehicleMoveNetwork>ENTER_VEHICLE_STD</EnterVehicleMoveNetwork>
      <EntryTranslation x="-0.842000" y="-0.551000" z="0.500000" />
      <OpenDoorTranslation x="-0.050000" y="0.150000" />
      <OpenDoorHeadingChange value="1.570000" />
      <EntryHeadingChange value="-1.570000" />
      <ExtraZForMPPlaneWarp value="-2.000000" />
      <EntryPointFlags>JackIncludesGetIn UsesNoDoorTransitionForExit UseOpenDoorBlendAnims FixUpMoverToEntryPointOnExit UseGetUpAfterJack JackVariationsIncludeGetIn HasCombatEntry</EntryPointFlags>
      <EntryAnimVariations ref="ENTRY_ANIM_VARIATIONS_STANDARD_FRONT_LEFT" />
      <NMJumpFromVehicleTuningSet>STANDARD</NMJumpFromVehicleTuningSet>
    </Item>
    <Item type="CVehicleEntryPointAnimInfo">
      <Name>ENTRY_POINT_ANIM_STD_POLGAUNTLET_FRONT_RIGHT</Name>
      <CommonClipSetMap ref="COMMON_CLIPSET_MAP_AUTOMOBILE_RIGHT" />
      <EntryClipSetMap ref="ENTRY_CLIPSET_MAP_STD_FRONT_RIGHT" />
      <ExitClipSetMap ref="EXIT_CLIPSET_MAP_STD_FRONT_RIGHT" />
      <AlternateTryLockedDoorClipId>STD_Locked_PS</AlternateTryLockedDoorClipId>
      <AlternateForcedEntryClipId>STD_Force_Entry_PS</AlternateForcedEntryClipId>
      <AlternateJackFromOutSideClipId>std_perp_ps</AlternateJackFromOutSideClipId>
      <AlternateBeJackedFromOutSideClipId>std_victim_ps</AlternateBeJackedFromOutSideClipId>
      <AlternateEntryPointClipSetId>clipset@veh@std@ps@female@enter_exit</AlternateEntryPointClipSetId>
      <EnterVehicleMoveNetwork>ENTER_VEHICLE_STD</EnterVehicleMoveNetwork>
      <EntryTranslation x="0.822600" y="-0.551700" z="0.500000" />
      <OpenDoorTranslation x="0.050000" y="0.150000" />
      <OpenDoorHeadingChange value="-1.570000" />
      <EntryHeadingChange value="1.570000" />
      <ExtraZForMPPlaneWarp value="-2.000000" />
      <EntryPointFlags>JackIncludesGetIn UsesNoDoorTransitionForExit UseOpenDoorBlendAnims FixUpMoverToEntryPointOnExit UseGetUpAfterJack JackVariationsIncludeGetIn HasCombatEntry</EntryPointFlags>
      <EntryAnimVariations ref="ENTRY_ANIM_VARIATIONS_STANDARD_FRONT_RIGHT" />
      <NMJumpFromVehicleTuningSet />
    </Item>
    <Item type="CVehicleEntryPointAnimInfo">
      <Name>ENTRY_POINT_ANIM_STD_TERMINUS_FRONT_LEFT</Name>
      <CommonClipSetMap ref="COMMON_CLIPSET_MAP_AUTOMOBILE_LEFT" />
      <EntryClipSetMap ref="ENTRY_CLIPSET_MAP_TERMINUS_FRONT_LEFT" />
      <ExitClipSetMap ref="EXIT_CLIPSET_MAP_TERMINUS_FRONT_LEFT" />
      <AlternateTryLockedDoorClipId />
      <AlternateForcedEntryClipId />
      <AlternateJackFromOutSideClipId>van_perp_ds_a</AlternateJackFromOutSideClipId>
      <AlternateBeJackedFromOutSideClipId>van_victim_ds_a</AlternateBeJackedFromOutSideClipId>
      <AlternateEntryPointClipSetId />
      <EnterVehicleMoveNetwork>ENTER_VEHICLE_STD</EnterVehicleMoveNetwork>
      <EntryTranslation x="-1.200000" y="-0.550000" z="0.000000" />
      <OpenDoorTranslation x="0.000000" y="0.000000" />
      <OpenDoorHeadingChange value="1.570000" />
      <EntryHeadingChange value="-1.570000" />
      <ExtraZForMPPlaneWarp value="-2.000000" />
      <EntryPointFlags>JackIncludesGetIn UsesNoDoorTransitionForExit UseOpenDoorBlendAnims UseGetUpAfterJack JackVariationsIncludeGetIn DeadJackIncludesGetIn HasCombatEntry</EntryPointFlags>
      <EntryAnimVariations ref="ENTRY_ANIM_VARIATIONS_VAN_FRONT_LEFT" />
      <NMJumpFromVehicleTuningSet>SLOWTRUCK</NMJumpFromVehicleTuningSet>
    </Item>
    <Item type="CVehicleEntryPointAnimInfo">
      <Name>ENTRY_POINT_ANIM_STD_TERMINUS_FRONT_RIGHT</Name>
      <CommonClipSetMap ref="COMMON_CLIPSET_MAP_AUTOMOBILE_RIGHT" />
      <EntryClipSetMap ref="ENTRY_CLIPSET_MAP_TERMINUS_FRONT_RIGHT" />
      <ExitClipSetMap ref="EXIT_CLIPSET_MAP_TERMINUS_FRONT_RIGHT" />
      <AlternateTryLockedDoorClipId />
      <AlternateForcedEntryClipId />
      <AlternateJackFromOutSideClipId>van_perp_ps_a</AlternateJackFromOutSideClipId>
      <AlternateBeJackedFromOutSideClipId>van_victim_ps_a</AlternateBeJackedFromOutSideClipId>
      <AlternateEntryPointClipSetId />
      <EnterVehicleMoveNetwork>ENTER_VEHICLE_STD</EnterVehicleMoveNetwork>
      <EntryTranslation x="1.200000" y="-0.500000" z="0.000000" />
      <OpenDoorTranslation x="0.000000" y="0.000000" />
      <OpenDoorHeadingChange value="-1.570000" />
      <EntryHeadingChange value="1.570000" />
      <ExtraZForMPPlaneWarp value="-2.000000" />
      <EntryPointFlags>JackIncludesGetIn UsesNoDoorTransitionForExit UseOpenDoorBlendAnims UseGetUpAfterJack JackVariationsIncludeGetIn DeadJackIncludesGetIn HasCombatEntry</EntryPointFlags>
      <EntryAnimVariations ref="ENTRY_ANIM_VARIATIONS_VAN_FRONT_RIGHT" />
      <NMJumpFromVehicleTuningSet>SLOWTRUCK</NMJumpFromVehicleTuningSet>
    </Item>
    <Item type="CVehicleEntryPointAnimInfo">
      <Name>ENTRY_POINT_ANIM_STD_TERMINUS_REAR_LEFT</Name>
      <CommonClipSetMap ref="COMMON_CLIPSET_MAP_AUTOMOBILE_LEFT" />
      <EntryClipSetMap ref="ENTRY_CLIPSET_MAP_STD_REAR_LEFT" />
      <ExitClipSetMap ref="EXIT_CLIPSET_MAP_STD_REAR_LEFT" />
      <AlternateTryLockedDoorClipId />
      <AlternateForcedEntryClipId />
      <AlternateJackFromOutSideClipId />
      <AlternateBeJackedFromOutSideClipId />
      <AlternateEntryPointClipSetId />
      <EnterVehicleMoveNetwork>ENTER_VEHICLE_STD</EnterVehicleMoveNetwork>
      <EntryTranslation x="-0.870000" y="-0.620000" z="0.000000" />
      <OpenDoorTranslation x="-0.200000" y="0.000000" />
      <OpenDoorHeadingChange value="1.570000" />
      <EntryHeadingChange value="-1.570000" />
      <ExtraZForMPPlaneWarp value="-2.000000" />
      <EntryPointFlags>JackIncludesGetIn UsesNoDoorTransitionForExit UseOpenDoorBlendAnims FixUpMoverToEntryPointOnExit UseGetUpAfterJack</EntryPointFlags>
      <EntryAnimVariations ref="ENTRY_ANIM_VARIATIONS_STANDARD_REAR_LEFT" />
      <NMJumpFromVehicleTuningSet />
    </Item>
    <Item type="CVehicleEntryPointAnimInfo">
      <Name>ENTRY_POINT_ANIM_STD_TERMINUS_REAR_RIGHT</Name>
      <CommonClipSetMap ref="COMMON_CLIPSET_MAP_AUTOMOBILE_RIGHT" />
      <EntryClipSetMap ref="ENTRY_CLIPSET_MAP_STD_REAR_RIGHT" />
      <ExitClipSetMap ref="EXIT_CLIPSET_MAP_STD_REAR_RIGHT" />
      <AlternateTryLockedDoorClipId />
      <AlternateForcedEntryClipId />
      <AlternateJackFromOutSideClipId />
      <AlternateBeJackedFromOutSideClipId />
      <AlternateEntryPointClipSetId />
      <EnterVehicleMoveNetwork>ENTER_VEHICLE_STD</EnterVehicleMoveNetwork>
      <EntryTranslation x="0.870000" y="-0.620000" z="0.000000" />
      <OpenDoorTranslation x="0.200000" y="0.000000" />
      <OpenDoorHeadingChange value="-1.570000" />
      <EntryHeadingChange value="1.570000" />
      <ExtraZForMPPlaneWarp value="-2.000000" />
      <EntryPointFlags>JackIncludesGetIn UsesNoDoorTransitionForExit UseOpenDoorBlendAnims FixUpMoverToEntryPointOnExit UseGetUpAfterJack</EntryPointFlags>
      <EntryAnimVariations ref="ENTRY_ANIM_VARIATIONS_STANDARD_REAR_RIGHT" />
      <NMJumpFromVehicleTuningSet />
    </Item>
    <Item type="CVehicleEntryPointAnimInfo">
      <Name>ENTRY_POINT_ANIM_VAN_VIVANITE_SIDEDOOR_REAR_LEFT</Name>
      <CommonClipSetMap ref="COMMON_CLIPSET_MAP_AUTOMOBILE_LEFT" />
      <EntryClipSetMap ref="ENTRY_CLIPSET_MAP_VAN_VIVANITE_SIDEDOOR_REAR_LEFT" />
      <ExitClipSetMap ref="EXIT_CLIPSET_MAP_VAN_VIVANITE_SIDEDOOR_REAR_LEFT" />
      <AlternateTryLockedDoorClipId />
      <AlternateForcedEntryClipId />
      <AlternateJackFromOutSideClipId />
      <AlternateBeJackedFromOutSideClipId />
      <AlternateEntryPointClipSetId />
      <EnterVehicleMoveNetwork>ENTER_VEHICLE_STD</EnterVehicleMoveNetwork>
      <EntryTranslation x="-1.479000" y="0.407000" z="0.200000" />
      <OpenDoorTranslation x="0.400000" y="-0.400000" />
      <OpenDoorHeadingChange value="0.000000" />
      <EntryHeadingChange value="-1.570000" />
      <ExtraZForMPPlaneWarp value="0.000000" />
      <EntryPointFlags>JackIncludesGetIn UseGetUpAfterJack JackVariationsIncludeGetIn DeadJackIncludesGetIn</EntryPointFlags>
      <EntryAnimVariations ref="NULL" />
      <NMJumpFromVehicleTuningSet />
    </Item>
    <Item type="CVehicleEntryPointAnimInfo">
      <Name>ENTRY_POINT_ANIM_VAN_VIVANITE_SIDEDOOR_REAR_RIGHT</Name>
      <CommonClipSetMap ref="COMMON_CLIPSET_MAP_AUTOMOBILE_RIGHT" />
      <EntryClipSetMap ref="ENTRY_CLIPSET_MAP_VAN_VIVANITE_SIDEDOOR_REAR_RIGHT" />
      <ExitClipSetMap ref="EXIT_CLIPSET_MAP_VAN_VIVANITE_SIDEDOOR_REAR_RIGHT" />
      <AlternateTryLockedDoorClipId />
      <AlternateForcedEntryClipId />
      <AlternateJackFromOutSideClipId />
      <AlternateBeJackedFromOutSideClipId />
      <AlternateEntryPointClipSetId />
      <EnterVehicleMoveNetwork>ENTER_VEHICLE_STD</EnterVehicleMoveNetwork>
      <EntryTranslation x="1.479000" y="0.407000" z="0.200000" />
      <OpenDoorTranslation x="-0.400000" y="-0.400000" />
      <OpenDoorHeadingChange value="0.000000" />
      <EntryHeadingChange value="1.570000" />
      <ExtraZForMPPlaneWarp value="0.000000" />
      <EntryPointFlags>JackIncludesGetIn UseGetUpAfterJack JackVariationsIncludeGetIn DeadJackIncludesGetIn</EntryPointFlags>
      <EntryAnimVariations ref="NULL" />
      <NMJumpFromVehicleTuningSet />
    </Item>
  </VehicleEntryPointAnimInfos>
  <VehicleExplosionInfos />
  <VehicleLayoutInfos>
    <Item type="CVehicleLayoutInfo">
      <Name>LAYOUT_STD_ALEUTIAN</Name>
      <Seats>
        <Item>
          <SeatInfo ref="SEAT_STANDARD_FRONT_LEFT" />
          <SeatAnimInfo ref="SEAT_ANIM_RANGER_FRONT_LEFT" />
        </Item>
        <Item>
          <SeatInfo ref="SEAT_STANDARD_FRONT_RIGHT" />
          <SeatAnimInfo ref="SEAT_ANIM_RANGER_FRONT_RIGHT" />
        </Item>
        <Item>
          <SeatInfo ref="SEAT_STANDARD_REAR_LEFT" />
          <SeatAnimInfo ref="SEAT_ANIM_RANGER_REAR_LEFT" />
        </Item>
        <Item>
          <SeatInfo ref="SEAT_STANDARD_REAR_RIGHT" />
          <SeatAnimInfo ref="SEAT_ANIM_RANGER_REAR_RIGHT" />
        </Item>
      </Seats>
      <EntryPoints>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_STANDARD_FRONT_LEFT" />
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_ALEUTIAN_FRONT_LEFT" />
        </Item>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_STANDARD_FRONT_RIGHT" />
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_ALEUTIAN_FRONT_RIGHT" />
        </Item>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_STANDARD_REAR_LEFT" />
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_ALEUTIAN_REAR_LEFT" />
        </Item>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_STANDARD_REAR_RIGHT" />
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_ALEUTIAN_REAR_RIGHT" />
        </Item>
      </EntryPoints>
      <LayoutFlags>StreamAnims UseVanOpenDoorBlendParams UseDoorOscillation UseLeanSteerAnims UseLowerDoorBlockTest</LayoutFlags>
      <BicycleInfo ref="NULL" />
      <AnimRateSet ref="NULL" />
      <HandsUpClipSetId>busted_vehicle_std</HandsUpClipSetId>
      <SteeringWheelOffset x="0.000000" y="0.350000" z="0.320000" />
      <MaxXAcceleration value="4.000000" />
      <BodyLeanXApproachSpeed value="5.000000" />
      <BodyLeanXSmallDelta value="0.300000" />
      <LookBackApproachSpeedScale value="1.000000" />
      <FirstPersonAdditiveIdleClipSets>
        <Item>clipset@veh@van@ds@idle_a</Item>
        <Item>clipset@veh@van@ds@idle_b</Item>
        <Item>clipset@veh@van@ds@idle_c</Item>
        <Item>clipset@veh@van@ds@idle_d</Item>
        <Item>clipset@veh@van@ds@idle_e</Item>
      </FirstPersonAdditiveIdleClipSets>
      <FirstPersonRoadRageClipSets>
        <Item>clipset@veh@van@ds@hit_wheel@idle_a</Item>
        <Item>clipset@veh@van@ds@hit_wheel@idle_b</Item>
        <Item>clipset@veh@van@ds@hit_wheel@idle_c</Item>
      </FirstPersonRoadRageClipSets>
      <CellphoneClipsets>
        <CellphoneClipsetDS>ANIM_GROUP_CELLPHONE_IN_CAR_DS</CellphoneClipsetDS>
        <CellphoneClipsetPS>ANIM_GROUP_CELLPHONE_IN_CAR_PS</CellphoneClipsetPS>
        <CellphoneClipsetDS_FPS>ANIM_GROUP_CELLPHONE_IN_CAR_DS_FPS</CellphoneClipsetDS_FPS>
        <CellphoneClipsetPS_FPS>ANIM_GROUP_CELLPHONE_IN_CAR_DS_FPS</CellphoneClipsetPS_FPS>
      </CellphoneClipsets>
    </Item>
    <Item type="CVehicleLayoutInfo">
      <Name>LAYOUT_STD_ASTEROPE2</Name>
      <Seats>
        <Item>
          <SeatInfo ref="SEAT_STANDARD_FRONT_LEFT" />
          <SeatAnimInfo ref="SEAT_ANIM_STANDARD_FRONT_LEFT" />
        </Item>
        <Item>
          <SeatInfo ref="SEAT_STANDARD_FRONT_RIGHT" />
          <SeatAnimInfo ref="SEAT_ANIM_STD_ASTEROPE2_FRONT_RIGHT" />
        </Item>
        <Item>
          <SeatInfo ref="SEAT_STANDARD_REAR_LEFT" />
          <SeatAnimInfo ref="SEAT_ANIM_STANDARD_REAR_LEFT" />
        </Item>
        <Item>
          <SeatInfo ref="SEAT_STANDARD_REAR_RIGHT" />
          <SeatAnimInfo ref="SEAT_ANIM_STANDARD_REAR_RIGHT" />
        </Item>
      </Seats>
      <EntryPoints>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_STANDARD_FRONT_LEFT" />
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_STANDARD_FRONT_LEFT" />
        </Item>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_STANDARD_FRONT_RIGHT" />
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_STANDARD_FRONT_RIGHT" />
        </Item>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_STANDARD_REAR_LEFT" />
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_STANDARD_REAR_LEFT" />
        </Item>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_STANDARD_REAR_RIGHT" />
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_STANDARD_REAR_RIGHT" />
        </Item>
      </EntryPoints>
      <LayoutFlags>StreamAnims UseDoorOscillation UseLeanSteerAnims UseSteeringWheelIk</LayoutFlags>
      <BicycleInfo ref="NULL" />
      <AnimRateSet ref="STD_LOW_ANIM_RATE_SET" />
      <HandsUpClipSetId>busted_vehicle_std</HandsUpClipSetId>
      <SteeringWheelOffset x="0.000000" y="0.350000" z="0.320000" />
      <MaxXAcceleration value="4.000000" />
      <BodyLeanXApproachSpeed value="5.000000" />
      <BodyLeanXSmallDelta value="0.300000" />
      <LookBackApproachSpeedScale value="1.000000" />
      <FirstPersonAdditiveIdleClipSets>
        <Item>clipset@veh@std@ds@idle_a</Item>
        <Item>clipset@veh@std@ds@idle_b</Item>
        <Item>clipset@veh@std@ds@idle_c</Item>
        <Item>clipset@veh@std@ds@idle_d</Item>
        <Item>clipset@veh@std@ds@idle_e</Item>
      </FirstPersonAdditiveIdleClipSets>
      <FirstPersonRoadRageClipSets>
        <Item>clipset@veh@std@ds@hit_wheel@idle_a</Item>
        <Item>clipset@veh@std@ds@hit_wheel@idle_b</Item>
        <Item>clipset@veh@std@ds@hit_wheel@idle_c</Item>
      </FirstPersonRoadRageClipSets>
      <CellphoneClipsets>
        <CellphoneClipsetDS>ANIM_GROUP_CELLPHONE_IN_CAR_DS</CellphoneClipsetDS>
        <CellphoneClipsetPS>ANIM_GROUP_CELLPHONE_IN_CAR_PS</CellphoneClipsetPS>
        <CellphoneClipsetDS_FPS>ANIM_GROUP_CELLPHONE_IN_CAR_DS_FPS</CellphoneClipsetDS_FPS>
        <CellphoneClipsetPS_FPS>ANIM_GROUP_CELLPHONE_IN_CAR_DS_FPS</CellphoneClipsetPS_FPS>
      </CellphoneClipsets>
    </Item>
    <Item type="CVehicleLayoutInfo">
      <Name>LAYOUT_STD_BALLER8</Name>
      <Seats>
        <Item>
          <SeatInfo ref="SEAT_STANDARD_FRONT_LEFT" />
          <SeatAnimInfo ref="SEAT_ANIM_STD_HIGHWINDOW_FRONT_LEFT" />
        </Item>
        <Item>
          <SeatInfo ref="SEAT_STANDARD_FRONT_RIGHT" />
          <SeatAnimInfo ref="SEAT_ANIM_STD_HIGHWINDOW_FRONT_RIGHT" />
        </Item>
        <Item>
          <SeatInfo ref="SEAT_STANDARD_REAR_LEFT" />
          <SeatAnimInfo ref="SEAT_ANIM_STD_HIGHWINDOW_REAR_LEFT" />
        </Item>
        <Item>
          <SeatInfo ref="SEAT_STANDARD_REAR_RIGHT" />
          <SeatAnimInfo ref="SEAT_ANIM_STD_HIGHWINDOW_REAR_RIGHT" />
        </Item>
      </Seats>
      <EntryPoints>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_STANDARD_FRONT_LEFT" />
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_STD_BOBCAT_FRONT_LEFT" />
        </Item>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_STANDARD_FRONT_RIGHT" />
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_STD_BOBCAT_FRONT_RIGHT" />
        </Item>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_STANDARD_REAR_LEFT" />
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_STD_BALLER8_REAR_LEFT" />
        </Item>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_STANDARD_REAR_RIGHT" />
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_STD_BALLER8_REAR_RIGHT" />
        </Item>
      </EntryPoints>
      <LayoutFlags>StreamAnims UseDoorOscillation UseLeanSteerAnims UseSteeringWheelIk</LayoutFlags>
      <BicycleInfo ref="NULL" />
      <AnimRateSet ref="STD_LOW_ANIM_RATE_SET" />
      <HandsUpClipSetId>busted_vehicle_std</HandsUpClipSetId>
      <SteeringWheelOffset x="0.000000" y="0.350000" z="0.320000" />
      <MaxXAcceleration value="4.000000" />
      <BodyLeanXApproachSpeed value="5.000000" />
      <BodyLeanXSmallDelta value="0.300000" />
      <LookBackApproachSpeedScale value="1.000000" />
      <FirstPersonAdditiveIdleClipSets>
        <Item>clipset@veh@std@ds@idle_a</Item>
        <Item>clipset@veh@std@ds@idle_b</Item>
        <Item>clipset@veh@std@ds@idle_c</Item>
        <Item>clipset@veh@std@ds@idle_d</Item>
        <Item>clipset@veh@std@ds@idle_e</Item>
      </FirstPersonAdditiveIdleClipSets>
      <FirstPersonRoadRageClipSets>
        <Item>clipset@veh@std@ds@hit_wheel@idle_a</Item>
        <Item>clipset@veh@std@ds@hit_wheel@idle_b</Item>
        <Item>clipset@veh@std@ds@hit_wheel@idle_c</Item>
      </FirstPersonRoadRageClipSets>
      <CellphoneClipsets>
        <CellphoneClipsetDS>ANIM_GROUP_CELLPHONE_IN_CAR_DS</CellphoneClipsetDS>
        <CellphoneClipsetPS>ANIM_GROUP_CELLPHONE_IN_CAR_PS</CellphoneClipsetPS>
        <CellphoneClipsetDS_FPS>ANIM_GROUP_CELLPHONE_IN_CAR_DS_FPS</CellphoneClipsetDS_FPS>
        <CellphoneClipsetPS_FPS>ANIM_GROUP_CELLPHONE_IN_CAR_DS_FPS</CellphoneClipsetPS_FPS>
      </CellphoneClipsets>
    </Item>
    <Item type="CVehicleLayoutInfo">
      <Name>LAYOUT_STD_DOMINATOR9</Name>
      <Seats>
        <Item>
          <SeatInfo ref="SEAT_STANDARD_FRONT_LEFT" />
          <SeatAnimInfo ref="SEAT_ANIM_STANDARD_FRONT_LEFT" />
        </Item>
        <Item>
          <SeatInfo ref="SEAT_STANDARD_FRONT_RIGHT" />
          <SeatAnimInfo ref="SEAT_ANIM_STANDARD_FRONT_RIGHT" />
        </Item>
        <Item>
          <SeatInfo ref="SEAT_STANDARD_REAR_LEFT" />
          <SeatAnimInfo ref="SEAT_ANIM_STANDARD_REAR_LEFT" />
        </Item>
        <Item>
          <SeatInfo ref="SEAT_STANDARD_REAR_RIGHT" />
          <SeatAnimInfo ref="SEAT_ANIM_STANDARD_REAR_RIGHT" />
        </Item>
      </Seats>
      <EntryPoints>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_STANDARD_FRONT_LEFT" />
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_DOMINATOR9_FRONT_LEFT" />
        </Item>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_STANDARD_FRONT_RIGHT" />
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_STANDARD_FRONT_RIGHT" />
        </Item>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_STANDARD_REAR_LEFT" />
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_STANDARD_REAR_LEFT" />
        </Item>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_STANDARD_REAR_RIGHT" />
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_STANDARD_REAR_RIGHT" />
        </Item>
      </EntryPoints>
      <LayoutFlags>StreamAnims UseDoorOscillation UseLeanSteerAnims UseSteeringWheelIk</LayoutFlags>
      <BicycleInfo ref="NULL" />
      <AnimRateSet ref="STD_LOW_ANIM_RATE_SET" />
      <HandsUpClipSetId>busted_vehicle_std</HandsUpClipSetId>
      <SteeringWheelOffset x="0.000000" y="0.350000" z="0.320000" />
      <MaxXAcceleration value="4.000000" />
      <BodyLeanXApproachSpeed value="5.000000" />
      <BodyLeanXSmallDelta value="0.300000" />
      <LookBackApproachSpeedScale value="1.000000" />
      <FirstPersonAdditiveIdleClipSets>
        <Item>clipset@veh@std@ds@idle_a</Item>
        <Item>clipset@veh@std@ds@idle_b</Item>
        <Item>clipset@veh@std@ds@idle_c</Item>
        <Item>clipset@veh@std@ds@idle_d</Item>
        <Item>clipset@veh@std@ds@idle_e</Item>
      </FirstPersonAdditiveIdleClipSets>
      <FirstPersonRoadRageClipSets>
        <Item>clipset@veh@std@ds@hit_wheel@idle_a</Item>
        <Item>clipset@veh@std@ds@hit_wheel@idle_b</Item>
        <Item>clipset@veh@std@ds@hit_wheel@idle_c</Item>
      </FirstPersonRoadRageClipSets>
      <CellphoneClipsets>
        <CellphoneClipsetDS>ANIM_GROUP_CELLPHONE_IN_CAR_DS</CellphoneClipsetDS>
        <CellphoneClipsetPS>ANIM_GROUP_CELLPHONE_IN_CAR_PS</CellphoneClipsetPS>
        <CellphoneClipsetDS_FPS>ANIM_GROUP_CELLPHONE_IN_CAR_DS_FPS</CellphoneClipsetDS_FPS>
        <CellphoneClipsetPS_FPS>ANIM_GROUP_CELLPHONE_IN_CAR_DS_FPS</CellphoneClipsetPS_FPS>
      </CellphoneClipsets>
    </Item>
    <Item type="CVehicleLayoutInfo">
      <Name>LAYOUT_STD_DORADO</Name>
      <Seats>
        <Item>
          <SeatInfo ref="SEAT_STANDARD_FRONT_LEFT" />
          <SeatAnimInfo ref="SEAT_ANIM_RANGER_FRONT_LEFT" />
        </Item>
        <Item>
          <SeatInfo ref="SEAT_STANDARD_FRONT_RIGHT" />
          <SeatAnimInfo ref="SEAT_ANIM_RANGER_FRONT_RIGHT" />
        </Item>
        <Item>
          <SeatInfo ref="SEAT_STANDARD_REAR_LEFT" />
          <SeatAnimInfo ref="SEAT_ANIM_STD_HIGHWINDOW_REAR_LEFT" />
        </Item>
        <Item>
          <SeatInfo ref="SEAT_STANDARD_REAR_RIGHT" />
          <SeatAnimInfo ref="SEAT_ANIM_STD_HIGHWINDOW_REAR_RIGHT" />
        </Item>
      </Seats>
      <EntryPoints>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_STANDARD_FRONT_LEFT" />
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_DORADO_FRONT_LEFT" />
        </Item>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_STANDARD_FRONT_RIGHT" />
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_DORADO_FRONT_RIGHT" />
        </Item>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_STANDARD_REAR_LEFT" />
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_STD_EXITFIXUP_REAR_LEFT" />
        </Item>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_STANDARD_REAR_RIGHT" />
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_STD_EXITFIXUP_REAR_RIGHT" />
        </Item>
      </EntryPoints>
      <LayoutFlags>StreamAnims UseVanOpenDoorBlendParams UseDoorOscillation UseLeanSteerAnims UseLowerDoorBlockTest</LayoutFlags>
      <BicycleInfo ref="NULL" />
      <AnimRateSet ref="NULL" />
      <HandsUpClipSetId>busted_vehicle_std</HandsUpClipSetId>
      <SteeringWheelOffset x="0.000000" y="0.350000" z="0.320000" />
      <MaxXAcceleration value="4.000000" />
      <BodyLeanXApproachSpeed value="5.000000" />
      <BodyLeanXSmallDelta value="0.300000" />
      <LookBackApproachSpeedScale value="1.000000" />
      <FirstPersonAdditiveIdleClipSets>
        <Item>clipset@veh@van@ds@idle_a</Item>
        <Item>clipset@veh@van@ds@idle_b</Item>
        <Item>clipset@veh@van@ds@idle_c</Item>
        <Item>clipset@veh@van@ds@idle_d</Item>
        <Item>clipset@veh@van@ds@idle_e</Item>
      </FirstPersonAdditiveIdleClipSets>
      <FirstPersonRoadRageClipSets>
        <Item>clipset@veh@van@ds@hit_wheel@idle_a</Item>
        <Item>clipset@veh@van@ds@hit_wheel@idle_b</Item>
        <Item>clipset@veh@van@ds@hit_wheel@idle_c</Item>
      </FirstPersonRoadRageClipSets>
      <CellphoneClipsets>
        <CellphoneClipsetDS>ANIM_GROUP_CELLPHONE_IN_CAR_DS</CellphoneClipsetDS>
        <CellphoneClipsetPS>ANIM_GROUP_CELLPHONE_IN_CAR_PS</CellphoneClipsetPS>
        <CellphoneClipsetDS_FPS>ANIM_GROUP_CELLPHONE_IN_CAR_DS_FPS</CellphoneClipsetDS_FPS>
        <CellphoneClipsetPS_FPS>ANIM_GROUP_CELLPHONE_IN_CAR_DS_FPS</CellphoneClipsetPS_FPS>
      </CellphoneClipsets>
    </Item>
    <Item type="CVehicleLayoutInfo">
      <Name>LAYOUT_TRAIN_FREIGHT2</Name>
      <Seats>
        <Item>
          <SeatInfo ref="SEAT_FREIGHT2_FRONT_LEFT" />
          <SeatAnimInfo ref="SEAT_ANIM_FREIGHT2_FRONT_LEFT" />
        </Item>
        <Item>
          <SeatInfo ref="SEAT_FREIGHT2_FRONT_RIGHT" />
          <SeatAnimInfo ref="SEAT_ANIM_FREIGHT2_FRONT_RIGHT" />
        </Item>
      </Seats>
      <EntryPoints>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_FREIGHT2_FRONT_LEFT" />
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_FREIGHT2_LEFT" />
        </Item>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_FREIGHT2_FRONT_RIGHT" />
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_FREIGHT2_RIGHT" />
        </Item>
      </EntryPoints>
      <LayoutFlags>StreamAnims DisableJackingAndBusting UseFinerAlignTolerance</LayoutFlags>
      <BicycleInfo ref="NULL" />
      <AnimRateSet ref="NULL" />
      <HandsUpClipSetId>busted_vehicle_std</HandsUpClipSetId>
      <SteeringWheelOffset x="0.000000" y="0.350000" z="0.320000" />
      <MaxXAcceleration value="25.000000" />
      <BodyLeanXApproachSpeed value="15.000000" />
      <BodyLeanXSmallDelta value="0.100000" />
      <LookBackApproachSpeedScale value="1.000000" />
      <FirstPersonAdditiveIdleClipSets />
      <FirstPersonRoadRageClipSets />
      <CellphoneClipsets>
        <CellphoneClipsetDS>ANIM_GROUP_CELLPHONE_IN_CAR_DS</CellphoneClipsetDS>
        <CellphoneClipsetPS>ANIM_GROUP_CELLPHONE_IN_CAR_PS</CellphoneClipsetPS>
        <CellphoneClipsetDS_FPS>ANIM_GROUP_CELLPHONE_IN_CAR_DS_FPS</CellphoneClipsetDS_FPS>
        <CellphoneClipsetPS_FPS>ANIM_GROUP_CELLPHONE_IN_CAR_DS_FPS</CellphoneClipsetPS_FPS>
      </CellphoneClipsets>
    </Item>
    <Item type="CVehicleLayoutInfo">
      <Name>LAYOUT_STD_IMPALER5</Name>
      <Seats>
        <Item>
          <SeatInfo ref="SEAT_STANDARD_FRONT_LEFT" />
          <SeatAnimInfo ref="SEAT_ANIM_STANDARD_FRONT_LEFT" />
        </Item>
        <Item>
          <SeatInfo ref="SEAT_STANDARD_FRONT_RIGHT" />
          <SeatAnimInfo ref="SEAT_ANIM_STANDARD_FRONT_RIGHT" />
        </Item>
        <Item>
          <SeatInfo ref="SEAT_STANDARD_REAR_LEFT" />
          <SeatAnimInfo ref="SEAT_ANIM_STANDARD_REAR_LEFT" />
        </Item>
        <Item>
          <SeatInfo ref="SEAT_STANDARD_REAR_RIGHT" />
          <SeatAnimInfo ref="SEAT_ANIM_STANDARD_REAR_RIGHT" />
        </Item>
      </Seats>
      <EntryPoints>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_STANDARD_FRONT_LEFT" />
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_STD_IMPALER5_FRONT_LEFT" />
        </Item>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_STANDARD_FRONT_RIGHT" />
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_STD_IMPALER5_FRONT_RIGHT" />
        </Item>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_STANDARD_REAR_LEFT" />
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_STD_IMPALER5_REAR_LEFT" />
        </Item>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_STANDARD_REAR_RIGHT" />
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_STD_IMPALER5_REAR_RIGHT" />
        </Item>
      </EntryPoints>
      <LayoutFlags>StreamAnims UseDoorOscillation UseLeanSteerAnims UseSteeringWheelIk</LayoutFlags>
      <BicycleInfo ref="NULL" />
      <AnimRateSet ref="STD_LOW_ANIM_RATE_SET" />
      <HandsUpClipSetId>busted_vehicle_std</HandsUpClipSetId>
      <SteeringWheelOffset x="0.000000" y="0.350000" z="0.320000" />
      <MaxXAcceleration value="4.000000" />
      <BodyLeanXApproachSpeed value="5.000000" />
      <BodyLeanXSmallDelta value="0.300000" />
      <LookBackApproachSpeedScale value="1.000000" />
      <FirstPersonAdditiveIdleClipSets>
        <Item>clipset@veh@std@ds@idle_a</Item>
        <Item>clipset@veh@std@ds@idle_b</Item>
        <Item>clipset@veh@std@ds@idle_c</Item>
        <Item>clipset@veh@std@ds@idle_d</Item>
        <Item>clipset@veh@std@ds@idle_e</Item>
      </FirstPersonAdditiveIdleClipSets>
      <FirstPersonRoadRageClipSets>
        <Item>clipset@veh@std@ds@hit_wheel@idle_a</Item>
        <Item>clipset@veh@std@ds@hit_wheel@idle_b</Item>
        <Item>clipset@veh@std@ds@hit_wheel@idle_c</Item>
      </FirstPersonRoadRageClipSets>
      <CellphoneClipsets>
        <CellphoneClipsetDS>ANIM_GROUP_CELLPHONE_IN_CAR_DS</CellphoneClipsetDS>
        <CellphoneClipsetPS>ANIM_GROUP_CELLPHONE_IN_CAR_PS</CellphoneClipsetPS>
        <CellphoneClipsetDS_FPS>ANIM_GROUP_CELLPHONE_IN_CAR_DS_FPS</CellphoneClipsetDS_FPS>
        <CellphoneClipsetPS_FPS>ANIM_GROUP_CELLPHONE_IN_CAR_DS_FPS</CellphoneClipsetPS_FPS>
      </CellphoneClipsets>
    </Item>
    <Item type="CVehicleLayoutInfo">
      <Name>LAYOUT_STD_POLGAUNTLET</Name>
      <Seats>
        <Item>
          <SeatInfo ref="SEAT_STANDARD_FRONT_LEFT" />
          <SeatAnimInfo ref="SEAT_ANIM_STANDARD_FRONT_LEFT" />
        </Item>
        <Item>
          <SeatInfo ref="SEAT_STANDARD_FRONT_RIGHT" />
          <SeatAnimInfo ref="SEAT_ANIM_STANDARD_FRONT_RIGHT" />
        </Item>
      </Seats>
      <EntryPoints>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_STANDARD_FRONT_LEFT" />
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_STD_POLGAUNTLET_FRONT_LEFT" />
        </Item>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_STANDARD_FRONT_RIGHT" />
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_STD_POLGAUNTLET_FRONT_RIGHT" />
        </Item>
      </EntryPoints>
      <LayoutFlags>StreamAnims UseDoorOscillation UseLeanSteerAnims UseSteeringWheelIk</LayoutFlags>
      <BicycleInfo ref="NULL" />
      <AnimRateSet ref="STD_LOW_ANIM_RATE_SET" />
      <HandsUpClipSetId>busted_vehicle_std</HandsUpClipSetId>
      <SteeringWheelOffset x="0.000000" y="0.350000" z="0.320000" />
      <MaxXAcceleration value="4.000000" />
      <BodyLeanXApproachSpeed value="5.000000" />
      <BodyLeanXSmallDelta value="0.300000" />
      <LookBackApproachSpeedScale value="1.000000" />
      <FirstPersonAdditiveIdleClipSets>
        <Item>clipset@veh@std@ds@idle_a</Item>
        <Item>clipset@veh@std@ds@idle_b</Item>
        <Item>clipset@veh@std@ds@idle_c</Item>
        <Item>clipset@veh@std@ds@idle_d</Item>
        <Item>clipset@veh@std@ds@idle_e</Item>
      </FirstPersonAdditiveIdleClipSets>
      <FirstPersonRoadRageClipSets>
        <Item>clipset@veh@std@ds@hit_wheel@idle_a</Item>
        <Item>clipset@veh@std@ds@hit_wheel@idle_b</Item>
        <Item>clipset@veh@std@ds@hit_wheel@idle_c</Item>
      </FirstPersonRoadRageClipSets>
      <CellphoneClipsets>
        <CellphoneClipsetDS>ANIM_GROUP_CELLPHONE_IN_CAR_DS</CellphoneClipsetDS>
        <CellphoneClipsetPS>ANIM_GROUP_CELLPHONE_IN_CAR_PS</CellphoneClipsetPS>
        <CellphoneClipsetDS_FPS>ANIM_GROUP_CELLPHONE_IN_CAR_DS_FPS</CellphoneClipsetDS_FPS>
        <CellphoneClipsetPS_FPS>ANIM_GROUP_CELLPHONE_IN_CAR_DS_FPS</CellphoneClipsetPS_FPS>
      </CellphoneClipsets>
    </Item>
    <Item type="CVehicleLayoutInfo">
      <Name>LAYOUT_STD_TERMINUS</Name>
      <Seats>
        <Item>
          <SeatInfo ref="SEAT_STANDARD_FRONT_LEFT" />
          <SeatAnimInfo ref="SEAT_ANIM_STANDARD_FRONT_LEFT" />
        </Item>
        <Item>
          <SeatInfo ref="SEAT_STANDARD_FRONT_RIGHT" />
          <SeatAnimInfo ref="SEAT_ANIM_STD_TERMINUS_FRONT_RIGHT" />
        </Item>
        <Item>
          <SeatInfo ref="SEAT_STANDARD_REAR_LEFT" />
          <SeatAnimInfo ref="SEAT_ANIM_STANDARD_REAR_LEFT" />
        </Item>
        <Item>
          <SeatInfo ref="SEAT_STANDARD_REAR_RIGHT" />
          <SeatAnimInfo ref="SEAT_ANIM_STANDARD_REAR_RIGHT" />
        </Item>
      </Seats>
      <EntryPoints>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_STANDARD_FRONT_LEFT" />
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_STD_TERMINUS_FRONT_LEFT" />
        </Item>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_STANDARD_FRONT_RIGHT" />
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_STD_TERMINUS_FRONT_RIGHT" />
        </Item>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_STANDARD_REAR_LEFT" />
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_STD_TERMINUS_REAR_LEFT" />
        </Item>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_STANDARD_REAR_RIGHT" />
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_STD_TERMINUS_REAR_RIGHT" />
        </Item>
      </EntryPoints>
      <LayoutFlags>StreamAnims UseDoorOscillation UseLeanSteerAnims UseSteeringWheelIk</LayoutFlags>
      <BicycleInfo ref="NULL" />
      <AnimRateSet ref="STD_LOW_ANIM_RATE_SET" />
      <HandsUpClipSetId>busted_vehicle_std</HandsUpClipSetId>
      <SteeringWheelOffset x="0.000000" y="0.350000" z="0.320000" />
      <MaxXAcceleration value="4.000000" />
      <BodyLeanXApproachSpeed value="5.000000" />
      <BodyLeanXSmallDelta value="0.300000" />
      <LookBackApproachSpeedScale value="1.000000" />
      <FirstPersonAdditiveIdleClipSets>
        <Item>clipset@veh@std@ds@idle_a</Item>
        <Item>clipset@veh@std@ds@idle_b</Item>
        <Item>clipset@veh@std@ds@idle_c</Item>
        <Item>clipset@veh@std@ds@idle_d</Item>
        <Item>clipset@veh@std@ds@idle_e</Item>
      </FirstPersonAdditiveIdleClipSets>
      <FirstPersonRoadRageClipSets>
        <Item>clipset@veh@std@ds@hit_wheel@idle_a</Item>
        <Item>clipset@veh@std@ds@hit_wheel@idle_b</Item>
        <Item>clipset@veh@std@ds@hit_wheel@idle_c</Item>
      </FirstPersonRoadRageClipSets>
      <CellphoneClipsets>
        <CellphoneClipsetDS>ANIM_GROUP_CELLPHONE_IN_CAR_DS</CellphoneClipsetDS>
        <CellphoneClipsetPS>ANIM_GROUP_CELLPHONE_IN_CAR_PS</CellphoneClipsetPS>
        <CellphoneClipsetDS_FPS>ANIM_GROUP_CELLPHONE_IN_CAR_DS_FPS</CellphoneClipsetDS_FPS>
        <CellphoneClipsetPS_FPS>ANIM_GROUP_CELLPHONE_IN_CAR_DS_FPS</CellphoneClipsetPS_FPS>
      </CellphoneClipsets>
    </Item>
    <Item type="CVehicleLayoutInfo">
      <Name>LAYOUT_VAN_VIVANITE</Name>
      <Seats>
        <Item>
          <SeatInfo ref="SEAT_VAN_FRONT_LEFT" />
          <SeatAnimInfo ref="SEAT_ANIM_VAN_VIVANITE_FRONT_LEFT" />
        </Item>
        <Item>
          <SeatInfo ref="SEAT_VAN_FRONT_RIGHT" />
          <SeatAnimInfo ref="SEAT_ANIM_VAN_VIVANITE_FRONT_RIGHT" />
        </Item>
        <Item>
          <SeatInfo ref="SEAT_VAN_REAR_LEFT" />
          <SeatAnimInfo ref="SEAT_ANIM_VAN_VIVANITE_SIDEDOOR_REAR_LEFT" />
        </Item>
        <Item>
          <SeatInfo ref="SEAT_VAN_REAR_RIGHT" />
          <SeatAnimInfo ref="SEAT_ANIM_VAN_VIVANITE_SIDEDOOR_REAR_RIGHT" />
        </Item>
      </Seats>
      <EntryPoints>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_VAN_FRONT_LEFT" />
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_VAN_FRONT_LEFT" />
        </Item>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_VAN_FRONT_RIGHT" />
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_VAN_FRONT_RIGHT" />
        </Item>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_VAN_REAR_LEFT" />
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_VAN_VIVANITE_SIDEDOOR_REAR_LEFT" />
        </Item>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_VAN_REAR_RIGHT" />
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_VAN_VIVANITE_SIDEDOOR_REAR_RIGHT" />
        </Item>
      </EntryPoints>
      <LayoutFlags>StreamAnims UseVanOpenDoorBlendParams UseDoorOscillation UseLeanSteerAnims UseLowerDoorBlockTest</LayoutFlags>
      <BicycleInfo ref="NULL" />
      <AnimRateSet ref="NULL" />
      <HandsUpClipSetId>busted_vehicle_van</HandsUpClipSetId>
      <SteeringWheelOffset x="0.006700" y="0.330000" z="0.270000" />
      <MaxXAcceleration value="4.000000" />
      <BodyLeanXApproachSpeed value="5.000000" />
      <BodyLeanXSmallDelta value="0.300000" />
      <LookBackApproachSpeedScale value="1.000000" />
      <FirstPersonAdditiveIdleClipSets>
        <Item>clipset@veh@van@ds@idle_a</Item>
        <Item>clipset@veh@van@ds@idle_b</Item>
        <Item>clipset@veh@van@ds@idle_c</Item>
        <Item>clipset@veh@van@ds@idle_d</Item>
        <Item>clipset@veh@van@ds@idle_e</Item>
      </FirstPersonAdditiveIdleClipSets>
      <FirstPersonRoadRageClipSets>
        <Item>clipset@veh@van@ds@hit_wheel@idle_a</Item>
        <Item>clipset@veh@van@ds@hit_wheel@idle_b</Item>
        <Item>clipset@veh@van@ds@hit_wheel@idle_c</Item>
      </FirstPersonRoadRageClipSets>
      <CellphoneClipsets>
        <CellphoneClipsetDS>ANIM_GROUP_CELLPHONE_IN_CAR_DS</CellphoneClipsetDS>
        <CellphoneClipsetPS>ANIM_GROUP_CELLPHONE_IN_CAR_PS</CellphoneClipsetPS>
        <CellphoneClipsetDS_FPS>ANIM_GROUP_CELLPHONE_IN_CAR_DS_FPS</CellphoneClipsetDS_FPS>
        <CellphoneClipsetPS_FPS>ANIM_GROUP_CELLPHONE_IN_CAR_DS_FPS</CellphoneClipsetPS_FPS>
      </CellphoneClipsets>
    </Item>
  </VehicleLayoutInfos>
  <VehicleScenarioLayoutInfos />
  <SeatOverrideAnimInfos />
  <InVehicleOverrideInfos />
  <FirstPersonDriveByLookAroundData>
    <Item type="CFirstPersonDriveByLookAroundData">
      <Name>STD_ALEUTIAN_FRONT_LEFT</Name>
      <AllowLookback value="true" />
      <HeadingLimits x="-183.000000" y="173.000000" />
      <DataLeft>
        <Offsets>
          <Item>
            <Offset value="-0.480000" />
            <AngleToBlendInOffset x="20.000000" y="120.000000" />
          </Item>
          <Item>
            <Offset value="0.050000" />
            <AngleToBlendInOffset x="20.000000" y="120.000000" />
          </Item>
          <Item>
            <Offset value="0.030000" />
            <AngleToBlendInOffset x="5.000000" y="50.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-10.000000" y="2.000000" />
        <AngleToBlendInExtraPitch x="5.000000" y="110.000000" />
      </DataLeft>
      <DataRight>
        <Offsets>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="0.000000" y="0.000000" />
          </Item>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="0.000000" y="0.000000" />
          </Item>
          <Item>
            <Offset value="0.050000" />
            <AngleToBlendInOffset x="5.000000" y="50.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-3.000000" y="-13.000000" />
        <AngleToBlendInExtraPitch x="0.000000" y="70.000000" />
      </DataRight>
      <WheelClipInfo>
        <HeadingLimitsLeft x="0.380000" y="0.430000" />
        <HeadingLimitsRight x="0.480000" y="0.530000" />
        <PitchLimits x="0.470000" y="0.530000" />
        <PitchOffset x="0.000000" y="0.080000" />
        <WheelAngleLimits x="-0.700000" y="0.000000" />
        <WheelAngleOffset x="0.010000" y="0.020000" />
        <MaxWheelOffsetY value="0.170000" />
        <WheelClipLerpInRate value="0.150000" />
        <WheelClipLerpOutRate value="0.180000" />
      </WheelClipInfo>
    </Item>
    <Item type="CFirstPersonDriveByLookAroundData">
      <Name>STD_ALEUTIAN_FRONT_RIGHT</Name>
      <AllowLookback value="true" />
      <HeadingLimits x="-183.000000" y="165.000000" />
      <DataLeft>
        <Offsets>
          <Item>
            <Offset value="0.100000" />
            <AngleToBlendInOffset x="20.000000" y="120.000000" />
          </Item>
          <Item>
            <Offset value="0.050000" />
            <AngleToBlendInOffset x="60.000000" y="120.000000" />
          </Item>
          <Item>
            <Offset value="-0.035000" />
            <AngleToBlendInOffset x="5.000000" y="50.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-1.000000" y="-11.000000" />
        <AngleToBlendInExtraPitch x="5.000000" y="110.000000" />
      </DataLeft>
      <DataRight>
        <Offsets>
          <Item>
            <Offset value="0.470000" />
            <AngleToBlendInOffset x="40.000000" y="160.000000" />
          </Item>
          <Item>
            <Offset value="0.100000" />
            <AngleToBlendInOffset x="40.000000" y="160.000000" />
          </Item>
          <Item>
            <Offset value="-0.050000" />
            <AngleToBlendInOffset x="10.000000" y="50.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-5.000000" y="3.000000" />
        <AngleToBlendInExtraPitch x="0.000000" y="70.000000" />
      </DataRight>
      <WheelClipInfo>
        <HeadingLimitsLeft x="0.380000" y="0.430000" />
        <HeadingLimitsRight x="0.480000" y="0.530000" />
        <PitchLimits x="0.470000" y="0.530000" />
        <PitchOffset x="0.000000" y="0.050000" />
        <WheelAngleLimits x="-0.700000" y="0.000000" />
        <WheelAngleOffset x="0.010000" y="0.040000" />
        <MaxWheelOffsetY value="0.170000" />
        <WheelClipLerpInRate value="0.150000" />
        <WheelClipLerpOutRate value="0.180000" />
      </WheelClipInfo>
    </Item>
    <Item type="CFirstPersonDriveByLookAroundData">
      <Name>STD_ALEUTIAN_REAR_LEFT</Name>
      <AllowLookback value="true" />
      <HeadingLimits x="-185.000000" y="160.000000" />
      <DataLeft>
        <Offsets>
          <Item>
            <Offset value="-0.475000" />
            <AngleToBlendInOffset x="40.000000" y="120.000000" />
          </Item>
          <Item>
            <Offset value="0.200000" />
            <AngleToBlendInOffset x="0.000000" y="120.000000" />
          </Item>
          <Item>
            <Offset value="-0.000000" />
            <AngleToBlendInOffset x="0.000000" y="0.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-10.000000" y="10.000000" />
        <AngleToBlendInExtraPitch x="5.000000" y="70.000000" />
      </DataLeft>
      <DataRight>
        <Offsets>
          <Item>
            <Offset value="-0.200000" />
            <AngleToBlendInOffset x="0.000000" y="120.000000" />
          </Item>
          <Item>
            <Offset value="-0.070000" />
            <AngleToBlendInOffset x="20.000000" y="120.000000" />
          </Item>
          <Item>
            <Offset value="0.125000" />
            <AngleToBlendInOffset x="20.000000" y="90.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-8.500000" y="-12.000000" />
        <AngleToBlendInExtraPitch x="0.000000" y="70.000000" />
      </DataRight>
      <WheelClipInfo>
        <HeadingLimitsLeft x="0.380000" y="0.430000" />
        <HeadingLimitsRight x="0.480000" y="0.530000" />
        <PitchLimits x="0.470000" y="0.530000" />
        <PitchOffset x="0.000000" y="0.050000" />
        <WheelAngleLimits x="-0.700000" y="0.000000" />
        <WheelAngleOffset x="0.010000" y="0.040000" />
        <MaxWheelOffsetY value="0.170000" />
        <WheelClipLerpInRate value="0.150000" />
        <WheelClipLerpOutRate value="0.180000" />
      </WheelClipInfo>
    </Item>
    <Item type="CFirstPersonDriveByLookAroundData">
      <Name>STD_ALEUTIAN_REAR_RIGHT</Name>
      <AllowLookback value="true" />
      <HeadingLimits x="-185.000000" y="160.000000" />
      <DataLeft>
        <Offsets>
          <Item>
            <Offset value="0.200000" />
            <AngleToBlendInOffset x="0.000000" y="120.000000" />
          </Item>
          <Item>
            <Offset value="-0.070000" />
            <AngleToBlendInOffset x="20.000000" y="120.000000" />
          </Item>
          <Item>
            <Offset value="0.085000" />
            <AngleToBlendInOffset x="20.000000" y="90.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-7.000000" y="-10.000000" />
        <AngleToBlendInExtraPitch x="0.000000" y="70.000000" />
      </DataLeft>
      <DataRight>
        <Offsets>
          <Item>
            <Offset value="0.475000" />
            <AngleToBlendInOffset x="40.000000" y="120.000000" />
          </Item>
          <Item>
            <Offset value="0.100000" />
            <AngleToBlendInOffset x="40.000000" y="120.000000" />
          </Item>
          <Item>
            <Offset value="-0.000000" />
            <AngleToBlendInOffset x="0.000000" y="0.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-10.000000" y="5.000000" />
        <AngleToBlendInExtraPitch x="5.000000" y="70.000000" />
      </DataRight>
      <WheelClipInfo>
        <HeadingLimitsLeft x="0.380000" y="0.430000" />
        <HeadingLimitsRight x="0.480000" y="0.530000" />
        <PitchLimits x="0.470000" y="0.530000" />
        <PitchOffset x="0.000000" y="0.050000" />
        <WheelAngleLimits x="-0.700000" y="0.000000" />
        <WheelAngleOffset x="0.010000" y="0.040000" />
        <MaxWheelOffsetY value="0.170000" />
        <WheelClipLerpInRate value="0.150000" />
        <WheelClipLerpOutRate value="0.180000" />
      </WheelClipInfo>
    </Item>
    <Item type="CFirstPersonDriveByLookAroundData">
      <Name>STD_ASTEROPE2_FRONT_RIGHT</Name>
      <AllowLookback value="true" />
      <HeadingLimits x="-190.000000" y="160.000000" />
      <DataLeft>
        <Offsets>
          <Item>
            <Offset value="-0.050000" />
            <AngleToBlendInOffset x="45.000000" y="90.000000" />
          </Item>
          <Item>
            <Offset value="-0.025000" />
            <AngleToBlendInOffset x="0.000000" y="190.000000" />
          </Item>
          <Item>
            <Offset value="0.050000" />
            <AngleToBlendInOffset x="0.000000" y="90.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="0.000000" y="-10.000000" />
        <AngleToBlendInExtraPitch x="0.000000" y="190.000000" />
      </DataLeft>
      <DataRight>
        <Offsets>
          <Item>
            <Offset value="0.400000" />
            <AngleToBlendInOffset x="40.000000" y="160.000000" />
          </Item>
          <Item>
            <Offset value="0.175000" />
            <AngleToBlendInOffset x="60.000000" y="160.000000" />
          </Item>
          <Item>
            <Offset value="0.060000" />
            <AngleToBlendInOffset x="60.000000" y="120.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-15.000000" y="20.000000" />
        <AngleToBlendInExtraPitch x="50.000000" y="110.000000" />
      </DataRight>
      <WheelClipInfo>
        <HeadingLimitsLeft x="0.380000" y="0.430000" />
        <HeadingLimitsRight x="0.480000" y="0.530000" />
        <PitchLimits x="0.470000" y="0.530000" />
        <PitchOffset x="0.000000" y="0.050000" />
        <WheelAngleLimits x="-0.700000" y="0.000000" />
        <WheelAngleOffset x="0.010000" y="0.040000" />
        <MaxWheelOffsetY value="0.170000" />
        <WheelClipLerpInRate value="0.150000" />
        <WheelClipLerpOutRate value="0.180000" />
      </WheelClipInfo>
    </Item>
    <Item type="CFirstPersonDriveByLookAroundData">
      <Name>STD_ASTEROPE2_REAR_LEFT</Name>
      <AllowLookback value="true" />
      <HeadingLimits x="-190.000000" y="160.000000" />
      <DataLeft>
        <Offsets>
          <Item>
            <Offset value="-0.350000" />
            <AngleToBlendInOffset x="0.000000" y="160.000000" />
          </Item>
          <Item>
            <Offset value="0.230000" />
            <AngleToBlendInOffset x="0.000000" y="160.000000" />
          </Item>
          <Item>
            <Offset value="-0.050000" />
            <AngleToBlendInOffset x="0.000000" y="140.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-10.000000" y="10.000000" />
        <AngleToBlendInExtraPitch x="20.000000" y="100.000000" />
      </DataLeft>
      <DataRight>
        <Offsets>
          <Item>
            <Offset value="-0.100000" />
            <AngleToBlendInOffset x="0.000000" y="90.000000" />
          </Item>
          <Item>
            <Offset value="0.050000" />
            <AngleToBlendInOffset x="0.000000" y="90.000000" />
          </Item>
          <Item>
            <Offset value="0.060000" />
            <AngleToBlendInOffset x="0.000000" y="140.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-2.500000" y="-4.000000" />
        <AngleToBlendInExtraPitch x="0.000000" y="90.000000" />
      </DataRight>
      <WheelClipInfo>
        <HeadingLimitsLeft x="0.380000" y="0.430000" />
        <HeadingLimitsRight x="0.480000" y="0.530000" />
        <PitchLimits x="0.470000" y="0.530000" />
        <PitchOffset x="0.000000" y="0.050000" />
        <WheelAngleLimits x="-0.700000" y="0.000000" />
        <WheelAngleOffset x="0.010000" y="0.040000" />
        <MaxWheelOffsetY value="0.170000" />
        <WheelClipLerpInRate value="0.150000" />
        <WheelClipLerpOutRate value="0.180000" />
      </WheelClipInfo>
    </Item>
    <Item type="CFirstPersonDriveByLookAroundData">
      <Name>STD_ASTEROPE2_REAR_RIGHT</Name>
      <AllowLookback value="true" />
      <HeadingLimits x="-190.000000" y="160.000000" />
      <DataLeft>
        <Offsets>
          <Item>
            <Offset value="0.150000" />
            <AngleToBlendInOffset x="0.000000" y="90.000000" />
          </Item>
          <Item>
            <Offset value="0.025000" />
            <AngleToBlendInOffset x="0.000000" y="90.000000" />
          </Item>
          <Item>
            <Offset value="0.060000" />
            <AngleToBlendInOffset x="0.000000" y="140.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-3.000000" y="-4.000000" />
        <AngleToBlendInExtraPitch x="0.000000" y="90.000000" />
      </DataLeft>
      <DataRight>
        <Offsets>
          <Item>
            <Offset value="0.400000" />
            <AngleToBlendInOffset x="0.000000" y="160.000000" />
          </Item>
          <Item>
            <Offset value="0.230000" />
            <AngleToBlendInOffset x="0.000000" y="160.000000" />
          </Item>
          <Item>
            <Offset value="-0.050000" />
            <AngleToBlendInOffset x="0.000000" y="140.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-10.000000" y="10.000000" />
        <AngleToBlendInExtraPitch x="20.000000" y="100.000000" />
      </DataRight>
      <WheelClipInfo>
        <HeadingLimitsLeft x="0.380000" y="0.430000" />
        <HeadingLimitsRight x="0.480000" y="0.530000" />
        <PitchLimits x="0.470000" y="0.530000" />
        <PitchOffset x="0.000000" y="0.050000" />
        <WheelAngleLimits x="-0.700000" y="0.000000" />
        <WheelAngleOffset x="0.010000" y="0.040000" />
        <MaxWheelOffsetY value="0.170000" />
        <WheelClipLerpInRate value="0.150000" />
        <WheelClipLerpOutRate value="0.180000" />
      </WheelClipInfo>
    </Item>
    <Item type="CFirstPersonDriveByLookAroundData">
      <Name>STD_BALLER8_REAR_LEFT</Name>
      <AllowLookback value="true" />
      <HeadingLimits x="-190.000000" y="170.000000" />
      <DataLeft>
        <Offsets>
          <Item>
            <Offset value="-0.450000" />
            <AngleToBlendInOffset x="35.000000" y="120.000000" />
          </Item>
          <Item>
            <Offset value="0.050000" />
            <AngleToBlendInOffset x="0.000000" y="160.000000" />
          </Item>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="0.000000" y="120.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-15.000000" y="10.000000" />
        <AngleToBlendInExtraPitch x="0.000000" y="80.000000" />
      </DataLeft>
      <DataRight>
        <Offsets>
          <Item>
            <Offset value="-0.150000" />
            <AngleToBlendInOffset x="80.000000" y="120.000000" />
          </Item>
          <Item>
            <Offset value="-0.075000" />
            <AngleToBlendInOffset x="0.000000" y="90.000000" />
          </Item>
          <Item>
            <Offset value="0.050000" />
            <AngleToBlendInOffset x="0.000000" y="190.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-4.500000" y="-10.000000" />
        <AngleToBlendInExtraPitch x="0.000000" y="190.000000" />
      </DataRight>
      <WheelClipInfo>
        <HeadingLimitsLeft x="0.380000" y="0.430000" />
        <HeadingLimitsRight x="0.480000" y="0.530000" />
        <PitchLimits x="0.470000" y="0.530000" />
        <PitchOffset x="0.000000" y="0.050000" />
        <WheelAngleLimits x="-0.700000" y="0.000000" />
        <WheelAngleOffset x="0.010000" y="0.040000" />
        <MaxWheelOffsetY value="0.170000" />
        <WheelClipLerpInRate value="0.150000" />
        <WheelClipLerpOutRate value="0.180000" />
      </WheelClipInfo>
    </Item>
    <Item type="CFirstPersonDriveByLookAroundData">
      <Name>STD_BALLER8_REAR_RIGHT</Name>
      <AllowLookback value="true" />
      <HeadingLimits x="-190.000000" y="170.000000" />
      <DataLeft>
        <Offsets>
          <Item>
            <Offset value="0.150000" />
            <AngleToBlendInOffset x="80.000000" y="120.000000" />
          </Item>
          <Item>
            <Offset value="-0.075000" />
            <AngleToBlendInOffset x="0.000000" y="90.000000" />
          </Item>
          <Item>
            <Offset value="0.050000" />
            <AngleToBlendInOffset x="0.000000" y="190.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-4.900000" y="-10.300000" />
        <AngleToBlendInExtraPitch x="0.000000" y="190.000000" />
      </DataLeft>
      <DataRight>
        <Offsets>
          <Item>
            <Offset value="0.450000" />
            <AngleToBlendInOffset x="35.000000" y="120.000000" />
          </Item>
          <Item>
            <Offset value="0.050000" />
            <AngleToBlendInOffset x="0.000000" y="160.000000" />
          </Item>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="0.000000" y="120.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-15.000000" y="10.000000" />
        <AngleToBlendInExtraPitch x="0.000000" y="80.000000" />
      </DataRight>
      <WheelClipInfo>
        <HeadingLimitsLeft x="0.380000" y="0.430000" />
        <HeadingLimitsRight x="0.480000" y="0.530000" />
        <PitchLimits x="0.470000" y="0.530000" />
        <PitchOffset x="0.000000" y="0.050000" />
        <WheelAngleLimits x="-0.700000" y="0.000000" />
        <WheelAngleOffset x="0.010000" y="0.040000" />
        <MaxWheelOffsetY value="0.170000" />
        <WheelClipLerpInRate value="0.150000" />
        <WheelClipLerpOutRate value="0.180000" />
      </WheelClipInfo>
    </Item>
    <Item type="CFirstPersonDriveByLookAroundData">
      <Name>VAN_BOXVILLE6_REAR_LEFT</Name>
      <AllowLookback value="true" />
      <HeadingLimits x="-105.000000" y="-29.000000" />
      <DataLeft>
        <Offsets>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="0.000000" y="0.000000" />
          </Item>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="0.000000" y="0.000000" />
          </Item>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="0.000000" y="0.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-17.000000" y="20.000000" />
        <AngleToBlendInExtraPitch x="0.000000" y="0.000000" />
      </DataLeft>
      <DataRight>
        <Offsets>
          <Item>
            <Offset value="-0.150000" />
            <AngleToBlendInOffset x="0.000000" y="50.000000" />
          </Item>
          <Item>
            <Offset value="0.150000" />
            <AngleToBlendInOffset x="50.000000" y="95.000000" />
          </Item>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="0.000000" y="0.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-17.000000" y="20.000000" />
        <AngleToBlendInExtraPitch x="0.000000" y="0.000000" />
      </DataRight>
      <WheelClipInfo>
        <HeadingLimitsLeft x="0.380000" y="0.430000" />
        <HeadingLimitsRight x="0.480000" y="0.530000" />
        <PitchLimits x="0.470000" y="0.530000" />
        <PitchOffset x="0.000000" y="0.050000" />
        <WheelAngleLimits x="-0.700000" y="0.000000" />
        <WheelAngleOffset x="0.010000" y="0.040000" />
        <MaxWheelOffsetY value="0.170000" />
        <WheelClipLerpInRate value="0.150000" />
        <WheelClipLerpOutRate value="0.180000" />
      </WheelClipInfo>
    </Item>
    <Item type="CFirstPersonDriveByLookAroundData">
      <Name>VAN_BOXVILLE6_REAR_RIGHT</Name>
      <AllowLookback value="true" />
      <HeadingLimits x="-95.000000" y="-32.000000" />
      <DataLeft>
        <Offsets>
          <Item>
            <Offset value="0.030000" />
            <AngleToBlendInOffset x="0.000000" y="50.000000" />
          </Item>
          <Item>
            <Offset value="0.050000" />
            <AngleToBlendInOffset x="0.000000" y="50.000000" />
          </Item>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="0.000000" y="0.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-17.000000" y="20.000000" />
        <AngleToBlendInExtraPitch x="0.000000" y="0.000000" />
      </DataLeft>
      <DataRight>
        <Offsets>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="0.000000" y="0.000000" />
          </Item>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="0.000000" y="0.000000" />
          </Item>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="0.000000" y="0.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-17.000000" y="20.000000" />
        <AngleToBlendInExtraPitch x="0.000000" y="0.000000" />
      </DataRight>
      <WheelClipInfo>
        <HeadingLimitsLeft x="0.380000" y="0.430000" />
        <HeadingLimitsRight x="0.480000" y="0.530000" />
        <PitchLimits x="0.470000" y="0.530000" />
        <PitchOffset x="0.000000" y="0.050000" />
        <WheelAngleLimits x="-0.700000" y="0.000000" />
        <WheelAngleOffset x="0.010000" y="0.040000" />
        <MaxWheelOffsetY value="0.170000" />
        <WheelClipLerpInRate value="0.150000" />
        <WheelClipLerpOutRate value="0.180000" />
      </WheelClipInfo>
    </Item>
    <Item type="CFirstPersonDriveByLookAroundData">
      <Name>CAVALCADE3_FRONT_LEFT</Name>
      <AllowLookback value="true" />
      <HeadingLimits x="-183.000000" y="143.000000" />
      <DataLeft>
        <Offsets>
          <Item>
            <Offset value="-0.380000" />
            <AngleToBlendInOffset x="20.000000" y="120.000000" />
          </Item>
          <Item>
            <Offset value="-0.050000" />
            <AngleToBlendInOffset x="20.000000" y="120.000000" />
          </Item>
          <Item>
            <Offset value="0.030000" />
            <AngleToBlendInOffset x="5.000000" y="50.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-10.000000" y="2.000000" />
        <AngleToBlendInExtraPitch x="5.000000" y="110.000000" />
      </DataLeft>
      <DataRight>
        <Offsets>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="0.000000" y="0.000000" />
          </Item>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="0.000000" y="0.000000" />
          </Item>
          <Item>
            <Offset value="0.050000" />
            <AngleToBlendInOffset x="5.000000" y="50.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="0.000000" y="-14.300000" />
        <AngleToBlendInExtraPitch x="0.000000" y="70.000000" />
      </DataRight>
      <WheelClipInfo>
        <HeadingLimitsLeft x="0.380000" y="0.430000" />
        <HeadingLimitsRight x="0.480000" y="0.530000" />
        <PitchLimits x="0.470000" y="0.530000" />
        <PitchOffset x="0.000000" y="0.080000" />
        <WheelAngleLimits x="-0.700000" y="0.000000" />
        <WheelAngleOffset x="0.010000" y="0.020000" />
        <MaxWheelOffsetY value="0.170000" />
        <WheelClipLerpInRate value="0.150000" />
        <WheelClipLerpOutRate value="0.180000" />
      </WheelClipInfo>
    </Item>
    <Item type="CFirstPersonDriveByLookAroundData">
      <Name>CAVALCADE3_FRONT_RIGHT</Name>
      <AllowLookback value="true" />
      <HeadingLimits x="-183.000000" y="138.000000" />
      <DataLeft>
        <Offsets>
          <Item>
            <Offset value="0.100000" />
            <AngleToBlendInOffset x="20.000000" y="120.000000" />
          </Item>
          <Item>
            <Offset value="0.050000" />
            <AngleToBlendInOffset x="60.000000" y="120.000000" />
          </Item>
          <Item>
            <Offset value="-0.035000" />
            <AngleToBlendInOffset x="5.000000" y="50.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="0.000000" y="-14.500000" />
        <AngleToBlendInExtraPitch x="5.000000" y="110.000000" />
      </DataLeft>
      <DataRight>
        <Offsets>
          <Item>
            <Offset value="0.330000" />
            <AngleToBlendInOffset x="40.000000" y="160.000000" />
          </Item>
          <Item>
            <Offset value="0.100000" />
            <AngleToBlendInOffset x="40.000000" y="160.000000" />
          </Item>
          <Item>
            <Offset value="-0.050000" />
            <AngleToBlendInOffset x="10.000000" y="50.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-5.000000" y="3.000000" />
        <AngleToBlendInExtraPitch x="0.000000" y="70.000000" />
      </DataRight>
      <WheelClipInfo>
        <HeadingLimitsLeft x="0.380000" y="0.430000" />
        <HeadingLimitsRight x="0.480000" y="0.530000" />
        <PitchLimits x="0.470000" y="0.530000" />
        <PitchOffset x="0.000000" y="0.050000" />
        <WheelAngleLimits x="-0.700000" y="0.000000" />
        <WheelAngleOffset x="0.010000" y="0.040000" />
        <MaxWheelOffsetY value="0.170000" />
        <WheelClipLerpInRate value="0.150000" />
        <WheelClipLerpOutRate value="0.180000" />
      </WheelClipInfo>
    </Item>
    <Item type="CFirstPersonDriveByLookAroundData">
      <Name>CAVALCADE3_REAR_LEFT</Name>
      <AllowLookback value="true" />
      <HeadingLimits x="-185.000000" y="145.000000" />
      <DataLeft>
        <Offsets>
          <Item>
            <Offset value="-0.265000" />
            <AngleToBlendInOffset x="40.000000" y="120.000000" />
          </Item>
          <Item>
            <Offset value="0.265000" />
            <AngleToBlendInOffset x="0.000000" y="120.000000" />
          </Item>
          <Item>
            <Offset value="-0.000000" />
            <AngleToBlendInOffset x="0.000000" y="0.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-10.000000" y="10.000000" />
        <AngleToBlendInExtraPitch x="5.000000" y="70.000000" />
      </DataLeft>
      <DataRight>
        <Offsets>
          <Item>
            <Offset value="-0.200000" />
            <AngleToBlendInOffset x="0.000000" y="120.000000" />
          </Item>
          <Item>
            <Offset value="-0.070000" />
            <AngleToBlendInOffset x="20.000000" y="120.000000" />
          </Item>
          <Item>
            <Offset value="0.125000" />
            <AngleToBlendInOffset x="20.000000" y="90.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-4.000000" y="-17.000000" />
        <AngleToBlendInExtraPitch x="0.000000" y="70.000000" />
      </DataRight>
      <WheelClipInfo>
        <HeadingLimitsLeft x="0.380000" y="0.430000" />
        <HeadingLimitsRight x="0.480000" y="0.530000" />
        <PitchLimits x="0.470000" y="0.530000" />
        <PitchOffset x="0.000000" y="0.050000" />
        <WheelAngleLimits x="-0.700000" y="0.000000" />
        <WheelAngleOffset x="0.010000" y="0.040000" />
        <MaxWheelOffsetY value="0.170000" />
        <WheelClipLerpInRate value="0.150000" />
        <WheelClipLerpOutRate value="0.180000" />
      </WheelClipInfo>
    </Item>
    <Item type="CFirstPersonDriveByLookAroundData">
      <Name>CAVALCADE3_REAR_RIGHT</Name>
      <AllowLookback value="true" />
      <HeadingLimits x="-185.000000" y="140.000000" />
      <DataLeft>
        <Offsets>
          <Item>
            <Offset value="0.200000" />
            <AngleToBlendInOffset x="0.000000" y="120.000000" />
          </Item>
          <Item>
            <Offset value="-0.070000" />
            <AngleToBlendInOffset x="20.000000" y="120.000000" />
          </Item>
          <Item>
            <Offset value="0.085000" />
            <AngleToBlendInOffset x="20.000000" y="90.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-3.000000" y="-15.500000" />
        <AngleToBlendInExtraPitch x="0.000000" y="70.000000" />
      </DataLeft>
      <DataRight>
        <Offsets>
          <Item>
            <Offset value="0.275000" />
            <AngleToBlendInOffset x="40.000000" y="120.000000" />
          </Item>
          <Item>
            <Offset value="0.300000" />
            <AngleToBlendInOffset x="40.000000" y="120.000000" />
          </Item>
          <Item>
            <Offset value="-0.000000" />
            <AngleToBlendInOffset x="0.000000" y="0.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-10.000000" y="5.000000" />
        <AngleToBlendInExtraPitch x="5.000000" y="70.000000" />
      </DataRight>
      <WheelClipInfo>
        <HeadingLimitsLeft x="0.380000" y="0.430000" />
        <HeadingLimitsRight x="0.480000" y="0.530000" />
        <PitchLimits x="0.470000" y="0.530000" />
        <PitchOffset x="0.000000" y="0.050000" />
        <WheelAngleLimits x="-0.700000" y="0.000000" />
        <WheelAngleOffset x="0.010000" y="0.040000" />
        <MaxWheelOffsetY value="0.170000" />
        <WheelClipLerpInRate value="0.150000" />
        <WheelClipLerpOutRate value="0.180000" />
      </WheelClipInfo>
    </Item>
    <Item type="CFirstPersonDriveByLookAroundData">
      <Name>LOW_DOMINATOR9_FRONT_LEFT</Name>
      <AllowLookback value="true" />
      <HeadingLimits x="-190.000000" y="160.000000" />
      <DataLeft>
        <Offsets>
          <Item>
            <Offset value="-0.320000" />
            <AngleToBlendInOffset x="45.000000" y="120.000000" />
          </Item>
          <Item>
            <Offset value="0.050000" />
            <AngleToBlendInOffset x="45.000000" y="120.000000" />
          </Item>
          <Item>
            <Offset value="0.005000" />
            <AngleToBlendInOffset x="45.000000" y="120.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-10.000000" y="0.000000" />
        <AngleToBlendInExtraPitch x="45.000000" y="110.000000" />
      </DataLeft>
      <DataRight>
        <Offsets>
          <Item>
            <Offset value="-0.010000" />
            <AngleToBlendInOffset x="45.000000" y="90.000000" />
          </Item>
          <Item>
            <Offset value="-0.100000" />
            <AngleToBlendInOffset x="80.000000" y="165.000000" />
          </Item>
          <Item>
            <Offset value="0.075000" />
            <AngleToBlendInOffset x="120.000000" y="160.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-4.000000" y="-13.000000" />
        <AngleToBlendInExtraPitch x="0.000000" y="100.000000" />
      </DataRight>
      <WheelClipInfo>
        <HeadingLimitsLeft x="0.380000" y="0.430000" />
        <HeadingLimitsRight x="0.480000" y="0.530000" />
        <PitchLimits x="0.470000" y="0.530000" />
        <PitchOffset x="0.000000" y="0.050000" />
        <WheelAngleLimits x="-0.700000" y="0.000000" />
        <WheelAngleOffset x="0.010000" y="0.040000" />
        <MaxWheelOffsetY value="0.170000" />
        <WheelClipLerpInRate value="0.150000" />
        <WheelClipLerpOutRate value="0.180000" />
      </WheelClipInfo>
    </Item>
    <Item type="CFirstPersonDriveByLookAroundData">
      <Name>LOW_DOMINATOR9_FRONT_RIGHT</Name>
      <AllowLookback value="true" />
      <HeadingLimits x="-190.000000" y="160.000000" />
      <DataLeft>
        <Offsets>
          <Item>
            <Offset value="0.080000" />
            <AngleToBlendInOffset x="45.000000" y="90.000000" />
          </Item>
          <Item>
            <Offset value="-0.100000" />
            <AngleToBlendInOffset x="80.000000" y="165.000000" />
          </Item>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="0.000000" y="0.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="0.000000" y="-10.500000" />
        <AngleToBlendInExtraPitch x="0.000000" y="57.000000" />
      </DataLeft>
      <DataRight>
        <Offsets>
          <Item>
            <Offset value="0.390000" />
            <AngleToBlendInOffset x="45.000000" y="90.000000" />
          </Item>
          <Item>
            <Offset value="-0.220000" />
            <AngleToBlendInOffset x="45.000000" y="90.000000" />
          </Item>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="45.000000" y="120.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-10.000000" y="0.000000" />
        <AngleToBlendInExtraPitch x="45.000000" y="110.000000" />
      </DataRight>
      <WheelClipInfo>
        <HeadingLimitsLeft x="0.380000" y="0.430000" />
        <HeadingLimitsRight x="0.480000" y="0.530000" />
        <PitchLimits x="0.470000" y="0.530000" />
        <PitchOffset x="0.000000" y="0.050000" />
        <WheelAngleLimits x="-0.700000" y="0.000000" />
        <WheelAngleOffset x="0.010000" y="0.040000" />
        <MaxWheelOffsetY value="0.170000" />
        <WheelClipLerpInRate value="0.150000" />
        <WheelClipLerpOutRate value="0.180000" />
      </WheelClipInfo>
    </Item>
    <Item type="CFirstPersonDriveByLookAroundData">
      <Name>STD_DORADO_FRONT_LEFT</Name>
      <AllowLookback value="true" />
      <HeadingLimits x="-189.000000" y="173.000000" />
      <DataLeft>
        <Offsets>
          <Item>
            <Offset value="-0.415000" />
            <AngleToBlendInOffset x="40.000000" y="120.000000" />
          </Item>
          <Item>
            <Offset value="0.150000" />
            <AngleToBlendInOffset x="50.000000" y="120.000000" />
          </Item>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="0.000000" y="0.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-17.000000" y="10.000000" />
        <AngleToBlendInExtraPitch x="5.000000" y="110.000000" />
      </DataLeft>
      <DataRight>
        <Offsets>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="0.000000" y="0.000000" />
          </Item>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="0.000000" y="0.000000" />
          </Item>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="0.000000" y="0.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="0.300000" y="-8.500000" />
        <AngleToBlendInExtraPitch x="0.000000" y="70.000000" />
      </DataRight>
      <WheelClipInfo>
        <HeadingLimitsLeft x="0.380000" y="0.430000" />
        <HeadingLimitsRight x="0.480000" y="0.530000" />
        <PitchLimits x="0.470000" y="0.530000" />
        <PitchOffset x="0.000000" y="0.010000" />
        <WheelAngleLimits x="-0.700000" y="0.000000" />
        <WheelAngleOffset x="0.010000" y="0.020000" />
        <MaxWheelOffsetY value="0.170000" />
        <WheelClipLerpInRate value="0.150000" />
        <WheelClipLerpOutRate value="0.180000" />
      </WheelClipInfo>
    </Item>
    <Item type="CFirstPersonDriveByLookAroundData">
      <Name>STD_DORADO_FRONT_RIGHT</Name>
      <AllowLookback value="true" />
      <HeadingLimits x="-189.000000" y="165.000000" />
      <DataLeft>
        <Offsets>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="0.000000" y="0.000000" />
          </Item>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="0.000000" y="0.000000" />
          </Item>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="0.000000" y="0.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="0.300000" y="-8.500000" />
        <AngleToBlendInExtraPitch x="0.000000" y="70.000000" />
      </DataLeft>
      <DataRight>
        <Offsets>
          <Item>
            <Offset value="0.350000" />
            <AngleToBlendInOffset x="60.000000" y="120.000000" />
          </Item>
          <Item>
            <Offset value="0.150000" />
            <AngleToBlendInOffset x="60.000000" y="120.000000" />
          </Item>
          <Item>
            <Offset value="0.030000" />
            <AngleToBlendInOffset x="5.000000" y="60.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-20.000000" y="5.000000" />
        <AngleToBlendInExtraPitch x="5.000000" y="90.000000" />
      </DataRight>
      <WheelClipInfo>
        <HeadingLimitsLeft x="0.380000" y="0.430000" />
        <HeadingLimitsRight x="0.480000" y="0.530000" />
        <PitchLimits x="0.470000" y="0.530000" />
        <PitchOffset x="0.000000" y="0.050000" />
        <WheelAngleLimits x="-0.700000" y="0.000000" />
        <WheelAngleOffset x="0.010000" y="0.040000" />
        <MaxWheelOffsetY value="0.170000" />
        <WheelClipLerpInRate value="0.150000" />
        <WheelClipLerpOutRate value="0.180000" />
      </WheelClipInfo>
    </Item>
    <Item type="CFirstPersonDriveByLookAroundData">
      <Name>STD_DORADO_REAR_LEFT</Name>
      <AllowLookback value="true" />
      <HeadingLimits x="-190.000000" y="140.000000" />
      <DataLeft>
        <Offsets>
          <Item>
            <Offset value="-0.250000" />
            <AngleToBlendInOffset x="25.000000" y="140.000000" />
          </Item>
          <Item>
            <Offset value="0.400000" />
            <AngleToBlendInOffset x="0.000000" y="120.000000" />
          </Item>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="0.000000" y="140.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-10.000000" y="10.000000" />
        <AngleToBlendInExtraPitch x="20.000000" y="100.000000" />
      </DataLeft>
      <DataRight>
        <Offsets>
          <Item>
            <Offset value="-0.140000" />
            <AngleToBlendInOffset x="0.000000" y="190.000000" />
          </Item>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="0.000000" y="190.000000" />
          </Item>
          <Item>
            <Offset value="0.050000" />
            <AngleToBlendInOffset x="0.000000" y="190.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-7.500000" y="-9.000000" />
        <AngleToBlendInExtraPitch x="0.000000" y="190.000000" />
      </DataRight>
      <WheelClipInfo>
        <HeadingLimitsLeft x="0.380000" y="0.430000" />
        <HeadingLimitsRight x="0.480000" y="0.530000" />
        <PitchLimits x="0.470000" y="0.530000" />
        <PitchOffset x="0.000000" y="0.050000" />
        <WheelAngleLimits x="-0.700000" y="0.000000" />
        <WheelAngleOffset x="0.010000" y="0.040000" />
        <MaxWheelOffsetY value="0.170000" />
        <WheelClipLerpInRate value="0.150000" />
        <WheelClipLerpOutRate value="0.180000" />
      </WheelClipInfo>
    </Item>
    <Item type="CFirstPersonDriveByLookAroundData">
      <Name>STD_DORADO_REAR_RIGHT</Name>
      <AllowLookback value="true" />
      <HeadingLimits x="-190.000000" y="140.000000" />
      <DataLeft>
        <Offsets>
          <Item>
            <Offset value="0.140000" />
            <AngleToBlendInOffset x="0.000000" y="190.000000" />
          </Item>
          <Item>
            <Offset value="-0.050000" />
            <AngleToBlendInOffset x="0.000000" y="190.000000" />
          </Item>
          <Item>
            <Offset value="0.050000" />
            <AngleToBlendInOffset x="0.000000" y="190.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-8.000000" y="-8.500000" />
        <AngleToBlendInExtraPitch x="0.000000" y="190.000000" />
      </DataLeft>
      <DataRight>
        <Offsets>
          <Item>
            <Offset value="0.250000" />
            <AngleToBlendInOffset x="25.000000" y="140.000000" />
          </Item>
          <Item>
            <Offset value="0.400000" />
            <AngleToBlendInOffset x="0.000000" y="120.000000" />
          </Item>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="0.000000" y="140.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-10.000000" y="10.000000" />
        <AngleToBlendInExtraPitch x="20.000000" y="100.000000" />
      </DataRight>
      <WheelClipInfo>
        <HeadingLimitsLeft x="0.380000" y="0.430000" />
        <HeadingLimitsRight x="0.480000" y="0.530000" />
        <PitchLimits x="0.470000" y="0.530000" />
        <PitchOffset x="0.000000" y="0.050000" />
        <WheelAngleLimits x="-0.700000" y="0.000000" />
        <WheelAngleOffset x="0.010000" y="0.040000" />
        <MaxWheelOffsetY value="0.170000" />
        <WheelClipLerpInRate value="0.150000" />
        <WheelClipLerpOutRate value="0.180000" />
      </WheelClipInfo>
    </Item>
    <Item type="CFirstPersonDriveByLookAroundData">
      <Name>LOW_FR36_FRONT_LEFT</Name>
      <AllowLookback value="true" />
      <HeadingLimits x="-185.000000" y="165.000000" />
      <DataLeft>
        <Offsets>
          <Item>
            <Offset value="-0.300000" />
            <AngleToBlendInOffset x="40.000000" y="160.000000" />
          </Item>
          <Item>
            <Offset value="0.150000" />
            <AngleToBlendInOffset x="60.000000" y="160.000000" />
          </Item>
          <Item>
            <Offset value="-0.045000" />
            <AngleToBlendInOffset x="25.000000" y="120.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-5.000000" y="8.000000" />
        <AngleToBlendInExtraPitch x="50.000000" y="110.000000" />
      </DataLeft>
      <DataRight>
        <Offsets>
          <Item>
            <Offset value="-0.050000" />
            <AngleToBlendInOffset x="45.000000" y="90.000000" />
          </Item>
          <Item>
            <Offset value="-0.060000" />
            <AngleToBlendInOffset x="0.000000" y="190.000000" />
          </Item>
          <Item>
            <Offset value="0.025000" />
            <AngleToBlendInOffset x="50.000000" y="90.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-1.000000" y="-10.000000" />
        <AngleToBlendInExtraPitch x="0.000000" y="50.000000" />
      </DataRight>
      <WheelClipInfo>
        <HeadingLimitsLeft x="0.200000" y="0.430000" />
        <HeadingLimitsRight x="0.480000" y="0.500000" />
        <PitchLimits x="0.470000" y="0.530000" />
        <PitchOffset x="0.000000" y="0.100000" />
        <WheelAngleLimits x="-0.700000" y="0.000000" />
        <WheelAngleOffset x="0.010000" y="0.040000" />
        <MaxWheelOffsetY value="0.170000" />
        <WheelClipLerpInRate value="0.150000" />
        <WheelClipLerpOutRate value="0.180000" />
      </WheelClipInfo>
    </Item>
    <Item type="CFirstPersonDriveByLookAroundData">
      <Name>LOW_FR36_FRONT_RIGHT</Name>
      <AllowLookback value="true" />
      <HeadingLimits x="-183.000000" y="160.000000" />
      <DataLeft>
        <Offsets>
          <Item>
            <Offset value="0.070000" />
            <AngleToBlendInOffset x="45.000000" y="90.000000" />
          </Item>
          <Item>
            <Offset value="-0.150000" />
            <AngleToBlendInOffset x="0.000000" y="190.000000" />
          </Item>
          <Item>
            <Offset value="-0.020000" />
            <AngleToBlendInOffset x="90.000000" y="140.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="1.000000" y="-7.000000" />
        <AngleToBlendInExtraPitch x="0.000000" y="50.000000" />
      </DataLeft>
      <DataRight>
        <Offsets>
          <Item>
            <Offset value="0.300000" />
            <AngleToBlendInOffset x="40.000000" y="160.000000" />
          </Item>
          <Item>
            <Offset value="0.120000" />
            <AngleToBlendInOffset x="60.000000" y="160.000000" />
          </Item>
          <Item>
            <Offset value="-0.040000" />
            <AngleToBlendInOffset x="30.000000" y="140.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-5.000000" y="5.000000" />
        <AngleToBlendInExtraPitch x="50.000000" y="110.000000" />
      </DataRight>
      <WheelClipInfo>
        <HeadingLimitsLeft x="0.380000" y="0.430000" />
        <HeadingLimitsRight x="0.480000" y="0.530000" />
        <PitchLimits x="0.470000" y="0.530000" />
        <PitchOffset x="0.000000" y="0.050000" />
        <WheelAngleLimits x="-0.700000" y="0.000000" />
        <WheelAngleOffset x="0.010000" y="0.040000" />
        <MaxWheelOffsetY value="0.170000" />
        <WheelClipLerpInRate value="0.150000" />
        <WheelClipLerpOutRate value="0.180000" />
      </WheelClipInfo>
    </Item>
    <Item type="CFirstPersonDriveByLookAroundData">
      <Name>FREIGHT2_FRONT_LEFT</Name>
      <AllowLookback value="true" />
      <HeadingLimits x="-200.000000" y="156.000000" />
      <DataLeft>
        <Offsets>
          <Item>
            <Offset value="-0.250000" />
            <AngleToBlendInOffset x="40.000000" y="120.000000" />
          </Item>
          <Item>
            <Offset value="0.150000" />
            <AngleToBlendInOffset x="60.000000" y="160.000000" />
          </Item>
          <Item>
            <Offset value="0.050000" />
            <AngleToBlendInOffset x="45.000000" y="120.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-15.000000" y="0.000000" />
        <AngleToBlendInExtraPitch x="45.000000" y="110.000000" />
      </DataLeft>
      <DataRight>
        <Offsets>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="45.000000" y="90.000000" />
          </Item>
          <Item>
            <Offset value="-0.000000" />
            <AngleToBlendInOffset x="0.000000" y="0.000000" />
          </Item>
          <Item>
            <Offset value="0.200000" />
            <AngleToBlendInOffset x="90.000000" y="180.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-10.000000" y="-10.000000" />
        <AngleToBlendInExtraPitch x="0.000000" y="190.000000" />
      </DataRight>
      <WheelClipInfo>
        <HeadingLimitsLeft x="0.380000" y="0.430000" />
        <HeadingLimitsRight x="0.480000" y="0.530000" />
        <PitchLimits x="0.470000" y="0.530000" />
        <PitchOffset x="0.000000" y="0.020000" />
        <WheelAngleLimits x="-0.700000" y="0.000000" />
        <WheelAngleOffset x="0.010000" y="0.020000" />
        <MaxWheelOffsetY value="0.170000" />
        <WheelClipLerpInRate value="0.150000" />
        <WheelClipLerpOutRate value="0.180000" />
      </WheelClipInfo>
    </Item>
    <Item type="CFirstPersonDriveByLookAroundData">
      <Name>FREIGHT2_FRONT_RIGHT</Name>
      <AllowLookback value="true" />
      <HeadingLimits x="-204.000000" y="157.000000" />
      <DataLeft>
        <Offsets>
          <Item>
            <Offset value="0.050000" />
            <AngleToBlendInOffset x="45.000000" y="90.000000" />
          </Item>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="0.000000" y="0.000000" />
          </Item>
          <Item>
            <Offset value="0.100000" />
            <AngleToBlendInOffset x="90.000000" y="180.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-10.000000" y="-3.000000" />
        <AngleToBlendInExtraPitch x="0.000000" y="190.000000" />
      </DataLeft>
      <DataRight>
        <Offsets>
          <Item>
            <Offset value="0.250000" />
            <AngleToBlendInOffset x="40.000000" y="120.000000" />
          </Item>
          <Item>
            <Offset value="0.150000" />
            <AngleToBlendInOffset x="60.000000" y="160.000000" />
          </Item>
          <Item>
            <Offset value="0.080000" />
            <AngleToBlendInOffset x="45.000000" y="120.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-10.000000" y="-3.000000" />
        <AngleToBlendInExtraPitch x="45.000000" y="110.000000" />
      </DataRight>
      <WheelClipInfo>
        <HeadingLimitsLeft x="0.380000" y="0.430000" />
        <HeadingLimitsRight x="0.480000" y="0.530000" />
        <PitchLimits x="0.470000" y="0.530000" />
        <PitchOffset x="0.000000" y="0.050000" />
        <WheelAngleLimits x="-0.700000" y="0.000000" />
        <WheelAngleOffset x="0.010000" y="0.040000" />
        <MaxWheelOffsetY value="0.170000" />
        <WheelClipLerpInRate value="0.150000" />
        <WheelClipLerpOutRate value="0.180000" />
      </WheelClipInfo>
    </Item>
    <Item type="CFirstPersonDriveByLookAroundData">
      <Name>STD_IMPALER5_FRONT_LEFT</Name>
      <AllowLookback value="true" />
      <HeadingLimits x="-190.000000" y="160.000000" />
      <DataLeft>
        <Offsets>
          <Item>
            <Offset value="-0.380000" />
            <AngleToBlendInOffset x="20.000000" y="100.000000" />
          </Item>
          <Item>
            <Offset value="0.100000" />
            <AngleToBlendInOffset x="40.000000" y="100.000000" />
          </Item>
          <Item>
            <Offset value="0.050000" />
            <AngleToBlendInOffset x="0.000000" y="50.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-15.000000" y="10.000000" />
        <AngleToBlendInExtraPitch x="45.000000" y="110.000000" />
      </DataLeft>
      <DataRight>
        <Offsets>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="0.000000" y="190.000000" />
          </Item>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="0.000000" y="190.000000" />
          </Item>
          <Item>
            <Offset value="0.070000" />
            <AngleToBlendInOffset x="5.000000" y="50.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-2.000000" y="-10.000000" />
        <AngleToBlendInExtraPitch x="0.000000" y="90.000000" />
      </DataRight>
      <WheelClipInfo>
        <HeadingLimitsLeft x="0.380000" y="0.430000" />
        <HeadingLimitsRight x="0.480000" y="0.530000" />
        <PitchLimits x="0.470000" y="0.530000" />
        <PitchOffset x="0.000000" y="0.050000" />
        <WheelAngleLimits x="-0.700000" y="0.000000" />
        <WheelAngleOffset x="0.010000" y="0.040000" />
        <MaxWheelOffsetY value="0.170000" />
        <WheelClipLerpInRate value="0.150000" />
        <WheelClipLerpOutRate value="0.180000" />
      </WheelClipInfo>
    </Item>
    <Item type="CFirstPersonDriveByLookAroundData">
      <Name>STD_IMPALER5_FRONT_RIGHT</Name>
      <AllowLookback value="true" />
      <HeadingLimits x="-190.000000" y="160.000000" />
      <DataLeft>
        <Offsets>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="0.000000" y="190.000000" />
          </Item>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="0.000000" y="190.000000" />
          </Item>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="0.000000" y="190.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-0.500000" y="-7.300000" />
        <AngleToBlendInExtraPitch x="0.000000" y="90.000000" />
      </DataLeft>
      <DataRight>
        <Offsets>
          <Item>
            <Offset value="0.400000" />
            <AngleToBlendInOffset x="20.000000" y="100.000000" />
          </Item>
          <Item>
            <Offset value="0.200000" />
            <AngleToBlendInOffset x="40.000000" y="100.000000" />
          </Item>
          <Item>
            <Offset value="-0.025000" />
            <AngleToBlendInOffset x="0.000000" y="90.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-15.000000" y="20.000000" />
        <AngleToBlendInExtraPitch x="45.000000" y="110.000000" />
      </DataRight>
      <WheelClipInfo>
        <HeadingLimitsLeft x="0.380000" y="0.430000" />
        <HeadingLimitsRight x="0.480000" y="0.530000" />
        <PitchLimits x="0.470000" y="0.530000" />
        <PitchOffset x="0.000000" y="0.050000" />
        <WheelAngleLimits x="-0.700000" y="0.000000" />
        <WheelAngleOffset x="0.010000" y="0.040000" />
        <MaxWheelOffsetY value="0.170000" />
        <WheelClipLerpInRate value="0.150000" />
        <WheelClipLerpOutRate value="0.180000" />
      </WheelClipInfo>
    </Item>
    <Item type="CFirstPersonDriveByLookAroundData">
      <Name>STD_IMPALER5_REAR_LEFT</Name>
      <AllowLookback value="true" />
      <HeadingLimits x="-190.000000" y="160.000000" />
      <DataLeft>
        <Offsets>
          <Item>
            <Offset value="-0.400000" />
            <AngleToBlendInOffset x="25.000000" y="150.000000" />
          </Item>
          <Item>
            <Offset value="0.350000" />
            <AngleToBlendInOffset x="20.000000" y="160.000000" />
          </Item>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="0.000000" y="120.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-10.000000" y="10.000000" />
        <AngleToBlendInExtraPitch x="20.000000" y="100.000000" />
      </DataLeft>
      <DataRight>
        <Offsets>
          <Item>
            <Offset value="-0.100000" />
            <AngleToBlendInOffset x="0.000000" y="90.000000" />
          </Item>
          <Item>
            <Offset value="0.050000" />
            <AngleToBlendInOffset x="0.000000" y="90.000000" />
          </Item>
          <Item>
            <Offset value="0.075000" />
            <AngleToBlendInOffset x="0.000000" y="100.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-4.000000" y="0.000000" />
        <AngleToBlendInExtraPitch x="0.000000" y="120.000000" />
      </DataRight>
      <WheelClipInfo>
        <HeadingLimitsLeft x="0.380000" y="0.430000" />
        <HeadingLimitsRight x="0.480000" y="0.530000" />
        <PitchLimits x="0.470000" y="0.530000" />
        <PitchOffset x="0.000000" y="0.050000" />
        <WheelAngleLimits x="-0.700000" y="0.000000" />
        <WheelAngleOffset x="0.010000" y="0.040000" />
        <MaxWheelOffsetY value="0.170000" />
        <WheelClipLerpInRate value="0.150000" />
        <WheelClipLerpOutRate value="0.180000" />
      </WheelClipInfo>
    </Item>
    <Item type="CFirstPersonDriveByLookAroundData">
      <Name>STD_IMPALER5_REAR_RIGHT</Name>
      <AllowLookback value="true" />
      <HeadingLimits x="-190.000000" y="160.000000" />
      <DataLeft>
        <Offsets>
          <Item>
            <Offset value="0.100000" />
            <AngleToBlendInOffset x="0.000000" y="90.000000" />
          </Item>
          <Item>
            <Offset value="0.020000" />
            <AngleToBlendInOffset x="0.000000" y="90.000000" />
          </Item>
          <Item>
            <Offset value="0.075000" />
            <AngleToBlendInOffset x="0.000000" y="100.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-4.000000" y="0.000000" />
        <AngleToBlendInExtraPitch x="0.000000" y="120.000000" />
      </DataLeft>
      <DataRight>
        <Offsets>
          <Item>
            <Offset value="0.400000" />
            <AngleToBlendInOffset x="25.000000" y="150.000000" />
          </Item>
          <Item>
            <Offset value="0.350000" />
            <AngleToBlendInOffset x="20.000000" y="160.000000" />
          </Item>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="0.000000" y="120.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-10.000000" y="10.000000" />
        <AngleToBlendInExtraPitch x="20.000000" y="100.000000" />
      </DataRight>
      <WheelClipInfo>
        <HeadingLimitsLeft x="0.380000" y="0.430000" />
        <HeadingLimitsRight x="0.480000" y="0.530000" />
        <PitchLimits x="0.470000" y="0.530000" />
        <PitchOffset x="0.000000" y="0.050000" />
        <WheelAngleLimits x="-0.700000" y="0.000000" />
        <WheelAngleOffset x="0.010000" y="0.040000" />
        <MaxWheelOffsetY value="0.170000" />
        <WheelClipLerpInRate value="0.150000" />
        <WheelClipLerpOutRate value="0.180000" />
      </WheelClipInfo>
    </Item>
    <Item type="CFirstPersonDriveByLookAroundData">
      <Name>STD_IMPALER6_FRONT_LEFT</Name>
      <AllowLookback value="true" />
      <HeadingLimits x="-190.000000" y="160.000000" />
      <DataLeft>
        <Offsets>
          <Item>
            <Offset value="-0.350000" />
            <AngleToBlendInOffset x="40.000000" y="160.000000" />
          </Item>
          <Item>
            <Offset value="0.150000" />
            <AngleToBlendInOffset x="60.000000" y="160.000000" />
          </Item>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="60.000000" y="120.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-15.000000" y="8.000000" />
        <AngleToBlendInExtraPitch x="50.000000" y="110.000000" />
      </DataLeft>
      <DataRight>
        <Offsets>
          <Item>
            <Offset value="-0.030000" />
            <AngleToBlendInOffset x="45.000000" y="130.000000" />
          </Item>
          <Item>
            <Offset value="-0.110000" />
            <AngleToBlendInOffset x="0.000000" y="130.000000" />
          </Item>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="0.000000" y="90.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-1.500000" y="-10.000000" />
        <AngleToBlendInExtraPitch x="0.000000" y="190.000000" />
      </DataRight>
      <WheelClipInfo>
        <HeadingLimitsLeft x="0.380000" y="0.430000" />
        <HeadingLimitsRight x="0.480000" y="0.530000" />
        <PitchLimits x="0.470000" y="0.530000" />
        <PitchOffset x="0.000000" y="0.050000" />
        <WheelAngleLimits x="-0.700000" y="0.000000" />
        <WheelAngleOffset x="0.010000" y="0.040000" />
        <MaxWheelOffsetY value="0.170000" />
        <WheelClipLerpInRate value="0.150000" />
        <WheelClipLerpOutRate value="0.180000" />
      </WheelClipInfo>
    </Item>
    <Item type="CFirstPersonDriveByLookAroundData">
      <Name>STD_IMPALER6_FRONT_RIGHT</Name>
      <AllowLookback value="true" />
      <HeadingLimits x="-190.000000" y="160.000000" />
      <DataLeft>
        <Offsets>
          <Item>
            <Offset value="0.050000" />
            <AngleToBlendInOffset x="45.000000" y="90.000000" />
          </Item>
          <Item>
            <Offset value="-0.150000" />
            <AngleToBlendInOffset x="0.000000" y="100.000000" />
          </Item>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="0.000000" y="90.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-1.000000" y="-10.000000" />
        <AngleToBlendInExtraPitch x="0.000000" y="190.000000" />
      </DataLeft>
      <DataRight>
        <Offsets>
          <Item>
            <Offset value="0.350000" />
            <AngleToBlendInOffset x="40.000000" y="160.000000" />
          </Item>
          <Item>
            <Offset value="0.150000" />
            <AngleToBlendInOffset x="60.000000" y="160.000000" />
          </Item>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="60.000000" y="120.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-15.000000" y="8.000000" />
        <AngleToBlendInExtraPitch x="50.000000" y="110.000000" />
      </DataRight>
      <WheelClipInfo>
        <HeadingLimitsLeft x="0.380000" y="0.430000" />
        <HeadingLimitsRight x="0.480000" y="0.530000" />
        <PitchLimits x="0.470000" y="0.530000" />
        <PitchOffset x="0.000000" y="0.050000" />
        <WheelAngleLimits x="-0.700000" y="0.000000" />
        <WheelAngleOffset x="0.010000" y="0.040000" />
        <MaxWheelOffsetY value="0.170000" />
        <WheelClipLerpInRate value="0.150000" />
        <WheelClipLerpOutRate value="0.180000" />
      </WheelClipInfo>
    </Item>
    <Item type="CFirstPersonDriveByLookAroundData">
      <Name>STD_IMPALER6_REAR_LEFT</Name>
      <AllowLookback value="true" />
      <HeadingLimits x="-190.000000" y="80.000000" />
      <DataLeft>
        <Offsets>
          <Item>
            <Offset value="-0.500000" />
            <AngleToBlendInOffset x="25.000000" y="0.000000" />
          </Item>
          <Item>
            <Offset value="-0.150000" />
            <AngleToBlendInOffset x="0.000000" y="90.000000" />
          </Item>
          <Item>
            <Offset value="-0.025000" />
            <AngleToBlendInOffset x="0.000000" y="90.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-10.000000" y="6.000000" />
        <AngleToBlendInExtraPitch x="20.000000" y="100.000000" />
      </DataLeft>
      <DataRight>
        <Offsets>
          <Item>
            <Offset value="-0.125000" />
            <AngleToBlendInOffset x="0.000000" y="90.000000" />
          </Item>
          <Item>
            <Offset value="-0.050000" />
            <AngleToBlendInOffset x="0.000000" y="90.000000" />
          </Item>
          <Item>
            <Offset value="0.035000" />
            <AngleToBlendInOffset x="0.000000" y="140.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="0.000000" y="-5.000000" />
        <AngleToBlendInExtraPitch x="0.000000" y="90.000000" />
      </DataRight>
      <WheelClipInfo>
        <HeadingLimitsLeft x="0.380000" y="0.430000" />
        <HeadingLimitsRight x="0.480000" y="0.530000" />
        <PitchLimits x="0.470000" y="0.530000" />
        <PitchOffset x="0.000000" y="0.050000" />
        <WheelAngleLimits x="-0.700000" y="0.000000" />
        <WheelAngleOffset x="0.010000" y="0.040000" />
        <MaxWheelOffsetY value="0.170000" />
        <WheelClipLerpInRate value="0.150000" />
        <WheelClipLerpOutRate value="0.180000" />
      </WheelClipInfo>
    </Item>
    <Item type="CFirstPersonDriveByLookAroundData">
      <Name>STD_IMPALER6_REAR_RIGHT</Name>
      <AllowLookback value="true" />
      <HeadingLimits x="-190.000000" y="150.000000" />
      <DataLeft>
        <Offsets>
          <Item>
            <Offset value="0.150000" />
            <AngleToBlendInOffset x="0.000000" y="90.000000" />
          </Item>
          <Item>
            <Offset value="-0.050000" />
            <AngleToBlendInOffset x="0.000000" y="90.000000" />
          </Item>
          <Item>
            <Offset value="0.035000" />
            <AngleToBlendInOffset x="0.000000" y="140.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="0.000000" y="-5.000000" />
        <AngleToBlendInExtraPitch x="0.000000" y="90.000000" />
      </DataLeft>
      <DataRight>
        <Offsets>
          <Item>
            <Offset value="0.300000" />
            <AngleToBlendInOffset x="25.000000" y="150.000000" />
          </Item>
          <Item>
            <Offset value="0.200000" />
            <AngleToBlendInOffset x="0.000000" y="90.000000" />
          </Item>
          <Item>
            <Offset value="-0.025000" />
            <AngleToBlendInOffset x="0.000000" y="90.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-10.000000" y="6.000000" />
        <AngleToBlendInExtraPitch x="20.000000" y="100.000000" />
      </DataRight>
      <WheelClipInfo>
        <HeadingLimitsLeft x="0.380000" y="0.430000" />
        <HeadingLimitsRight x="0.480000" y="0.530000" />
        <PitchLimits x="0.470000" y="0.530000" />
        <PitchOffset x="0.000000" y="0.050000" />
        <WheelAngleLimits x="-0.700000" y="0.000000" />
        <WheelAngleOffset x="0.010000" y="0.040000" />
        <MaxWheelOffsetY value="0.170000" />
        <WheelClipLerpInRate value="0.150000" />
        <WheelClipLerpOutRate value="0.180000" />
      </WheelClipInfo>
    </Item>
    <Item type="CFirstPersonDriveByLookAroundData">
      <Name>TRUCK_PHANTOM4_FRONT_LEFT</Name>
      <AllowLookback value="true" />
      <HeadingLimits x="-89.000000" y="120.000000" />
      <DataLeft>
        <Offsets>
          <Item>
            <Offset value="-0.200000" />
            <AngleToBlendInOffset x="40.000000" y="90.000000" />
          </Item>
          <Item>
            <Offset value="0.250000" />
            <AngleToBlendInOffset x="5.000000" y="120.000000" />
          </Item>
          <Item>
            <Offset value="0.065000" />
            <AngleToBlendInOffset x="25.000000" y="120.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-20.000000" y="5.000000" />
        <AngleToBlendInExtraPitch x="5.000000" y="90.000000" />
      </DataLeft>
      <DataRight>
        <Offsets>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="0.000000" y="0.000000" />
          </Item>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="0.000000" y="0.000000" />
          </Item>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="0.000000" y="0.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-16.000000" y="0.000000" />
        <AngleToBlendInExtraPitch x="25.000000" y="195.000000" />
      </DataRight>
      <WheelClipInfo>
        <HeadingLimitsLeft x="0.380000" y="0.430000" />
        <HeadingLimitsRight x="0.480000" y="0.530000" />
        <PitchLimits x="0.470000" y="0.530000" />
        <PitchOffset x="0.000000" y="0.050000" />
        <WheelAngleLimits x="-0.700000" y="0.000000" />
        <WheelAngleOffset x="0.010000" y="0.040000" />
        <MaxWheelOffsetY value="0.170000" />
        <WheelClipLerpInRate value="0.150000" />
        <WheelClipLerpOutRate value="0.180000" />
      </WheelClipInfo>
    </Item>
    <Item type="CFirstPersonDriveByLookAroundData">
      <Name>TRUCK_PHANTOM4_FRONT_RIGHT</Name>
      <AllowLookback value="true" />
      <HeadingLimits x="-89.000000" y="120.000000" />
      <DataLeft>
        <Offsets>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="0.000000" y="0.000000" />
          </Item>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="0.000000" y="0.000000" />
          </Item>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="0.000000" y="0.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-17.000000" y="-2.000000" />
        <AngleToBlendInExtraPitch x="25.000000" y="195.000000" />
      </DataLeft>
      <DataRight>
        <Offsets>
          <Item>
            <Offset value="0.200000" />
            <AngleToBlendInOffset x="40.000000" y="90.000000" />
          </Item>
          <Item>
            <Offset value="0.250000" />
            <AngleToBlendInOffset x="5.000000" y="120.000000" />
          </Item>
          <Item>
            <Offset value="0.065000" />
            <AngleToBlendInOffset x="25.000000" y="120.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-20.000000" y="5.000000" />
        <AngleToBlendInExtraPitch x="5.000000" y="90.000000" />
      </DataRight>
      <WheelClipInfo>
        <HeadingLimitsLeft x="0.380000" y="0.430000" />
        <HeadingLimitsRight x="0.480000" y="0.530000" />
        <PitchLimits x="0.470000" y="0.530000" />
        <PitchOffset x="0.000000" y="0.050000" />
        <WheelAngleLimits x="-0.700000" y="0.000000" />
        <WheelAngleOffset x="0.010000" y="0.040000" />
        <MaxWheelOffsetY value="0.170000" />
        <WheelClipLerpInRate value="0.150000" />
        <WheelClipLerpOutRate value="0.180000" />
      </WheelClipInfo>
    </Item>
    <Item type="CFirstPersonDriveByLookAroundData">
      <Name>STD_POLGAUNTLET_FRONT_LEFT</Name>
      <AllowLookback value="false" />
      <HeadingLimits x="-107.000000" y="160.000000" />
      <DataLeft>
        <Offsets>
          <Item>
            <Offset value="-0.375000" />
            <AngleToBlendInOffset x="40.000000" y="160.000000" />
          </Item>
          <Item>
            <Offset value="0.020000" />
            <AngleToBlendInOffset x="40.000000" y="160.000000" />
          </Item>
          <Item>
            <Offset value="-0.010000" />
            <AngleToBlendInOffset x="40.000000" y="100.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-7.000000" y="0.000000" />
        <AngleToBlendInExtraPitch x="35.000000" y="100.000000" />
      </DataLeft>
      <DataRight>
        <Offsets>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="35.000000" y="100.000000" />
          </Item>
          <Item>
            <Offset value="0.200000" />
            <AngleToBlendInOffset x="35.000000" y="100.000000" />
          </Item>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="35.000000" y="100.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-5.000000" y="-12.000000" />
        <AngleToBlendInExtraPitch x="35.000000" y="100.000000" />
      </DataRight>
      <WheelClipInfo>
        <HeadingLimitsLeft x="0.280000" y="0.430000" />
        <HeadingLimitsRight x="0.480000" y="0.530000" />
        <PitchLimits x="0.470000" y="0.530000" />
        <PitchOffset x="0.000000" y="0.010000" />
        <WheelAngleLimits x="-0.700000" y="0.000000" />
        <WheelAngleOffset x="0.010000" y="0.030000" />
        <MaxWheelOffsetY value="0.170000" />
        <WheelClipLerpInRate value="0.150000" />
        <WheelClipLerpOutRate value="0.180000" />
      </WheelClipInfo>
    </Item>
    <Item type="CFirstPersonDriveByLookAroundData">
      <Name>STD_POLGAUNTLET_FRONT_RIGHT</Name>
      <AllowLookback value="false" />
      <HeadingLimits x="-107.000000" y="160.000000" />
      <DataLeft>
        <Offsets>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="35.000000" y="100.000000" />
          </Item>
          <Item>
            <Offset value="0.200000" />
            <AngleToBlendInOffset x="35.000000" y="100.000000" />
          </Item>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="35.000000" y="100.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-5.000000" y="-12.000000" />
        <AngleToBlendInExtraPitch x="35.000000" y="100.000000" />
      </DataLeft>
      <DataRight>
        <Offsets>
          <Item>
            <Offset value="0.375000" />
            <AngleToBlendInOffset x="40.000000" y="160.000000" />
          </Item>
          <Item>
            <Offset value="0.020000" />
            <AngleToBlendInOffset x="40.000000" y="160.000000" />
          </Item>
          <Item>
            <Offset value="-0.010000" />
            <AngleToBlendInOffset x="40.000000" y="80.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-7.000000" y="0.000000" />
        <AngleToBlendInExtraPitch x="40.000000" y="100.000000" />
      </DataRight>
      <WheelClipInfo>
        <HeadingLimitsLeft x="0.280000" y="0.430000" />
        <HeadingLimitsRight x="0.480000" y="0.530000" />
        <PitchLimits x="0.470000" y="0.530000" />
        <PitchOffset x="0.000000" y="0.010000" />
        <WheelAngleLimits x="-0.700000" y="0.000000" />
        <WheelAngleOffset x="0.010000" y="0.030000" />
        <MaxWheelOffsetY value="0.170000" />
        <WheelClipLerpInRate value="0.150000" />
        <WheelClipLerpOutRate value="0.180000" />
      </WheelClipInfo>
    </Item>
    <Item type="CFirstPersonDriveByLookAroundData">
      <Name>RANGER_TERMINUS_FRONT_LEFT</Name>
      <AllowLookback value="true" />
      <HeadingLimits x="-191.000000" y="160.000000" />
      <DataLeft>
        <Offsets>
          <Item>
            <Offset value="-0.375000" />
            <AngleToBlendInOffset x="40.000000" y="160.000000" />
          </Item>
          <Item>
            <Offset value="0.220000" />
            <AngleToBlendInOffset x="40.000000" y="160.000000" />
          </Item>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="0.000000" y="50.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-15.000000" y="-3.000000" />
        <AngleToBlendInExtraPitch x="0.000000" y="60.000000" />
      </DataLeft>
      <DataRight>
        <Offsets>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="0.000000" y="100.000000" />
          </Item>
          <Item>
            <Offset value="-0.070000" />
            <AngleToBlendInOffset x="0.000000" y="1.000000" />
          </Item>
          <Item>
            <Offset value="0.050000" />
            <AngleToBlendInOffset x="30.000000" y="90.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-8.000000" y="-14.500000" />
        <AngleToBlendInExtraPitch x="30.000000" y="90.000000" />
      </DataRight>
      <WheelClipInfo>
        <HeadingLimitsLeft x="0.380000" y="0.430000" />
        <HeadingLimitsRight x="0.480000" y="0.530000" />
        <PitchLimits x="0.470000" y="0.530000" />
        <PitchOffset x="0.000000" y="0.020000" />
        <WheelAngleLimits x="-0.700000" y="0.000000" />
        <WheelAngleOffset x="0.010000" y="0.020000" />
        <MaxWheelOffsetY value="0.170000" />
        <WheelClipLerpInRate value="0.150000" />
        <WheelClipLerpOutRate value="0.180000" />
      </WheelClipInfo>
    </Item>
    <Item type="CFirstPersonDriveByLookAroundData">
      <Name>RANGER_TERMINUS_FRONT_RIGHT</Name>
      <AllowLookback value="true" />
      <HeadingLimits x="-191.000000" y="160.000000" />
      <DataLeft>
        <Offsets>
          <Item>
            <Offset value="-0.000000" />
            <AngleToBlendInOffset x="0.000000" y="100.000000" />
          </Item>
          <Item>
            <Offset value="-0.050000" />
            <AngleToBlendInOffset x="0.000000" y="1.000000" />
          </Item>
          <Item>
            <Offset value="0.070000" />
            <AngleToBlendInOffset x="30.000000" y="90.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-8.500000" y="-15.300000" />
        <AngleToBlendInExtraPitch x="30.000000" y="90.000000" />
      </DataLeft>
      <DataRight>
        <Offsets>
          <Item>
            <Offset value="0.375000" />
            <AngleToBlendInOffset x="40.000000" y="160.000000" />
          </Item>
          <Item>
            <Offset value="0.220000" />
            <AngleToBlendInOffset x="40.000000" y="160.000000" />
          </Item>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="0.000000" y="50.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-15.000000" y="-3.000000" />
        <AngleToBlendInExtraPitch x="0.000000" y="60.000000" />
      </DataRight>
      <WheelClipInfo>
        <HeadingLimitsLeft x="0.380000" y="0.430000" />
        <HeadingLimitsRight x="0.480000" y="0.530000" />
        <PitchLimits x="0.470000" y="0.530000" />
        <PitchOffset x="0.000000" y="0.020000" />
        <WheelAngleLimits x="-0.700000" y="0.000000" />
        <WheelAngleOffset x="0.010000" y="0.020000" />
        <MaxWheelOffsetY value="0.170000" />
        <WheelClipLerpInRate value="0.150000" />
        <WheelClipLerpOutRate value="0.180000" />
      </WheelClipInfo>
    </Item>
    <Item type="CFirstPersonDriveByLookAroundData">
      <Name>RANGER_TERMINUS_REAR_LEFT</Name>
      <AllowLookback value="true" />
      <HeadingLimits x="-187.000000" y="160.000000" />
      <DataLeft>
        <Offsets>
          <Item>
            <Offset value="-0.350000" />
            <AngleToBlendInOffset x="0.000000" y="160.000000" />
          </Item>
          <Item>
            <Offset value="0.240000" />
            <AngleToBlendInOffset x="30.000000" y="160.000000" />
          </Item>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="0.000000" y="0.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-15.000000" y="5.000000" />
        <AngleToBlendInExtraPitch x="30.000000" y="60.000000" />
      </DataLeft>
      <DataRight>
        <Offsets>
          <Item>
            <Offset value="-0.300000" />
            <AngleToBlendInOffset x="100.000000" y="180.000000" />
          </Item>
          <Item>
            <Offset value="0.120000" />
            <AngleToBlendInOffset x="0.000000" y="180.000000" />
          </Item>
          <Item>
            <Offset value="0.100000" />
            <AngleToBlendInOffset x="45.000000" y="180.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-7.000000" y="-16.000000" />
        <AngleToBlendInExtraPitch x="0.000000" y="70.000000" />
      </DataRight>
      <WheelClipInfo>
        <HeadingLimitsLeft x="0.380000" y="0.430000" />
        <HeadingLimitsRight x="0.480000" y="0.530000" />
        <PitchLimits x="0.470000" y="0.530000" />
        <PitchOffset x="0.000000" y="0.020000" />
        <WheelAngleLimits x="-0.700000" y="0.000000" />
        <WheelAngleOffset x="0.010000" y="0.020000" />
        <MaxWheelOffsetY value="0.170000" />
        <WheelClipLerpInRate value="0.150000" />
        <WheelClipLerpOutRate value="0.180000" />
      </WheelClipInfo>
    </Item>
    <Item type="CFirstPersonDriveByLookAroundData">
      <Name>RANGER_TERMINUS_REAR_RIGHT</Name>
      <AllowLookback value="true" />
      <HeadingLimits x="-187.000000" y="160.000000" />
      <DataLeft>
        <Offsets>
          <Item>
            <Offset value="0.300000" />
            <AngleToBlendInOffset x="100.000000" y="180.000000" />
          </Item>
          <Item>
            <Offset value="0.120000" />
            <AngleToBlendInOffset x="0.000000" y="180.000000" />
          </Item>
          <Item>
            <Offset value="0.100000" />
            <AngleToBlendInOffset x="45.000000" y="180.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-7.000000" y="-16.000000" />
        <AngleToBlendInExtraPitch x="0.000000" y="70.000000" />
      </DataLeft>
      <DataRight>
        <Offsets>
          <Item>
            <Offset value="0.350000" />
            <AngleToBlendInOffset x="0.000000" y="160.000000" />
          </Item>
          <Item>
            <Offset value="0.240000" />
            <AngleToBlendInOffset x="30.000000" y="160.000000" />
          </Item>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="0.000000" y="0.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-15.000000" y="5.000000" />
        <AngleToBlendInExtraPitch x="30.000000" y="60.000000" />
      </DataRight>
      <WheelClipInfo>
        <HeadingLimitsLeft x="0.380000" y="0.430000" />
        <HeadingLimitsRight x="0.480000" y="0.530000" />
        <PitchLimits x="0.470000" y="0.530000" />
        <PitchOffset x="0.000000" y="0.020000" />
        <WheelAngleLimits x="-0.700000" y="0.000000" />
        <WheelAngleOffset x="0.010000" y="0.020000" />
        <MaxWheelOffsetY value="0.170000" />
        <WheelClipLerpInRate value="0.150000" />
        <WheelClipLerpOutRate value="0.180000" />
      </WheelClipInfo>
    </Item>
    <Item type="CFirstPersonDriveByLookAroundData">
      <Name>LOW_TURISMO3_FRONT_LEFT</Name>
      <AllowLookback value="false" />
      <HeadingLimits x="-95.000000" y="139.699997" />
      <DataLeft>
        <Offsets>
          <Item>
            <Offset value="-0.400000" />
            <AngleToBlendInOffset x="0.000000" y="70.000000" />
          </Item>
          <Item>
            <Offset value="0.100000" />
            <AngleToBlendInOffset x="0.000000" y="120.000000" />
          </Item>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="0.000000" y="0.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-5.000000" y="2.000000" />
        <AngleToBlendInExtraPitch x="50.000000" y="120.000000" />
      </DataLeft>
      <DataRight>
        <Offsets>
          <Item>
            <Offset value="-0.020000" />
            <AngleToBlendInOffset x="50.000000" y="80.000000" />
          </Item>
          <Item>
            <Offset value="0.050000" />
            <AngleToBlendInOffset x="0.000000" y="100.000000" />
          </Item>
          <Item>
            <Offset value="0.040000" />
            <AngleToBlendInOffset x="0.000000" y="140.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-3.000000" y="-17.000000" />
        <AngleToBlendInExtraPitch x="0.000000" y="120.000000" />
      </DataRight>
      <WheelClipInfo>
        <HeadingLimitsLeft x="0.340000" y="0.400000" />
        <HeadingLimitsRight x="0.450000" y="0.500000" />
        <PitchLimits x="0.470000" y="0.530000" />
        <PitchOffset x="0.000000" y="0.065000" />
        <WheelAngleLimits x="-0.700000" y="0.000000" />
        <WheelAngleOffset x="0.000000" y="0.020000" />
        <MaxWheelOffsetY value="0.170000" />
        <WheelClipLerpInRate value="0.150000" />
        <WheelClipLerpOutRate value="0.180000" />
      </WheelClipInfo>
    </Item>
    <Item type="CFirstPersonDriveByLookAroundData">
      <Name>LOW_TURISMO3_FRONT_RIGHT</Name>
      <AllowLookback value="false" />
      <HeadingLimits x="-95.000000" y="140.000000" />
      <DataLeft>
        <Offsets>
          <Item>
            <Offset value="-0.020000" />
            <AngleToBlendInOffset x="50.000000" y="80.000000" />
          </Item>
          <Item>
            <Offset value="0.050000" />
            <AngleToBlendInOffset x="0.000000" y="100.000000" />
          </Item>
          <Item>
            <Offset value="0.040000" />
            <AngleToBlendInOffset x="0.000000" y="140.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-3.000000" y="-17.000000" />
        <AngleToBlendInExtraPitch x="0.000000" y="120.000000" />
      </DataLeft>
      <DataRight>
        <Offsets>
          <Item>
            <Offset value="0.300000" />
            <AngleToBlendInOffset x="0.000000" y="70.000000" />
          </Item>
          <Item>
            <Offset value="0.100000" />
            <AngleToBlendInOffset x="0.000000" y="120.000000" />
          </Item>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="0.000000" y="0.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-5.000000" y="2.000000" />
        <AngleToBlendInExtraPitch x="50.000000" y="120.000000" />
      </DataRight>
      <WheelClipInfo>
        <HeadingLimitsLeft x="0.340000" y="0.400000" />
        <HeadingLimitsRight x="0.450000" y="0.500000" />
        <PitchLimits x="0.470000" y="0.530000" />
        <PitchOffset x="0.000000" y="0.065000" />
        <WheelAngleLimits x="-0.700000" y="0.000000" />
        <WheelAngleOffset x="0.000000" y="0.020000" />
        <MaxWheelOffsetY value="0.170000" />
        <WheelClipLerpInRate value="0.150000" />
        <WheelClipLerpOutRate value="0.180000" />
      </WheelClipInfo>
    </Item>
    <Item type="CFirstPersonDriveByLookAroundData">
      <Name>LOW_VIGERO3_FRONT_LEFT</Name>
      <AllowLookback value="true" />
      <HeadingLimits x="-190.000000" y="160.000000" />
      <DataLeft>
        <Offsets>
          <Item>
            <Offset value="-0.320000" />
            <AngleToBlendInOffset x="45.000000" y="120.000000" />
          </Item>
          <Item>
            <Offset value="0.050000" />
            <AngleToBlendInOffset x="45.000000" y="120.000000" />
          </Item>
          <Item>
            <Offset value="0.005000" />
            <AngleToBlendInOffset x="45.000000" y="120.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-10.000000" y="0.000000" />
        <AngleToBlendInExtraPitch x="45.000000" y="110.000000" />
      </DataLeft>
      <DataRight>
        <Offsets>
          <Item>
            <Offset value="-0.010000" />
            <AngleToBlendInOffset x="45.000000" y="90.000000" />
          </Item>
          <Item>
            <Offset value="-0.100000" />
            <AngleToBlendInOffset x="80.000000" y="165.000000" />
          </Item>
          <Item>
            <Offset value="0.075000" />
            <AngleToBlendInOffset x="120.000000" y="95.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-4.000000" y="-13.000000" />
        <AngleToBlendInExtraPitch x="0.000000" y="100.000000" />
      </DataRight>
      <WheelClipInfo>
        <HeadingLimitsLeft x="0.380000" y="0.430000" />
        <HeadingLimitsRight x="0.480000" y="0.530000" />
        <PitchLimits x="0.470000" y="0.530000" />
        <PitchOffset x="0.000000" y="0.050000" />
        <WheelAngleLimits x="-0.700000" y="0.000000" />
        <WheelAngleOffset x="0.010000" y="0.040000" />
        <MaxWheelOffsetY value="0.170000" />
        <WheelClipLerpInRate value="0.150000" />
        <WheelClipLerpOutRate value="0.180000" />
      </WheelClipInfo>
    </Item>
    <Item type="CFirstPersonDriveByLookAroundData">
      <Name>LOW_VIGERO3_FRONT_RIGHT</Name>
      <AllowLookback value="true" />
      <HeadingLimits x="-190.000000" y="160.000000" />
      <DataLeft>
        <Offsets>
          <Item>
            <Offset value="0.080000" />
            <AngleToBlendInOffset x="45.000000" y="90.000000" />
          </Item>
          <Item>
            <Offset value="-0.100000" />
            <AngleToBlendInOffset x="80.000000" y="165.000000" />
          </Item>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="0.000000" y="0.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="0.000000" y="-10.500000" />
        <AngleToBlendInExtraPitch x="0.000000" y="57.000000" />
      </DataLeft>
      <DataRight>
        <Offsets>
          <Item>
            <Offset value="0.390000" />
            <AngleToBlendInOffset x="45.000000" y="90.000000" />
          </Item>
          <Item>
            <Offset value="-0.220000" />
            <AngleToBlendInOffset x="45.000000" y="90.000000" />
          </Item>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="45.000000" y="120.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-10.000000" y="0.000000" />
        <AngleToBlendInExtraPitch x="45.000000" y="110.000000" />
      </DataRight>
      <WheelClipInfo>
        <HeadingLimitsLeft x="0.380000" y="0.430000" />
        <HeadingLimitsRight x="0.480000" y="0.530000" />
        <PitchLimits x="0.470000" y="0.530000" />
        <PitchOffset x="0.000000" y="0.050000" />
        <WheelAngleLimits x="-0.700000" y="0.000000" />
        <WheelAngleOffset x="0.010000" y="0.040000" />
        <MaxWheelOffsetY value="0.170000" />
        <WheelClipLerpInRate value="0.150000" />
        <WheelClipLerpOutRate value="0.180000" />
      </WheelClipInfo>
    </Item>
    <Item type="CFirstPersonDriveByLookAroundData">
      <Name>STD_VIVANITE_FRONT_LEFT</Name>
      <AllowLookback value="true" />
      <HeadingLimits x="-182.500000" y="170.000000" />
      <DataLeft>
        <Offsets>
          <Item>
            <Offset value="-0.450000" />
            <AngleToBlendInOffset x="40.000000" y="120.000000" />
          </Item>
          <Item>
            <Offset value="0.150000" />
            <AngleToBlendInOffset x="50.000000" y="120.000000" />
          </Item>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="0.000000" y="0.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-17.000000" y="10.000000" />
        <AngleToBlendInExtraPitch x="5.000000" y="110.000000" />
      </DataLeft>
      <DataRight>
        <Offsets>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="0.000000" y="0.000000" />
          </Item>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="0.000000" y="0.000000" />
          </Item>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="0.000000" y="0.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-0.500000" y="-12.100000" />
        <AngleToBlendInExtraPitch x="0.000000" y="70.000000" />
      </DataRight>
      <WheelClipInfo>
        <HeadingLimitsLeft x="0.380000" y="0.430000" />
        <HeadingLimitsRight x="0.480000" y="0.530000" />
        <PitchLimits x="0.470000" y="0.530000" />
        <PitchOffset x="0.000000" y="0.010000" />
        <WheelAngleLimits x="-0.700000" y="0.000000" />
        <WheelAngleOffset x="0.010000" y="0.020000" />
        <MaxWheelOffsetY value="0.170000" />
        <WheelClipLerpInRate value="0.150000" />
        <WheelClipLerpOutRate value="0.180000" />
      </WheelClipInfo>
    </Item>
    <Item type="CFirstPersonDriveByLookAroundData">
      <Name>STD_VIVANITE_FRONT_RIGHT</Name>
      <AllowLookback value="true" />
      <HeadingLimits x="-182.500000" y="160.000000" />
      <DataLeft>
        <Offsets>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="0.000000" y="0.000000" />
          </Item>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="0.000000" y="0.000000" />
          </Item>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="0.000000" y="0.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-0.500000" y="-12.100000" />
        <AngleToBlendInExtraPitch x="0.000000" y="70.000000" />
      </DataLeft>
      <DataRight>
        <Offsets>
          <Item>
            <Offset value="0.400000" />
            <AngleToBlendInOffset x="60.000000" y="120.000000" />
          </Item>
          <Item>
            <Offset value="0.150000" />
            <AngleToBlendInOffset x="60.000000" y="120.000000" />
          </Item>
          <Item>
            <Offset value="0.030000" />
            <AngleToBlendInOffset x="5.000000" y="60.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-20.000000" y="5.000000" />
        <AngleToBlendInExtraPitch x="5.000000" y="90.000000" />
      </DataRight>
      <WheelClipInfo>
        <HeadingLimitsLeft x="0.380000" y="0.430000" />
        <HeadingLimitsRight x="0.480000" y="0.530000" />
        <PitchLimits x="0.470000" y="0.530000" />
        <PitchOffset x="0.000000" y="0.050000" />
        <WheelAngleLimits x="-0.700000" y="0.000000" />
        <WheelAngleOffset x="0.010000" y="0.040000" />
        <MaxWheelOffsetY value="0.170000" />
        <WheelClipLerpInRate value="0.150000" />
        <WheelClipLerpOutRate value="0.180000" />
      </WheelClipInfo>
    </Item>
    <Item type="CFirstPersonDriveByLookAroundData">
      <Name>STD_VIVANITE_REAR_LEFT</Name>
      <AllowLookback value="true" />
      <HeadingLimits x="-180.000000" y="170.000000" />
      <DataLeft>
        <Offsets>
          <Item>
            <Offset value="-0.450000" />
            <AngleToBlendInOffset x="0.000000" y="170.000000" />
          </Item>
          <Item>
            <Offset value="0.150000" />
            <AngleToBlendInOffset x="0.000000" y="170.000000" />
          </Item>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="0.000000" y="120.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-15.000000" y="10.000000" />
        <AngleToBlendInExtraPitch x="0.000000" y="170.000000" />
      </DataLeft>
      <DataRight>
        <Offsets>
          <Item>
            <Offset value="-0.100000" />
            <AngleToBlendInOffset x="0.000000" y="90.000000" />
          </Item>
          <Item>
            <Offset value="0.050000" />
            <AngleToBlendInOffset x="0.000000" y="90.000000" />
          </Item>
          <Item>
            <Offset value="0.075000" />
            <AngleToBlendInOffset x="0.000000" y="100.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-6.000000" y="-14.500000" />
        <AngleToBlendInExtraPitch x="0.000000" y="180.000000" />
      </DataRight>
      <WheelClipInfo>
        <HeadingLimitsLeft x="0.380000" y="0.430000" />
        <HeadingLimitsRight x="0.480000" y="0.530000" />
        <PitchLimits x="0.470000" y="0.530000" />
        <PitchOffset x="0.000000" y="0.050000" />
        <WheelAngleLimits x="-0.700000" y="0.000000" />
        <WheelAngleOffset x="0.010000" y="0.040000" />
        <MaxWheelOffsetY value="0.170000" />
        <WheelClipLerpInRate value="0.150000" />
        <WheelClipLerpOutRate value="0.180000" />
      </WheelClipInfo>
    </Item>
    <Item type="CFirstPersonDriveByLookAroundData">
      <Name>STD_VIVANITE_REAR_RIGHT</Name>
      <AllowLookback value="true" />
      <HeadingLimits x="-180.000000" y="170.000000" />
      <DataLeft>
        <Offsets>
          <Item>
            <Offset value="0.100000" />
            <AngleToBlendInOffset x="0.000000" y="90.000000" />
          </Item>
          <Item>
            <Offset value="0.050000" />
            <AngleToBlendInOffset x="0.000000" y="90.000000" />
          </Item>
          <Item>
            <Offset value="0.075000" />
            <AngleToBlendInOffset x="0.000000" y="100.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-6.000000" y="-14.500000" />
        <AngleToBlendInExtraPitch x="0.000000" y="180.000000" />
      </DataLeft>
      <DataRight>
        <Offsets>
          <Item>
            <Offset value="0.400000" />
            <AngleToBlendInOffset x="0.000000" y="170.000000" />
          </Item>
          <Item>
            <Offset value="0.150000" />
            <AngleToBlendInOffset x="0.000000" y="170.000000" />
          </Item>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="0.000000" y="120.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-15.000000" y="10.000000" />
        <AngleToBlendInExtraPitch x="0.000000" y="170.000000" />
      </DataRight>
      <WheelClipInfo>
        <HeadingLimitsLeft x="0.380000" y="0.430000" />
        <HeadingLimitsRight x="0.480000" y="0.530000" />
        <PitchLimits x="0.470000" y="0.530000" />
        <PitchOffset x="0.000000" y="0.050000" />
        <WheelAngleLimits x="-0.700000" y="0.000000" />
        <WheelAngleOffset x="0.010000" y="0.040000" />
        <MaxWheelOffsetY value="0.170000" />
        <WheelClipLerpInRate value="0.150000" />
        <WheelClipLerpOutRate value="0.180000" />
      </WheelClipInfo>
    </Item>
 </FirstPersonDriveByLookAroundData>
</CVehicleMetadataMgr>