<?xml version="1.0" encoding="UTF-8"?>
<CVehicleModelInfoVarGlobal>    
  <Kits>
   <Item>
      <kitName>8365_rmod_gauntlet4_modkit</kitName>
      <id value="8365" />	 	  
      <kitType>MKT_SPECIAL</kitType>
      <visibleMods>
      <Item>
          <modelName>gauntlet4_hood_a</modelName>
          <modShopLabel>GAUNT4_HOODA</modShopLabel>
          <linkedModels />          
		  <turnOffBones>
         	<Item>bonnet</Item>
         	<Item>misc_g</Item>
		  </turnOffBones> 
          <type>VMT_BONNET</type>
          <bone>bonnet</bone>
          <collisionBone>bonnet</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
       <Item>
          <modelName>gauntlet4_hood_b</modelName>
          <modShopLabel>GAUNT4_HOODB</modShopLabel>
          <linkedModels>
			<Item>gauntlet4_shaker_a</Item>
          </linkedModels>
		  <turnOffBones>
         	<Item>bonnet</Item>
         	<Item>misc_g</Item>
		  </turnOffBones> 
          <type>VMT_BONNET</type>
          <bone>bonnet</bone>
          <collisionBone>bonnet</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
       <Item>
          <modelName>gauntlet4_hood_c</modelName>
          <modShopLabel>GAUNT4_HOODC</modShopLabel>
          <linkedModels>
			<Item>gauntlet4_shaker_b</Item>
          </linkedModels>
		  <turnOffBones>
         	<Item>bonnet</Item>
         	<Item>misc_g</Item>
		  </turnOffBones> 
          <type>VMT_BONNET</type>
          <bone>bonnet</bone>
          <collisionBone>bonnet</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
       <Item>
          <modelName>gauntlet4_hood_d</modelName>
          <modShopLabel>GAUNT4_HOODD</modShopLabel>
          <linkedModels />          
		  <turnOffBones>
         	<Item>bonnet</Item>
		  </turnOffBones> 
          <type>VMT_BONNET</type>
          <bone>bonnet</bone>
          <collisionBone>bonnet</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
       <Item>
          <modelName>gauntlet4_hood_e</modelName>
          <modShopLabel>GAUNT4_HOODE</modShopLabel>
          <linkedModels>
			<Item>gauntlet4_shaker_a</Item>
          </linkedModels>
		  <turnOffBones>
         	<Item>bonnet</Item>
         	<Item>misc_g</Item>
		  </turnOffBones> 
          <type>VMT_BONNET</type>
          <bone>bonnet</bone>
          <collisionBone>bonnet</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
       <Item>
          <modelName>gauntlet4_hood_f</modelName>
          <modShopLabel>GAUNT4_HOODF</modShopLabel>
          <linkedModels>
			<Item>gauntlet4_shaker_b</Item>
          </linkedModels>
		  <turnOffBones>
         	<Item>bonnet</Item>
         	<Item>misc_g</Item>
		  </turnOffBones> 
          <type>VMT_BONNET</type>
          <bone>bonnet</bone>
          <collisionBone>bonnet</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
       <Item>
          <modelName>gauntlet4_blower_a</modelName>
          <modShopLabel>GAUNT4_HOODG</modShopLabel>
          <linkedModels>
			<Item>gauntlet4_hood_g</Item>
          </linkedModels>
		  <turnOffBones>
         	<Item>bonnet</Item>
         	<Item>misc_g</Item>
			<Item>misc_k</Item>
			<Item>misc_l</Item>
		  </turnOffBones> 
          <type>VMT_BONNET</type>
          <bone>misc_a</bone>
          <collisionBone>mod_col_2</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="false" />
        </Item>
       <Item>
          <modelName>gauntlet4_blower_b</modelName>
          <modShopLabel>GAUNT4_HOODH</modShopLabel>
          <linkedModels>
			<Item>gauntlet4_hood_g</Item>
          </linkedModels>
		  <turnOffBones>
         	<Item>bonnet</Item>
         	<Item>misc_g</Item>
			<Item>misc_k</Item>
			<Item>misc_l</Item>
		  </turnOffBones> 
          <type>VMT_BONNET</type>
          <bone>misc_a</bone>
          <collisionBone>mod_col_2</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="false" />
        </Item>
       <Item>
          <modelName>gauntlet4_blower_c</modelName>
          <modShopLabel>GAUNT4_HOODJ</modShopLabel>
          <linkedModels>
			<Item>gauntlet4_hood_g</Item>
          </linkedModels>
		  <turnOffBones>
         	<Item>bonnet</Item>
         	<Item>misc_g</Item>
			<Item>misc_k</Item>
			<Item>misc_l</Item>
		  </turnOffBones> 
          <type>VMT_BONNET</type>
          <bone>misc_a</bone>
          <collisionBone>mod_col_2</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="false" />
        </Item>
       <Item>
          <modelName>gauntlet4_blower_d</modelName>
          <modShopLabel>GAUNT4_HOODK</modShopLabel>
          <linkedModels>
			<Item>gauntlet4_hood_g</Item>
			<Item>gauntlet4_butterfly_a</Item>
          </linkedModels>
		  <turnOffBones>
         	<Item>bonnet</Item>
         	<Item>misc_g</Item>
			<Item>misc_k</Item>
			<Item>misc_l</Item>
		  </turnOffBones> 
          <type>VMT_BONNET</type>
          <bone>misc_a</bone>
          <collisionBone>mod_col_2</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="false" />
        </Item>
       <Item>
          <modelName>gauntlet4_wing_a</modelName>
          <modShopLabel>GAUNT4_WINGA</modShopLabel>
          <linkedModels />
		  <turnOffBones>
         	<Item>misc_d</Item>
		  </turnOffBones> 
          <type>VMT_SPOILER</type>
          <bone>boot</bone>
          <collisionBone>boot</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
       <Item>
          <modelName>gauntlet4_wing_b</modelName>
          <modShopLabel>GAUNT4_WINGB</modShopLabel>
          <linkedModels />
		  <turnOffBones>
         	<Item>misc_d</Item>
		  </turnOffBones> 
          <type>VMT_SPOILER</type>
          <bone>boot</bone>
          <collisionBone>boot</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
       <Item>
          <modelName>gauntlet4_wing_c</modelName>
          <modShopLabel>GAUNT4_WINGC</modShopLabel>
          <linkedModels />
		  <turnOffBones>
         	<Item>misc_d</Item>
		  </turnOffBones> 
          <type>VMT_SPOILER</type>
          <bone>boot</bone>
          <collisionBone>boot</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
       <Item>
          <modelName>gauntlet4_wing_d</modelName>
          <modShopLabel>GAUNT4_WINGD</modShopLabel>
          <linkedModels />
		  <turnOffBones>
         	<Item>misc_d</Item>
		  </turnOffBones> 
          <type>VMT_SPOILER</type>
          <bone>boot</bone>
          <collisionBone>boot</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
       <Item>
          <modelName>gauntlet4_wing_e</modelName>
          <modShopLabel>GAUNT4_WINGE</modShopLabel>
          <linkedModels />
		  <turnOffBones>
         	<Item>misc_d</Item>
		  </turnOffBones> 
          <type>VMT_SPOILER</type>
          <bone>boot</bone>
          <collisionBone>boot</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
       <Item>
          <modelName>gauntlet4_wing_f</modelName>
          <modShopLabel>GAUNT4_WINGF</modShopLabel>
          <linkedModels />
		  <turnOffBones>
         	<Item>misc_d</Item>
		  </turnOffBones> 
          <type>VMT_SPOILER</type>
          <bone>boot</bone>
          <collisionBone>mod_col_5</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
       <Item>
          <modelName>gauntlet4_wing_g</modelName>
          <modShopLabel>GAUNT4_WINGG</modShopLabel>
          <linkedModels />
		  <turnOffBones>
         	<Item>misc_d</Item>
		  </turnOffBones> 
          <type>VMT_SPOILER</type>
          <bone>boot</bone>
          <collisionBone>mod_col_4</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
       <Item>
          <modelName>gauntlet4_wing_h</modelName>
          <modShopLabel>GAUNT4_WINGH</modShopLabel>
          <linkedModels />
		  <turnOffBones>
         	<Item>misc_d</Item>
		  </turnOffBones> 
          <type>VMT_SPOILER</type>
          <bone>boot</bone>
          <collisionBone>mod_col_3</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
       <Item>
          <modelName>gauntlet4_wing_i</modelName>
          <modShopLabel>GAUNT4_WINGI</modShopLabel>
          <linkedModels />
		  <turnOffBones>
         	<Item>misc_d</Item>
		  </turnOffBones> 
          <type>VMT_SPOILER</type>
          <bone>boot</bone>
          <collisionBone>mod_col_4</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
       <Item>
          <modelName>gauntlet4_wing_j</modelName>
          <modShopLabel>GAUNT4_WINGJ</modShopLabel>
          <linkedModels />
		  <turnOffBones>
         	<Item>misc_d</Item>
		  </turnOffBones> 
          <type>VMT_SPOILER</type>
          <bone>boot</bone>
          <collisionBone>mod_col_4</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
       <Item>
          <modelName>gauntlet4_roof_a</modelName>
          <modShopLabel>GAUNT4_ROOF</modShopLabel>
          <linkedModels />
		  <turnOffBones>
         	<Item>misc_i</Item>
		  </turnOffBones> 
          <type>VMT_ROOF</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
       <Item>
          <modelName>gauntlet4_skirt_1a</modelName>
          <modShopLabel>GAUNT4_SKIRT1</modShopLabel>
          <linkedModels>
			<Item>gauntlet4_skirt_1b</Item>
			<Item>gauntlet4_skirt_1c</Item>
          </linkedModels>
		  <turnOffBones>
         	<Item>misc_o</Item>
         	<Item>misc_p</Item>
         	<Item>misc_q</Item>
		  </turnOffBones> 
          <type>VMT_SKIRT</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
       <Item>
          <modelName>gauntlet4_skirt_2a</modelName>
          <modShopLabel>GAUNT4_SKIRT2</modShopLabel>
          <linkedModels>
			<Item>gauntlet4_skirt_2b</Item>
			<Item>gauntlet4_skirt_2c</Item>
          </linkedModels>
		  <turnOffBones>
         	<Item>misc_o</Item>
         	<Item>misc_p</Item>
         	<Item>misc_q</Item>
		  </turnOffBones> 
          <type>VMT_SKIRT</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
       <Item>
          <modelName>gauntlet4_null</modelName>
          <modShopLabel>GAUNT4_GRILL0</modShopLabel>
          <linkedModels />
		  <turnOffBones>
         	<Item>misc_f</Item>
		  </turnOffBones> 
          <type>VMT_GRILL</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
       <Item>
          <modelName>gauntlet4_grill_a</modelName>
          <modShopLabel>GAUNT4_GRILLA</modShopLabel>
          <linkedModels />
		  <turnOffBones>
         	<Item>misc_f</Item>
		  </turnOffBones> 
          <type>VMT_GRILL</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
       <Item>
          <modelName>gauntlet4_grill_b</modelName>
          <modShopLabel>GAUNT4_GRILLB</modShopLabel>
          <linkedModels />
		  <turnOffBones>
         	<Item>misc_f</Item>
		  </turnOffBones> 
          <type>VMT_GRILL</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
       <Item>
          <modelName>gauntlet4_grill_c</modelName>
          <modShopLabel>GAUNT4_GRILLC</modShopLabel>
          <linkedModels />
		  <turnOffBones>
         	<Item>misc_f</Item>
		  </turnOffBones> 
          <type>VMT_GRILL</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
       <Item>
          <modelName>gauntlet4_grill_d</modelName>
          <modShopLabel>GAUNT4_GRILLD</modShopLabel>
          <linkedModels />
		  <turnOffBones>
         	<Item>misc_f</Item>
		  </turnOffBones> 
          <type>VMT_GRILL</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
       <Item>
          <modelName>gauntlet4_grill_e</modelName>
          <modShopLabel>GAUNT4_GRILLE</modShopLabel>
          <linkedModels />
		  <turnOffBones>
         	<Item>misc_f</Item>
		  </turnOffBones> 
          <type>VMT_GRILL</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
       <Item>
          <modelName>gauntlet4_lightcover_a</modelName>
          <modShopLabel>GAUNT4_COVA</modShopLabel>
          <linkedModels />
		  <turnOffBones />
          <type>VMT_WING_R</type>
          <bone>bumper_f</bone>
          <collisionBone>bumper_f</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
       <Item>
          <modelName>gauntlet4_lightcover_b</modelName>
          <modShopLabel>GAUNT4_COVB</modShopLabel>
          <linkedModels />
		  <turnOffBones />
          <type>VMT_WING_R</type>
          <bone>bumper_f</bone>
          <collisionBone>bumper_f</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
       <Item>
          <modelName>gauntlet4_lightcover_d</modelName>
          <modShopLabel>GAUNT4_COVD</modShopLabel>
          <linkedModels />
		  <turnOffBones>
         	<Item>misc_b</Item>
         	<Item>headlight_l</Item>
         	<Item>headlight_r</Item>
		  </turnOffBones> 
          <type>VMT_WING_R</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
       <Item>
          <modelName>gauntlet4_lightcover_e</modelName>
          <modShopLabel>GAUNT4_COVE</modShopLabel>
          <linkedModels />
		  <turnOffBones>
         	<Item>headlight_r</Item>
		  </turnOffBones> 
          <type>VMT_WING_R</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
       <Item>
          <modelName>gauntlet4_lightcover_f</modelName>
          <modShopLabel>GAUNT4_COVF</modShopLabel>
          <linkedModels />
		  <turnOffBones>
         	<Item>misc_b</Item>
         	<Item>headlight_l</Item>
         	<Item>headlight_r</Item>
		  </turnOffBones> 
          <type>VMT_WING_R</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
       <Item>
          <modelName>gauntlet4_lightcover_g</modelName>
          <modShopLabel>GAUNT4_COVG</modShopLabel>
          <linkedModels />
		  <turnOffBones>
         	<Item>headlight_l</Item>
         	<Item>headlight_r</Item>
		  </turnOffBones> 
          <type>VMT_WING_R</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
       <Item>
          <modelName>gauntlet4_lightcover_c</modelName>
          <modShopLabel>GAUNT4_COVC</modShopLabel>
          <linkedModels />
		  <turnOffBones>
         	<Item>headlight_l</Item>
         	<Item>headlight_r</Item>
		  </turnOffBones> 
          <type>VMT_WING_R</type>
          <bone>bumper_f</bone>
          <collisionBone>bumper_f</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
       <Item>
          <modelName>gauntlet4_lightcover_h</modelName>
          <modShopLabel>GAUNT4_COVH</modShopLabel>
          <linkedModels />
		  <turnOffBones>
         	<Item>headlight_l</Item>
         	<Item>headlight_r</Item>
		  </turnOffBones> 
          <type>VMT_WING_R</type>
          <bone>bumper_f</bone>
          <collisionBone>bumper_f</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
       <Item>
          <modelName>gauntlet4_bullbar_a</modelName>
          <modShopLabel>GAUNT4_BBARA</modShopLabel>
          <linkedModels />
		  <turnOffBones />
          <type>VMT_WING_R</type>
          <bone>bumper_f</bone>
          <collisionBone>bumper_f</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
       <Item>
          <modelName>gauntlet4_cage_a</modelName>
          <modShopLabel>GAUNT4_CAGEA</modShopLabel>
          <linkedModels />
		  <turnOffBones />
          <type>VMT_CHASSIS</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
       <Item>
          <modelName>gauntlet4_cage_b</modelName>
          <modShopLabel>GAUNT4_CAGEB</modShopLabel>
          <linkedModels />
		  <turnOffBones>
         	<Item>misc_r</Item>
		  </turnOffBones> 
          <type>VMT_CHASSIS</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
       <Item>
          <modelName>gauntlet4_cage_c</modelName>
          <modShopLabel>GAUNT4_CAGEC</modShopLabel>
          <linkedModels />
		  <turnOffBones>
         	<Item>misc_r</Item>
		  </turnOffBones> 
          <type>VMT_CHASSIS</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
       <Item>
          <modelName>gauntlet4_cage_d</modelName>
          <modShopLabel>GAUNT4_CAGED</modShopLabel>
          <linkedModels />
		  <turnOffBones>
         	<Item>misc_r</Item>
		  </turnOffBones> 
          <type>VMT_CHASSIS</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
       <Item>
          <modelName>gauntlet4_cage_e</modelName>
          <modShopLabel>GAUNT4_CAGEE</modShopLabel>
          <linkedModels />
		  <turnOffBones>
         	<Item>misc_r</Item>
		  </turnOffBones> 
          <type>VMT_CHASSIS</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
       <Item>
          <modelName>gauntlet4_cage_f</modelName>
          <modShopLabel>GAUNT4_CAGEF</modShopLabel>
          <linkedModels />
		  <turnOffBones>
         	<Item>misc_r</Item>
		  </turnOffBones> 
          <type>VMT_CHASSIS</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
       <Item>
          <modelName>gauntlet4_rpanel_a</modelName>
          <modShopLabel>GAUNT4_RPANA</modShopLabel>
          <linkedModels>
			<Item>gauntlet4_rtrim</Item>
          </linkedModels>
		  <turnOffBones>
         	<Item>misc_t</Item>
         	<Item>misc_e</Item>
		  </turnOffBones> 
          <type>VMT_WING_L</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
       <Item>
          <modelName>gauntlet4_rpanel_b</modelName>
          <modShopLabel>GAUNT4_RPANB</modShopLabel>
          <linkedModels>
			<Item>gauntlet4_rtrim2</Item>
          </linkedModels>
		  <turnOffBones>
         	<Item>misc_t</Item>
         	<Item>misc_e</Item>
		  </turnOffBones> 
          <type>VMT_WING_L</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
       <Item>
          <modelName>gauntlet4_rpanel_c</modelName>
          <modShopLabel>GAUNT4_RPANC</modShopLabel>
          <linkedModels>
			<Item>gauntlet4_rtrim3</Item>
          </linkedModels>
		  <turnOffBones>
         	<Item>misc_t</Item>
         	<Item>misc_e</Item>
		  </turnOffBones> 
          <type>VMT_WING_L</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
       <Item>
          <modelName>gauntlet4_rpanel_d</modelName>
          <modShopLabel>GAUNT4_RPAND</modShopLabel>
          <linkedModels />
		  <turnOffBones>
         	<Item>misc_e</Item>
         	<Item>taillight_l</Item>
         	<Item>taillight_r</Item>
         	<Item>taillight_l</Item>
         	<Item>inidcator_lr</Item>
         	<Item>inidcator_rr</Item>
         	<Item>reversinglight_l</Item>
         	<Item>reversinglight_r</Item>
		  </turnOffBones> 
          <type>VMT_WING_L</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
       <Item>
          <modelName>gauntlet4_rpanel_e</modelName>
          <modShopLabel>GAUNT4_RPANE</modShopLabel>
          <linkedModels>
			<Item>gauntlet4_rtrim</Item>
          </linkedModels>
		  <turnOffBones>
         	<Item>misc_t</Item>
         	<Item>misc_e</Item>
         	<Item>taillight_l</Item>
         	<Item>taillight_r</Item>
         	<Item>taillight_l</Item>
         	<Item>inidcator_lr</Item>
         	<Item>inidcator_rr</Item>
         	<Item>reversinglight_l</Item>
         	<Item>reversinglight_r</Item>
		  </turnOffBones> 
          <type>VMT_WING_L</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
       <Item>
          <modelName>gauntlet4_rpanel_f</modelName>
          <modShopLabel>GAUNT4_RPANF</modShopLabel>
          <linkedModels>
			<Item>gauntlet4_rtrim2</Item>
          </linkedModels>
		  <turnOffBones>
         	<Item>misc_t</Item>
         	<Item>misc_e</Item>
         	<Item>taillight_l</Item>
         	<Item>taillight_r</Item>
         	<Item>taillight_l</Item>
         	<Item>inidcator_lr</Item>
         	<Item>inidcator_rr</Item>
         	<Item>reversinglight_l</Item>
         	<Item>reversinglight_r</Item>
		  </turnOffBones> 
          <type>VMT_WING_L</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
       <Item>
          <modelName>gauntlet4_bumr_a</modelName>
          <modShopLabel>GAUNT4_BUMRA</modShopLabel>
          <linkedModels />
		  <turnOffBones />
          <type>VMT_BUMPER_R</type>
          <bone>bumper_r</bone>
          <collisionBone>mod_col_1</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
       <Item>
          <modelName>gauntlet4_split_a</modelName>
          <modShopLabel>GAUNT4_SPLITA</modShopLabel>
          <linkedModels />
		  <turnOffBones>
         	<Item>misc_h</Item>
		  </turnOffBones> 
          <type>VMT_BUMPER_F</type>
          <bone>bumper_f</bone>
          <collisionBone>bumper_f</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
       <Item>
          <modelName>gauntlet4_split_b</modelName>
          <modShopLabel>GAUNT4_SPLITB</modShopLabel>
          <linkedModels />
		  <turnOffBones>
         	<Item>misc_h</Item>
		  </turnOffBones> 
          <type>VMT_BUMPER_F</type>
          <bone>bumper_f</bone>
          <collisionBone>bumper_f</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
       <Item>
          <modelName>gauntlet4_split_c</modelName>
          <modShopLabel>GAUNT4_SPLITC</modShopLabel>
          <linkedModels />
		  <turnOffBones>
         	<Item>misc_h</Item>
		  </turnOffBones> 
          <type>VMT_BUMPER_F</type>
          <bone>bumper_f</bone>
          <collisionBone>bumper_f</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
       <Item>
          <modelName>gauntlet4_bumf_a</modelName>
          <modShopLabel>GAUNT4_BUMFA</modShopLabel>
          <linkedModels />
		  <turnOffBones>
         	<Item>misc_s</Item>
		  </turnOffBones> 
          <type>VMT_BUMPER_F</type>
          <bone>bumper_f</bone>
          <collisionBone>bumper_f</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
       <Item>
          <modelName>gauntlet4_bumf_a</modelName>
          <modShopLabel>GAUNT4_BUMFC</modShopLabel>
          <linkedModels>
			<Item>gauntlet4_split_b</Item>
          </linkedModels>
		  <turnOffBones>
         	<Item>misc_s</Item>
         	<Item>misc_h</Item>
		  </turnOffBones> 
          <type>VMT_BUMPER_F</type>
          <bone>bumper_f</bone>
          <collisionBone>bumper_f</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
       <Item>
          <modelName>gauntlet4_bumf_a</modelName>
          <modShopLabel>GAUNT4_BUMFD</modShopLabel>
          <linkedModels>
			<Item>gauntlet4_split_c</Item>
          </linkedModels>
		  <turnOffBones>
         	<Item>misc_s</Item>
         	<Item>misc_h</Item>
		  </turnOffBones> 
          <type>VMT_BUMPER_F</type>
          <bone>bumper_f</bone>
          <collisionBone>bumper_f</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
       <Item>
          <modelName>gauntlet4_exh_a</modelName>
          <modShopLabel>GAUNT4_EHXA</modShopLabel>
          <linkedModels />
		  <turnOffBones>
         	<Item>misc_c</Item>
		  </turnOffBones> 
          <type>VMT_EXHAUST</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
       <Item>
          <modelName>gauntlet4_exh_b</modelName>
          <modShopLabel>GAUNT4_EHXB</modShopLabel>
          <linkedModels />
		  <turnOffBones>
         	<Item>misc_c</Item>
		  </turnOffBones> 
          <type>VMT_EXHAUST</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
       <Item>
          <modelName>gauntlet4_exh_c</modelName>
          <modShopLabel>GAUNT4_EHXC</modShopLabel>
          <linkedModels />
		  <turnOffBones>
         	<Item>misc_c</Item>
		  </turnOffBones> 
          <type>VMT_EXHAUST</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
       <Item>
          <modelName>gauntlet4_exh_d</modelName>
          <modShopLabel>GAUNT4_EHXD</modShopLabel>
          <linkedModels />
		  <turnOffBones>
         	<Item>misc_c</Item>
         	<Item>exhaust</Item>
         	<Item>exhaust_2</Item>
		  </turnOffBones> 
          <type>VMT_EXHAUST</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
       <Item>
          <modelName>gauntlet4_exh_e</modelName>
          <modShopLabel>GAUNT4_EHXE</modShopLabel>
          <linkedModels />
		  <turnOffBones>
         	<Item>misc_c</Item>
         	<Item>exhaust</Item>
         	<Item>exhaust_2</Item>
		  </turnOffBones> 
          <type>VMT_EXHAUST</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
       <Item>
          <modelName>gauntlet4_livery1</modelName>
          <modShopLabel>GAUNT4_LIV1</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_LIVERY_MOD</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
       <Item>
          <modelName>gauntlet4_livery2</modelName>
          <modShopLabel>GAUNT4_LIV2</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_LIVERY_MOD</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
       <Item>
          <modelName>gauntlet4_livery3</modelName>
          <modShopLabel>GAUNT4_LIV3</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_LIVERY_MOD</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
       <Item>
          <modelName>gauntlet4_livery4</modelName>
          <modShopLabel>GAUNT4_LIV4</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_LIVERY_MOD</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
       <Item>
          <modelName>gauntlet4_livery5</modelName>
          <modShopLabel>GAUNT4_LIV5</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_LIVERY_MOD</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
       <Item>
          <modelName>gauntlet4_livery6</modelName>
          <modShopLabel>GAUNT4_LIV6</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_LIVERY_MOD</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
       <Item>
          <modelName>gauntlet4_livery7</modelName>
          <modShopLabel>GAUNT4_LIV7</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_LIVERY_MOD</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
       <Item>
          <modelName>gauntlet4_livery8</modelName>
          <modShopLabel>GAUNT4_LIV8</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_LIVERY_MOD</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
       <Item>
          <modelName>gauntlet4_livery9</modelName>
          <modShopLabel>GAUNT4_LIV9</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_LIVERY_MOD</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
       <Item>
          <modelName>gauntlet4_livery10</modelName>
          <modShopLabel>GAUNT4_LIV10</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_LIVERY_MOD</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
       <Item>
          <modelName>gauntlet4_livery11</modelName>
          <modShopLabel>GAUNT4_LIV11</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_LIVERY_MOD</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
       <Item>
          <modelName>gauntlet4_livery12</modelName>
          <modShopLabel>GAUNT4_LIV12</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_LIVERY_MOD</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
      </visibleMods>
	  <linkMods>
		<Item>
          <modelName>gauntlet4_hood_g</modelName>
          <bone>bonnet</bone>
          <turnOffExtra value="false" />
		</Item>
		<Item>
          <modelName>gauntlet4_shaker_a</modelName>
          <bone>misc_a</bone>
          <turnOffExtra value="false" />
		</Item>
		<Item>
          <modelName>gauntlet4_shaker_b</modelName>
          <bone>misc_a</bone>
          <turnOffExtra value="false" />
		</Item>
		<Item>
          <modelName>gauntlet4_butterfly_a</modelName>
          <bone>misc_m</bone>
          <turnOffExtra value="false" />
		</Item>
		<Item>
          <modelName>gauntlet4_skirt_1b</modelName>
          <bone>wing_lf</bone>
          <turnOffExtra value="false" />
		</Item>
		<Item>
          <modelName>gauntlet4_skirt_1c</modelName>
          <bone>wing_rf</bone>
          <turnOffExtra value="false" />
		</Item>
		<Item>
          <modelName>gauntlet4_skirt_2b</modelName>
          <bone>wing_lf</bone>
          <turnOffExtra value="false" />
		</Item>
		<Item>
          <modelName>gauntlet4_skirt_2c</modelName>
          <bone>wing_rf</bone>
          <turnOffExtra value="false" />
		</Item>
		<Item>
          <modelName>gauntlet4_split_a</modelName>
          <bone>bumper_f</bone>
          <turnOffExtra value="false" />
		</Item>
		<Item>
          <modelName>gauntlet4_split_b</modelName>
          <bone>bumper_f</bone>
          <turnOffExtra value="false" />
		</Item>
		<Item>
          <modelName>gauntlet4_split_c</modelName>
          <bone>bumper_f</bone>
          <turnOffExtra value="false" />
		</Item>
		<Item>
          <modelName>gauntlet4_rtrim</modelName>
          <bone>boot</bone>
          <turnOffExtra value="false" />
		</Item>
		<Item>
          <modelName>gauntlet4_rtrim2</modelName>
          <bone>boot</bone>
          <turnOffExtra value="false" />
		</Item>
		<Item>
          <modelName>gauntlet4_rtrim3</modelName>
          <bone>boot</bone>
          <turnOffExtra value="false" />
		</Item>
	  </linkMods>
      <statMods>
        <Item>
          <identifier />
          <modifier value="25" />
          <audioApply value="1.000000" />
          <weight value="20" />
          <type>VMT_ENGINE</type>
        </Item>
        <Item>
          <identifier />
          <modifier value="50" />
          <audioApply value="1.000000" />
          <weight value="20" />
          <type>VMT_ENGINE</type>
        </Item>
        <Item>
          <identifier />
          <modifier value="75" />
          <audioApply value="1.000000" />
          <weight value="20" />
          <type>VMT_ENGINE</type>
        </Item>
        <Item>
          <identifier />
          <modifier value="150" />
          <audioApply value="1.000000" />
          <weight value="20" />
          <type>VMT_ENGINE</type>
        </Item>
        <Item>
          <identifier />
          <modifier value="25" />
          <audioApply value="1.000000" />
          <weight value="5" />
          <type>VMT_BRAKES</type>
        </Item>
        <Item>
          <identifier />
          <modifier value="50" />
          <audioApply value="1.000000" />
          <weight value="5" />
          <type>VMT_BRAKES</type>
        </Item>
        <Item>
          <identifier />
          <modifier value="100" />
          <audioApply value="1.000000" />
          <weight value="5" />
          <type>VMT_BRAKES</type>
        </Item>
        <Item>
          <identifier />
          <modifier value="25" />
          <audioApply value="1.000000" />
          <weight value="5" />
          <type>VMT_GEARBOX</type>
        </Item>
        <Item>
          <identifier />
          <modifier value="50" />
          <audioApply value="1.000000" />
          <weight value="5" />
          <type>VMT_GEARBOX</type>
        </Item>
        <Item>
          <identifier />
          <modifier value="100" />
          <audioApply value="1.000000" />
          <weight value="5" />
          <type>VMT_GEARBOX</type>
        </Item>
        <Item>
          <identifier />
          <modifier value="20" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_ARMOUR</type>
        </Item>
        <Item>
          <identifier />
          <modifier value="40" />
          <audioApply value="1.000000" />
          <weight value="10" />
          <type>VMT_ARMOUR</type>
        </Item>
        <Item>
          <identifier />
          <modifier value="60" />
          <audioApply value="1.000000" />
          <weight value="20" />
          <type>VMT_ARMOUR</type>
        </Item>
        <Item>
          <identifier />
          <modifier value="80" />
          <audioApply value="1.000000" />
          <weight value="30" />
          <type>VMT_ARMOUR</type>
        </Item>
        <Item>
          <identifier />
          <modifier value="100" />
          <audioApply value="1.000000" />
          <weight value="40" />
          <type>VMT_ARMOUR</type>
        </Item>
        <Item>
          <identifier>HORN_TRUCK</identifier>
          <modifier value="1766676233" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>HORN_COP</identifier>
          <modifier value="2904189469" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>HORN_CLOWN</identifier>
          <modifier value="2543206147" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>HORN_MUSICAL_1</identifier>
          <modifier value="1732399718" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>HORN_MUSICAL_2</identifier>
          <modifier value="2046162893" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>HORN_MUSICAL_3</identifier>
          <modifier value="2194999691" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>HORN_MUSICAL_4</identifier>
          <modifier value="2508304100" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>HORN_MUSICAL_5</identifier>
          <modifier value="3707223535" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
		<Item>
          <identifier>HORN_SAD_TROMBONE</identifier>
          <modifier value="632950117" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>MUSICAL_HORN_BUSINESS_1</identifier>
          <modifier value="3628534289" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>MUSICAL_HORN_BUSINESS_2</identifier>
          <modifier value="3892554122" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>MUSICAL_HORN_BUSINESS_3</identifier>
          <modifier value="4112892878" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>MUSICAL_HORN_BUSINESS_4</identifier>
          <modifier value="116877169" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>MUSICAL_HORN_BUSINESS_5</identifier>
          <modifier value="2684983719" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>MUSICAL_HORN_BUSINESS_6</identifier>
          <modifier value="2982690084" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
		</Item>
		<Item>
          <identifier>MUSICAL_HORN_BUSINESS_7</identifier>
          <modifier value="3203290992" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
		</Item>
		<Item>
          <identifier>DLC_BUSI2_C_MAJOR_NOTES_C0</identifier>
          <modifier value="771284519" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
		</Item>
		<Item>
          <identifier>DLC_BUSI2_C_MAJOR_NOTES_D0</identifier>
          <modifier value="2586621229" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
		</Item>
		<Item>
          <identifier>DLC_BUSI2_C_MAJOR_NOTES_E0</identifier>
          <modifier value="283386134" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
		</Item>
		<Item>
          <identifier>DLC_BUSI2_C_MAJOR_NOTES_F0</identifier>
          <modifier value="3884502400" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
		</Item>
		<Item>
          <identifier>DLC_BUSI2_C_MAJOR_NOTES_G0</identifier>
          <modifier value="265723083" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
		</Item>
		<Item>
          <identifier>DLC_BUSI2_C_MAJOR_NOTES_A0</identifier>
          <modifier value="1746883687" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
		</Item>
		<Item>
          <identifier>DLC_BUSI2_C_MAJOR_NOTES_B0</identifier>
          <modifier value="1919870950" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
		</Item>
		<Item>
          <identifier>DLC_BUSI2_C_MAJOR_NOTES_C1</identifier>
          <modifier value="1085277077" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
		</Item>
        <Item>
          <identifier>HIPSTER_HORN_1</identifier>
          <modifier value="444549672" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>HIPSTER_HORN_2</identifier>
          <modifier value="1603064898" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>HIPSTER_HORN_3</identifier>
          <modifier value="240366033" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>HIPSTER_HORN_4</identifier>
          <modifier value="960137118" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>INDEP_HORN_1</identifier>
          <modifier value="3572144790" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>INDEP_HORN_2</identifier>
          <modifier value="3801396714" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>INDEP_HORN_3</identifier>
          <modifier value="2843657151" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>INDEP_HORN_4</identifier>
          <modifier value="3341811489" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
		<Item>
          <identifier>LUXE_HORN_1</identifier>
          <modifier value="3199657341" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
		<Item>
          <identifier>LUXE_HORN_2</identifier>
          <modifier value="2900378064" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
		<Item>
          <identifier>LUXE_HORN_3</identifier>
          <modifier value="3956195248" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
		<!-- PREVIEW HORN - FOR MOD SHOP USE ONLY -->
		<Item>
          <identifier>LUXORY_HORN_1</identifier>
          <modifier value="676333254" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
		<!-- PREVIEW HORN - FOR MOD SHOP USE ONLY -->
		<Item>
          <identifier>LUXURY_HORN_2</identifier>
          <modifier value="2099578296" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
		<!-- PREVIEW HORN - FOR MOD SHOP USE ONLY -->
		<Item>
          <identifier>LUXURY_HORN_3</identifier>
          <modifier value="1373384483" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
		<Item>
          <identifier>ORGAN_HORN_LOOP_01</identifier>
          <modifier value="2916775806" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
		<!-- PREVIEW HORN - FOR MOD SHOP USE ONLY -->
		<Item>
          <identifier>ORGAN_HORN_LOOP_01_PREVIEW</identifier>
          <modifier value="3714706952" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
		<Item>
          <identifier>ORGAN_HORN_LOOP_02</identifier>
          <modifier value="2611860261" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
		<!-- PREVIEW HORN - FOR MOD SHOP USE ONLY -->
		<Item>
          <identifier>ORGAN_HORN_LOOP_02_PREVIEW</identifier>
          <modifier value="3206770359" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
		<Item>
          <identifier>LOWRIDER_HORN_1</identifier>
          <modifier value="310529291" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
		<!-- PREVIEW HORN - FOR MOD SHOP USE ONLY -->
		<Item>
          <identifier>LOWRIDER_HORN_1_PREVIEW</identifier>
          <modifier value="2965568987" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
		<Item>
          <identifier>LOWRIDER_HORN_2</identifier>
          <modifier value="55291550" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
		<!-- PREVIEW HORN - FOR MOD SHOP USE ONLY -->
		<Item>
          <identifier>LOWRIDER_HORN_2_PREVIEW</identifier>
          <modifier value="965054819" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
		<Item>
          <identifier />
          <modifier value="2" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_SUSPENSION</type>
        </Item>
        <Item>
          <identifier />
          <modifier value="4" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_SUSPENSION</type>
        </Item>
        <Item>
          <identifier />
          <modifier value="6" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_SUSPENSION</type>
        </Item>
        <Item>
          <identifier />
          <modifier value="8" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_SUSPENSION</type>
        </Item>
      </statMods>
      <slotNames />
      <liveryNames>
      </liveryNames>
    </Item>
  </Kits>
      <Lights>
      <Item>
      <id value="8365" />
      <indicator>
        <intensity value="0.375000" />
        <falloffMax value="2.500000" />
        <falloffExponent value="8.000000" />
        <innerConeAngle value="20.000000" />
        <outerConeAngle value="50.000000" />
        <emmissiveBoost value="false" />
        <color value="0xFFFF7300" />
      </indicator>
      <rearIndicatorCorona>
        <size value="0.000000" />
        <size_far value="0.000000" />
        <intensity value="0.000000" />
        <intensity_far value="0.000000" />
        <color value="0x00000000" />
        <numCoronas value="0" />
        <distBetweenCoronas value="128" />
        <distBetweenCoronas_far value="255" />
        <xRotation value="0.000000" />
        <yRotation value="0.000000" />
        <zRotation value="0.000000" />
        <zBias value="0.250000" />
        <pullCoronaIn value="false" />
      </rearIndicatorCorona>
      <frontIndicatorCorona>
        <size value="0.000000" />
        <size_far value="0.000000" />
        <intensity value="0.000000" />
        <intensity_far value="0.000000" />
        <color value="0x00000000" />
        <numCoronas value="1" />
        <distBetweenCoronas value="128" />
        <distBetweenCoronas_far value="255" />
        <xRotation value="0.000000" />
        <yRotation value="0.000000" />
        <zRotation value="0.000000" />
        <zBias value="0.250000" />
        <pullCoronaIn value="false" />
      </frontIndicatorCorona>
      <tailLight>
        <intensity value="0.250000" />
        <falloffMax value="4.000000" />
        <falloffExponent value="16.000000" />
        <innerConeAngle value="45.000000" />
        <outerConeAngle value="90.000000" />
        <emmissiveBoost value="false" />
        <color value="0xFFFF0000" />
      </tailLight>
      <tailLightCorona>
        <size value="1.200000" />
        <size_far value="2.500000" />
        <intensity value="5.000000" />
        <intensity_far value="1.000000" />
        <color value="0xFFFF0F05" />
        <numCoronas value="0" />
        <distBetweenCoronas value="25" />
        <distBetweenCoronas_far value="49" />
        <xRotation value="0.270177" />
        <yRotation value="1.407433" />
        <zRotation value="0.000000" />
        <zBias value="0.250000" />
        <pullCoronaIn value="false" />
      </tailLightCorona>
      <tailLightMiddleCorona>
        <size value="0.000000" />
        <size_far value="0.000000" />
        <intensity value="0.000000" />
        <intensity_far value="0.000000" />
        <color value="0x00000000" />
        <numCoronas value="0" />
        <distBetweenCoronas value="128" />
        <distBetweenCoronas_far value="255" />
        <xRotation value="0.000000" />
        <yRotation value="0.000000" />
        <zRotation value="0.000000" />
        <zBias value="0.250000" />
        <pullCoronaIn value="false" />
      </tailLightMiddleCorona>
      <headLight>
        <intensity value="1.500000" />
        <falloffMax value="35.000000" />
        <falloffExponent value="16.000000" />
        <innerConeAngle value="0.000000" />
        <outerConeAngle value="60.000000" />
        <emmissiveBoost value="false" />
        <color value="0xFF7FA7E3" />
        <textureName>VehicleLight_car_standardmodern</textureName>
        <mirrorTexture value="false" />
      </headLight>
      <headLightCorona>
        <size value="0.100000" />
        <size_far value="7.000000" />
        <intensity value="7.000000" />
        <intensity_far value="5.000000" />
        <color value="0xFF61A5FF" />
        <numCoronas value="1" />
        <distBetweenCoronas value="27" />
        <distBetweenCoronas_far value="89" />
        <xRotation value="0.000000" />
        <yRotation value="0.175929" />
        <zRotation value="0.527788" />
        <zBias value="0.250000" />
        <pullCoronaIn value="false" />
      </headLightCorona>
      <reversingLight>
        <intensity value="0.500000" />
        <falloffMax value="7.200000" />
        <falloffExponent value="32.000000" />
        <innerConeAngle value="20.000000" />
        <outerConeAngle value="78.000000" />
        <emmissiveBoost value="false" />
        <color value="0xFFFFFFFF" />
      </reversingLight>
      <reversingLightCorona>
        <size value="0.800000" />
        <size_far value="2.000000" />
        <intensity value="1.500000" />
        <intensity_far value="1.000000" />
        <color value="0x00F7F7F7" />
        <numCoronas value="0" />
        <distBetweenCoronas value="128" />
        <distBetweenCoronas_far value="255" />
        <xRotation value="0.000000" />
        <yRotation value="0.000000" />
        <zRotation value="0.000000" />
        <zBias value="0.250000" />
        <pullCoronaIn value="false" />
      </reversingLightCorona>
      <name>rmod_gauntlet4</name>
    </Item>
    </Lights>
</CVehicleModelInfoVarGlobal>