fx_version 'adamant'
game "gta5"

files {
	"audioconfig/*.dat151.rel",
	"audioconfig/*.dat54.rel",
	"audioconfig/*.dat10.rel",
	"sfx/**/*.awc"
}

data_file "AUDIO_GAMEDATA" "audioconfig/akuma_game.dat"
data_file "AUDIO_SOUNDDATA" "audioconfig/akuma_sounds.dat"
data_file "AUDIO_WAVEPACK" "sfx/dlc_akuma"

data_file "AUDIO_GAMEDATA" "audioconfig/bagger_game.dat"
data_file "AUDIO_SOUNDDATA" "audioconfig/bagger_sounds.dat"
data_file "AUDIO_WAVEPACK" "sfx/dlc_bagger"

data_file "AUDIO_GAMEDATA" "audioconfig/bati_game.dat"
data_file "AUDIO_SOUNDDATA" "audioconfig/bati_sounds.dat"
data_file "AUDIO_WAVEPACK" "sfx/dlc_bati"

data_file "AUDIO_GAMEDATA" "audioconfig/bati2_game.dat"
data_file "AUDIO_SOUNDDATA" "audioconfig/bati2_sounds.dat"
data_file "AUDIO_WAVEPACK" "sfx/dlc_bati2"

data_file "AUDIO_GAMEDATA" "audioconfig/carbonrs_game.dat"
data_file "AUDIO_SOUNDDATA" "audioconfig/carbonrs_sounds.dat"
data_file "AUDIO_WAVEPACK" "sfx/dlc_carbonrs"

data_file "AUDIO_GAMEDATA" "audioconfig/daemon_game.dat"
data_file "AUDIO_SOUNDDATA" "audioconfig/daemon_sounds.dat"
data_file "AUDIO_WAVEPACK" "sfx/dlc_daemon"

data_file "AUDIO_GAMEDATA" "audioconfig/double_game.dat"
data_file "AUDIO_SOUNDDATA" "audioconfig/double_sounds.dat"
data_file "AUDIO_WAVEPACK" "sfx/dlc_double"

data_file "AUDIO_GAMEDATA" "audioconfig/faggio_game.dat"
data_file "AUDIO_SOUNDDATA" "audioconfig/faggio_sounds.dat"
data_file "AUDIO_WAVEPACK" "sfx/dlc_faggio"

data_file "AUDIO_GAMEDATA" "audioconfig/hakuchou_game.dat"
data_file "AUDIO_SOUNDDATA" "audioconfig/hakuchou_sounds.dat"
data_file "AUDIO_WAVEPACK" "sfx/dlc_hakuchou"

data_file "AUDIO_GAMEDATA" "audioconfig/hexer_game.dat"
data_file "AUDIO_SOUNDDATA" "audioconfig/hexer_sounds.dat"
data_file "AUDIO_WAVEPACK" "sfx/dlc_hexer"

data_file "AUDIO_GAMEDATA" "audioconfig/innovation_game.dat"
data_file "AUDIO_SOUNDDATA" "audioconfig/innovation_sounds.dat"
data_file "AUDIO_WAVEPACK" "sfx/dlc_innovation"

data_file "AUDIO_GAMEDATA" "audioconfig/nemesis_game.dat"
data_file "AUDIO_SOUNDDATA" "audioconfig/nemesis_sounds.dat"
data_file "AUDIO_WAVEPACK" "sfx/dlc_nemesis"

data_file "AUDIO_GAMEDATA" "audioconfig/pcjss_game.dat"
data_file "AUDIO_SOUNDDATA" "audioconfig/pcjss_sounds.dat"
data_file "AUDIO_WAVEPACK" "sfx/dlc_pcjss"

data_file "AUDIO_GAMEDATA" "audioconfig/ruffian_game.dat"
data_file "AUDIO_SOUNDDATA" "audioconfig/ruffian_sounds.dat"
data_file "AUDIO_WAVEPACK" "sfx/dlc_ruffian"

data_file "AUDIO_GAMEDATA" "audioconfig/sanchez_game.dat"
data_file "AUDIO_SOUNDDATA" "audioconfig/sanchez_sounds.dat"
data_file "AUDIO_WAVEPACK" "sfx/dlc_sanchez"

data_file "AUDIO_GAMEDATA" "audioconfig/sovereign_game.dat"
data_file "AUDIO_SOUNDDATA" "audioconfig/sovereign_sounds.dat"
data_file "AUDIO_WAVEPACK" "sfx/dlc_sovereign"

data_file "AUDIO_GAMEDATA" "audioconfig/thrust_game.dat"
data_file "AUDIO_SOUNDDATA" "audioconfig/thrust_sounds.dat"
data_file "AUDIO_WAVEPACK" "sfx/dlc_thrust"

data_file "AUDIO_GAMEDATA" "audioconfig/vader_game.dat"
data_file "AUDIO_SOUNDDATA" "audioconfig/vader_sounds.dat"
data_file "AUDIO_WAVEPACK" "sfx/dlc_vader"

data_file "AUDIO_GAMEDATA" "audioconfig/vindicator_game.dat"
data_file "AUDIO_SOUNDDATA" "audioconfig/vindicator_sounds.dat"
data_file "AUDIO_WAVEPACK" "sfx/dlc_vindicator"
