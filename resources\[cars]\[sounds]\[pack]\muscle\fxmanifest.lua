fx_version 'adamant'
game "gta5"

files {
	"audioconfig/*.dat151.rel",
	"audioconfig/*.dat54.rel",
	"audioconfig/*.dat10.rel",
	"sfx/**/*.awc"
}

data_file "AUDIO_GAMEDATA" "audioconfig/blade_game.dat"
data_file "AUDIO_SOUNDDATA" "audioconfig/blade_sounds.dat"
data_file "AUDIO_WAVEPACK" "sfx/dlc_blade"

data_file "AUDIO_GAMEDATA" "audioconfig/buccaneer_game.dat"
data_file "AUDIO_SOUNDDATA" "audioconfig/buccaneer_sounds.dat"
data_file "AUDIO_WAVEPACK" "sfx/dlc_buccaneer"

data_file "AUDIO_GAMEDATA" "audioconfig/chino_game.dat"
data_file "AUDIO_SOUNDDATA" "audioconfig/chino_sounds.dat"
data_file "AUDIO_WAVEPACK" "sfx/dlc_chino"

data_file "AUDIO_GAMEDATA" "audioconfig/coquette3_game.dat"
data_file "AUDIO_SOUNDDATA" "audioconfig/coquette3_sounds.dat"
data_file "AUDIO_WAVEPACK" "sfx/dlc_coquette3"

data_file "AUDIO_GAMEDATA" "audioconfig/dominator_game.dat"
data_file "AUDIO_SOUNDDATA" "audioconfig/dominator_sounds.dat"
data_file "AUDIO_WAVEPACK" "sfx/dlc_dominator"

data_file "AUDIO_GAMEDATA" "audioconfig/dominator2_game.dat"
data_file "AUDIO_SOUNDDATA" "audioconfig/dominator2_sounds.dat"
data_file "AUDIO_WAVEPACK" "sfx/dlc_dominator2"

data_file "AUDIO_GAMEDATA" "audioconfig/dukes_game.dat"
data_file "AUDIO_SOUNDDATA" "audioconfig/dukes_sounds.dat"
data_file "AUDIO_WAVEPACK" "sfx/dlc_dukes"

data_file "AUDIO_GAMEDATA" "audioconfig/gauntlet_game.dat"
data_file "AUDIO_SOUNDDATA" "audioconfig/gauntlet_sounds.dat"
data_file "AUDIO_WAVEPACK" "sfx/dlc_gauntlet"

data_file "AUDIO_GAMEDATA" "audioconfig/gauntlet2_game.dat"
data_file "AUDIO_SOUNDDATA" "audioconfig/gauntlet2_sounds.dat"
data_file "AUDIO_WAVEPACK" "sfx/dlc_gauntlet2"

data_file "AUDIO_GAMEDATA" "audioconfig/hotknife_game.dat"
data_file "AUDIO_SOUNDDATA" "audioconfig/hotknife_sounds.dat"
data_file "AUDIO_WAVEPACK" "sfx/dlc_hotknife"

data_file "AUDIO_GAMEDATA" "audioconfig/phoenix_game.dat"
data_file "AUDIO_SOUNDDATA" "audioconfig/phoenix_sounds.dat"
data_file "AUDIO_WAVEPACK" "sfx/dlc_phoenix"

data_file "AUDIO_GAMEDATA" "audioconfig/picador_game.dat"
data_file "AUDIO_SOUNDDATA" "audioconfig/picador_sounds.dat"
data_file "AUDIO_WAVEPACK" "sfx/dlc_picador"

data_file "AUDIO_GAMEDATA" "audioconfig/rloader_game.dat"
data_file "AUDIO_SOUNDDATA" "audioconfig/rloader_sounds.dat"
data_file "AUDIO_WAVEPACK" "sfx/dlc_rloader"

data_file "AUDIO_GAMEDATA" "audioconfig/rloader2_game.dat"
data_file "AUDIO_SOUNDDATA" "audioconfig/rloader2_sounds.dat"
data_file "AUDIO_WAVEPACK" "sfx/dlc_rloader2"

data_file "AUDIO_GAMEDATA" "audioconfig/ruiner_game.dat"
data_file "AUDIO_SOUNDDATA" "audioconfig/ruiner_sounds.dat"
data_file "AUDIO_WAVEPACK" "sfx/dlc_ruiner"

data_file "AUDIO_GAMEDATA" "audioconfig/sabregt_game.dat"
data_file "AUDIO_SOUNDDATA" "audioconfig/sabregt_sounds.dat"
data_file "AUDIO_WAVEPACK" "sfx/dlc_sabregt"

data_file "AUDIO_GAMEDATA" "audioconfig/slamvan_game.dat"
data_file "AUDIO_SOUNDDATA" "audioconfig/slamvan_sounds.dat"
data_file "AUDIO_WAVEPACK" "sfx/dlc_slamvan"

data_file "AUDIO_GAMEDATA" "audioconfig/stallion_game.dat"
data_file "AUDIO_SOUNDDATA" "audioconfig/stallion_sounds.dat"
data_file "AUDIO_WAVEPACK" "sfx/dlc_stallion"

data_file "AUDIO_GAMEDATA" "audioconfig/stallion2_game.dat"
data_file "AUDIO_SOUNDDATA" "audioconfig/stallion2_sounds.dat"
data_file "AUDIO_WAVEPACK" "sfx/dlc_stallion2"

data_file "AUDIO_GAMEDATA" "audioconfig/vigero_game.dat"
data_file "AUDIO_SOUNDDATA" "audioconfig/vigero_sounds.dat"
data_file "AUDIO_WAVEPACK" "sfx/dlc_vigero"

data_file "AUDIO_GAMEDATA" "audioconfig/virgo_game.dat"
data_file "AUDIO_SOUNDDATA" "audioconfig/virgo_sounds.dat"
data_file "AUDIO_WAVEPACK" "sfx/dlc_virgo"

data_file "AUDIO_GAMEDATA" "audioconfig/voodoo2_game.dat"
data_file "AUDIO_SOUNDDATA" "audioconfig/voodoo2_sounds.dat"
data_file "AUDIO_WAVEPACK" "sfx/dlc_voodoo2"
