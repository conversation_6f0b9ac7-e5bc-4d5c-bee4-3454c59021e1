fx_version 'adamant'
game "gta5"

files {
	"audioconfig/*.dat151.rel",
	"audioconfig/*.dat54.rel",
	"audioconfig/*.dat10.rel",
	"sfx/**/*.awc"
}

data_file "AUDIO_GAMEDATA" "audioconfig/baller_game.dat"
data_file "AUDIO_SOUNDDATA" "audioconfig/baller_sounds.dat"
data_file "AUDIO_WAVEPACK" "sfx/dlc_baller"

data_file "AUDIO_GAMEDATA" "audioconfig/baller2_game.dat"
data_file "AUDIO_SOUNDDATA" "audioconfig/baller2_sounds.dat"
data_file "AUDIO_WAVEPACK" "sfx/dlc_baller2"

data_file "AUDIO_GAMEDATA" "audioconfig/bjxl_game.dat"
data_file "AUDIO_SOUNDDATA" "audioconfig/bjxl_sounds.dat"
data_file "AUDIO_WAVEPACK" "sfx/dlc_bjxl"

data_file "AUDIO_GAMEDATA" "audioconfig/cavalcade_game.dat"
data_file "AUDIO_SOUNDDATA" "audioconfig/cavalcade_sounds.dat"
data_file "AUDIO_WAVEPACK" "sfx/dlc_cavalcade"

data_file "AUDIO_GAMEDATA" "audioconfig/cavalcade2_game.dat"
data_file "AUDIO_SOUNDDATA" "audioconfig/cavalcade2_sounds.dat"
data_file "AUDIO_WAVEPACK" "sfx/dlc_cavalcade2"

data_file "AUDIO_GAMEDATA" "audioconfig/dubsta_game.dat"
data_file "AUDIO_SOUNDDATA" "audioconfig/dubsta_sounds.dat"
data_file "AUDIO_WAVEPACK" "sfx/dlc_dubsta"

data_file "AUDIO_GAMEDATA" "audioconfig/dubsta2_game.dat"
data_file "AUDIO_SOUNDDATA" "audioconfig/dubsta2_sounds.dat"
data_file "AUDIO_WAVEPACK" "sfx/dlc_dubsta2"

data_file "AUDIO_GAMEDATA" "audioconfig/ifnq2_game.dat"
data_file "AUDIO_SOUNDDATA" "audioconfig/ifnq2_sounds.dat"
data_file "AUDIO_WAVEPACK" "sfx/dlc_ifnq2"

data_file "AUDIO_GAMEDATA" "audioconfig/granger_game.dat"
data_file "AUDIO_SOUNDDATA" "audioconfig/granger_sounds.dat"
data_file "AUDIO_WAVEPACK" "sfx/dlc_granger"

data_file "AUDIO_GAMEDATA" "audioconfig/gresley_game.dat"
data_file "AUDIO_SOUNDDATA" "audioconfig/gresley_sounds.dat"
data_file "AUDIO_WAVEPACK" "sfx/dlc_gresley"

data_file "AUDIO_GAMEDATA" "audioconfig/habanero_game.dat"
data_file "AUDIO_SOUNDDATA" "audioconfig/habanero_sounds.dat"
data_file "AUDIO_WAVEPACK" "sfx/dlc_habanero"

data_file "AUDIO_GAMEDATA" "audioconfig/huntley_game.dat"
data_file "AUDIO_SOUNDDATA" "audioconfig/huntley_sounds.dat"
data_file "AUDIO_WAVEPACK" "sfx/dlc_huntley"

data_file "AUDIO_GAMEDATA" "audioconfig/landstalker_game.dat"
data_file "AUDIO_SOUNDDATA" "audioconfig/landstalker_sounds.dat"
data_file "AUDIO_WAVEPACK" "sfx/dlc_landstalker"

data_file "AUDIO_GAMEDATA" "audioconfig/mesa_game.dat"
data_file "AUDIO_SOUNDDATA" "audioconfig/mesa_sounds.dat"
data_file "AUDIO_WAVEPACK" "sfx/dlc_mesa"

data_file "AUDIO_GAMEDATA" "audioconfig/patriot_game.dat"
data_file "AUDIO_SOUNDDATA" "audioconfig/patriot_sounds.dat"
data_file "AUDIO_WAVEPACK" "sfx/dlc_patriot"

data_file "AUDIO_GAMEDATA" "audioconfig/radi_game.dat"
data_file "AUDIO_SOUNDDATA" "audioconfig/radi_sounds.dat"
data_file "AUDIO_WAVEPACK" "sfx/dlc_radi"

data_file "AUDIO_GAMEDATA" "audioconfig/rocoto_game.dat"
data_file "AUDIO_SOUNDDATA" "audioconfig/rocoto_sounds.dat"
data_file "AUDIO_WAVEPACK" "sfx/dlc_rocoto"

data_file "AUDIO_GAMEDATA" "audioconfig/seminole_game.dat"
data_file "AUDIO_SOUNDDATA" "audioconfig/seminole_sounds.dat"
data_file "AUDIO_WAVEPACK" "sfx/dlc_seminole"

data_file "AUDIO_GAMEDATA" "audioconfig/serrano_game.dat"
data_file "AUDIO_SOUNDDATA" "audioconfig/serrano_sounds.dat"
data_file "AUDIO_WAVEPACK" "sfx/dlc_serrano"
