<?xml version="1.0" encoding="UTF-8"?>
    
<CVehicleModelInfoVarGlobal>
  <Kits>
    <Item>
      <kitName>345865_rt3000varis_modkit</kitName>
      <id value="345865" />
      <kitType>MKT_SPECIAL</kitType>
      <visibleMods> 
  	  <Item>
          <modelName>rt3000_stockfilt</modelName>
          <modShopLabel>RT3000_FILT</modShopLabel>
          <linkedModels />
          <turnOffBones>
          <Item>misc_p</Item>
          </turnOffBones>
          <type>VMT_DOOR_R</type>
          <bone>engineblock</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
	  <Item>
          <modelName>rt3000_filt_a</modelName>
          <modShopLabel>FUTO2_FILTA</modShopLabel>
          <linkedModels>
          <Item>rt3000_stockfilt</Item>
          </linkedModels>
          <turnOffBones>
          <Item>misc_p</Item>
          </turnOffBones>
          <type>VMT_DOOR_R</type>
          <bone>engineblock</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
	  <Item>
          <modelName>rt3000_filt_b</modelName>
          <modShopLabel>FUTO2_FILTB</modShopLabel>
          <linkedModels />
          <turnOffBones>
          <Item>misc_p</Item>
          </turnOffBones>
          <type>VMT_DOOR_R</type>
          <bone>engineblock</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
	  <Item>
          <modelName>rt3000_filt_c</modelName>
          <modShopLabel>FUTO2_FILTC</modShopLabel>
          <linkedModels />
          <turnOffBones>
          <Item>misc_p</Item>
          </turnOffBones>
          <type>VMT_DOOR_R</type>
          <bone>engineblock</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
	  <Item>
          <modelName>rt3000_filt_d</modelName>
          <modShopLabel>FUTO2_FILTD</modShopLabel>
          <linkedModels>
          <Item>rt3000_stockfilt</Item>
          </linkedModels>
          <turnOffBones>
          <Item>misc_p</Item>
          </turnOffBones>
          <type>VMT_DOOR_R</type>
          <bone>engineblock</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
	  <Item>
          <modelName>rt3000_filt_e</modelName>
          <modShopLabel>FUTO2_FILTE</modShopLabel>
          <linkedModels />
          <turnOffBones>
          <Item>misc_p</Item>
          </turnOffBones>
          <type>VMT_DOOR_R</type>
          <bone>engineblock</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
	  <Item>
          <modelName>rt3000_filt_f</modelName>
          <modShopLabel>FUTO2_FILTF</modShopLabel>
          <linkedModels />
          <turnOffBones>
          <Item>misc_p</Item>
          </turnOffBones>
          <type>VMT_DOOR_R</type>
          <bone>engineblock</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
	  <Item>
          <modelName>rt3000_filt_g</modelName>
          <modShopLabel>FUTO2_FILTG</modShopLabel>
          <linkedModels />
          <turnOffBones>
          <Item>misc_p</Item>
          </turnOffBones>
          <type>VMT_DOOR_R</type>
          <bone>engineblock</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
<!-- DASH_MODS -->	
	  <Item>
          <modelName>rt3000_dash1</modelName>
          <modShopLabel>SULTAN_INT2</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_INTERIOR3</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
	  <Item>
          <modelName>rt3000_dash2</modelName>
          <modShopLabel>SULTAN_INT3</modShopLabel>
          <linkedModels />
          <turnOffBones>
		  <Item>misc_h</Item>
          </turnOffBones>
          <type>VMT_INTERIOR3</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
	  <Item>
          <modelName>rt3000_dash3</modelName>
          <modShopLabel>SULTAN_INT5</modShopLabel>
          <linkedModels />
          <turnOffBones>
		  <Item>misc_h</Item>
          </turnOffBones>
          <type>VMT_INTERIOR3</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
	  <Item>
          <modelName>rt3000_dash4</modelName>
          <modShopLabel>SULTAN_INT4</modShopLabel>
          <linkedModels>
          <Item>rt3000_boot</Item>
          </linkedModels>
          <turnOffBones>
		  <Item>misc_h</Item>
		  <Item>misc_j</Item>
          </turnOffBones>
          <type>VMT_INTERIOR3</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
<!-- SUNSTRIP_MODS -->	
	  <Item>
          <modelName>rt3000_sstripa</modelName>
          <modShopLabel>REMUS_SSTRIPA</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_INTERIOR1</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
	  <Item>
          <modelName>rt3000_sstripb</modelName>
          <modShopLabel>REMUS_SSTRIPB</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_INTERIOR1</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
	  <Item>
          <modelName>rt3000_sstripc</modelName>
          <modShopLabel>REMUS_SSTRIPC</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_INTERIOR1</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
<!-- HOOD_MODS -->
	  <Item>
          <modelName>rt3000_nomod</modelName>
          <modShopLabel>RT3000_HOOD</modShopLabel>
          <linkedModels />
          <turnOffBones>
		  <Item>bonnet</Item>
          </turnOffBones>
          <type>VMT_BONNET</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
	  <Item>
          <modelName>rt3000_hooda</modelName>
          <modShopLabel>RT3000_HOODA</modShopLabel>
          <linkedModels />
          <turnOffBones>
          <Item>bonnet</Item>
          </turnOffBones>
          <type>VMT_BONNET</type>
          <bone>bonnet</bone>
          <collisionBone>bonnet</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
	  <Item>
          <modelName>rt3000_hoodb</modelName>
          <modShopLabel>RT3000_HOODB</modShopLabel>
          <linkedModels />
          <turnOffBones>
          <Item>bonnet</Item>
          </turnOffBones>
          <type>VMT_BONNET</type>
          <bone>bonnet</bone>
          <collisionBone>bonnet</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
	  <Item>
          <modelName>rt3000_hoodc</modelName>
          <modShopLabel>RT3000_HOODC</modShopLabel>
          <linkedModels />
          <turnOffBones>
          <Item>bonnet</Item>
          </turnOffBones>
          <type>VMT_BONNET</type>
          <bone>bonnet</bone>
          <collisionBone>bonnet</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
	  <Item>
          <modelName>rt3000_hoodd</modelName>
          <modShopLabel>RT3000_HOODD</modShopLabel>
          <linkedModels />
          <turnOffBones>
          <Item>bonnet</Item>
          </turnOffBones>
          <type>VMT_BONNET</type>
          <bone>bonnet</bone>
          <collisionBone>bonnet</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
	  <Item>
          <modelName>rt3000_hoode</modelName>
          <modShopLabel>RT3000_HOODE</modShopLabel>
          <linkedModels />
          <turnOffBones>
          <Item>bonnet</Item>
          </turnOffBones>
          <type>VMT_BONNET</type>
          <bone>bonnet</bone>
          <collisionBone>bonnet</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
	  <Item>
          <modelName>rt3000_hoodf</modelName>
          <modShopLabel>RT3000_HOODF</modShopLabel>
          <linkedModels />
          <turnOffBones>
          <Item>bonnet</Item>
          </turnOffBones>
          <type>VMT_BONNET</type>
          <bone>bonnet</bone>
          <collisionBone>bonnet</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
	  <Item>
          <modelName>rt3000_hoodg</modelName>
          <modShopLabel>RT3000_HOODG</modShopLabel>
          <linkedModels />
          <turnOffBones>
          <Item>bonnet</Item>
          </turnOffBones>
          <type>VMT_BONNET</type>
          <bone>bonnet</bone>
          <collisionBone>bonnet</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
	  <Item>
          <modelName>rt3000_hoodh</modelName>
          <modShopLabel>RT3000_HOODH</modShopLabel>
          <linkedModels />
          <turnOffBones>
          <Item>bonnet</Item>
          </turnOffBones>
          <type>VMT_BONNET</type>
          <bone>bonnet</bone>
          <collisionBone>bonnet</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
	  <Item>
          <modelName>rt3000_hoodi</modelName>
          <modShopLabel>RT3000_HOODI</modShopLabel>
          <linkedModels />
          <turnOffBones>
          <Item>bonnet</Item>
          </turnOffBones>
          <type>VMT_BONNET</type>
          <bone>bonnet</bone>
          <collisionBone>bonnet</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
	  <Item>
          <modelName>rt3000_door1l</modelName>
          <modShopLabel>SULTAN_DOOR1</modShopLabel>
          <linkedModels>
          <Item>rt3000_door1r</Item>
          </linkedModels>
          <turnOffBones>
          <Item>misc_f</Item>
          <Item>misc_g</Item>
          </turnOffBones>
          <type>VMT_INTERIOR5</type>
          <bone>door_dside_f	</bone>
          <collisionBone>door_dside_f</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
	  <Item>
          <modelName>rt3000_door2l</modelName>
          <modShopLabel>SULTAN_DOOR2</modShopLabel>
          <linkedModels>
          <Item>rt3000_door2r</Item>
          </linkedModels>
          <turnOffBones>
          <Item>misc_f</Item>
          <Item>misc_g</Item>
          </turnOffBones>
          <type>VMT_INTERIOR5</type>
          <bone>door_dside_f</bone>
          <collisionBone>door_dside_f</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
	  <Item>
          <modelName>rt3000_door3l</modelName>
          <modShopLabel>SULTAN_DOOR3</modShopLabel>
          <linkedModels>
          <Item>rt3000_door3r</Item>
          </linkedModels>
          <turnOffBones>
          <Item>misc_f</Item>
          <Item>misc_g</Item>
          </turnOffBones>
          <type>VMT_INTERIOR5</type>
          <bone>door_dside_f</bone>
          <collisionBone>door_dside_f</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
	  <Item>
          <modelName>rt3000_door4l</modelName>
          <modShopLabel>SULTAN_DOOR4</modShopLabel>
          <linkedModels>
          <Item>rt3000_door4r</Item>
          </linkedModels>
          <turnOffBones>
          <Item>misc_f</Item>
          <Item>misc_g</Item>
          </turnOffBones>
          <type>VMT_INTERIOR5</type>
          <bone>door_dside_f</bone>
          <collisionBone>door_dside_f</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
	  <Item>
          <modelName>rt3000_door5l</modelName>
          <modShopLabel>SULTAN_DOOR5</modShopLabel>
          <linkedModels>
          <Item>rt3000_door5r</Item>
          </linkedModels>
          <turnOffBones>
          <Item>misc_f</Item>
          <Item>misc_g</Item>
          </turnOffBones>
          <type>VMT_INTERIOR5</type>
          <bone>door_dside_f</bone>
          <collisionBone>door_dside_f</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
	  <Item>
          <modelName>rt3000_door6l</modelName>
          <modShopLabel>SULTAN_DOOR6</modShopLabel>
          <linkedModels>
          <Item>rt3000_door6r</Item>
          </linkedModels>
          <turnOffBones>
          <Item>misc_f</Item>
          <Item>misc_g</Item>
          </turnOffBones>
          <type>VMT_INTERIOR5</type>
          <bone>door_dside_f</bone>
          <collisionBone>door_dside_f</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
	  <Item>
          <modelName>rt3000_door7l</modelName>
          <modShopLabel>SULTAN_DOOR7</modShopLabel>
          <linkedModels>
          <Item>rt3000_door7r</Item>
          </linkedModels>
          <turnOffBones>
          <Item>misc_f</Item>
          <Item>misc_g</Item>
          </turnOffBones>
          <type>VMT_INTERIOR5</type>
          <bone>door_dside_f</bone>
          <collisionBone>door_dside_f</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
	  <Item>
          <modelName>rt3000_brace1</modelName>
          <modShopLabel>SULTAN_SBRACE1</modShopLabel>
          <linkedModels />
          <turnOffBones/>
          <type>VMT_ENGINEBAY3</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
	  <Item>
          <modelName>rt3000_brace2</modelName>
          <modShopLabel>SULTAN_SBRACE2</modShopLabel>
          <linkedModels />
          <turnOffBones/>
          <type>VMT_ENGINEBAY3</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
	  <Item>
          <modelName>rt3000_brace3</modelName>
          <modShopLabel>SULTAN_SBRACE3</modShopLabel>
          <linkedModels />
          <turnOffBones/>
          <type>VMT_ENGINEBAY3</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
	  <Item>
          <modelName>rt3000_brace4</modelName>
          <modShopLabel>SULTAN_SBRACE4</modShopLabel>
          <linkedModels />
          <turnOffBones/>
          <type>VMT_ENGINEBAY3</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
	  <Item>
          <modelName>rt3000_brace5</modelName>
          <modShopLabel>SULTAN_SBRACE5</modShopLabel>
          <linkedModels />
          <turnOffBones/>
          <type>VMT_ENGINEBAY3</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
	  <Item>
          <modelName>rt3000_brace6</modelName>
          <modShopLabel>SULTAN_SBRACE6</modShopLabel>
          <linkedModels />
          <turnOffBones/>
          <type>VMT_ENGINEBAY3</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
	  <Item>
          <modelName>rt3000_brace7</modelName>
          <modShopLabel>SULTAN_SBRACE7</modShopLabel>
          <linkedModels />
          <turnOffBones/>
          <type>VMT_ENGINEBAY3</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
	  <Item>
          <modelName>rt3000_brace8</modelName>
          <modShopLabel>SULTAN_SBRACE8</modShopLabel>
          <linkedModels />
          <turnOffBones/>
          <type>VMT_ENGINEBAY3</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
	  <Item>
          <modelName>rt3000_brace9</modelName>
          <modShopLabel>SULTAN_SBRACE9</modShopLabel>
          <linkedModels />
          <turnOffBones/>
          <type>VMT_ENGINEBAY3</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
	  <Item>
          <modelName>rt3000_brace10</modelName>
          <modShopLabel>SULTAN_SBRACE10</modShopLabel>
          <linkedModels />
          <turnOffBones/>
          <type>VMT_ENGINEBAY3</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
	  <Item>
          <modelName>rt3000_brace11</modelName>
          <modShopLabel>SULTAN_SBRACE11</modShopLabel>
          <linkedModels />
          <turnOffBones/>
          <type>VMT_ENGINEBAY3</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
	  <Item>
          <modelName>rt3000_brace12</modelName>
          <modShopLabel>SULTAN_SBRACE12</modShopLabel>
          <linkedModels />
          <turnOffBones/>
          <type>VMT_ENGINEBAY3</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
	  <Item>
          <modelName>rt3000_6cyl1</modelName>
          <modShopLabel>REMUS_6CYL1</modShopLabel>
          <linkedModels />
          <turnOffBones>
          <Item>misc_r</Item>
          </turnOffBones>
          <type>VMT_ENGINEBAY1</type>
          <bone>engineblock</bone>
          <collisionBone>engineblock</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
	  <Item>
          <modelName>rt3000_6cyl2</modelName>
          <modShopLabel>REMUS_6CYL2</modShopLabel>
          <linkedModels />
          <turnOffBones>
          <Item>misc_r</Item>
          </turnOffBones>
          <type>VMT_ENGINEBAY1</type>
          <bone>engineblock</bone>
          <collisionBone>engineblock</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
	  <Item>
          <modelName>rt3000_6cyl3</modelName>
          <modShopLabel>REMUS_6CYL3</modShopLabel>
          <linkedModels />
          <turnOffBones>
          <Item>misc_r</Item>
          </turnOffBones>
          <type>VMT_ENGINEBAY1</type>
          <bone>engineblock</bone>
          <collisionBone>engineblock</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
	  <Item>
          <modelName>rt3000_6cyl4</modelName>
          <modShopLabel>REMUS_6CYL4</modShopLabel>
          <linkedModels />
          <turnOffBones>
          <Item>misc_r</Item>
          </turnOffBones>
          <type>VMT_ENGINEBAY1</type>
          <bone>engineblock</bone>
          <collisionBone>engineblock</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
	  <Item>
          <modelName>rt3000_6cyl5</modelName>
          <modShopLabel>REMUS_6CYL5</modShopLabel>
          <linkedModels />
          <turnOffBones>
          <Item>misc_r</Item>
          </turnOffBones>
          <type>VMT_ENGINEBAY1</type>
          <bone>engineblock</bone>
          <collisionBone>engineblock</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
	  <Item>
          <modelName>rt3000_6cyl6</modelName>
          <modShopLabel>REMUS_6CYL6</modShopLabel>
          <linkedModels />
          <turnOffBones>
          <Item>misc_r</Item>
          </turnOffBones>
          <type>VMT_ENGINEBAY1</type>
          <bone>engineblock</bone>
          <collisionBone>engineblock</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
	  <Item>
          <modelName>rt3000_6cyl1a</modelName>
          <modShopLabel>REMUS_6CYL1A</modShopLabel>
          <linkedModels />
          <turnOffBones>
          <Item>misc_s</Item>
          </turnOffBones>
          <type>VMT_ENGINEBAY2</type>
          <bone>engineblock</bone>
          <collisionBone>engineblock</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
	  <Item>
          <modelName>rt3000_6cyl2a</modelName>
          <modShopLabel>REMUS_6CYL2A</modShopLabel>
          <linkedModels />
          <turnOffBones>
          <Item>misc_s</Item>
          </turnOffBones>
          <type>VMT_ENGINEBAY2</type>
          <bone>engineblock</bone>
          <collisionBone>engineblock</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
	  <Item>
          <modelName>rt3000_6cyl3a</modelName>
          <modShopLabel>REMUS_6CYL3A</modShopLabel>
          <linkedModels />
          <turnOffBones>
          <Item>misc_s</Item>
          </turnOffBones>
          <type>VMT_ENGINEBAY2</type>
          <bone>engineblock</bone>
          <collisionBone>engineblock</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
	  <Item>
          <modelName>rt3000_6cyl4a</modelName>
          <modShopLabel>REMUS_6CYL4A</modShopLabel>
          <linkedModels />
          <turnOffBones>
          <Item>misc_s</Item>
          </turnOffBones>
          <type>VMT_ENGINEBAY2</type>
          <bone>engineblock</bone>
          <collisionBone>engineblock</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
	  <Item>
          <modelName>rt3000_6cyl5a</modelName>
          <modShopLabel>REMUS_6CYL5A</modShopLabel>
          <linkedModels />
          <turnOffBones>
          <Item>misc_s</Item>
          </turnOffBones>
          <type>VMT_ENGINEBAY2</type>
          <bone>engineblock</bone>
          <collisionBone>engineblock</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
	  <Item>
          <modelName>rt3000_6cyl6a</modelName>
          <modShopLabel>REMUS_6CYL6A</modShopLabel>
          <linkedModels />
          <turnOffBones>
          <Item>misc_s</Item>
          </turnOffBones>
          <type>VMT_ENGINEBAY2</type>
          <bone>engineblock</bone>
          <collisionBone>engineblock</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
	  <Item>
          <modelName>rt3000_pulley1</modelName>
          <modShopLabel>REMUS_PULL1</modShopLabel>
          <linkedModels />
          <turnOffBones>
          <Item>misc_s</Item>
          </turnOffBones>
          <type>VMT_ENGINEBAY2</type>
          <bone>engineblock</bone>
          <collisionBone>engineblock</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
	  <Item>
          <modelName>rt3000_pulley2</modelName>
          <modShopLabel>REMUS_PULL2</modShopLabel>
          <linkedModels />
          <turnOffBones>
          <Item>misc_s</Item>
          </turnOffBones>
          <type>VMT_ENGINEBAY2</type>
          <bone>engineblock</bone>
          <collisionBone>engineblock</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
	  <Item>
          <modelName>rt3000_pulley3</modelName>
          <modShopLabel>REMUS_PULL3</modShopLabel>
          <linkedModels />
          <turnOffBones>
          <Item>misc_s</Item>
          </turnOffBones>
          <type>VMT_ENGINEBAY2</type>
          <bone>engineblock</bone>
          <collisionBone>engineblock</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
	  <Item>
          <modelName>rt3000_pulley4</modelName>
          <modShopLabel>REMUS_PULL4</modShopLabel>
          <linkedModels />
          <turnOffBones>
          <Item>misc_s</Item>
          </turnOffBones>
          <type>VMT_ENGINEBAY2</type>
          <bone>engineblock</bone>
          <collisionBone>engineblock</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
	<!-- SEATS -->	
        <Item>
          <modelName>rt3000_sports1_smod</modelName>
          <modShopLabel>SMOD_BSEAT1</modShopLabel>
          <linkedModels />
          <turnOffBones>
            <Item>misc_b</Item>
          </turnOffBones>
          <type>VMT_SEATS</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>rt3000_bucketa1_smod</modelName>
          <modShopLabel>SMOD_BSEAT2</modShopLabel>
          <linkedModels />
          <turnOffBones>
            <Item>misc_b</Item>
          </turnOffBones>
          <type>VMT_SEATS</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>rt3000_bucketa2_smod</modelName>
          <modShopLabel>SMOD_BSEAT3</modShopLabel>
          <linkedModels />
          <turnOffBones>
            <Item>misc_b</Item>
          </turnOffBones>
          <type>VMT_SEATS</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>rt3000_bucketa3_smod</modelName>
          <modShopLabel>SMOD_BSEAT4</modShopLabel>
          <linkedModels />
          <turnOffBones>
            <Item>misc_b</Item>
          </turnOffBones>
          <type>VMT_SEATS</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>rt3000_bucketb1_smod</modelName>
          <modShopLabel>SMOD_BSEAT5</modShopLabel>
          <linkedModels />
          <turnOffBones>
            <Item>misc_b</Item>
          </turnOffBones>
          <type>VMT_SEATS</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>rt3000_bucketb2_smod</modelName>
          <modShopLabel>SMOD_BSEAT6</modShopLabel>
          <linkedModels />
          <turnOffBones>
            <Item>misc_b</Item>
          </turnOffBones>
          <type>VMT_SEATS</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>rt3000_bucketb3_smod</modelName>
          <modShopLabel>SMOD_BSEAT7</modShopLabel>
          <linkedModels />
          <turnOffBones>
            <Item>misc_b</Item>
          </turnOffBones>
          <type>VMT_SEATS</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>rt3000_bucketc1_smod</modelName>
          <modShopLabel>SMOD_BSEAT8</modShopLabel>
          <linkedModels />
          <turnOffBones>
            <Item>misc_b</Item>
          </turnOffBones>
          <type>VMT_SEATS</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>rt3000_bucketc2_smod</modelName>
          <modShopLabel>SMOD_BSEAT9</modShopLabel>
          <linkedModels />
          <turnOffBones>
            <Item>misc_b</Item>
          </turnOffBones>
          <type>VMT_SEATS</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>rt3000_bucketc3_smod</modelName>
          <modShopLabel>SMOD_BSEAT10</modShopLabel>
          <linkedModels />
          <turnOffBones>
            <Item>misc_b</Item>
          </turnOffBones>
          <type>VMT_SEATS</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>rt3000_bucketd1_smod</modelName>
          <modShopLabel>SMOD_BSEAT11</modShopLabel>
          <linkedModels />
          <turnOffBones>
            <Item>misc_b</Item>
          </turnOffBones>
          <type>VMT_SEATS</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>rt3000_bucketd2_smod</modelName>
          <modShopLabel>SMOD_BSEAT12</modShopLabel>
          <linkedModels />
          <turnOffBones>
            <Item>misc_b</Item>
          </turnOffBones>
          <type>VMT_SEATS</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>rt3000_bucketd3_smod</modelName>
          <modShopLabel>SMOD_BSEAT13</modShopLabel>
          <linkedModels />
          <turnOffBones>
            <Item>misc_b</Item>
          </turnOffBones>
          <type>VMT_SEATS</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>rt3000_buckete1_smod</modelName>
          <modShopLabel>SMOD_BSEAT15</modShopLabel>
          <linkedModels />
          <turnOffBones>
            <Item>misc_b</Item>
          </turnOffBones>
          <type>VMT_SEATS</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
	<!-- STEERING WHEELS -->	
        <Item>
          <modelName>rt3000_stwheel1</modelName>
          <modShopLabel>SMOD2_STEER1</modShopLabel>
          <linkedModels />
          <turnOffBones>
            <Item>steeringwheel</Item>
          </turnOffBones>
          <type>VMT_STEERING</type>
          <bone>steeringwheel</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>rt3000_stwheel2</modelName>
          <modShopLabel>SMOD2_STEER2</modShopLabel>
          <linkedModels />
          <turnOffBones>
            <Item>steeringwheel</Item>
          </turnOffBones>
          <type>VMT_STEERING</type>
          <bone>steeringwheel</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>rt3000_stwheel3</modelName>
          <modShopLabel>SMOD2_STEER3</modShopLabel>
          <linkedModels />
          <turnOffBones>
            <Item>steeringwheel</Item>
          </turnOffBones>
          <type>VMT_STEERING</type>
          <bone>steeringwheel</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>rt3000_stwheel4</modelName>
          <modShopLabel>SMOD2_STEER4</modShopLabel>
          <linkedModels />
          <turnOffBones>
            <Item>steeringwheel</Item>
          </turnOffBones>
          <type>VMT_STEERING</type>
          <bone>steeringwheel</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>rt3000_stwheel5</modelName>
          <modShopLabel>SMOD2_STEER5</modShopLabel>
          <linkedModels />
          <turnOffBones>
            <Item>steeringwheel</Item>
          </turnOffBones>
          <type>VMT_STEERING</type>
          <bone>steeringwheel</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>rt3000_stwheel6</modelName>
          <modShopLabel>SMOD2_STEER6</modShopLabel>
          <linkedModels />
          <turnOffBones>
            <Item>steeringwheel</Item>
          </turnOffBones>
          <type>VMT_STEERING</type>
          <bone>steeringwheel</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>rt3000_stwheel7</modelName>
          <modShopLabel>SMOD2_STEER7</modShopLabel>
          <linkedModels />
          <turnOffBones>
            <Item>steeringwheel</Item>
          </turnOffBones>
          <type>VMT_STEERING</type>
          <bone>steeringwheel</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>rt3000_stwheel8</modelName>
          <modShopLabel>SMOD2_STEER8</modShopLabel>
          <linkedModels />
          <turnOffBones>
            <Item>steeringwheel</Item>
          </turnOffBones>
          <type>VMT_STEERING</type>
          <bone>steeringwheel</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>rt3000_stwheel9</modelName>
          <modShopLabel>SMOD2_STEER9</modShopLabel>
          <linkedModels />
          <turnOffBones>
            <Item>steeringwheel</Item>
          </turnOffBones>
          <type>VMT_STEERING</type>
          <bone>steeringwheel</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>rt3000_stwheel10</modelName>
          <modShopLabel>SMOD2_STEER10</modShopLabel>
          <linkedModels />
          <turnOffBones>
            <Item>steeringwheel</Item>
          </turnOffBones>
          <type>VMT_STEERING</type>
          <bone>steeringwheel</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>rt3000_stwheel11</modelName>
          <modShopLabel>SMOD2_STEER11</modShopLabel>
          <linkedModels />
          <turnOffBones>
            <Item>steeringwheel</Item>
          </turnOffBones>
          <type>VMT_STEERING</type>
          <bone>steeringwheel</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>rt3000_stwheel12</modelName>
          <modShopLabel>SMOD2_STEER12</modShopLabel>
          <linkedModels />
          <turnOffBones>
            <Item>steeringwheel</Item>
          </turnOffBones>
          <type>VMT_STEERING</type>
          <bone>steeringwheel</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>rt3000_stwheel13</modelName>
          <modShopLabel>SMOD2_STEER13</modShopLabel>
          <linkedModels />
          <turnOffBones>
            <Item>steeringwheel</Item>
          </turnOffBones>
          <type>VMT_STEERING</type>
          <bone>steeringwheel</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>rt3000_stwheel14</modelName>
          <modShopLabel>SMOD2_STEER14</modShopLabel>
          <linkedModels />
          <turnOffBones>
            <Item>steeringwheel</Item>
          </turnOffBones>
          <type>VMT_STEERING</type>
          <bone>steeringwheel</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>rt3000_stwheel15</modelName>
          <modShopLabel>SMOD2_STEER15</modShopLabel>
          <linkedModels />
          <turnOffBones>
            <Item>steeringwheel</Item>
          </turnOffBones>
          <type>VMT_STEERING</type>
          <bone>steeringwheel</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>rt3000_stwheel16</modelName>
          <modShopLabel>SMOD2_STEER16</modShopLabel>
          <linkedModels />
          <turnOffBones>
            <Item>steeringwheel</Item>
          </turnOffBones>
          <type>VMT_STEERING</type>
          <bone>steeringwheel</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>rt3000_mirr1a</modelName>
          <modShopLabel>RT3000_MIRRA</modShopLabel>
          <linkedModels>
          <Item>rt3000_mirr1b</Item>
          </linkedModels>
          <turnOffBones>
            <Item>misc_d</Item>
            <Item>misc_e</Item>
          </turnOffBones>
          <type>VMT_DOOR_L</type>
          <bone>door_dside_f</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>rt3000_mirr2a</modelName>
          <modShopLabel>RT3000_MIRRB</modShopLabel>
          <linkedModels>
          <Item>rt3000_mirr2b</Item>
          </linkedModels>
          <turnOffBones>
            <Item>misc_d</Item>
            <Item>misc_e</Item>
          </turnOffBones>
          <type>VMT_DOOR_L</type>
          <bone>door_dside_f</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
	</visibleMods>	
	<linkMods>
		<Item>
          <modelName>rt3000_hlight_rr</modelName>
          <bone>headlight_r</bone>
          <turnOffExtra value="false" />
        </Item>
		<Item>
          <modelName>rt3000_hlight_rg</modelName>
          <bone>headlight_r</bone>
          <turnOffExtra value="false" />
        </Item>
		<Item>
          <modelName>rt3000_hlight_rb</modelName>
          <bone>headlight_r</bone>
          <turnOffExtra value="false" />
        </Item>
		<Item>
          <modelName>rt3000_hlight_ry</modelName>
          <bone>headlight_r</bone>
          <turnOffExtra value="false" />
        </Item>
		<Item>
          <modelName>rt3000_hlight_r</modelName>
          <bone>headlight_r</bone>
          <turnOffExtra value="false" />
        </Item>
		<Item>
          <modelName>rt3000_mirr1b</modelName>
          <bone>door_pside_f</bone>
          <turnOffExtra value="false" />
        </Item>
		<Item>
          <modelName>rt3000_mirr2b</modelName>
          <bone>door_pside_f</bone>
          <turnOffExtra value="false" />
        </Item>
		<Item>
          <modelName>rt3000_roofcov</modelName>
          <bone>chassis</bone>
          <turnOffExtra value="false" />
        </Item>
		<Item>
          <modelName>rt3000_fend1b</modelName>
          <bone>wing_rf</bone>
          <turnOffExtra value="false" />
        </Item>	
		<Item>
          <modelName>rt3000_fend2b</modelName>
          <bone>wing_rf</bone>
          <turnOffExtra value="false" />
        </Item>	
		<Item>
          <modelName>rt3000_fend3b</modelName>
          <bone>wing_rf</bone>
          <turnOffExtra value="false" />
        </Item>	
		<Item>
          <modelName>rt3000_fend4b</modelName>
          <bone>wing_rf</bone>
          <turnOffExtra value="false" />
        </Item>	
		<Item>
          <modelName>rt3000_fend5b</modelName>
          <bone>wing_rf</bone>
          <turnOffExtra value="false" />
        </Item>	
		<Item>
          <modelName>rt3000_fend6b</modelName>
          <bone>wing_rf</bone>
          <turnOffExtra value="false" />
        </Item>	
		<Item>
          <modelName>rt3000_fend7b</modelName>
          <bone>wing_rf</bone>
          <turnOffExtra value="false" />
        </Item>	
		<Item>
          <modelName>rt3000_fend8b</modelName>
          <bone>wing_rf</bone>
          <turnOffExtra value="false" />
        </Item>	
		<Item>
          <modelName>rt3000_boot</modelName>
          <bone>boot</bone>
          <turnOffExtra value="false" />
        </Item>	
		<Item>
          <modelName>rt3000_diffa</modelName>
          <bone>chassis</bone>
          <turnOffExtra value="false" />
        </Item>	
		<Item>
          <modelName>rt3000_diffb</modelName>
          <bone>chassis</bone>
          <turnOffExtra value="false" />
        </Item>	
		<Item>
          <modelName>rt3000_door1r</modelName>
          <bone>door_pside_f</bone>
          <turnOffExtra value="false" />
        </Item>	
		<Item>
          <modelName>rt3000_door2r</modelName>
          <bone>door_pside_f</bone>
          <turnOffExtra value="false" />
        </Item>	
		<Item>
          <modelName>rt3000_door3r</modelName>
          <bone>door_pside_f</bone>
          <turnOffExtra value="false" />
        </Item>	
		<Item>
          <modelName>rt3000_door4r</modelName>
          <bone>door_pside_f</bone>
          <turnOffExtra value="false" />
        </Item>	
		<Item>
          <modelName>rt3000_door5r</modelName>
          <bone>door_pside_f</bone>
          <turnOffExtra value="false" />
        </Item>	
		<Item>
          <modelName>rt3000_door6r</modelName>
          <bone>door_pside_f</bone>
          <turnOffExtra value="false" />
        </Item>	
		<Item>
          <modelName>rt3000_door7r</modelName>
          <bone>door_pside_f</bone>
          <turnOffExtra value="false" />
        </Item>	
		<Item>
          <modelName>rt3000_stockfilt</modelName>
          <bone>engineblock</bone>
          <turnOffExtra value="false" />
        </Item>
	</linkMods>
	  <statMods>
        <Item>
          <identifier />
          <modifier value="40" />
          <audioApply value="1.000000" />
          <weight value="20" />
          <type>VMT_ENGINE</type>
        </Item>
        <Item>
          <identifier />
          <modifier value="70" />
          <audioApply value="1.000000" />
          <weight value="20" />
          <type>VMT_ENGINE</type>
        </Item>
        <Item>
          <identifier />
          <modifier value="100" />
          <audioApply value="1.000000" />
          <weight value="20" />
          <type>VMT_ENGINE</type>
        </Item>
        <Item>
          <identifier />
          <modifier value="125" />
          <audioApply value="1.000000" />
          <weight value="20" />
          <type>VMT_ENGINE</type>
        </Item>
        <Item>
          <identifier />
          <modifier value="30" />
          <audioApply value="1.000000" />
          <weight value="5" />
          <type>VMT_BRAKES</type>
        </Item>
        <Item>
          <identifier />
          <modifier value="65" />
          <audioApply value="1.000000" />
          <weight value="5" />
          <type>VMT_BRAKES</type>
        </Item>
        <Item>
          <identifier />
          <modifier value="120" />
          <audioApply value="1.000000" />
          <weight value="5" />
          <type>VMT_BRAKES</type>
        </Item>
        <Item>
          <identifier />
          <modifier value="50" />
          <audioApply value="1.000000" />
          <weight value="5" />
          <type>VMT_GEARBOX</type>
        </Item>
        <Item>
          <identifier />
          <modifier value="100" />
          <audioApply value="1.000000" />
          <weight value="5" />
          <type>VMT_GEARBOX</type>
        </Item>
        <Item>
          <identifier />
          <modifier value="200" />
          <audioApply value="1.000000" />
          <weight value="5" />
          <type>VMT_GEARBOX</type>
        </Item>
        <Item>
          <identifier />
          <modifier value="15" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_SUSPENSION</type>
        </Item>
        <Item>
          <identifier />
          <modifier value="20" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_SUSPENSION</type>
        </Item>
		<Item>
          <identifier />
          <modifier value="40" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_SUSPENSION</type>
        </Item>
		<Item>
          <identifier />
          <modifier value="60" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_SUSPENSION</type>
        </Item>
        <Item>
          <identifier />
          <modifier value="20" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_ARMOUR</type>
        </Item>
        <Item>
          <identifier />
          <modifier value="40" />
          <audioApply value="1.000000" />
          <weight value="10" />
          <type>VMT_ARMOUR</type>
        </Item>
        <Item>
          <identifier />
          <modifier value="60" />
          <audioApply value="1.000000" />
          <weight value="20" />
          <type>VMT_ARMOUR</type>
        </Item>
        <Item>
          <identifier />
          <modifier value="80" />
          <audioApply value="1.000000" />
          <weight value="30" />
          <type>VMT_ARMOUR</type>
        </Item>
        <Item>
          <identifier />
          <modifier value="100" />
          <audioApply value="1.000000" />
          <weight value="40" />
          <type>VMT_ARMOUR</type>
        </Item>
        <Item>
          <identifier>HORN_TRUCK</identifier>
          <modifier value="1766676233" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>HORN_COP</identifier>
          <modifier value="2904189469" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>HORN_CLOWN</identifier>
          <modifier value="2543206147" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>HORN_MUSICAL_1</identifier>
          <modifier value="1732399718" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>HORN_MUSICAL_2</identifier>
          <modifier value="2046162893" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>HORN_MUSICAL_3</identifier>
          <modifier value="2194999691" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>HORN_MUSICAL_4</identifier>
          <modifier value="2508304100" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>HORN_MUSICAL_5</identifier>
          <modifier value="3707223535" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
		<Item>
          <identifier>HORN_SAD_TROMBONE</identifier>
          <modifier value="632950117" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>MUSICAL_HORN_BUSINESS_1</identifier>
          <modifier value="3628534289" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>MUSICAL_HORN_BUSINESS_2</identifier>
          <modifier value="3892554122" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>MUSICAL_HORN_BUSINESS_3</identifier>
          <modifier value="4112892878" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>MUSICAL_HORN_BUSINESS_4</identifier>
          <modifier value="116877169" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>MUSICAL_HORN_BUSINESS_5</identifier>
          <modifier value="2684983719" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>MUSICAL_HORN_BUSINESS_6</identifier>
          <modifier value="2982690084" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
		</Item>
		<Item>
          <identifier>MUSICAL_HORN_BUSINESS_7</identifier>
          <modifier value="3203290992" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
		</Item>
		<Item>
          <identifier>DLC_BUSI2_C_MAJOR_NOTES_C0</identifier>
          <modifier value="771284519" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
		</Item>
		<Item>
          <identifier>DLC_BUSI2_C_MAJOR_NOTES_D0</identifier>
          <modifier value="2586621229" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
		</Item>
		<Item>
          <identifier>DLC_BUSI2_C_MAJOR_NOTES_E0</identifier>
          <modifier value="283386134" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
		</Item>
		<Item>
          <identifier>DLC_BUSI2_C_MAJOR_NOTES_F0</identifier>
          <modifier value="3884502400" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
		</Item>
		<Item>
          <identifier>DLC_BUSI2_C_MAJOR_NOTES_G0</identifier>
          <modifier value="265723083" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
		</Item>
		<Item>
          <identifier>DLC_BUSI2_C_MAJOR_NOTES_A0</identifier>
          <modifier value="1746883687" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
		</Item>
		<Item>
          <identifier>DLC_BUSI2_C_MAJOR_NOTES_B0</identifier>
          <modifier value="1919870950" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
		</Item>
		<Item>
          <identifier>DLC_BUSI2_C_MAJOR_NOTES_C1</identifier>
          <modifier value="1085277077" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
		</Item>
        <Item>
          <identifier>HIPSTER_HORN_1</identifier>
          <modifier value="444549672" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>HIPSTER_HORN_2</identifier>
          <modifier value="1603064898" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>HIPSTER_HORN_3</identifier>
          <modifier value="240366033" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>HIPSTER_HORN_4</identifier>
          <modifier value="960137118" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>INDEP_HORN_1</identifier>
          <modifier value="3572144790" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>INDEP_HORN_2</identifier>
          <modifier value="3801396714" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>INDEP_HORN_3</identifier>
          <modifier value="2843657151" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>INDEP_HORN_4</identifier>
          <modifier value="3341811489" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>LUXE_HORN_1</identifier>
          <modifier value="3199657341" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
    <Item>
          <identifier>LUXE_HORN_2</identifier>
          <modifier value="2900378064" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
    <Item>
          <identifier>LUXE_HORN_3</identifier>
          <modifier value="3956195248" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
		<!-- PREVIEW HORN - FOR MOD SHOP USE ONLY -->
		<Item>
          <identifier>LUXORY_HORN_1</identifier>
          <modifier value="676333254" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
		<!-- PREVIEW HORN - FOR MOD SHOP USE ONLY -->
		<Item>
          <identifier>LUXURY_HORN_2</identifier>
          <modifier value="2099578296" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
		<!-- PREVIEW HORN - FOR MOD SHOP USE ONLY -->
		<Item>
          <identifier>LUXURY_HORN_3</identifier>
          <modifier value="1373384483" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
		<Item>
          <identifier>ORGAN_HORN_LOOP_01</identifier>
          <modifier value="2916775806" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
		<!-- PREVIEW HORN - FOR MOD SHOP USE ONLY -->
		<Item>
          <identifier>ORGAN_HORN_LOOP_01_PREVIEW</identifier>
          <modifier value="3714706952" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
		<Item>
          <identifier>ORGAN_HORN_LOOP_02</identifier>
          <modifier value="2611860261" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
		<!-- PREVIEW HORN - FOR MOD SHOP USE ONLY -->
		<Item>
          <identifier>ORGAN_HORN_LOOP_02_PREVIEW</identifier>
          <modifier value="3206770359" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
		<Item>
          <identifier>LOWRIDER_HORN_1</identifier>
          <modifier value="310529291" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
		<!-- PREVIEW HORN - FOR MOD SHOP USE ONLY -->
		<Item>
          <identifier>LOWRIDER_HORN_1_PREVIEW</identifier>
          <modifier value="2965568987" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
		<Item>
          <identifier>LOWRIDER_HORN_2</identifier>
          <modifier value="55291550" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
		<!-- PREVIEW HORN - FOR MOD SHOP USE ONLY -->
		<Item>
          <identifier>LOWRIDER_HORN_2_PREVIEW</identifier>
          <modifier value="965054819" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
		<Item>
          <identifier>XM15_HORN_01</identifier>
          <modifier value="55862314" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
		<!-- PREVIEW HORN - FOR MOD SHOP USE ONLY -->
		<Item>
          <identifier>XM15_HORN_01_PREVIEW</identifier>
          <modifier value="2156743178" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
		<Item>
          <identifier>XM15_HORN_02</identifier>
          <modifier value="400002352" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
		<!-- PREVIEW HORN - FOR MOD SHOP USE ONLY -->
		<Item>
          <identifier>XM15_HORN_02_PREVIEW</identifier>
          <modifier value="897484282" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
		<Item>
          <identifier>XM15_HORN_03</identifier>
          <modifier value="560832604" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
		<!-- PREVIEW HORN - FOR MOD SHOP USE ONLY -->
		<Item>
          <identifier>XM15_HORN_03_PREVIEW</identifier>
          <modifier value="314232747" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
   		<Item>
          <identifier>DLC_AW_Airhorn_01</identifier>
          <modifier value="3851180092" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
		<!-- PREVIEW HORN - FOR MOD SHOP USE ONLY -->
		<Item>
          <identifier>DLC_AW_Airhorn_01_Preview</identifier>
          <modifier value="246182814" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
		<Item>
          <identifier>DLC_AW_Airhorn_02</identifier>
          <modifier value="3412861948" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
		<!-- PREVIEW HORN - FOR MOD SHOP USE ONLY -->
		<Item>
          <identifier>DLC_AW_Airhorn_02_Preview</identifier>
          <modifier value="1804608241" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
		<Item>
          <identifier>DLC_AW_Airhorn_03</identifier>
          <modifier value="3374260066" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
		<!-- PREVIEW HORN - FOR MOD SHOP USE ONLY -->
		<Item>
          <identifier>DLC_AW_Airhorn_03_Preview</identifier>
          <modifier value="2798044638" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
      </statMods>
	   <slotNames>
		<Item>
          <slot>VMT_DOOR_L</slot>
          <name>TOP_MIR</name>
        </Item>
		<Item>
          <slot>VMT_PLTVANITY</slot>
          <name>TOP_SPLIT</name>
        </Item>
		<Item>
          <slot>VMT_INTERIOR1</slot>
          <name>TOP_SUNST</name>
        </Item>
		<Item>
          <slot>VMT_DOOR_R</slot>
          <name>TOP_AIR</name>
        </Item>
		<Item>
          <slot>VMT_CHASSIS2</slot>
          <name>TOP_HL_CV</name>
        </Item>
      </slotNames>
      <liveryNames />
    </Item>
  </Kits>
  <Lights>
      <Item>
      <id value="162" />
      <indicator>
        <intensity value="0.375000" />
        <falloffMax value="2.500000" />
        <falloffExponent value="8.000000" />
        <innerConeAngle value="30.000000" />
        <outerConeAngle value="80.000000" />
        <emmissiveBoost value="false" />
        <color value="0xFFFF8000" />
        <textureName />
        <mirrorTexture value="true" />
      </indicator>
      <rearIndicatorCorona>
        <size value="0.000000" />
        <size_far value="0.000000" />
        <intensity value="0.000000" />
        <intensity_far value="0.000000" />
        <color value="0x00000000" />
        <numCoronas value="1" />
        <distBetweenCoronas value="128" />
        <distBetweenCoronas_far value="255" />
        <xRotation value="0.000000" />
        <yRotation value="0.000000" />
        <zRotation value="0.000000" />
        <zBias value="0.250000" />
        <pullCoronaIn value="false" />
      </rearIndicatorCorona>
      <frontIndicatorCorona>
        <size value="0.000000" />
        <size_far value="0.000000" />
        <intensity value="0.000000" />
        <intensity_far value="0.000000" />
        <color value="0x00000000" />
        <numCoronas value="1" />
        <distBetweenCoronas value="128" />
        <distBetweenCoronas_far value="255" />
        <xRotation value="0.000000" />
        <yRotation value="0.000000" />
        <zRotation value="0.000000" />
        <zBias value="0.250000" />
        <pullCoronaIn value="false" />
      </frontIndicatorCorona>
      <tailLight>
        <intensity value="0.250000" />
        <falloffMax value="4.000000" />
        <falloffExponent value="16.000000" />
        <innerConeAngle value="45.000000" />
        <outerConeAngle value="90.000000" />
        <emmissiveBoost value="false" />
        <color value="0xFFFF0000" />
        <textureName />
        <mirrorTexture value="true" />
      </tailLight>
      <tailLightCorona>
        <size value="0.000000" />
        <size_far value="8.000000" />
        <intensity value="4.000000" />
        <intensity_far value="1.000000" />
        <color value="0xFFFF1E05" />
        <numCoronas value="1" />
        <distBetweenCoronas value="128" />
        <distBetweenCoronas_far value="255" />
        <xRotation value="0.000000" />
        <yRotation value="0.000000" />
        <zRotation value="0.000000" />
        <zBias value="0.250000" />
        <pullCoronaIn value="false" />
      </tailLightCorona>
      <tailLightMiddleCorona>
        <size value="0.000000" />
        <size_far value="0.000000" />
        <intensity value="0.000000" />
        <intensity_far value="0.000000" />
        <color value="0x00000000" />
        <numCoronas value="1" />
        <distBetweenCoronas value="128" />
        <distBetweenCoronas_far value="255" />
        <xRotation value="0.000000" />
        <yRotation value="0.000000" />
        <zRotation value="0.000000" />
        <zBias value="0.250000" />
        <pullCoronaIn value="false" />
      </tailLightMiddleCorona>
      <headLight>
        <intensity value="1.000000" />
        <falloffMax value="35.000000" />
        <falloffExponent value="16.000000" />
        <innerConeAngle value="0.000000" />
        <outerConeAngle value="60.209999" />
        <emmissiveBoost value="false" />
        <color value="0xFFFFFFCC" />
        <textureName>VehicleLight_car_LED1</textureName>
        <mirrorTexture value="false" />
      </headLight>
      <headLightCorona>
        <size value="0.600000" />
        <size_far value="0.600000" />
        <intensity value="5.000000" />
        <intensity_far value="1.000000" />
        <color value="0xFFFFFFFF" />
        <numCoronas value="2" />
        <distBetweenCoronas value="1" />
        <distBetweenCoronas_far value="160" />
        <xRotation value="0.000000" />
        <yRotation value="0.000000" />
        <zRotation value="0.270000" />
        <zBias value="0.250000" />
        <pullCoronaIn value="false" />
      </headLightCorona>
      <reversingLight>
        <intensity value="0.500000" />
        <falloffMax value="4.000000" />
        <falloffExponent value="32.000000" />
        <innerConeAngle value="45.000000" />
        <outerConeAngle value="90.000000" />
        <emmissiveBoost value="false" />
        <color value="0xFFFFFFFF" />
        <textureName />
        <mirrorTexture value="true" />
      </reversingLight>
      <reversingLightCorona>
        <size value="0.800000" />
        <size_far value="2.000000" />
        <intensity value="1.500000" />
        <intensity_far value="1.000000" />
        <color value="0x00F7F7F7" />
        <numCoronas value="1" />
        <distBetweenCoronas value="128" />
        <distBetweenCoronas_far value="255" />
        <xRotation value="0.000000" />
        <yRotation value="0.000000" />
        <zRotation value="0.000000" />
        <zBias value="0.250000" />
        <pullCoronaIn value="false" />
      </reversingLightCorona>
      <name>dominator7</name>
    </Item>
	<Item>
      <id value="171" />	
      <indicator>
        <intensity value="0.375000" />
        <falloffMax value="2.500000" />
        <falloffExponent value="8.000000" />
        <innerConeAngle value="30.000000" />
        <outerConeAngle value="80.000000" />
        <emmissiveBoost value="false" />
        <color value="0xFFFF8000" />
      </indicator>
      <rearIndicatorCorona>
        <size value="0.000000" />
        <size_far value="0.000000" />
        <intensity value="0.000000" />
        <intensity_far value="0.000000" />
        <color value="0x00000000" />
        <numCoronas value="1" />
        <distBetweenCoronas value="128" />
        <distBetweenCoronas_far value="255" />
        <xRotation value="0.000000" />
        <yRotation value="0.000000" />
        <zRotation value="0.000000" />
        <zBias value="0.250000" />
        <pullCoronaIn value="false" />
      </rearIndicatorCorona>
      <frontIndicatorCorona>
        <size value="0.000000" />
        <size_far value="0.000000" />
        <intensity value="0.000000" />
        <intensity_far value="0.000000" />
        <color value="0x00000000" />
        <numCoronas value="1" />
        <distBetweenCoronas value="128" />
        <distBetweenCoronas_far value="255" />
        <xRotation value="0.000000" />
        <yRotation value="0.000000" />
        <zRotation value="0.000000" />
        <zBias value="0.250000" />
        <pullCoronaIn value="false" />
      </frontIndicatorCorona>
      <tailLight>
        <intensity value="0.250000" />
        <falloffMax value="4.000000" />
        <falloffExponent value="16.000000" />
        <innerConeAngle value="45.000000" />
        <outerConeAngle value="90.000000" />
        <emmissiveBoost value="false" />
        <color value="0xFFFF0000" />
      </tailLight>
      <tailLightCorona>
        <size value="0.000000" />
        <size_far value="5.000000" />
        <intensity value="4.000000" />
        <intensity_far value="1.000000" />
        <color value="0xFFFF1E05" />
        <numCoronas value="2" />
        <distBetweenCoronas value="26" />
        <distBetweenCoronas_far value="1" />
        <xRotation value="0.000000" />
        <yRotation value="0.000000" />
        <zRotation value="5.667000" />
        <zBias value="0.250000" />
        <pullCoronaIn value="false" />
      </tailLightCorona>
      <tailLightMiddleCorona>
        <size value="0.000000" />
        <size_far value="0.000000" />
        <intensity value="0.000000" />
        <intensity_far value="0.000000" />
        <color value="0x00000000" />
        <numCoronas value="1" />
        <distBetweenCoronas value="128" />
        <distBetweenCoronas_far value="255" />
        <xRotation value="0.000000" />
        <yRotation value="0.000000" />
        <zRotation value="0.000000" />
        <zBias value="0.250000" />
        <pullCoronaIn value="false" />
      </tailLightMiddleCorona>
      <headLight>
        <intensity value="1.000000" />
        <falloffMax value="35.000000" />
        <falloffExponent value="16.000000" />
        <innerConeAngle value="0.000000" />
        <outerConeAngle value="60.209999" />
        <emmissiveBoost value="false" />
        <color value="0xFFFFFFCC" />
        <textureName>VehicleLight_car_standardmodern</textureName>
        <mirrorTexture value="false" />
      </headLight>
      <headLightCorona>
        <size value="0.100000" />
        <size_far value="10.000000" />
        <intensity value="5.000000" />
        <intensity_far value="1.000000" />
        <color value="0xFFFFFFFF" />
        <numCoronas value="1" />
        <distBetweenCoronas value="128" />
        <distBetweenCoronas_far value="255" />
        <xRotation value="0.000000" />
        <yRotation value="0.000000" />
        <zRotation value="0.000000" />
        <zBias value="0.250000" />
        <pullCoronaIn value="false" />
      </headLightCorona>
      <reversingLight>
        <intensity value="0.500000" />
        <falloffMax value="4.000000" />
        <falloffExponent value="32.000000" />
        <innerConeAngle value="45.000000" />
        <outerConeAngle value="90.000000" />
        <emmissiveBoost value="false" />
        <color value="0xFFFFFFFF" />
      </reversingLight>
      <reversingLightCorona>
        <size value="0.800000" />
        <size_far value="2.000000" />
        <intensity value="1.500000" />
        <intensity_far value="1.000000" />
        <color value="0x00F7F7F7" />
        <numCoronas value="1" />
        <distBetweenCoronas value="128" />
        <distBetweenCoronas_far value="255" />
        <xRotation value="0.000000" />
        <yRotation value="0.000000" />
        <zRotation value="0.000000" />
        <zBias value="0.250000" />
        <pullCoronaIn value="false" />
      </reversingLightCorona>
      <name>growler</name>
    </Item>
	 <Item>
      <id value="163" />
      <indicator>
        <intensity value="0.375000" />
        <falloffMax value="2.500000" />
        <falloffExponent value="8.000000" />
        <innerConeAngle value="30.000000" />
        <outerConeAngle value="80.000000" />
        <emmissiveBoost value="false" />
        <color value="0xFFFF8000" />
        <textureName />
        <mirrorTexture value="true" />
      </indicator>
      <rearIndicatorCorona>
        <size value="0.000000" />
        <size_far value="0.000000" />
        <intensity value="0.000000" />
        <intensity_far value="0.000000" />
        <color value="0x00000000" />
        <numCoronas value="1" />
        <distBetweenCoronas value="128" />
        <distBetweenCoronas_far value="255" />
        <xRotation value="0.000000" />
        <yRotation value="0.000000" />
        <zRotation value="0.000000" />
        <zBias value="0.250000" />
        <pullCoronaIn value="false" />
      </rearIndicatorCorona>
      <frontIndicatorCorona>
        <size value="0.000000" />
        <size_far value="0.000000" />
        <intensity value="0.000000" />
        <intensity_far value="0.000000" />
        <color value="0x00000000" />
        <numCoronas value="1" />
        <distBetweenCoronas value="128" />
        <distBetweenCoronas_far value="255" />
        <xRotation value="0.000000" />
        <yRotation value="0.000000" />
        <zRotation value="0.000000" />
        <zBias value="0.250000" />
        <pullCoronaIn value="false" />
      </frontIndicatorCorona>
      <tailLight>
        <intensity value="0.60000" />
        <falloffMax value="3.500000" />
        <falloffExponent value="42.240000" />
        <innerConeAngle value="45.000000" />
        <outerConeAngle value="90.000000" />
        <emmissiveBoost value="false" />
        <color value="0xFFFF0000" />
        <textureName />
        <mirrorTexture value="true" />
      </tailLight>
      <tailLightCorona>
        <size value="0.000000" />
        <size_far value="0.000000" />
        <intensity value="0.000000" />
        <intensity_far value="0.000000" />
        <color value="0xFFFF1E05" />
        <numCoronas value="1" />
        <distBetweenCoronas value="128" />
        <distBetweenCoronas_far value="255" />
        <xRotation value="0.000000" />
        <yRotation value="0.000000" />
        <zRotation value="0.000000" />
        <zBias value="0.250000" />
        <pullCoronaIn value="false" />
      </tailLightCorona>
      <tailLightMiddleCorona>
        <size value="0.000000" />
        <size_far value="0.000000" />
        <intensity value="0.000000" />
        <intensity_far value="0.000000" />
        <color value="0x00000000" />
        <numCoronas value="1" />
        <distBetweenCoronas value="128" />
        <distBetweenCoronas_far value="255" />
        <xRotation value="0.000000" />
        <yRotation value="0.000000" />
        <zRotation value="0.000000" />
        <zBias value="0.250000" />
        <pullCoronaIn value="false" />
      </tailLightMiddleCorona>
      <headLight>
        <intensity value="1.000000" />
        <falloffMax value="9.700000" />
        <falloffExponent value="60.000000" />
        <innerConeAngle value="60.000000" />
        <outerConeAngle value="65.000000" />
        <emmissiveBoost value="false" />
        <color value="0xFFFFFFCC" />
        <textureName>VehicleLight_car_LED1</textureName>
        <mirrorTexture value="false" />
      </headLight>
      <headLightCorona>
        <size value="0.600000" />
        <size_far value="0.600000" />
        <intensity value="5.000000" />
        <intensity_far value="5.700000" />
        <color value="0xFFFFFFFF" />
        <numCoronas value="1" />
        <distBetweenCoronas value="1" />
        <distBetweenCoronas_far value="160" />
        <xRotation value="0.000000" />
        <yRotation value="0.000000" />
        <zRotation value="0.000000" />
        <zBias value="0.250000" />
        <pullCoronaIn value="false" />
      </headLightCorona>
      <reversingLight>
        <intensity value="0.500000" />
        <falloffMax value="4.000000" />
        <falloffExponent value="32.000000" />
        <innerConeAngle value="45.000000" />
        <outerConeAngle value="90.000000" />
        <emmissiveBoost value="false" />
        <color value="0xFFFFFFFF" />
        <textureName />
        <mirrorTexture value="true" />
      </reversingLight>
      <reversingLightCorona>
        <size value="0.000000" />
        <size_far value="0.000000" />
        <intensity value="1.500000" />
        <intensity_far value="1.000000" />
        <color value="0x00F7F7F7" />
        <numCoronas value="1" />
        <distBetweenCoronas value="128" />
        <distBetweenCoronas_far value="255" />
        <xRotation value="0.000000" />
        <yRotation value="0.000000" />
        <zRotation value="0.000000" />
        <zBias value="0.250000" />
        <pullCoronaIn value="false" />
      </reversingLightCorona>
      <name>jester4</name>
    </Item>
    <Item>
      <id value="164" />
      <indicator>
        <intensity value="0.375000" />
        <falloffMax value="2.500000" />
        <falloffExponent value="8.000000" />
        <innerConeAngle value="20.000000" />
        <outerConeAngle value="50.000000" />
        <emmissiveBoost value="false" />
        <color value="0xFFFF7300" />
        <textureName />
        <mirrorTexture value="true" />
      </indicator>
      <rearIndicatorCorona>
        <size value="0.000000" />
        <size_far value="0.000000" />
        <intensity value="0.000000" />
        <intensity_far value="0.000000" />
        <color value="0x00000000" />
        <numCoronas value="1" />
        <distBetweenCoronas value="128" />
        <distBetweenCoronas_far value="255" />
        <xRotation value="0.000000" />
        <yRotation value="0.000000" />
        <zRotation value="0.000000" />
        <zBias value="0.250000" />
        <pullCoronaIn value="false" />
      </rearIndicatorCorona>
      <frontIndicatorCorona>
        <size value="0.000000" />
        <size_far value="0.000000" />
        <intensity value="0.000000" />
        <intensity_far value="0.000000" />
        <color value="0x00000000" />
        <numCoronas value="1" />
        <distBetweenCoronas value="128" />
        <distBetweenCoronas_far value="255" />
        <xRotation value="0.000000" />
        <yRotation value="0.000000" />
        <zRotation value="0.000000" />
        <zBias value="0.250000" />
        <pullCoronaIn value="false" />
      </frontIndicatorCorona>
      <tailLight>
        <intensity value="0.250000" />
        <falloffMax value="4.000000" />
        <falloffExponent value="16.000000" />
        <innerConeAngle value="45.000000" />
        <outerConeAngle value="90.000000" />
        <emmissiveBoost value="false" />
        <color value="0xFFFF0000" />
        <textureName />
        <mirrorTexture value="true" />
      </tailLight>
      <tailLightCorona>
        <size value="0.000000" />
        <size_far value="2.500000" />
        <intensity value="5.000000" />
        <intensity_far value="1.000000" />
        <color value="0xFFFF0F05" />
        <numCoronas value="1" />
        <distBetweenCoronas value="128" />
        <distBetweenCoronas_far value="255" />
        <xRotation value="0.000000" />
        <yRotation value="0.000000" />
        <zRotation value="0.000000" />
        <zBias value="0.250000" />
        <pullCoronaIn value="false" />
      </tailLightCorona>
      <tailLightMiddleCorona>
        <size value="0.000000" />
        <size_far value="0.000000" />
        <intensity value="0.000000" />
        <intensity_far value="0.000000" />
        <color value="0x00000000" />
        <numCoronas value="1" />
        <distBetweenCoronas value="128" />
        <distBetweenCoronas_far value="255" />
        <xRotation value="0.000000" />
        <yRotation value="0.000000" />
        <zRotation value="0.000000" />
        <zBias value="0.250000" />
        <pullCoronaIn value="false" />
      </tailLightMiddleCorona>
      <headLight>
        <intensity value="1.500000" />
        <falloffMax value="35.000000" />
        <falloffExponent value="16.000000" />
        <innerConeAngle value="0.000000" />
        <outerConeAngle value="60.000000" />
        <emmissiveBoost value="false" />
        <color value="0xFF7FA7E3" />
        <textureName>VehicleLight_car_standardmodern</textureName>
        <mirrorTexture value="false" />
      </headLight>
      <headLightCorona>
        <size value="0.100000" />
        <size_far value="7.000000" />
        <intensity value="7.000000" />
        <intensity_far value="5.000000" />
        <color value="0xFF61A5FF" />
        <numCoronas value="2" />
        <distBetweenCoronas value="27" />
        <distBetweenCoronas_far value="101" />
        <xRotation value="0.000000" />
        <yRotation value="0.119000" />
        <zRotation value="0.527788" />
        <zBias value="0.250000" />
        <pullCoronaIn value="false" />
      </headLightCorona>
      <reversingLight>
        <intensity value="0.500000" />
        <falloffMax value="7.200000" />
        <falloffExponent value="32.000000" />
        <innerConeAngle value="20.000000" />
        <outerConeAngle value="78.000000" />
        <emmissiveBoost value="false" />
        <color value="0xFFFFFFFF" />
        <textureName />
        <mirrorTexture value="true" />
      </reversingLight>
      <reversingLightCorona>
        <size value="0.800000" />
        <size_far value="2.000000" />
        <intensity value="1.500000" />
        <intensity_far value="1.000000" />
        <color value="0x00F7F7F7" />
        <numCoronas value="1" />
        <distBetweenCoronas value="128" />
        <distBetweenCoronas_far value="255" />
        <xRotation value="0.000000" />
        <yRotation value="0.000000" />
        <zRotation value="0.000000" />
        <zBias value="0.250000" />
        <pullCoronaIn value="false" />
      </reversingLightCorona>
      <name>tailgater2</name>
    </Item>
    <Item>
      <id value="165" />
      <indicator>
        <intensity value="0.375000" />
        <falloffMax value="2.500000" />
        <falloffExponent value="8.000000" />
        <innerConeAngle value="30.000000" />
        <outerConeAngle value="80.000000" />
        <emmissiveBoost value="false" />
        <color value="0xFFFF8000" />
        <textureName />
        <mirrorTexture value="true" />
      </indicator>
      <rearIndicatorCorona>
        <size value="0.000000" />
        <size_far value="0.000000" />
        <intensity value="0.000000" />
        <intensity_far value="0.000000" />
        <color value="0x00000000" />
        <numCoronas value="1" />
        <distBetweenCoronas value="128" />
        <distBetweenCoronas_far value="255" />
        <xRotation value="0.000000" />
        <yRotation value="0.000000" />
        <zRotation value="0.000000" />
        <zBias value="0.250000" />
        <pullCoronaIn value="false" />
      </rearIndicatorCorona>
      <frontIndicatorCorona>
        <size value="0.000000" />
        <size_far value="0.000000" />
        <intensity value="0.000000" />
        <intensity_far value="0.000000" />
        <color value="0x00000000" />
        <numCoronas value="1" />
        <distBetweenCoronas value="128" />
        <distBetweenCoronas_far value="255" />
        <xRotation value="0.000000" />
        <yRotation value="0.000000" />
        <zRotation value="0.000000" />
        <zBias value="0.250000" />
        <pullCoronaIn value="false" />
      </frontIndicatorCorona>
      <tailLight>
        <intensity value="0.250000" />
        <falloffMax value="4.000000" />
        <falloffExponent value="16.000000" />
        <innerConeAngle value="45.000000" />
        <outerConeAngle value="90.000000" />
        <emmissiveBoost value="false" />
        <color value="0xFFFF0000" />
        <textureName />
        <mirrorTexture value="true" />
      </tailLight>
      <tailLightCorona>
        <size value="0.000000" />
        <size_far value="0.000000" />
        <intensity value="4.000000" />
        <intensity_far value="1.000000" />
        <color value="0xFFFF1E05" />
        <numCoronas value="1" />
        <distBetweenCoronas value="128" />
        <distBetweenCoronas_far value="255" />
        <xRotation value="0.000000" />
        <yRotation value="0.000000" />
        <zRotation value="0.000000" />
        <zBias value="0.250000" />
        <pullCoronaIn value="false" />
      </tailLightCorona>
      <tailLightMiddleCorona>
        <size value="0.000000" />
        <size_far value="0.000000" />
        <intensity value="0.000000" />
        <intensity_far value="0.000000" />
        <color value="0x00000000" />
        <numCoronas value="1" />
        <distBetweenCoronas value="128" />
        <distBetweenCoronas_far value="255" />
        <xRotation value="0.000000" />
        <yRotation value="0.000000" />
        <zRotation value="0.000000" />
        <zBias value="0.250000" />
        <pullCoronaIn value="false" />
      </tailLightMiddleCorona>
      <headLight>
        <intensity value="1.000000" />
        <falloffMax value="20.000000" />
        <falloffExponent value="16.000000" />
        <innerConeAngle value="3.000000" />
        <outerConeAngle value="62.000000" />
        <emmissiveBoost value="false" />
        <color value="0xFFFFFFCC" />
        <textureName>VehicleLight_car_LED1</textureName>
        <mirrorTexture value="false" />
      </headLight>
      <headLightCorona>
        <size value="0.600000" />
        <size_far value="0.600000" />
        <intensity value="5.000000" />
        <intensity_far value="1.000000" />
        <color value="0xFFFFFFFF" />
        <numCoronas value="2" />
        <distBetweenCoronas value="1" />
        <distBetweenCoronas_far value="160" />
        <xRotation value="0.000000" />
        <yRotation value="0.000000" />
        <zRotation value="0.270000" />
        <zBias value="0.250000" />
        <pullCoronaIn value="false" />
      </headLightCorona>
      <reversingLight>
        <intensity value="0.500000" />
        <falloffMax value="4.000000" />
        <falloffExponent value="32.000000" />
        <innerConeAngle value="45.000000" />
        <outerConeAngle value="90.000000" />
        <emmissiveBoost value="false" />
        <color value="0xFFFFFFFF" />
        <textureName />
        <mirrorTexture value="true" />
      </reversingLight>
      <reversingLightCorona>
        <size value="0.800000" />
        <size_far value="2.000000" />
        <intensity value="1.500000" />
        <intensity_far value="1.000000" />
        <color value="0x00F7F7F7" />
        <numCoronas value="1" />
        <distBetweenCoronas value="128" />
        <distBetweenCoronas_far value="255" />
        <xRotation value="0.000000" />
        <yRotation value="0.000000" />
        <zRotation value="0.000000" />
        <zBias value="0.250000" />
        <pullCoronaIn value="false" />
      </reversingLightCorona>
      <name>vectre</name>
    </Item>
    <Item>
      <id value="166" />
      <indicator>
        <intensity value="0.375000" />
        <falloffMax value="2.500000" />
        <falloffExponent value="8.000000" />
        <innerConeAngle value="30.000000" />
        <outerConeAngle value="80.000000" />
        <emmissiveBoost value="false" />
        <color value="0xFFFF8000" />
        <textureName />
        <mirrorTexture value="true" />
      </indicator>
      <rearIndicatorCorona>
        <size value="0.000000" />
        <size_far value="0.000000" />
        <intensity value="0.000000" />
        <intensity_far value="0.000000" />
        <color value="0x00000000" />
        <numCoronas value="1" />
        <distBetweenCoronas value="128" />
        <distBetweenCoronas_far value="255" />
        <xRotation value="0.000000" />
        <yRotation value="0.000000" />
        <zRotation value="0.000000" />
        <zBias value="0.250000" />
        <pullCoronaIn value="false" />
      </rearIndicatorCorona>
      <frontIndicatorCorona>
        <size value="0.000000" />
        <size_far value="0.000000" />
        <intensity value="0.000000" />
        <intensity_far value="0.000000" />
        <color value="0x00000000" />
        <numCoronas value="1" />
        <distBetweenCoronas value="128" />
        <distBetweenCoronas_far value="255" />
        <xRotation value="0.000000" />
        <yRotation value="0.000000" />
        <zRotation value="0.000000" />
        <zBias value="0.250000" />
        <pullCoronaIn value="false" />
      </frontIndicatorCorona>
      <tailLight>
        <intensity value="0.250000" />
        <falloffMax value="4.000000" />
        <falloffExponent value="16.000000" />
        <innerConeAngle value="45.000000" />
        <outerConeAngle value="90.000000" />
        <emmissiveBoost value="false" />
        <color value="0xFFFF0000" />
        <textureName />
        <mirrorTexture value="true" />
      </tailLight>
      <tailLightCorona>
        <size value="0.000000" />
        <size_far value="3
		.000000" />
        <intensity value="4.000000" />
        <intensity_far value="1.000000" />
        <color value="0xFFFF1E05" />
        <numCoronas value="2" />
        <distBetweenCoronas value="1" />
        <distBetweenCoronas_far value="170" />
        <xRotation value="0.000000" />
        <yRotation value="1.500000" />
        <zRotation value="5.500000" />
        <zBias value="0.250000" />
        <pullCoronaIn value="false" />
      </tailLightCorona>
      <tailLightMiddleCorona>
        <size value="0.000000" />
        <size_far value="0.000000" />
        <intensity value="0.000000" />
        <intensity_far value="0.000000" />
        <color value="0x00000000" />
        <numCoronas value="1" />
        <distBetweenCoronas value="128" />
        <distBetweenCoronas_far value="255" />
        <xRotation value="0.000000" />
        <yRotation value="0.000000" />
        <zRotation value="0.000000" />
        <zBias value="0.250000" />
        <pullCoronaIn value="false" />
      </tailLightMiddleCorona>
      <headLight>
        <intensity value="1.000000" />
        <falloffMax value="35.000000" />
        <falloffExponent value="16.000000" />
        <innerConeAngle value="0.000000" />
        <outerConeAngle value="60.209999" />
        <emmissiveBoost value="false" />
        <color value="0xFFFFFFCC" />
        <textureName>VehicleLight_car_LED1</textureName>
        <mirrorTexture value="false" />
      </headLight>
      <headLightCorona>
        <size value="0.600000" />
        <size_far value="0.600000" />
        <intensity value="5.000000" />
        <intensity_far value="1.000000" />
        <color value="0xFFFFFFFF" />
        <numCoronas value="2" />
        <distBetweenCoronas value="1" />
        <distBetweenCoronas_far value="230" />
        <xRotation value="0.000000" />
        <yRotation value="1.130000" />
        <zRotation value="1.030000" />
        <zBias value="0.250000" />
        <pullCoronaIn value="false" />
      </headLightCorona>
      <reversingLight>
        <intensity value="0.500000" />
        <falloffMax value="4.000000" />
        <falloffExponent value="32.000000" />
        <innerConeAngle value="45.000000" />
        <outerConeAngle value="90.000000" />
        <emmissiveBoost value="false" />
        <color value="0xFFFFFFFF" />
        <textureName />
        <mirrorTexture value="true" />
      </reversingLight>
      <reversingLightCorona>
        <size value="0.800000" />
        <size_far value="2.000000" />
        <intensity value="1.500000" />
        <intensity_far value="1.000000" />
        <color value="0x00F7F7F7" />
        <numCoronas value="1" />
        <distBetweenCoronas value="128" />
        <distBetweenCoronas_far value="255" />
        <xRotation value="0.000000" />
        <yRotation value="0.000000" />
        <zRotation value="0.000000" />
        <zBias value="0.250000" />
        <pullCoronaIn value="false" />
      </reversingLightCorona>
      <name>euros</name>
    </Item>
    <Item>
      <id value="167" />
      <indicator>
        <intensity value="0.375000" />
        <falloffMax value="2.500000" />
        <falloffExponent value="8.000000" />
        <innerConeAngle value="20.000000" />
        <outerConeAngle value="50.000000" />
        <emmissiveBoost value="false" />
        <color value="0xFFFF8000" />
        <textureName />
        <mirrorTexture value="true" />
      </indicator>
      <rearIndicatorCorona>
        <size value="0.000000" />
        <size_far value="0.000000" />
        <intensity value="0.000000" />
        <intensity_far value="0.000000" />
        <color value="0x00000000" />
        <numCoronas value="1" />
        <distBetweenCoronas value="128" />
        <distBetweenCoronas_far value="255" />
        <xRotation value="0.000000" />
        <yRotation value="0.000000" />
        <zRotation value="0.000000" />
        <zBias value="0.250000" />
        <pullCoronaIn value="false" />
      </rearIndicatorCorona>
      <frontIndicatorCorona>
        <size value="0.000000" />
        <size_far value="0.000000" />
        <intensity value="0.000000" />
        <intensity_far value="0.000000" />
        <color value="0x00000000" />
        <numCoronas value="1" />
        <distBetweenCoronas value="128" />
        <distBetweenCoronas_far value="255" />
        <xRotation value="0.000000" />
        <yRotation value="0.000000" />
        <zRotation value="0.000000" />
        <zBias value="0.250000" />
        <pullCoronaIn value="false" />
      </frontIndicatorCorona>
      <tailLight>
        <intensity value="0.250000" />
        <falloffMax value="4.000000" />
        <falloffExponent value="16.000000" />
        <innerConeAngle value="45.000000" />
        <outerConeAngle value="90.000000" />
        <emmissiveBoost value="false" />
        <color value="0xFFFF0A01" />
        <textureName />
        <mirrorTexture value="true" />
      </tailLight>
      <tailLightCorona>
        <size value="0.000000" />
        <size_far value="8.000000" />
        <intensity value="4.000000" />
        <intensity_far value="1.000000" />
        <color value="0xFFFF2D05" />
        <numCoronas value="3" />
        <distBetweenCoronas value="17" />
        <distBetweenCoronas_far value="1" />
        <xRotation value="0.000000" />
        <yRotation value="0.000000" />
        <zRotation value="0.000000" />
        <zBias value="0.250000" />
        <pullCoronaIn value="false" />
      </tailLightCorona>
      <tailLightMiddleCorona>
        <size value="0.000000" />
        <size_far value="0.000000" />
        <intensity value="0.000000" />
        <intensity_far value="0.000000" />
        <color value="0x00000000" />
        <numCoronas value="1" />
        <distBetweenCoronas value="128" />
        <distBetweenCoronas_far value="255" />
        <xRotation value="0.000000" />
        <yRotation value="0.000000" />
        <zRotation value="0.000000" />
        <zBias value="0.250000" />
        <pullCoronaIn value="false" />
      </tailLightMiddleCorona>
      <headLight>
        <intensity value="1.000000" />
        <falloffMax value="35.000000" />
        <falloffExponent value="16.000000" />
        <innerConeAngle value="0.000000" />
        <outerConeAngle value="60.000000" />
        <emmissiveBoost value="false" />
        <color value="0xFFFFD859" />
        <textureName>VehicleLight_car_standardmodern</textureName>
        <mirrorTexture value="false" />
      </headLight>
      <headLightCorona>
        <size value="0.100000" />
        <size_far value="10.000000" />
        <intensity value="5.000000" />
        <intensity_far value="1.000000" />
        <color value="0xFFFCBB42" />
        <numCoronas value="1" />
        <distBetweenCoronas value="37" />
        <distBetweenCoronas_far value="122" />
        <xRotation value="0.000000" />
        <yRotation value="0.000000" />
        <zRotation value="0.000000" />
        <zBias value="0.250000" />
        <pullCoronaIn value="false" />
      </headLightCorona>
      <reversingLight>
        <intensity value="0.350000" />
        <falloffMax value="20.000000" />
        <falloffExponent value="32.000000" />
        <innerConeAngle value="20.000000" />
        <outerConeAngle value="90.000000" />
        <emmissiveBoost value="false" />
        <color value="0xFFFFFFFF" />
        <textureName />
        <mirrorTexture value="true" />
      </reversingLight>
      <reversingLightCorona>
        <size value="0.800000" />
        <size_far value="2.000000" />
        <intensity value="1.500000" />
        <intensity_far value="1.000000" />
        <color value="0x00F7F7F7" />
        <numCoronas value="1" />
        <distBetweenCoronas value="128" />
        <distBetweenCoronas_far value="255" />
        <xRotation value="0.000000" />
        <yRotation value="0.000000" />
        <zRotation value="0.000000" />
        <zBias value="0.250000" />
        <pullCoronaIn value="false" />
      </reversingLightCorona>
      <name>dominator8</name>
    </Item>
    <Item>
      <id value="168" />
      <indicator>
        <intensity value="0.375000" />
        <falloffMax value="2.500000" />
        <falloffExponent value="8.000000" />
        <innerConeAngle value="20.000000" />
        <outerConeAngle value="50.000000" />
        <emmissiveBoost value="false" />
        <color value="0xFFFF8000" />
        <textureName />
        <mirrorTexture value="true" />
      </indicator>
      <rearIndicatorCorona>
        <size value="0.000000" />
        <size_far value="0.000000" />
        <intensity value="0.000000" />
        <intensity_far value="0.000000" />
        <color value="0x00000000" />
        <numCoronas value="1" />
        <distBetweenCoronas value="128" />
        <distBetweenCoronas_far value="255" />
        <xRotation value="0.000000" />
        <yRotation value="0.000000" />
        <zRotation value="0.000000" />
        <zBias value="0.250000" />
        <pullCoronaIn value="false" />
      </rearIndicatorCorona>
      <frontIndicatorCorona>
        <size value="0.000000" />
        <size_far value="0.000000" />
        <intensity value="0.000000" />
        <intensity_far value="0.000000" />
        <color value="0x00000000" />
        <numCoronas value="1" />
        <distBetweenCoronas value="128" />
        <distBetweenCoronas_far value="255" />
        <xRotation value="0.000000" />
        <yRotation value="0.000000" />
        <zRotation value="0.000000" />
        <zBias value="0.250000" />
        <pullCoronaIn value="false" />
      </frontIndicatorCorona>
      <tailLight>
        <intensity value="0.250000" />
        <falloffMax value="4.000000" />
        <falloffExponent value="16.000000" />
        <innerConeAngle value="45.000000" />
        <outerConeAngle value="90.000000" />
        <emmissiveBoost value="false" />
        <color value="0xFFFF0A01" />
        <textureName />
        <mirrorTexture value="true" />
      </tailLight>
      <tailLightCorona>
        <size value="0.000000" />
        <size_far value="8.000000" />
        <intensity value="3.000000" />
        <intensity_far value="1.000000" />
        <color value="0xFFFF2D05" />
        <numCoronas value="2" />
        <distBetweenCoronas value="66" />
        <distBetweenCoronas_far value="1" />
        <xRotation value="0.000000" />
        <yRotation value="0.000000" />
        <zRotation value="6.185000" />
        <zBias value="0.250000" />
        <pullCoronaIn value="false" />
      </tailLightCorona>
      <tailLightMiddleCorona>
        <size value="0.000000" />
        <size_far value="0.000000" />
        <intensity value="0.000000" />
        <intensity_far value="0.000000" />
        <color value="0x00000000" />
        <numCoronas value="1" />
        <distBetweenCoronas value="128" />
        <distBetweenCoronas_far value="255" />
        <xRotation value="0.000000" />
        <yRotation value="0.000000" />
        <zRotation value="0.000000" />
        <zBias value="0.250000" />
        <pullCoronaIn value="false" />
      </tailLightMiddleCorona>
      <headLight>
        <intensity value="1.000000" />
        <falloffMax value="35.000000" />
        <falloffExponent value="16.000000" />
        <innerConeAngle value="0.000000" />
        <outerConeAngle value="60.000000" />
        <emmissiveBoost value="false" />
        <color value="0xFFFFD859" />
        <textureName>VehicleLight_car_standardmodern</textureName>
        <mirrorTexture value="false" />
      </headLight>
      <headLightCorona>
        <size value="0.100000" />
        <size_far value="10.000000" />
        <intensity value="5.000000" />
        <intensity_far value="1.000000" />
        <color value="0xFFFCBB42" />
        <numCoronas value="1" />
        <distBetweenCoronas value="37" />
        <distBetweenCoronas_far value="122" />
        <xRotation value="0.000000" />
        <yRotation value="0.000000" />
        <zRotation value="0.000000" />
        <zBias value="0.250000" />
        <pullCoronaIn value="false" />
      </headLightCorona>
      <reversingLight>
        <intensity value="0.350000" />
        <falloffMax value="20.000000" />
        <falloffExponent value="32.000000" />
        <innerConeAngle value="20.000000" />
        <outerConeAngle value="90.000000" />
        <emmissiveBoost value="false" />
        <color value="0xFFFFFFFF" />
        <textureName />
        <mirrorTexture value="true" />
      </reversingLight>
      <reversingLightCorona>
        <size value="0.800000" />
        <size_far value="2.000000" />
        <intensity value="1.500000" />
        <intensity_far value="1.000000" />
        <color value="0x00F7F7F7" />
        <numCoronas value="1" />
        <distBetweenCoronas value="128" />
        <distBetweenCoronas_far value="255" />
        <xRotation value="0.000000" />
        <yRotation value="0.000000" />
        <zRotation value="0.000000" />
        <zBias value="0.250000" />
        <pullCoronaIn value="false" />
      </reversingLightCorona>
      <name>remus</name>
    </Item>
    <Item>
      <id value="169" />
      <indicator>
        <intensity value="0.375000" />
        <falloffMax value="2.500000" />
        <falloffExponent value="8.000000" />
        <innerConeAngle value="20.000000" />
        <outerConeAngle value="50.000000" />
        <emmissiveBoost value="false" />
        <color value="0xFFFF8000" />
        <textureName />
        <mirrorTexture value="true" />
      </indicator>
      <rearIndicatorCorona>
        <size value="0.000000" />
        <size_far value="0.000000" />
        <intensity value="0.000000" />
        <intensity_far value="0.000000" />
        <color value="0x00000000" />
        <numCoronas value="1" />
        <distBetweenCoronas value="128" />
        <distBetweenCoronas_far value="255" />
        <xRotation value="0.000000" />
        <yRotation value="0.000000" />
        <zRotation value="0.000000" />
        <zBias value="0.250000" />
        <pullCoronaIn value="false" />
      </rearIndicatorCorona>
      <frontIndicatorCorona>
        <size value="0.000000" />
        <size_far value="0.000000" />
        <intensity value="0.000000" />
        <intensity_far value="0.000000" />
        <color value="0x00000000" />
        <numCoronas value="1" />
        <distBetweenCoronas value="128" />
        <distBetweenCoronas_far value="255" />
        <xRotation value="0.000000" />
        <yRotation value="0.000000" />
        <zRotation value="0.000000" />
        <zBias value="0.250000" />
        <pullCoronaIn value="false" />
      </frontIndicatorCorona>
      <tailLight>
        <intensity value="0.250000" />
        <falloffMax value="4.000000" />
        <falloffExponent value="16.000000" />
        <innerConeAngle value="45.000000" />
        <outerConeAngle value="90.000000" />
        <emmissiveBoost value="false" />
        <color value="0xFFFF0A01" />
        <textureName />
        <mirrorTexture value="true" />
      </tailLight>
      <tailLightCorona>
        <size value="0.000000" />
        <size_far value="4.000000" />
        <intensity value="3.000000" />
        <intensity_far value="1.000000" />
        <color value="0xFFFF2D05" />
        <numCoronas value="2" />
        <distBetweenCoronas value="27" />
        <distBetweenCoronas_far value="1" />
        <xRotation value="0.000000" />
        <yRotation value="0.000000" />
        <zRotation value="0.000000" />
        <zBias value="0.250000" />
        <pullCoronaIn value="false" />
      </tailLightCorona>
      <tailLightMiddleCorona>
        <size value="0.000000" />
        <size_far value="0.000000" />
        <intensity value="0.000000" />
        <intensity_far value="0.000000" />
        <color value="0x00000000" />
        <numCoronas value="1" />
        <distBetweenCoronas value="128" />
        <distBetweenCoronas_far value="255" />
        <xRotation value="0.000000" />
        <yRotation value="0.000000" />
        <zRotation value="0.000000" />
        <zBias value="0.250000" />
        <pullCoronaIn value="false" />
      </tailLightMiddleCorona>
      <headLight>
        <intensity value="1.000000" />
        <falloffMax value="35.000000" />
        <falloffExponent value="16.000000" />
        <innerConeAngle value="0.000000" />
        <outerConeAngle value="60.000000" />
        <emmissiveBoost value="false" />
        <color value="0xFFFFD859" />
        <textureName>VehicleLight_car_standardmodern</textureName>
        <mirrorTexture value="false" />
      </headLight>
      <headLightCorona>
        <size value="0.100000" />
        <size_far value="10.000000" />
        <intensity value="5.000000" />
        <intensity_far value="1.000000" />
        <color value="0xFFFCBB42" />
        <numCoronas value="2" />
        <distBetweenCoronas value="11" />
        <distBetweenCoronas_far value="122" />
        <xRotation value="0.000000" />
        <yRotation value="0.000000" />
        <zRotation value="0.641000" />
        <zBias value="0.250000" />
        <pullCoronaIn value="false" />
      </headLightCorona>
      <reversingLight>
        <intensity value="0.350000" />
        <falloffMax value="20.000000" />
        <falloffExponent value="32.000000" />
        <innerConeAngle value="20.000000" />
        <outerConeAngle value="90.000000" />
        <emmissiveBoost value="false" />
        <color value="0xFFFFFFFF" />
        <textureName />
        <mirrorTexture value="true" />
      </reversingLight>
      <reversingLightCorona>
        <size value="0.800000" />
        <size_far value="2.000000" />
        <intensity value="1.500000" />
        <intensity_far value="1.000000" />
        <color value="0x00F7F7F7" />
        <numCoronas value="1" />
        <distBetweenCoronas value="128" />
        <distBetweenCoronas_far value="255" />
        <xRotation value="0.000000" />
        <yRotation value="0.000000" />
        <zRotation value="0.000000" />
        <zBias value="0.250000" />
        <pullCoronaIn value="false" />
      </reversingLightCorona>
      <name>rt3000</name>
    </Item>
	<Item>
      <id value="170" />
      <indicator>
        <intensity value="0.375000" />
        <falloffMax value="2.500000" />
        <falloffExponent value="8.000000" />
        <innerConeAngle value="30.000000" />
        <outerConeAngle value="80.000000" />
        <emmissiveBoost value="false" />
        <color value="0xFFFF8000" />
        <textureName />
        <mirrorTexture value="true" />
      </indicator>
      <rearIndicatorCorona>
        <size value="0.000000" />
        <size_far value="0.000000" />
        <intensity value="0.000000" />
        <intensity_far value="0.000000" />
        <color value="0x00000000" />
        <numCoronas value="1" />
        <distBetweenCoronas value="128" />
        <distBetweenCoronas_far value="255" />
        <xRotation value="0.000000" />
        <yRotation value="0.000000" />
        <zRotation value="0.000000" />
        <zBias value="0.250000" />
        <pullCoronaIn value="false" />
      </rearIndicatorCorona>
      <frontIndicatorCorona>
        <size value="0.000000" />
        <size_far value="0.000000" />
        <intensity value="0.000000" />
        <intensity_far value="0.000000" />
        <color value="0x00000000" />
        <numCoronas value="1" />
        <distBetweenCoronas value="128" />
        <distBetweenCoronas_far value="255" />
        <xRotation value="0.000000" />
        <yRotation value="0.000000" />
        <zRotation value="0.000000" />
        <zBias value="0.250000" />
        <pullCoronaIn value="false" />
      </frontIndicatorCorona>
      <tailLight>
        <intensity value="0.250000" />
        <falloffMax value="4.000000" />
        <falloffExponent value="16.000000" />
        <innerConeAngle value="45.000000" />
        <outerConeAngle value="90.000000" />
        <emmissiveBoost value="false" />
        <color value="0xFFFF0000" />
        <textureName />
        <mirrorTexture value="true" />
      </tailLight>
      <tailLightCorona>
        <size value="0.500000" />
        <size_far value="0.000000" />
        <intensity value="6.700000" />
        <intensity_far value="20.700000" />
        <color value="0xFFFF1E05" />
        <numCoronas value="1" />
        <distBetweenCoronas value="25" />
        <distBetweenCoronas_far value="52" />
        <xRotation value="0.000000" />
        <yRotation value="0.000000" />
        <zRotation value="5.900000" />
        <zBias value="0.250000" />
        <pullCoronaIn value="false" />
      </tailLightCorona>
      <tailLightMiddleCorona>
        <size value="0.500000" />
        <size_far value="0.000000" />
        <intensity value="0.000000" />
        <intensity_far value="0.000000" />
        <color value="0x00000000" />
        <numCoronas value="1" />
        <distBetweenCoronas value="128" />
        <distBetweenCoronas_far value="255" />
        <xRotation value="0.000000" />
        <yRotation value="0.000000" />
        <zRotation value="0.000000" />
        <zBias value="0.250000" />
        <pullCoronaIn value="false" />
      </tailLightMiddleCorona>
      <headLight>
        <intensity value="1.500000" />
        <falloffMax value="0.000000" />
        <falloffExponent value="28.000000" />
        <innerConeAngle value="50.000000" />
        <outerConeAngle value="67.500000" />
        <emmissiveBoost value="false" />
        <color value="0xFFFFFFCC" />
        <textureName>VehicleLight_car_LED1</textureName>
        <mirrorTexture value="false" />
      </headLight>
      <headLightCorona>
        <size value="2.000000" />
        <size_far value="4.000000" />
        <intensity value="0.800000" />
        <intensity_far value="1.000000" />
        <color value="0xFFFFFFFF" />
        <numCoronas value="2" />
        <distBetweenCoronas value="38" />
        <distBetweenCoronas_far value="1" />
        <xRotation value="0.000000" />
        <yRotation value="0.220000" />
        <zRotation value="3.933000" />
        <zBias value="0.250000" />
        <pullCoronaIn value="false" />
      </headLightCorona>
      <reversingLight>
        <intensity value="0.000000" />
        <falloffMax value="4.000000" />
        <falloffExponent value="32.000000" />
        <innerConeAngle value="45.000000" />
        <outerConeAngle value="90.000000" />
        <emmissiveBoost value="false" />
        <color value="0xFFFFFFFF" />
        <textureName />
        <mirrorTexture value="true" />
      </reversingLight>
      <reversingLightCorona>
        <size value="0.800000" />
        <size_far value="2.000000" />
        <intensity value="0.300000" />
        <intensity_far value="0.000000" />
        <color value="0x00F7F7F7" />
        <numCoronas value="1" />
        <distBetweenCoronas value="128" />
        <distBetweenCoronas_far value="255" />
        <xRotation value="0.000000" />
        <yRotation value="0.000000" />
        <zRotation value="0.000000" />
        <zBias value="0.250000" />
        <pullCoronaIn value="false" />
      </reversingLightCorona>
      <name>cypher</name>
    </Item>
    </Lights>
  <Wheels>
    <Item/> <!-- VWT_SPORT -->
    <Item/> <!-- VWT_MUSCLE -->
    <Item/> <!-- VWT_LOWRIDER -->
    <Item/> <!-- VWT_SUV -->
    <Item/> <!-- VWT_OFFROAD -->
    <Item/> <!-- VWT_TUNER -->
    <Item/> <!-- VWT_BIKE -->
    <Item/> <!-- VWT_HIEND -->
    <Item/> <!-- VWT_SUPERMOD1 -->
    <Item/> <!-- VWT_SUPERMOD2 -->
    <Item/> <!-- VWT_SUPERMOD3 -->
    <Item/> <!-- VWT_SUPERMOD4 -->
    <Item>  <!-- VWT_SUPERMOD5 -->
      <Item>
        <wheelName>wheel_5smod1a</wheelName>
        <wheelVariation />
        <modShopLabel>SMOD5_WHL_1</modShopLabel>
        <rimRadius value="0.398000"/>
        <rear value="false"/>
      </Item>
      <Item>
        <wheelName>wheel_5smod2a</wheelName>
        <wheelVariation />
        <modShopLabel>SMOD5_WHL_2</modShopLabel>
        <rimRadius value="0.398000"/>
        <rear value="false"/>
      </Item>
      <Item>
        <wheelName>wheel_5smod3a</wheelName>
        <wheelVariation />
        <modShopLabel>SMOD5_WHL_3</modShopLabel>
        <rimRadius value="0.398000"/>
        <rear value="false"/>
      </Item>
      <Item>
        <wheelName>wheel_5smod4a</wheelName>
        <wheelVariation />
        <modShopLabel>SMOD5_WHL_4</modShopLabel>
        <rimRadius value="0.398000"/>
        <rear value="false"/>
      </Item>
      <Item>
        <wheelName>wheel_5smod5a</wheelName>
        <wheelVariation />
        <modShopLabel>SMOD5_WHL_5</modShopLabel>
        <rimRadius value="0.398000"/>
        <rear value="false"/>
      </Item>
      <Item>
        <wheelName>wheel_5smod6a</wheelName>
        <wheelVariation />
        <modShopLabel>SMOD5_WHL_6</modShopLabel>
        <rimRadius value="0.398000"/>
        <rear value="false"/>
      </Item>
      <Item>
        <wheelName>wheel_5smod7a</wheelName>
        <wheelVariation />
        <modShopLabel>SMOD5_WHL_7</modShopLabel>
        <rimRadius value="0.398000"/>
        <rear value="false"/>
      </Item>
      <Item>
        <wheelName>wheel_5smod8a</wheelName>
        <wheelVariation />
        <modShopLabel>SMOD5_WHL_8</modShopLabel>
        <rimRadius value="0.398000"/>
        <rear value="false"/>
      </Item>
      <Item>
        <wheelName>wheel_5smod9a</wheelName>
        <wheelVariation />
        <modShopLabel>SMOD5_WHL_9</modShopLabel>
        <rimRadius value="0.398000"/>
        <rear value="false"/>
      </Item>
      <Item>
        <wheelName>wheel_5smod10a</wheelName>
        <wheelVariation />
        <modShopLabel>SMOD5_WHL_10</modShopLabel>
        <rimRadius value="0.398000"/>
        <rear value="false"/>
      </Item>
      <Item>
        <wheelName>wheel_5smod11a</wheelName>
        <wheelVariation />
        <modShopLabel>SMOD5_WHL_11</modShopLabel>
        <rimRadius value="0.398000"/>
        <rear value="false"/>
      </Item>
      <Item>
        <wheelName>wheel_5smod12a</wheelName>
        <wheelVariation />
        <modShopLabel>SMOD5_WHL_12</modShopLabel>
        <rimRadius value="0.398000"/>
        <rear value="false"/>
      </Item>
      <Item>
        <wheelName>wheel_5smod13a</wheelName>
        <wheelVariation />
        <modShopLabel>SMOD5_WHL_13</modShopLabel>
        <rimRadius value="0.398000"/>
        <rear value="false"/>
      </Item>
      <Item>
        <wheelName>wheel_5smod14a</wheelName>
        <wheelVariation />
        <modShopLabel>SMOD5_WHL_14</modShopLabel>
        <rimRadius value="0.398000"/>
        <rear value="false"/>
      </Item>
      <Item>
        <wheelName>wheel_5smod15a</wheelName>
        <wheelVariation />
        <modShopLabel>SMOD5_WHL_15</modShopLabel>
        <rimRadius value="0.398000"/>
        <rear value="false"/>
      </Item>
      <Item>
        <wheelName>wheel_5smod16a</wheelName>
        <wheelVariation />
        <modShopLabel>SMOD5_WHL_16</modShopLabel>
        <rimRadius value="0.398000"/>
        <rear value="false"/>
      </Item>
      <Item>
        <wheelName>wheel_5smod17a</wheelName>
        <wheelVariation />
        <modShopLabel>SMOD5_WHL_17</modShopLabel>
        <rimRadius value="0.398000"/>
        <rear value="false"/>
      </Item>
      <Item>
        <wheelName>wheel_5smod18a</wheelName>
        <wheelVariation />
        <modShopLabel>SMOD5_WHL_18</modShopLabel>
        <rimRadius value="0.398000"/>
        <rear value="false"/>
      </Item>
      <Item>
        <wheelName>wheel_5smod19a</wheelName>
        <wheelVariation />
        <modShopLabel>SMOD5_WHL_19</modShopLabel>
        <rimRadius value="0.398000"/>
        <rear value="false"/>
      </Item>
      <Item>
        <wheelName>wheel_5smod20a</wheelName>
        <wheelVariation />
        <modShopLabel>SMOD5_WHL_20</modShopLabel>
        <rimRadius value="0.398000"/>
        <rear value="false"/>
      </Item>
      <Item>
        <wheelName>wheel_5smod21a</wheelName>
        <wheelVariation />
        <modShopLabel>SMOD5_WHL_21</modShopLabel>
        <rimRadius value="0.398000"/>
        <rear value="false"/>
      </Item>
      <Item>
        <wheelName>wheel_5smod22a</wheelName>
        <wheelVariation />
        <modShopLabel>SMOD5_WHL_22</modShopLabel>
        <rimRadius value="0.398000"/>
        <rear value="false"/>
      </Item>
      <Item>
        <wheelName>wheel_5smod23a</wheelName>
        <wheelVariation />
        <modShopLabel>SMOD5_WHL_23</modShopLabel>
        <rimRadius value="0.398000"/>
        <rear value="false"/>
      </Item>
      <Item>
        <wheelName>wheel_5smod24a</wheelName>
        <wheelVariation />
        <modShopLabel>SMOD5_WHL_24</modShopLabel>
        <rimRadius value="0.398000"/>
        <rear value="false"/>
      </Item>
      <Item>
        <wheelName>wheel_5smod25a</wheelName>
        <wheelVariation />
        <modShopLabel>SMOD5_WHL_25</modShopLabel>
        <rimRadius value="0.398000"/>
        <rear value="false"/>
      </Item>
      <Item>
        <wheelName>wheel_5smod26a</wheelName>
        <wheelVariation />
        <modShopLabel>SMOD5_WHL_26</modShopLabel>
        <rimRadius value="0.398000"/>
        <rear value="false"/>
      </Item>
      <Item>
        <wheelName>wheel_5smod27a</wheelName>
        <wheelVariation />
        <modShopLabel>SMOD5_WHL_27</modShopLabel>
        <rimRadius value="0.398000"/>
        <rear value="false"/>
      </Item>
      <Item>
        <wheelName>wheel_5smod28a</wheelName>
        <wheelVariation />
        <modShopLabel>SMOD5_WHL_28</modShopLabel>
        <rimRadius value="0.398000"/>
        <rear value="false"/>
      </Item>
      <Item>
        <wheelName>wheel_5smod29a</wheelName>
        <wheelVariation />
        <modShopLabel>SMOD5_WHL_29</modShopLabel>
        <rimRadius value="0.398000"/>
        <rear value="false"/>
      </Item>
      <Item>
        <wheelName>wheel_5smod30a</wheelName>
        <wheelVariation />
        <modShopLabel>SMOD5_WHL_30</modShopLabel>
        <rimRadius value="0.398000"/>
        <rear value="false"/>
      </Item>
      <Item>
        <wheelName>wheel_5smod1b</wheelName>
        <wheelVariation />
        <modShopLabel>SMOD5_WHL_1</modShopLabel>
        <rimRadius value="0.398000"/>
        <rear value="false"/>
      </Item>
      <Item>
        <wheelName>wheel_5smod2b</wheelName>
        <wheelVariation />
        <modShopLabel>SMOD5_WHL_2</modShopLabel>
        <rimRadius value="0.398000"/>
        <rear value="false"/>
      </Item>
      <Item>
        <wheelName>wheel_5smod3b</wheelName>
        <wheelVariation />
        <modShopLabel>SMOD5_WHL_3</modShopLabel>
        <rimRadius value="0.398000"/>
        <rear value="false"/>
      </Item>
      <Item>
        <wheelName>wheel_5smod4b</wheelName>
        <wheelVariation />
        <modShopLabel>SMOD5_WHL_4</modShopLabel>
        <rimRadius value="0.398000"/>
        <rear value="false"/>
      </Item>
      <Item>
        <wheelName>wheel_5smod5b</wheelName>
        <wheelVariation />
        <modShopLabel>SMOD5_WHL_5</modShopLabel>
        <rimRadius value="0.398000"/>
        <rear value="false"/>
      </Item>
      <Item>
        <wheelName>wheel_5smod6b</wheelName>
        <wheelVariation />
        <modShopLabel>SMOD5_WHL_6</modShopLabel>
        <rimRadius value="0.398000"/>
        <rear value="false"/>
      </Item>
      <Item>
        <wheelName>wheel_5smod7b</wheelName>
        <wheelVariation />
        <modShopLabel>SMOD5_WHL_7</modShopLabel>
        <rimRadius value="0.398000"/>
        <rear value="false"/>
      </Item>
      <Item>
        <wheelName>wheel_5smod8b</wheelName>
        <wheelVariation />
        <modShopLabel>SMOD5_WHL_8</modShopLabel>
        <rimRadius value="0.398000"/>
        <rear value="false"/>
      </Item>
      <Item>
        <wheelName>wheel_5smod9b</wheelName>
        <wheelVariation />
        <modShopLabel>SMOD5_WHL_9</modShopLabel>
        <rimRadius value="0.398000"/>
        <rear value="false"/>
      </Item>
      <Item>
        <wheelName>wheel_5smod10b</wheelName>
        <wheelVariation />
        <modShopLabel>SMOD5_WHL_10</modShopLabel>
        <rimRadius value="0.398000"/>
        <rear value="false"/>
      </Item>
      <Item>
        <wheelName>wheel_5smod11b</wheelName>
        <wheelVariation />
        <modShopLabel>SMOD5_WHL_11</modShopLabel>
        <rimRadius value="0.398000"/>
        <rear value="false"/>
      </Item>
      <Item>
        <wheelName>wheel_5smod12b</wheelName>
        <wheelVariation />
        <modShopLabel>SMOD5_WHL_12</modShopLabel>
        <rimRadius value="0.398000"/>
        <rear value="false"/>
      </Item>
      <Item>
        <wheelName>wheel_5smod13b</wheelName>
        <wheelVariation />
        <modShopLabel>SMOD5_WHL_13</modShopLabel>
        <rimRadius value="0.398000"/>
        <rear value="false"/>
      </Item>
      <Item>
        <wheelName>wheel_5smod14b</wheelName>
        <wheelVariation />
        <modShopLabel>SMOD5_WHL_14</modShopLabel>
        <rimRadius value="0.398000"/>
        <rear value="false"/>
      </Item>
      <Item>
        <wheelName>wheel_5smod15b</wheelName>
        <wheelVariation />
        <modShopLabel>SMOD5_WHL_15</modShopLabel>
        <rimRadius value="0.398000"/>
        <rear value="false"/>
      </Item>
      <Item>
        <wheelName>wheel_5smod16b</wheelName>
        <wheelVariation />
        <modShopLabel>SMOD5_WHL_16</modShopLabel>
        <rimRadius value="0.398000"/>
        <rear value="false"/>
      </Item>
      <Item>
        <wheelName>wheel_5smod17b</wheelName>
        <wheelVariation />
        <modShopLabel>SMOD5_WHL_17</modShopLabel>
        <rimRadius value="0.398000"/>
        <rear value="false"/>
      </Item>
      <Item>
        <wheelName>wheel_5smod18b</wheelName>
        <wheelVariation />
        <modShopLabel>SMOD5_WHL_18</modShopLabel>
        <rimRadius value="0.398000"/>
        <rear value="false"/>
      </Item>
      <Item>
        <wheelName>wheel_5smod19b</wheelName>
        <wheelVariation />
        <modShopLabel>SMOD5_WHL_19</modShopLabel>
        <rimRadius value="0.398000"/>
        <rear value="false"/>
      </Item>
      <Item>
        <wheelName>wheel_5smod20b</wheelName>
        <wheelVariation />
        <modShopLabel>SMOD5_WHL_20</modShopLabel>
        <rimRadius value="0.398000"/>
        <rear value="false"/>
      </Item>
      <Item>
        <wheelName>wheel_5smod21b</wheelName>
        <wheelVariation />
        <modShopLabel>SMOD5_WHL_21</modShopLabel>
        <rimRadius value="0.398000"/>
        <rear value="false"/>
      </Item>
      <Item>
        <wheelName>wheel_5smod22b</wheelName>
        <wheelVariation />
        <modShopLabel>SMOD5_WHL_22</modShopLabel>
        <rimRadius value="0.398000"/>
        <rear value="false"/>
      </Item>
      <Item>
        <wheelName>wheel_5smod23b</wheelName>
        <wheelVariation />
        <modShopLabel>SMOD5_WHL_23</modShopLabel>
        <rimRadius value="0.398000"/>
        <rear value="false"/>
      </Item>
      <Item>
        <wheelName>wheel_5smod24b</wheelName>
        <wheelVariation />
        <modShopLabel>SMOD5_WHL_24</modShopLabel>
        <rimRadius value="0.398000"/>
        <rear value="false"/>
      </Item>
      <Item>
        <wheelName>wheel_5smod25b</wheelName>
        <wheelVariation />
        <modShopLabel>SMOD5_WHL_25</modShopLabel>
        <rimRadius value="0.398000"/>
        <rear value="false"/>
      </Item>
      <Item>
        <wheelName>wheel_5smod26b</wheelName>
        <wheelVariation />
        <modShopLabel>SMOD5_WHL_26</modShopLabel>
        <rimRadius value="0.398000"/>
        <rear value="false"/>
      </Item>
      <Item>
        <wheelName>wheel_5smod27b</wheelName>
        <wheelVariation />
        <modShopLabel>SMOD5_WHL_27</modShopLabel>
        <rimRadius value="0.398000"/>
        <rear value="false"/>
      </Item>
      <Item>
        <wheelName>wheel_5smod28b</wheelName>
        <wheelVariation />
        <modShopLabel>SMOD5_WHL_28</modShopLabel>
        <rimRadius value="0.398000"/>
        <rear value="false"/>
      </Item>
      <Item>
        <wheelName>wheel_5smod29b</wheelName>
        <wheelVariation />
        <modShopLabel>SMOD5_WHL_29</modShopLabel>
        <rimRadius value="0.398000"/>
        <rear value="false"/>
      </Item>
      <Item>
        <wheelName>wheel_5smod30b</wheelName>
        <wheelVariation />
        <modShopLabel>SMOD5_WHL_30</modShopLabel>
        <rimRadius value="0.398000"/>
        <rear value="false"/>
      </Item>
      <Item>
        <wheelName>wheel_5smod1c</wheelName>
        <wheelVariation />
        <modShopLabel>SMOD5_WHL_1</modShopLabel>
        <rimRadius value="0.398000"/>
        <rear value="false"/>
      </Item>
      <Item>
        <wheelName>wheel_5smod2c</wheelName>
        <wheelVariation />
        <modShopLabel>SMOD5_WHL_2</modShopLabel>
        <rimRadius value="0.398000"/>
        <rear value="false"/>
      </Item>
      <Item>
        <wheelName>wheel_5smod3c</wheelName>
        <wheelVariation />
        <modShopLabel>SMOD5_WHL_3</modShopLabel>
        <rimRadius value="0.398000"/>
        <rear value="false"/>
      </Item>
      <Item>
        <wheelName>wheel_5smod4c</wheelName>
        <wheelVariation />
        <modShopLabel>SMOD5_WHL_4</modShopLabel>
        <rimRadius value="0.398000"/>
        <rear value="false"/>
      </Item>
      <Item>
        <wheelName>wheel_5smod5c</wheelName>
        <wheelVariation />
        <modShopLabel>SMOD5_WHL_5</modShopLabel>
        <rimRadius value="0.398000"/>
        <rear value="false"/>
      </Item>
      <Item>
        <wheelName>wheel_5smod6c</wheelName>
        <wheelVariation />
        <modShopLabel>SMOD5_WHL_6</modShopLabel>
        <rimRadius value="0.398000"/>
        <rear value="false"/>
      </Item>
      <Item>
        <wheelName>wheel_5smod7c</wheelName>
        <wheelVariation />
        <modShopLabel>SMOD5_WHL_7</modShopLabel>
        <rimRadius value="0.398000"/>
        <rear value="false"/>
      </Item>
      <Item>
        <wheelName>wheel_5smod8c</wheelName>
        <wheelVariation />
        <modShopLabel>SMOD5_WHL_8</modShopLabel>
        <rimRadius value="0.398000"/>
        <rear value="false"/>
      </Item>
      <Item>
        <wheelName>wheel_5smod9c</wheelName>
        <wheelVariation />
        <modShopLabel>SMOD5_WHL_9</modShopLabel>
        <rimRadius value="0.398000"/>
        <rear value="false"/>
      </Item>
      <Item>
        <wheelName>wheel_5smod10c</wheelName>
        <wheelVariation />
        <modShopLabel>SMOD5_WHL_10</modShopLabel>
        <rimRadius value="0.398000"/>
        <rear value="false"/>
      </Item>
      <Item>
        <wheelName>wheel_5smod11c</wheelName>
        <wheelVariation />
        <modShopLabel>SMOD5_WHL_11</modShopLabel>
        <rimRadius value="0.398000"/>
        <rear value="false"/>
      </Item>
      <Item>
        <wheelName>wheel_5smod12c</wheelName>
        <wheelVariation />
        <modShopLabel>SMOD5_WHL_12</modShopLabel>
        <rimRadius value="0.398000"/>
        <rear value="false"/>
      </Item>
      <Item>
        <wheelName>wheel_5smod13c</wheelName>
        <wheelVariation />
        <modShopLabel>SMOD5_WHL_13</modShopLabel>
        <rimRadius value="0.398000"/>
        <rear value="false"/>
      </Item>
      <Item>
        <wheelName>wheel_5smod14c</wheelName>
        <wheelVariation />
        <modShopLabel>SMOD5_WHL_14</modShopLabel>
        <rimRadius value="0.398000"/>
        <rear value="false"/>
      </Item>
      <Item>
        <wheelName>wheel_5smod15c</wheelName>
        <wheelVariation />
        <modShopLabel>SMOD5_WHL_15</modShopLabel>
        <rimRadius value="0.398000"/>
        <rear value="false"/>
      </Item>
      <Item>
        <wheelName>wheel_5smod16c</wheelName>
        <wheelVariation />
        <modShopLabel>SMOD5_WHL_16</modShopLabel>
        <rimRadius value="0.398000"/>
        <rear value="false"/>
      </Item>
      <Item>
        <wheelName>wheel_5smod17c</wheelName>
        <wheelVariation />
        <modShopLabel>SMOD5_WHL_17</modShopLabel>
        <rimRadius value="0.398000"/>
        <rear value="false"/>
      </Item>
      <Item>
        <wheelName>wheel_5smod18c</wheelName>
        <wheelVariation />
        <modShopLabel>SMOD5_WHL_18</modShopLabel>
        <rimRadius value="0.398000"/>
        <rear value="false"/>
      </Item>
      <Item>
        <wheelName>wheel_5smod19c</wheelName>
        <wheelVariation />
        <modShopLabel>SMOD5_WHL_19</modShopLabel>
        <rimRadius value="0.398000"/>
        <rear value="false"/>
      </Item>
      <Item>
        <wheelName>wheel_5smod20c</wheelName>
        <wheelVariation />
        <modShopLabel>SMOD5_WHL_20</modShopLabel>
        <rimRadius value="0.398000"/>
        <rear value="false"/>
      </Item>
      <Item>
        <wheelName>wheel_5smod21c</wheelName>
        <wheelVariation />
        <modShopLabel>SMOD5_WHL_21</modShopLabel>
        <rimRadius value="0.398000"/>
        <rear value="false"/>
      </Item>
      <Item>
        <wheelName>wheel_5smod22c</wheelName>
        <wheelVariation />
        <modShopLabel>SMOD5_WHL_22</modShopLabel>
        <rimRadius value="0.398000"/>
        <rear value="false"/>
      </Item>
      <Item>
        <wheelName>wheel_5smod23c</wheelName>
        <wheelVariation />
        <modShopLabel>SMOD5_WHL_23</modShopLabel>
        <rimRadius value="0.398000"/>
        <rear value="false"/>
      </Item>
      <Item>
        <wheelName>wheel_5smod24c</wheelName>
        <wheelVariation />
        <modShopLabel>SMOD5_WHL_24</modShopLabel>
        <rimRadius value="0.398000"/>
        <rear value="false"/>
      </Item>
      <Item>
        <wheelName>wheel_5smod25c</wheelName>
        <wheelVariation />
        <modShopLabel>SMOD5_WHL_25</modShopLabel>
        <rimRadius value="0.398000"/>
        <rear value="false"/>
      </Item>
      <Item>
        <wheelName>wheel_5smod26c</wheelName>
        <wheelVariation />
        <modShopLabel>SMOD5_WHL_26</modShopLabel>
        <rimRadius value="0.398000"/>
        <rear value="false"/>
      </Item>
      <Item>
        <wheelName>wheel_5smod27c</wheelName>
        <wheelVariation />
        <modShopLabel>SMOD5_WHL_27</modShopLabel>
        <rimRadius value="0.398000"/>
        <rear value="false"/>
      </Item>
      <Item>
        <wheelName>wheel_5smod28c</wheelName>
        <wheelVariation />
        <modShopLabel>SMOD5_WHL_28</modShopLabel>
        <rimRadius value="0.398000"/>
        <rear value="false"/>
      </Item>
      <Item>
        <wheelName>wheel_5smod29c</wheelName>
        <wheelVariation />
        <modShopLabel>SMOD5_WHL_29</modShopLabel>
        <rimRadius value="0.398000"/>
        <rear value="false"/>
      </Item>
      <Item>
        <wheelName>wheel_5smod30c</wheelName>
        <wheelVariation />
        <modShopLabel>SMOD5_WHL_30</modShopLabel>
        <rimRadius value="0.398000"/>
        <rear value="false"/>
      </Item>
      <Item>
        <wheelName>wheel_5smod1d</wheelName>
        <wheelVariation />
        <modShopLabel>SMOD5_WHL_1</modShopLabel>
        <rimRadius value="0.398000"/>
        <rear value="false"/>
      </Item>
      <Item>
        <wheelName>wheel_5smod2d</wheelName>
        <wheelVariation />
        <modShopLabel>SMOD5_WHL_2</modShopLabel>
        <rimRadius value="0.398000"/>
        <rear value="false"/>
      </Item>
      <Item>
        <wheelName>wheel_5smod3d</wheelName>
        <wheelVariation />
        <modShopLabel>SMOD5_WHL_3</modShopLabel>
        <rimRadius value="0.398000"/>
        <rear value="false"/>
      </Item>
      <Item>
        <wheelName>wheel_5smod4d</wheelName>
        <wheelVariation />
        <modShopLabel>SMOD5_WHL_4</modShopLabel>
        <rimRadius value="0.398000"/>
        <rear value="false"/>
      </Item>
      <Item>
        <wheelName>wheel_5smod5d</wheelName>
        <wheelVariation />
        <modShopLabel>SMOD5_WHL_5</modShopLabel>
        <rimRadius value="0.398000"/>
        <rear value="false"/>
      </Item>
      <Item>
        <wheelName>wheel_5smod6d</wheelName>
        <wheelVariation />
        <modShopLabel>SMOD5_WHL_6</modShopLabel>
        <rimRadius value="0.398000"/>
        <rear value="false"/>
      </Item>
      <Item>
        <wheelName>wheel_5smod7d</wheelName>
        <wheelVariation />
        <modShopLabel>SMOD5_WHL_7</modShopLabel>
        <rimRadius value="0.398000"/>
        <rear value="false"/>
      </Item>
      <Item>
        <wheelName>wheel_5smod8d</wheelName>
        <wheelVariation />
        <modShopLabel>SMOD5_WHL_8</modShopLabel>
        <rimRadius value="0.398000"/>
        <rear value="false"/>
      </Item>
      <Item>
        <wheelName>wheel_5smod9d</wheelName>
        <wheelVariation />
        <modShopLabel>SMOD5_WHL_9</modShopLabel>
        <rimRadius value="0.398000"/>
        <rear value="false"/>
      </Item>
      <Item>
        <wheelName>wheel_5smod10d</wheelName>
        <wheelVariation />
        <modShopLabel>SMOD5_WHL_10</modShopLabel>
        <rimRadius value="0.398000"/>
        <rear value="false"/>
      </Item>
      <Item>
        <wheelName>wheel_5smod11d</wheelName>
        <wheelVariation />
        <modShopLabel>SMOD5_WHL_11</modShopLabel>
        <rimRadius value="0.398000"/>
        <rear value="false"/>
      </Item>
      <Item>
        <wheelName>wheel_5smod12d</wheelName>
        <wheelVariation />
        <modShopLabel>SMOD5_WHL_12</modShopLabel>
        <rimRadius value="0.398000"/>
        <rear value="false"/>
      </Item>
      <Item>
        <wheelName>wheel_5smod13d</wheelName>
        <wheelVariation />
        <modShopLabel>SMOD5_WHL_13</modShopLabel>
        <rimRadius value="0.398000"/>
        <rear value="false"/>
      </Item>
      <Item>
        <wheelName>wheel_5smod14d</wheelName>
        <wheelVariation />
        <modShopLabel>SMOD5_WHL_14</modShopLabel>
        <rimRadius value="0.398000"/>
        <rear value="false"/>
      </Item>
      <Item>
        <wheelName>wheel_5smod15d</wheelName>
        <wheelVariation />
        <modShopLabel>SMOD5_WHL_15</modShopLabel>
        <rimRadius value="0.398000"/>
        <rear value="false"/>
      </Item>
      <Item>
        <wheelName>wheel_5smod16d</wheelName>
        <wheelVariation />
        <modShopLabel>SMOD5_WHL_16</modShopLabel>
        <rimRadius value="0.398000"/>
        <rear value="false"/>
      </Item>
      <Item>
        <wheelName>wheel_5smod17d</wheelName>
        <wheelVariation />
        <modShopLabel>SMOD5_WHL_17</modShopLabel>
        <rimRadius value="0.398000"/>
        <rear value="false"/>
      </Item>
      <Item>
        <wheelName>wheel_5smod18d</wheelName>
        <wheelVariation />
        <modShopLabel>SMOD5_WHL_18</modShopLabel>
        <rimRadius value="0.398000"/>
        <rear value="false"/>
      </Item>
      <Item>
        <wheelName>wheel_5smod19d</wheelName>
        <wheelVariation />
        <modShopLabel>SMOD5_WHL_19</modShopLabel>
        <rimRadius value="0.398000"/>
        <rear value="false"/>
      </Item>
      <Item>
        <wheelName>wheel_5smod20d</wheelName>
        <wheelVariation />
        <modShopLabel>SMOD5_WHL_20</modShopLabel>
        <rimRadius value="0.398000"/>
        <rear value="false"/>
      </Item>
      <Item>
        <wheelName>wheel_5smod21d</wheelName>
        <wheelVariation />
        <modShopLabel>SMOD5_WHL_21</modShopLabel>
        <rimRadius value="0.398000"/>
        <rear value="false"/>
      </Item>
      <Item>
        <wheelName>wheel_5smod22d</wheelName>
        <wheelVariation />
        <modShopLabel>SMOD5_WHL_22</modShopLabel>
        <rimRadius value="0.398000"/>
        <rear value="false"/>
      </Item>
      <Item>
        <wheelName>wheel_5smod23d</wheelName>
        <wheelVariation />
        <modShopLabel>SMOD5_WHL_23</modShopLabel>
        <rimRadius value="0.398000"/>
        <rear value="false"/>
      </Item>
      <Item>
        <wheelName>wheel_5smod24d</wheelName>
        <wheelVariation />
        <modShopLabel>SMOD5_WHL_24</modShopLabel>
        <rimRadius value="0.398000"/>
        <rear value="false"/>
      </Item>
      <Item>
        <wheelName>wheel_5smod25d</wheelName>
        <wheelVariation />
        <modShopLabel>SMOD5_WHL_25</modShopLabel>
        <rimRadius value="0.398000"/>
        <rear value="false"/>
      </Item>
      <Item>
        <wheelName>wheel_5smod26d</wheelName>
        <wheelVariation />
        <modShopLabel>SMOD5_WHL_26</modShopLabel>
        <rimRadius value="0.398000"/>
        <rear value="false"/>
      </Item>
      <Item>
        <wheelName>wheel_5smod27d</wheelName>
        <wheelVariation />
        <modShopLabel>SMOD5_WHL_27</modShopLabel>
        <rimRadius value="0.398000"/>
        <rear value="false"/>
      </Item>
      <Item>
        <wheelName>wheel_5smod28d</wheelName>
        <wheelVariation />
        <modShopLabel>SMOD5_WHL_28</modShopLabel>
        <rimRadius value="0.398000"/>
        <rear value="false"/>
      </Item>
      <Item>
        <wheelName>wheel_5smod29d</wheelName>
        <wheelVariation />
        <modShopLabel>SMOD5_WHL_29</modShopLabel>
        <rimRadius value="0.398000"/>
        <rear value="false"/>
      </Item>
      <Item>
        <wheelName>wheel_5smod30d</wheelName>
        <wheelVariation />
        <modShopLabel>SMOD5_WHL_30</modShopLabel>
        <rimRadius value="0.398000"/>
        <rear value="false"/>
      </Item>
      <Item>
        <wheelName>wheel_5smod1e</wheelName>
        <wheelVariation />
        <modShopLabel>SMOD5_WHL_1</modShopLabel>
        <rimRadius value="0.398000"/>
        <rear value="false"/>
      </Item>
      <Item>
        <wheelName>wheel_5smod2e</wheelName>
        <wheelVariation />
        <modShopLabel>SMOD5_WHL_2</modShopLabel>
        <rimRadius value="0.398000"/>
        <rear value="false"/>
      </Item>
      <Item>
        <wheelName>wheel_5smod3e</wheelName>
        <wheelVariation />
        <modShopLabel>SMOD5_WHL_3</modShopLabel>
        <rimRadius value="0.398000"/>
        <rear value="false"/>
      </Item>
      <Item>
        <wheelName>wheel_5smod4e</wheelName>
        <wheelVariation />
        <modShopLabel>SMOD5_WHL_4</modShopLabel>
        <rimRadius value="0.398000"/>
        <rear value="false"/>
      </Item>
      <Item>
        <wheelName>wheel_5smod5e</wheelName>
        <wheelVariation />
        <modShopLabel>SMOD5_WHL_5</modShopLabel>
        <rimRadius value="0.398000"/>
        <rear value="false"/>
      </Item>
      <Item>
        <wheelName>wheel_5smod6e</wheelName>
        <wheelVariation />
        <modShopLabel>SMOD5_WHL_6</modShopLabel>
        <rimRadius value="0.398000"/>
        <rear value="false"/>
      </Item>
      <Item>
        <wheelName>wheel_5smod7e</wheelName>
        <wheelVariation />
        <modShopLabel>SMOD5_WHL_7</modShopLabel>
        <rimRadius value="0.398000"/>
        <rear value="false"/>
      </Item>
      <Item>
        <wheelName>wheel_5smod8e</wheelName>
        <wheelVariation />
        <modShopLabel>SMOD5_WHL_8</modShopLabel>
        <rimRadius value="0.398000"/>
        <rear value="false"/>
      </Item>
      <Item>
        <wheelName>wheel_5smod9e</wheelName>
        <wheelVariation />
        <modShopLabel>SMOD5_WHL_9</modShopLabel>
        <rimRadius value="0.398000"/>
        <rear value="false"/>
      </Item>
      <Item>
        <wheelName>wheel_5smod10e</wheelName>
        <wheelVariation />
        <modShopLabel>SMOD5_WHL_10</modShopLabel>
        <rimRadius value="0.398000"/>
        <rear value="false"/>
      </Item>
      <Item>
        <wheelName>wheel_5smod11e</wheelName>
        <wheelVariation />
        <modShopLabel>SMOD5_WHL_11</modShopLabel>
        <rimRadius value="0.398000"/>
        <rear value="false"/>
      </Item>
      <Item>
        <wheelName>wheel_5smod12e</wheelName>
        <wheelVariation />
        <modShopLabel>SMOD5_WHL_12</modShopLabel>
        <rimRadius value="0.398000"/>
        <rear value="false"/>
      </Item>
      <Item>
        <wheelName>wheel_5smod13e</wheelName>
        <wheelVariation />
        <modShopLabel>SMOD5_WHL_13</modShopLabel>
        <rimRadius value="0.398000"/>
        <rear value="false"/>
      </Item>
      <Item>
        <wheelName>wheel_5smod14e</wheelName>
        <wheelVariation />
        <modShopLabel>SMOD5_WHL_14</modShopLabel>
        <rimRadius value="0.398000"/>
        <rear value="false"/>
      </Item>
      <Item>
        <wheelName>wheel_5smod15e</wheelName>
        <wheelVariation />
        <modShopLabel>SMOD5_WHL_15</modShopLabel>
        <rimRadius value="0.398000"/>
        <rear value="false"/>
      </Item>
      <Item>
        <wheelName>wheel_5smod16e</wheelName>
        <wheelVariation />
        <modShopLabel>SMOD5_WHL_16</modShopLabel>
        <rimRadius value="0.398000"/>
        <rear value="false"/>
      </Item>
      <Item>
        <wheelName>wheel_5smod17e</wheelName>
        <wheelVariation />
        <modShopLabel>SMOD5_WHL_17</modShopLabel>
        <rimRadius value="0.398000"/>
        <rear value="false"/>
      </Item>
      <Item>
        <wheelName>wheel_5smod18e</wheelName>
        <wheelVariation />
        <modShopLabel>SMOD5_WHL_18</modShopLabel>
        <rimRadius value="0.398000"/>
        <rear value="false"/>
      </Item>
      <Item>
        <wheelName>wheel_5smod19e</wheelName>
        <wheelVariation />
        <modShopLabel>SMOD5_WHL_19</modShopLabel>
        <rimRadius value="0.398000"/>
        <rear value="false"/>
      </Item>
      <Item>
        <wheelName>wheel_5smod20e</wheelName>
        <wheelVariation />
        <modShopLabel>SMOD5_WHL_20</modShopLabel>
        <rimRadius value="0.398000"/>
        <rear value="false"/>
      </Item>
      <Item>
        <wheelName>wheel_5smod21e</wheelName>
        <wheelVariation />
        <modShopLabel>SMOD5_WHL_21</modShopLabel>
        <rimRadius value="0.398000"/>
        <rear value="false"/>
      </Item>
      <Item>
        <wheelName>wheel_5smod22e</wheelName>
        <wheelVariation />
        <modShopLabel>SMOD5_WHL_22</modShopLabel>
        <rimRadius value="0.398000"/>
        <rear value="false"/>
      </Item>
      <Item>
        <wheelName>wheel_5smod23e</wheelName>
        <wheelVariation />
        <modShopLabel>SMOD5_WHL_23</modShopLabel>
        <rimRadius value="0.398000"/>
        <rear value="false"/>
      </Item>
      <Item>
        <wheelName>wheel_5smod24e</wheelName>
        <wheelVariation />
        <modShopLabel>SMOD5_WHL_24</modShopLabel>
        <rimRadius value="0.398000"/>
        <rear value="false"/>
      </Item>
      <Item>
        <wheelName>wheel_5smod25e</wheelName>
        <wheelVariation />
        <modShopLabel>SMOD5_WHL_25</modShopLabel>
        <rimRadius value="0.398000"/>
        <rear value="false"/>
      </Item>
      <Item>
        <wheelName>wheel_5smod26e</wheelName>
        <wheelVariation />
        <modShopLabel>SMOD5_WHL_26</modShopLabel>
        <rimRadius value="0.398000"/>
        <rear value="false"/>
      </Item>
      <Item>
        <wheelName>wheel_5smod27e</wheelName>
        <wheelVariation />
        <modShopLabel>SMOD5_WHL_27</modShopLabel>
        <rimRadius value="0.398000"/>
        <rear value="false"/>
      </Item>
      <Item>
        <wheelName>wheel_5smod28e</wheelName>
        <wheelVariation />
        <modShopLabel>SMOD5_WHL_28</modShopLabel>
        <rimRadius value="0.398000"/>
        <rear value="false"/>
      </Item>
      <Item>
        <wheelName>wheel_5smod29e</wheelName>
        <wheelVariation />
        <modShopLabel>SMOD5_WHL_29</modShopLabel>
        <rimRadius value="0.398000"/>
        <rear value="false"/>
      </Item>
      <Item>
        <wheelName>wheel_5smod30e</wheelName>
        <wheelVariation />
        <modShopLabel>SMOD5_WHL_30</modShopLabel>
        <rimRadius value="0.398000"/>
        <rear value="false"/>
      </Item>
      <Item>
        <wheelName>wheel_5smod1f</wheelName>
        <wheelVariation />
        <modShopLabel>SMOD5_WHL_1</modShopLabel>
        <rimRadius value="0.398000"/>
        <rear value="false"/>
      </Item>
      <Item>
        <wheelName>wheel_5smod2f</wheelName>
        <wheelVariation />
        <modShopLabel>SMOD5_WHL_2</modShopLabel>
        <rimRadius value="0.398000"/>
        <rear value="false"/>
      </Item>
      <Item>
        <wheelName>wheel_5smod3f</wheelName>
        <wheelVariation />
        <modShopLabel>SMOD5_WHL_3</modShopLabel>
        <rimRadius value="0.398000"/>
        <rear value="false"/>
      </Item>
      <Item>
        <wheelName>wheel_5smod4f</wheelName>
        <wheelVariation />
        <modShopLabel>SMOD5_WHL_4</modShopLabel>
        <rimRadius value="0.398000"/>
        <rear value="false"/>
      </Item>
      <Item>
        <wheelName>wheel_5smod5f</wheelName>
        <wheelVariation />
        <modShopLabel>SMOD5_WHL_5</modShopLabel>
        <rimRadius value="0.398000"/>
        <rear value="false"/>
      </Item>
      <Item>
        <wheelName>wheel_5smod6f</wheelName>
        <wheelVariation />
        <modShopLabel>SMOD5_WHL_6</modShopLabel>
        <rimRadius value="0.398000"/>
        <rear value="false"/>
      </Item>
      <Item>
        <wheelName>wheel_5smod7f</wheelName>
        <wheelVariation />
        <modShopLabel>SMOD5_WHL_7</modShopLabel>
        <rimRadius value="0.398000"/>
        <rear value="false"/>
      </Item>
      <Item>
        <wheelName>wheel_5smod8f</wheelName>
        <wheelVariation />
        <modShopLabel>SMOD5_WHL_8</modShopLabel>
        <rimRadius value="0.398000"/>
        <rear value="false"/>
      </Item>
      <Item>
        <wheelName>wheel_5smod9f</wheelName>
        <wheelVariation />
        <modShopLabel>SMOD5_WHL_9</modShopLabel>
        <rimRadius value="0.398000"/>
        <rear value="false"/>
      </Item>
      <Item>
        <wheelName>wheel_5smod10f</wheelName>
        <wheelVariation />
        <modShopLabel>SMOD5_WHL_10</modShopLabel>
        <rimRadius value="0.398000"/>
        <rear value="false"/>
      </Item>
      <Item>
        <wheelName>wheel_5smod11f</wheelName>
        <wheelVariation />
        <modShopLabel>SMOD5_WHL_11</modShopLabel>
        <rimRadius value="0.398000"/>
        <rear value="false"/>
      </Item>
      <Item>
        <wheelName>wheel_5smod12f</wheelName>
        <wheelVariation />
        <modShopLabel>SMOD5_WHL_12</modShopLabel>
        <rimRadius value="0.398000"/>
        <rear value="false"/>
      </Item>
      <Item>
        <wheelName>wheel_5smod13f</wheelName>
        <wheelVariation />
        <modShopLabel>SMOD5_WHL_13</modShopLabel>
        <rimRadius value="0.398000"/>
        <rear value="false"/>
      </Item>
      <Item>
        <wheelName>wheel_5smod14f</wheelName>
        <wheelVariation />
        <modShopLabel>SMOD5_WHL_14</modShopLabel>
        <rimRadius value="0.398000"/>
        <rear value="false"/>
      </Item>
      <Item>
        <wheelName>wheel_5smod15f</wheelName>
        <wheelVariation />
        <modShopLabel>SMOD5_WHL_15</modShopLabel>
        <rimRadius value="0.398000"/>
        <rear value="false"/>
      </Item>
      <Item>
        <wheelName>wheel_5smod16f</wheelName>
        <wheelVariation />
        <modShopLabel>SMOD5_WHL_16</modShopLabel>
        <rimRadius value="0.398000"/>
        <rear value="false"/>
      </Item>
      <Item>
        <wheelName>wheel_5smod17f</wheelName>
        <wheelVariation />
        <modShopLabel>SMOD5_WHL_17</modShopLabel>
        <rimRadius value="0.398000"/>
        <rear value="false"/>
      </Item>
      <Item>
        <wheelName>wheel_5smod18f</wheelName>
        <wheelVariation />
        <modShopLabel>SMOD5_WHL_18</modShopLabel>
        <rimRadius value="0.398000"/>
        <rear value="false"/>
      </Item>
      <Item>
        <wheelName>wheel_5smod19f</wheelName>
        <wheelVariation />
        <modShopLabel>SMOD5_WHL_19</modShopLabel>
        <rimRadius value="0.398000"/>
        <rear value="false"/>
      </Item>
      <Item>
        <wheelName>wheel_5smod20f</wheelName>
        <wheelVariation />
        <modShopLabel>SMOD5_WHL_20</modShopLabel>
        <rimRadius value="0.398000"/>
        <rear value="false"/>
      </Item>
      <Item>
        <wheelName>wheel_5smod21f</wheelName>
        <wheelVariation />
        <modShopLabel>SMOD5_WHL_21</modShopLabel>
        <rimRadius value="0.398000"/>
        <rear value="false"/>
      </Item>
      <Item>
        <wheelName>wheel_5smod22f</wheelName>
        <wheelVariation />
        <modShopLabel>SMOD5_WHL_22</modShopLabel>
        <rimRadius value="0.398000"/>
        <rear value="false"/>
      </Item>
      <Item>
        <wheelName>wheel_5smod23f</wheelName>
        <wheelVariation />
        <modShopLabel>SMOD5_WHL_23</modShopLabel>
        <rimRadius value="0.398000"/>
        <rear value="false"/>
      </Item>
      <Item>
        <wheelName>wheel_5smod24f</wheelName>
        <wheelVariation />
        <modShopLabel>SMOD5_WHL_24</modShopLabel>
        <rimRadius value="0.398000"/>
        <rear value="false"/>
      </Item>
      <Item>
        <wheelName>wheel_5smod25f</wheelName>
        <wheelVariation />
        <modShopLabel>SMOD5_WHL_25</modShopLabel>
        <rimRadius value="0.398000"/>
        <rear value="false"/>
      </Item>
      <Item>
        <wheelName>wheel_5smod26f</wheelName>
        <wheelVariation />
        <modShopLabel>SMOD5_WHL_26</modShopLabel>
        <rimRadius value="0.398000"/>
        <rear value="false"/>
      </Item>
      <Item>
        <wheelName>wheel_5smod27f</wheelName>
        <wheelVariation />
        <modShopLabel>SMOD5_WHL_27</modShopLabel>
        <rimRadius value="0.398000"/>
        <rear value="false"/>
      </Item>
      <Item>
        <wheelName>wheel_5smod28f</wheelName>
        <wheelVariation />
        <modShopLabel>SMOD5_WHL_28</modShopLabel>
        <rimRadius value="0.398000"/>
        <rear value="false"/>
      </Item>
      <Item>
        <wheelName>wheel_5smod29f</wheelName>
        <wheelVariation />
        <modShopLabel>SMOD5_WHL_29</modShopLabel>
        <rimRadius value="0.398000"/>
        <rear value="false"/>
      </Item>
      <Item>
        <wheelName>wheel_5smod30f</wheelName>
        <wheelVariation />
        <modShopLabel>SMOD5_WHL_30</modShopLabel>
        <rimRadius value="0.398000"/>
        <rear value="false"/>
      </Item>
      <Item>
        <wheelName>wheel_5smod1g</wheelName>
        <wheelVariation />
        <modShopLabel>SMOD5_WHL_1</modShopLabel>
        <rimRadius value="0.398000"/>
        <rear value="false"/>
      </Item>
      <Item>
        <wheelName>wheel_5smod2g</wheelName>
        <wheelVariation />
        <modShopLabel>SMOD5_WHL_2</modShopLabel>
        <rimRadius value="0.398000"/>
        <rear value="false"/>
      </Item>
      <Item>
        <wheelName>wheel_5smod3g</wheelName>
        <wheelVariation />
        <modShopLabel>SMOD5_WHL_3</modShopLabel>
        <rimRadius value="0.398000"/>
        <rear value="false"/>
      </Item>
      <Item>
        <wheelName>wheel_5smod4g</wheelName>
        <wheelVariation />
        <modShopLabel>SMOD5_WHL_4</modShopLabel>
        <rimRadius value="0.398000"/>
        <rear value="false"/>
      </Item>
      <Item>
        <wheelName>wheel_5smod5g</wheelName>
        <wheelVariation />
        <modShopLabel>SMOD5_WHL_5</modShopLabel>
        <rimRadius value="0.398000"/>
        <rear value="false"/>
      </Item>
      <Item>
        <wheelName>wheel_5smod6g</wheelName>
        <wheelVariation />
        <modShopLabel>SMOD5_WHL_6</modShopLabel>
        <rimRadius value="0.398000"/>
        <rear value="false"/>
      </Item>
      <Item>
        <wheelName>wheel_5smod7g</wheelName>
        <wheelVariation />
        <modShopLabel>SMOD5_WHL_7</modShopLabel>
        <rimRadius value="0.398000"/>
        <rear value="false"/>
      </Item>
      <Item>
        <wheelName>wheel_5smod8g</wheelName>
        <wheelVariation />
        <modShopLabel>SMOD5_WHL_8</modShopLabel>
        <rimRadius value="0.398000"/>
        <rear value="false"/>
      </Item>
      <Item>
        <wheelName>wheel_5smod9g</wheelName>
        <wheelVariation />
        <modShopLabel>SMOD5_WHL_9</modShopLabel>
        <rimRadius value="0.398000"/>
        <rear value="false"/>
      </Item>
      <Item>
        <wheelName>wheel_5smod10g</wheelName>
        <wheelVariation />
        <modShopLabel>SMOD5_WHL_10</modShopLabel>
        <rimRadius value="0.398000"/>
        <rear value="false"/>
      </Item>
      <Item>
        <wheelName>wheel_5smod11g</wheelName>
        <wheelVariation />
        <modShopLabel>SMOD5_WHL_11</modShopLabel>
        <rimRadius value="0.398000"/>
        <rear value="false"/>
      </Item>
      <Item>
        <wheelName>wheel_5smod12g</wheelName>
        <wheelVariation />
        <modShopLabel>SMOD5_WHL_12</modShopLabel>
        <rimRadius value="0.398000"/>
        <rear value="false"/>
      </Item>
      <Item>
        <wheelName>wheel_5smod13g</wheelName>
        <wheelVariation />
        <modShopLabel>SMOD5_WHL_13</modShopLabel>
        <rimRadius value="0.398000"/>
        <rear value="false"/>
      </Item>
      <Item>
        <wheelName>wheel_5smod14g</wheelName>
        <wheelVariation />
        <modShopLabel>SMOD5_WHL_14</modShopLabel>
        <rimRadius value="0.398000"/>
        <rear value="false"/>
      </Item>
      <Item>
        <wheelName>wheel_5smod15g</wheelName>
        <wheelVariation />
        <modShopLabel>SMOD5_WHL_15</modShopLabel>
        <rimRadius value="0.398000"/>
        <rear value="false"/>
      </Item>
      <Item>
        <wheelName>wheel_5smod16g</wheelName>
        <wheelVariation />
        <modShopLabel>SMOD5_WHL_16</modShopLabel>
        <rimRadius value="0.398000"/>
        <rear value="false"/>
      </Item>
      <Item>
        <wheelName>wheel_5smod17g</wheelName>
        <wheelVariation />
        <modShopLabel>SMOD5_WHL_17</modShopLabel>
        <rimRadius value="0.398000"/>
        <rear value="false"/>
      </Item>
      <Item>
        <wheelName>wheel_5smod18g</wheelName>
        <wheelVariation />
        <modShopLabel>SMOD5_WHL_18</modShopLabel>
        <rimRadius value="0.398000"/>
        <rear value="false"/>
      </Item>
      <Item>
        <wheelName>wheel_5smod19g</wheelName>
        <wheelVariation />
        <modShopLabel>SMOD5_WHL_19</modShopLabel>
        <rimRadius value="0.398000"/>
        <rear value="false"/>
      </Item>
      <Item>
        <wheelName>wheel_5smod20g</wheelName>
        <wheelVariation />
        <modShopLabel>SMOD5_WHL_20</modShopLabel>
        <rimRadius value="0.398000"/>
        <rear value="false"/>
      </Item>
      <Item>
        <wheelName>wheel_5smod21g</wheelName>
        <wheelVariation />
        <modShopLabel>SMOD5_WHL_21</modShopLabel>
        <rimRadius value="0.398000"/>
        <rear value="false"/>
      </Item>
      <Item>
        <wheelName>wheel_5smod22g</wheelName>
        <wheelVariation />
        <modShopLabel>SMOD5_WHL_22</modShopLabel>
        <rimRadius value="0.398000"/>
        <rear value="false"/>
      </Item>
      <Item>
        <wheelName>wheel_5smod23g</wheelName>
        <wheelVariation />
        <modShopLabel>SMOD5_WHL_23</modShopLabel>
        <rimRadius value="0.398000"/>
        <rear value="false"/>
      </Item>
      <Item>
        <wheelName>wheel_5smod24g</wheelName>
        <wheelVariation />
        <modShopLabel>SMOD5_WHL_24</modShopLabel>
        <rimRadius value="0.398000"/>
        <rear value="false"/>
      </Item>
      <Item>
        <wheelName>wheel_5smod25g</wheelName>
        <wheelVariation />
        <modShopLabel>SMOD5_WHL_25</modShopLabel>
        <rimRadius value="0.398000"/>
        <rear value="false"/>
      </Item>
      <Item>
        <wheelName>wheel_5smod26g</wheelName>
        <wheelVariation />
        <modShopLabel>SMOD5_WHL_26</modShopLabel>
        <rimRadius value="0.398000"/>
        <rear value="false"/>
      </Item>
      <Item>
        <wheelName>wheel_5smod27g</wheelName>
        <wheelVariation />
        <modShopLabel>SMOD5_WHL_27</modShopLabel>
        <rimRadius value="0.398000"/>
        <rear value="false"/>
      </Item>
      <Item>
        <wheelName>wheel_5smod28g</wheelName>
        <wheelVariation />
        <modShopLabel>SMOD5_WHL_28</modShopLabel>
        <rimRadius value="0.398000"/>
        <rear value="false"/>
      </Item>
      <Item>
        <wheelName>wheel_5smod29g</wheelName>
        <wheelVariation />
        <modShopLabel>SMOD5_WHL_29</modShopLabel>
        <rimRadius value="0.398000"/>
        <rear value="false"/>
      </Item>
      <Item>
        <wheelName>wheel_5smod30g</wheelName>
        <wheelVariation />
        <modShopLabel>SMOD5_WHL_30</modShopLabel>
        <rimRadius value="0.398000"/>
        <rear value="false"/>
      </Item>
     </Item>
  </Wheels>    
</CVehicleModelInfoVarGlobal>