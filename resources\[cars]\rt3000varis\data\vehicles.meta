<?xml version="1.0" encoding="UTF-8"?>
<CVehicleModelInfo__InitDataList>
  <residentTxd>vehshare</residentTxd>
  <residentAnims />
  <InitDatas> 
	<Item>
      <modelName>rt3000varis</modelName>
      <txdName>rt3000varis</txdName>
      <handlingId>rt3000varis</handlingId>
      <gameName>rt3000varis</gameName>
      <vehicleMakeName>DINKA</vehicleMakeName>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected>
        <Item>VEH_EXT_WINDOW_LF</Item>
        <Item>VEH_EXT_WINDOW_RF</Item>
      </animConvRoofWindowsAffected>
      <ptfxAssetName>null</ptfxAssetName>
      <audioNameHash>RT3000</audioNameHash>
      <layout>LAYOUT_LOW_RESTRICTED_RT3000</layout>
      <coverBoundOffsets>RT3000_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout />
      <cameraName>FOLLOW_CHEETAH_CAMERA</cameraName>
      <aimCameraName>DEFAULT_THIRD_PERSON_VEHICLE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>VEHICLE_BONNET_CAMERA_STANDARD_LONG_REBLA</bonnetCameraName>
      <povCameraName>REDUCED_NEAR_CLIP_POV_CAMERA</povCameraName>
      <FirstPersonDriveByIKOffset x="0.010000" y="-0.155000" z="-0.030000" />
      <FirstPersonDriveByUnarmedIKOffset x="0.000000" y="-0.060000" z="0.000000" />
	  <FirstPersonProjectileDriveByIKOffset x="0.085000" y="-0.140000" z="-0.060000" />
	  <FirstPersonProjectileDriveByPassengerIKOffset x="-0.140000" y="-0.130000" z="-0.045000" />
	  <FirstPersonDriveByRightPassengerIKOffset x="0.000000" y="-0.135000" z="-0.030000" />
	  <FirstPersonDriveByRightPassengerUnarmedIKOffset x="0.000000" y="-0.080000" z="0.000000" />
	  <FirstPersonMobilePhoneOffset x="0.140000" y="0.243000" z="0.506000" />
      <FirstPersonPassengerMobilePhoneOffset x="0.206000" y="0.216000" z="0.405000" />
      <PovCameraOffset x="0.000000" y="-0.150000" z="0.605000" />
      <PovCameraVerticalAdjustmentForRollCage value="-0.045000" />
      <PovPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000" />
      <vfxInfoName>VFXVEHICLEINFO_CAR_GENERIC</vfxInfoName>
      <shouldUseCinematicViewMode value="true" />
      <shouldCameraTransitionOnClimbUpDown value="false" />
      <shouldCameraIgnoreExiting value="false" />
      <AllowPretendOccupants value="true" />
      <AllowJoyriding value="true" />
      <AllowSundayDriving value="true" />
      <AllowBodyColorMapping value="true" />
      <wheelScale value="0.224300" />
      <wheelScaleRear value="0.224300" />
      <dirtLevelMin value="0.000000" />
      <dirtLevelMax value="0.700000" />
      <envEffScaleMin value="0.000000" />
      <envEffScaleMax value="1.000000" />
      <envEffScaleMin2 value="0.000000" />
      <envEffScaleMax2 value="1.000000" />
      <damageMapScale value="0.600000" />
      <damageOffsetScale value="1.000000" />
      <diffuseTint value="0x00FFFFFF" />
      <steerWheelMult value="0.500000" />
      <HDTextureDist value="5.000000" />
      <lodDistances content="float_array">
        10.000000
        25.000000
        60.000000
        120.000000
        500.000000
        500.000000
      </lodDistances>
      <minSeatHeight value="0.821" />
      <identicalModelSpawnDistance value="20" />
      <maxNumOfSameColor value="1" />
      <defaultBodyHealth value="1000.000000" />
      <pretendOccupantsScale value="1.000000" />
      <visibleSpawnDistScale value="1.000000" />
      <trackerPathWidth value="2.000000" />
      <weaponForceMult value="1.000000" />
      <frequency value="30" />
      <swankness>SWANKNESS_2</swankness>
      <maxNum value="4" />
      <flags>FLAG_HAS_LIVERY FLAG_SPORTS FLAG_SPAWN_ON_TRAILER FLAG_PARKING_SENSORS FLAG_RICH_CAR FLAG_NO_BROKEN_DOWN_SCENARIO FLAG_CAN_HAVE_NEONS FLAG_INCREASE_CAMBER_WITH_SUSPENSION_MOD</flags>
      <type>VEHICLE_TYPE_CAR</type>
      <plateType>VPT_BACK_PLATES</plateType>
	  <dashboardType>VDT_FEROCI</dashboardType>
      <vehicleClass>VC_SPORT</vehicleClass>
      <wheelType>VWT_SPORT</wheelType>
      <trailers />
      <additionalTrailers />
      <drivers />
      <extraIncludes />
      <doorsWithCollisionWhenClosed />
      <driveableDoors />
      <bumpersNeedToCollideWithMap value="false" />
      <needsRopeTexture value="false" />
      <requiredExtras />
      <rewards />
      <cinematicPartCamera>
        <Item>WHEEL_FRONT_RIGHT_CAMERA</Item>
        <Item>WHEEL_FRONT_LEFT_CAMERA</Item>
        <Item>WHEEL_REAR_RIGHT_CAMERA</Item>
        <Item>WHEEL_REAR_LEFT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet />
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000" />
      <buoyancySphereSizeScale value="1.000000" />
      <pOverrideRagdollThreshold type="NULL" />
      <firstPersonDrivebyData>
        <Item>LOW_RT3000_FRONT_LEFT</Item>
        <Item>LOW_RT3000_FRONT_RIGHT</Item>
      </firstPersonDrivebyData>
    </Item>
  </InitDatas>
  <txdRelationships>
    <Item>
      <parent>vehshare</parent>
      <child>vehicles_pfister_interior</child>
    </Item>
    <Item>
      <parent>vehshare</parent>
      <child>vehicles_fmj_w_interior</child>
    </Item>
    <Item>
      <parent>vehshare</parent>
      <child>vehicles_jugular_w_interior</child>
    </Item>
    <Item>
      <parent>vehicles_race_generic</parent>
      <child>vehicles_coquette_w1_interior</child>
    </Item>
    <Item>
      <parent>vehicles_race_generic</parent>
      <child>vehicles_coquette_w2_interior</child>
    </Item>
    <Item>
      <parent>vehicles_race_generic</parent>
      <child>vehicles_fmj_w_race_interior</child>
    </Item>
    <Item>
      <parent>vehicles_race_generic</parent>
      <child>vehicles_futo_race_interior</child>
    </Item>
    <Item>
      <parent>vehicles_race_generic</parent>
      <child>vehicles_jugular_race_interior</child>
    </Item>
    <Item>
      <parent>vehicles_race_generic</parent>
      <child>vehicles_monroe_race_interior</child>
    </Item>
    <Item>
      <parent>vehicles_race_generic</parent>
      <child>vehicles_muscle_o_race_interior</child>
    </Item>
    <Item>
      <parent>vehicles_race_generic</parent>
      <child>vehicles_pfister_race_interior</child>
    </Item>
    <Item>
      <parent>vehicles_race_generic</parent>
      <child>vehicles_supergt_w1_interior</child>
    </Item>
    <Item>
      <parent>vehicles_race_generic</parent>
      <child>vehicles_supergt_w2_interior</child>
    </Item>
    <Item>
      <parent>vehicles_coquette_w1_interior</parent>
      <child>euros</child>
    </Item>
    <Item>
      <parent>vehicles_supergt_w1_interior</parent>
      <child>tailgater2</child>
    </Item>
   	<Item>
      <parent>vehicles_fmj_w_race_interior</parent>
      <child>vectre</child>
    </Item>	
    <Item>
      <parent>vehicles_sultanrs_interior</parent>
      <child>dominator7</child>
    </Item>
    <Item>
      <parent>vehicles_banshee_w_interior</parent>
      <child>zr350</child>
    </Item>
    <Item>
      <parent>vehicles_coquette_w1_interior</parent>
      <child>previon</child>
    </Item>
    <Item>
      <parent>vehicles_monroe_race_interior</parent>
      <child>warrener2</child>
    </Item>
	<Item>
      <parent>vehicles_fmj_w_race_interior</parent>
      <child>jester4</child>
    </Item>
	<Item>
      <parent>vehicles_jugular_race_interior</parent>
      <child>cypher</child>
    </Item>
	<Item>
      <parent>vehicles_coquette_w1_interior</parent>
      <child>calico</child>
    </Item>
    <Item>
      <parent>vehicles_elegy_race</parent>
      <child>sultan3</child>
    </Item>
    <Item>
      <parent>vehicles_elegy_race</parent>
      <child>remus</child>
    </Item>
    <Item>
      <parent>vehicles_coquette_w1_interior</parent>
      <child>rt3000varis</child>
    </Item>
    <Item>
      <parent>vehicles_pfister_race_interior</parent>
      <child>comet6</child>
    </Item>
	<Item>
      <parent>vehicles_pfister_race_interior</parent>
      <child>growler</child>
    </Item>
    <Item>
      <parent>vehicles_futo_race_interior</parent>
      <child>futo2</child>
    </Item>
    <Item>
      <parent>vehicles_muscle_o_race_interior</parent>
      <child>dominator8</child>
    </Item>
    <Item>
      <parent>vehshare_worn</parent>
      <child>freightcar2</child>
    </Item>	
  </txdRelationships>
</CVehicleModelInfo__InitDataList>
