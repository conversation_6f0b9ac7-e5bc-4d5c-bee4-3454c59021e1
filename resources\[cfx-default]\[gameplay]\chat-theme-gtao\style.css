* {
    font-family: inherit;
}

.chat-window {
    --size: calc(((2.7vh * 1.2)) * 6);

    position: absolute;
    right: calc(2.77vh);
    top: calc(50% - (var(--size) / 2));
    height: var(--size) !important;

    background: inherit !important;

    text-align: right;

    left: auto;

    user-select: none;
}

@font-face {
    font-family: 'Font2';
    src: url(https://runtime.fivem.net/temp/ChaletLondonNineteenSixty.otf?a);
}

@font-face {
    font-family: 'Font2_cond';
    src: url(https://runtime.fivem.net/temp/chaletcomprime-colognesixty-webfont.ttf?a);
}

.msg {
    font-family: Font2, sans-serif;
    color: #fff;

    font-size: calc(1.8vh); /* 13px in 720p, calc'd by width */
    filter: url(#svgDropShadowFilter);

    line-height: calc(2.7vh * 1.2);

    margin-bottom: 0;
}

.chat-messages {
    margin: 0;
    height: 100%;
}

.msg > span > span > b {
    font-family: Font2_cond, sans-serif;
    font-weight: normal;

    vertical-align: baseline;
    padding-right: 11px;

    line-height: 1;

    font-size: calc(2.7vh);
}

.msg > span > span > span {
    vertical-align: baseline;
}

.msg i:first-of-type {
    font-style: normal;
    color: #c0c0c0;
}

.chat-input {
    position: absolute;
    right: calc(2.77vh);
    bottom: calc(2.77vh);

    background: inherit !important;

    text-align: right;

    top: auto;
    left: auto;

    height: auto;

    font-family: Font2, sans-serif;
}

.chat-input > div {
    background-color: rgba(0, 0, 0, .6) !important;
    border: calc(0.28vh / 2) solid rgba(180, 180, 180, .6);
    outline: calc(0.28vh / 2) solid rgba(0, 0, 0, .8); /* to replace margin-background */
    padding: calc(0.28vh / 2);
}

.chat-input .prefix {
    margin: 0;
    margin-left: 0.7%;
    margin-top: -0.1%;
    line-height: 2.8vh;
}

.chat-input .prefix.any {
    opacity: 0.8;
}

.chat-input .prefix.any:before {
    content: '[';
}

.chat-input .prefix.any:after {
    content: ']';
}

.chat-input > div + div {
    position: absolute;
    bottom: calc(1.65vh + 0.28vh + 0.28vh + 0.28vh + (0.28vh / 2));
    width: 99.6%;

    text-align: left;
}

.suggestions {
    border: calc(0.28vh / 2) solid rgba(180, 180, 180, .6);
    background: transparent;
}

textarea {
    background: transparent;
    padding: 0.5vh;
}

@media screen and (min-aspect-ratio: 21/9) {
	.chat-window, .chat-input {
		right: calc(12.8vw);
	}
}

@media screen and (min-aspect-ratio: 32/9) {
	.chat-window, .chat-input {
		right: calc(25vw);
	}
}
