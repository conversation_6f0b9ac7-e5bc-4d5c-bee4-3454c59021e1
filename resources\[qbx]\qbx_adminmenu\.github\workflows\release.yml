name: Create New Release

on:
  workflow_dispatch:
    inputs:
      version:
        required: true

jobs:
  create-release:
    name: Create New Release
    runs-on: ubuntu-latest
    steps:
      - name: Get App Token
        uses: actions/create-github-app-token@v1
        id: generate_token
        with:
          app-id: ${{ secrets.APP_ID }}
          private-key: ${{ secrets.PRIVATE_KEY }}

      - name: Checkout Repository
        uses: actions/checkout@v4
        with:
          token: ${{ steps.generate_token.outputs.token }}

      - name: Setup node
        uses: actions/setup-node@v4
        with:
          node-version: 20.x

      - name: Bump manifest version
        run: node .github/actions/bump-manifest-version.js
        env:
          TGT_RELEASE_VERSION: ${{ inputs.version }}

      - name: Push manifest change
        uses: EndBug/add-and-commit@v9
        with:
          add: fxmanifest.lua
          push: true
          message: 'chore: bump manifest version to ${{ inputs.version }}'
          tag: ${{ inputs.version }}
