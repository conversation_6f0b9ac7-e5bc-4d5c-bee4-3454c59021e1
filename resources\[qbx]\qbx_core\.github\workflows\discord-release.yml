name: "Discord Releases"

on:
  release:
    types: [published]
  
jobs:
  github-releases-to-discord:
    name: Discord Releases Changelog
    runs-on: ubuntu-latest
    steps:
    - name: Checkout Repository
      uses: actions/checkout@v4
    - name: Github Releases To Discord
      uses: Seth<PERSON><PERSON><PERSON>/github-releases-to-discord@v1.16.2
      with:
        webhook_url: ${{ secrets.WEBHOOK_URL }}
        color: "15852866"
        username: ${{ github.event.repository.name }}
        avatar_url: "https://i.imgur.com/Eh1yiLI.png"
        footer_timestamp: true
