local jobs = require 'shared.jobs'
local gangs = require 'shared.gangs'

---@return table<string, Job>
function GetJobs()
    return jobs
end

exports('<PERSON><PERSON><PERSON><PERSON>', GetJobs)

---@return table<string, Gang>
function GetGangs()
    return gangs
end

exports('<PERSON>Gang<PERSON>', <PERSON>Gangs)

---@param name string
---@return Job?
function GetJob(name)
    return jobs[name]
end

exports('<PERSON><PERSON><PERSON>', <PERSON><PERSON><PERSON>)

---@param name string
---@return Gang?
function GetGang(name)
    return gangs[name]
end

exports('GetGang', GetGang)

RegisterNetEvent('qbx_core:client:onJobUpdate', function(jobName, job)
    jobs[jobName] = job
end)

RegisterNetEvent('qbx_core:client:onGangUpdate', function(gangName, gang)
    gangs[gangName] = gang
end)

local groups = lib.callback.await('qbx_core:server:getGroups')
jobs = groups.jobs
gangs = groups.gangs