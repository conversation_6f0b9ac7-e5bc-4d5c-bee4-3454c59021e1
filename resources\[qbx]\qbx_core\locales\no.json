{"error": {"not_online": "Spiller ikke online", "wrong_format": "Ugyldig format", "missing_args": "Ikke alle argumenter er lagt inn (x, y, z)", "missing_args2": "Alle argumenter må fylles ut!", "no_access": "Ingen tilgang til denne kommandoen", "company_too_poor": "Arbeidsgiveren din er blakk", "item_not_exist": "Gjenstand finnes ikke", "too_heavy": "<PERSON><PERSON>ne er fulle", "location_not_exist": "Plassering finnes ikke", "duplicate_license": "Duplikat Rockstar-lisens funnet", "no_valid_license": "Ingen gyldig Rockstar-lisens funnet", "not_whitelisted": "Du har ikke tilgang til serveren", "server_already_open": "Serveren er allerede åpen", "server_already_closed": "<PERSON>en er allerede stengt", "no_permission": "Du har ikke till<PERSON><PERSON>er for dette..", "no_waypoint": "Ingen veipunkt satt.", "tp_error": "Feil under teleportering.", "connecting_database_timeout": "Connection to database timed out. (Is the SQL server on?)", "connecting_error": "An error occurred while connecting to the server. (Check your server console)", "no_match_character_registration": "Anything other than letters aren't allowed, trailing whitespaces aren't allowed either and words must start with a capital letter in input fields. You can however add words with spaces inbetween.", "already_in_queue": "You are already in queue.", "no_subqueue": "You were not let in any sub-queue."}, "success": {"server_opened": "<PERSON><PERSON> er <PERSON>", "server_closed": "<PERSON><PERSON> er stengt", "teleported_waypoint": "Teleporter til veipunkt.", "character_deleted": "Character deleted!", "character_deleted_citizenid": "You successfully deleted the character with Citizen ID %s."}, "info": {"received_paycheck": "Du mottok lønnsslippen din på kr %s", "job_info": "Jobb: %s | Grad: %s | vakt: %s", "gang_info": "Gjeng: %s | Grad: %s", "on_duty": "Du er nå på vakt!", "off_duty": "Du er nå av vakt!", "checking_ban": "Hei %s. Vi sjekker om du er utestengt.", "join_server": "Velkommen %s til %s.", "checking_whitelisted": "Hei %s. Vi sjekker tilganger.", "exploit_banned": "<PERSON> har blitt utest<PERSON>t for juks. Sjekk vår Discord for mer informasjon: %s", "exploit_dropped": "Du har blitt sparket for utnyttelse", "multichar_title": "Qbox Multichar", "multichar_new_character": "New Character #%s", "char_male": "Male", "char_female": "Female", "play": "Play", "play_description": "Play as %s", "delete_character": "Delete Character", "delete_character_description": "Delete %s", "logout_command_help": "Logs you out of your current character", "check_id": "Check your Server ID", "deletechar_command_help": "Delete a players character", "deletechar_command_arg_player_id": "Player ID", "character_registration_title": "Character Registration", "first_name": "First Name", "last_name": "Last Name", "nationality": "Nationality", "gender": "Sex", "birth_date": "Birth Date", "select_gender": "Select your gender...", "confirm_delete": "Are you sure you wish to delete this character?", "in_queue": "🐌 You are %s/%s in queue. (%s) %s"}, "command": {"tp": {"help": "TP til spiller eller koordinater (kun admin)", "params": {"x": {"name": "id/x", "help": "ID for spiller eller <PERSON>-p<PERSON>"}, "y": {"name": "y", "help": "Y posisjon"}, "z": {"name": "z", "help": "Z posisjon"}}}, "tpm": {"help": "TP <PERSON><PERSON> (kun admin)"}, "togglepvp": {"help": "Slå PVP på serveren (kun admin)"}, "addpermission": {"help": "<PERSON><PERSON> (Kun gud)", "params": {"id": {"name": "id", "help": "<PERSON> på spiller"}, "permission": {"name": "permission", "help": "Tillatelsesnivå "}}}, "removepermission": {"help": "<PERSON><PERSON><PERSON> (kun gud)", "params": {"id": {"name": "id", "help": "<PERSON> på spiller"}, "permission": {"name": "permission", "help": "Tillatelsesnivå"}}}, "openserver": {"help": "<PERSON><PERSON><PERSON> serveren for alle (kun admin)"}, "closeserver": {"help": "Lukk serveren for personer uten tillate<PERSON>er (kun admin)", "params": {"reason": {"name": "reason", "help": "Årsak til stenging (valgfritt)"}}}, "car": {"help": "Spawn Vehicle (kun admin)", "params": {"model": {"name": "model", "help": "Modellnavn på kjøretøyet"}}}, "dv": {"help": "<PERSON><PERSON> kjøretøy (kun admin)"}, "givemoney": {"help": "<PERSON><PERSON> en spiller penger (kun admin)", "params": {"id": {"name": "id", "help": "<PERSON> på spiller"}, "moneytype": {"name": "moneytype", "help": "Penge type (cash, bank, crypto)"}, "amount": {"name": "amount", "help": "<PERSON><PERSON> be<PERSON>"}}}, "setmoney": {"help": "<PERSON><PERSON> (kun admin)", "params": {"id": {"name": "id", "help": "<PERSON> på spiller"}, "moneytype": {"name": "moneytype", "help": "Penge type (cash, bank, crypto)"}, "amount": {"name": "amount", "help": "<PERSON><PERSON> be<PERSON>"}}}, "job": {"help": "Sjekk din <PERSON>b"}, "setjob": {"help": "<PERSON>t en spillerjobb (kun admin)", "params": {"id": {"name": "id", "help": "<PERSON> på spiller"}, "job": {"name": "job", "help": "Jobb navn"}, "grade": {"name": "grade", "help": "Jobb grad"}}}, "changejob": {"help": "Change Active Job of Player (Admin Only)", "params": {"id": {"name": "id", "help": "Player ID"}, "job": {"name": "job", "help": "Job name"}}}, "addjob": {"help": "Add Job to Player (Admin Only)", "params": {"id": {"name": "id", "help": "Player ID"}, "job": {"name": "job", "help": "Job name"}, "grade": {"name": "grade", "help": "Job grade"}}}, "removejob": {"help": "Re<PERSON><PERSON> Job from Player (Admin Only)", "params": {"id": {"name": "id", "help": "Player ID"}, "job": {"name": "job", "help": "Job name"}}}, "gang": {"help": "Sjekk din gjeng"}, "setgang": {"help": "<PERSON>t en spill<PERSON>g<PERSON>ng (kun admin)", "params": {"id": {"name": "id", "help": "<PERSON> på spiller"}, "gang": {"name": "gang", "help": "Gjeng navn"}, "grade": {"name": "grade", "help": "Gjeng grad"}}}, "ooc": {"help": "UAK Chat Melding"}, "me": {"help": "Vis lokal melding", "params": {"message": {"name": "message", "help": "Melding å sende"}}}}}