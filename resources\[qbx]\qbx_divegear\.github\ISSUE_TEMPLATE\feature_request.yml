name: Feature request
description: Suggest an idea for Qbox
labels: enhancement
body:
  - type: markdown
    attributes:
      value: |
        Please use our Discord Server to ask questions and receive support: https://discord.gg/Z6Whda5hHA
  - type: textarea
    id: problem
    attributes:
      label: The problem
      description: A clear and concise description of what the problem is, or what feature you want to be implemented.
      placeholder: |
        Some examples:
        I'm frustrated that ...
        It would be nice if ...
    validations:
      required: true
  - type: textarea
    id: solution
    attributes:
      label: Ideal solution
      description: A clear and concise description of what you want to happen, with as much detail as possible.
    validations:
      required: true
  - type: textarea
    id: alternatives
    attributes:
      label: Alternative solutions
      description: A clear and concise description of any alternative solutions or features you've considered.
  - type: textarea
    id: additional
    attributes:
      label: Additional context
      description: If you have any other context about the problem such as screenshots or videos, add them here.
