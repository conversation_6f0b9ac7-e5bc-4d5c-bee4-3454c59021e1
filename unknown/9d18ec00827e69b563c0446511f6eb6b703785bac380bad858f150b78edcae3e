{"error": {"not_online": "Mängija pole serveris!", "wrong_format": "Vale formaat.", "missing_args": "Kõiki argumente pole sisestatud (x, y, z)", "missing_args2": "K<PERSON>ik argumendid tuleb täita!", "no_access": "<PERSON><PERSON><PERSON> käsule pole juurdepääsu!", "company_too_poor": "<PERSON><PERSON> on pankrotis.", "item_not_exist": "<PERSON><PERSON><PERSON> asja ei eks<PERSON>i", "too_heavy": "Inventuur on liiga täis", "duplicate_license": "Leiti Rockstari litsentsi duplikaat", "no_valid_license": "Kehtivat Rockstari litsentsi ei leitud", "not_whitelisted": "Te'pole serveri Allowlistis!", "server_already_open": "The server is already open", "server_already_closed": "The server is already closed", "no_permission": "You don't have permissions for this..", "no_waypoint": "No Waypoint Set.", "tp_error": "Error While Teleporting.", "connecting_database_timeout": "Connection to database timed out. (Is the SQL server on?)", "connecting_error": "An error occurred while connecting to the server. (Check your server console)", "no_match_character_registration": "Anything other than letters aren't allowed, trailing whitespaces aren't allowed either and words must start with a capital letter in input fields. You can however add words with spaces inbetween.", "already_in_queue": "You are already in queue.", "no_subqueue": "You were not let in any sub-queue."}, "success": {"server_opened": "The server has been opened", "server_closed": "The server has been closed", "teleported_waypoint": "Teleported To Waypoint.", "character_deleted": "Character deleted!", "character_deleted_citizenid": "You successfully deleted the character with Citizen ID %s."}, "info": {"received_paycheck": "Saite oma töötasu kätte $%s", "job_info": "Töökoht: %s | Auaste: %s | Tööl: %s", "gang_info": "Gang: %s | Auaste: %s", "on_duty": "Alustasite enda tööpäeva!", "off_duty": "Lõpetasite enda tööpäeva!", "checking_ban": "Tere %s. <PERSON> kontrollime, kas olete keelustatud.", "join_server": "Tere tulemast %s serverisse %s.", "checking_whitelisted": "Tere %s. Kontrollime teie Allowlisti olemasolu.", "exploit_banned": "You have been banned for cheating. Check our Discord for more information: %s", "exploit_dropped": "You Have Been Kicked For Exploitation", "multichar_title": "Qbox Multichar", "multichar_new_character": "New Character #%s", "char_male": "Male", "char_female": "Female", "play": "Play", "play_description": "Play as %s", "delete_character": "Delete Character", "delete_character_description": "Delete %s", "logout_command_help": "Logs you out of your current character", "check_id": "Check your Server ID", "deletechar_command_help": "Delete a players character", "deletechar_command_arg_player_id": "Player ID", "character_registration_title": "Character Registration", "first_name": "First Name", "last_name": "Last Name", "nationality": "Nationality", "gender": "Sex", "birth_date": "Birth Date", "select_gender": "Select your gender...", "confirm_delete": "Are you sure you wish to delete this character?", "in_queue": "🐌 You are %s/%s in queue. (%s) %s"}, "command": {"tp": {"help": "TP To Player or Coords (Admin Only)", "params": {"x": {"name": "id/x", "help": "ID of player or X position"}, "y": {"name": "y", "help": "Y position"}, "z": {"name": "z", "help": "Z position"}}}, "tpm": {"help": "TP <PERSON> (Admin Only)"}, "togglepvp": {"help": "Toggle PVP on the server (Admin Only)"}, "addpermission": {"help": "Give Player Permissions (God Only)", "params": {"id": {"name": "id", "help": "ID of player"}, "permission": {"name": "permission", "help": "Permission level"}}}, "removepermission": {"help": "Remove Player Permissions (God Only)", "params": {"id": {"name": "id", "help": "ID of player"}, "permission": {"name": "permission", "help": "Permission level"}}}, "openserver": {"help": "Open the server for everyone (Admin Only)"}, "closeserver": {"help": "Close the server for people without permissions (Admin Only)", "params": {"reason": {"name": "reason", "help": "Reason for closing (optional)"}}}, "car": {"help": "Spawn Vehicle (Admin Only)", "params": {"model": {"name": "model", "help": "Model name of the vehicle"}, "keepCurrentVehicle": {"name": "keepCurrentVehicle", "help": "Keep the vehicle you're in right now (leave empty to delete current vehicle)"}}}, "dv": {"help": "Delete Vehicle (Admin Only)", "params": {"radius": {"name": "radius", "help": "Radius to delete vehicles in (meters)"}}}, "givemoney": {"help": "Give A Player Money (Admin Only)", "params": {"id": {"name": "id", "help": "Player ID"}, "moneytype": {"name": "moneytype", "help": "Type of money (cash, bank, crypto)"}, "amount": {"name": "amount", "help": "Amount of money"}}}, "setmoney": {"help": "Set Players Money Amount (Admin Only)", "params": {"id": {"name": "id", "help": "Player ID"}, "moneytype": {"name": "moneytype", "help": "Type of money (cash, bank, crypto)"}, "amount": {"name": "amount", "help": "Amount of money"}}}, "job": {"help": "Check Your Job"}, "setjob": {"help": "Set A Players Job (Admin Only)", "params": {"id": {"name": "id", "help": "Player ID"}, "job": {"name": "job", "help": "Job name"}, "grade": {"name": "grade", "help": "Job grade"}}}, "changejob": {"help": "Change Active Job of Player (Admin Only)", "params": {"id": {"name": "id", "help": "Player ID"}, "job": {"name": "job", "help": "Job name"}}}, "addjob": {"help": "Add Job to Player (Admin Only)", "params": {"id": {"name": "id", "help": "Player ID"}, "job": {"name": "job", "help": "Job name"}, "grade": {"name": "grade", "help": "Job grade"}}}, "removejob": {"help": "Re<PERSON><PERSON> Job from Player (Admin Only)", "params": {"id": {"name": "id", "help": "Player ID"}, "job": {"name": "job", "help": "Job name"}}}, "gang": {"help": "Check Your Gang"}, "setgang": {"help": "Set A Players Gang (Admin Only)", "params": {"id": {"name": "id", "help": "Player ID"}, "gang": {"name": "gang", "help": "Gang name"}, "grade": {"name": "grade", "help": "Gang grade"}}}, "ooc": {"help": "OOC Chat Message"}, "me": {"help": "Show local message", "params": {"message": {"name": "message", "help": "Message to send"}}}}}